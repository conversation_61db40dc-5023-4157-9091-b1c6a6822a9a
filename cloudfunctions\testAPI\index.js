const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  console.log('🔄 测试云函数被调用')
  console.log('📥 接收到的参数:', event)
  
  try {
    // 返回基本信息
    return {
      success: true,
      message: '云函数调用成功',
      timestamp: new Date().toISOString(),
      env: cloud.DYNAMIC_CURRENT_ENV,
      event: event,
      context: {
        requestId: context.requestId,
        functionName: context.functionName,
        functionVersion: context.functionVersion
      }
    }
  } catch (error) {
    console.error('❌ 云函数执行失败:', error)
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
}
