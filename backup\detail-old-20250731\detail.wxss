/* pages/detail/detail.wxss - 旧版本备份（已被 detail-new.wxss 替代） */
.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 表情包图片 */
.emoji-container {
  margin: 0 20rpx 20rpx 20rpx; /* 移除顶部margin，保留左右下margin */
  padding: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.emoji-image {
  max-width: 100%;
  max-height: 500rpx;
  border-radius: 20rpx;
}

/* 表情包信息 */
.emoji-info {
  margin: 0 20rpx 20rpx 20rpx; /* 移除顶部margin */
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.emoji-header {
  margin-bottom: 30rpx;
}

.emoji-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.emoji-meta {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.date-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.date-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 30rpx;
}

.view-count {
  font-size: 24rpx;
  color: #666;
  margin-right: 30rpx;
}

.hot-icon {
  font-size: 24rpx;
  color: #ff6b6b;
}

.author-tag {
  display: inline-block;
  background: #f0f0ff;
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
}

.author-text {
  font-size: 24rpx;
  color: #8B5CF6;
}

/* 标签 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background: #f0f0ff;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #8B5CF6;
}

/* 统计信息 */
.stats-container {
  margin: 0 20rpx 20rpx 20rpx; /* 移除顶部margin */
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.stats-row {
  display: flex;
  justify-content: space-around;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.stat-icon.liked {
  color: #ff4757;
}

.stat-icon.collected {
  color: #ffa502;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  min-width: 80rpx;
  text-align: center;
  font-variant-numeric: tabular-nums;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  margin: 0 20rpx 20rpx 20rpx; /* 移除顶部margin */
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
  /* 默认隐藏状态 */
  opacity: 0;
  transform: translateY(20rpx);
  pointer-events: none;
}

/* 控制操作按钮的显示和隐藏 */
.action-buttons.hide {
  opacity: 0;
  transform: translateY(20rpx);
  pointer-events: none;
}

.action-buttons.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.button-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.button-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  background: #f5f5f5;
  color: #666;
}

/* 完全移除active效果 */
/* .action-btn:active {
  opacity: 0.8;
  transition: opacity 0.1s ease;
} */

.action-btn[disabled] {
  opacity: 0.6;
  pointer-events: none;
}

.like-btn {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.like-btn.liked {
  background: #ff4757;
  color: white;
  border: 2rpx solid #ff4757;
}

.collect-btn {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.collect-btn.collected {
  background: #ffa726;
  color: white;
  border: 2rpx solid #ffa726;
}

.download-btn {
  background: #8B5CF6;
  color: white;
}

.share-btn {
  background: #4facfe;
  color: white;
}

.btn-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  margin: 0 20rpx 20rpx 20rpx; /* 移除顶部margin */
  padding: 60rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 相关推荐 */
.related-section {
  margin: 40rpx 20rpx 0;
  padding: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
  padding: 0 20rpx;
}

/* 使用与首页完全一致的表情包列表样式 */
.related-section .emoji-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0 20rpx;
}

.related-section .emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.related-section .emoji-item:active {
  transform: scale(0.98);
}

.related-section .emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
}

.related-section .emoji-image {
  width: 100%;
  height: 100%;
}

.related-section .emoji-info {
  padding: 24rpx;
}

.related-section .emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-section .emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 数据统计区域 - 与首页保持一致 */
.related-section .emoji-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.related-section .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx;
  transition: all 0.3s ease;
}

.related-section .stat-item:active {
  transform: scale(0.95);
}

.related-section .stat-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.related-section .stat-number {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 轻量提示组件已移除，使用控制台提示 */