/**
 * 分享功能管理器
 * 处理表情包分享、生成分享图片、分享统计等功能
 */

const ShareManager = {
  // 分享配置
  _config: {
    appName: '表情包小程序',
    defaultTitle: '发现有趣的表情包',
    defaultDesc: '海量表情包，让聊天更有趣',
    shareImageQuality: 0.8,
    watermarkEnabled: true,
    trackShares: true
  },

  // 分享统计
  _stats: {
    totalShares: 0,
    sharesByType: {
      emoji: 0,
      collection: 0,
      app: 0
    },
    sharesByPlatform: {
      wechat: 0,
      moments: 0,
      other: 0
    }
  },

  // 分享模板
  _templates: {
    emoji: {
      title: '#{title}# - 超有趣的表情包',
      desc: '来自#{appName}#，发现更多有趣表情',
      imageUrl: ''
    },
    collection: {
      title: '#{count}#个精选表情包合集',
      desc: '#{appName}# - 让聊天更有趣',
      imageUrl: ''
    },
    app: {
      title: '#{appName}# - 海量表情包',
      desc: '发现有趣的表情包，让聊天更生动',
      imageUrl: ''
    }
  },

  /**
   * 初始化分享管理器
   */
  init(config = {}) {
    this._config = { ...this._config, ...config }
    console.log('📤 分享管理器初始化')
    
    // 加载分享统计
    this.loadShareStats()
    
    // 设置全局分享
    this.setupGlobalShare()
  },

  /**
   * 分享单个表情包
   * @param {Object} emoji - 表情包数据
   * @param {Object} options - 分享选项
   */
  async shareEmoji(emoji, options = {}) {
    try {
      console.log('📤 分享表情包:', emoji.title)
      
      const shareData = await this.generateEmojiShareData(emoji, options)
      
      // 记录分享统计
      this.recordShare('emoji', 'wechat')
      
      return new Promise((resolve, reject) => {
        wx.shareAppMessage({
          ...shareData,
          success: (res) => {
            console.log('✅ 表情包分享成功')
            this.onShareSuccess('emoji', emoji.id)
            resolve(res)
          },
          fail: (error) => {
            console.error('❌ 表情包分享失败:', error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('❌ 分享表情包失败:', error)
      throw error
    }
  },

  /**
   * 分享表情包合集
   * @param {Array} emojis - 表情包数组
   * @param {Object} options - 分享选项
   */
  async shareCollection(emojis, options = {}) {
    try {
      console.log('📤 分享表情包合集:', emojis.length, '个')
      
      const shareData = await this.generateCollectionShareData(emojis, options)
      
      // 记录分享统计
      this.recordShare('collection', 'wechat')
      
      return new Promise((resolve, reject) => {
        wx.shareAppMessage({
          ...shareData,
          success: (res) => {
            console.log('✅ 合集分享成功')
            this.onShareSuccess('collection', emojis.map(e => e.id).join(','))
            resolve(res)
          },
          fail: (error) => {
            console.error('❌ 合集分享失败:', error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('❌ 分享合集失败:', error)
      throw error
    }
  },

  /**
   * 分享到朋友圈
   * @param {Object} data - 分享数据
   * @param {Object} options - 分享选项
   */
  async shareToMoments(data, options = {}) {
    try {
      console.log('📤 分享到朋友圈')
      
      const shareImage = await this.generateShareImage(data, options)
      
      // 记录分享统计
      this.recordShare(data.type || 'app', 'moments')
      
      return new Promise((resolve, reject) => {
        wx.shareToMoments({
          imageUrl: shareImage,
          success: (res) => {
            console.log('✅ 朋友圈分享成功')
            this.onShareSuccess('moments', data.id)
            resolve(res)
          },
          fail: (error) => {
            console.error('❌ 朋友圈分享失败:', error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('❌ 分享到朋友圈失败:', error)
      throw error
    }
  },

  /**
   * 生成表情包分享数据
   * @param {Object} emoji - 表情包数据
   * @param {Object} options - 选项
   */
  async generateEmojiShareData(emoji, options = {}) {
    const template = this._templates.emoji
    
    const shareData = {
      title: this.processTemplate(template.title, {
        title: emoji.title,
        appName: this._config.appName
      }),
      desc: this.processTemplate(template.desc, {
        appName: this._config.appName
      }),
      path: `/pages/detail/detail?id=${emoji.id}&from=share`,
      imageUrl: emoji.imageUrl || emoji.url
    }
    
    // 如果需要生成自定义分享图片
    if (options.generateImage) {
      shareData.imageUrl = await this.generateEmojiShareImage(emoji, options)
    }
    
    return shareData
  },

  /**
   * 生成合集分享数据
   * @param {Array} emojis - 表情包数组
   * @param {Object} options - 选项
   */
  async generateCollectionShareData(emojis, options = {}) {
    const template = this._templates.collection
    
    const shareData = {
      title: this.processTemplate(template.title, {
        count: emojis.length,
        appName: this._config.appName
      }),
      desc: this.processTemplate(template.desc, {
        appName: this._config.appName
      }),
      path: `/pages/index/index?collection=${emojis.map(e => e.id).join(',')}&from=share`,
      imageUrl: await this.generateCollectionShareImage(emojis, options)
    }
    
    return shareData
  },

  /**
   * 生成表情包分享图片
   * @param {Object} emoji - 表情包数据
   * @param {Object} options - 选项
   */
  async generateEmojiShareImage(emoji, options = {}) {
    try {
      // 这里可以实现自定义分享图片生成
      // 暂时返回原图
      return emoji.imageUrl || emoji.url
    } catch (error) {
      console.error('❌ 生成分享图片失败:', error)
      return emoji.imageUrl || emoji.url
    }
  },

  /**
   * 生成合集分享图片
   * @param {Array} emojis - 表情包数组
   * @param {Object} options - 选项
   */
  async generateCollectionShareImage(emojis, options = {}) {
    try {
      // 这里可以实现合集分享图片生成
      // 暂时返回第一个表情包的图片
      return emojis[0]?.imageUrl || emojis[0]?.url || ''
    } catch (error) {
      console.error('❌ 生成合集分享图片失败:', error)
      return emojis[0]?.imageUrl || emojis[0]?.url || ''
    }
  },

  /**
   * 生成通用分享图片
   * @param {Object} data - 数据
   * @param {Object} options - 选项
   */
  async generateShareImage(data, options = {}) {
    try {
      // 这里可以实现通用分享图片生成
      // 暂时返回默认图片
      return data.imageUrl || ''
    } catch (error) {
      console.error('❌ 生成分享图片失败:', error)
      return ''
    }
  },

  /**
   * 处理分享模板
   * @param {string} template - 模板字符串
   * @param {Object} data - 数据对象
   */
  processTemplate(template, data) {
    let result = template
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `#{${key}}#`
      result = result.replace(new RegExp(placeholder, 'g'), value)
    }
    
    return result
  },

  /**
   * 设置全局分享
   */
  setupGlobalShare() {
    // 设置默认的全局分享
    const globalShareData = {
      title: this._config.defaultTitle,
      desc: this._config.defaultDesc,
      path: '/pages/index/index?from=share'
    }
    
    // 这里可以设置全局分享配置
    console.log('🌐 全局分享配置完成')
  },

  /**
   * 记录分享统计
   * @param {string} type - 分享类型
   * @param {string} platform - 分享平台
   */
  recordShare(type, platform) {
    if (!this._config.trackShares) return
    
    this._stats.totalShares++
    this._stats.sharesByType[type] = (this._stats.sharesByType[type] || 0) + 1
    this._stats.sharesByPlatform[platform] = (this._stats.sharesByPlatform[platform] || 0) + 1
    
    // 保存统计数据
    this.saveShareStats()
    
    console.log('📊 分享统计更新:', { type, platform })
  },

  /**
   * 分享成功回调
   * @param {string} type - 分享类型
   * @param {string} id - 内容ID
   */
  onShareSuccess(type, id) {
    // 可以在这里实现分享成功后的逻辑
    // 比如积分奖励、分享记录等
    console.log('🎉 分享成功:', { type, id })
  },

  /**
   * 加载分享统计
   */
  loadShareStats() {
    try {
      const stats = wx.getStorageSync('share_stats')
      if (stats) {
        this._stats = { ...this._stats, ...stats }
      }
    } catch (error) {
      console.error('❌ 加载分享统计失败:', error)
    }
  },

  /**
   * 保存分享统计
   */
  saveShareStats() {
    try {
      wx.setStorageSync('share_stats', this._stats)
    } catch (error) {
      console.error('❌ 保存分享统计失败:', error)
    }
  },

  /**
   * 获取分享统计
   */
  getShareStats() {
    return { ...this._stats }
  },

  /**
   * 清除分享统计
   */
  clearShareStats() {
    this._stats = {
      totalShares: 0,
      sharesByType: {
        emoji: 0,
        collection: 0,
        app: 0
      },
      sharesByPlatform: {
        wechat: 0,
        moments: 0,
        other: 0
      }
    }
    
    this.saveShareStats()
    console.log('🧹 分享统计已清除')
  }
}

module.exports = ShareManager
