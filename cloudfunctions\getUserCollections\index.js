// 云函数入口文件 - 获取用户收藏列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { page = 1, limit = 20 } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 获取用户收藏的表情包ID列表
    const collectResult = await db.collection('user_collections').where({
      userId: OPENID
    }).orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()
    
    if (collectResult.data.length === 0) {
      return {
        success: true,
        data: []
      }
    }
    
    // 获取表情包详情
    const emojiIds = collectResult.data.map(collect => collect.emojiId)
    const emojiResult = await db.collection('emojis').where({
      _id: db.command.in(emojiIds)
    }).get()
    
    // 按收藏时间排序
    const sortedEmojis = collectResult.data.map(collect => {
      const emoji = emojiResult.data.find(e => e._id === collect.emojiId)
      return emoji ? {
        ...emoji,
        id: emoji._id,
        collectedAt: collect.createTime
      } : null
    }).filter(Boolean)
    
    return {
      success: true,
      data: sortedEmojis
    }
  } catch (error) {
    console.error('获取用户收藏列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}