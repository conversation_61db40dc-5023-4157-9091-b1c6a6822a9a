/* 自定义TabBar样式 - 炫酷动效版 */

.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border-top: 1px solid rgba(255, 140, 0, 0.1);
  box-shadow: 0 -2px 25px rgba(255, 140, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 背景光晕效果 */
.tab-bar-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 248, 240, 0.8) 100%);
  pointer-events: none;
  z-index: -1;
}

/* TabBar项目 */
.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

/* 选中状态背景光晕 */
.item-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 140, 0, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  z-index: -1;
}

.item-glow.active {
  width: 45px;
  height: 45px;
  animation: glowPulse 2s ease-in-out infinite alternate;
}

/* 光晕脉动动画 */
@keyframes glowPulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 图标容器 */
.icon-container {
  position: relative;
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

/* 图标样式 */
.icon {
  width: 100%;
  height: 100%;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center bottom;
}

/* 选中状态图标动效 */
.icon-selected {
  transform: translateY(-3px) scale(1.15);
  animation: iconBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 弹跳动画 */
@keyframes iconBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  30% {
    transform: translateY(-5px) scale(1.2);
  }
  50% {
    transform: translateY(-2px) scale(1.1);
  }
  70% {
    transform: translateY(-4px) scale(1.18);
  }
  100% {
    transform: translateY(-3px) scale(1.15);
  }
}

/* 点击波纹效果 */
.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 140, 0, 0.3);
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.ripple-active {
  animation: rippleEffect 0.6s ease-out;
}

/* 波纹动画 */
@keyframes rippleEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 50px;
    height: 50px;
    opacity: 0;
  }
}

/* 文字样式 */
.text {
  font-size: 10px;
  color: #8A8A8A;
  transition: all 0.3s ease;
  font-weight: 500;
  letter-spacing: 0.2px;
}

/* 选中状态文字 */
.text-selected {
  color: #FF8C00;
  font-weight: 600;
  transform: scale(1.05);
  text-shadow: 0 0 8px rgba(255, 140, 0, 0.3);
}

/* 点击动画状态 */
.tab-bar-item.animating {
  transform: scale(0.95);
}

.tab-bar-item.animating .icon {
  transform: translateY(-1px) scale(1.05);
}

/* 选中状态整体效果 */
.tab-bar-item.selected {
  transform: translateY(-1px);
}

/* 悬浮效果（如果支持触摸反馈） */
.tab-bar-item:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .tab-bar {
    height: 48px;
  }
  
  .icon-container {
    width: 22px;
    height: 22px;
  }
  
  .text {
    font-size: 9px;
  }
  
  .icon-selected {
    transform: translateY(-2px) scale(1.1);
  }
}

/* 横屏适配 */
@media (orientation: landscape) {
  .tab-bar {
    height: 45px;
  }
  
  .text {
    font-size: 9px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .tab-bar-item,
  .icon,
  .text,
  .item-glow {
    transition: none !important;
    animation: none !important;
  }
}