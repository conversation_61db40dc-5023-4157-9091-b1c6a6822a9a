@echo off
chcp 65001 >nul
echo ========================================
echo 🔄 启动自动同步系统
echo ========================================
echo.

echo 正在停止现有进程...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo 创建必要目录...
if not exist "admin-unified\data" mkdir "admin-unified\data"
if not exist "miniprogram-data" mkdir "miniprogram-data"

echo.
echo 🚀 启动服务...
echo.

echo 1. 启动管理后台服务器 (端口8001)...
cd admin-unified
start /b node fixed-server.js
cd ..

echo 2. 启动小程序数据服务 (端口8003)...
start /b node miniprogram-data-service.js

echo 3. 等待服务启动...
timeout /t 5 /nobreak >nul

echo 4. 测试服务连接...
echo.

echo 测试管理后台服务...
curl -s http://localhost:8001/health
echo.

echo 测试小程序数据服务...
curl -s http://localhost:8003/health
echo.

echo 5. 打开管理界面...
start http://localhost:8001/index-fixed.html

echo.
echo ========================================
echo ✅ 自动同步系统启动完成！
echo ========================================
echo.
echo 🔄 数据流向:
echo   管理后台操作 → JSON文件保存 → 自动同步 → 小程序数据格式
echo.
echo 📊 服务地址:
echo   管理后台: http://localhost:8001/index-fixed.html
echo   小程序API: http://localhost:8003/wx-cloud-api
echo   数据统计: http://localhost:8003/api/stats
echo.
echo 🎯 现在你可以:
echo   1. 在管理后台添加/编辑/删除数据
echo   2. 数据会自动保存到 miniprogram-data/ 目录
echo   3. 小程序可以通过 http://localhost:8003 获取数据
echo   4. 实现真正的自动同步！
echo.
echo 📝 小程序配置:
echo   将小程序的云函数调用改为HTTP请求:
echo   wx.request({
echo     url: 'http://localhost:8003/wx-cloud-api',
echo     method: 'POST',
echo     data: { action: 'getCategories' }
echo   })
echo.
echo ⚠️  注意: 
echo   - 关闭此窗口会停止所有服务
echo   - 数据会自动同步，无需手动操作
echo   - 查看 miniprogram-data/ 目录确认同步状态
echo ========================================

echo.
echo 按任意键查看实时同步日志...
pause >nul

echo.
echo 🔍 实时同步监控:
echo ========================================

:monitor
echo [%time%] 检查服务状态...

curl -s http://localhost:8001/health | findstr "ok" >nul
if %errorlevel% equ 0 (
    echo [%time%] ✅ 管理后台服务正常
) else (
    echo [%time%] ❌ 管理后台服务异常
)

curl -s http://localhost:8003/health | findstr "ok" >nul
if %errorlevel% equ 0 (
    echo [%time%] ✅ 小程序数据服务正常
) else (
    echo [%time%] ❌ 小程序数据服务异常
)

echo [%time%] 检查同步状态...
curl -s http://localhost:8003/api/sync-status >nul
if %errorlevel% equ 0 (
    echo [%time%] ✅ 数据同步正常
) else (
    echo [%time%] ⚠️ 数据同步状态未知
)

echo.
timeout /t 10 /nobreak >nul
goto monitor
