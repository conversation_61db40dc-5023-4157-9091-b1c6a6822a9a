/**
 * 小程序数据服务
 * 提供HTTP API让小程序能够获取管理后台同步的数据
 */

const express = require('express');
const path = require('path');
const cors = require('cors');
const fs = require('fs');

const app = express();
const PORT = 8003;

// 数据目录
const MINIPROGRAM_DATA_DIR = path.join(__dirname, 'miniprogram-data');

// 确保数据目录存在
if (!fs.existsSync(MINIPROGRAM_DATA_DIR)) {
  fs.mkdirSync(MINIPROGRAM_DATA_DIR, { recursive: true });
}

// 中间件
app.use(cors());
app.use(express.json());

// 读取小程序数据
function loadMiniProgramData(collection) {
  const filePath = path.join(MINIPROGRAM_DATA_DIR, `${collection}.json`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`数据文件不存在: ${collection}.json`);
    return [];
  }
  
  try {
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    return data;
  } catch (error) {
    console.error(`读取数据失败 [${collection}]:`, error);
    return [];
  }
}

// 健康检查
app.get('/health', (req, res) => {
  const syncReport = getSyncReport();
  res.json({
    status: 'ok',
    message: '小程序数据服务正常',
    timestamp: new Date().toISOString(),
    syncReport
  });
});

// 获取同步报告
function getSyncReport() {
  const reportFile = path.join(MINIPROGRAM_DATA_DIR, 'sync-report.json');
  
  if (!fs.existsSync(reportFile)) {
    return { status: 'no_sync_data' };
  }
  
  try {
    return JSON.parse(fs.readFileSync(reportFile, 'utf8'));
  } catch (error) {
    return { status: 'error', error: error.message };
  }
}

// 模拟云函数API - 让小程序能够无缝切换
app.post('/wx-cloud-api', (req, res) => {
  const { action, data = {} } = req.body;
  
  console.log(`📱 小程序API调用: ${action}`, data);
  
  try {
    let result;
    
    switch (action) {
      case 'getCategories':
        result = {
          success: true,
          data: loadMiniProgramData('categories')
        };
        break;
        
      case 'getEmojis':
        const { category = 'all', page = 1, limit = 20 } = data;
        let emojis = loadMiniProgramData('emojis');
        
        // 分类筛选
        if (category && category !== 'all') {
          emojis = emojis.filter(emoji => emoji.category === category);
        }
        
        // 分页
        const start = (page - 1) * limit;
        const paginatedEmojis = emojis.slice(start, start + limit);
        
        result = {
          success: true,
          data: paginatedEmojis,
          total: emojis.length,
          page,
          limit,
          hasMore: start + limit < emojis.length
        };
        break;
        
      case 'getBanners':
        result = {
          success: true,
          data: loadMiniProgramData('banners')
        };
        break;
        
      case 'getEmojiDetail':
        const { id } = data;
        const allEmojis = loadMiniProgramData('emojis');
        const emoji = allEmojis.find(e => e._id === id);
        
        result = {
          success: !!emoji,
          data: emoji || null
        };
        break;
        
      case 'searchEmojis':
        const { keyword, category: searchCategory } = data;
        let searchEmojis = loadMiniProgramData('emojis');
        
        if (keyword) {
          searchEmojis = searchEmojis.filter(emoji => 
            emoji.title.includes(keyword) || 
            (emoji.tags && emoji.tags.some(tag => tag.includes(keyword)))
          );
        }
        
        if (searchCategory && searchCategory !== 'all') {
          searchEmojis = searchEmojis.filter(emoji => emoji.category === searchCategory);
        }
        
        result = {
          success: true,
          data: searchEmojis,
          total: searchEmojis.length
        };
        break;
        
      case 'ping':
        result = {
          success: true,
          message: 'pong',
          timestamp: new Date().toISOString(),
          dataSource: 'miniprogram-data-service'
        };
        break;
        
      default:
        result = {
          success: false,
          message: `未知操作: ${action}`
        };
    }
    
    res.json(result);
    
  } catch (error) {
    console.error('API调用失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 直接数据API
app.get('/api/:collection', (req, res) => {
  const { collection } = req.params;
  const data = loadMiniProgramData(collection);
  
  res.json({
    success: true,
    data,
    count: data.length,
    timestamp: new Date().toISOString()
  });
});

// 数据统计API
app.get('/api/stats', (req, res) => {
  const categories = loadMiniProgramData('categories');
  const emojis = loadMiniProgramData('emojis');
  const banners = loadMiniProgramData('banners');
  
  const stats = {
    categories: categories.length,
    emojis: emojis.length,
    banners: banners.length,
    totalLikes: emojis.reduce((sum, emoji) => sum + (emoji.likes || 0), 0),
    totalDownloads: emojis.reduce((sum, emoji) => sum + (emoji.downloads || 0), 0),
    lastUpdate: new Date().toISOString()
  };
  
  res.json({
    success: true,
    data: stats
  });
});

// 同步状态API
app.get('/api/sync-status', (req, res) => {
  const syncReport = getSyncReport();
  res.json({
    success: true,
    data: syncReport
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('========================================');
  console.log('📱 小程序数据服务已启动');
  console.log(`🌐 访问地址: http://localhost:${PORT}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/health`);
  console.log(`📋 数据统计: http://localhost:${PORT}/api/stats`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
  console.log('');
  console.log('✨ 功能:');
  console.log('   - 📦 读取管理后台同步的数据');
  console.log('   - 🔄 提供小程序兼容的API');
  console.log('   - 📊 数据统计和同步状态');
  console.log('   - 🚀 无缝数据切换');
  console.log('========================================');
});

app.on('error', (err) => {
  console.error('服务器错误:', err);
});
