// 测试我的点赞和收藏页面状态显示
// 在微信开发者工具控制台中运行

console.log('🔍 测试我的点赞和收藏页面状态显示')

// 获取StateManager实例
const { StateManager } = require('../../utils/stateManager.js')

// 测试数据
const testEmojiIds = ['1', '2', '3', '4', '5']

// 模拟一些测试状态
console.log('设置测试状态...')
StateManager.toggleLike('1')  // 点赞表情包1
StateManager.toggleLike('3')  // 点赞表情包3
StateManager.toggleCollect('2')  // 收藏表情包2
StateManager.toggleCollect('4')  // 收藏表情包4

// 检查状态
console.log('检查状态:')
testEmojiIds.forEach(id => {
  const state = StateManager.getEmojiState(id)
  console.log(`表情包${id}:`, {
    isLiked: state.isLiked,
    isCollected: state.isCollected,
    显示点赞图标: state.isLiked ? '❤️' : '🤍',
    显示收藏图标: state.isCollected ? '⭐' : '☆'
  })
})

// 检查列表
console.log('\n点赞列表:', StateManager.getLikedEmojis())
console.log('收藏列表:', StateManager.getCollectedEmojis())

console.log('\n✅ 测试完成')