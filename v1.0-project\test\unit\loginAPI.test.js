// loginAPI 单元测试
const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const { mockJWTToken, mockAdminInfo } = require('../mocks');

// 模拟云开发SDK
jest.mock('wx-server-sdk', () => ({
  init: jest.fn(),
  database: jest.fn(() => ({
    collection: jest.fn(() => ({
      where: jest.fn(() => ({
        get: jest.fn()
      })),
      add: jest.fn(),
      update: jest.fn()
    }))
  }))
}));

// 模拟JWT库
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(() => mockJWTToken()),
  verify: jest.fn(() => mockAdminInfo()),
  decode: jest.fn(() => mockAdminInfo())
}));

describe('loginAPI 单元测试', () => {
  let loginAPI;
  
  beforeEach(() => {
    // 重新加载模块以确保每个测试的独立性
    jest.resetModules();
    loginAPI = require('../../cloudfunctions/loginAPI/index');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('T1.1 认证功能测试', () => {
    test('T1.1.1 正确的用户名和密码应该返回有效令牌', async () => {
      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('token');
      expect(result.data).toHaveProperty('adminId');
      expect(result.data).toHaveProperty('expiresIn');
      expect(result.data.token).toBeValidJWT();
      expect(result.message).toBe('登录成功');
    });

    test('T1.1.2 错误的用户名应该返回认证失败', async () => {
      const event = {
        action: 'login',
        username: 'wronguser',
        password: 'admin123456'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('用户名或密码错误');
      expect(result.code).toBe('INVALID_CREDENTIALS');
    });

    test('T1.1.3 错误的密码应该返回认证失败', async () => {
      const event = {
        action: 'login',
        username: 'admin',
        password: 'wrongpassword'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('用户名或密码错误');
      expect(result.code).toBe('INVALID_CREDENTIALS');
    });

    test('T1.1.4 空用户名应该返回参数错误', async () => {
      const event = {
        action: 'login',
        username: '',
        password: 'admin123456'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('用户名不能为空');
      expect(result.code).toBe('INVALID_PARAMS');
    });

    test('T1.1.5 空密码应该返回参数错误', async () => {
      const event = {
        action: 'login',
        username: 'admin',
        password: ''
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('密码不能为空');
      expect(result.code).toBe('INVALID_PARAMS');
    });
  });

  describe('T1.2 令牌验证测试', () => {
    test('T1.2.1 有效令牌应该通过验证', async () => {
      const event = {
        action: 'validateToken',
        token: mockJWTToken()
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('adminId');
      expect(result.data).toHaveProperty('permissions');
      expect(result.data.isValid).toBe(true);
    });

    test('T1.2.2 无效令牌应该验证失败', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        throw new Error('invalid token');
      });

      const event = {
        action: 'validateToken',
        token: 'invalid.token.here'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('令牌无效');
      expect(result.code).toBe('INVALID_TOKEN');
    });

    test('T1.2.3 过期令牌应该验证失败', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        const error = new Error('jwt expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      const event = {
        action: 'validateToken',
        token: 'expired.token.here'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('令牌已过期');
      expect(result.code).toBe('TOKEN_EXPIRED');
    });

    test('T1.2.4 空令牌应该返回参数错误', async () => {
      const event = {
        action: 'validateToken',
        token: ''
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('令牌不能为空');
      expect(result.code).toBe('INVALID_PARAMS');
    });
  });

  describe('T1.3 令牌刷新测试', () => {
    test('T1.3.1 有效令牌应该能够刷新', async () => {
      const event = {
        action: 'refreshToken',
        token: mockJWTToken()
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('token');
      expect(result.data).toHaveProperty('expiresIn');
      expect(result.data.token).toBeValidJWT();
      expect(result.message).toBe('令牌刷新成功');
    });

    test('T1.3.2 无效令牌不应该能够刷新', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        throw new Error('invalid token');
      });

      const event = {
        action: 'refreshToken',
        token: 'invalid.token.here'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('令牌无效');
      expect(result.code).toBe('INVALID_TOKEN');
    });

    test('T1.3.3 刷新后的令牌应该与原令牌不同', async () => {
      const jwt = require('jsonwebtoken');
      let callCount = 0;
      jwt.sign.mockImplementation(() => {
        callCount++;
        return `mock.jwt.token.${callCount}`;
      });

      const event = {
        action: 'refreshToken',
        token: 'mock.jwt.token.1'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data.token).not.toBe('mock.jwt.token.1');
      expect(result.data.token).toBe('mock.jwt.token.2');
    });
  });

  describe('T1.4 登出功能测试', () => {
    test('T1.4.1 有效令牌应该能够登出', async () => {
      const event = {
        action: 'logout',
        token: mockJWTToken()
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.message).toBe('登出成功');
    });

    test('T1.4.2 无效令牌也应该能够登出（幂等操作）', async () => {
      const event = {
        action: 'logout',
        token: 'invalid.token.here'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.message).toBe('登出成功');
    });
  });

  describe('T1.5 错误处理测试', () => {
    test('T1.5.1 未知操作应该返回错误', async () => {
      const event = {
        action: 'unknownAction'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('未知的操作类型');
      expect(result.code).toBe('UNKNOWN_ACTION');
    });

    test('T1.5.2 缺少action参数应该返回错误', async () => {
      const event = {};

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('缺少操作类型');
      expect(result.code).toBe('MISSING_ACTION');
    });

    test('T1.5.3 系统异常应该返回内部错误', async () => {
      const jwt = require('jsonwebtoken');
      jwt.sign.mockImplementation(() => {
        throw new Error('System error');
      });

      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      const result = await loginAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('T1.6 安全性测试', () => {
    test('T1.6.1 密码不应该出现在日志中', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      await loginAPI.main(event, {});

      const logCalls = consoleSpy.mock.calls.flat().join(' ');
      expect(logCalls).not.toContain('admin123456');

      consoleSpy.mockRestore();
    });

    test('T1.6.2 令牌应该包含必要的声明', async () => {
      const jwt = require('jsonwebtoken');
      let signPayload;
      jwt.sign.mockImplementation((payload) => {
        signPayload = payload;
        return mockJWTToken();
      });

      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      await loginAPI.main(event, {});

      expect(signPayload).toHaveProperty('adminId');
      expect(signPayload).toHaveProperty('permissions');
      expect(signPayload).toHaveProperty('iat');
    });

    test('T1.6.3 令牌应该有合理的过期时间', async () => {
      const jwt = require('jsonwebtoken');
      let signOptions;
      jwt.sign.mockImplementation((payload, secret, options) => {
        signOptions = options;
        return mockJWTToken();
      });

      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      await loginAPI.main(event, {});

      expect(signOptions).toHaveProperty('expiresIn');
      expect(signOptions.expiresIn).toBe('24h');
    });
  });

  describe('T1.7 性能测试', () => {
    test('T1.7.1 登录操作应该在合理时间内完成', async () => {
      const startTime = Date.now();

      const event = {
        action: 'login',
        username: 'admin',
        password: 'admin123456'
      };

      await loginAPI.main(event, {});

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    test('T1.7.2 令牌验证应该在合理时间内完成', async () => {
      const startTime = Date.now();

      const event = {
        action: 'validateToken',
        token: mockJWTToken()
      };

      await loginAPI.main(event, {});

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(500); // 应该在0.5秒内完成
    });
  });
});
