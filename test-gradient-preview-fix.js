// 专门测试渐变预览功能
const { chromium } = require('playwright');

async function testGradientPreviewFix() {
    console.log('🎨 专门测试渐变预览功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('渐变') || text.includes('预览') || text.includes('gradient')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：进入分类管理并创建新分类');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(5000);
        
        // 创建新分类
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        await addCategoryBtn.click();
        await page.waitForTimeout(3000);
        
        console.log('\n📍 第二步：检查渐变表单元素');
        
        // 检查表单元素
        const formElements = await page.evaluate(() => {
            const gradientPreset = document.querySelector('#category-gradient-preset');
            const gradientInput = document.querySelector('#category-gradient');
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                hasPreset: !!gradientPreset,
                hasInput: !!gradientInput,
                hasPreview: !!gradientPreview,
                presetOptions: gradientPreset ? Array.from(gradientPreset.options).map(opt => ({
                    value: opt.value,
                    text: opt.textContent
                })) : [],
                inputValue: gradientInput ? gradientInput.value : 'N/A',
                previewStyle: gradientPreview ? {
                    background: gradientPreview.style.background,
                    color: gradientPreview.style.color,
                    textContent: gradientPreview.textContent
                } : null
            };
        });
        
        console.log('📊 渐变表单元素检查:');
        console.log(`预设选择器存在: ${formElements.hasPreset}`);
        console.log(`渐变输入框存在: ${formElements.hasInput}`);
        console.log(`渐变预览存在: ${formElements.hasPreview}`);
        console.log(`预设选项数量: ${formElements.presetOptions.length}`);
        console.log(`当前输入值: ${formElements.inputValue}`);
        
        if (formElements.hasPreview) {
            console.log('预览当前状态:');
            console.log(`  背景: ${formElements.previewStyle.background}`);
            console.log(`  颜色: ${formElements.previewStyle.color}`);
            console.log(`  文本: ${formElements.previewStyle.textContent}`);
        }
        
        console.log('\n📍 第三步：测试预设渐变选择');
        
        if (formElements.hasPreset && formElements.presetOptions.length > 1) {
            // 选择第一个预设渐变（跳过空选项）
            const firstPreset = formElements.presetOptions.find(opt => opt.value);
            if (firstPreset) {
                await page.selectOption('#category-gradient-preset', firstPreset.value);
                console.log(`✅ 已选择预设渐变: ${firstPreset.text}`);
                
                await page.waitForTimeout(1000);
                
                // 检查预设选择后的状态
                const afterPresetState = await page.evaluate(() => {
                    const gradientInput = document.querySelector('#category-gradient');
                    const gradientPreview = document.querySelector('#gradient-preview');
                    
                    return {
                        inputValue: gradientInput ? gradientInput.value : 'N/A',
                        previewStyle: gradientPreview ? {
                            background: gradientPreview.style.background,
                            color: gradientPreview.style.color,
                            textContent: gradientPreview.textContent
                        } : null
                    };
                });
                
                console.log('📊 预设选择后状态:');
                console.log(`输入框值: ${afterPresetState.inputValue}`);
                if (afterPresetState.previewStyle) {
                    console.log(`预览背景: ${afterPresetState.previewStyle.background}`);
                    console.log(`预览颜色: ${afterPresetState.previewStyle.color}`);
                    console.log(`预览文本: ${afterPresetState.previewStyle.textContent}`);
                    
                    if (afterPresetState.previewStyle.background && 
                        afterPresetState.previewStyle.background !== '#f5f5f5') {
                        console.log('✅ 预设渐变预览功能正常');
                    } else {
                        console.log('🔴 预设渐变预览功能异常');
                    }
                }
            }
        }
        
        console.log('\n📍 第四步：测试自定义渐变输入');
        
        // 清空输入框并输入自定义渐变
        await page.fill('#category-gradient', '');
        await page.waitForTimeout(500);
        
        const customGradient = 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%)';
        await page.fill('#category-gradient', customGradient);
        console.log(`✅ 已输入自定义渐变: ${customGradient}`);
        
        await page.waitForTimeout(1000);
        
        // 检查自定义渐变后的状态
        const afterCustomState = await page.evaluate(() => {
            const gradientInput = document.querySelector('#category-gradient');
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                inputValue: gradientInput ? gradientInput.value : 'N/A',
                previewStyle: gradientPreview ? {
                    background: gradientPreview.style.background,
                    color: gradientPreview.style.color,
                    textContent: gradientPreview.textContent
                } : null
            };
        });
        
        console.log('📊 自定义渐变后状态:');
        console.log(`输入框值: ${afterCustomState.inputValue}`);
        if (afterCustomState.previewStyle) {
            console.log(`预览背景: ${afterCustomState.previewStyle.background}`);
            console.log(`预览颜色: ${afterCustomState.previewStyle.color}`);
            console.log(`预览文本: ${afterCustomState.previewStyle.textContent}`);
            
            if (afterCustomState.previewStyle.background === customGradient) {
                console.log('✅ 自定义渐变预览功能正常');
            } else {
                console.log('🔴 自定义渐变预览功能异常');
            }
        }
        
        console.log('\n📍 第五步：测试错误格式处理');
        
        // 输入错误的渐变格式
        const invalidGradient = 'invalid-gradient-format';
        await page.fill('#category-gradient', invalidGradient);
        console.log(`✅ 已输入错误格式: ${invalidGradient}`);
        
        await page.waitForTimeout(1000);
        
        // 检查错误格式处理
        const afterErrorState = await page.evaluate(() => {
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                previewStyle: gradientPreview ? {
                    background: gradientPreview.style.background,
                    color: gradientPreview.style.color,
                    textContent: gradientPreview.textContent
                } : null
            };
        });
        
        console.log('📊 错误格式处理状态:');
        if (afterErrorState.previewStyle) {
            console.log(`预览背景: ${afterErrorState.previewStyle.background}`);
            console.log(`预览颜色: ${afterErrorState.previewStyle.color}`);
            console.log(`预览文本: ${afterErrorState.previewStyle.textContent}`);
            
            if (afterErrorState.previewStyle.textContent.includes('错误')) {
                console.log('✅ 错误格式处理功能正常');
            } else {
                console.log('🔴 错误格式处理功能异常');
            }
        }
        
        console.log('\n📍 第六步：测试保存和表格显示');
        
        // 恢复正确的渐变值
        await page.fill('#category-gradient', customGradient);
        await page.fill('#category-name', '渐变预览测试分类');
        await page.fill('#category-description', '测试渐变预览功能');
        
        // 检查状态选择器的实际选项
        const statusOptions = await page.evaluate(() => {
            const statusSelect = document.querySelector('#category-status');
            if (!statusSelect) return [];
            return Array.from(statusSelect.options).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        });
        
        console.log('📊 状态选择器选项:');
        statusOptions.forEach(opt => {
            console.log(`  ${opt.value}: ${opt.text}`);
        });
        
        // 选择第一个非空状态
        const validStatus = statusOptions.find(opt => opt.value && opt.value !== '');
        if (validStatus) {
            await page.selectOption('#category-status', validStatus.value);
            console.log(`✅ 已选择状态: ${validStatus.text}`);
        }
        
        // 保存分类
        const saveResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveResult.success) {
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000);
            
            // 检查表格中的渐变显示
            const tableGradientDisplay = await page.evaluate(() => {
                const categoryTable = document.querySelector('#category-content table');
                if (!categoryTable) return { error: '未找到分类表格' };
                
                const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
                const lastRow = rows[rows.length - 1];
                
                if (!lastRow) return { error: '未找到表格行' };
                
                const nameCell = lastRow.querySelector('td:nth-child(3)');
                const gradientCell = lastRow.querySelector('td:nth-child(4)');
                
                return {
                    found: true,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    gradientHTML: gradientCell ? gradientCell.innerHTML : 'N/A',
                    hasGradientStyle: gradientCell ? gradientCell.innerHTML.includes('gradient') : false
                };
            });
            
            console.log('\n📊 表格渐变显示检查:');
            if (tableGradientDisplay.found) {
                console.log(`分类名称: ${tableGradientDisplay.name}`);
                console.log(`渐变HTML: ${tableGradientDisplay.gradientHTML}`);
                console.log(`有渐变样式: ${tableGradientDisplay.hasGradientStyle}`);
                
                if (tableGradientDisplay.name.includes('渐变预览测试') && 
                    tableGradientDisplay.hasGradientStyle) {
                    console.log('✅ 表格渐变显示功能正常');
                } else {
                    console.log('🔴 表格渐变显示功能异常');
                }
            } else {
                console.log(`表格检查失败: ${tableGradientDisplay.error}`);
            }
        }
        
        // 截图
        await page.screenshot({ path: 'gradient-preview-fix-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: gradient-preview-fix-test.png');
        
        console.log('\n🎯 渐变预览功能测试总结:');
        
        const previewWorking = afterCustomState.previewStyle && 
            afterCustomState.previewStyle.background === customGradient;
        const errorHandling = afterErrorState.previewStyle && 
            afterErrorState.previewStyle.textContent.includes('错误');
        const tableDisplay = tableGradientDisplay.found && 
            tableGradientDisplay.hasGradientStyle;
        
        console.log(`预览功能: ${previewWorking ? '✅ 正常' : '🔴 异常'}`);
        console.log(`错误处理: ${errorHandling ? '✅ 正常' : '🔴 异常'}`);
        console.log(`表格显示: ${tableDisplay ? '✅ 正常' : '🔴 异常'}`);
        
        if (previewWorking && errorHandling && tableDisplay) {
            console.log('\n🎉 渐变预览功能完全正常！');
            return { success: true, allWorking: true };
        } else {
            console.log('\n⚠️ 渐变预览功能存在问题');
            return { success: true, allWorking: false, issues: {
                preview: !previewWorking,
                errorHandling: !errorHandling,
                tableDisplay: !tableDisplay
            }};
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'gradient-preview-fix-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testGradientPreviewFix().then(result => {
    console.log('\n🎯 渐变预览修复测试最终结果:', result);
}).catch(console.error);
