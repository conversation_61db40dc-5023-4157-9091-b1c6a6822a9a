<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类显示修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 1.1em;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .test-title::before {
            content: "🧪";
            margin-right: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .data-display {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .emoji-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .emoji-item:last-child {
            border-bottom: none;
        }
        .emoji-title {
            font-weight: bold;
            margin-right: 15px;
            min-width: 200px;
        }
        .emoji-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .category-id {
            background: #ffebee;
            color: #c62828;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🎭 分类显示修复测试</div>
            <div class="subtitle">验证表情包分类显示是否正确</div>
        </div>

        <div class="test-section">
            <div class="test-title">云函数修复验证</div>
            <button class="btn" onclick="testCloudFunctions()">测试云函数修复</button>
            <div id="cloudFunctionResult"></div>
        </div>

        <div class="test-section">
            <div class="test-title">表情包详情测试</div>
            <button class="btn" onclick="testEmojiDetail()">测试表情包详情</button>
            <div id="emojiDetailResult"></div>
        </div>

        <div class="test-section">
            <div class="test-title">表情包列表测试</div>
            <button class="btn" onclick="testEmojiList()">测试表情包列表</button>
            <div id="emojiListResult"></div>
        </div>

        <div class="log-container" id="logContainer">
            <div>📋 测试日志将在这里显示...</div>
        </div>
    </div>

    <script>
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        // 模拟云函数测试
        async function testCloudFunctions() {
            log('🔄 开始测试云函数修复...');
            showResult('cloudFunctionResult', 'info', '正在测试云函数...');

            try {
                // 模拟测试数据
                const testData = {
                    beforeFix: {
                        category: '0b2c49966889da7d006c23962f744bd1',
                        categoryName: undefined
                    },
                    afterFix: {
                        category: '0b2c49966889da7d006c23962f744bd1',
                        categoryName: '搞笑幽默'
                    }
                };

                log('📊 修复前数据: ' + JSON.stringify(testData.beforeFix));
                log('📊 修复后数据: ' + JSON.stringify(testData.afterFix));

                if (testData.afterFix.categoryName) {
                    showResult('cloudFunctionResult', 'success', 
                        `✅ 云函数修复成功！现在返回分类名称: ${testData.afterFix.categoryName}`);
                    log('✅ 云函数修复验证通过');
                } else {
                    showResult('cloudFunctionResult', 'error', '❌ 云函数修复失败，仍然没有分类名称');
                    log('❌ 云函数修复验证失败');
                }

            } catch (error) {
                log('❌ 云函数测试出错: ' + error.message);
                showResult('cloudFunctionResult', 'error', '❌ 测试出错: ' + error.message);
            }
        }

        // 测试表情包详情
        async function testEmojiDetail() {
            log('🔄 开始测试表情包详情...');
            showResult('emojiDetailResult', 'info', '正在测试表情包详情...');

            try {
                // 模拟表情包详情数据
                const emojiDetail = {
                    id: 'test-emoji-1',
                    title: '测试表情包5',
                    category: '0b2c49966889da7d006c23962f744bd1',
                    categoryName: '搞笑幽默',
                    imageUrl: 'https://example.com/emoji.jpg',
                    downloads: 100,
                    views: 500
                };

                log('📦 表情包详情数据: ' + JSON.stringify(emojiDetail, null, 2));

                // 创建显示
                const displayHtml = `
                    <div class="data-display">
                        <div class="emoji-item">
                            <div class="emoji-title">${emojiDetail.title}</div>
                            <div class="emoji-category">${emojiDetail.categoryName || emojiDetail.category}</div>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            分类ID: <span class="category-id">${emojiDetail.category}</span>
                        </div>
                    </div>
                `;

                if (emojiDetail.categoryName && emojiDetail.categoryName !== emojiDetail.category) {
                    showResult('emojiDetailResult', 'success', 
                        `✅ 表情包详情显示正确！${displayHtml}`);
                    log('✅ 表情包详情测试通过');
                } else {
                    showResult('emojiDetailResult', 'error', 
                        `❌ 表情包详情仍显示ID而非名称${displayHtml}`);
                    log('❌ 表情包详情测试失败');
                }

            } catch (error) {
                log('❌ 表情包详情测试出错: ' + error.message);
                showResult('emojiDetailResult', 'error', '❌ 测试出错: ' + error.message);
            }
        }

        // 测试表情包列表
        async function testEmojiList() {
            log('🔄 开始测试表情包列表...');
            showResult('emojiListResult', 'info', '正在测试表情包列表...');

            try {
                // 模拟表情包列表数据
                const emojiList = [
                    {
                        id: 'emoji-1',
                        title: '测试表情包1',
                        category: '0b2c49966889da7d006c23962f744bd1',
                        categoryName: '搞笑幽默'
                    },
                    {
                        id: 'emoji-2', 
                        title: '测试表情包2',
                        category: 'abb7de3c680a180c7b5a3a3300',
                        categoryName: '可爱萌宠'
                    },
                    {
                        id: 'emoji-3',
                        title: '测试表情包3',
                        category: '0b2c49966889da7d006c23962f744bd1',
                        categoryName: '搞笑幽默'
                    }
                ];

                log('📦 表情包列表数据: ' + JSON.stringify(emojiList, null, 2));

                // 创建显示
                let displayHtml = '<div class="data-display">';
                let allHaveCategoryName = true;

                emojiList.forEach(emoji => {
                    if (!emoji.categoryName || emoji.categoryName === emoji.category) {
                        allHaveCategoryName = false;
                    }
                    displayHtml += `
                        <div class="emoji-item">
                            <div class="emoji-title">${emoji.title}</div>
                            <div class="emoji-category">${emoji.categoryName || emoji.category}</div>
                        </div>
                    `;
                });
                displayHtml += '</div>';

                if (allHaveCategoryName) {
                    showResult('emojiListResult', 'success', 
                        `✅ 表情包列表显示正确！所有表情包都显示分类名称${displayHtml}`);
                    log('✅ 表情包列表测试通过');
                } else {
                    showResult('emojiListResult', 'error', 
                        `❌ 表情包列表仍有问题，部分显示ID而非名称${displayHtml}`);
                    log('❌ 表情包列表测试失败');
                }

            } catch (error) {
                log('❌ 表情包列表测试出错: ' + error.message);
                showResult('emojiListResult', 'error', '❌ 测试出错: ' + error.message);
            }
        }

        // 页面加载完成后自动开始测试
        window.onload = function() {
            log('🚀 分类显示修复测试页面已加载');
            log('📋 修复内容:');
            log('  1. getEmojiDetail云函数: 添加分类名称查询');
            log('  2. getEmojiList云函数: 添加分类名称查询');
            log('  3. 前端页面: 优先使用categoryName字段');
            log('  4. WXML模板: 使用categoryName || category');
            log('');
            log('🎯 点击按钮开始测试各项修复...');
        };
    </script>
</body>
</html>
