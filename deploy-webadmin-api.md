# WebAdminAPI云函数部署指南

## 问题描述
WebAdminAPI云函数调用时返回 `you can't request without auth` 错误，这是因为云函数的权限配置问题。

## 解决方案

### 方法1: 重新部署webAdminAPI云函数（推荐）

我已经修改了webAdminAPI云函数的配置，需要重新部署：

1. **打开微信开发者工具**
2. **打开项目**：选择当前项目目录
3. **部署云函数**：
   - 右键点击 `cloudfunctions/webAdminAPI` 文件夹
   - 选择"上传并部署：云端安装依赖"
   - 等待部署完成

### 方法2: 启用匿名登录（治本方案）

1. **打开云开发控制台**：
   - 在微信开发者工具中点击"云开发"
   - 或直接访问：https://console.cloud.tencent.com/tcb

2. **进入环境管理**：
   - 选择环境：`cloud1-5g6pvnpl88dc0142`

3. **启用匿名登录**：
   - 点击左侧菜单"环境" → "登录授权"
   - 找到"匿名登录"选项
   - 点击"启用"

4. **配置数据库权限**：
   - 点击左侧菜单"数据库" → "权限设置"
   - 确保匿名用户有读写权限

## 修改内容

### cloudfunctions/webAdminAPI/index.js
```javascript
// 添加了 traceUser: false 配置
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  // 允许未认证用户调用（Web端专用）
  traceUser: false
})
```

### cloudfunctions/webAdminAPI/package.json
```json
{
  "cloudbaseFunction": {
    "timeout": 60,
    "envVariables": {},
    "installDependency": true,
    "triggers": [],
    "permissions": {
      "openapi": [
        "cloudbase.database.read",
        "cloudbase.database.write"
      ]
    }
  }
}
```

## 验证修复效果

部署完成后，使用以下工具验证：

1. **打开诊断工具**：`diagnose-webadmin-api.html`
2. **点击"诊断WebAdminAPI"按钮**
3. **检查是否还有认证错误**

## 预期结果

修复成功后，应该看到：
- ✅ 云函数存在并可调用
- ✅ 返回正确的数据结果
- ❌ 不再出现 `you can't request without auth` 错误

## 如果仍有问题

如果重新部署后仍有问题，请：
1. 检查云函数部署日志
2. 确认环境ID是否正确：`cloud1-5g6pvnpl88dc0142`
3. 尝试启用匿名登录（方法2）
4. 联系技术支持检查云开发环境配置
