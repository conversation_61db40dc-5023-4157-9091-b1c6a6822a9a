// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 内置用户验证函数
async function verifyUser(openid) {
  try {
    if (!openid) {
      return { isValid: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isValid: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isValid = user.auth && user.auth.status === 'active'

    return {
      isValid,
      user,
      message: isValid ? '用户验证通过' : '用户状态异常'
    }
  } catch (error) {
    console.error('用户验证失败:', error)
    return { isValid: false, message: '用户验证失败' }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { emojiId, isCollected } = event
  const wxContext = cloud.getWXContext()

  console.log('收藏操作请求:', { emojiId, isCollected, openid: wxContext.OPENID })

  try {
    // 验证用户权限
    const authResult = await verifyUser(wxContext.OPENID)
    if (!authResult.isValid) {
      return {
        success: false,
        message: '用户未登录或权限不足'
      }
    }
    const user = authResult.user
    const userId = user._id

    // 检查表情包是否存在
    const emojiResult = await db.collection('emojis')
      .doc(emojiId)
      .get()

    if (!emojiResult.data) {
      return {
        success: false,
        message: '表情包不存在'
      }
    }

    // 查找现有的收藏记录
    const existingCollect = await db.collection('user_actions')
      .where({
        userId: userId,
        emojiId: emojiId,
        action: 'collect'
      })
      .get()

    let result

    if (isCollected) {
      // 添加收藏
      if (existingCollect.data.length === 0) {
        result = await db.collection('user_actions').add({
          data: {
            userId: userId,
            emojiId: emojiId,
            action: 'collect',
            timestamp: new Date(),
            userInfo: {
              openid: wxContext.OPENID,
              nickName: user.nickName || '匿名用户'
            }
          }
        })

        // 更新表情包收藏数
        await db.collection('emojis')
          .doc(emojiId)
          .update({
            data: {
              collections: _.inc(1)
            }
          })

        console.log('✅ 收藏成功:', result._id)
      } else {
        console.log('⚠️ 已经收藏过了')
      }
    } else {
      // 取消收藏
      if (existingCollect.data.length > 0) {
        result = await db.collection('user_actions')
          .doc(existingCollect.data[0]._id)
          .remove()

        // 更新表情包收藏数
        await db.collection('emojis')
          .doc(emojiId)
          .update({
            data: {
              collections: _.inc(-1)
            }
          })

        console.log('✅ 取消收藏成功')
      } else {
        console.log('⚠️ 没有收藏记录')
      }
    }

    // 获取更新后的表情包信息
    const updatedEmoji = await db.collection('emojis')
      .doc(emojiId)
      .get()

    return {
      success: true,
      isCollected: isCollected,
      collections: updatedEmoji.data.collections || 0,
      message: isCollected ? '收藏成功' : '取消收藏成功'
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}