/**
 * 增量同步管理器
 * 实现数据的增量同步和更新
 */

const { VersionManager } = require('./versionManager.js')
const { DataManager } = require('./newDataManager.js')

const IncrementalSync = {
  // 同步状态
  syncStatus: {
    isRunning: false,
    lastSyncTime: null,
    errorCount: 0,
    maxErrors: 3
  },

  // 同步配置
  config: {
    batchSize: 20,        // 批量同步大小
    timeout: 10000,       // 超时时间
    retryDelay: 2000,     // 重试延迟
    maxRetries: 3         // 最大重试次数
  },

  // 事件监听器
  listeners: new Map(),

  /**
   * 初始化增量同步
   */
  init(options = {}) {
    this.config = { ...this.config, ...options }
    console.log('🔄 增量同步管理器初始化完成')
  },

  /**
   * 获取增量数据
   */
  async getIncrementalData(type, lastSyncTime) {
    try {
      console.log(`📥 获取 ${type} 的增量数据，上次同步: ${lastSyncTime}`)
      
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getIncrementalData',
          type,
          since: lastSyncTime || 0
        }
      })

      if (result.result && result.result.success) {
        const changes = result.result.changes || []
        console.log(`📥 获取到 ${changes.length} 条 ${type} 增量数据`)
        return changes
      } else {
        throw new Error(result.result?.message || '获取增量数据失败')
      }
    } catch (error) {
      console.error(`❌ 获取 ${type} 增量数据失败:`, error.message)
      throw error
    }
  },

  /**
   * 应用增量更新
   */
  async applyIncrementalUpdate(type, changes) {
    if (!changes || changes.length === 0) {
      console.log(`📦 ${type} 无增量更新`)
      return { applied: 0, errors: 0 }
    }

    console.log(`🔄 应用 ${type} 增量更新，共 ${changes.length} 条`)
    
    let applied = 0
    let errors = 0

    for (const change of changes) {
      try {
        switch (change.operation) {
          case 'insert':
            await DataManager.addToCache(type, change.data)
            console.log(`➕ 插入 ${type}:`, change.data._id || change.data.id)
            break

          case 'update':
            await DataManager.updateCache(type, change.id, change.data)
            console.log(`✏️ 更新 ${type}:`, change.id)
            break

          case 'delete':
            await DataManager.removeFromCache(type, change.id)
            console.log(`🗑️ 删除 ${type}:`, change.id)
            break

          default:
            console.warn(`⚠️ 未知操作类型: ${change.operation}`)
            continue
        }

        applied++
      } catch (error) {
        console.error(`❌ 应用更新失败 ${type}:`, error.message)
        errors++
      }
    }

    // 通知UI更新
    this.notifyUIUpdate(type, changes)

    console.log(`✅ ${type} 增量更新完成: 成功 ${applied}，失败 ${errors}`)
    return { applied, errors }
  },

  /**
   * 同步单个数据类型
   */
  async syncDataType(type) {
    try {
      console.log(`🔄 开始同步 ${type}...`)
      
      // 获取本地版本
      const localVersions = VersionManager.getLocalVersions()
      const lastSyncTime = this.syncStatus.lastSyncTime

      // 获取增量数据
      const changes = await this.getIncrementalData(type, lastSyncTime)
      
      // 应用更新
      const result = await this.applyIncrementalUpdate(type, changes)
      
      // 更新版本号（从服务器获取最新版本）
      const serverVersions = await VersionManager.getServerVersions()
      if (serverVersions[type]) {
        VersionManager.updateLocalVersion(type, serverVersions[type])
      }

      return {
        success: true,
        type,
        applied: result.applied,
        errors: result.errors
      }

    } catch (error) {
      console.error(`❌ 同步 ${type} 失败:`, error.message)
      return {
        success: false,
        type,
        error: error.message
      }
    }
  },

  /**
   * 同步所有数据
   */
  async syncAllData() {
    if (this.syncStatus.isRunning) {
      console.log('⚠️ 同步正在进行中，跳过本次同步')
      return { success: false, message: '同步正在进行中' }
    }

    this.syncStatus.isRunning = true
    console.log('🚀 开始全量数据同步...')

    const results = []
    const dataTypes = Object.keys(VersionManager.getLocalVersions())

    try {
      for (const type of dataTypes) {
        const result = await this.syncDataType(type)
        results.push(result)
        
        // 短暂延迟，避免请求过于频繁
        await this.delay(100)
      }

      // 更新同步状态
      this.syncStatus.lastSyncTime = Date.now()
      this.syncStatus.errorCount = 0

      const successCount = results.filter(r => r.success).length
      console.log(`✅ 全量同步完成: 成功 ${successCount}/${results.length}`)

      return {
        success: true,
        results,
        syncTime: this.syncStatus.lastSyncTime
      }

    } catch (error) {
      console.error('❌ 全量同步失败:', error.message)
      this.syncStatus.errorCount++
      
      return {
        success: false,
        error: error.message,
        results
      }
    } finally {
      this.syncStatus.isRunning = false
    }
  },

  /**
   * 检查并同步更新
   */
  async checkAndSync() {
    try {
      console.log('🔍 检查数据更新...')
      
      // 检查版本更新
      const updateCheck = await VersionManager.checkUpdates()
      
      if (!updateCheck.hasUpdates) {
        console.log('✅ 数据已是最新版本')
        return { success: true, hasUpdates: false }
      }

      console.log(`📦 发现 ${updateCheck.updates.length} 个更新`)
      
      // 同步有更新的数据类型
      const results = []
      for (const update of updateCheck.updates) {
        const result = await this.syncDataType(update.type)
        results.push(result)
      }

      const successCount = results.filter(r => r.success).length
      console.log(`✅ 增量同步完成: 成功 ${successCount}/${results.length}`)

      return {
        success: true,
        hasUpdates: true,
        results
      }

    } catch (error) {
      console.error('❌ 检查并同步失败:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  },

  /**
   * 添加UI更新监听器
   */
  addListener(type, callback) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type).push(callback)
    console.log(`📡 已添加 ${type} 监听器`)
  },

  /**
   * 移除UI更新监听器
   */
  removeListener(type, callback) {
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
        console.log(`📡 已移除 ${type} 监听器`)
      }
    }
  },

  /**
   * 通知UI更新
   */
  notifyUIUpdate(type, changes) {
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type)
      callbacks.forEach(callback => {
        try {
          callback(changes)
        } catch (error) {
          console.error(`❌ UI更新回调失败:`, error.message)
        }
      })
      console.log(`📡 已通知 ${callbacks.length} 个 ${type} 监听器`)
    }
  },

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return { ...this.syncStatus }
  },

  /**
   * 重置同步状态
   */
  resetSyncStatus() {
    this.syncStatus = {
      isRunning: false,
      lastSyncTime: null,
      errorCount: 0,
      maxErrors: 3
    }
    console.log('🔄 同步状态已重置')
  }
}

module.exports = {
  IncrementalSync
}
