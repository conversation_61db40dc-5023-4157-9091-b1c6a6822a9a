// 实际测试数据初始化功能
const fs = require('fs');
const path = require('path');

// 检查云函数是否存在
console.log('🔍 检查云函数文件...');
const dataApiPath = path.join(__dirname, 'cloudfunctions', 'dataAPI', 'index.js');
if (!fs.existsSync(dataApiPath)) {
  console.log('❌ dataAPI 云函数不存在');
} else {
  console.log('✅ dataAPI 云函数存在');
}

// 检查云函数配置
console.log('\n🔍 检查云函数配置...');
const appConfigPath = path.join(__dirname, 'app.js');
const appConfig = fs.readFileSync(appConfigPath, 'utf8');

// 查找云环境ID
const envMatch = appConfig.match(/env:\s*['"`]([^'"`]+)['"`]/);
if (envMatch) {
  console.log('✅ 云环境ID配置:', envMatch[1]);
} else {
  console.log('❌ 未找到云环境ID配置');
}

// 检查数据API功能
console.log('\n📋 检查dataAPI功能...');
const dataApiContent = fs.readFileSync(dataApiPath, 'utf8');

const hasForceInit = dataApiContent.includes('forceInitDatabase');
console.log('forceInitDatabase:', hasForceInit ? '✅' : '❌');

const hasGetBanners = dataApiContent.includes('getBanners');
console.log('getBanners:', hasGetBanners ? '✅' : '❌');

const hasInitTestData = dataApiContent.includes('initTestData');
console.log('initTestData:', hasInitTestData ? '✅' : '❌');

// 检查前端调用
console.log('\n🎯 检查前端调用...');
const indexPath = path.join(__dirname, 'pages', 'index', 'index.js');
const indexContent = fs.readFileSync(indexPath, 'utf8');

const hasAutoInit = indexContent.includes('checkAndInitializeData');
console.log('自动初始化:', hasAutoInit ? '✅' : '❌');

const hasForceInitCall = indexContent.includes('forceInitDatabase');
console.log('强制初始化调用:', hasForceInitCall ? '✅' : '❌');

// 检查数据管理器
console.log('\n📊 检查数据管理器...');
const dataManagerPath = path.join(__dirname, 'utils', 'newDataManager.js');
const dataManagerContent = fs.readFileSync(dataManagerPath, 'utf8');

const hasCloudFunctionCall = dataManagerContent.includes('wx.cloud.callFunction');
console.log('云函数调用:', hasCloudFunctionCall ? '✅' : '❌');

const hasCacheClear = dataManagerContent.includes('clearCache');
console.log('缓存清除:', hasCacheClear ? '✅' : '❌');

console.log('\n📋 测试总结:');
if (hasForceInit && hasGetBanners && hasAutoInit && hasCloudFunctionCall) {
  console.log('✅ 数据初始化功能已完整实现');
} else {
  console.log('❌ 部分功能缺失，需要修复');
}