@echo off
echo ========================================
echo Testing Data Persistence
echo ========================================
echo.

cd admin-unified

echo 1. Starting server...
start /b node fixed-server.js
timeout /t 3 /nobreak >nul

echo 2. Testing data operations...

echo.
echo Testing GET categories:
curl -s http://localhost:8001/api/categories

echo.
echo.
echo Testing POST new category:
curl -s -X POST http://localhost:8001/api/data/categories ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"测试分类\",\"icon\":\"🧪\",\"status\":\"show\",\"sort\":99}"

echo.
echo.
echo Testing GET categories again (should include new category):
curl -s http://localhost:8001/api/categories

echo.
echo.
echo Testing cloud function call:
curl -s -X POST http://localhost:8001/api/cloud-function ^
  -H "Content-Type: application/json" ^
  -d "{\"functionName\":\"dataAPI\",\"data\":{\"action\":\"getCategories\"}}"

echo.
echo.
echo ========================================
echo Test complete! Check if:
echo 1. New category appears in the list
echo 2. Data persists after server restart
echo 3. admin-unified/data/ folder contains JSON files
echo ========================================

pause
