<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发REST API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 云开发REST API测试</h1>
        <p>绕过SDK问题，直接使用REST API访问云开发服务</p>
        
        <div class="info-box">
            <h3>📋 基础信息</h3>
            <p><strong>环境ID:</strong> cloud1-5g6pvnpl88dc0142</p>
            <p><strong>测试目的:</strong> 验证云开发服务可用性和数据库访问权限</p>
        </div>
        
        <button onclick="testRestAPI()">测试REST API连接</button>
        <button onclick="testDatabase()">测试数据库查询</button>
        <button onclick="initTestData()">初始化测试数据</button>
        
        <div id="results"></div>
    </div>

    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        async function testRestAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status info">开始测试REST API...</div>';
            
            try {
                log('🔍 测试云开发REST API连接...', 'info');
                
                // 测试云开发服务状态
                const response = await fetch(`https://tcb-api.tencentcloudapi.com/web`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'database.query',
                        env: ENV_ID,
                        query: 'db.collection("test").limit(1).get()'
                    })
                });
                
                log(`响应状态: ${response.status}`, 'info');
                
                if (response.ok) {
                    const data = await response.text();
                    log('✅ REST API连接成功', 'success');
                    log(`响应数据: ${data.substring(0, 200)}...`, 'info');
                } else {
                    log(`⚠️ REST API响应异常: ${response.status} ${response.statusText}`, 'warning');
                }
                
            } catch (error) {
                log(`❌ REST API测试失败: ${error.message}`, 'error');
                log('💡 这可能是正常的，因为需要认证', 'info');
            }
        }

        async function testDatabase() {
            log('🔍 测试数据库连接...', 'info');
            
            try {
                // 尝试使用简单的HTTP请求测试数据库
                const testUrl = `https://${ENV_ID}.service.tcloudbase.com/`;
                
                const response = await fetch(testUrl, {
                    method: 'GET',
                    mode: 'no-cors' // 避免CORS问题
                });
                
                log('✅ 云开发环境可访问', 'success');
                
            } catch (error) {
                log(`数据库测试: ${error.message}`, 'info');
            }
        }

        async function initTestData() {
            log('🚀 准备初始化测试数据...', 'info');
            
            // 创建测试数据
            const testData = [
                {
                    name: '测试表情包1',
                    url: 'https://example.com/emoji1.gif',
                    tags: ['搞笑', '可爱'],
                    category: '动物',
                    uploadTime: new Date().toISOString()
                },
                {
                    name: '测试表情包2', 
                    url: 'https://example.com/emoji2.gif',
                    tags: ['惊讶', '搞笑'],
                    category: '人物',
                    uploadTime: new Date().toISOString()
                }
            ];
            
            log('📝 测试数据准备完成:', 'info');
            testData.forEach((item, index) => {
                log(`  ${index + 1}. ${item.name} - ${item.category}`, 'info');
            });
            
            log('💡 提示: 由于SDK问题，建议使用以下方案:', 'warning');
            log('1. 使用云开发控制台直接操作数据库', 'info');
            log('2. 或者修复SDK加载问题后重新测试', 'info');
            log('3. 或者使用云函数作为中间层', 'info');
        }

        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', function() {
            log('📱 页面加载完成，REST API测试工具准备就绪', 'success');
            log('💡 点击上方按钮开始测试云开发服务', 'info');
        });
    </script>
</body>
</html>
