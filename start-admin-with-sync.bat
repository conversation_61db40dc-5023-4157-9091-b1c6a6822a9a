@echo off
chcp 65001 >nul
echo ========================================
echo Admin Panel Startup - Full Version
echo ========================================
echo.

cd admin-unified

echo 1. Cleaning old processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo 2. Checking and creating directories...
if not exist "data" (
    echo    Creating data directory: data/
    mkdir data
)

if not exist "..\miniprogram\data" (
    echo    Creating miniprogram data directory: ..\miniprogram\data\
    mkdir "..\miniprogram\data"
)

echo 3. Starting admin server...
start /b node fixed-server.js

echo 4. Waiting for server startup...
timeout /t 3 /nobreak >nul

echo 5. Testing server connection...
curl -s http://localhost:8001/health > nul
if %errorlevel% neq 0 (
    echo    ERROR: Server startup failed, check if port 8001 is occupied
    pause
    exit /b 1
)
echo    SUCCESS: Server started successfully

echo 6. Running initial data sync...
node sync-to-miniprogram.js
if %errorlevel% neq 0 (
    echo    WARNING: Data sync failed, but admin panel is still usable
) else (
    echo    SUCCESS: Data sync completed
)

echo 7. Starting data monitoring service...
start /b node sync-to-miniprogram.js --watch

echo 8. Opening admin panel...
start http://localhost:8001/index-fixed.html

echo.
echo ========================================
echo Startup Complete!
echo ========================================
echo.
echo Service Status:
echo    Admin Panel: http://localhost:8001/index-fixed.html
echo    Health Check: http://localhost:8001/health
echo    Data API: http://localhost:8001/api/categories
echo.
echo Data Storage:
echo    Admin Data: admin-unified/data/
echo    MiniProgram Data: miniprogram/data/
echo.
echo Real-time Sync:
echo    - Admin operations auto-sync to miniprogram data
echo    - Data persists across restarts
echo    - Full CRUD operations supported
echo.
echo You can now:
echo    1. Add/Edit/Delete categories - immediate sync
echo    2. Add/Edit/Delete emojis - immediate sync
echo    3. Manage banners - immediate sync
echo    4. View user data - real-time stats
echo.
echo Notes:
echo    - All operations save to JSON files
echo    - Data auto-syncs to miniprogram directory
echo    - Press Ctrl+C to stop monitoring service
echo ========================================

echo.
echo Press any key to view real-time logs...
pause >nul

echo.
echo Real-time Monitoring (Press Ctrl+C to exit):
echo ========================================
timeout /t 2 /nobreak >nul

:monitor
echo [%time%] Checking service status...
curl -s http://localhost:8001/health | findstr "ok" >nul
if %errorlevel% equ 0 (
    echo [%time%] Service running normally
) else (
    echo [%time%] Service error, attempting restart...
    taskkill /f /im node.exe 2>nul
    timeout /t 2 /nobreak >nul
    start /b node fixed-server.js
    timeout /t 3 /nobreak >nul
)

timeout /t 10 /nobreak >nul
goto monitor
