/* pages/category-detail/category-detail.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.emoji-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.emoji-image {
  width: 100%;
  height: 300rpx;
}

.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.emoji-stats {
  display: flex;
  justify-content: space-between;
}

.likes, .collections {
  font-size: 22rpx;
  color: #666;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}