<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试triggerAutoSync修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 测试triggerAutoSync修复</h1>
        
        <div style="text-align: center;">
            <button onclick="testTriggerAutoSync()">测试triggerAutoSync方法</button>
            <button onclick="testCloudAPIAdd()">测试CloudAPI.database.add</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 模拟CloudAPI对象（简化版本）
        const CloudAPI = {
            autoSync: {
                enabled: true,
                delay: 2000,
                pendingSync: new Set(),
                syncTimer: null
            },

            triggerAutoSync: function(dataType) {
                if (!this.autoSync.enabled) return;

                console.log(`🔄 触发自动同步: ${dataType}`);
                log(`🔄 触发自动同步: ${dataType}`, 'success');

                // 添加到待同步列表
                this.autoSync.pendingSync.add(dataType);

                // 清除之前的定时器
                if (this.autoSync.syncTimer) {
                    clearTimeout(this.autoSync.syncTimer);
                }

                // 设置延迟同步
                this.autoSync.syncTimer = setTimeout(() => {
                    this.performAutoSync();
                }, this.autoSync.delay);
            },

            performAutoSync: function() {
                log('🔄 执行自动同步...', 'info');
                console.log('🔄 执行自动同步，待同步项目:', Array.from(this.autoSync.pendingSync));
                
                // 清空待同步列表
                this.autoSync.pendingSync.clear();
                this.autoSync.syncTimer = null;
                
                log('✅ 自动同步完成', 'success');
            },

            database: {
                add: async function(collection, newData) {
                    try {
                        log(`☁️ 模拟添加数据到 [${collection}]`, 'info');
                        log(`📊 数据内容: ${JSON.stringify(newData, null, 2)}`, 'info');

                        // 模拟数据库操作
                        await new Promise(resolve => setTimeout(resolve, 500));

                        log(`✅ 数据添加成功 [${collection}]`, 'success');

                        // 触发自动同步 - 使用修复后的调用方式
                        CloudAPI.triggerAutoSync(collection);

                        return { success: true, id: 'mock_id_' + Date.now(), data: { _id: 'mock_id_' + Date.now(), ...newData } };
                    } catch (error) {
                        log(`❌ 数据添加失败 [${collection}]: ${error.message}`, 'error');
                        return { success: false, error: error.message };
                    }
                }
            }
        };

        // 测试triggerAutoSync方法
        function testTriggerAutoSync() {
            try {
                log('🧪 开始测试triggerAutoSync方法...');
                
                // 测试1: 直接调用triggerAutoSync
                log('📋 测试1: 直接调用CloudAPI.triggerAutoSync');
                CloudAPI.triggerAutoSync('categories');
                
                // 测试2: 检查方法是否存在
                log('📋 测试2: 检查方法是否存在');
                log('  - CloudAPI对象: ' + (CloudAPI ? '存在' : '不存在'));
                log('  - triggerAutoSync方法: ' + (CloudAPI.triggerAutoSync ? '存在' : '不存在'));
                log('  - 方法类型: ' + typeof CloudAPI.triggerAutoSync);
                
                // 测试3: 检查autoSync配置
                log('📋 测试3: 检查autoSync配置');
                log('  - autoSync.enabled: ' + CloudAPI.autoSync.enabled);
                log('  - autoSync.delay: ' + CloudAPI.autoSync.delay);
                log('  - pendingSync大小: ' + CloudAPI.autoSync.pendingSync.size);
                
                log('🎉 triggerAutoSync方法测试完成', 'success');
                
            } catch (error) {
                log('❌ triggerAutoSync方法测试失败: ' + error.message, 'error');
                log('📋 错误堆栈: ' + error.stack, 'error');
            }
        }

        // 测试CloudAPI.database.add方法
        async function testCloudAPIAdd() {
            try {
                log('🧪 开始测试CloudAPI.database.add方法...');
                
                const testData = {
                    name: '测试分类',
                    icon: '🧪',
                    description: '这是一个测试分类',
                    sort: 999
                };
                
                log('📋 调用CloudAPI.database.add方法...');
                const result = await CloudAPI.database.add('categories', testData);
                
                log('📊 调用结果:');
                log('  - 成功: ' + result.success);
                if (result.success) {
                    log('  - ID: ' + result.id);
                    log('  - 数据: ' + JSON.stringify(result.data, null, 2));
                } else {
                    log('  - 错误: ' + result.error);
                }
                
                log('🎉 CloudAPI.database.add方法测试完成', 'success');
                
            } catch (error) {
                log('❌ CloudAPI.database.add方法测试失败: ' + error.message, 'error');
                log('📋 错误堆栈: ' + error.stack, 'error');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，开始测试triggerAutoSync修复效果');
            log('💡 这个工具将验证triggerAutoSync方法是否能正常工作');
        });
    </script>
</body>
</html>
