// 测试渐变功能修复
const { chromium } = require('playwright');

async function testGradientFix() {
    console.log('🎨 测试渐变功能修复...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('保存分类数据') || text.includes('渐变') || text.includes('gradient')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 点击添加分类按钮');
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        }
        
        console.log('\n📍 测试1: 检查初始状态（未选择渐变）');
        
        // 检查初始状态
        const initialState = await page.evaluate(() => {
            const gradientInput = document.querySelector('#category-gradient');
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                inputValue: gradientInput ? gradientInput.value : null,
                previewText: gradientPreview ? gradientPreview.textContent.trim() : null,
                previewBackground: gradientPreview ? gradientPreview.style.background : null,
                previewColor: gradientPreview ? gradientPreview.style.color : null
            };
        });
        
        console.log('📊 初始状态:');
        console.log('输入框值:', `"${initialState.inputValue}"`);
        console.log('预览文本:', `"${initialState.previewText}"`);
        console.log('预览背景:', initialState.previewBackground);
        console.log('预览文字颜色:', initialState.previewColor);
        
        if (initialState.inputValue === '' && initialState.previewText === '🎨 请选择渐变色') {
            console.log('✅ 初始状态正确 - 显示"请选择渐变色"');
        } else {
            console.log('🔴 初始状态错误 - 不应该有默认渐变');
        }
        
        console.log('\n📍 测试2: 选择预设渐变');
        
        // 选择预设渐变
        const gradientSelect = await page.locator('#category-gradient-preset').first();
        await gradientSelect.selectOption('linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'); // 粉红渐变
        console.log('✅ 已选择粉红渐变');
        
        await page.waitForTimeout(2000);
        
        // 检查选择后的状态
        const afterSelectState = await page.evaluate(() => {
            const gradientInput = document.querySelector('#category-gradient');
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                inputValue: gradientInput ? gradientInput.value : null,
                previewText: gradientPreview ? gradientPreview.textContent.trim() : null,
                previewBackground: gradientPreview ? gradientPreview.style.background : null,
                previewColor: gradientPreview ? gradientPreview.style.color : null
            };
        });
        
        console.log('📊 选择渐变后的状态:');
        console.log('输入框值:', `"${afterSelectState.inputValue}"`);
        console.log('预览文本:', `"${afterSelectState.previewText}"`);
        console.log('预览背景:', afterSelectState.previewBackground);
        console.log('预览文字颜色:', afterSelectState.previewColor);
        
        if (afterSelectState.inputValue.includes('f093fb') && afterSelectState.previewText === '🎨 渐变预览效果') {
            console.log('✅ 选择渐变后状态正确 - 显示渐变预览');
        } else {
            console.log('🔴 选择渐变后状态错误');
        }
        
        console.log('\n📍 测试3: 重新选择"选择预设渐变"（清空）');
        
        // 重新选择空值
        await gradientSelect.selectOption(''); // 选择"选择预设渐变"
        console.log('✅ 已选择"选择预设渐变"（空值）');
        
        await page.waitForTimeout(2000);
        
        // 检查清空后的状态
        const afterClearState = await page.evaluate(() => {
            const gradientInput = document.querySelector('#category-gradient');
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                inputValue: gradientInput ? gradientInput.value : null,
                previewText: gradientPreview ? gradientPreview.textContent.trim() : null,
                previewBackground: gradientPreview ? gradientPreview.style.background : null,
                previewColor: gradientPreview ? gradientPreview.style.color : null
            };
        });
        
        console.log('📊 清空后的状态:');
        console.log('输入框值:', `"${afterClearState.inputValue}"`);
        console.log('预览文本:', `"${afterClearState.previewText}"`);
        console.log('预览背景:', afterClearState.previewBackground);
        console.log('预览文字颜色:', afterClearState.previewColor);
        
        if (afterClearState.inputValue === '' && afterClearState.previewText === '🎨 请选择渐变色') {
            console.log('✅ 清空后状态正确 - 重新显示"请选择渐变色"');
        } else {
            console.log('🔴 清空后状态错误');
        }
        
        console.log('\n📍 测试4: 输入错误的渐变格式');
        
        // 输入错误格式
        const gradientInput = await page.locator('#category-gradient').first();
        await gradientInput.fill('invalid-gradient-format');
        console.log('✅ 已输入错误的渐变格式');
        
        await page.waitForTimeout(2000);
        
        // 检查错误状态
        const errorState = await page.evaluate(() => {
            const gradientPreview = document.querySelector('#gradient-preview');
            
            return {
                previewText: gradientPreview ? gradientPreview.textContent.trim() : null,
                previewBackground: gradientPreview ? gradientPreview.style.background : null,
                previewColor: gradientPreview ? gradientPreview.style.color : null
            };
        });
        
        console.log('📊 错误格式状态:');
        console.log('预览文本:', `"${errorState.previewText}"`);
        console.log('预览背景:', errorState.previewBackground);
        console.log('预览文字颜色:', errorState.previewColor);
        
        if (errorState.previewText === '❌ 渐变格式错误') {
            console.log('✅ 错误格式处理正确 - 显示错误提示');
        } else {
            console.log('🔴 错误格式处理有问题');
        }
        
        console.log('\n📊 渐变功能修复测试总结:');
        const allTestsPassed = 
            initialState.inputValue === '' && initialState.previewText === '🎨 请选择渐变色' &&
            afterSelectState.inputValue.includes('f093fb') && afterSelectState.previewText === '🎨 渐变预览效果' &&
            afterClearState.inputValue === '' && afterClearState.previewText === '🎨 请选择渐变色' &&
            errorState.previewText === '❌ 渐变格式错误';
        
        if (allTestsPassed) {
            console.log('🎉 所有渐变功能测试通过！');
        } else {
            console.log('🔴 部分渐变功能测试失败');
        }
        
        // 截图
        await page.screenshot({ path: 'test-gradient-fix.png', fullPage: true });
        console.log('\n📸 渐变修复测试截图已保存: test-gradient-fix.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            allTestsPassed: allTestsPassed
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'gradient-fix-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
testGradientFix().then(result => {
    console.log('\n🎯 渐变修复测试结果:', result);
}).catch(console.error);
