#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo ""
echo "========================================"
echo -e "${PURPLE}🎭 表情包管理后台 - 一键启动脚本${NC}"
echo "========================================"
echo ""

echo -e "${BLUE}🚀 正在启动代理服务器...${NC}"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误：未检测到Node.js${NC}"
    echo "请先安装Node.js: https://nodejs.org/"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ Node.js 已安装${NC}"
echo ""

# 检查proxy-server.js是否存在
if [ ! -f "proxy-server.js" ]; then
    echo -e "${RED}❌ 错误：未找到proxy-server.js文件${NC}"
    echo "请确保在admin-serverless目录下运行此脚本"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ 代理服务器文件存在${NC}"
echo ""

echo -e "${CYAN}🌐 启动代理服务器...${NC}"
echo ""
echo -e "${YELLOW}📍 本地地址: http://localhost:9000${NC}"
echo ""
echo -e "${BLUE}🎯 可用页面:${NC}"
echo "   • 管理后台 (UI不变版): http://localhost:9000/index-ui-unchanged.html"
echo "   • 管理后台 (新版): http://localhost:9000/index-websdk.html"
echo "   • 功能测试页面: http://localhost:9000/test-websdk.html"
echo "   • 数据同步验证: http://localhost:9000/sync-verification.html"
echo ""
echo -e "${YELLOW}💡 提示: 按 Ctrl+C 停止服务器${NC}"
echo ""
echo "========================================"
echo ""

# 启动服务器
node proxy-server.js

echo ""
echo -e "${RED}🛑 服务器已停止${NC}"
