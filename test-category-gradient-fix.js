// 测试分类渐变显示修复效果
const { chromium } = require('playwright');

async function testCategoryGradientFix() {
    console.log('🎨 测试分类渐变显示修复效果...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('自动加载分类数据') || text.includes('清理分类数据') || text.includes('渐变')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：进入分类管理页面（测试修复后的加载逻辑）');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(8000); // 等待数据加载完成
        
        console.log('\n📍 第二步：检查修复后的渐变显示效果');
        
        // 检查表格中的渐变显示
        const gradientDisplayCheck = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                found: true,
                totalRows: rows.length,
                categories: rows.map((row, index) => {
                    const nameCell = row.querySelector('td:nth-child(3)');
                    const gradientCell = row.querySelector('td:nth-child(4)');
                    const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                    
                    return {
                        index: index + 1,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        hasGradientDiv: !!gradientDiv,
                        gradientDivText: gradientDiv ? gradientDiv.textContent.trim() : 'N/A',
                        gradientDivStyle: gradientDiv ? gradientDiv.getAttribute('style') : 'N/A',
                        hasGradientStyle: gradientDiv ? gradientDiv.getAttribute('style').includes('gradient') : false,
                        showsPreview: gradientDiv ? gradientDiv.textContent.trim() === '预览' : false,
                        showsNoGradient: gradientDiv ? gradientDiv.textContent.trim() === '无渐变' : false
                    };
                })
            };
        });
        
        console.log('📊 修复后渐变显示检查:');
        if (gradientDisplayCheck.found) {
            console.log(`表格总行数: ${gradientDisplayCheck.totalRows}`);
            
            gradientDisplayCheck.categories.forEach((cat) => {
                console.log(`\n分类 ${cat.index}: ${cat.name}`);
                console.log(`  有渐变div: ${cat.hasGradientDiv}`);
                console.log(`  渐变div文本: ${cat.gradientDivText}`);
                console.log(`  有渐变样式: ${cat.hasGradientStyle}`);
                console.log(`  显示预览: ${cat.showsPreview}`);
                console.log(`  显示无渐变: ${cat.showsNoGradient}`);
                console.log(`  样式预览: ${cat.gradientDivStyle.substring(0, 80)}...`);
                
                if (cat.showsPreview && cat.hasGradientStyle) {
                    console.log(`  ✅ 渐变显示正常`);
                } else if (cat.showsNoGradient) {
                    console.log(`  🔴 仍显示"无渐变"`);
                } else {
                    console.log(`  ⚠️ 渐变显示状态不明`);
                }
            });
            
            // 统计修复效果
            const withGradient = gradientDisplayCheck.categories.filter(cat => 
                cat.showsPreview && cat.hasGradientStyle).length;
            const withoutGradient = gradientDisplayCheck.categories.filter(cat => 
                cat.showsNoGradient).length;
            
            console.log(`\n📊 修复效果统计:`);
            console.log(`  正确显示渐变: ${withGradient}/${gradientDisplayCheck.totalRows}`);
            console.log(`  仍显示"无渐变": ${withoutGradient}/${gradientDisplayCheck.totalRows}`);
            
            if (withGradient === gradientDisplayCheck.totalRows && withoutGradient === 0) {
                console.log(`  🎉 渐变显示修复完全成功！`);
            } else if (withGradient > 0) {
                console.log(`  ✅ 渐变显示部分修复成功`);
            } else {
                console.log(`  🔴 渐变显示修复失败`);
            }
            
        } else {
            console.log(`❌ 表格检查失败: ${gradientDisplayCheck.error}`);
        }
        
        console.log('\n📍 第三步：验证AdminApp.data.categories中的数据');
        
        // 检查内存中的分类数据
        const memoryDataCheck = await page.evaluate(() => {
            const categories = window.AdminApp && window.AdminApp.data && window.AdminApp.data.categories;
            
            if (!categories) {
                return { error: '未找到AdminApp.data.categories' };
            }
            
            return {
                found: true,
                count: categories.length,
                categories: categories.map(cat => ({
                    _id: cat._id,
                    name: cat.name,
                    hasGradient: !!cat.gradient,
                    gradientValue: cat.gradient || 'N/A',
                    gradientPreview: cat.gradient ? cat.gradient.substring(0, 50) + '...' : 'N/A'
                }))
            };
        });
        
        console.log('📊 内存数据验证:');
        if (memoryDataCheck.found) {
            console.log(`内存中分类数量: ${memoryDataCheck.count}`);
            
            memoryDataCheck.categories.forEach((cat, index) => {
                console.log(`\n内存分类 ${index + 1}: ${cat.name}`);
                console.log(`  ID: ${cat._id}`);
                console.log(`  有渐变: ${cat.hasGradient}`);
                console.log(`  渐变预览: ${cat.gradientPreview}`);
                
                if (cat.hasGradient) {
                    console.log(`  ✅ 内存数据包含渐变`);
                } else {
                    console.log(`  🔴 内存数据缺少渐变`);
                }
            });
            
            const categoriesWithGradient = memoryDataCheck.categories.filter(cat => cat.hasGradient).length;
            console.log(`\n📊 内存数据统计: ${categoriesWithGradient}/${memoryDataCheck.count} 个分类有渐变数据`);
            
        } else {
            console.log(`❌ 内存数据检查失败: ${memoryDataCheck.error}`);
        }
        
        console.log('\n📍 第四步：多次切换页面测试稳定性');
        
        // 切换到其他页面再回来，测试稳定性
        const dashboardLink = await page.locator('[onclick="showPage(\'dashboard\')"]').first();
        await dashboardLink.click();
        await page.waitForTimeout(2000);
        
        console.log('✅ 已切换到仪表板页面');
        
        // 再次回到分类管理页面
        const categoryLink2 = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink2.click();
        await page.waitForTimeout(8000);
        
        console.log('✅ 已重新进入分类管理页面');
        
        // 再次检查渐变显示
        const secondCheck = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            const withGradient = rows.filter(row => {
                const gradientCell = row.querySelector('td:nth-child(4)');
                const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                return gradientDiv && 
                       gradientDiv.textContent.trim() === '预览' && 
                       gradientDiv.getAttribute('style').includes('gradient');
            }).length;
            
            const withoutGradient = rows.filter(row => {
                const gradientCell = row.querySelector('td:nth-child(4)');
                const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                return gradientDiv && gradientDiv.textContent.trim() === '无渐变';
            }).length;
            
            return {
                found: true,
                totalRows: rows.length,
                withGradient: withGradient,
                withoutGradient: withoutGradient
            };
        });
        
        console.log('📊 第二次检查结果:');
        if (secondCheck.found) {
            console.log(`表格总行数: ${secondCheck.totalRows}`);
            console.log(`正确显示渐变: ${secondCheck.withGradient}/${secondCheck.totalRows}`);
            console.log(`仍显示"无渐变": ${secondCheck.withoutGradient}/${secondCheck.totalRows}`);
            
            if (secondCheck.withGradient === secondCheck.totalRows && secondCheck.withoutGradient === 0) {
                console.log('🎉 多次切换后渐变显示仍然正常！');
            } else {
                console.log('⚠️ 多次切换后渐变显示出现问题');
            }
        }
        
        console.log('\n🎯 分类渐变显示修复测试总结:');
        
        const firstTestSuccess = gradientDisplayCheck.found && 
            gradientDisplayCheck.categories.filter(cat => cat.showsPreview && cat.hasGradientStyle).length > 0;
        const memoryDataCorrect = memoryDataCheck.found && 
            memoryDataCheck.categories.filter(cat => cat.hasGradient).length > 0;
        const secondTestSuccess = secondCheck.found && secondCheck.withGradient > 0;
        
        console.log(`首次进入测试: ${firstTestSuccess ? '✅ 成功' : '🔴 失败'}`);
        console.log(`内存数据正确: ${memoryDataCorrect ? '✅ 正确' : '🔴 错误'}`);
        console.log(`稳定性测试: ${secondTestSuccess ? '✅ 稳定' : '🔴 不稳定'}`);
        
        const overallSuccess = firstTestSuccess && memoryDataCorrect && secondTestSuccess;
        
        console.log(`\n🎯 修复结果: ${overallSuccess ? '🎉 完全成功！' : '⚠️ 仍有问题'}`);
        
        if (overallSuccess) {
            console.log('✅ 分类渐变色预览显示问题已彻底修复！');
        } else {
            console.log('🔴 分类渐变色预览显示问题仍需进一步修复');
        }
        
        return {
            success: true,
            overallSuccess: overallSuccess,
            firstTest: gradientDisplayCheck,
            memoryData: memoryDataCheck,
            secondTest: secondCheck
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-gradient-fix-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'category-gradient-fix-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: category-gradient-fix-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testCategoryGradientFix().then(result => {
    console.log('\n🎯 分类渐变显示修复测试最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.overallSuccess) {
        console.log('🎉 分类渐变色预览显示问题已彻底修复！');
    } else if (result.success) {
        console.log('⚠️ 修复部分成功，仍需进一步完善。');
    } else {
        console.log('❌ 修复测试失败。');
    }
}).catch(console.error);
