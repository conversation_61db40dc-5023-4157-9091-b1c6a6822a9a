// 深度测试首页数据加载流程
const fs = require('fs');
const path = require('path');

async function testHomepageDataFlow() {
    console.log('🔧 深度测试首页数据加载流程...\n');
    
    try {
        console.log('📍 第一步：验证云函数和数据库配置');
        
        // 检查云函数配置
        const cloudFunctions = [
            'dataAPI',
            'initEmojiData'
        ];
        
        console.log('☁️ 云函数检查:');
        for (const funcName of cloudFunctions) {
            const funcPath = path.join(__dirname, `cloudfunctions/${funcName}/index.js`);
            const exists = fs.existsSync(funcPath);
            console.log(`  ${funcName}: ${exists ? '✅ 存在' : '🔴 缺失'}`);
            
            if (exists && funcName === 'dataAPI') {
                const content = fs.readFileSync(funcPath, 'utf8');
                const hasGetEmojis = content.includes('getEmojis');
                const hasGetCategories = content.includes('getCategories');
                console.log(`    - getEmojis方法: ${hasGetEmojis ? '✅' : '🔴'}`);
                console.log(`    - getCategories方法: ${hasGetCategories ? '✅' : '🔴'}`);
            }
        }
        
        console.log('\n📍 第二步：分析首页数据加载逻辑');
        
        const indexJsPath = path.join(__dirname, 'pages/index/index.js');
        const indexJsContent = fs.readFileSync(indexJsPath, 'utf8');
        
        // 检查关键方法
        const keyMethods = [
            'onReady',
            'loadEmojiData',
            'loadCategoryData', 
            'loadBannerData',
            'getCategoryColor'
        ];
        
        console.log('🔍 关键方法检查:');
        for (const method of keyMethods) {
            const hasMethod = indexJsContent.includes(`${method}(`);
            console.log(`  ${method}: ${hasMethod ? '✅ 存在' : '🔴 缺失'}`);
        }
        
        // 检查数据初始化
        const dataInitChecks = [
            { key: 'emojiList: []', desc: '表情包列表初始化' },
            { key: 'hotCategories: []', desc: '分类列表初始化' },
            { key: 'bannerList: []', desc: '横幅列表初始化' },
            { key: 'searchResults: []', desc: '搜索结果初始化' }
        ];
        
        console.log('\n📊 数据初始化检查:');
        for (const check of dataInitChecks) {
            const hasInit = indexJsContent.includes(check.key);
            console.log(`  ${check.desc}: ${hasInit ? '✅' : '🔴'}`);
        }
        
        console.log('\n📍 第三步：检查WXML渲染逻辑');
        
        const indexWxmlPath = path.join(__dirname, 'pages/index/index.wxml');
        const indexWxmlContent = fs.readFileSync(indexWxmlPath, 'utf8');
        
        // 检查渲染条件
        const renderChecks = [
            {
                condition: 'searchResults.length === 0 && emojiList.length > 0',
                desc: '表情包列表渲染条件'
            },
            {
                condition: 'searchResults.length === 0 && hotCategories.length > 0',
                desc: '分类列表渲染条件'
            },
            {
                condition: 'searchResults.length === 0 && bannerList.length > 0',
                desc: '横幅渲染条件'
            },
            {
                condition: 'wx:for="{{emojiList}}"',
                desc: '表情包循环渲染'
            },
            {
                condition: 'wx:for="{{hotCategories}}"',
                desc: '分类循环渲染'
            }
        ];
        
        console.log('🎨 WXML渲染逻辑检查:');
        for (const check of renderChecks) {
            const hasCondition = indexWxmlContent.includes(check.condition);
            console.log(`  ${check.desc}: ${hasCondition ? '✅' : '🔴'}`);
        }
        
        // 检查样式修复
        const styleChecks = [
            {
                pattern: 'background: {{item.color}}',
                desc: '分类图标渐变色样式'
            },
            {
                pattern: '<!-- 横幅文字和按钮已隐藏',
                desc: '横幅文字隐藏'
            },
            {
                pattern: '<!-- 实时同步状态显示 - 已隐藏',
                desc: '同步状态隐藏'
            }
        ];
        
        console.log('\n🎨 样式修复检查:');
        for (const check of styleChecks) {
            const hasPattern = indexWxmlContent.includes(check.pattern);
            console.log(`  ${check.desc}: ${hasPattern ? '✅ 已修复' : '🔴 未修复'}`);
        }
        
        console.log('\n📍 第四步：生成测试脚本');
        
        // 创建小程序端测试脚本
        const testScript = `// 首页数据加载测试脚本
// 在微信开发者工具控制台中运行

console.log('🧪 开始首页数据加载测试...');

// 获取首页实例
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];

if (currentPage.route !== 'pages/index/index') {
  console.error('❌ 请在首页运行此测试');
} else {
  console.log('✅ 当前在首页，开始测试...');
  
  // 测试1: 检查数据状态
  console.log('\\n📊 测试1: 检查当前数据状态');
  console.log('表情包数量:', currentPage.data.emojiList.length);
  console.log('分类数量:', currentPage.data.hotCategories.length);
  console.log('横幅数量:', currentPage.data.bannerList.length);
  console.log('搜索结果数量:', currentPage.data.searchResults.length);
  
  // 测试2: 检查渲染条件
  console.log('\\n🎨 测试2: 检查渲染条件');
  const shouldShowEmojis = currentPage.data.searchResults.length === 0 && currentPage.data.emojiList.length > 0;
  const shouldShowCategories = currentPage.data.searchResults.length === 0 && currentPage.data.hotCategories.length > 0;
  const shouldShowBanners = currentPage.data.searchResults.length === 0 && currentPage.data.bannerList.length > 0;
  
  console.log('应该显示表情包:', shouldShowEmojis);
  console.log('应该显示分类:', shouldShowCategories);
  console.log('应该显示横幅:', shouldShowBanners);
  
  // 测试3: 重新加载数据
  console.log('\\n🔄 测试3: 重新加载数据');
  
  Promise.all([
    currentPage.loadEmojiData(),
    currentPage.loadCategoryData(),
    currentPage.loadBannerData()
  ]).then(() => {
    console.log('✅ 数据重新加载完成');
    console.log('新的表情包数量:', currentPage.data.emojiList.length);
    console.log('新的分类数量:', currentPage.data.hotCategories.length);
    console.log('新的横幅数量:', currentPage.data.bannerList.length);
    
    // 测试4: 检查数据结构
    console.log('\\n🔍 测试4: 检查数据结构');
    if (currentPage.data.emojiList.length > 0) {
      console.log('第一个表情包:', currentPage.data.emojiList[0]);
    }
    if (currentPage.data.hotCategories.length > 0) {
      console.log('第一个分类:', currentPage.data.hotCategories[0]);
    }
    
  }).catch(error => {
    console.error('❌ 数据加载失败:', error);
  });
}`;

        // 保存测试脚本
        const testScriptPath = path.join(__dirname, 'homepage-test-script.js');
        fs.writeFileSync(testScriptPath, testScript);
        
        console.log('✅ 测试脚本已生成:', testScriptPath);
        
        console.log('\n📍 第五步：生成调试指南');
        
        console.log('🔧 调试步骤:');
        console.log('1. 在微信开发者工具中打开项目');
        console.log('2. 上传并部署initEmojiData云函数');
        console.log('3. 在云开发控制台调用initEmojiData云函数初始化数据');
        console.log('4. 重新编译小程序');
        console.log('5. 在首页控制台运行测试脚本');
        console.log('6. 检查控制台输出和页面显示');
        
        console.log('\n💡 问题排查:');
        console.log('如果表情包仍不显示:');
        console.log('- 检查云函数dataAPI是否正确部署');
        console.log('- 检查数据库emojis集合是否有数据');
        console.log('- 检查数据状态是否为published');
        console.log('- 检查控制台是否有错误信息');
        console.log('- 检查网络连接和云开发环境');
        
        console.log('\n🎯 预期结果:');
        console.log('修复完成后应该看到:');
        console.log('✅ 表情包列表正常显示');
        console.log('✅ 分类图标有渐变色背景');
        console.log('✅ 横幅只显示图片，无文字按钮');
        console.log('✅ 搜索框下方无多余内容');
        
        return {
            success: true,
            message: '首页数据加载流程测试完成',
            testScriptPath: testScriptPath,
            checks: {
                cloudFunctions: cloudFunctions.length,
                keyMethods: keyMethods.length,
                renderLogic: renderChecks.length,
                styleFixes: styleChecks.length
            }
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 运行测试
testHomepageDataFlow().then(result => {
    console.log('\\n🎯 首页数据加载流程测试结果:', result.success ? '成功' : '失败');
    
    if (result.success) {
        console.log('✅ 所有检查已完成，测试脚本已生成！');
        console.log('📋 请按照调试指南进行测试。');
    } else {
        console.log('❌ 测试失败:', result.error);
    }
}).catch(console.error);
