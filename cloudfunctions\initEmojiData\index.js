const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  console.log('🚀 开始初始化表情包数据...')
  
  try {
    // 检查是否已有数据
    const existingData = await db.collection('emojis').count()
    console.log('现有表情包数量:', existingData.total)
    
    if (existingData.total > 0) {
      console.log('✅ 数据库中已有表情包数据')
      
      // 检查数据状态
      const publishedData = await db.collection('emojis').where({
        status: 'published'
      }).count()
      
      console.log('已发布表情包数量:', publishedData.total)
      
      if (publishedData.total === 0) {
        console.log('⚠️ 没有已发布的表情包，更新状态...')
        
        // 将所有表情包状态设为published
        const updateResult = await db.collection('emojis').where({}).update({
          data: {
            status: 'published'
          }
        })
        
        console.log('✅ 更新表情包状态完成:', updateResult.stats)
      }
      
      return {
        success: true,
        message: '数据检查完成',
        total: existingData.total,
        published: publishedData.total
      }
    }
    
    // 创建测试数据
    console.log('📝 创建测试表情包数据...')
    
    const testEmojis = [
      {
        title: '搞笑表情包1',
        description: '超级搞笑的表情包',
        imageUrl: 'https://picsum.photos/200/200?random=1',
        status: 'published',
        categoryId: 'funny',
        category: '搞笑幽默',
        likes: 128,
        collections: 45,
        downloads: 89,
        views: 256,
        tags: ['搞笑', '幽默', '表情'],
        fileSize: 15420,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        title: '可爱萌宠2',
        description: '超级可爱的小动物表情包',
        imageUrl: 'https://picsum.photos/200/200?random=2',
        status: 'published',
        categoryId: 'cute',
        category: '可爱萌宠',
        likes: 89,
        collections: 67,
        downloads: 123,
        views: 189,
        tags: ['可爱', '萌宠', '动物'],
        fileSize: 18650,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        title: '情感表达3',
        description: '表达各种情感的表情包',
        imageUrl: 'https://picsum.photos/200/200?random=3',
        status: 'published',
        categoryId: 'emotion',
        category: '情感表达',
        likes: 156,
        collections: 78,
        downloads: 234,
        views: 345,
        tags: ['情感', '表达', '心情'],
        fileSize: 12340,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        title: '网络热梗4',
        description: '最新的网络热门梗图',
        imageUrl: 'https://via.placeholder.com/200x200/96CEB4/FFFFFF?text=🔥',
        status: 'published',
        categoryId: 'hot',
        category: '网络热梗',
        likes: 234,
        collections: 123,
        downloads: 456,
        views: 567,
        tags: ['热梗', '网络', '流行'],
        fileSize: 20180,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        title: '动漫二次元5',
        description: '精选动漫角色表情包',
        imageUrl: 'https://via.placeholder.com/200x200/FFEAA7/FFFFFF?text=🎌',
        status: 'published',
        categoryId: '2d',
        category: '动漫二次元',
        likes: 178,
        collections: 89,
        downloads: 167,
        views: 289,
        tags: ['动漫', '二次元', '角色'],
        fileSize: 16780,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        title: '节日庆典6',
        description: '各种节日庆祝表情包',
        imageUrl: 'https://via.placeholder.com/200x200/DDA0DD/FFFFFF?text=🎉',
        status: 'published',
        categoryId: 'festival',
        category: '节日庆典',
        likes: 145,
        collections: 67,
        downloads: 198,
        views: 234,
        tags: ['节日', '庆典', '庆祝'],
        fileSize: 19450,
        fileType: 'image/png',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]
    
    // 批量插入数据
    const insertResult = await db.collection('emojis').add({
      data: testEmojis
    })
    
    console.log('✅ 测试数据创建完成:', insertResult)
    
    return {
      success: true,
      message: '测试数据创建成功',
      count: testEmojis.length,
      insertResult: insertResult
    }
    
  } catch (error) {
    console.error('❌ 初始化数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}