<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 云开发连接诊断工具</h1>
    
    <div class="test-panel">
        <h2>📊 基础信息</h2>
        <div id="basicInfo"></div>
    </div>

    <div class="test-panel">
        <h2>🌐 SDK加载测试</h2>
        <button onclick="testSDKLoad()">测试SDK加载</button>
        <div id="sdkStatus"></div>
    </div>

    <div class="test-panel">
        <h2>☁️ 云开发初始化测试</h2>
        <button onclick="testCloudInit()">测试云开发初始化</button>
        <div id="cloudStatus"></div>
    </div>

    <div class="test-panel">
        <h2>🔐 身份认证测试</h2>
        <button onclick="testAuth()">测试匿名登录</button>
        <div id="authStatus"></div>
    </div>

    <div class="test-panel">
        <h2>📊 数据库连接测试</h2>
        <button onclick="testDatabase()">测试数据库连接</button>
        <div id="dbStatus"></div>
    </div>

    <div class="test-panel">
        <h2>📝 详细日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="detailLog" class="log"></div>
    </div>

    <script>
        // 环境配置
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let tcbApp = null;
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('detailLog');
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('detailLog').innerHTML = '';
        }

        // 显示基础信息
        function showBasicInfo() {
            const info = `
                <div class="status info">
                    <strong>环境ID:</strong> ${ENV_ID}<br>
                    <strong>用户代理:</strong> ${navigator.userAgent}<br>
                    <strong>当前URL:</strong> ${window.location.href}<br>
                    <strong>协议:</strong> ${window.location.protocol}<br>
                    <strong>时间:</strong> ${new Date().toLocaleString()}
                </div>
            `;
            document.getElementById('basicInfo').innerHTML = info;
        }

        // 测试SDK加载
        async function testSDKLoad() {
            const statusDiv = document.getElementById('sdkStatus');
            statusDiv.innerHTML = '<div class="status info">正在测试SDK加载...</div>';
            
            try {
                log('开始测试SDK加载', 'info');
                
                // 检查现有SDK
                if (typeof window.cloudbase !== 'undefined') {
                    statusDiv.innerHTML = '<div class="status success">✅ CloudBase SDK已加载</div>';
                    log('CloudBase SDK已存在', 'success');
                    return true;
                }
                
                if (typeof window.tcb !== 'undefined') {
                    statusDiv.innerHTML = '<div class="status success">✅ TCB SDK已加载</div>';
                    log('TCB SDK已存在', 'success');
                    return true;
                }

                // 尝试加载SDK
                log('尝试从CDN加载SDK', 'info');
                const script = document.createElement('script');
                script.src = 'https://static.cloudbase.net/cloudbase-js-sdk/1.8.0/cloudbase.full.js';
                
                return new Promise((resolve, reject) => {
                    script.onload = () => {
                        if (typeof window.cloudbase !== 'undefined') {
                            statusDiv.innerHTML = '<div class="status success">✅ SDK加载成功</div>';
                            log('SDK从CDN加载成功', 'success');
                            resolve(true);
                        } else {
                            statusDiv.innerHTML = '<div class="status error">❌ SDK加载后对象不可用</div>';
                            log('SDK加载后对象不可用', 'error');
                            reject(new Error('SDK对象不可用'));
                        }
                    };
                    
                    script.onerror = () => {
                        statusDiv.innerHTML = '<div class="status error">❌ SDK加载失败</div>';
                        log('SDK从CDN加载失败', 'error');
                        reject(new Error('SDK加载失败'));
                    };
                    
                    document.head.appendChild(script);
                });
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ SDK加载异常: ${error.message}</div>`;
                log(`SDK加载异常: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试云开发初始化
        async function testCloudInit() {
            const statusDiv = document.getElementById('cloudStatus');
            statusDiv.innerHTML = '<div class="status info">正在测试云开发初始化...</div>';
            
            try {
                log('开始测试云开发初始化', 'info');
                
                // 确保SDK已加载
                if (typeof window.cloudbase === 'undefined' && typeof window.tcb === 'undefined') {
                    await testSDKLoad();
                }
                
                const sdk = window.cloudbase || window.tcb;
                if (!sdk) {
                    throw new Error('SDK不可用');
                }
                
                log(`使用SDK: ${window.cloudbase ? 'cloudbase' : 'tcb'}`, 'info');
                
                // 初始化云开发
                tcbApp = sdk.init({
                    env: ENV_ID
                });
                
                log(`云开发初始化完成，环境ID: ${ENV_ID}`, 'success');
                statusDiv.innerHTML = '<div class="status success">✅ 云开发初始化成功</div>';
                
                return true;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 云开发初始化失败: ${error.message}</div>`;
                log(`云开发初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试身份认证
        async function testAuth() {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.innerHTML = '<div class="status info">正在测试身份认证...</div>';
            
            try {
                log('开始测试身份认证', 'info');
                
                if (!tcbApp) {
                    await testCloudInit();
                }
                
                if (!tcbApp) {
                    throw new Error('云开发未初始化');
                }
                
                const auth = tcbApp.auth();
                log('获取认证对象成功', 'info');
                
                // 检查当前登录状态
                const loginState = await auth.getLoginState();

                // 安全地记录登录状态，避免循环引用
                if (loginState) {
                    log(`当前登录状态: 已登录 (uid: ${loginState.uid || 'unknown'})`, 'info');
                } else {
                    log('当前登录状态: 未登录', 'info');
                }

                if (!loginState) {
                    log('执行匿名登录', 'info');
                    await auth.anonymousAuthProvider().signIn();
                    log('匿名登录成功', 'success');

                    const newLoginState = await auth.getLoginState();
                    if (newLoginState) {
                        log(`新登录状态: 已登录 (uid: ${newLoginState.uid || 'unknown'})`, 'success');
                    } else {
                        log('新登录状态: 仍未登录', 'warning');
                    }
                }
                
                statusDiv.innerHTML = '<div class="status success">✅ 身份认证成功</div>';
                return true;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 身份认证失败: ${error.message}</div>`;
                log(`身份认证失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 测试数据库连接
        async function testDatabase() {
            const statusDiv = document.getElementById('dbStatus');
            statusDiv.innerHTML = '<div class="status info">正在测试数据库连接...</div>';
            
            try {
                log('开始测试数据库连接', 'info');
                
                if (!tcbApp) {
                    await testCloudInit();
                    await testAuth();
                }
                
                if (!tcbApp) {
                    throw new Error('云开发未初始化');
                }
                
                const db = tcbApp.database();
                log('获取数据库对象成功', 'info');
                
                // 尝试查询一个简单的集合
                log('尝试查询emojis集合', 'info');
                const result = await db.collection('emojis').limit(1).get();

                // 安全地检查结果
                if (result && result.data && Array.isArray(result.data)) {
                    log(`数据库查询成功，返回 ${result.data.length} 条记录`, 'success');
                    statusDiv.innerHTML = `<div class="status success">✅ 数据库连接成功，查询到 ${result.data.length} 条记录</div>`;
                } else {
                    log('数据库查询成功，但结果格式异常', 'warning');
                    log(`结果对象: ${result ? '存在' : '不存在'}`, 'info');
                    log(`数据字段: ${result && result.data ? '存在' : '不存在'}`, 'info');
                    statusDiv.innerHTML = '<div class="status warning">⚠️ 数据库连接成功，但数据格式异常</div>';
                }
                
                return true;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ 数据库连接失败: ${error.message}</div>`;
                log(`数据库连接失败: ${error.message}`, 'error');
                
                // 提供详细的错误分析
                if (error.message.includes('PERMISSION_DENIED')) {
                    log('权限被拒绝 - 可能的原因:', 'warning');
                    log('1. 数据库安全规则限制了访问', 'warning');
                    log('2. 匿名用户没有读取权限', 'warning');
                    log('3. 环境ID配置错误', 'warning');
                }
                
                return false;
            }
        }

        // 页面加载完成后显示基础信息
        document.addEventListener('DOMContentLoaded', function() {
            showBasicInfo();
            log('页面加载完成，开始诊断', 'info');
        });
    </script>
</body>
</html>
