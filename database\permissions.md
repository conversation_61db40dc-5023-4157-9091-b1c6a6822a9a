# 数据库权限配置说明

## 集合权限设置

### 1. categories（分类表）
```json
{
  "read": true,
  "write": "doc._openid == '{openid}' && doc.role == 'admin'"
}
```

### 2. emojis（表情包表）
```json
{
  "read": true,
  "write": "doc._openid == '{openid}' && doc.role == 'admin'"
}
```

### 3. users（用户表）
```json
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

### 4. user_likes（点赞记录表）
```json
{
  "read": "doc.userId == auth.openid",
  "write": "doc.userId == auth.openid"
}
```

### 5. user_collections（收藏记录表）
```json
{
  "read": "doc.userId == auth.openid", 
  "write": "doc.userId == auth.openid"
}
```

### 6. banners（轮播图表）
```json
{
  "read": true,
  "write": "doc._openid == '{openid}' && doc.role == 'admin'"
}
```

## 权限说明

- `read: true` - 所有用户可读
- `write: "doc._openid == '{openid}' && doc.role == 'admin'"` - 仅管理员可写
- `read/write: "doc._openid == auth.openid"` - 仅创建者可读写
- `read/write: "doc.userId == auth.openid"` - 仅对应用户可读写

## 配置步骤

1. 在云开发控制台进入数据库
2. 创建对应的集合
3. 点击集合名称进入详情页
4. 点击"权限设置"标签
5. 将上述权限规则复制到对应的读写权限框中
6. 保存设置