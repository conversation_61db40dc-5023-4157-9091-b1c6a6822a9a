<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时云函数测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .loading { color: #007bff; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 实时云函数测试</h1>
        <p>直接测试云函数是否能正确处理数据同步问题</p>

        <div class="test-section info">
            <h3>🚀 开始测试</h3>
            <button onclick="runAllTests()" id="runAllBtn">运行所有测试</button>
            <button onclick="clearResults()" id="clearBtn">清除结果</button>
        </div>

        <div class="step">
            <h3>步骤1: 初始化云开发</h3>
            <button onclick="testCloudInit()">测试云开发初始化</button>
            <div id="cloudInitResult"></div>
        </div>

        <div class="step">
            <h3>步骤2: 直接查询数据库</h3>
            <button onclick="testDirectDatabase()">直接查询数据库</button>
            <div id="directDbResult"></div>
        </div>

        <div class="step">
            <h3>步骤3: 测试dataAPI云函数</h3>
            <button onclick="testDataAPIFunction()">测试dataAPI云函数</button>
            <div id="dataAPIResult"></div>
        </div>

        <div class="step">
            <h3>步骤4: 创建测试数据</h3>
            <button onclick="createTestData()">创建测试数据</button>
            <div id="createDataResult"></div>
        </div>

        <div class="step">
            <h3>步骤5: 验证数据读取</h3>
            <button onclick="verifyDataRead()">验证数据读取</button>
            <div id="verifyReadResult"></div>
        </div>

        <div class="step">
            <h3>步骤6: 问题分析</h3>
            <button onclick="analyzeIssues()">分析问题</button>
            <div id="analysisResult"></div>
        </div>
    </div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        let app = null;
        let db = null;
        let testResults = {};
        const testTimestamp = Date.now();

        // 步骤1: 初始化云开发
        async function testCloudInit() {
            const resultDiv = document.getElementById('cloudInitResult');
            resultDiv.innerHTML = '<p class="loading">正在初始化云开发...</p>';

            try {
                app = cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
                await app.auth().signInAnonymously();
                db = app.database();

                // 测试基本连接
                const testQuery = await db.collection('categories').limit(1).get();

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 云开发初始化成功</h4>
                        <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
                        <p>认证状态: 匿名登录成功</p>
                        <p>数据库连接: 正常</p>
                        <p>测试查询: 成功</p>
                    </div>
                `;

                testResults.cloudInit = { success: true };
                return true;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 云开发初始化失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.cloudInit = { success: false, error: error.message };
                return false;
            }
        }

        // 步骤2: 直接查询数据库
        async function testDirectDatabase() {
            const resultDiv = document.getElementById('directDbResult');
            resultDiv.innerHTML = '<p class="loading">正在直接查询数据库...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先初始化云开发</h4></div>';
                return;
            }

            try {
                // 查询所有集合
                const [categoriesResult, emojisResult, bannersResult] = await Promise.all([
                    db.collection('categories').get(),
                    db.collection('emojis').get(),
                    db.collection('banners').get()
                ]);

                const categories = categoriesResult.data;
                const emojis = emojisResult.data;
                const banners = bannersResult.data;

                // 分析数据格式
                let emojiFieldAnalysis = { useCategory: 0, useCategoryId: 0 };
                if (emojis.length > 0) {
                    emojiFieldAnalysis.useCategory = emojis.filter(e => e.category && typeof e.category === 'string').length;
                    emojiFieldAnalysis.useCategoryId = emojis.filter(e => e.categoryId).length;
                }

                let html = `
                    <div class="success">
                        <h4>✅ 数据库直接查询成功</h4>
                        <p><strong>分类数量:</strong> ${categories.length}</p>
                        <p><strong>表情包数量:</strong> ${emojis.length}</p>
                        <p><strong>轮播图数量:</strong> ${banners.length}</p>
                        
                        <h5>表情包字段分析:</h5>
                        <p>使用category字段: ${emojiFieldAnalysis.useCategory} 个</p>
                        <p>使用categoryId字段: ${emojiFieldAnalysis.useCategoryId} 个</p>
                `;

                if (categories.length > 0) {
                    html += '<h5>分类数据示例:</h5><pre>' + JSON.stringify(categories.slice(0, 2), null, 2) + '</pre>';
                }

                if (emojis.length > 0) {
                    html += '<h5>表情包数据示例:</h5><pre>' + JSON.stringify(emojis.slice(0, 2), null, 2) + '</pre>';
                } else {
                    html += '<div class="warning"><p>⚠️ 数据库中没有表情包数据！这是问题的根源。</p></div>';
                }

                html += '</div>';
                resultDiv.innerHTML = html;

                testResults.directDatabase = {
                    success: true,
                    categoriesCount: categories.length,
                    emojisCount: emojis.length,
                    bannersCount: banners.length,
                    emojiFieldAnalysis,
                    categories: categories.slice(0, 3),
                    emojis: emojis.slice(0, 3)
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 数据库查询失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.directDatabase = { success: false, error: error.message };
            }
        }

        // 步骤3: 测试dataAPI云函数
        async function testDataAPIFunction() {
            const resultDiv = document.getElementById('dataAPIResult');
            resultDiv.innerHTML = '<p class="loading">正在测试dataAPI云函数...</p>';

            if (!app) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先初始化云开发</h4></div>';
                return;
            }

            try {
                // 测试各个API
                const [categoriesResult, emojisAllResult, bannersResult] = await Promise.all([
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getCategories' }
                    }),
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 20 } }
                    }),
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getBanners' }
                    })
                ]);

                let html = `
                    <div class="success">
                        <h4>✅ dataAPI云函数测试成功</h4>
                        
                        <h5>分类API结果:</h5>
                        <p>成功: ${categoriesResult.result.success}</p>
                        <p>数量: ${categoriesResult.result.data?.length || 0}</p>
                        <pre>${JSON.stringify(categoriesResult.result, null, 2)}</pre>
                        
                        <h5>表情包API结果:</h5>
                        <p>成功: ${emojisAllResult.result.success}</p>
                        <p>数量: ${emojisAllResult.result.data?.length || 0}</p>
                        <pre>${JSON.stringify(emojisAllResult.result, null, 2)}</pre>
                        
                        <h5>轮播图API结果:</h5>
                        <p>成功: ${bannersResult.result.success}</p>
                        <p>数量: ${bannersResult.result.data?.length || 0}</p>
                        <pre>${JSON.stringify(bannersResult.result, null, 2)}</pre>
                    </div>
                `;

                resultDiv.innerHTML = html;

                testResults.dataAPI = {
                    success: true,
                    categories: categoriesResult.result,
                    emojis: emojisAllResult.result,
                    banners: bannersResult.result
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ dataAPI云函数测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.dataAPI = { success: false, error: error.message };
            }
        }

        // 步骤4: 创建测试数据
        async function createTestData() {
            const resultDiv = document.getElementById('createDataResult');
            resultDiv.innerHTML = '<p class="loading">正在创建测试数据...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先初始化云开发</h4></div>';
                return;
            }

            try {
                const testCategoryName = `实时测试分类_${testTimestamp}`;
                const testEmojiTitle = `实时测试表情包_${testTimestamp}`;

                // 创建测试分类
                const categoryData = {
                    name: testCategoryName,
                    icon: '🧪',
                    sort: 999,
                    status: 'active',
                    description: '实时测试创建的分类',
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const categoryResult = await db.collection('categories').add({
                    data: categoryData
                });

                // 创建测试表情包（使用管理后台格式）
                const emojiData = {
                    title: testEmojiTitle,
                    category: testCategoryName, // 使用category字段存储分类名称
                    description: '实时测试创建的表情包',
                    imageUrl: 'https://via.placeholder.com/150x150?text=LiveTest',
                    status: 'published',
                    likes: 0,
                    downloads: 0,
                    collections: 0,
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const emojiResult = await db.collection('emojis').add({
                    data: emojiData
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 测试数据创建成功</h4>
                        <p>分类ID: ${categoryResult._id}</p>
                        <p>表情包ID: ${emojiResult._id}</p>
                        <p>分类名称: ${testCategoryName}</p>
                        <p>表情包标题: ${testEmojiTitle}</p>
                        
                        <h5>创建的分类数据:</h5>
                        <pre>${JSON.stringify(categoryData, null, 2)}</pre>
                        
                        <h5>创建的表情包数据:</h5>
                        <pre>${JSON.stringify(emojiData, null, 2)}</pre>
                    </div>
                `;

                testResults.createData = {
                    success: true,
                    categoryId: categoryResult._id,
                    emojiId: emojiResult._id,
                    testCategoryName,
                    testEmojiTitle,
                    categoryData,
                    emojiData
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 测试数据创建失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.createData = { success: false, error: error.message };
            }
        }

        // 步骤5: 验证数据读取
        async function verifyDataRead() {
            const resultDiv = document.getElementById('verifyReadResult');
            resultDiv.innerHTML = '<p class="loading">正在验证数据读取...</p>';

            if (!testResults.createData || !testResults.createData.success) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先创建测试数据</h4></div>';
                return;
            }

            try {
                const testCategoryName = testResults.createData.testCategoryName;

                // 测试云函数是否能读取到刚创建的数据
                const [categoriesResult, emojisAllResult, emojisByCategoryResult] = await Promise.all([
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getCategories' }
                    }),
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 100 } }
                    }),
                    app.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getEmojis', data: { category: testCategoryName, page: 1, limit: 10 } }
                    })
                ]);

                // 检查结果
                const foundCategory = categoriesResult.result.data?.find(cat => cat.name === testCategoryName);
                const allEmojis = emojisAllResult.result.data || [];
                const categoryEmojis = emojisByCategoryResult.result.data || [];
                const foundTestEmoji = allEmojis.find(emoji => emoji.title === testResults.createData.testEmojiTitle);

                let html = `
                    <div class="${foundCategory && categoryEmojis.length > 0 ? 'success' : 'error'}">
                        <h4>${foundCategory && categoryEmojis.length > 0 ? '✅' : '❌'} 数据读取验证</h4>
                        <p>测试分类名称: ${testCategoryName}</p>
                        <p>是否找到分类: ${foundCategory ? '✅ 是' : '❌ 否'}</p>
                        <p>所有表情包数量: ${allEmojis.length}</p>
                        <p>按分类查询的表情包数量: ${categoryEmojis.length}</p>
                        <p>是否找到测试表情包: ${foundTestEmoji ? '✅ 是' : '❌ 否'}</p>
                `;

                if (foundCategory) {
                    html += '<h5>找到的分类:</h5><pre>' + JSON.stringify(foundCategory, null, 2) + '</pre>';
                }

                if (categoryEmojis.length > 0) {
                    html += '<h5>按分类查询的表情包:</h5><pre>' + JSON.stringify(categoryEmojis, null, 2) + '</pre>';
                } else if (foundTestEmoji) {
                    html += '<h5>在所有表情包中找到的测试数据:</h5><pre>' + JSON.stringify(foundTestEmoji, null, 2) + '</pre>';
                    html += '<div class="warning"><p>⚠️ 表情包存在但按分类查询失败，说明字段匹配有问题</p></div>';
                }

                html += '</div>';
                resultDiv.innerHTML = html;

                testResults.verifyRead = {
                    success: foundCategory && categoryEmojis.length > 0,
                    foundCategory: !!foundCategory,
                    foundTestEmoji: !!foundTestEmoji,
                    allEmojisCount: allEmojis.length,
                    categoryEmojisCount: categoryEmojis.length,
                    testCategoryName
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 数据读取验证失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.verifyRead = { success: false, error: error.message };
            }
        }

        // 步骤6: 分析问题
        function analyzeIssues() {
            const resultDiv = document.getElementById('analysisResult');
            
            const issues = [];
            const solutions = [];

            // 分析各个步骤的结果
            if (!testResults.cloudInit?.success) {
                issues.push('❌ 云开发初始化失败');
                solutions.push('检查网络连接和环境配置');
            }

            if (!testResults.directDatabase?.success) {
                issues.push('❌ 数据库直接查询失败');
                solutions.push('检查数据库权限和连接');
            } else if (testResults.directDatabase.emojisCount === 0) {
                issues.push('❌ 数据库中没有表情包数据');
                solutions.push('管理后台没有成功保存数据到数据库');
                solutions.push('检查管理后台的数据保存逻辑');
            }

            if (!testResults.dataAPI?.success) {
                issues.push('❌ dataAPI云函数调用失败');
                solutions.push('检查云函数部署状态');
                solutions.push('查看云函数执行日志');
            } else if (testResults.dataAPI.emojis?.data?.length === 0) {
                issues.push('❌ dataAPI云函数返回空数据');
                solutions.push('云函数查询逻辑有问题');
            }

            if (testResults.verifyRead && !testResults.verifyRead.success) {
                if (testResults.verifyRead.foundTestEmoji && testResults.verifyRead.categoryEmojisCount === 0) {
                    issues.push('⚠️ 表情包存在但按分类查询失败');
                    solutions.push('字段匹配逻辑有问题，需要检查云函数的分类查询代码');
                }
            }

            let html = '<div class="info"><h4>🔍 问题分析结果</h4>';
            
            if (issues.length === 0) {
                html += '<div class="success"><p>✅ 所有测试通过，数据同步正常！</p></div>';
            } else {
                html += '<h5>发现的问题:</h5><ul>';
                issues.forEach(issue => html += `<li>${issue}</li>`);
                html += '</ul>';

                html += '<h5>建议的解决方案:</h5><ol>';
                solutions.forEach(solution => html += `<li>${solution}</li>`);
                html += '</ol>';
            }

            html += '<h5>完整测试结果:</h5>';
            html += '<pre>' + JSON.stringify(testResults, null, 2) + '</pre>';
            html += '</div>';

            resultDiv.innerHTML = html;
        }

        // 运行所有测试
        async function runAllTests() {
            const runAllBtn = document.getElementById('runAllBtn');
            runAllBtn.disabled = true;
            runAllBtn.textContent = '测试中...';

            try {
                if (await testCloudInit()) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await testDirectDatabase();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await testDataAPIFunction();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await createTestData();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    await verifyDataRead();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    analyzeIssues();
                }
            } catch (error) {
                console.error('测试过程出错:', error);
            } finally {
                runAllBtn.disabled = false;
                runAllBtn.textContent = '重新运行所有测试';
            }
        }

        // 清除结果
        function clearResults() {
            const resultDivs = ['cloudInitResult', 'directDbResult', 'dataAPIResult', 'createDataResult', 'verifyReadResult', 'analysisResult'];
            resultDivs.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            testResults = {};
        }
    </script>
</body>
</html>
