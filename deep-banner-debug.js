// 深度调试横幅数据问题
const { chromium } = require('playwright');

async function deepBannerDebug() {
    console.log('🔍 深度调试横幅数据问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[CONSOLE] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 步骤1: 获取原始横幅数据');
        
        // 获取原始数据
        const rawBannerData = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('banners');
                return {
                    success: result.success,
                    dataLength: result.data ? result.data.length : 0,
                    fullData: result.data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 原始数据结果:');
        console.log('成功:', rawBannerData.success);
        console.log('数据长度:', rawBannerData.dataLength);
        
        if (rawBannerData.fullData && rawBannerData.fullData.length > 0) {
            console.log('\n📋 所有横幅原始数据:');
            rawBannerData.fullData.forEach((banner, index) => {
                console.log(`\n横幅 ${index + 1}:`);
                console.log('  完整结构:', JSON.stringify(banner, null, 2));
                
                if (banner.data) {
                    console.log('  data字段内容:', JSON.stringify(banner.data, null, 2));
                    console.log('  data.title:', banner.data.title);
                    console.log('  data.status:', banner.data.status);
                    console.log('  data.imageUrl:', banner.data.imageUrl);
                } else {
                    console.log('  无data字段');
                    console.log('  直接title:', banner.title);
                    console.log('  直接status:', banner.status);
                }
            });
        }
        
        console.log('\n📍 步骤2: 进入横幅管理页面');
        
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            console.log('✅ 已进入横幅管理页面');
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 步骤3: 检查数据处理过程');
        
        // 检查AdminApp中的处理后数据
        const processedData = await page.evaluate(() => {
            return {
                hasBanners: !!AdminApp.data.banners,
                bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                allBanners: AdminApp.data.banners || []
            };
        });
        
        console.log('📊 AdminApp处理后数据:');
        console.log('有横幅数据:', processedData.hasBanners);
        console.log('横幅数量:', processedData.bannersCount);
        
        if (processedData.allBanners.length > 0) {
            console.log('\n📋 所有处理后的横幅数据:');
            processedData.allBanners.forEach((banner, index) => {
                console.log(`\n处理后横幅 ${index + 1}:`);
                console.log('  完整结构:', JSON.stringify(banner, null, 2));
                console.log('  title:', banner.title);
                console.log('  status:', banner.status);
                console.log('  _id:', banner._id);
            });
        }
        
        console.log('\n📍 步骤4: 检查表格渲染');
        
        // 检查表格内容
        const tableContent = await page.evaluate(() => {
            const container = document.getElementById('banner-content');
            const table = container ? container.querySelector('table') : null;
            const tbody = table ? table.querySelector('tbody') : null;
            
            if (!container) return { error: '未找到banner-content容器' };
            if (!table) return { error: '未找到表格', containerHTML: container.innerHTML.substring(0, 500) };
            if (!tbody) return { error: '未找到tbody', tableHTML: table.innerHTML.substring(0, 500) };
            
            const rows = Array.from(tbody.querySelectorAll('tr'));
            return {
                success: true,
                rowCount: rows.length,
                rows: rows.map((row, index) => {
                    const cells = Array.from(row.querySelectorAll('td'));
                    return {
                        index: index,
                        cellCount: cells.length,
                        data: cells.map(cell => cell.textContent?.trim().substring(0, 50)),
                        hasDataBannerId: !!row.getAttribute('data-banner-id'),
                        bannerId: row.getAttribute('data-banner-id')
                    };
                })
            };
        });
        
        console.log('📊 表格渲染结果:');
        if (tableContent.error) {
            console.log('❌ 错误:', tableContent.error);
            if (tableContent.containerHTML) {
                console.log('容器HTML:', tableContent.containerHTML);
            }
            if (tableContent.tableHTML) {
                console.log('表格HTML:', tableContent.tableHTML);
            }
        } else {
            console.log('✅ 表格渲染成功');
            console.log('行数:', tableContent.rowCount);
            
            tableContent.rows.forEach((row, index) => {
                console.log(`\n表格行 ${index + 1}:`);
                console.log('  单元格数:', row.cellCount);
                console.log('  数据:', row.data);
                console.log('  有banner-id:', row.hasDataBannerId);
                console.log('  banner-id:', row.bannerId);
            });
        }
        
        console.log('\n📍 步骤5: 手动调用loadBanners函数');
        
        // 手动调用loadBanners函数
        const manualLoadResult = await page.evaluate(async () => {
            try {
                console.log('🔧 手动调用loadBanners...');
                await loadBanners();
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
        
        console.log('📊 手动加载结果:', manualLoadResult);
        
        // 等待一下再检查
        await page.waitForTimeout(3000);
        
        // 再次检查表格
        const finalTableCheck = await page.evaluate(() => {
            const tbody = document.querySelector('#banner-tbody');
            if (!tbody) return { error: '未找到banner-tbody' };
            
            const rows = Array.from(tbody.querySelectorAll('tr'));
            return {
                success: true,
                rowCount: rows.length,
                firstRowData: rows.length > 0 ? 
                    Array.from(rows[0].querySelectorAll('td')).map(cell => cell.textContent?.trim()) : []
            };
        });
        
        console.log('\n📊 最终表格检查:');
        console.log('成功:', finalTableCheck.success);
        console.log('行数:', finalTableCheck.rowCount);
        if (finalTableCheck.firstRowData) {
            console.log('第一行数据:', finalTableCheck.firstRowData);
        }
        
        // 截图
        await page.screenshot({ path: 'deep-banner-debug.png', fullPage: true });
        console.log('\n📸 调试截图已保存: deep-banner-debug.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
deepBannerDebug().catch(console.error);
