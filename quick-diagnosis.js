// 快速诊断脚本 - 在微信开发者工具控制台运行
console.log('🔍 开始快速诊断...')

// 1. 检查基础环境
console.log('\n📋 基础环境检查:')
console.log('   wx 对象:', typeof wx !== 'undefined' ? '✅ 存在' : '❌ 不存在')
console.log('   wx.cloud:', typeof wx !== 'undefined' && wx.cloud ? '✅ 存在' : '❌ 不存在')

// 2. 检查应用状态
const app = getApp()
console.log('\n📊 应用状态:')
console.log('   云开发初始化:', app.globalData.cloudInitialized ? '✅' : '❌')
console.log('   数据库初始化:', app.globalData.databaseInitialized ? '✅' : '❌')
console.log('   核心服务初始化:', app.globalData.coreServicesInitialized ? '✅' : '❌')

// 3. 测试云函数连通性
console.log('\n☁️ 测试云函数连通性...')

// 简单的ping测试
const testCloudFunction = (name) => {
  return new Promise((resolve) => {
    const startTime = Date.now()
    
    wx.cloud.callFunction({
      name: name,
      data: { action: 'ping' },
      success: (res) => {
        const duration = Date.now() - startTime
        console.log(`   ✅ ${name}: 响应时间 ${duration}ms`)
        resolve({ success: true, duration, response: res })
      },
      fail: (err) => {
        const duration = Date.now() - startTime
        console.log(`   ❌ ${name}: 失败 (${duration}ms) -`, err.errMsg || err.message)
        resolve({ success: false, duration, error: err })
      }
    })
    
    // 10秒超时
    setTimeout(() => {
      console.log(`   ⏰ ${name}: 超时 (10秒)`)
      resolve({ success: false, duration: 10000, error: 'timeout' })
    }, 10000)
  })
}

// 测试核心云函数
const testFunctions = async () => {
  const functions = ['dataAPI', 'syncAPI', 'login']
  
  for (const funcName of functions) {
    await testCloudFunction(funcName)
  }
  
  console.log('\n🗄️ 测试数据库连接...')
  
  // 测试数据库连接
  try {
    const result = await wx.cloud.database().collection('categories').limit(1).get()
    console.log('   ✅ 数据库连接正常，categories 集合存在')
    console.log('   📊 记录数:', result.data.length)
  } catch (err) {
    console.log('   ❌ 数据库连接失败:', err.errMsg || err.message)
    
    if (err.errCode === -502005) {
      console.log('   💡 集合不存在，尝试创建测试数据...')
      
      try {
        await wx.cloud.database().collection('categories').add({
          data: {
            id: 'test',
            name: '测试分类',
            status: 'active',
            createdAt: new Date()
          }
        })
        console.log('   ✅ 测试数据创建成功')
      } catch (createErr) {
        console.log('   ❌ 创建测试数据失败:', createErr.errMsg || createErr.message)
      }
    }
  }
  
  console.log('\n🎯 诊断完成！')
  console.log('💡 如果云函数测试失败，请检查云函数是否已正确部署')
  console.log('💡 如果数据库测试失败，请检查数据库权限配置')
}

// 延迟2秒开始测试，确保初始化完成
setTimeout(testFunctions, 2000)
console.log('⏳ 等待2秒后开始测试...')
