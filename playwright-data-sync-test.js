/**
 * 自动化数据同步测试 - 不依赖Playwright
 * 使用Node.js直接测试数据流程
 */

const fs = require('fs');
const path = require('path');
const path = require('path');

class DataSyncTester {
    constructor() {
        this.browser = null;
        this.adminPage = null;
        this.miniProgramPage = null;
        this.testData = {
            categoryName: `测试分类_${Date.now()}`,
            emojiTitle: `测试表情包_${Date.now()}`,
            bannerTitle: `测试轮播图_${Date.now()}`
        };
        this.results = {
            adminDataCreation: null,
            miniProgramDataDisplay: null,
            cloudFunctionTest: null
        };
    }

    async init() {
        console.log('🚀 启动Playwright浏览器...');
        this.browser = await chromium.launch({ 
            headless: false, // 显示浏览器窗口以便观察
            slowMo: 1000 // 减慢操作速度以便观察
        });
    }

    async testAdminDataCreation() {
        console.log('📝 测试管理后台数据创建...');
        
        try {
            // 打开管理后台
            this.adminPage = await this.browser.newPage();
            const adminPath = path.resolve(__dirname, 'admin/index.html');
            await this.adminPage.goto(`file://${adminPath}`);
            
            // 等待页面加载
            await this.adminPage.waitForTimeout(3000);
            
            // 检查是否有云开发初始化
            const cloudStatus = await this.adminPage.evaluate(() => {
                return window.CloudConfig ? window.CloudConfig.initialized : false;
            });
            
            console.log('云开发初始化状态:', cloudStatus);
            
            // 测试创建分类
            await this.testCreateCategory();
            
            // 测试创建表情包
            await this.testCreateEmoji();
            
            // 测试创建轮播图
            await this.testCreateBanner();
            
            this.results.adminDataCreation = {
                success: true,
                cloudInitialized: cloudStatus,
                testData: this.testData
            };
            
        } catch (error) {
            console.error('❌ 管理后台测试失败:', error);
            this.results.adminDataCreation = {
                success: false,
                error: error.message
            };
        }
    }

    async testCreateCategory() {
        console.log('📋 创建测试分类...');
        
        try {
            // 点击分类管理
            await this.adminPage.click('[data-page="categories"]');
            await this.adminPage.waitForTimeout(1000);
            
            // 点击添加分类按钮
            await this.adminPage.click('button:has-text("添加分类")');
            await this.adminPage.waitForTimeout(500);
            
            // 填写分类信息
            await this.adminPage.fill('#categoryName', this.testData.categoryName);
            await this.adminPage.fill('#categoryIcon', '🧪');
            await this.adminPage.fill('#categoryDescription', '自动化测试创建的分类');
            
            // 提交表单
            await this.adminPage.click('button:has-text("保存")');
            await this.adminPage.waitForTimeout(2000);
            
            // 检查是否创建成功
            const categoryExists = await this.adminPage.locator(`text=${this.testData.categoryName}`).count() > 0;
            console.log('分类创建结果:', categoryExists ? '成功' : '失败');
            
        } catch (error) {
            console.error('分类创建失败:', error);
        }
    }

    async testCreateEmoji() {
        console.log('😊 创建测试表情包...');
        
        try {
            // 切换到表情包管理
            await this.adminPage.click('[data-page="emojis"]');
            await this.adminPage.waitForTimeout(1000);
            
            // 点击添加表情包按钮
            await this.adminPage.click('button:has-text("添加表情包")');
            await this.adminPage.waitForTimeout(500);
            
            // 填写表情包信息
            await this.adminPage.fill('#emojiTitle', this.testData.emojiTitle);
            await this.adminPage.selectOption('#emojiCategory', this.testData.categoryName);
            await this.adminPage.fill('#emojiDescription', '自动化测试创建的表情包');
            
            // 模拟上传图片（使用base64数据）
            const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            await this.adminPage.evaluate((imageData) => {
                const input = document.querySelector('#emojiImage');
                if (input) {
                    input.value = imageData;
                }
            }, testImageData);
            
            // 提交表单
            await this.adminPage.click('button:has-text("保存")');
            await this.adminPage.waitForTimeout(2000);
            
            // 检查是否创建成功
            const emojiExists = await this.adminPage.locator(`text=${this.testData.emojiTitle}`).count() > 0;
            console.log('表情包创建结果:', emojiExists ? '成功' : '失败');
            
        } catch (error) {
            console.error('表情包创建失败:', error);
        }
    }

    async testCreateBanner() {
        console.log('🎯 创建测试轮播图...');
        
        try {
            // 切换到轮播图管理
            await this.adminPage.click('[data-page="banners"]');
            await this.adminPage.waitForTimeout(1000);
            
            // 点击添加轮播图按钮
            await this.adminPage.click('button:has-text("添加轮播图")');
            await this.adminPage.waitForTimeout(500);
            
            // 填写轮播图信息
            await this.adminPage.fill('#bannerTitle', this.testData.bannerTitle);
            await this.adminPage.fill('#bannerSubtitle', '自动化测试创建的轮播图');
            
            // 提交表单
            await this.adminPage.click('button:has-text("保存")');
            await this.adminPage.waitForTimeout(2000);
            
            // 检查是否创建成功
            const bannerExists = await this.adminPage.locator(`text=${this.testData.bannerTitle}`).count() > 0;
            console.log('轮播图创建结果:', bannerExists ? '成功' : '失败');
            
        } catch (error) {
            console.error('轮播图创建失败:', error);
        }
    }

    async testCloudFunction() {
        console.log('☁️ 测试云函数数据读取...');
        
        try {
            // 在管理后台页面测试云函数调用
            const cloudFunctionResult = await this.adminPage.evaluate(async (testData) => {
                try {
                    // 测试获取分类
                    const categoriesResult = await window.CloudAPI.database.get('categories');
                    
                    // 测试获取表情包
                    const emojisResult = await window.CloudAPI.database.get('emojis');
                    
                    // 查找测试数据
                    const foundCategory = categoriesResult.data?.find(cat => cat.name === testData.categoryName);
                    const foundEmoji = emojisResult.data?.find(emoji => emoji.title === testData.emojiTitle);
                    
                    return {
                        success: true,
                        categoriesCount: categoriesResult.data?.length || 0,
                        emojisCount: emojisResult.data?.length || 0,
                        foundTestCategory: !!foundCategory,
                        foundTestEmoji: !!foundEmoji,
                        testData: testData
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            }, this.testData);
            
            console.log('云函数测试结果:', cloudFunctionResult);
            this.results.cloudFunctionTest = cloudFunctionResult;
            
        } catch (error) {
            console.error('❌ 云函数测试失败:', error);
            this.results.cloudFunctionTest = {
                success: false,
                error: error.message
            };
        }
    }

    async testMiniProgramDataDisplay() {
        console.log('📱 测试小程序数据显示...');
        
        try {
            // 打开诊断页面来模拟小程序数据读取
            this.miniProgramPage = await this.browser.newPage();
            const diagnosisPath = path.resolve(__dirname, 'comprehensive-diagnosis.html');
            await this.miniProgramPage.goto(`file://${diagnosisPath}`);
            
            // 等待页面加载
            await this.miniProgramPage.waitForTimeout(2000);
            
            // 执行诊断步骤
            await this.miniProgramPage.click('button:has-text("检查云开发连接")');
            await this.miniProgramPage.waitForTimeout(3000);
            
            await this.miniProgramPage.click('button:has-text("检查数据库数据")');
            await this.miniProgramPage.waitForTimeout(3000);
            
            await this.miniProgramPage.click('button:has-text("测试dataAPI云函数")');
            await this.miniProgramPage.waitForTimeout(3000);
            
            // 获取诊断结果
            const diagnosisResult = await this.miniProgramPage.evaluate(() => {
                return window.diagnosticResults || {};
            });
            
            console.log('小程序诊断结果:', diagnosisResult);
            this.results.miniProgramDataDisplay = diagnosisResult;
            
        } catch (error) {
            console.error('❌ 小程序测试失败:', error);
            this.results.miniProgramDataDisplay = {
                success: false,
                error: error.message
            };
        }
    }

    async generateReport() {
        console.log('📊 生成测试报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            testData: this.testData,
            results: this.results,
            analysis: this.analyzeResults(),
            recommendations: this.getRecommendations()
        };
        
        // 保存报告到文件
        const fs = require('fs');
        fs.writeFileSync('data-sync-test-report.json', JSON.stringify(report, null, 2));
        
        console.log('📋 测试报告已保存到 data-sync-test-report.json');
        console.log('\n=== 测试总结 ===');
        console.log('管理后台数据创建:', this.results.adminDataCreation?.success ? '✅ 成功' : '❌ 失败');
        console.log('云函数数据读取:', this.results.cloudFunctionTest?.success ? '✅ 成功' : '❌ 失败');
        console.log('小程序数据显示:', this.results.miniProgramDataDisplay?.success ? '✅ 成功' : '❌ 失败');
        
        return report;
    }

    analyzeResults() {
        const issues = [];
        
        if (!this.results.adminDataCreation?.success) {
            issues.push('管理后台数据创建失败');
        }
        
        if (!this.results.cloudFunctionTest?.success) {
            issues.push('云函数数据读取失败');
        } else if (!this.results.cloudFunctionTest.foundTestCategory) {
            issues.push('云函数无法找到管理后台创建的分类数据');
        } else if (!this.results.cloudFunctionTest.foundTestEmoji) {
            issues.push('云函数无法找到管理后台创建的表情包数据');
        }
        
        if (!this.results.miniProgramDataDisplay?.success) {
            issues.push('小程序数据显示测试失败');
        }
        
        return {
            issues: issues,
            rootCause: this.identifyRootCause(issues)
        };
    }

    identifyRootCause(issues) {
        if (issues.includes('管理后台数据创建失败')) {
            return '管理后台与云开发的连接或权限问题';
        }
        
        if (issues.includes('云函数无法找到管理后台创建的分类数据') || 
            issues.includes('云函数无法找到管理后台创建的表情包数据')) {
            return '数据库字段格式不匹配或查询逻辑问题';
        }
        
        if (issues.includes('云函数数据读取失败')) {
            return '云函数部署或执行问题';
        }
        
        if (issues.includes('小程序数据显示测试失败')) {
            return '小程序端数据获取逻辑问题';
        }
        
        return '未知问题，需要进一步调查';
    }

    getRecommendations() {
        const recommendations = [];
        
        if (!this.results.adminDataCreation?.success) {
            recommendations.push('检查管理后台的云开发SDK配置和权限');
        }
        
        if (this.results.cloudFunctionTest?.success && !this.results.cloudFunctionTest.foundTestCategory) {
            recommendations.push('检查云函数dataAPI中的分类查询逻辑，确保兼容管理后台的数据格式');
        }
        
        if (this.results.cloudFunctionTest?.success && !this.results.cloudFunctionTest.foundTestEmoji) {
            recommendations.push('检查云函数dataAPI中的表情包查询逻辑，特别是category字段的匹配');
        }
        
        recommendations.push('使用comprehensive-diagnosis.html进行详细的步骤诊断');
        recommendations.push('检查数据库中的实际数据格式和字段名称');
        
        return recommendations;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.init();
            await this.testAdminDataCreation();
            await this.testCloudFunction();
            await this.testMiniProgramDataDisplay();
            const report = await this.generateReport();
            return report;
        } finally {
            await this.cleanup();
        }
    }
}

// 运行测试
async function runDataSyncTest() {
    console.log('🔍 开始数据同步问题自动化诊断...\n');
    
    const tester = new DataSyncTester();
    try {
        const report = await tester.run();
        console.log('\n✅ 测试完成！请查看 data-sync-test-report.json 获取详细报告。');
        return report;
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
        throw error;
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runDataSyncTest().catch(console.error);
}

module.exports = { DataSyncTester, runDataSyncTest };
