// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const result = await db.collection('categories')
      .where({
        status: 'active'
      })
      .orderBy('sort', 'asc')
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}