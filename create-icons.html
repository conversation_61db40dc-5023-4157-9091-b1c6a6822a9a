<!DOCTYPE html>
<html>
<head>
    <title>TabBar图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { display: inline-block; margin: 10px; text-align: center; }
        canvas { border: 1px solid #ddd; margin: 5px; }
        .download-btn { 
            background: #8B5CF6; 
            color: white; 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🎨 TabBar图标生成器</h1>
    <p>点击下载按钮获取高质量的TabBar图标</p>
    
    <div id="icons"></div>
    
    <script>
        // 图标配置
        const icons = [
            { name: 'home', label: '首页', path: 'M40.5 15L15 35V65H30V50H51V65H66V35L40.5 15Z' },
            { name: 'search', label: '搜索', circle: { cx: 35, cy: 35, r: 20 }, line: 'M51 51L65 65' },
            { name: 'category', label: '分类', rects: [
                { x: 15, y: 15, w: 20, h: 20 },
                { x: 46, y: 15, w: 20, h: 20 },
                { x: 15, y: 46, w: 20, h: 20 },
                { x: 46, y: 46, w: 20, h: 20 }
            ]},
            { name: 'profile', label: '我的', circle: { cx: 40.5, cy: 30, r: 15 }, path: 'M15 65C15 52 26 42 40.5 42C55 42 66 52 66 65' }
        ];

        function createIcon(config, isActive = false) {
            const canvas = document.createElement('canvas');
            canvas.width = 81;
            canvas.height = 81;
            const ctx = canvas.getContext('2d');
            
            // 设置样式
            const color = isActive ? '#8B5CF6' : '#6B7280';
            const lineWidth = isActive ? 2 : 3;
            
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = lineWidth;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 绘制图标
            ctx.beginPath();
            
            if (config.path) {
                // 使用Path2D绘制复杂路径
                const path = new Path2D(config.path);
                if (isActive) {
                    ctx.fill(path);
                }
                ctx.stroke(path);
            }
            
            if (config.circle) {
                ctx.arc(config.circle.cx, config.circle.cy, config.circle.r, 0, 2 * Math.PI);
                if (isActive) ctx.fill();
                ctx.stroke();
            }
            
            if (config.line) {
                const coords = config.line.match(/M(\d+) (\d+)L(\d+) (\d+)/);
                if (coords) {
                    ctx.moveTo(coords[1], coords[2]);
                    ctx.lineTo(coords[3], coords[4]);
                    ctx.stroke();
                }
            }
            
            if (config.rects) {
                config.rects.forEach(rect => {
                    ctx.rect(rect.x, rect.y, rect.w, rect.h);
                    if (isActive) ctx.fill();
                    ctx.stroke();
                });
            }
            
            return canvas;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 生成所有图标
        const container = document.getElementById('icons');
        
        icons.forEach(config => {
            const div = document.createElement('div');
            div.className = 'icon-preview';
            
            const normalCanvas = createIcon(config, false);
            const activeCanvas = createIcon(config, true);
            
            div.innerHTML = `
                <h3>${config.label}</h3>
                <div>
                    ${normalCanvas.outerHTML}
                    ${activeCanvas.outerHTML}
                </div>
                <div>
                    <button class="download-btn" onclick="downloadCanvas(this.parentNode.parentNode.querySelector('canvas:first-child'), '${config.name}.png')">
                        下载普通状态
                    </button>
                    <button class="download-btn" onclick="downloadCanvas(this.parentNode.parentNode.querySelector('canvas:last-child'), '${config.name}-active.png')">
                        下载选中状态
                    </button>
                </div>
            `;
            
            container.appendChild(div);
        });
        
        // 全局下载函数
        window.downloadCanvas = downloadCanvas;
    </script>
</body>
</html>