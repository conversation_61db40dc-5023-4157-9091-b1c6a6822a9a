// 最终横幅修复验证
const { chromium } = require('playwright');

async function finalBannerVerification() {
    console.log('🎯 最终横幅修复验证...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('初始化清理横幅数据') || text.includes('横幅数据加载')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录完成');
            await page.waitForTimeout(10000); // 等待初始化完成
        }
        
        console.log('\n📍 检查初始化后的横幅数据');
        
        // 检查AdminApp中的数据
        const initialData = await page.evaluate(() => {
            return {
                hasBanners: !!AdminApp.data.banners,
                bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                sampleBanner: AdminApp.data.banners && AdminApp.data.banners.length > 0 ? {
                    title: AdminApp.data.banners[0].title,
                    status: AdminApp.data.banners[0].status,
                    priority: AdminApp.data.banners[0].priority
                } : null
            };
        });
        
        console.log('📊 初始化后的横幅数据:');
        console.log('有横幅数据:', initialData.hasBanners);
        console.log('横幅数量:', initialData.bannersCount);
        
        if (initialData.sampleBanner) {
            console.log('示例横幅:');
            console.log('  标题:', initialData.sampleBanner.title);
            console.log('  状态:', initialData.sampleBanner.status);
            console.log('  优先级:', initialData.sampleBanner.priority);
            
            if (initialData.sampleBanner.title === 'undefined' || initialData.sampleBanner.title === undefined) {
                console.log('🔴 初始化修复失败 - 标题仍然是undefined');
            } else {
                console.log('✅ 初始化修复成功 - 标题正常');
            }
        }
        
        console.log('\n📍 进入横幅管理页面');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            await page.waitForTimeout(3000);
        }
        
        console.log('\n📍 检查表格显示');
        
        // 检查表格显示
        const tableData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#banner-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const titleCell = cells[2]; // 标题列
                const statusCell = cells[8]; // 状态列
                
                return {
                    index: index + 1,
                    title: titleCell ? titleCell.textContent?.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent?.trim() : 'N/A',
                    hasImage: !!cells[1]?.querySelector('img')
                };
            });
        });
        
        console.log('📊 表格显示结果:');
        let allGood = true;
        
        tableData.forEach(row => {
            console.log(`\n横幅 ${row.index}:`);
            console.log(`  标题: "${row.title}"`);
            console.log(`  状态: "${row.status}"`);
            console.log(`  有图片: ${row.hasImage}`);
            
            if (row.title === 'undefined') {
                console.log('  🔴 标题显示undefined');
                allGood = false;
            } else if (row.title && row.title !== 'N/A') {
                console.log('  ✅ 标题显示正常');
            }
            
            if (row.status === '草稿' || row.status.includes('草稿')) {
                console.log('  🔴 状态显示错误（草稿）');
                allGood = false;
            } else if (row.status === '显示' || row.status === '隐藏') {
                console.log('  ✅ 状态显示正常');
            }
        });
        
        console.log('\n📊 最终评估:');
        if (allGood && tableData.length > 0) {
            console.log('🎉 横幅管理功能修复完全成功！');
            console.log('✅ 初始化数据处理正确');
            console.log('✅ 表格显示正常');
            console.log('✅ 标题和状态都正确');
        } else if (tableData.length === 0) {
            console.log('❌ 未找到横幅数据');
        } else {
            console.log('⚠️ 仍有部分问题');
        }
        
        // 截图
        await page.screenshot({ path: 'final-banner-verification.png', fullPage: true });
        console.log('\n📸 最终验证截图已保存: final-banner-verification.png');
        
        console.log('\n⏸️ 验证完成，浏览器将保持打开5秒供查看...');
        await page.waitForTimeout(5000);
        
    } catch (error) {
        console.error('❌ 验证过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行验证
finalBannerVerification().catch(console.error);
