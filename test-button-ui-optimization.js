const { chromium } = require('playwright');
const fs = require('fs');

async function testButtonUIOptimization() {
  console.log('🎨 测试按钮UI优化效果...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 1. 验证样式文件修改
    console.log('\n📋 步骤1：验证样式文件修改');
    
    const wxssContent = fs.readFileSync('pages/detail/detail-new.wxss', 'utf8');
    
    // 检查现代化样式特征
    const hasModernFeatures = {
      boxShadow: wxssContent.includes('box-shadow:') && wxssContent.includes('rgba'),
      borderRadius: wxssContent.includes('border-radius: 24rpx') || wxssContent.includes('border-radius: 32rpx'),
      gradients: wxssContent.includes('linear-gradient'),
      transitions: wxssContent.includes('cubic-bezier'),
      backdropFilter: wxssContent.includes('backdrop-filter'),
      modernColors: wxssContent.includes('#ff6b9d') || wxssContent.includes('#4fc3f7'),
      textShadow: wxssContent.includes('text-shadow'),
      dropShadow: wxssContent.includes('drop-shadow')
    };
    
    console.log('🎨 现代化样式特征检查:');
    Object.entries(hasModernFeatures).forEach(([feature, hasIt]) => {
      console.log(`  - ${feature}: ${hasIt ? '✅' : '❌'}`);
    });
    
    const modernFeaturesCount = Object.values(hasModernFeatures).filter(Boolean).length;
    const totalFeatures = Object.keys(hasModernFeatures).length;
    
    console.log(`📊 现代化程度: ${modernFeaturesCount}/${totalFeatures} (${Math.round(modernFeaturesCount/totalFeatures*100)}%)`);
    
    // 2. 检查按钮样式完整性
    console.log('\n📋 步骤2：检查按钮样式完整性');
    
    const buttonStyles = {
      likeBtn: wxssContent.includes('.like-btn'),
      collectBtn: wxssContent.includes('.collect-btn'),
      downloadBtn: wxssContent.includes('.download-btn'),
      shareBtn: wxssContent.includes('.share-btn'),
      likedState: wxssContent.includes('.like-btn.liked'),
      collectedState: wxssContent.includes('.collect-btn.collected'),
      activeStates: wxssContent.includes(':active'),
      beforePseudo: wxssContent.includes('::before')
    };
    
    console.log('🔘 按钮样式完整性:');
    Object.entries(buttonStyles).forEach(([style, hasIt]) => {
      console.log(`  - ${style}: ${hasIt ? '✅' : '❌'}`);
    });
    
    // 3. 获取测试数据
    console.log('\n📋 步骤3：获取测试数据');
    
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(8000);
    
    const testData = await page.evaluate(async () => {
      try {
        if (!window.cloudbase) return { error: 'SDK未加载' };
        
        const app = window.cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
        await app.auth().signInAnonymously();
        
        // 获取表情包数据
        const listResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 1 } }
        });
        
        if (!listResult.result?.success) {
          return { error: '获取表情包列表失败' };
        }
        
        const testEmoji = listResult.result.data[0];
        
        return {
          success: true,
          testEmojiId: testEmoji._id,
          testEmojiTitle: testEmoji.title
        };
        
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (testData.error) {
      console.error('❌ 数据获取失败:', testData.error);
      return;
    }
    
    console.log('✅ 获取到测试数据');
    console.log(`🎯 测试表情包: ${testData.testEmojiTitle} (${testData.testEmojiId})`);
    
    // 4. 模拟详情页访问（如果可能）
    console.log('\n📋 步骤4：模拟详情页访问');
    
    const detailUrl = `http://localhost:8080/pages/detail/detail-new?id=${testData.testEmojiId}`;
    console.log(`🔗 测试URL: ${detailUrl}`);
    
    try {
      const detailPage = await context.newPage();
      await detailPage.goto(detailUrl, { timeout: 10000 });
      await detailPage.waitForTimeout(3000);
      
      // 检查页面按钮元素
      const buttonElements = await detailPage.evaluate(() => {
        return {
          hasActionButtons: !!document.querySelector('.action-buttons'),
          hasActionRows: document.querySelectorAll('.action-row').length,
          hasLikeBtn: !!document.querySelector('.like-btn'),
          hasCollectBtn: !!document.querySelector('.collect-btn'),
          hasDownloadBtn: !!document.querySelector('.download-btn'),
          hasShareBtn: !!document.querySelector('.share-btn'),
          buttonCount: document.querySelectorAll('.action-btn').length
        };
      });
      
      console.log('🔘 页面按钮元素检查:');
      console.log(`  - 按钮容器: ${buttonElements.hasActionButtons ? '✅' : '❌'}`);
      console.log(`  - 按钮行数: ${buttonElements.hasActionRows}`);
      console.log(`  - 点赞按钮: ${buttonElements.hasLikeBtn ? '✅' : '❌'}`);
      console.log(`  - 收藏按钮: ${buttonElements.hasCollectBtn ? '✅' : '❌'}`);
      console.log(`  - 下载按钮: ${buttonElements.hasDownloadBtn ? '✅' : '❌'}`);
      console.log(`  - 分享按钮: ${buttonElements.hasShareBtn ? '✅' : '❌'}`);
      console.log(`  - 按钮总数: ${buttonElements.buttonCount}`);
      
      if (buttonElements.buttonCount === 4) {
        console.log('✅ 所有按钮都存在');
        
        // 截图保存
        await detailPage.screenshot({ 
          path: 'detail-page-buttons-optimized.png',
          fullPage: true 
        });
        console.log('📸 页面截图已保存: detail-page-buttons-optimized.png');
      } else {
        console.log('⚠️ 按钮数量不正确');
      }
      
      await detailPage.close();
      
    } catch (error) {
      console.log('ℹ️ 无法直接测试小程序页面，需要在微信开发者工具中验证');
    }
    
    // 5. 生成优化报告
    console.log('\n📋 步骤5：生成优化报告');
    
    const optimizationReport = {
      timestamp: new Date().toISOString(),
      operation: 'button_ui_optimization',
      results: {
        modernFeaturesImplemented: modernFeaturesCount,
        totalModernFeatures: totalFeatures,
        modernizationPercentage: Math.round(modernFeaturesCount/totalFeatures*100),
        buttonStylesComplete: Object.values(buttonStyles).every(Boolean),
        functionalityTested: testData.success
      },
      modernFeatures: hasModernFeatures,
      buttonStyles: buttonStyles,
      improvements: [
        '增加了现代化的阴影效果',
        '使用了渐变背景和毛玻璃效果',
        '优化了按钮圆角和间距',
        '添加了平滑的动画过渡',
        '增强了视觉层次感',
        '改进了交互反馈效果'
      ],
      testResults: testData,
      recommendations: [
        '在微信开发者工具中测试按钮视觉效果',
        '验证按钮点击交互是否流畅',
        '检查在不同设备上的显示效果',
        '确认颜色对比度符合无障碍标准'
      ]
    };
    
    fs.writeFileSync('button-ui-optimization-report.json', JSON.stringify(optimizationReport, null, 2));
    console.log('📄 优化报告已保存: button-ui-optimization-report.json');
    
    console.log('\n🎉 按钮UI优化测试完成！');
    
    if (optimizationReport.results.modernizationPercentage >= 80) {
      console.log('✅ 按钮UI已成功现代化，视觉效果大幅提升');
    } else {
      console.log('⚠️ 按钮UI优化可能不完整，需要进一步检查');
    }
    
    console.log('\n📱 请在微信开发者工具中验证：');
    console.log('1. 按钮是否显示现代化的渐变和阴影效果');
    console.log('2. 点击按钮时是否有平滑的动画反馈');
    console.log('3. 按钮布局是否美观协调');
    console.log('4. 颜色搭配是否和谐');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testButtonUIOptimization().catch(console.error);
