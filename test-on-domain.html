<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发域名测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 云开发域名测试</h1>
        
        <div class="info">
            <h3>✅ 域名访问成功！</h3>
            <p>当前访问域名：<strong id="currentDomain"></strong></p>
            <p>这意味着静态网站托管已经开启，且没有CORS问题！</p>
        </div>

        <div class="step">
            <h3>🧪 测试云开发SDK</h3>
            <button onclick="testCloudbaseSDK()">测试SDK连接</button>
            <div id="sdkTestResult"></div>
        </div>

        <div class="step">
            <h3>📊 测试数据库查询</h3>
            <button onclick="testDatabaseQuery()">查询数据库</button>
            <div id="dbTestResult"></div>
        </div>

        <div class="step">
            <h3>🔧 测试数据创建</h3>
            <button onclick="testDataCreation()">创建测试数据</button>
            <div id="createTestResult"></div>
        </div>

        <div class="step">
            <h3>☁️ 测试云函数调用</h3>
            <button onclick="testCloudFunction()">调用dataAPI</button>
            <div id="functionTestResult"></div>
        </div>

        <div class="step">
            <h3>📋 完整诊断报告</h3>
            <button onclick="generateReport()">生成报告</button>
            <div id="reportResult"></div>
        </div>
    </div>

    <!-- 使用最新的云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let app = null;
        let db = null;
        let testResults = {};

        // 显示当前域名
        document.getElementById('currentDomain').textContent = window.location.host;

        // 测试云开发SDK
        async function testCloudbaseSDK() {
            const resultDiv = document.getElementById('sdkTestResult');
            resultDiv.innerHTML = '<p>正在测试SDK...</p>';

            try {
                // 检查SDK是否加载
                if (typeof cloudbase === 'undefined') {
                    throw new Error('cloudbase SDK未加载');
                }

                // 初始化云开发
                app = cloudbase.init({ env: ENV_ID });
                
                // 匿名登录
                await app.auth().signInAnonymously();
                
                // 获取数据库实例
                db = app.database();

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ SDK测试成功</h4>
                        <p>SDK版本: 2.19.1</p>
                        <p>环境ID: ${ENV_ID}</p>
                        <p>认证状态: 匿名登录成功</p>
                        <p>数据库实例: 已获取</p>
                    </div>
                `;

                testResults.sdk = { success: true };
                return true;

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ SDK测试失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.sdk = { success: false, error: error.message };
                return false;
            }
        }

        // 测试数据库查询
        async function testDatabaseQuery() {
            const resultDiv = document.getElementById('dbTestResult');
            resultDiv.innerHTML = '<p>正在查询数据库...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先测试SDK连接</h4></div>';
                return;
            }

            try {
                // 查询各个集合
                const [categoriesResult, emojisResult, bannersResult] = await Promise.all([
                    db.collection('categories').get(),
                    db.collection('emojis').get(),
                    db.collection('banners').get()
                ]);

                const categoriesCount = categoriesResult.data.length;
                const emojisCount = emojisResult.data.length;
                const bannersCount = bannersResult.data.length;

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 数据库查询成功</h4>
                        <p>分类数量: ${categoriesCount}</p>
                        <p>表情包数量: ${emojisCount}</p>
                        <p>轮播图数量: ${bannersCount}</p>
                        
                        ${categoriesCount > 0 ? '<p>✅ 有分类数据</p>' : '<p>⚠️ 没有分类数据</p>'}
                        ${emojisCount > 0 ? '<p>✅ 有表情包数据</p>' : '<p>⚠️ 没有表情包数据</p>'}
                    </div>
                `;

                testResults.database = {
                    success: true,
                    categoriesCount,
                    emojisCount,
                    bannersCount
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 数据库查询失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.database = { success: false, error: error.message };
            }
        }

        // 测试数据创建
        async function testDataCreation() {
            const resultDiv = document.getElementById('createTestResult');
            resultDiv.innerHTML = '<p>正在创建测试数据...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先测试SDK连接</h4></div>';
                return;
            }

            try {
                const timestamp = Date.now();
                
                // 创建测试分类
                const testCategory = {
                    name: `域名测试分类_${timestamp}`,
                    icon: '🌐',
                    sort: 999,
                    status: 'active',
                    description: '域名测试创建的分类',
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const categoryResult = await db.collection('categories').add({
                    data: testCategory
                });

                // 创建测试表情包
                const testEmoji = {
                    title: `域名测试表情包_${timestamp}`,
                    category: testCategory.name,
                    description: '域名测试创建的表情包',
                    imageUrl: 'https://via.placeholder.com/150x150?text=DomainTest',
                    status: 'published',
                    likes: 0,
                    downloads: 0,
                    collections: 0,
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const emojiResult = await db.collection('emojis').add({
                    data: testEmoji
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 数据创建成功</h4>
                        <p>分类ID: ${categoryResult._id}</p>
                        <p>表情包ID: ${emojiResult._id}</p>
                        <p>分类名称: ${testCategory.name}</p>
                        <p>表情包标题: ${testEmoji.title}</p>
                        
                        <h5>这证明：</h5>
                        <ul>
                            <li>✅ 数据库写入权限正常</li>
                            <li>✅ 管理后台可以正常创建数据</li>
                            <li>✅ 没有CORS跨域问题</li>
                        </ul>
                    </div>
                `;

                testResults.creation = {
                    success: true,
                    categoryId: categoryResult._id,
                    emojiId: emojiResult._id
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 数据创建失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.creation = { success: false, error: error.message };
            }
        }

        // 测试云函数调用
        async function testCloudFunction() {
            const resultDiv = document.getElementById('functionTestResult');
            resultDiv.innerHTML = '<p>正在测试云函数...</p>';

            if (!app) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先测试SDK连接</h4></div>';
                return;
            }

            try {
                // 测试dataAPI云函数
                const result = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ 云函数调用成功</h4>
                        <p>函数名: dataAPI</p>
                        <p>调用结果: ${result.result.success ? '成功' : '失败'}</p>
                        <p>返回数据量: ${result.result.data?.length || 0}</p>
                        
                        <h5>返回结果:</h5>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    </div>
                `;

                testResults.cloudFunction = {
                    success: true,
                    result: result.result
                };

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 云函数调用失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
                testResults.cloudFunction = { success: false, error: error.message };
            }
        }

        // 生成完整报告
        function generateReport() {
            const resultDiv = document.getElementById('reportResult');
            
            const report = {
                timestamp: new Date().toISOString(),
                domain: window.location.host,
                environment: ENV_ID,
                testResults: testResults
            };

            // 分析结果
            const issues = [];
            const successes = [];

            if (testResults.sdk?.success) {
                successes.push('✅ SDK连接正常');
            } else {
                issues.push('❌ SDK连接失败');
            }

            if (testResults.database?.success) {
                successes.push('✅ 数据库查询正常');
                if (testResults.database.emojisCount === 0) {
                    issues.push('⚠️ 数据库中没有表情包数据');
                }
            } else {
                issues.push('❌ 数据库查询失败');
            }

            if (testResults.creation?.success) {
                successes.push('✅ 数据创建正常');
            } else {
                issues.push('❌ 数据创建失败');
            }

            if (testResults.cloudFunction?.success) {
                successes.push('✅ 云函数调用正常');
            } else {
                issues.push('❌ 云函数调用失败');
            }

            let html = `
                <div class="info">
                    <h4>📊 完整诊断报告</h4>
                    <p><strong>测试时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>访问域名:</strong> ${window.location.host}</p>
                    <p><strong>环境ID:</strong> ${ENV_ID}</p>
                    
                    <h5>✅ 成功项目:</h5>
                    <ul>
                        ${successes.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                    
                    ${issues.length > 0 ? `
                        <h5>❌ 问题项目:</h5>
                        <ul>
                            ${issues.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    ` : ''}
                    
                    <h5>🎯 结论:</h5>
                    ${issues.length === 0 ? 
                        '<p style="color: green; font-weight: bold;">🎉 所有功能正常！数据同步问题已解决！</p>' :
                        '<p style="color: orange;">⚠️ 还有一些问题需要解决，请查看上面的问题列表。</p>'
                    }
                    
                    <h5>📋 详细数据:</h5>
                    <pre>${JSON.stringify(report, null, 2)}</pre>
                </div>
            `;

            resultDiv.innerHTML = html;
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🌐 域名测试页面加载完成');
            console.log('当前域名:', window.location.host);
            console.log('环境ID:', ENV_ID);
        });
    </script>
</body>
</html>
