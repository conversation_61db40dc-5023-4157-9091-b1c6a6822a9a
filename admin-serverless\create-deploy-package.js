/**
 * 创建部署包
 * 将所有需要的文件复制到一个deploy文件夹中
 */

const fs = require('fs');
const path = require('path');

console.log('📦 创建部署包...\n');

// 创建deploy目录
const deployDir = path.join(__dirname, 'deploy');
if (!fs.existsSync(deployDir)) {
    fs.mkdirSync(deployDir);
    console.log('✅ 创建 deploy 目录');
} else {
    console.log('📁 deploy 目录已存在');
}

// 需要复制的文件
const filesToCopy = [
    'index.html',
    'js/app.js',
    'test.html'
];

// 复制文件
filesToCopy.forEach(file => {
    const sourcePath = path.join(__dirname, file);
    const targetPath = path.join(deployDir, file);
    
    // 确保目标目录存在
    const targetDir = path.dirname(targetPath);
    if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
    }
    
    if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ 复制 ${file}`);
    } else {
        console.log(`❌ 文件不存在: ${file}`);
    }
});

// 创建部署说明文件
const deployReadme = `# 管理后台部署包

## 📁 文件说明
- index.html - 主页面
- js/app.js - 应用逻辑
- test.html - 测试页面

## 🚀 部署步骤

### 方法1：微信开发者工具
1. 打开微信开发者工具
2. 选择你的小程序项目
3. 点击"云开发"
4. 进入"静态网站托管"
5. 创建 "admin" 文件夹
6. 将本文件夹中的所有文件上传到 admin 文件夹

### 方法2：云开发控制台
1. 访问 https://console.cloud.tencent.com/tcb
2. 选择环境: cloud1-5g6pvnpl88dc0142
3. 进入"静态网站托管"
4. 上传文件

## 🌐 访问地址
部署完成后访问：
https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/

## 🧪 测试
- 主页面: /admin/
- 测试页面: /admin/test.html

## ⚠️ 注意事项
1. 确保云函数 adminAPI 和 dataAPI 已部署
2. 首次访问可能显示模拟数据
3. 如有问题请检查浏览器控制台
`;

fs.writeFileSync(path.join(deployDir, 'README.md'), deployReadme);
console.log('✅ 创建 README.md');

console.log('\n🎉 部署包创建完成！');
console.log('📁 位置: ./deploy/');
console.log('📖 请查看 deploy/README.md 获取部署说明');

// 列出deploy目录内容
console.log('\n📋 部署包内容:');
function listDirectory(dir, prefix = '') {
    const items = fs.readdirSync(dir);
    items.forEach(item => {
        const fullPath = path.join(dir, item);
        if (fs.statSync(fullPath).isDirectory()) {
            console.log(`${prefix}📁 ${item}/`);
            listDirectory(fullPath, prefix + '  ');
        } else {
            console.log(`${prefix}📄 ${item}`);
        }
    });
}

listDirectory(deployDir);
