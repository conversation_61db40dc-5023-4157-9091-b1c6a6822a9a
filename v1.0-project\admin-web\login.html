<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台登录 - V1.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            position: relative;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }

        .form-input.error {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .login-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background: #fdf2f2;
            border-radius: 6px;
            border-left: 4px solid #e74c3c;
            display: none;
        }

        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background: #f2fdf2;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-accounts {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .demo-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
            color: #6c757d;
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .version-info {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">管理后台</h1>
            <p class="login-subtitle">V1.0 核心优化版本</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input 
                    type="text" 
                    id="username" 
                    class="form-input" 
                    placeholder="请输入用户名"
                    autocomplete="username"
                    required
                >
            </div>

            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input 
                    type="password" 
                    id="password" 
                    class="form-input" 
                    placeholder="请输入密码"
                    autocomplete="current-password"
                    required
                >
            </div>

            <button type="submit" class="login-button" id="loginButton">
                登录
            </button>

            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                正在登录...
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </form>

        <div class="demo-accounts">
            <div class="demo-title">测试账户</div>
            <div class="demo-account">
                <span>管理员:</span>
                <span>admin / admin123456</span>
            </div>
            <div class="demo-account">
                <span>经理:</span>
                <span>manager / manager123456</span>
            </div>
        </div>

        <div class="version-info">
            V1.0 - JWT认证 + 实时同步 + 事务保护
        </div>
    </div>

    <!-- CloudBase Web SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.10.10/tcb.js"></script>
    
    <!-- 认证管理器 -->
    <script src="./js/auth-manager.js"></script>

    <script>
        // 登录页面逻辑
        class LoginPage {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.usernameInput = document.getElementById('username');
                this.passwordInput = document.getElementById('password');
                this.loginButton = document.getElementById('loginButton');
                this.loading = document.getElementById('loading');
                this.errorMessage = document.getElementById('errorMessage');
                this.successMessage = document.getElementById('successMessage');
                
                this.initEventListeners();
                this.checkExistingLogin();
            }

            initEventListeners() {
                // 表单提交
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 输入框回车
                this.passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin();
                    }
                });

                // 清除错误状态
                [this.usernameInput, this.passwordInput].forEach(input => {
                    input.addEventListener('input', () => {
                        this.clearMessages();
                        input.classList.remove('error');
                    });
                });
            }

            async checkExistingLogin() {
                // 如果已经登录，直接跳转
                if (window.authManager.isLoggedIn()) {
                    console.log('✅ 已登录，跳转到管理页面');
                    window.location.href = './index.html';
                }
            }

            async handleLogin() {
                const username = this.usernameInput.value.trim();
                const password = this.passwordInput.value;

                // 输入验证
                if (!username) {
                    this.showError('请输入用户名');
                    this.usernameInput.classList.add('error');
                    this.usernameInput.focus();
                    return;
                }

                if (!password) {
                    this.showError('请输入密码');
                    this.passwordInput.classList.add('error');
                    this.passwordInput.focus();
                    return;
                }

                // 显示加载状态
                this.setLoading(true);

                try {
                    console.log('🔐 开始登录流程...');
                    
                    const result = await window.authManager.login(username, password);

                    if (result.success) {
                        this.showSuccess('登录成功，正在跳转...');
                        
                        // 延迟跳转，让用户看到成功提示
                        setTimeout(() => {
                            window.location.href = './index.html';
                        }, 1000);
                    } else {
                        this.showError(result.error || '登录失败');
                        
                        // 标记错误的输入框
                        if (result.error && result.error.includes('用户名或密码')) {
                            this.usernameInput.classList.add('error');
                            this.passwordInput.classList.add('error');
                        }
                    }
                } catch (error) {
                    console.error('❌ 登录异常:', error);
                    this.showError('登录失败，请检查网络连接');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(isLoading) {
                this.loginButton.disabled = isLoading;
                this.loading.style.display = isLoading ? 'block' : 'none';
                this.loginButton.textContent = isLoading ? '登录中...' : '登录';
            }

            showError(message) {
                this.clearMessages();
                this.errorMessage.textContent = message;
                this.errorMessage.style.display = 'block';
            }

            showSuccess(message) {
                this.clearMessages();
                this.successMessage.textContent = message;
                this.successMessage.style.display = 'block';
            }

            clearMessages() {
                this.errorMessage.style.display = 'none';
                this.successMessage.style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 登录页面初始化');
            new LoginPage();
        });

        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('🚨 页面错误:', event.error);
        });

        // 全局未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 未处理的Promise错误:', event.reason);
        });
    </script>
</body>
</html>
