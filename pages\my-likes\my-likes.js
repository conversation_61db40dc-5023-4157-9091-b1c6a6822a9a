// pages/my-likes/my-likes.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')

Page({
  data: {
    likedEmojis: [],
    loading: false,
    isEmpty: false
  },

  onLoad() {
    this.loadLikedEmojis()
  },

  onShow() {
    // 每次显示时刷新数据，确保与详情页同步
    this.loadLikedEmojis()
    
    // 监听状态变化
    this.stateListener = (data) => {
      if (data.type === 'like') {
        this.loadLikedEmojis()
      }
    }
    StateManager.addListener('global', this.stateListener)
  },

  onHide() {
    // 移除状态监听器
    if (this.stateListener) {
      StateManager.removeListener('global', this.stateListener)
      this.stateListener = null
    }
  },

  async loadLikedEmojis() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 使用StateManager获取点赞的表情包ID列表
      const likedEmojiIds = StateManager.getLikedEmojis()

      if (likedEmojiIds.length === 0) {
        this.setData({
          likedEmojis: [],
          isEmpty: true,
          loading: false
        })
        return
      }

      // 从全局数据管理器获取完整的表情包信息
      const likedEmojis = []

      for (const emojiId of likedEmojiIds) {
        let emojiData = DataManager.getEmojiDataFromCache(emojiId)

        // 如果缓存中没有，尝试从数据库获取
        if (!emojiData) {
          try {
            emojiData = await DataManager.getEmojiDataFromDatabase(emojiId)
          } catch (error) {
            console.error(`获取表情包 ${emojiId} 失败:`, error)
            continue
          }
        }

        if (emojiData) {
          const emojiState = StateManager.getEmojiState(emojiId)
          likedEmojis.push({
            ...emojiData,
            // 确保 likes 和 collections 有默认值
            likes: emojiData.likes || 0,
            collections: emojiData.collections || 0,
            likesText: DataManager.formatNumber(emojiData.likes || 0),
            collectionsText: DataManager.formatNumber(emojiData.collections || 0),
            isLiked: emojiState.isLiked,
            isCollected: emojiState.isCollected
          })
        } else {
          console.warn(`表情包 ${emojiId} 可能已被删除，从点赞列表中移除`)
          // 从点赞列表中移除不存在的表情包
          if (StateManager.isLiked(emojiId)) {
            StateManager.toggleLike(emojiId)
          }
        }
      }

      this.setData({
        likedEmojis: likedEmojis,
        isEmpty: likedEmojis.length === 0,
        loading: false
      })

      console.log('✅ 我的点赞列表加载完成:', likedEmojis.length, '个表情包')
    } catch (error) {
      console.error('❌ 加载点赞列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({
        likedEmojis: [],
        isEmpty: true,
        loading: false
      })
    }
  },

  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    })
  },

  // 取消点赞操作
  onUnlikeEmoji(e) {
    const emojiId = e.currentTarget.dataset.id

    try {
      // 使用StateManager切换点赞状态
      const newIsLiked = StateManager.toggleLike(emojiId)

      if (!newIsLiked) {
        // 更新全局数据中的点赞数
        const emojiData = DataManager.getEmojiData(emojiId)
        if (emojiData) {
          const newLikes = Math.max(0, emojiData.likes - 1)
          DataManager.updateLikes(emojiId, newLikes)
        }

        // 刷新列表
        this.loadLikedEmojis()

        wx.showToast({
          title: '已取消点赞',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('❌ 取消点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },

  onPullDownRefresh() {
    this.loadLikedEmojis()
    wx.stopPullDownRefresh()
  },

  // 去探索更多表情包
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})