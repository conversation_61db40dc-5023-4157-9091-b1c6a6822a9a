const { chromium } = require('playwright');
const fs = require('fs');

async function testTagsHidden() {
  console.log('🏷️ 测试标签隐藏效果...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 1. 验证WXML文件修改
    console.log('\n📋 步骤1：验证WXML文件修改');
    
    const wxmlContent = fs.readFileSync('pages/detail/detail-new.wxml', 'utf8');
    
    // 检查标签代码是否被注释
    const hasCommentedTags = wxmlContent.includes('<!-- 标签 - 已隐藏保持页面简洁 -->');
    const hasActiveTagsContainer = wxmlContent.includes('<view class="tags-container"') && 
                                   !wxmlContent.includes('<!-- <view class="tags-container"');
    
    if (hasCommentedTags) {
      console.log('✅ 标签代码已被注释');
    } else {
      console.error('❌ 标签代码未被正确注释');
    }
    
    if (!hasActiveTagsContainer) {
      console.log('✅ 活动的标签容器已移除');
    } else {
      console.error('❌ 仍存在活动的标签容器');
    }
    
    // 2. 获取测试数据
    console.log('\n📋 步骤2：获取测试数据');
    
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(8000);
    
    const testData = await page.evaluate(async () => {
      try {
        if (!window.cloudbase) return { error: 'SDK未加载' };
        
        const app = window.cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
        await app.auth().signInAnonymously();
        
        // 获取表情包数据
        const listResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 3 } }
        });
        
        if (!listResult.result?.success) {
          return { error: '获取表情包列表失败' };
        }
        
        const emojis = listResult.result.data;
        const testEmoji = emojis[0];
        
        // 获取详情数据
        const detailResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojiDetail', data: { id: testEmoji._id } }
        });
        
        return {
          success: true,
          testEmojiId: testEmoji._id,
          testEmojiTitle: testEmoji.title,
          detailSuccess: detailResult.result?.success,
          detailData: detailResult.result?.data
        };
        
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (testData.error) {
      console.error('❌ 数据获取失败:', testData.error);
      return;
    }
    
    console.log('✅ 获取到测试数据');
    console.log(`🎯 测试表情包: ${testData.testEmojiTitle} (${testData.testEmojiId})`);
    
    // 3. 模拟详情页访问（如果可能）
    console.log('\n📋 步骤3：模拟详情页访问');
    
    const detailUrl = `http://localhost:8080/pages/detail/detail-new?id=${testData.testEmojiId}`;
    console.log(`🔗 测试URL: ${detailUrl}`);
    
    try {
      const detailPage = await context.newPage();
      await detailPage.goto(detailUrl, { timeout: 10000 });
      await detailPage.waitForTimeout(3000);
      
      // 检查页面内容
      const pageContent = await detailPage.evaluate(() => {
        return {
          hasTagsContainer: !!document.querySelector('.tags-container'),
          hasTagItems: !!document.querySelector('.tag-item'),
          hasTagText: !!document.querySelector('.tag-text'),
          bodyContent: document.body.innerHTML.substring(0, 1000),
          title: document.title
        };
      });
      
      console.log('📄 页面内容检查:');
      console.log(`  - 有标签容器: ${pageContent.hasTagsContainer ? '❌' : '✅'}`);
      console.log(`  - 有标签项目: ${pageContent.hasTagItems ? '❌' : '✅'}`);
      console.log(`  - 有标签文本: ${pageContent.hasTagText ? '❌' : '✅'}`);
      
      if (!pageContent.hasTagsContainer && !pageContent.hasTagItems && !pageContent.hasTagText) {
        console.log('✅ 标签已成功隐藏');
        
        // 截图保存
        await detailPage.screenshot({ 
          path: 'detail-page-tags-hidden.png',
          fullPage: true 
        });
        console.log('📸 页面截图已保存: detail-page-tags-hidden.png');
      } else {
        console.log('⚠️ 标签可能仍然显示');
      }
      
      await detailPage.close();
      
    } catch (error) {
      console.log('ℹ️ 无法直接测试小程序页面，需要在微信开发者工具中验证');
    }
    
    // 4. 验证相关样式文件
    console.log('\n📋 步骤4：验证相关样式文件');
    
    const wxssContent = fs.readFileSync('pages/detail/detail-new.wxss', 'utf8');
    
    // 检查是否有标签相关样式
    const hasTagStyles = wxssContent.includes('.tags-container') || 
                        wxssContent.includes('.tag-item') || 
                        wxssContent.includes('.tag-text');
    
    if (hasTagStyles) {
      console.log('ℹ️ 样式文件中仍有标签样式（可保留，不影响功能）');
    } else {
      console.log('✅ 样式文件中无标签样式');
    }
    
    // 5. 生成隐藏报告
    console.log('\n📋 步骤5：生成隐藏报告');
    
    const hidingReport = {
      timestamp: new Date().toISOString(),
      operation: 'tags_hiding',
      results: {
        wxmlModified: hasCommentedTags && !hasActiveTagsContainer,
        tagsCommented: hasCommentedTags,
        activeTagsRemoved: !hasActiveTagsContainer,
        stylesPreserved: hasTagStyles,
        functionalityTested: testData.success
      },
      modifications: [
        '在detail-new.wxml中注释了标签容器代码',
        '保持了代码结构完整性',
        '添加了说明注释'
      ],
      testResults: testData,
      recommendations: [
        '在微信开发者工具中验证页面显示效果',
        '确认页面布局没有异常',
        '检查其他页面功能是否正常'
      ]
    };
    
    fs.writeFileSync('tags-hiding-report.json', JSON.stringify(hidingReport, null, 2));
    console.log('📄 隐藏报告已保存: tags-hiding-report.json');
    
    console.log('\n🎉 标签隐藏测试完成！');
    
    if (hidingReport.results.wxmlModified) {
      console.log('✅ 标签已成功隐藏，页面更简洁');
    } else {
      console.log('⚠️ 标签隐藏可能不完整，需要检查');
    }
    
    console.log('\n📱 请在微信开发者工具中验证：');
    console.log('1. 打开详情页，确认标签不再显示');
    console.log('2. 检查页面布局是否正常');
    console.log('3. 确认其他功能正常工作');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testTagsHidden().catch(console.error);
