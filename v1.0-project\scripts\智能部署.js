#!/usr/bin/env node

// V1.0 智能部署脚本 - 自动检测环境并部署
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class SmartDeployer {
  constructor() {
    this.envId = process.env.TCB_ENV || null;
    this.projectRoot = path.resolve(__dirname, '..');
    this.logFile = path.join(this.projectRoot, 'deploy.log');
    this.startTime = Date.now();
  }

  // 彩色日志输出
  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
      info: '\x1b[36m',    // 青色
      success: '\x1b[32m', // 绿色
      warning: '\x1b[33m', // 黄色
      error: '\x1b[31m',   // 红色
      reset: '\x1b[0m'     // 重置
    };

    const coloredMessage = `${colors[type]}[${timestamp}] ${message}${colors.reset}`;
    console.log(coloredMessage);

    // 同时写入日志文件
    const logMessage = `[${timestamp}] ${message}\n`;
    fs.appendFileSync(this.logFile, logMessage);
  }

  // 执行命令并显示输出
  runCommand(command, description) {
    this.log(`🔄 ${description}...`, 'info');
    try {
      const output = execSync(command, { 
        encoding: 'utf8',
        cwd: this.projectRoot,
        stdio: 'pipe'
      });
      this.log(`✅ ${description}完成`, 'success');
      return output;
    } catch (error) {
      this.log(`❌ ${description}失败: ${error.message}`, 'error');
      throw error;
    }
  }

  // 检查环境
  async checkEnvironment() {
    this.log('🔍 检查部署环境...', 'info');

    // 检查Node.js版本
    try {
      const nodeVersion = process.version;
      this.log(`✅ Node.js版本: ${nodeVersion}`, 'success');
    } catch (error) {
      this.log('❌ Node.js未安装', 'error');
      throw new Error('请先安装Node.js 14.0+');
    }

    // 检查CloudBase CLI
    try {
      const tcbVersion = this.runCommand('tcb --version', '检查CloudBase CLI');
      this.log(`✅ CloudBase CLI版本: ${tcbVersion.trim()}`, 'success');
    } catch (error) {
      this.log('❌ CloudBase CLI未安装', 'error');
      this.log('💡 正在自动安装CloudBase CLI...', 'info');
      this.runCommand('npm install -g @cloudbase/cli', '安装CloudBase CLI');
    }

    // 检查登录状态
    try {
      this.runCommand('tcb env:list', '检查登录状态');
      this.log('✅ 已登录腾讯云', 'success');
    } catch (error) {
      this.log('❌ 未登录腾讯云', 'error');
      this.log('💡 请运行: tcb login', 'warning');
      throw new Error('请先登录腾讯云: tcb login');
    }
  }

  // 自动检测或创建环境
  async setupEnvironment() {
    if (!this.envId) {
      this.log('🔍 未指定环境ID，尝试自动检测...', 'info');
      
      try {
        const envList = this.runCommand('tcb env:list --json', '获取环境列表');
        const environments = JSON.parse(envList);
        
        // 查找表情包相关的环境
        const emojiEnv = environments.find(env => 
          env.alias && env.alias.includes('表情包') || 
          env.envId.includes('emoji')
        );

        if (emojiEnv) {
          this.envId = emojiEnv.envId;
          this.log(`✅ 自动检测到环境: ${this.envId}`, 'success');
        } else {
          this.log('🆕 未找到现有环境，创建新环境...', 'info');
          await this.createNewEnvironment();
        }
      } catch (error) {
        this.log('⚠️ 环境检测失败，创建新环境...', 'warning');
        await this.createNewEnvironment();
      }
    } else {
      this.log(`✅ 使用指定环境: ${this.envId}`, 'success');
    }
  }

  // 创建新环境
  async createNewEnvironment() {
    const envName = `emoji-v1-${Date.now().toString().slice(-6)}`;
    
    try {
      this.runCommand(
        `tcb env:create ${envName} --alias "表情包小程序V1.0"`,
        '创建云开发环境'
      );
      
      // 等待环境创建完成
      this.log('⏳ 等待环境初始化...', 'info');
      await this.sleep(10000); // 等待10秒
      
      // 获取新创建的环境ID
      const envList = this.runCommand('tcb env:list --json', '获取环境列表');
      const environments = JSON.parse(envList);
      const newEnv = environments.find(env => env.alias && env.alias.includes('表情包'));
      
      if (newEnv) {
        this.envId = newEnv.envId;
        this.log(`✅ 新环境创建成功: ${this.envId}`, 'success');
      } else {
        throw new Error('环境创建失败');
      }
    } catch (error) {
      this.log('❌ 环境创建失败', 'error');
      throw error;
    }
  }

  // 更新配置文件
  async updateConfigs() {
    this.log('🔧 更新配置文件...', 'info');

    const configFiles = [
      {
        path: 'config/production.js',
        pattern: /ENV_ID:\s*['"`].*?['"`]/,
        replacement: `ENV_ID: '${this.envId}'`
      },
      {
        path: 'miniprogram/app.js',
        pattern: /env:\s*['"`].*?['"`]/,
        replacement: `env: '${this.envId}'`
      },
      {
        path: 'admin-web/js/auth-manager.js',
        pattern: /env:\s*['"`].*?['"`]/,
        replacement: `env: '${this.envId}'`
      }
    ];

    for (const config of configFiles) {
      const filePath = path.join(this.projectRoot, config.path);
      
      if (fs.existsSync(filePath)) {
        let content = fs.readFileSync(filePath, 'utf8');
        content = content.replace(config.pattern, config.replacement);
        fs.writeFileSync(filePath, content);
        this.log(`✅ 更新 ${config.path}`, 'success');
      } else {
        this.log(`⚠️ 文件不存在: ${config.path}`, 'warning');
      }
    }
  }

  // 安装依赖
  async installDependencies() {
    this.log('📦 安装项目依赖...', 'info');

    const cloudFunctions = ['loginAPI', 'webAdminAPI', 'dataAPI'];
    
    for (const func of cloudFunctions) {
      const funcPath = path.join(this.projectRoot, 'cloudfunctions', func);
      if (fs.existsSync(funcPath)) {
        try {
          this.runCommand('npm install', `安装 ${func} 依赖`);
        } catch (error) {
          this.log(`⚠️ ${func} 依赖安装失败，继续部署...`, 'warning');
        }
      }
    }

    // 安装项目根目录依赖
    try {
      this.runCommand('npm install', '安装项目依赖');
    } catch (error) {
      this.log('⚠️ 项目依赖安装失败，继续部署...', 'warning');
    }
  }

  // 部署云函数
  async deployCloudfunctions() {
    this.log('☁️ 部署云函数...', 'info');

    const functions = ['loginAPI', 'webAdminAPI', 'dataAPI'];
    
    for (const func of functions) {
      const funcPath = path.join(this.projectRoot, 'cloudfunctions', func);
      
      if (fs.existsSync(funcPath)) {
        try {
          this.runCommand(
            `tcb functions:deploy ${func} --env ${this.envId}`,
            `部署 ${func} 云函数`
          );
        } catch (error) {
          this.log(`❌ ${func} 部署失败: ${error.message}`, 'error');
          throw error;
        }
      } else {
        this.log(`⚠️ 云函数目录不存在: ${func}`, 'warning');
      }
    }
  }

  // 初始化数据库
  async initDatabase() {
    this.log('🗄️ 初始化数据库...', 'info');

    const collections = [
      'categories',
      'emojis', 
      'banners',
      'sync_notifications',
      'admin_logs'
    ];

    for (const collection of collections) {
      try {
        this.runCommand(
          `tcb db:createCollection ${collection} --env ${this.envId}`,
          `创建 ${collection} 集合`
        );
      } catch (error) {
        if (error.message.includes('already exists')) {
          this.log(`✅ ${collection} 集合已存在`, 'success');
        } else {
          this.log(`⚠️ ${collection} 集合创建失败，继续...`, 'warning');
        }
      }
    }
  }

  // 部署静态网站
  async deployWebsite() {
    this.log('🌐 部署管理后台...', 'info');

    const webPath = path.join(this.projectRoot, 'admin-web');
    
    if (fs.existsSync(webPath)) {
      try {
        this.runCommand(
          `tcb hosting:deploy admin-web --env ${this.envId}`,
          '部署管理后台'
        );
        
        // 获取访问地址
        const hostingInfo = this.runCommand(
          `tcb hosting:detail --env ${this.envId}`,
          '获取访问地址'
        );
        
        this.log(`🌐 管理后台地址: https://${this.envId}.service.tcloudbase.com`, 'success');
      } catch (error) {
        this.log(`❌ 管理后台部署失败: ${error.message}`, 'error');
        throw error;
      }
    } else {
      this.log('⚠️ 管理后台目录不存在', 'warning');
    }
  }

  // 验证部署
  async verifyDeployment() {
    this.log('🔍 验证部署结果...', 'info');

    try {
      // 检查云函数
      const functions = this.runCommand(`tcb functions:list --env ${this.envId}`, '检查云函数');
      this.log('✅ 云函数部署验证通过', 'success');

      // 检查数据库
      const collections = this.runCommand(`tcb db:collection:list --env ${this.envId}`, '检查数据库');
      this.log('✅ 数据库初始化验证通过', 'success');

      // 检查静态网站
      const hosting = this.runCommand(`tcb hosting:detail --env ${this.envId}`, '检查静态网站');
      this.log('✅ 静态网站部署验证通过', 'success');

    } catch (error) {
      this.log('⚠️ 部分验证失败，但部署可能已成功', 'warning');
    }
  }

  // 显示部署结果
  showResults() {
    const duration = ((Date.now() - this.startTime) / 1000).toFixed(1);
    
    this.log('', 'info');
    this.log('🎉 ===============================', 'success');
    this.log('🎉 V1.0系统部署完成！', 'success');
    this.log('🎉 ===============================', 'success');
    this.log('', 'info');
    this.log(`⏱️ 部署耗时: ${duration}秒`, 'info');
    this.log(`🌐 环境ID: ${this.envId}`, 'info');
    this.log(`🖥️ 管理后台: https://${this.envId}.service.tcloudbase.com`, 'info');
    this.log(`👤 管理员账号: admin`, 'info');
    this.log(`🔑 管理员密码: admin123456 (请登录后修改)`, 'warning');
    this.log('', 'info');
    this.log('📱 下一步：配置小程序', 'info');
    this.log(`   1. 打开微信开发者工具`, 'info');
    this.log(`   2. 导入 miniprogram 文件夹`, 'info');
    this.log(`   3. 在 app.js 中确认环境ID: ${this.envId}`, 'info');
    this.log(`   4. 预览测试后上传发布`, 'info');
    this.log('', 'info');
    this.log(`📄 详细日志: ${this.logFile}`, 'info');
  }

  // 工具函数：延迟
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 主部署流程
  async deploy() {
    try {
      this.log('🚀 开始V1.0智能部署...', 'info');
      this.log(`📅 部署时间: ${new Date().toLocaleString()}`, 'info');
      
      // 清空日志文件
      fs.writeFileSync(this.logFile, '');
      
      await this.checkEnvironment();
      await this.setupEnvironment();
      await this.updateConfigs();
      await this.installDependencies();
      await this.deployCloudfunctions();
      await this.initDatabase();
      await this.deployWebsite();
      await this.verifyDeployment();
      
      this.showResults();
      
    } catch (error) {
      this.log('', 'error');
      this.log('❌ ===============================', 'error');
      this.log('❌ 部署失败！', 'error');
      this.log('❌ ===============================', 'error');
      this.log(`❌ 错误信息: ${error.message}`, 'error');
      this.log('', 'error');
      this.log('💡 解决建议:', 'info');
      this.log('   1. 检查网络连接', 'info');
      this.log('   2. 确认已登录腾讯云: tcb login', 'info');
      this.log('   3. 检查账户权限和余额', 'info');
      this.log('   4. 查看详细日志: ' + this.logFile, 'info');
      
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const deployer = new SmartDeployer();
  await deployer.deploy();
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = SmartDeployer;
