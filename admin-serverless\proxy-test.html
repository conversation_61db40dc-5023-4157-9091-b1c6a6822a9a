<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>代理连接测试</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        button { background: #333; color: #0f0; border: 1px solid #0f0; padding: 10px; margin: 5px; }
        button:hover { background: #0f0; color: #000; }
        #log { background: #111; border: 1px solid #333; padding: 10px; height: 400px; overflow-y: auto; }
        .success { color: #0f0; }
        .error { color: #f00; }
        .info { color: #ff0; }
        .header { color: #0ff; font-size: 18px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">🔗 通过本地代理测试云开发连接</div>
    <p>代理服务器: http://localhost:3001</p>
    <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
    
    <button onclick="testHealth()">健康检查</button>
    <button onclick="testStats()">获取统计</button>
    <button onclick="testCategories()">获取分类</button>
    <button onclick="testEmojis()">获取表情</button>
    <button onclick="clearLog()">清空日志</button>
    
    <div id="log"></div>

    <script>
        const PROXY_URL = 'http://localhost:3001';
        
        function log(msg, type = 'info') {
            const div = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            div.innerHTML += `<div class="${type}">[${time}] ${msg}</div>`;
            div.scrollTop = div.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 健康检查
        async function testHealth() {
            log('🏥 检查代理服务器健康状态...', 'info');
            try {
                const response = await fetch(`${PROXY_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 代理服务器正常运行`, 'success');
                    log(`📊 状态: ${data.status}`, 'success');
                    log(`🕐 时间: ${data.timestamp}`, 'success');
                    log(`🌐 环境: ${data.envId}`, 'success');
                } else {
                    log(`❌ 代理服务器异常: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 无法连接代理服务器: ${error.message}`, 'error');
                log(`💡 请确保已启动代理服务器: npm start`, 'info');
            }
        }
        
        // 测试获取统计数据
        async function testStats() {
            log('📊 测试获取统计数据...', 'info');
            try {
                const response = await fetch(`${PROXY_URL}/api/adminAPI`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getStats' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 统计数据获取成功!`, 'success');
                    log(`📈 结果: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求错误: ${error.message}`, 'error');
            }
        }
        
        // 测试获取分类数据
        async function testCategories() {
            log('📋 测试获取分类数据...', 'info');
            try {
                const response = await fetch(`${PROXY_URL}/api/adminAPI`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getCategoryList' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 分类数据获取成功!`, 'success');
                    log(`📋 结果: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.data && Array.isArray(result.data)) {
                        log(`🎯 找到 ${result.data.length} 个分类`, 'success');
                        result.data.forEach((cat, index) => {
                            log(`  ${index + 1}. ${cat.name} (${cat.emoji_count || 0}个表情)`, 'info');
                        });
                    }
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求错误: ${error.message}`, 'error');
            }
        }
        
        // 测试获取表情数据
        async function testEmojis() {
            log('😀 测试获取表情数据...', 'info');
            try {
                const response = await fetch(`${PROXY_URL}/api/adminAPI`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getEmojis', limit: 5 })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 表情数据获取成功!`, 'success');
                    log(`😀 结果: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.data && Array.isArray(result.data)) {
                        log(`🎭 找到 ${result.data.length} 个表情`, 'success');
                    }
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成
        log('🌐 代理测试页面加载完成', 'success');
        log('💡 请先启动代理服务器，然后点击"健康检查"', 'info');
        
        // 自动检查代理服务器
        setTimeout(testHealth, 1000);
    </script>
</body>
</html>
