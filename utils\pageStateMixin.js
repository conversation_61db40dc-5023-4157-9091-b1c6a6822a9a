/**
 * 页面状态混入工具
 * 为页面提供全局状态管理功能，简化状态同步操作
 */

const { StateManager } = require('./stateManager.js')
const ResourceManager = require('./resourceManager.js')
const { DownloadManager } = require('./downloadManager.js')
const { PaginationManager } = require('./paginationManager.js')

/**
 * 页面状态混入
 * 使用方法：
 * 1. 在页面的 onLoad 中调用 PageStateMixin.onLoad.call(this)
 * 2. 在页面的 onUnload 中调用 PageStateMixin.onUnload.call(this)
 * 3. 实现 onEmojiStateChange 方法来处理状态变更
 */
const PageStateMixin = {
  /**
   * 页面加载时的初始化
   */
  onLoad() {
    // 生成页面唯一ID
    this.pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    console.log(`📄 页面状态混入初始化: ${this.pageId}`)
    
    // 注册全局状态监听器（关联页面ID）
    StateManager.addListener('global', this.handleGlobalStateChange.bind(this), this.pageId)
    
    // 如果页面有初始化表情包数据，更新状态
    if (this.data && this.data.emojiList) {
      this.updateEmojiListState()
    }
  },

  /**
   * 页面卸载时的清理
   */
  onUnload() {
    console.log(`📄 页面状态混入清理: ${this.pageId}`)

    // 自动清理该页面的所有监听器
    if (this.pageId) {
      StateManager.removePageListeners(this.pageId)
      // 清理页面资源
      ResourceManager.cleanupPageResources(this.pageId)
    }
  },

  /**
   * 创建页面定时器（自动管理）
   * @param {Function} callback - 回调函数
   * @param {number} delay - 延迟时间
   * @param {boolean} isInterval - 是否为间隔定时器
   * @returns {number} 定时器ID
   */
  createTimer(callback, delay, isInterval = false) {
    if (!this.pageId) {
      console.warn('⚠️ 页面ID不存在，无法创建管理的定时器')
      return isInterval ? setInterval(callback, delay) : setTimeout(callback, delay)
    }
    return ResourceManager.createTimer(this.pageId, callback, delay, isInterval)
  },

  /**
   * 清理页面定时器
   * @param {number} timerId - 定时器ID
   */
  clearTimer(timerId) {
    if (this.pageId) {
      ResourceManager.clearTimer(this.pageId, timerId)
    }
  },

  /**
   * 创建页面观察器（自动管理）
   * @param {string} type - 观察器类型
   * @param {Object} observer - 观察器实例
   * @returns {string} 观察器ID
   */
  createObserver(type, observer) {
    if (!this.pageId) {
      console.warn('⚠️ 页面ID不存在，无法创建管理的观察器')
      return null
    }
    return ResourceManager.createObserver(this.pageId, type, observer)
  },

  /**
   * 清理页面观察器
   * @param {string} observerId - 观察器ID
   */
  clearObserver(observerId) {
    if (this.pageId) {
      ResourceManager.clearObserver(this.pageId, observerId)
    }
  },

  /**
   * 处理全局状态变更
   * @param {Object} data - 状态变更数据
   */
  handleGlobalStateChange(data) {
    const { type, emojiId } = data
    
    switch (type) {
      case 'like':
        this.updateEmojiStateInList(emojiId, { isLiked: data.isLiked })
        break
      case 'collect':
        this.updateEmojiStateInList(emojiId, { isCollected: data.isCollected })
        break
      case 'download':
        this.updateEmojiStateInList(emojiId, { downloadTime: data.downloadTime })
        break
      case 'init':
      case 'sync':
        // 全量更新
        this.updateEmojiListState()
        break
    }
    
    // 调用页面自定义的状态变更处理方法
    if (this.onEmojiStateChange) {
      this.onEmojiStateChange(data)
    }
  },

  /**
   * 更新列表中单个表情包的状态
   * @param {string} emojiId - 表情包ID
   * @param {Object} updates - 更新的状态
   */
  updateEmojiStateInList(emojiId, updates) {
    if (!this.data || !this.data.emojiList) {
      return
    }

    const emojiList = this.data.emojiList
    const index = emojiList.findIndex(emoji => 
      (emoji._id || emoji.id) === emojiId
    )

    if (index !== -1) {
      const updateData = {}
      updateData[`emojiList[${index}]`] = {
        ...emojiList[index],
        ...updates
      }
      
      this.setData(updateData)
      console.log(`📄 页面 ${this.pageId} 更新表情包状态:`, emojiId, updates)
    }
  },

  /**
   * 更新整个表情包列表的状态
   */
  updateEmojiListState() {
    if (!this.data || !this.data.emojiList) {
      return
    }

    const updatedList = this.data.emojiList.map(emoji => {
      const emojiId = emoji._id || emoji.id
      const state = StateManager.getEmojiState(emojiId)
      
      return {
        ...emoji,
        isLiked: state.isLiked,
        isCollected: state.isCollected,
        downloadTime: state.downloadTime
      }
    })

    this.setData({ emojiList: updatedList })
    console.log(`📄 页面 ${this.pageId} 全量更新表情包状态`)
  },

  /**
   * 切换点赞状态
   * @param {string} emojiId - 表情包ID
   */
  toggleLike(emojiId) {
    StateManager.toggleLike(emojiId)
  },

  /**
   * 切换收藏状态
   * @param {string} emojiId - 表情包ID
   */
  toggleCollect(emojiId) {
    StateManager.toggleCollect(emojiId)
  },

  /**
   * 记录下载
   * @param {string} emojiId - 表情包ID
   */
  recordDownload(emojiId) {
    StateManager.recordDownload(emojiId)
  },

  /**
   * 获取表情包状态
   * @param {string} emojiId - 表情包ID
   * @returns {Object} 状态对象
   */
  getEmojiState(emojiId) {
    return StateManager.getEmojiState(emojiId)
  },

  /**
   * 批量更新表情包状态
   * @param {Array} emojiList - 表情包列表
   * @returns {Array} 更新后的列表
   */
  batchUpdateEmojiState(emojiList) {
    return emojiList.map(emoji => {
      const emojiId = emoji._id || emoji.id
      const state = StateManager.getEmojiState(emojiId)
      
      return {
        ...emoji,
        isLiked: state.isLiked,
        isCollected: state.isCollected,
        downloadTime: state.downloadTime
      }
    })
  }
}

/**
 * 创建页面状态混入装饰器
 * @param {Object} pageConfig - 页面配置对象
 * @returns {Object} 增强后的页面配置
 */
function withPageState(pageConfig) {
  const originalOnLoad = pageConfig.onLoad
  const originalOnUnload = pageConfig.onUnload

  return {
    ...pageConfig,
    
    // 增强 onLoad
    onLoad(options) {
      // 先调用混入的 onLoad
      PageStateMixin.onLoad.call(this)
      
      // 再调用原始的 onLoad
      if (originalOnLoad) {
        originalOnLoad.call(this, options)
      }
    },

    // 增强 onUnload
    onUnload() {
      // 先调用原始的 onUnload
      if (originalOnUnload) {
        originalOnUnload.call(this)
      }
      
      // 再调用混入的 onUnload
      PageStateMixin.onUnload.call(this)
    },

    // 添加混入的方法
    ...PageStateMixin
  }
}

/**
 * 简化的状态操作方法
 */
const EmojiStateHelper = {
  /**
   * 处理点赞按钮点击
   * @param {Event} e - 事件对象
   */
  onLikeClick(e) {
    const emojiId = e.currentTarget.dataset.id
    if (!emojiId) {
      console.warn('⚠️ 点赞操作缺少表情包ID')
      return
    }
    
    this.toggleLike(emojiId)
    
    // 触觉反馈
    wx.vibrateShort()
  },

  /**
   * 处理收藏按钮点击
   * @param {Event} e - 事件对象
   */
  onCollectClick(e) {
    const emojiId = e.currentTarget.dataset.id
    if (!emojiId) {
      console.warn('⚠️ 收藏操作缺少表情包ID')
      return
    }
    
    this.toggleCollect(emojiId)
    
    // 触觉反馈
    wx.vibrateShort()
  },

  /**
   * 处理下载按钮点击
   * @param {Event} e - 事件对象
   */
  async onDownloadClick(e) {
    const emojiId = e.currentTarget.dataset.id
    const emojiIndex = e.currentTarget.dataset.index

    if (!emojiId) {
      console.warn('⚠️ 下载操作缺少表情包ID')
      return
    }

    // 获取表情包数据
    let emoji = null
    if (emojiIndex !== undefined && this.data.emojiList) {
      emoji = this.data.emojiList[emojiIndex]
    } else {
      // 从列表中查找
      emoji = this.data.emojiList?.find(item =>
        (item._id || item.id) === emojiId
      )
    }

    if (!emoji) {
      console.warn('⚠️ 未找到表情包数据')
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      })
      return
    }

    // 使用下载管理器下载
    const success = await DownloadManager.downloadEmoji(emoji)

    if (success) {
      // 触觉反馈
      wx.vibrateShort()
    }
  },

  /**
   * 批量下载
   * @param {Array} emojiList - 表情包列表
   */
  async onBatchDownload(emojiList) {
    if (!emojiList || emojiList.length === 0) {
      wx.showToast({
        title: '没有可下载的内容',
        icon: 'none'
      })
      return
    }

    // 确认批量下载
    const confirmResult = await new Promise((resolve) => {
      wx.showModal({
        title: '批量下载',
        content: `确定要下载 ${emojiList.length} 个表情包吗？`,
        success: resolve,
        fail: () => resolve({ confirm: false })
      })
    })

    if (!confirmResult.confirm) {
      return
    }

    // 执行批量下载
    await DownloadManager.batchDownload(emojiList)
  }
}

/**
 * 分页功能混入
 */
const PaginationMixin = {
  /**
   * 初始化分页功能
   * @param {Object} options - 分页配置
   */
  initPagination(options = {}) {
    const defaultOptions = {
      pageSize: 20,
      dataSource: 'emojis',
      category: 'all',
      searchKeyword: '',
      onDataLoad: this.onPaginationDataLoad.bind(this),
      onError: this.onPaginationError.bind(this),
      enablePullRefresh: true,
      enableLoadMore: true
    }

    this.paginationInstance = PaginationManager.createInstance({
      ...defaultOptions,
      ...options
    })

    // 初始化加载数据
    this.paginationInstance.init()
  },

  /**
   * 分页数据加载回调
   * @param {Object} result - 加载结果
   */
  onPaginationDataLoad(result) {
    const { type, data, pagination } = result

    // 更新页面数据
    this.setData({
      emojiList: data,
      pagination: {
        hasMore: pagination.hasMore,
        loading: pagination.loading,
        refreshing: pagination.refreshing
      }
    })

    // 更新表情包状态
    if (this.updateEmojiListState) {
      this.updateEmojiListState()
    }

    console.log(`📄 分页数据更新: ${type}, 数据量: ${data.length}`)
  },

  /**
   * 分页错误处理回调
   * @param {Object} error - 错误信息
   */
  onPaginationError(error) {
    console.error('❌ 分页操作失败:', error)

    wx.showToast({
      title: error.message || '加载失败',
      icon: 'none'
    })
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    if (this.paginationInstance) {
      await this.paginationInstance.refresh()
    }

    // 停止下拉刷新动画
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  async onReachBottom() {
    if (this.paginationInstance) {
      await this.paginationInstance.loadMore()
    }
  },

  /**
   * 更新搜索关键词
   * @param {string} keyword - 搜索关键词
   */
  async updateSearchKeyword(keyword) {
    if (this.paginationInstance) {
      await this.paginationInstance.updateSearchKeyword(keyword)
    }
  },

  /**
   * 更新分类
   * @param {string} category - 分类ID
   */
  async updateCategory(category) {
    if (this.paginationInstance) {
      await this.paginationInstance.updateCategory(category)
    }
  },

  /**
   * 销毁分页实例
   */
  destroyPagination() {
    if (this.paginationInstance) {
      this.paginationInstance.destroy()
      this.paginationInstance = null
    }
  }
}

module.exports = {
  PageStateMixin,
  PaginationMixin,
  withPageState,
  EmojiStateHelper
}
