/**
 * 下载管理器
 * 处理表情包下载、进度显示、失败重试等功能
 */

const { StateManager } = require('./stateManager.js')

const DownloadManager = {
  // 下载队列
  _downloadQueue: [],
  // 正在下载的任务
  _activeDownloads: new Map(),
  // 最大并发下载数
  _maxConcurrent: 3,
  // 重试次数
  _maxRetries: 3,

  /**
   * 下载表情包
   * @param {Object} emoji - 表情包对象
   * @param {Object} options - 下载选项
   * @returns {Promise<boolean>} 下载是否成功
   */
  async downloadEmoji(emoji, options = {}) {
    const { 
      showProgress = true, 
      showToast = true,
      saveToAlbum = true 
    } = options
    
    const emojiId = emoji._id || emoji.id
    const imageUrl = emoji.imageUrl
    
    if (!imageUrl) {
      console.error('❌ 下载失败: 图片URL为空')
      if (showToast) {
        wx.showToast({
          title: '下载失败: 无效图片',
          icon: 'none'
        })
      }
      return false
    }

    console.log(`📥 开始下载表情包: ${emoji.title}`)

    try {
      // 检查是否已在下载队列中
      if (this._activeDownloads.has(emojiId)) {
        console.log('⚠️ 表情包正在下载中')
        if (showToast) {
          wx.showToast({
            title: '正在下载中...',
            icon: 'none'
          })
        }
        return false
      }

      // 添加到活跃下载列表
      this._activeDownloads.set(emojiId, {
        emoji,
        startTime: Date.now(),
        retryCount: 0
      })

      // 显示下载进度
      if (showProgress) {
        wx.showLoading({
          title: '下载中...',
          mask: true
        })
      }

      // 下载图片
      const downloadResult = await this.downloadImage(imageUrl, {
        onProgress: (progress) => {
          if (showProgress) {
            wx.showLoading({
              title: `下载中 ${Math.round(progress)}%`,
              mask: true
            })
          }
        }
      })

      if (!downloadResult.success) {
        throw new Error(downloadResult.error || '下载失败')
      }

      // 保存到相册
      if (saveToAlbum) {
        await this.saveToAlbum(downloadResult.tempFilePath, emoji.title)
      }

      // 记录下载状态
      StateManager.recordDownload(emojiId)

      // 隐藏加载提示
      if (showProgress) {
        wx.hideLoading()
      }

      // 显示成功提示
      if (showToast) {
        wx.showToast({
          title: '下载成功',
          icon: 'success'
        })
      }

      console.log(`✅ 表情包下载成功: ${emoji.title}`)
      return true

    } catch (error) {
      console.error(`❌ 表情包下载失败: ${emoji.title}`, error)
      
      // 隐藏加载提示
      if (showProgress) {
        wx.hideLoading()
      }

      // 尝试重试
      const downloadInfo = this._activeDownloads.get(emojiId)
      if (downloadInfo && downloadInfo.retryCount < this._maxRetries) {
        downloadInfo.retryCount++
        console.log(`🔄 重试下载 (${downloadInfo.retryCount}/${this._maxRetries}): ${emoji.title}`)
        
        // 延迟重试
        setTimeout(() => {
          this.downloadEmoji(emoji, { ...options, showProgress: false })
        }, 1000 * downloadInfo.retryCount)
      } else {
        // 显示失败提示
        if (showToast) {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      }

      return false
    } finally {
      // 从活跃下载列表中移除
      this._activeDownloads.delete(emojiId)
    }
  },

  /**
   * 下载图片
   * @param {string} imageUrl - 图片URL
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 下载结果
   */
  async downloadImage(imageUrl, options = {}) {
    const { onProgress } = options
    
    return new Promise((resolve, reject) => {
      const downloadTask = wx.downloadFile({
        url: imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({
              success: true,
              tempFilePath: res.tempFilePath
            })
          } else {
            resolve({
              success: false,
              error: `HTTP ${res.statusCode}`
            })
          }
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '下载失败'
          })
        }
      })

      // 监听下载进度
      if (onProgress) {
        downloadTask.onProgressUpdate((res) => {
          const progress = (res.progress || 0)
          onProgress(progress)
        })
      }
    })
  },

  /**
   * 保存到相册
   * @param {string} filePath - 文件路径
   * @param {string} title - 图片标题
   * @returns {Promise<boolean>} 是否成功
   */
  async saveToAlbum(filePath, title = '') {
    try {
      // 检查相册权限
      const authResult = await this.checkAlbumAuth()
      if (!authResult) {
        throw new Error('无相册权限')
      }

      // 保存图片到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath,
          success: resolve,
          fail: reject
        })
      })

      console.log(`📱 图片已保存到相册: ${title}`)
      return true
    } catch (error) {
      console.error('❌ 保存到相册失败:', error)
      
      // 如果是权限问题，引导用户开启权限
      if (error.errMsg && error.errMsg.includes('auth')) {
        wx.showModal({
          title: '需要相册权限',
          content: '请在设置中开启相册权限，以便保存表情包',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
      
      throw error
    }
  },

  /**
   * 检查相册权限
   * @returns {Promise<boolean>} 是否有权限
   */
  async checkAlbumAuth() {
    try {
      const setting = await new Promise((resolve, reject) => {
        wx.getSetting({
          success: resolve,
          fail: reject
        })
      })

      const albumAuth = setting.authSetting['scope.writePhotosAlbum']
      
      if (albumAuth === false) {
        // 用户之前拒绝了权限，需要引导到设置页面
        return false
      } else if (albumAuth === undefined) {
        // 首次请求权限
        try {
          await new Promise((resolve, reject) => {
            wx.authorize({
              scope: 'scope.writePhotosAlbum',
              success: resolve,
              fail: reject
            })
          })
          return true
        } catch (error) {
          return false
        }
      } else {
        // 已有权限
        return true
      }
    } catch (error) {
      console.error('检查相册权限失败:', error)
      return false
    }
  },

  /**
   * 批量下载
   * @param {Array} emojiList - 表情包列表
   * @param {Object} options - 选项
   */
  async batchDownload(emojiList, options = {}) {
    const { showProgress = true } = options
    
    if (!emojiList || emojiList.length === 0) {
      wx.showToast({
        title: '没有可下载的内容',
        icon: 'none'
      })
      return
    }

    console.log(`📥 开始批量下载: ${emojiList.length} 个表情包`)

    if (showProgress) {
      wx.showLoading({
        title: `下载中 0/${emojiList.length}`,
        mask: true
      })
    }

    let successCount = 0
    let failCount = 0

    for (let i = 0; i < emojiList.length; i++) {
      const emoji = emojiList[i]
      
      if (showProgress) {
        wx.showLoading({
          title: `下载中 ${i + 1}/${emojiList.length}`,
          mask: true
        })
      }

      try {
        const success = await this.downloadEmoji(emoji, {
          showProgress: false,
          showToast: false
        })
        
        if (success) {
          successCount++
        } else {
          failCount++
        }
      } catch (error) {
        failCount++
      }

      // 添加延迟，避免请求过于频繁
      if (i < emojiList.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    if (showProgress) {
      wx.hideLoading()
    }

    // 显示批量下载结果
    wx.showModal({
      title: '批量下载完成',
      content: `成功: ${successCount} 个\n失败: ${failCount} 个`,
      showCancel: false,
      confirmText: '知道了'
    })

    console.log(`✅ 批量下载完成: 成功 ${successCount}, 失败 ${failCount}`)
  },

  /**
   * 获取下载状态
   * @returns {Object} 下载状态信息
   */
  getDownloadStatus() {
    return {
      activeDownloads: this._activeDownloads.size,
      queueLength: this._downloadQueue.length,
      maxConcurrent: this._maxConcurrent
    }
  }
}

module.exports = {
  DownloadManager
}
