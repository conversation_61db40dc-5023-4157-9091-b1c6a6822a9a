<!--pages/category/category.wxml-->
<view class="container">
  <!-- 分类网格 -->
  <view class="category-grid">
    <view
      class="category-card"
      wx:for="{{categories}}"
      wx:key="id"
      bindtap="onCategoryTap"
      data-category="{{item}}"
      style="background: {{item.gradient}}; --index: {{index}};"
    >
      <view class="category-icon">
        <text class="icon-text">{{item.icon}}</text>
      </view>
      <view class="category-info">
        <text class="category-name">{{item.name}}</text>
        <text class="category-count">{{item.count}} 个表情</text>
      </view>
      <view class="category-arrow">
        <text class="arrow-icon">→</text>
      </view>
    </view>
  </view>
</view>