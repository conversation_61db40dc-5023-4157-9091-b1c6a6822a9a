<!--components/error-handler/error-handler.wxml-->
<view class="error-handler {{customClass}}">
  <!-- 错误图标和标题 -->
  <view class="error-header">
    <view class="error-icon">{{errorIcons[errorType] || errorIcons.default}}</view>
    <view class="error-title">{{errorTitles[errorType] || errorTitles.default}}</view>
  </view>

  <!-- 错误消息 -->
  <view class="error-message" wx:if="{{errorMessage}}">
    {{errorMessage}}
  </view>

  <!-- 错误建议 -->
  <view class="error-suggestions">
    <view class="suggestion-title">建议解决方案：</view>
    <view class="suggestion-list">
      <view 
        class="suggestion-item" 
        wx:for="{{errorSuggestions[errorType] || errorSuggestions.default}}" 
        wx:key="index"
      >
        • {{item}}
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="error-actions">
    <!-- 重试按钮 -->
    <button 
      wx:if="{{showRetry}}" 
      class="retry-btn" 
      type="primary" 
      size="default"
      bindtap="onRetry"
    >
      重试 {{retryCount > 0 ? '(' + retryCount + ')' : ''}}
    </button>

    <!-- 详细信息按钮 -->
    <button 
      wx:if="{{showDetails}}" 
      class="details-btn" 
      type="default" 
      size="mini"
      bindtap="toggleDetails"
    >
      {{showDetailInfo ? '隐藏' : '显示'}}详细信息
    </button>

    <!-- 联系客服按钮 -->
    <button 
      class="support-btn" 
      type="default" 
      size="mini"
      bindtap="contactSupport"
    >
      联系客服
    </button>
  </view>

  <!-- 详细信息面板 -->
  <view class="error-details" wx:if="{{showDetailInfo && showDetails}}">
    <view class="details-title">错误详细信息</view>
    <view class="details-content">
      <view class="detail-item">
        <text class="detail-label">错误类型：</text>
        <text class="detail-value">{{errorType}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">错误消息：</text>
        <text class="detail-value">{{errorMessage}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">重试次数：</text>
        <text class="detail-value">{{retryCount}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">时间：</text>
        <text class="detail-value">{{currentTime}}</text>
      </view>
    </view>
    
    <!-- 复制错误信息按钮 -->
    <button 
      class="copy-btn" 
      type="default" 
      size="mini"
      bindtap="copyErrorInfo"
    >
      复制错误信息
    </button>
  </view>

  <!-- 关闭按钮 -->
  <view class="close-btn" bindtap="onClose">
    <text class="close-icon">×</text>
  </view>
</view>
