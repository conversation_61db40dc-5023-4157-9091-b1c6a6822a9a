/**
 * 云开发诊断工具
 * 用于检测和验证云开发环境的各项功能
 */

class CloudBaseDiagnostic {
    constructor() {
        this.envId = 'cloud1-5g6pvnpl88dc0142';
        this.app = null;
        this.results = [];
    }

    // 添加结果
    addResult(test, status, message, details = null) {
        const result = {
            test,
            status, // 'success', 'error', 'warning'
            message,
            details,
            timestamp: new Date().toISOString()
        };
        this.results.push(result);
        this.logResult(result);
    }

    // 输出结果
    logResult(result) {
        const icon = result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️';
        console.log(`${icon} ${result.test}: ${result.message}`);
        if (result.details) {
            console.log('   详情:', result.details);
        }
    }

    // 运行所有诊断
    async runAllDiagnostics() {
        console.log('🚀 开始运行全部诊断测试...');
        
        await this.testBasicEnvironment();
        await this.testSDKLoading();
        await this.testInitialization();
        await this.testAuthentication();
        await this.testDatabase();
        await this.testCloudFunctions();
        await this.testDataOperations();
        
        this.generateReport();
    }

    // 1. 基础环境检测
    async testBasicEnvironment() {
        console.log('🔍 开始基础环境检测...');
        
        // Promise支持
        try {
            await new Promise(resolve => setTimeout(resolve, 1));
            this.addResult('Promise支持', 'success', 'Promise支持正常');
        } catch (error) {
            this.addResult('Promise支持', 'error', 'Promise不支持', error.message);
        }

        // fetch支持
        if (typeof fetch !== 'undefined') {
            this.addResult('fetch支持', 'success', 'fetch支持正常');
        } else {
            this.addResult('fetch支持', 'error', 'fetch不支持');
        }

        // 网络连接测试
        try {
            console.log('🌐 测试网络连接...');
            const response = await fetch('https://www.baidu.com', { 
                method: 'HEAD',
                mode: 'no-cors'
            });
            this.addResult('网络连接', 'success', '网络连接正常');
        } catch (error) {
            this.addResult('网络连接', 'error', '网络连接失败', error.message);
        }

        console.log('✅ 基础环境检测通过');
    }

    // 2. SDK加载检测
    async testSDKLoading() {
        console.log('📦 开始SDK加载测试...');

        // 检查本地SDK
        const hasCloudbase = typeof window.cloudbase !== 'undefined';
        const hasTcb = typeof window.tcb !== 'undefined';

        if (hasCloudbase) {
            this.addResult('本地SDK加载', 'success', 'cloudbase SDK已加载');
        } else if (hasTcb) {
            this.addResult('本地SDK加载', 'success', 'tcb SDK已加载');
        } else {
            this.addResult('本地SDK加载', 'error', '本地SDK未加载');
            return;
        }

        // 检查SDK功能
        const sdk = window.cloudbase || window.tcb;
        if (typeof sdk.init === 'function') {
            this.addResult('SDK功能检查', 'success', 'SDK init方法可用');
        } else {
            this.addResult('SDK功能检查', 'error', 'SDK init方法不可用');
        }
    }

    // 3. 初始化测试
    async testInitialization() {
        console.log('🚀 开始初始化测试...');

        try {
            const sdk = window.cloudbase || window.tcb;
            if (!sdk) {
                throw new Error('SDK未加载');
            }

            this.app = sdk.init({ env: this.envId });
            this.addResult('SDK初始化', 'success', 'SDK初始化成功');

            // 检查应用对象
            if (this.app && typeof this.app.auth === 'function') {
                this.addResult('应用对象检查', 'success', '应用对象创建成功');
            } else {
                this.addResult('应用对象检查', 'error', '应用对象创建失败');
            }

        } catch (error) {
            this.addResult('SDK初始化', 'error', 'SDK初始化失败', error.message);
        }
    }

    // 4. 身份验证测试
    async testAuthentication() {
        console.log('🔐 开始身份验证测试...');

        if (!this.app) {
            this.addResult('身份验证', 'error', '应用未初始化');
            return;
        }

        try {
            const auth = this.app.auth();
            const user = await auth.signInAnonymously();
            
            this.addResult('匿名登录', 'success', `匿名登录成功，用户ID: ${user.uid}`);

            // 检查用户状态
            if (auth.currentUser && auth.currentUser.uid) {
                this.addResult('用户状态检查', 'success', '用户状态正常');
            } else {
                this.addResult('用户状态检查', 'warning', '用户状态异常');
            }

        } catch (error) {
            this.addResult('身份验证', 'error', '身份验证失败', error.message);
        }
    }

    // 5. 数据库测试
    async testDatabase() {
        console.log('📊 开始数据库测试...');

        if (!this.app) {
            this.addResult('数据库连接', 'error', '应用未初始化');
            return;
        }

        try {
            const db = this.app.database();
            this.addResult('数据库连接', 'success', '数据库连接成功');

            // 测试集合操作
            const collection = db.collection('categories');
            if (collection && typeof collection.get === 'function') {
                this.addResult('集合操作', 'success', '集合操作接口正常');
            } else {
                this.addResult('集合操作', 'error', '集合操作接口异常');
            }

        } catch (error) {
            this.addResult('数据库连接', 'error', '数据库连接失败', error.message);
        }
    }

    // 6. 云函数测试
    async testCloudFunctions() {
        console.log('☁️ 开始云函数测试...');

        if (!this.app) {
            this.addResult('云函数调用', 'error', '应用未初始化');
            return;
        }

        try {
            const result = await this.app.callFunction({
                name: 'adminAPI',
                data: { action: 'getStats' }
            });

            if (result && result.result) {
                this.addResult('云函数调用', 'success', '云函数调用成功', result.result);
            } else {
                this.addResult('云函数调用', 'warning', '云函数调用返回异常', result);
            }

        } catch (error) {
            this.addResult('云函数调用', 'error', '云函数调用失败', error.message);
        }
    }

    // 7. 数据操作测试
    async testDataOperations() {
        console.log('📝 开始数据操作测试...');

        if (!this.app) {
            this.addResult('数据操作', 'error', '应用未初始化');
            return;
        }

        try {
            const db = this.app.database();

            // 测试数据写入
            const addResult = await db.collection('test').add({
                data: {
                    name: '诊断测试数据',
                    createTime: new Date(),
                    type: 'diagnostic'
                }
            });

            if (addResult && addResult._id) {
                this.addResult('数据写入', 'success', `数据写入成功，ID: ${addResult._id}`);
            } else {
                this.addResult('数据写入', 'warning', '数据写入返回异常', addResult);
            }

            // 测试数据查询
            const queryResult = await db.collection('categories').get();
            if (queryResult && queryResult.data) {
                this.addResult('数据查询', 'success', `数据查询成功，返回 ${queryResult.data.length} 条记录`);
            } else {
                this.addResult('数据查询', 'warning', '数据查询返回异常', queryResult);
            }

            // 测试数据统计
            const countResult = await db.collection('categories').count();
            if (countResult && typeof countResult.total === 'number') {
                this.addResult('数据统计', 'success', `数据统计成功，总数: ${countResult.total}`);
            } else {
                this.addResult('数据统计', 'warning', '数据统计返回异常', countResult);
            }

        } catch (error) {
            this.addResult('数据操作', 'error', '数据操作失败', error.message);
        }
    }

    // 生成诊断报告
    generateReport() {
        console.log('\n📋 诊断报告生成中...');
        
        const total = this.results.length;
        const success = this.results.filter(r => r.status === 'success').length;
        const errors = this.results.filter(r => r.status === 'error').length;
        const warnings = this.results.filter(r => r.status === 'warning').length;

        console.log('\n🎯 诊断结果汇总:');
        console.log(`📊 总测试数: ${total}`);
        console.log(`✅ 成功: ${success}`);
        console.log(`❌ 失败: ${errors}`);
        console.log(`⚠️ 警告: ${warnings}`);
        console.log(`📈 成功率: ${Math.round(success / total * 100)}%`);

        if (errors > 0) {
            console.log('\n❌ 失败的测试:');
            this.results.filter(r => r.status === 'error').forEach(r => {
                console.log(`  - ${r.test}: ${r.message}`);
            });
        }

        if (warnings > 0) {
            console.log('\n⚠️ 警告的测试:');
            this.results.filter(r => r.status === 'warning').forEach(r => {
                console.log(`  - ${r.test}: ${r.message}`);
            });
        }

        // 生成建议
        this.generateRecommendations();
    }

    // 生成建议
    generateRecommendations() {
        console.log('\n💡 建议和解决方案:');

        const hasSDKError = this.results.some(r => r.test.includes('SDK') && r.status === 'error');
        const hasNetworkError = this.results.some(r => r.test.includes('网络') && r.status === 'error');
        const hasAuthError = this.results.some(r => r.test.includes('身份验证') && r.status === 'error');

        if (hasSDKError) {
            console.log('🔧 SDK问题解决方案:');
            console.log('  1. 检查 cloudbase-js-sdk.min.js 文件是否正确加载');
            console.log('  2. 确认文件路径是否正确');
            console.log('  3. 检查浏览器控制台是否有JavaScript错误');
        }

        if (hasNetworkError) {
            console.log('🌐 网络问题解决方案:');
            console.log('  1. 检查网络连接');
            console.log('  2. 检查防火墙设置');
            console.log('  3. 尝试使用其他网络环境');
        }

        if (hasAuthError) {
            console.log('🔐 身份验证问题解决方案:');
            console.log('  1. 检查云开发环境配置');
            console.log('  2. 确认环境ID是否正确');
            console.log('  3. 检查安全规则设置');
        }

        console.log('\n🎉 诊断完成！');
    }
}

// 导出诊断工具
window.CloudBaseDiagnostic = CloudBaseDiagnostic;
