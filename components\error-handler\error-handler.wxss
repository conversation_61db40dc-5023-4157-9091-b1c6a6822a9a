/* components/error-handler/error-handler.wxss */

.error-handler {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #f0f0f0;
}

/* 错误头部 */
.error-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  line-height: 1;
}

.error-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

/* 错误消息 */
.error-message {
  background: #fef7f7;
  border: 2rpx solid #fecaca;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #dc2626;
  line-height: 1.5;
}

/* 错误建议 */
.error-suggestions {
  margin-bottom: 40rpx;
}

.suggestion-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.suggestion-list {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
}

.suggestion-item {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

/* 操作按钮 */
.error-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.retry-btn {
  background: #3b82f6 !important;
  color: #fff !important;
  border-radius: 12rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  padding: 24rpx !important;
  border: none !important;
}

.retry-btn::after {
  border: none !important;
}

.details-btn,
.support-btn,
.copy-btn {
  background: #f3f4f6 !important;
  color: #374151 !important;
  border-radius: 8rpx !important;
  font-size: 26rpx !important;
  padding: 16rpx !important;
  border: 2rpx solid #e5e7eb !important;
}

.details-btn::after,
.support-btn::after,
.copy-btn::after {
  border: none !important;
}

/* 详细信息面板 */
.error-details {
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.details-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.details-content {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  color: #374151;
  flex: 1;
  word-break: break-all;
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
  cursor: pointer;
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .error-handler {
    margin: 16rpx;
    padding: 32rpx 24rpx;
  }
  
  .error-icon {
    font-size: 64rpx;
  }
  
  .error-title {
    font-size: 32rpx;
  }
  
  .error-message {
    font-size: 26rpx;
    padding: 20rpx;
  }
  
  .suggestion-title {
    font-size: 28rpx;
  }
  
  .suggestion-item {
    font-size: 26rpx;
  }
}

/* 动画效果 */
.error-handler {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮点击效果 */
.retry-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.details-btn:active,
.support-btn:active,
.copy-btn:active {
  background: #e5e7eb !important;
}

.close-btn:active {
  background: #e5e7eb;
  transform: scale(0.95);
}
