// 数据迁移云函数 - 将base64图片上传到云存储
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action = 'migrate', batchSize = 10 } = event
  
  console.log('🔄 开始base64图片迁移任务...')
  
  try {
    switch (action) {
      case 'migrate':
        return await migrateBase64Images(batchSize)
      case 'check':
        return await checkBase64Images()
      case 'cleanup':
        return await cleanupFailedUploads()
      default:
        return { success: false, message: '未知操作' }
    }
  } catch (error) {
    console.error('❌ 迁移任务失败:', error)
    return { success: false, message: error.message }
  }
}

// 检查需要迁移的base64图片数量
async function checkBase64Images() {
  try {
    console.log('🔍 检查base64图片数量...')
    
    // 查询所有包含base64图片的记录
    const result = await db.collection('emojis')
      .where({
        imageUrl: db.RegExp({
          regexp: '^data:image/',
          options: 'i'
        })
      })
      .count()
    
    console.log(`📊 发现 ${result.total} 个base64图片需要迁移`)
    
    return {
      success: true,
      data: {
        total: result.total,
        needMigration: result.total
      }
    }
  } catch (error) {
    console.error('❌ 检查失败:', error)
    throw error
  }
}

// 迁移base64图片到云存储
async function migrateBase64Images(batchSize = 10) {
  try {
    console.log(`🔄 开始批量迁移，批次大小: ${batchSize}`)
    
    // 分批查询需要迁移的记录
    const result = await db.collection('emojis')
      .where({
        imageUrl: db.RegExp({
          regexp: '^data:image/',
          options: 'i'
        })
      })
      .limit(batchSize)
      .get()
    
    const records = result.data
    console.log(`📦 本批次处理 ${records.length} 条记录`)
    
    if (records.length === 0) {
      return {
        success: true,
        message: '所有base64图片已迁移完成',
        data: {
          processed: 0,
          success: 0,
          failed: 0
        }
      }
    }
    
    let successCount = 0
    let failedCount = 0
    const results = []
    
    // 逐个处理记录
    for (const record of records) {
      try {
        console.log(`📸 处理记录: ${record._id}`)
        
        // 上传base64图片到云存储
        const uploadResult = await uploadBase64ToCloud(
          record.imageUrl, 
          `emojis/migrated_${record._id}_${Date.now()}.png`
        )
        
        if (uploadResult.success) {
          // 更新数据库记录
          await db.collection('emojis').doc(record._id).update({
            data: {
              imageUrl: uploadResult.fileID,
              migratedAt: new Date(),
              originalImageUrl: record.imageUrl.substring(0, 100) + '...' // 保留前100字符作为备份
            }
          })
          
          successCount++
          results.push({
            id: record._id,
            status: 'success',
            fileID: uploadResult.fileID
          })
          
          console.log(`✅ 记录 ${record._id} 迁移成功: ${uploadResult.fileID}`)
        } else {
          failedCount++
          results.push({
            id: record._id,
            status: 'failed',
            error: uploadResult.error
          })
          
          console.error(`❌ 记录 ${record._id} 迁移失败: ${uploadResult.error}`)
        }
        
        // 添加延迟，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        failedCount++
        results.push({
          id: record._id,
          status: 'error',
          error: error.message
        })
        
        console.error(`❌ 处理记录 ${record._id} 时发生异常:`, error)
      }
    }
    
    console.log(`📊 批次处理完成: 成功 ${successCount}, 失败 ${failedCount}`)
    
    return {
      success: true,
      message: `批次迁移完成`,
      data: {
        processed: records.length,
        success: successCount,
        failed: failedCount,
        results: results
      }
    }
    
  } catch (error) {
    console.error('❌ 批量迁移失败:', error)
    throw error
  }
}

// 将base64图片上传到云存储
async function uploadBase64ToCloud(base64Data, cloudPath) {
  try {
    // 解析base64数据
    const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/)
    if (!matches) {
      throw new Error('无效的base64图片格式')
    }
    
    const imageType = matches[1]
    const base64Content = matches[2]
    
    // 将base64转换为Buffer
    const buffer = Buffer.from(base64Content, 'base64')
    
    console.log(`📸 上传图片: ${cloudPath}, 大小: ${buffer.length} bytes, 类型: ${imageType}`)
    
    // 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: buffer
    })
    
    console.log(`✅ 图片上传成功: ${uploadResult.fileID}`)
    
    return {
      success: true,
      fileID: uploadResult.fileID,
      size: buffer.length
    }
    
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 清理失败的上传记录
async function cleanupFailedUploads() {
  try {
    console.log('🧹 清理失败的上传记录...')
    
    // 这里可以添加清理逻辑，比如删除上传失败的临时文件等
    
    return {
      success: true,
      message: '清理完成'
    }
  } catch (error) {
    console.error('❌ 清理失败:', error)
    throw error
  }
}
