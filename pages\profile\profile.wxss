/* pages/profile/profile.wxss */
.container {
  padding: 0 20rpx 120rpx 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息卡片 - 酷炫蓝紫渐变 + 动态粒子特效 */
.user-card.card {
  display: flex;
  align-items: center;
  padding: 120rpx 40rpx;
  margin: 20rpx 0 30rpx 0;
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%),
    radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(102, 126, 234, 0.3) 0%, transparent 50%);
  color: #fff;
  border-radius: 32rpx;
  box-shadow:
    0 20rpx 60rpx rgba(102, 126, 234, 0.3),
    0 8rpx 24rpx rgba(102, 126, 234, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -2rpx 0 rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 动态粒子特效系统 */
.user-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.3) 2rpx, transparent 2rpx),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 1rpx, transparent 1rpx),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.25) 1.5rpx, transparent 1.5rpx),
    radial-gradient(circle at 60% 90%, rgba(255, 255, 255, 0.15) 1rpx, transparent 1rpx),
    radial-gradient(circle at 90% 60%, rgba(255, 255, 255, 0.2) 2rpx, transparent 2rpx),
    radial-gradient(circle at 10% 30%, rgba(255, 255, 255, 0.1) 1rpx, transparent 1rpx);
  background-size: 200rpx 200rpx, 150rpx 150rpx, 180rpx 180rpx, 120rpx 120rpx, 160rpx 160rpx, 100rpx 100rpx;
  animation: particleFloat 15s linear infinite;
  opacity: 0.8;
}

.user-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 1rpx, transparent 1rpx),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.25) 1.5rpx, transparent 1.5rpx),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.1) 1rpx, transparent 1rpx),
    radial-gradient(circle at 85% 40%, rgba(255, 255, 255, 0.2) 2rpx, transparent 2rpx),
    radial-gradient(circle at 15% 70%, rgba(255, 255, 255, 0.18) 1rpx, transparent 1rpx);
  background-size: 180rpx 180rpx, 140rpx 140rpx, 110rpx 110rpx, 170rpx 170rpx, 130rpx 130rpx;
  animation: particleFloat 20s linear infinite reverse;
  opacity: 0.6;
}

/* 粒子动画效果 */
@keyframes particleFloat {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
    opacity: 0.8;
  }
  25% {
    transform: translateX(20rpx) translateY(-15rpx) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateX(-10rpx) translateY(-30rpx) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    transform: translateX(-25rpx) translateY(-10rpx) rotate(270deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) translateY(0) rotate(360deg);
    opacity: 0.8;
  }
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(90deg);
  }
}

.card-content {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 2;
}

/* 编辑按钮样式 */
.edit-profile-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  z-index: 3;
}

.edit-profile-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.edit-icon {
  font-size: 28rpx;
}

/* 响应式设计 - 只在非常小的屏幕上调整间距 */
@media (max-width: 320px) {
  .user-card {
    padding: 40rpx 30rpx;
  }

  .user-avatar {
    margin-right: 20rpx;
  }

  .user-name {
    font-size: 32rpx;
  }

  .user-desc {
    font-size: 24rpx;
  }

  .stat-number {
    font-size: 28rpx;
  }

  .stat-label {
    font-size: 20rpx;
  }
}

.user-avatar {
  width: 130rpx;
  height: 130rpx;
  margin-right: 32rpx;
  position: relative;
  z-index: 2;
  flex-shrink: 0; /* 防止头像被压缩 */
}

/* 头像按钮样式 */
.avatar-btn {
  width: 130rpx;
  height: 130rpx;
  background: transparent;
  border: none;
  border-radius: 50%;
  padding: 0;
  margin: 0;
  display: block;
  position: relative;
  overflow: hidden; /* 确保图片不会溢出 */
}

.avatar-btn::after {
  border: none;
}

/* 头像按钮点击效果 */
.avatar-btn:active .avatar-image {
  transform: scale(0.95);
}

/* 头像图片样式 - 关键：依赖mode="aspectFill"而不是object-fit */
.avatar-image {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 8rpx 24rpx rgba(33, 150, 243, 0.2),
    0 4rpx 12rpx rgba(33, 150, 243, 0.1);
  transition: transform 0.3s ease;
  display: block;
}



.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.user-name {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  color: #ffffff;
  line-height: 1.2;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.user-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
}

.user-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 14rpx;
  margin-left: 20rpx;
  position: relative;
  z-index: 2;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18rpx 22rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  min-width: 85rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15rpx);
  box-shadow:
    0 6rpx 20rpx rgba(33, 150, 243, 0.15),
    0 2rpx 8rpx rgba(33, 150, 243, 0.08);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(1rpx) scale(0.98);
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 4rpx 16rpx rgba(33, 150, 243, 0.12),
    0 2rpx 6rpx rgba(33, 150, 243, 0.06);
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  font-size: 20rpx;
  color: #666;
  font-weight: 400;
}

/* 登录提示卡片 */
.login-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  margin: 20rpx 0 30rpx 0;
  background:
    linear-gradient(135deg, #FF8C00 0%, #FFA500 25%, #FFB84D 50%, #FFCC80 75%, #FFE0B3 100%),
    radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(255, 140, 0, 0.3) 0%, transparent 50%);
  text-align: center;
  position: relative;
  overflow: hidden;
  border-radius: 32rpx;
  box-shadow:
    0 20rpx 60rpx rgba(255, 140, 0, 0.3),
    0 8rpx 24rpx rgba(255, 140, 0, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.4),
    inset 0 -2rpx 0 rgba(255, 140, 0, 0.2);
}

.login-prompt::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    conic-gradient(from 0deg at 50% 50%,
      rgba(255, 255, 255, 0.2) 0deg,
      rgba(255, 255, 255, 0.1) 60deg,
      rgba(255, 255, 255, 0.3) 120deg,
      rgba(255, 255, 255, 0.05) 180deg,
      rgba(255, 255, 255, 0.25) 240deg,
      rgba(255, 255, 255, 0.1) 300deg,
      rgba(255, 255, 255, 0.2) 360deg);
  animation: shimmer 4s ease-in-out infinite;
}

.login-prompt::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shine 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes shine {
  0%, 100% {
    transform: translateX(-100%) skewX(-15deg);
    opacity: 0;
  }
  50% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 1;
  }
}

.login-prompt-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.login-icon {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 32rpx;
  opacity: 0.9;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.15));
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8rpx); }
}

.login-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 60rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
  line-height: 1.2;
}



.login-prompt-btn {
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 248, 240, 0.95) 100%),
    linear-gradient(45deg, rgba(255, 140, 0, 0.1) 0%, transparent 50%, rgba(255, 140, 0, 0.1) 100%);
  color: #FF8C00;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 32rpx 64rpx;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow:
    0 12rpx 32rpx rgba(255, 140, 0, 0.2),
    0 4rpx 16rpx rgba(0, 0, 0, 0.1),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(255, 140, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20rpx) saturate(180%);
  letter-spacing: 1rpx;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-prompt-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.login-prompt-btn:active::before {
  left: 100%;
}

.login-prompt-btn::after {
  border: none;
}

.login-prompt-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow:
    0 6rpx 20rpx rgba(255, 140, 0, 0.3),
    0 2rpx 8rpx rgba(0, 0, 0, 0.15),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -1rpx 0 rgba(255, 140, 0, 0.2);
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 240, 0.9) 100%),
    linear-gradient(45deg, rgba(255, 140, 0, 0.15) 0%, transparent 50%, rgba(255, 140, 0, 0.15) 100%);
  color: #E67E00;
}

/* 功能菜单 */
.menu-section {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f9fa;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.browse-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.like-icon {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.collect-icon {
  background: linear-gradient(135deg, #ffa726, #ff9800);
}

.download-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.settings-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.icon-text {
  font-size: 36rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #666;
}

.menu-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}

/* 最近浏览 - 暂时隐藏，后续产品迭代再考虑 */
/*
.recent-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #8B5CF6;
}

.recent-scroll {
  white-space: nowrap;
}

.recent-list {
  display: inline-flex;
  gap: 20rpx;
}

.recent-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 160rpx;
}

.recent-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
}

.recent-title {
  font-size: 22rpx;
  color: #333;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
*/



/* 关于我们 - 简洁样式 */
.about-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  text-align: center;
  margin: 20rpx 0 40rpx 0;
}

.about-text {
  font-size: 28rpx;
  color: #4facfe;
  margin-bottom: 12rpx;
  font-weight: 600;
}

.about-desc {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

/* ==================== 用户信息编辑弹窗样式 ==================== */

.profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
}

.modal-content {
  position: relative;
  width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #f5f5f5;
  transform: scale(0.9);
}

.modal-body {
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 头像选择器 */
.avatar-selector {
  display: flex;
  justify-content: center;
}

.avatar-choose-btn {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-choose-btn::after {
  border: none;
}

.preview-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid #f0f0f0;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar-choose-btn:active .avatar-overlay {
  opacity: 1;
}

.overlay-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

/* 昵称输入 */
.nickname-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  transition: all 0.3s ease;
}

.nickname-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.nickname-tips {
  margin-top: 12rpx;
}

.tips-text {
  font-size: 24rpx;
  font-weight: 500;
}

.tips-text.success {
  color: #10b981;
}

.tips-text.error {
  color: #ef4444;
}

/* 弹窗底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.save-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e0e0e0;
  transform: scale(0.98);
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.save-btn[disabled] {
  background: #e0e0e0;
  color: #999;
  transform: none;
  box-shadow: none;
}