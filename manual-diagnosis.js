/**
 * 手动诊断脚本 - 数据同步问题
 * 在浏览器控制台中运行，逐步诊断问题
 */

console.log('🔍 开始数据同步问题手动诊断...\n');

// 诊断配置
const DIAGNOSIS_CONFIG = {
    env: 'cloud1-5g6pvnpl88dc0142',
    testPrefix: 'DIAGNOSIS_' + Date.now()
};

// 诊断结果存储
window.diagnosisResults = {
    steps: [],
    issues: [],
    recommendations: []
};

// 步骤1: 检查云开发环境
async function step1_checkCloudEnvironment() {
    console.log('📋 步骤1: 检查云开发环境...');
    
    try {
        // 检查是否有云开发SDK
        if (typeof cloudbase === 'undefined' && typeof tcb === 'undefined') {
            throw new Error('云开发SDK未加载');
        }
        
        // 初始化云开发
        const app = cloudbase ? cloudbase.init({ env: DIAGNOSIS_CONFIG.env }) : tcb.init({ env: DIAGNOSIS_CONFIG.env });
        await app.auth().signInAnonymously();
        const db = app.database();
        
        // 测试数据库连接
        const testResult = await db.collection('categories').limit(1).get();
        
        console.log('✅ 云开发环境检查通过');
        console.log('   - 环境ID:', DIAGNOSIS_CONFIG.env);
        console.log('   - 数据库连接: 正常');
        
        window.diagnosisResults.steps.push({
            step: 1,
            name: '云开发环境检查',
            status: 'success',
            details: { env: DIAGNOSIS_CONFIG.env, dbConnection: true }
        });
        
        return { success: true, app, db };
        
    } catch (error) {
        console.error('❌ 云开发环境检查失败:', error.message);
        window.diagnosisResults.steps.push({
            step: 1,
            name: '云开发环境检查',
            status: 'failed',
            error: error.message
        });
        window.diagnosisResults.issues.push('云开发环境连接失败');
        return { success: false, error };
    }
}

// 步骤2: 检查数据库实际数据
async function step2_checkDatabaseData(db) {
    console.log('📋 步骤2: 检查数据库实际数据...');
    
    try {
        // 获取所有集合的数据
        const [categoriesResult, emojisResult, bannersResult] = await Promise.all([
            db.collection('categories').get(),
            db.collection('emojis').get(),
            db.collection('banners').get()
        ]);
        
        const categories = categoriesResult.data;
        const emojis = emojisResult.data;
        const banners = bannersResult.data;
        
        console.log('✅ 数据库数据检查完成');
        console.log('   - 分类数量:', categories.length);
        console.log('   - 表情包数量:', emojis.length);
        console.log('   - 轮播图数量:', banners.length);
        
        // 分析数据格式
        if (categories.length > 0) {
            console.log('   - 分类示例:', categories[0]);
        }
        if (emojis.length > 0) {
            console.log('   - 表情包示例:', emojis[0]);
            
            // 检查字段格式
            const hasCategory = emojis.some(emoji => emoji.category);
            const hasCategoryId = emojis.some(emoji => emoji.categoryId);
            
            console.log('   - 表情包字段分析:');
            console.log('     * 使用category字段:', hasCategory ? '是' : '否');
            console.log('     * 使用categoryId字段:', hasCategoryId ? '是' : '否');
        }
        
        window.diagnosisResults.steps.push({
            step: 2,
            name: '数据库数据检查',
            status: 'success',
            details: {
                categoriesCount: categories.length,
                emojisCount: emojis.length,
                bannersCount: banners.length,
                categories: categories.slice(0, 2),
                emojis: emojis.slice(0, 2)
            }
        });
        
        return { success: true, categories, emojis, banners };
        
    } catch (error) {
        console.error('❌ 数据库数据检查失败:', error.message);
        window.diagnosisResults.steps.push({
            step: 2,
            name: '数据库数据检查',
            status: 'failed',
            error: error.message
        });
        window.diagnosisResults.issues.push('数据库数据访问失败');
        return { success: false, error };
    }
}

// 步骤3: 测试云函数dataAPI
async function step3_testDataAPI(app) {
    console.log('📋 步骤3: 测试云函数dataAPI...');
    
    try {
        // 测试获取分类
        const categoriesResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getCategories' }
        });
        
        // 测试获取表情包
        const emojisResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 10 } }
        });
        
        // 测试获取轮播图
        const bannersResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getBanners' }
        });
        
        console.log('✅ 云函数dataAPI测试完成');
        console.log('   - 分类API结果:', categoriesResult.result);
        console.log('   - 表情包API结果:', emojisResult.result);
        console.log('   - 轮播图API结果:', bannersResult.result);
        
        // 分析API返回的数据
        const apiCategories = categoriesResult.result?.data || [];
        const apiEmojis = emojisResult.result?.data || [];
        const apiBanners = bannersResult.result?.data || [];
        
        console.log('   - API返回数据统计:');
        console.log('     * 分类数量:', apiCategories.length);
        console.log('     * 表情包数量:', apiEmojis.length);
        console.log('     * 轮播图数量:', apiBanners.length);
        
        window.diagnosisResults.steps.push({
            step: 3,
            name: '云函数dataAPI测试',
            status: 'success',
            details: {
                categoriesAPI: categoriesResult.result,
                emojisAPI: emojisResult.result,
                bannersAPI: bannersResult.result
            }
        });
        
        return { 
            success: true, 
            apiCategories, 
            apiEmojis, 
            apiBanners,
            rawResults: { categoriesResult, emojisResult, bannersResult }
        };
        
    } catch (error) {
        console.error('❌ 云函数dataAPI测试失败:', error.message);
        window.diagnosisResults.steps.push({
            step: 3,
            name: '云函数dataAPI测试',
            status: 'failed',
            error: error.message
        });
        window.diagnosisResults.issues.push('云函数dataAPI调用失败');
        return { success: false, error };
    }
}

// 步骤4: 创建测试数据
async function step4_createTestData(db) {
    console.log('📋 步骤4: 创建测试数据...');
    
    try {
        const testCategoryName = DIAGNOSIS_CONFIG.testPrefix + '_分类';
        const testEmojiTitle = DIAGNOSIS_CONFIG.testPrefix + '_表情包';
        
        // 创建测试分类
        const categoryData = {
            name: testCategoryName,
            icon: '🔧',
            sort: 999,
            status: 'active',
            description: '诊断测试分类',
            createTime: new Date(),
            updateTime: new Date()
        };
        
        const categoryResult = await db.collection('categories').add({
            data: categoryData
        });
        
        // 创建测试表情包（使用管理后台格式）
        const emojiData = {
            title: testEmojiTitle,
            category: testCategoryName, // 注意：使用category字段存储分类名称
            description: '诊断测试表情包',
            imageUrl: 'https://via.placeholder.com/150x150?text=Test',
            status: 'published',
            likes: 0,
            downloads: 0,
            collections: 0,
            createTime: new Date(),
            updateTime: new Date()
        };
        
        const emojiResult = await db.collection('emojis').add({
            data: emojiData
        });
        
        console.log('✅ 测试数据创建成功');
        console.log('   - 分类ID:', categoryResult._id);
        console.log('   - 表情包ID:', emojiResult._id);
        console.log('   - 分类名称:', testCategoryName);
        console.log('   - 表情包标题:', testEmojiTitle);
        
        window.diagnosisResults.steps.push({
            step: 4,
            name: '创建测试数据',
            status: 'success',
            details: {
                categoryId: categoryResult._id,
                emojiId: emojiResult._id,
                testCategoryName,
                testEmojiTitle,
                categoryData,
                emojiData
            }
        });
        
        return { 
            success: true, 
            categoryId: categoryResult._id,
            emojiId: emojiResult._id,
            testCategoryName,
            testEmojiTitle
        };
        
    } catch (error) {
        console.error('❌ 测试数据创建失败:', error.message);
        window.diagnosisResults.steps.push({
            step: 4,
            name: '创建测试数据',
            status: 'failed',
            error: error.message
        });
        window.diagnosisResults.issues.push('测试数据创建失败');
        return { success: false, error };
    }
}

// 步骤5: 验证数据读取
async function step5_verifyDataRead(app, testCategoryName) {
    console.log('📋 步骤5: 验证数据读取...');
    
    try {
        // 测试云函数是否能读取到刚创建的数据
        const categoriesResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getCategories' }
        });
        
        const emojisAllResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 100 } }
        });
        
        const emojisByCategoryResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getEmojis', data: { category: testCategoryName, page: 1, limit: 10 } }
        });
        
        // 检查是否能找到测试数据
        const foundCategory = categoriesResult.result?.data?.find(cat => cat.name === testCategoryName);
        const allEmojis = emojisAllResult.result?.data || [];
        const categoryEmojis = emojisByCategoryResult.result?.data || [];
        
        console.log('✅ 数据读取验证完成');
        console.log('   - 是否找到测试分类:', foundCategory ? '是' : '否');
        console.log('   - 所有表情包数量:', allEmojis.length);
        console.log('   - 按分类查询的表情包数量:', categoryEmojis.length);
        
        if (foundCategory) {
            console.log('   - 找到的分类:', foundCategory);
        }
        if (categoryEmojis.length > 0) {
            console.log('   - 找到的表情包:', categoryEmojis);
        }
        
        // 分析问题
        const issues = [];
        if (!foundCategory) {
            issues.push('云函数无法读取到新创建的分类');
        }
        if (categoryEmojis.length === 0) {
            issues.push('云函数无法按分类名称查询到表情包');
        }
        
        window.diagnosisResults.steps.push({
            step: 5,
            name: '验证数据读取',
            status: issues.length === 0 ? 'success' : 'partial',
            details: {
                foundCategory: !!foundCategory,
                allEmojisCount: allEmojis.length,
                categoryEmojisCount: categoryEmojis.length,
                issues
            }
        });
        
        if (issues.length > 0) {
            window.diagnosisResults.issues.push(...issues);
        }
        
        return { 
            success: issues.length === 0, 
            foundCategory, 
            allEmojis, 
            categoryEmojis,
            issues
        };
        
    } catch (error) {
        console.error('❌ 数据读取验证失败:', error.message);
        window.diagnosisResults.steps.push({
            step: 5,
            name: '验证数据读取',
            status: 'failed',
            error: error.message
        });
        window.diagnosisResults.issues.push('数据读取验证失败');
        return { success: false, error };
    }
}

// 生成诊断报告
function generateDiagnosisReport() {
    console.log('\n📊 生成诊断报告...');
    
    const report = {
        timestamp: new Date().toISOString(),
        config: DIAGNOSIS_CONFIG,
        steps: window.diagnosisResults.steps,
        issues: window.diagnosisResults.issues,
        recommendations: []
    };
    
    // 分析问题并生成建议
    if (report.issues.includes('云函数无法按分类名称查询到表情包')) {
        report.recommendations.push('检查云函数dataAPI中的表情包查询逻辑，确保支持按category字段查询');
        report.recommendations.push('验证表情包数据的category字段是否正确保存');
    }
    
    if (report.issues.includes('云函数无法读取到新创建的分类')) {
        report.recommendations.push('检查云函数dataAPI中的分类查询逻辑');
        report.recommendations.push('验证分类数据是否正确保存到数据库');
    }
    
    if (report.issues.includes('云函数dataAPI调用失败')) {
        report.recommendations.push('检查云函数dataAPI是否正确部署');
        report.recommendations.push('查看云函数执行日志');
    }
    
    if (report.issues.length === 0) {
        report.recommendations.push('数据流程正常，问题可能在小程序端的数据显示逻辑');
    }
    
    console.log('📋 诊断报告:');
    console.log('   - 执行步骤:', report.steps.length);
    console.log('   - 发现问题:', report.issues.length);
    console.log('   - 建议措施:', report.recommendations.length);
    
    if (report.issues.length > 0) {
        console.log('\n❌ 发现的问题:');
        report.issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    if (report.recommendations.length > 0) {
        console.log('\n💡 建议措施:');
        report.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
    }
    
    window.diagnosisResults.report = report;
    return report;
}

// 主诊断函数
async function runDiagnosis() {
    console.log('🚀 开始完整诊断流程...\n');
    
    try {
        // 步骤1: 检查云开发环境
        const step1Result = await step1_checkCloudEnvironment();
        if (!step1Result.success) {
            console.log('❌ 诊断中止：云开发环境检查失败');
            return generateDiagnosisReport();
        }
        
        // 步骤2: 检查数据库数据
        const step2Result = await step2_checkDatabaseData(step1Result.db);
        if (!step2Result.success) {
            console.log('❌ 诊断中止：数据库数据检查失败');
            return generateDiagnosisReport();
        }
        
        // 步骤3: 测试云函数
        const step3Result = await step3_testDataAPI(step1Result.app);
        if (!step3Result.success) {
            console.log('❌ 诊断中止：云函数测试失败');
            return generateDiagnosisReport();
        }
        
        // 步骤4: 创建测试数据
        const step4Result = await step4_createTestData(step1Result.db);
        if (!step4Result.success) {
            console.log('❌ 诊断中止：测试数据创建失败');
            return generateDiagnosisReport();
        }
        
        // 步骤5: 验证数据读取
        const step5Result = await step5_verifyDataRead(step1Result.app, step4Result.testCategoryName);
        
        // 生成最终报告
        const report = generateDiagnosisReport();
        
        console.log('\n✅ 诊断完成！');
        console.log('详细结果已保存在 window.diagnosisResults 中');
        
        return report;
        
    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error);
        window.diagnosisResults.issues.push('诊断过程异常: ' + error.message);
        return generateDiagnosisReport();
    }
}

// 导出函数供手动调用
window.runDiagnosis = runDiagnosis;
window.step1_checkCloudEnvironment = step1_checkCloudEnvironment;
window.step2_checkDatabaseData = step2_checkDatabaseData;
window.step3_testDataAPI = step3_testDataAPI;
window.step4_createTestData = step4_createTestData;
window.step5_verifyDataRead = step5_verifyDataRead;
window.generateDiagnosisReport = generateDiagnosisReport;

console.log('📋 诊断脚本已加载！');
console.log('运行 runDiagnosis() 开始完整诊断');
console.log('或者单独运行各个步骤函数进行分步诊断');

// 自动运行诊断（可选）
// runDiagnosis();
