// 测试表情包管理功能
const { chromium } = require('playwright');

async function testEmojiManagement() {
    console.log('😊 开始表情包管理测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理表情包数据') || text.includes('表情包数据加载') || text.includes('undefined')) {
            console.log('🔧', text);
        }
    });
    
    try {
        console.log('📍 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录完成');
            await page.waitForTimeout(8000);
        }
        
        console.log('\n📍 进入表情包管理');
        const emojiLink = await page.locator('text=😊 表情包管理').first();
        if (await emojiLink.isVisible()) {
            await emojiLink.click();
            console.log('✅ 已进入表情包管理页面');
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查表情包数据显示');
        
        // 检查表情包表格数据
        const emojiData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                return {
                    index: index,
                    data: cells.map(cell => cell.textContent?.trim()),
                    hasUndefined: cells.some(cell => cell.textContent?.includes('undefined')),
                    hasImage: !!row.querySelector('img')
                };
            });
        });
        
        console.log('📋 表情包表格数据:');
        let hasIssues = false;
        
        emojiData.forEach((row, index) => {
            console.log(`\n  行 ${index + 1}:`);
            console.log(`    数据: [${row.data.slice(0, 5).join(', ')}...]`); // 只显示前5列
            console.log(`    有undefined: ${row.hasUndefined}`);
            console.log(`    有图片: ${row.hasImage}`);
            
            if (row.hasUndefined) {
                console.log('    🔴 发现undefined数据');
                hasIssues = true;
            }
        });
        
        if (!hasIssues) {
            console.log('\n✅ 表情包数据显示正常 - 未发现undefined问题');
        } else {
            console.log('\n🔴 表情包数据有问题');
        }
        
        // 检查AdminApp数据状态
        const adminAppData = await page.evaluate(() => {
            return {
                hasEmojis: !!AdminApp.data.emojis,
                emojisCount: AdminApp.data.emojis ? AdminApp.data.emojis.length : 0,
                sampleEmoji: AdminApp.data.emojis && AdminApp.data.emojis.length > 0 ? {
                    name: AdminApp.data.emojis[0].name,
                    status: AdminApp.data.emojis[0].status,
                    hasName: !!AdminApp.data.emojis[0].name
                } : null
            };
        });
        
        console.log('\n📊 AdminApp表情包数据状态:');
        console.log('  有表情包数据:', adminAppData.hasEmojis);
        console.log('  表情包数量:', adminAppData.emojisCount);
        if (adminAppData.sampleEmoji) {
            console.log('  示例表情包:');
            console.log('    名称:', adminAppData.sampleEmoji.name);
            console.log('    状态:', adminAppData.sampleEmoji.status);
            console.log('    有名称:', adminAppData.sampleEmoji.hasName);
        }
        
        // 获取原始表情包数据
        const rawEmojiData = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('emojis');
                return {
                    success: result.success,
                    dataLength: result.data ? result.data.length : 0,
                    sampleData: result.data && result.data.length > 0 ? result.data[0] : null
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('\n📊 原始表情包数据:');
        console.log('成功:', rawEmojiData.success);
        console.log('数据长度:', rawEmojiData.dataLength);
        
        if (rawEmojiData.sampleData) {
            console.log('\n📋 示例表情包数据:');
            console.log('完整数据:', JSON.stringify(rawEmojiData.sampleData, null, 2));
        }
        
        // 截图
        await page.screenshot({ path: 'emoji-management-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: emoji-management-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testEmojiManagement().catch(console.error);
