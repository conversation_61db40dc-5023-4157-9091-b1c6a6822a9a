<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            display: block;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            background: white;
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .tab.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: #f7fafc;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table tr:hover {
            background: #f7fafc;
        }

        .emoji-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            background: #667eea;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #c6f6d5;
            color: #22543d;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
        }

        .close {
            float: right;
            font-size: 24px;
            cursor: pointer;
            color: #a0aec0;
        }

        .close:hover {
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
            
            .table {
                font-size: 13px;
            }
            
            .table th,
            .table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎭 表情包管理后台</h1>
        <p>管理表情包内容和分类</p>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number" id="totalEmojis">-</span>
                <span class="stat-label">表情包总数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalCategories">-</span>
                <span class="stat-label">分类总数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalLikes">-</span>
                <span class="stat-label">总点赞数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalDownloads">-</span>
                <span class="stat-label">总下载数</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
            <button class="btn btn-success" onclick="initTestData()">
                🔄 初始化测试数据
            </button>
            <button class="btn btn-primary" onclick="refreshData()">
                🔃 刷新数据
            </button>
            <button class="btn btn-danger" onclick="clearAllData()">
                🗑️ 清空所有数据
            </button>
        </div>

        <!-- 选项卡 -->
        <div class="tabs">
            <button class="tab active" onclick="switchTab('emojis')">📦 表情包管理</button>
            <button class="tab" onclick="switchTab('categories')">📂 分类管理</button>
        </div>

        <!-- 表情包管理 -->
        <div class="tab-content" id="emojis-content">
            <h3>📦 表情包列表</h3>
            <div id="emojis-loading" class="loading">
                <p>加载中...</p>
            </div>
            <div id="emojis-list"></div>
        </div>

        <!-- 分类管理 -->
        <div class="tab-content" id="categories-content" style="display: none;">
            <h3>📂 分类列表</h3>
            <div id="categories-loading" class="loading">
                <p>加载中...</p>
            </div>
            <div id="categories-list"></div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
