/**
 * 数据同步配置文件
 * 管理所有与数据同步相关的配置
 */

module.exports = {
  // 云开发环境配置
  cloud: {
    env: 'cloud1-5g6pvnpl88dc0142',
    timeout: 10000, // 10秒超时
    retryTimes: 3,   // 重试次数
    retryDelay: 1000 // 重试延迟(ms)
  },

  // 服务器配置
  server: {
    port: 8001,
    host: 'localhost',
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }
  },

  // 数据库集合配置
  collections: {
    categories: {
      name: 'categories',
      indexes: ['status', 'sort', 'createTime'],
      defaultSort: { sort: 1, createTime: -1 }
    },
    emojis: {
      name: 'emojis',
      indexes: ['status', 'category', 'createTime'],
      defaultSort: { createTime: -1 }
    },
    banners: {
      name: 'banners',
      indexes: ['status', 'priority', 'createTime'],
      defaultSort: { priority: -1, createTime: -1 }
    },
    users: {
      name: 'users',
      indexes: ['openid', 'createTime'],
      defaultSort: { createTime: -1 }
    }
  },

  // 数据验证规则
  validation: {
    categories: {
      required: ['name', 'icon', 'status'],
      maxLength: {
        name: 50,
        icon: 10
      },
      allowedStatus: ['show', 'hide']
    },
    emojis: {
      required: ['title', 'category', 'status'],
      maxLength: {
        title: 100,
        category: 50
      },
      allowedStatus: ['published', 'draft']
    },
    banners: {
      required: ['title', 'imageUrl', 'status'],
      maxLength: {
        title: 100,
        imageUrl: 500
      },
      allowedStatus: ['show', 'hide']
    }
  },

  // 同步配置
  sync: {
    batchSize: 100,        // 批量操作大小
    maxConcurrency: 5,     // 最大并发数
    syncInterval: 5000,    // 同步间隔(ms)
    enableRealtime: true,  // 启用实时同步
    enableCache: true,     // 启用缓存
    cacheTimeout: 300000   // 缓存超时(ms) - 5分钟
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: true,
    logDir: './logs',
    maxFileSize: '10MB',
    maxFiles: 5,
    datePattern: 'YYYY-MM-DD'
  },

  // 错误处理配置
  errorHandling: {
    enableGlobalHandler: true,
    enableDetailedErrors: process.env.NODE_ENV !== 'production',
    maxRetries: 3,
    retryDelay: 1000,
    timeoutMs: 30000
  },

  // 性能监控配置
  monitoring: {
    enableMetrics: true,
    metricsInterval: 60000, // 1分钟
    enableHealthCheck: true,
    healthCheckInterval: 30000, // 30秒
    alertThresholds: {
      responseTime: 5000,    // 5秒
      errorRate: 0.05,       // 5%
      memoryUsage: 0.8       // 80%
    }
  },

  // 安全配置
  security: {
    enableRateLimit: true,
    rateLimitWindow: 900000, // 15分钟
    rateLimitMax: 100,       // 最大请求数
    enableCors: true,
    enableHelmet: true,
    secretRotationDays: 30
  },

  // 开发环境特殊配置
  development: {
    enableDebugLogs: true,
    enableMockData: false,
    enableHotReload: true,
    skipAuthentication: false
  },

  // 生产环境特殊配置
  production: {
    enableDebugLogs: false,
    enableMockData: false,
    enableHotReload: false,
    skipAuthentication: false,
    enableCompression: true,
    enableHttps: true
  },

  // 测试环境配置
  test: {
    enableDebugLogs: true,
    enableMockData: true,
    enableHotReload: false,
    skipAuthentication: true,
    testTimeout: 10000,
    mockDataSize: 10
  }
}

// 根据环境变量获取当前配置
function getCurrentConfig() {
  const env = process.env.NODE_ENV || 'development'
  const baseConfig = module.exports
  const envConfig = baseConfig[env] || {}
  
  return {
    ...baseConfig,
    ...envConfig,
    currentEnv: env
  }
}

module.exports.getCurrentConfig = getCurrentConfig
