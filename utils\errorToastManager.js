/**
 * 错误提示管理器
 * 提供统一的错误提示接口，支持不同类型的错误展示
 */

const ErrorToastManager = {
  // 当前显示的错误提示
  currentToast: null,
  
  // 错误提示队列
  toastQueue: [],
  
  // 配置选项
  config: {
    maxQueueSize: 5,        // 最大队列长度
    defaultDuration: 3000,  // 默认显示时长
    enableQueue: true,      // 是否启用队列
    enableVibration: true   // 是否启用震动反馈
  },

  /**
   * 显示错误提示
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showError(type, message, options = {}) {
    const errorInfo = {
      type: type || 'default',
      message: message || '操作失败',
      duration: options.duration || this.config.defaultDuration,
      showRetry: options.showRetry || false,
      retryCallback: options.retryCallback,
      timestamp: Date.now(),
      id: this.generateToastId()
    }

    // 如果启用队列且当前有提示在显示
    if (this.config.enableQueue && this.currentToast) {
      this.addToQueue(errorInfo)
      return errorInfo.id
    }

    this.displayError(errorInfo)
    return errorInfo.id
  },

  /**
   * 显示网络错误
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showNetworkError(message = '网络连接异常，请检查网络设置', options = {}) {
    return this.showError('network', message, {
      ...options,
      showRetry: true
    })
  },

  /**
   * 显示API错误
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showApiError(message = '服务暂时不可用，请稍后重试', options = {}) {
    return this.showError('api', message, {
      ...options,
      showRetry: true
    })
  },

  /**
   * 显示用户操作错误
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showUserError(message = '操作失败，请重试', options = {}) {
    return this.showError('user', message, options)
  },

  /**
   * 显示验证错误
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showValidationError(message = '输入信息有误，请检查后重试', options = {}) {
    return this.showError('validation', message, options)
  },

  /**
   * 显示系统错误
   * @param {string} message - 错误消息
   * @param {Object} options - 选项
   */
  showSystemError(message = '系统异常，请重启应用', options = {}) {
    return this.showError('system', message, options)
  },

  /**
   * 实际显示错误提示
   * @param {Object} errorInfo - 错误信息
   */
  displayError(errorInfo) {
    this.currentToast = errorInfo

    // 震动反馈
    if (this.config.enableVibration) {
      this.triggerVibration(errorInfo.type)
    }

    // 显示微信原生提示
    this.showWxToast(errorInfo)

    // 设置自动隐藏
    if (errorInfo.duration > 0) {
      setTimeout(() => {
        this.hideCurrentToast()
      }, errorInfo.duration)
    }
  },

  /**
   * 显示微信原生提示
   * @param {Object} errorInfo - 错误信息
   */
  showWxToast(errorInfo) {
    const iconMap = {
      network: 'none',
      api: 'none',
      user: 'none',
      validation: 'none',
      system: 'none',
      default: 'none'
    }

    wx.showToast({
      title: errorInfo.message,
      icon: iconMap[errorInfo.type] || 'none',
      duration: errorInfo.duration,
      mask: true
    })
  },

  /**
   * 触发震动反馈
   * @param {string} errorType - 错误类型
   */
  triggerVibration(errorType) {
    try {
      const vibrationMap = {
        network: 'medium',
        api: 'medium',
        user: 'light',
        validation: 'light',
        system: 'heavy',
        default: 'light'
      }

      const vibrationType = vibrationMap[errorType] || 'light'
      
      wx.vibrateShort({
        type: vibrationType
      })
    } catch (error) {
      console.warn('⚠️ 震动反馈失败:', error)
    }
  },

  /**
   * 添加到队列
   * @param {Object} errorInfo - 错误信息
   */
  addToQueue(errorInfo) {
    // 检查队列长度
    if (this.toastQueue.length >= this.config.maxQueueSize) {
      this.toastQueue.shift() // 移除最旧的
    }

    this.toastQueue.push(errorInfo)
    console.log(`📋 错误提示已加入队列，当前队列长度: ${this.toastQueue.length}`)
  },

  /**
   * 隐藏当前提示
   */
  hideCurrentToast() {
    if (this.currentToast) {
      console.log(`🔇 隐藏错误提示: ${this.currentToast.id}`)
      this.currentToast = null

      // 显示队列中的下一个提示
      this.showNextInQueue()
    }
  },

  /**
   * 显示队列中的下一个提示
   */
  showNextInQueue() {
    if (this.toastQueue.length > 0) {
      const nextError = this.toastQueue.shift()
      setTimeout(() => {
        this.displayError(nextError)
      }, 300) // 短暂延迟，避免提示重叠
    }
  },

  /**
   * 清空队列
   */
  clearQueue() {
    this.toastQueue = []
    console.log('🧹 错误提示队列已清空')
  },

  /**
   * 隐藏所有提示
   */
  hideAll() {
    wx.hideToast()
    this.currentToast = null
    this.clearQueue()
  },

  /**
   * 生成提示ID
   */
  generateToastId() {
    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      currentToast: this.currentToast,
      queueLength: this.toastQueue.length,
      isShowing: !!this.currentToast
    }
  },

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    console.log('⚙️ 错误提示管理器配置已更新:', this.config)
  },

  /**
   * 创建带重试功能的错误提示
   * @param {string} type - 错误类型
   * @param {string} message - 错误消息
   * @param {Function} retryCallback - 重试回调
   * @param {Object} options - 选项
   */
  showRetryableError(type, message, retryCallback, options = {}) {
    return this.showError(type, message, {
      ...options,
      showRetry: true,
      retryCallback: retryCallback,
      duration: 0 // 不自动隐藏，等待用户操作
    })
  },

  /**
   * 显示加载错误（带重试）
   * @param {string} message - 错误消息
   * @param {Function} retryCallback - 重试回调
   */
  showLoadingError(message = '加载失败', retryCallback) {
    return this.showRetryableError('network', message, retryCallback, {
      duration: 5000 // 5秒后自动隐藏
    })
  }
}

module.exports = ErrorToastManager
