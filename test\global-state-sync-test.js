/**
 * 全局状态同步测试
 * 测试跨页面状态同步功能
 */

// 设置测试环境
process.env.NODE_ENV = 'test'

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    const storage = global._mockStorage || {}
    return storage[key] || null
  },
  setStorageSync: (key, value) => {
    if (!global._mockStorage) global._mockStorage = {}
    global._mockStorage[key] = value
  },
  removeStorageSync: (key) => {
    if (global._mockStorage) {
      delete global._mockStorage[key]
    }
  },
  showToast: () => {},
  onNetworkStatusChange: () => {},
  cloud: {
    callFunction: () => Promise.resolve({ result: { success: false } })
  }
}

// 模拟getApp
global.getApp = () => ({
  globalData: {
    userId: 'test_user_123',
    version: '2.0.0'
  }
})

// 导入测试模块
const { StateManager } = require('../utils/stateManager.js')

/**
 * 全局状态同步测试套件
 */
class GlobalStateSyncTest {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    }
    this.mockPages = new Map()
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始全局状态同步测试...\n')

    try {
      // 初始化状态管理器
      await this.initializeStateManager()

      // 基础状态管理测试
      await this.testBasicStateManagement()

      // 跨页面状态同步测试
      await this.testCrossPageSync()

      // 监听器管理测试
      await this.testListenerManagement()

      // 状态持久化测试
      await this.testStatePersistence()

      // 输出测试结果
      this.printTestResults()

    } catch (error) {
      console.error('❌ 测试运行失败:', error)
    }
  }

  /**
   * 初始化状态管理器
   */
  async initializeStateManager() {
    console.log('🔄 初始化状态管理器...')
    StateManager.init()
    console.log('✅ 状态管理器初始化完成\n')
  }

  /**
   * 基础状态管理测试
   */
  async testBasicStateManagement() {
    console.log('📊 基础状态管理测试开始...')

    // 测试1: 点赞状态管理
    await this.runTest('点赞状态管理', () => {
      const emojiId = 'test_emoji_1'
      
      // 初始状态应该是未点赞
      const initialState = StateManager.getEmojiState(emojiId)
      if (initialState.isLiked) {
        throw new Error('初始状态应该是未点赞')
      }

      // 切换点赞状态
      StateManager.toggleLike(emojiId)
      const likedState = StateManager.getEmojiState(emojiId)
      if (!likedState.isLiked) {
        throw new Error('点赞后状态应该是已点赞')
      }

      // 再次切换应该取消点赞
      StateManager.toggleLike(emojiId)
      const unlikedState = StateManager.getEmojiState(emojiId)
      if (unlikedState.isLiked) {
        throw new Error('取消点赞后状态应该是未点赞')
      }

      return true
    })

    // 测试2: 收藏状态管理
    await this.runTest('收藏状态管理', () => {
      const emojiId = 'test_emoji_2'
      
      // 初始状态应该是未收藏
      const initialState = StateManager.getEmojiState(emojiId)
      if (initialState.isCollected) {
        throw new Error('初始状态应该是未收藏')
      }

      // 切换收藏状态
      StateManager.toggleCollect(emojiId)
      const collectedState = StateManager.getEmojiState(emojiId)
      if (!collectedState.isCollected) {
        throw new Error('收藏后状态应该是已收藏')
      }

      return true
    })

    // 测试3: 下载状态管理
    await this.runTest('下载状态管理', () => {
      const emojiId = 'test_emoji_3'
      
      // 记录下载
      StateManager.recordDownload(emojiId)
      const downloadedState = StateManager.getEmojiState(emojiId)
      
      if (!downloadedState.downloadTime) {
        throw new Error('下载后应该有下载时间记录')
      }

      return true
    })

    console.log('✅ 基础状态管理测试完成\n')
  }

  /**
   * 跨页面状态同步测试
   */
  async testCrossPageSync() {
    console.log('🔄 跨页面状态同步测试开始...')

    // 测试1: 多页面监听器同步
    await this.runTest('多页面监听器同步', () => {
      const emojiId = 'test_emoji_sync_1'
      let page1Updated = false
      let page2Updated = false

      // 模拟两个页面的监听器
      const page1Listener = (data) => {
        if (data.emojiId === emojiId && data.type === 'like') {
          page1Updated = true
        }
      }

      const page2Listener = (data) => {
        if (data.emojiId === emojiId && data.type === 'like') {
          page2Updated = true
        }
      }

      // 添加监听器
      StateManager.addListener('global', page1Listener, 'page1')
      StateManager.addListener('global', page2Listener, 'page2')

      // 在一个页面触发状态变更
      StateManager.toggleLike(emojiId)

      // 检查两个页面都收到了通知
      if (!page1Updated || !page2Updated) {
        throw new Error('跨页面状态同步失败')
      }

      // 清理监听器
      StateManager.removePageListeners('page1')
      StateManager.removePageListeners('page2')

      return true
    })

    // 测试2: 状态一致性检查
    await this.runTest('状态一致性检查', () => {
      const emojiId = 'test_emoji_sync_2'
      
      // 在多个地方获取状态，应该保持一致
      StateManager.toggleLike(emojiId)
      StateManager.toggleCollect(emojiId)

      const state1 = StateManager.getEmojiState(emojiId)
      const state2 = StateManager.getEmojiState(emojiId)
      const state3 = StateManager.getEmojiState(emojiId)

      if (state1.isLiked !== state2.isLiked || 
          state2.isLiked !== state3.isLiked ||
          state1.isCollected !== state2.isCollected ||
          state2.isCollected !== state3.isCollected) {
        throw new Error('状态一致性检查失败')
      }

      return true
    })

    console.log('✅ 跨页面状态同步测试完成\n')
  }

  /**
   * 监听器管理测试
   */
  async testListenerManagement() {
    console.log('📡 监听器管理测试开始...')

    // 测试1: 监听器添加和移除
    await this.runTest('监听器添加和移除', () => {
      let callCount = 0
      const testListener = () => { callCount++ }

      // 添加监听器
      StateManager.addListener('like', testListener, 'test_page')

      // 触发事件
      StateManager.toggleLike('test_emoji_listener')
      
      if (callCount !== 1) {
        throw new Error('监听器未正确触发')
      }

      // 移除页面监听器
      StateManager.removePageListeners('test_page')

      // 再次触发事件
      StateManager.toggleLike('test_emoji_listener')
      
      if (callCount !== 1) {
        throw new Error('监听器未正确移除')
      }

      return true
    })

    // 测试2: 监听器统计
    await this.runTest('监听器统计', () => {
      // 添加一些监听器
      StateManager.addListener('like', () => {}, 'stats_page_1')
      StateManager.addListener('collect', () => {}, 'stats_page_1')
      StateManager.addListener('global', () => {}, 'stats_page_2')

      const stats = StateManager.getListenerStats()
      
      if (stats.like < 1 || stats.collect < 1 || stats.global < 1) {
        throw new Error('监听器统计不正确')
      }

      // 清理
      StateManager.removePageListeners('stats_page_1')
      StateManager.removePageListeners('stats_page_2')

      return true
    })

    console.log('✅ 监听器管理测试完成\n')
  }

  /**
   * 状态持久化测试
   */
  async testStatePersistence() {
    console.log('💾 状态持久化测试开始...')

    // 测试1: 本地存储保存和加载
    await this.runTest('本地存储保存和加载', () => {
      const emojiId = 'test_emoji_persist'
      
      // 设置一些状态
      StateManager.toggleLike(emojiId)
      StateManager.toggleCollect(emojiId)
      StateManager.recordDownload(emojiId)

      // 手动保存到本地存储
      StateManager.saveToLocalStorage()

      // 清空内存状态
      StateManager._state.likedEmojis.clear()
      StateManager._state.collectedEmojis.clear()
      StateManager._state.downloadedEmojis.clear()
      StateManager._state.downloadTimes = {}

      // 从本地存储加载
      StateManager.loadFromLocalStorage()

      // 检查状态是否正确恢复
      const restoredState = StateManager.getEmojiState(emojiId)
      
      if (!restoredState.isLiked || !restoredState.isCollected || !restoredState.downloadTime) {
        throw new Error('状态持久化失败')
      }

      return true
    })

    console.log('✅ 状态持久化测试完成\n')
  }

  /**
   * 运行单个测试
   */
  async runTest(testName, testFunction) {
    try {
      console.log(`  🧪 ${testName}...`)
      const result = await testFunction()
      
      if (result) {
        this.testResults.passed++
        this.testResults.tests.push({ name: testName, status: 'PASSED' })
        console.log(`  ✅ ${testName} - 通过`)
      } else {
        throw new Error('测试返回false')
      }
    } catch (error) {
      this.testResults.failed++
      this.testResults.tests.push({ 
        name: testName, 
        status: 'FAILED', 
        error: error.message 
      })
      console.log(`  ❌ ${testName} - 失败: ${error.message}`)
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60))
    console.log('📊 全局状态同步测试结果')
    console.log('='.repeat(60))

    console.log(`总计: ${this.testResults.passed + this.testResults.failed} 个测试`)
    console.log(`通过: ${this.testResults.passed} 个`)
    console.log(`失败: ${this.testResults.failed} 个`)
    console.log(`成功率: ${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)}%`)

    if (this.testResults.failed > 0) {
      console.log('\n失败的测试:')
      this.testResults.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error}`)
        })
    }

    if (this.testResults.failed === 0) {
      console.log('\n🎉 所有测试通过！全局状态同步功能正常！')
    } else {
      console.log('\n⚠️ 部分测试失败，需要修复后重新测试')
    }

    console.log('='.repeat(60))
  }
}

// 运行测试
if (require.main === module) {
  const test = new GlobalStateSyncTest()
  test.runAllTests().catch(console.error)
}

module.exports = GlobalStateSyncTest
