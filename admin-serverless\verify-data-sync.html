<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步验证工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }
        
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .data-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
        }
        
        .data-panel h4 {
            margin: 0 0 12px 0;
            color: #333;
        }
        
        .data-item {
            background: white;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            border-left: 4px solid #667eea;
        }
        
        .status {
            padding: 8px 16px;
            border-radius: 6px;
            margin: 8px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🔄 数据同步验证工具</h1>
    <p>验证管理后台写入的数据是否能被小程序正确读取</p>

    <!-- 环境检测 -->
    <div class="test-card">
        <div class="test-title">0. 🔍 环境检测</div>
        <button class="test-button" onclick="checkEnvironment()">检测环境</button>
        <div id="env-result" class="test-result"></div>
    </div>

    <!-- 第一步：初始化Web SDK -->
    <div class="test-card">
        <div class="test-title">1. 🌤️ 初始化Web SDK</div>
        <button class="test-button" onclick="initWebSDK()">初始化SDK</button>
        <div id="init-result" class="test-result"></div>
    </div>

    <!-- 第二步：写入测试数据 -->
    <div class="test-card">
        <div class="test-title">2. 📝 管理后台写入数据</div>
        <button class="test-button" onclick="writeTestData()">写入测试数据</button>
        <button class="test-button danger" onclick="clearTestData()">清空测试数据</button>
        <div id="write-result" class="test-result"></div>
    </div>

    <!-- 第三步：模拟小程序读取 -->
    <div class="test-card">
        <div class="test-title">3. 📱 模拟小程序读取数据</div>
        <button class="test-button" onclick="simulateMiniProgramRead()">模拟小程序调用</button>
        <div id="read-result" class="test-result"></div>
    </div>

    <!-- 第四步：数据对比 -->
    <div class="test-card">
        <div class="test-title">4. 🔍 数据同步验证</div>
        <button class="test-button success" onclick="verifyDataSync()">验证数据同步</button>
        <div id="verify-result" class="test-result"></div>
        
        <div class="data-comparison" id="data-comparison" style="display: none;">
            <div class="data-panel">
                <h4>📝 管理后台写入的数据</h4>
                <div id="admin-data"></div>
            </div>
            <div class="data-panel">
                <h4>📱 小程序读取的数据</h4>
                <div id="miniprogram-data"></div>
            </div>
        </div>
    </div>

    <script>
        let adminWrittenData = null;
        let miniprogramReadData = null;

        // 环境检测
        async function checkEnvironment() {
            const resultDiv = document.getElementById('env-result');
            resultDiv.textContent = '正在检测环境...\n';

            try {
                // 检测网络连接
                resultDiv.textContent += '🌐 检测网络连接...\n';
                const networkTest = await fetch('https://www.baidu.com', {
                    method: 'HEAD',
                    mode: 'no-cors',
                    timeout: 5000
                });
                resultDiv.textContent += '✅ 网络连接正常\n';

                // 检测CDN可用性
                resultDiv.textContent += '📦 检测CDN可用性...\n';
                const cdnUrls = [
                    'https://web.sdk.qcloud.com/tcb/1.10.10/tcb.js',
                    'https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js'
                ];

                for (const url of cdnUrls) {
                    try {
                        const response = await fetch(url, { method: 'HEAD' });
                        if (response.ok) {
                            resultDiv.textContent += `✅ CDN可用: ${url}\n`;
                        } else {
                            resultDiv.textContent += `❌ CDN不可用: ${url} (${response.status})\n`;
                        }
                    } catch (error) {
                        resultDiv.textContent += `❌ CDN不可用: ${url} (${error.message})\n`;
                    }
                }

                // 检测浏览器兼容性
                resultDiv.textContent += '🔧 检测浏览器兼容性...\n';
                if (typeof Promise === 'undefined') {
                    resultDiv.textContent += '❌ 浏览器不支持Promise\n';
                } else {
                    resultDiv.textContent += '✅ Promise支持正常\n';
                }

                if (typeof fetch === 'undefined') {
                    resultDiv.textContent += '❌ 浏览器不支持fetch\n';
                } else {
                    resultDiv.textContent += '✅ fetch支持正常\n';
                }

                // 检测本地存储
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    resultDiv.textContent += '✅ localStorage支持正常\n';
                } catch (error) {
                    resultDiv.textContent += '❌ localStorage不可用\n';
                }

                resultDiv.textContent += '\n🎯 环境检测完成，可以尝试初始化SDK\n';

            } catch (error) {
                resultDiv.textContent += `❌ 环境检测失败: ${error.message}\n`;
            }
        }

        // 初始化Web SDK
        async function initWebSDK() {
            const resultDiv = document.getElementById('init-result');
            resultDiv.textContent = '正在初始化Web SDK...\n';

            try {
                // 尝试多个CDN地址
                const cdnUrls = [
                    'https://web.sdk.qcloud.com/tcb/1.10.10/tcb.js',
                    'https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js',
                    'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@1.7.0/dist/index.umd.js'
                ];

                if (!window.tcb) {
                    resultDiv.textContent += '📦 尝试加载云开发SDK...\n';

                    let sdkLoaded = false;
                    for (const url of cdnUrls) {
                        try {
                            resultDiv.textContent += `🔄 尝试CDN: ${url}\n`;
                            await loadScript(url);
                            if (window.tcb || window.cloudbase) {
                                sdkLoaded = true;
                                resultDiv.textContent += `✅ SDK加载成功\n`;
                                break;
                            }
                        } catch (error) {
                            resultDiv.textContent += `❌ CDN失败: ${error.message}\n`;
                            continue;
                        }
                    }

                    if (!sdkLoaded) {
                        throw new Error('所有CDN都无法加载SDK');
                    }
                }

                // 兼容不同的SDK版本
                const tcb = window.tcb || window.cloudbase;
                if (!tcb) {
                    throw new Error('SDK加载后未找到tcb或cloudbase对象');
                }

                resultDiv.textContent += '🔧 正在初始化云开发应用...\n';

                // 初始化云开发应用
                window.tcbApp = tcb.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });

                resultDiv.textContent += '🔐 正在进行身份验证...\n';

                // 匿名登录
                const auth = window.tcbApp.auth();
                await auth.signInAnonymously();

                // 验证登录状态
                const user = auth.currentUser;
                if (!user) {
                    throw new Error('登录失败，未获取到用户信息');
                }

                resultDiv.textContent += '✅ Web SDK初始化成功\n';
                resultDiv.textContent += `✅ 匿名登录成功，用户ID: ${user.uid}\n`;
                resultDiv.textContent += `🌤️ 云环境: cloud1-5g6pvnpl88dc0142\n`;

            } catch (error) {
                resultDiv.textContent += `❌ 初始化失败: ${error.message}\n`;
                resultDiv.textContent += `💡 可能的原因:\n`;
                resultDiv.textContent += `   1. 网络连接问题\n`;
                resultDiv.textContent += `   2. 云环境ID不正确\n`;
                resultDiv.textContent += `   3. CDN被屏蔽\n`;
                resultDiv.textContent += `   4. 云开发服务未开启\n`;
            }
        }

        // 写入测试数据（管理后台操作）
        async function writeTestData() {
            const resultDiv = document.getElementById('write-result');
            resultDiv.textContent = '正在写入测试数据...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const db = window.tcbApp.database();
                
                // 准备测试数据 - 严格按照云函数期望的字段结构
                const testCategories = [
                    { 
                        id: 'test_funny', 
                        name: '测试搞笑', 
                        icon: '😂', 
                        sort: 1,
                        status: 'show',  // 重要：云函数查询条件
                        description: '测试搞笑表情包分类'
                    }
                ];
                
                const testEmojis = [
                    { 
                        id: 'test_emoji_1', 
                        name: '测试表情1',
                        title: '测试表情1',
                        url: 'https://example.com/test1.gif',
                        imageUrl: 'https://example.com/test1.gif',
                        category: 'test_funny',
                        categoryId: 'test_funny',  // 重要：云函数查询条件
                        tags: ['测试', '搞笑'],
                        downloads: 0,
                        likes: 5,
                        collections: 3,
                        status: 'published'  // 重要：云函数查询条件
                    },
                    { 
                        id: 'test_emoji_2', 
                        name: '测试表情2',
                        title: '测试表情2',
                        url: 'https://example.com/test2.gif',
                        imageUrl: 'https://example.com/test2.gif',
                        category: 'test_funny',
                        categoryId: 'test_funny',  // 重要：云函数查询条件
                        tags: ['测试', '可爱'],
                        downloads: 0,
                        likes: 8,
                        collections: 6,
                        status: 'published'  // 重要：云函数查询条件
                    }
                ];

                // 写入分类数据
                resultDiv.textContent += '📂 写入分类数据...\n';
                for (const category of testCategories) {
                    await db.collection('categories').add({
                        data: {
                            ...category,
                            createTime: new Date(),
                            updateTime: new Date()
                        }
                    });
                }
                
                // 写入表情包数据
                resultDiv.textContent += '😀 写入表情包数据...\n';
                for (const emoji of testEmojis) {
                    await db.collection('emojis').add({
                        data: {
                            ...emoji,
                            createTime: new Date(),
                            updateTime: new Date()
                        }
                    });
                }
                
                adminWrittenData = {
                    categories: testCategories,
                    emojis: testEmojis
                };
                
                resultDiv.textContent += `✅ 测试数据写入完成\n`;
                resultDiv.textContent += `📊 写入了 ${testCategories.length} 个分类和 ${testEmojis.length} 个表情包\n`;
                resultDiv.textContent += `🔍 数据包含正确的字段：status, categoryId 等\n`;
                
            } catch (error) {
                resultDiv.textContent += `❌ 写入失败: ${error.message}\n`;
            }
        }

        // 清空测试数据
        async function clearTestData() {
            const resultDiv = document.getElementById('write-result');
            resultDiv.textContent += '\n正在清空测试数据...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const db = window.tcbApp.database();
                
                // 删除测试分类
                const categories = await db.collection('categories').where({
                    id: db.RegExp({
                        regexp: '^test_',
                        options: 'i'
                    })
                }).get();
                
                for (const category of categories.data) {
                    await db.collection('categories').doc(category._id).remove();
                }
                
                // 删除测试表情包
                const emojis = await db.collection('emojis').where({
                    id: db.RegExp({
                        regexp: '^test_',
                        options: 'i'
                    })
                }).get();
                
                for (const emoji of emojis.data) {
                    await db.collection('emojis').doc(emoji._id).remove();
                }
                
                resultDiv.textContent += `✅ 清空完成，删除了 ${categories.data.length} 个分类和 ${emojis.data.length} 个表情包\n`;
                
            } catch (error) {
                resultDiv.textContent += `❌ 清空失败: ${error.message}\n`;
            }
        }

        // 模拟小程序读取数据
        async function simulateMiniProgramRead() {
            const resultDiv = document.getElementById('read-result');
            resultDiv.textContent = '正在模拟小程序调用云函数...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                // 模拟小程序调用dataAPI云函数
                resultDiv.textContent += '📱 模拟调用: wx.cloud.callFunction({ name: "dataAPI", data: { action: "getCategories" } })\n';
                
                const categoriesResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                resultDiv.textContent += '📱 模拟调用: wx.cloud.callFunction({ name: "dataAPI", data: { action: "getEmojis" } })\n';
                
                const emojisResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 20 }
                    }
                });

                miniprogramReadData = {
                    categories: categoriesResult.result,
                    emojis: emojisResult.result
                };

                resultDiv.textContent += '✅ 云函数调用成功\n';
                resultDiv.textContent += `📊 分类数据: ${JSON.stringify(categoriesResult.result, null, 2)}\n`;
                resultDiv.textContent += `📊 表情包数据: ${JSON.stringify(emojisResult.result, null, 2)}\n`;
                
            } catch (error) {
                resultDiv.textContent += `❌ 模拟调用失败: ${error.message}\n`;
            }
        }

        // 验证数据同步
        async function verifyDataSync() {
            const resultDiv = document.getElementById('verify-result');
            const comparisonDiv = document.getElementById('data-comparison');
            
            resultDiv.textContent = '正在验证数据同步...\n';

            try {
                if (!adminWrittenData || !miniprogramReadData) {
                    throw new Error('请先完成数据写入和读取测试');
                }

                // 检查分类数据同步
                const adminCategories = adminWrittenData.categories;
                const mpCategories = miniprogramReadData.categories.data || [];
                
                const syncedCategories = mpCategories.filter(mpCat => 
                    adminCategories.some(adminCat => adminCat.id === mpCat.id)
                );

                // 检查表情包数据同步
                const adminEmojis = adminWrittenData.emojis;
                const mpEmojis = miniprogramReadData.emojis.data || [];
                
                const syncedEmojis = mpEmojis.filter(mpEmoji => 
                    adminEmojis.some(adminEmoji => adminEmoji.id === mpEmoji.id)
                );

                // 显示验证结果
                resultDiv.textContent += `📊 数据同步验证结果:\n`;
                resultDiv.textContent += `   管理后台写入分类: ${adminCategories.length} 个\n`;
                resultDiv.textContent += `   小程序读取分类: ${mpCategories.length} 个\n`;
                resultDiv.textContent += `   成功同步分类: ${syncedCategories.length} 个\n`;
                resultDiv.textContent += `   管理后台写入表情包: ${adminEmojis.length} 个\n`;
                resultDiv.textContent += `   小程序读取表情包: ${mpEmojis.length} 个\n`;
                resultDiv.textContent += `   成功同步表情包: ${syncedEmojis.length} 个\n`;

                if (syncedCategories.length === adminCategories.length && 
                    syncedEmojis.length === adminEmojis.length) {
                    resultDiv.textContent += `\n🎉 数据同步验证成功！\n`;
                    resultDiv.textContent += `✅ 管理后台写入的数据完全同步到小程序\n`;
                } else {
                    resultDiv.textContent += `\n⚠️ 数据同步不完整\n`;
                    resultDiv.textContent += `❌ 可能的原因：字段不匹配、查询条件不符等\n`;
                }

                // 显示数据对比
                comparisonDiv.style.display = 'grid';
                displayDataComparison();
                
            } catch (error) {
                resultDiv.textContent += `❌ 验证失败: ${error.message}\n`;
            }
        }

        // 显示数据对比
        function displayDataComparison() {
            const adminDataDiv = document.getElementById('admin-data');
            const miniprogramDataDiv = document.getElementById('miniprogram-data');

            // 显示管理后台数据
            adminDataDiv.innerHTML = '';
            if (adminWrittenData) {
                adminWrittenData.categories.forEach(cat => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.textContent = `${cat.name} (${cat.id}) - status: ${cat.status}`;
                    adminDataDiv.appendChild(div);
                });
                
                adminWrittenData.emojis.forEach(emoji => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.textContent = `${emoji.name} (${emoji.id}) - categoryId: ${emoji.categoryId}, status: ${emoji.status}`;
                    adminDataDiv.appendChild(div);
                });
            }

            // 显示小程序数据
            miniprogramDataDiv.innerHTML = '';
            if (miniprogramReadData) {
                const mpCategories = miniprogramReadData.categories.data || [];
                const mpEmojis = miniprogramReadData.emojis.data || [];
                
                mpCategories.forEach(cat => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.textContent = `${cat.name} (${cat.id}) - status: ${cat.status}`;
                    miniprogramDataDiv.appendChild(div);
                });
                
                mpEmojis.forEach(emoji => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.textContent = `${emoji.name || emoji.title} (${emoji.id}) - categoryId: ${emoji.categoryId}, status: ${emoji.status}`;
                    miniprogramDataDiv.appendChild(div);
                });
            }
        }

        // 动态加载脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
    </script>
</body>
</html>
