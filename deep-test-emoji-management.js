// 深度测试表情包管理功能
const { chromium } = require('playwright');

async function deepTestEmojiManagement() {
    console.log('😀 深度测试表情包管理功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('表情包') || text.includes('emoji') || text.includes('保存') || text.includes('状态')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入表情包管理页面');

        // 使用onclick属性来定位表情包管理链接
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        if (await emojiLink.isVisible()) {
            await emojiLink.click();
            await page.waitForTimeout(5000);
        } else {
            console.log('❌ 未找到表情包管理链接');
            return { success: false, error: '未找到表情包管理链接' };
        }
        
        console.log('\n📍 检查现有表情包数据');
        
        // 检查现有表情包数据
        const existingEmojis = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2]; // 名称列
                const statusCell = cells[7]; // 状态列
                const imageCell = cells[1]; // 图片列
                const img = imageCell ? imageCell.querySelector('img') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent.trim() : 'N/A',
                    hasImage: !!img,
                    imageSrc: img ? img.src : 'N/A',
                    imageAlt: img ? img.alt : 'N/A'
                };
            });
        });
        
        console.log('📊 现有表情包数据:');
        existingEmojis.forEach(emoji => {
            console.log(`\n表情包 ${emoji.index}: ${emoji.name}`);
            console.log(`  状态: ${emoji.status}`);
            console.log(`  有图片: ${emoji.hasImage}`);
            console.log(`  图片源: ${emoji.imageSrc}`);
            console.log(`  图片描述: ${emoji.imageAlt}`);
            
            if (emoji.status === '草稿') {
                console.log(`  🔴 状态显示为草稿`);
            } else if (emoji.status === '显示') {
                console.log(`  ✅ 状态显示正常`);
            }
            
            if (!emoji.hasImage || emoji.imageSrc === 'N/A') {
                console.log(`  🔴 图片预览有问题`);
            } else {
                console.log(`  ✅ 图片预览正常`);
            }
        });
        
        console.log('\n📍 创建新表情包测试');
        
        // 点击添加表情包按钮
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        if (await addEmojiBtn.isVisible()) {
            await addEmojiBtn.click();
            console.log('✅ 已点击添加表情包按钮');
            await page.waitForTimeout(3000);
        } else {
            console.log('❌ 未找到添加表情包按钮');
            return { success: false, error: '未找到添加表情包按钮' };
        }
        
        console.log('\n📍 检查表情包创建表单');

        // 等待弹窗完全加载
        await page.waitForTimeout(2000);

        // 检查表单元素
        const formCheck = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };
            
            const nameInput = modal.querySelector('#emoji-title');
            const categorySelect = modal.querySelector('#emoji-category');
            const statusSelect = modal.querySelector('#emoji-status');
            const fileInput = modal.querySelector('#emoji-image-file');
            const submitBtn = modal.querySelector('button[type="submit"]');
            
            return {
                modalExists: true,
                nameInputExists: !!nameInput,
                categorySelectExists: !!categorySelect,
                statusSelectExists: !!statusSelect,
                statusSelectValue: statusSelect ? statusSelect.value : null,
                fileInputExists: !!fileInput,
                submitBtnExists: !!submitBtn,
                submitBtnText: submitBtn ? submitBtn.textContent?.trim() : null
            };
        });
        
        console.log('📊 表情包表单检查:');
        console.log('弹窗存在:', formCheck.modalExists);
        console.log('名称输入框存在:', formCheck.nameInputExists);
        console.log('分类选择框存在:', formCheck.categorySelectExists);
        console.log('状态选择框存在:', formCheck.statusSelectExists);
        console.log('状态选择框默认值:', formCheck.statusSelectValue);
        console.log('文件输入框存在:', formCheck.fileInputExists);
        console.log('提交按钮存在:', formCheck.submitBtnExists);
        console.log('提交按钮文本:', formCheck.submitBtnText);
        
        if (!formCheck.modalExists) {
            console.log('❌ 表情包创建弹窗未出现');
            return { success: false, error: '表情包创建弹窗未出现' };
        }
        
        console.log('\n📍 填写表情包信息');
        
        // 填写表情包名称
        await page.fill('#emoji-title', '状态测试表情包');
        console.log('✅ 已填写表情包名称');
        
        // 选择分类
        const categoryOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-category');
            if (!select) return [];
            return Array.from(select.options).map(option => ({
                value: option.value,
                text: option.textContent
            }));
        });
        
        console.log('可用分类:', categoryOptions);
        
        if (categoryOptions.length > 1) {
            await page.selectOption('#emoji-category', categoryOptions[1].value);
            console.log(`✅ 已选择分类: ${categoryOptions[1].text}`);
        }
        
        // 重点测试：设置状态为"显示"
        console.log('\n📍 重点测试：设置状态为"显示"');
        
        const statusOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-status');
            if (!select) return [];
            return Array.from(select.options).map(option => ({
                value: option.value,
                text: option.textContent,
                selected: option.selected
            }));
        });
        
        console.log('状态选项:', statusOptions);
        
        // 明确选择"已发布"状态
        await page.selectOption('#emoji-status', 'published');
        console.log('✅ 已明确选择状态为"已发布"');
        
        // 验证状态选择
        const selectedStatus = await page.evaluate(() => {
            const select = document.querySelector('#emoji-status');
            return {
                value: select ? select.value : null,
                text: select ? select.options[select.selectedIndex]?.textContent : null
            };
        });
        
        console.log('选择后的状态:', selectedStatus);
        
        // 模拟文件上传（创建一个测试图片）
        console.log('\n📍 测试图片上传');
        
        // 创建一个简单的测试图片文件
        const testImagePath = await page.evaluate(() => {
            // 创建一个canvas并转换为blob
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            // 绘制一个简单的测试图片
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(0, 0, 100, 100);
            ctx.fillStyle = '#ffffff';
            ctx.font = '20px Arial';
            ctx.fillText('TEST', 30, 55);
            
            return canvas.toDataURL();
        });
        
        console.log('✅ 已创建测试图片');
        
        // 注意：实际的文件上传需要真实文件，这里我们先跳过文件上传，测试其他功能
        console.log('⚠️ 跳过文件上传测试（需要真实文件）');
        
        console.log('\n📍 保存表情包');
        
        // 点击保存按钮
        console.log('\n📍 尝试点击保存按钮');

        // 使用JavaScript直接点击，绕过视窗限制
        const clickResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;

            if (submitBtn) {
                submitBtn.click();
                return { success: true, buttonText: submitBtn.textContent?.trim() };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });

        console.log('点击结果:', clickResult);

        if (clickResult.success) {
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000); // 等待保存完成
        } else {
            console.log('❌ 保存按钮点击失败:', clickResult.error);
        }
        
        console.log('\n📍 验证表情包创建结果');
        
        // 检查表情包列表
        const updatedEmojis = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const statusCell = cells[7];
                const imageCell = cells[1];
                const img = imageCell ? imageCell.querySelector('img') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent.trim() : 'N/A',
                    isNewEmoji: nameCell ? nameCell.textContent.includes('状态测试表情包') : false,
                    hasImage: !!img,
                    imageSrc: img ? img.src : 'N/A'
                };
            });
        });
        
        console.log('📊 更新后的表情包列表:');
        let newEmojiFound = false;
        let statusCorrect = false;
        let imagePreviewWorking = false;
        
        updatedEmojis.forEach(emoji => {
            console.log(`\n表情包 ${emoji.index}: ${emoji.name}`);
            console.log(`  状态: ${emoji.status}`);
            console.log(`  是新表情包: ${emoji.isNewEmoji}`);
            console.log(`  有图片: ${emoji.hasImage}`);
            console.log(`  图片源: ${emoji.imageSrc}`);
            
            if (emoji.isNewEmoji) {
                newEmojiFound = true;
                console.log('  ✅ 找到新创建的表情包！');
                
                if (emoji.status === '已发布' || emoji.status === 'published') {
                    statusCorrect = true;
                    console.log('  ✅ 状态正确显示为"已发布"');
                } else if (emoji.status === '草稿' || emoji.status === 'draft') {
                    console.log('  🔴 状态错误显示为"草稿"');
                } else {
                    console.log(`  ⚠️ 状态显示异常: "${emoji.status}"`);
                }
                
                if (emoji.hasImage && emoji.imageSrc !== 'N/A') {
                    imagePreviewWorking = true;
                    console.log('  ✅ 图片预览正常');
                } else {
                    console.log('  🔴 图片预览有问题');
                }
            }
        });
        
        console.log('\n📊 表情包管理测试结果总结:');
        if (newEmojiFound) {
            console.log('✅ 表情包创建成功');
            if (statusCorrect) {
                console.log('✅ 状态保存正确');
            } else {
                console.log('🔴 状态保存错误 - 需要修复');
            }
            if (imagePreviewWorking) {
                console.log('✅ 图片预览正常');
            } else {
                console.log('🔴 图片预览有问题 - 需要修复');
            }
        } else {
            console.log('🔴 表情包创建失败');
        }
        
        // 截图
        await page.screenshot({ path: 'deep-test-emoji-management.png', fullPage: true });
        console.log('\n📸 测试截图已保存: deep-test-emoji-management.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开20秒供查看...');
        await page.waitForTimeout(20000);
        
        return {
            success: true,
            emojiCreated: newEmojiFound,
            statusCorrect: statusCorrect,
            imagePreviewWorking: imagePreviewWorking
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'emoji-test-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
deepTestEmojiManagement().then(result => {
    console.log('\n🎯 表情包管理测试结果:', result);
}).catch(console.error);
