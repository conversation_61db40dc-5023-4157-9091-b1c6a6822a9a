const { chromium } = require('playwright');

async function testDetailPageDiagnosis() {
  console.log('🔍 开始诊断表情包详情页问题...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 导航到管理后台
    console.log('📱 打开管理后台...');
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForTimeout(8000);
    
    // 点击表情包管理
    console.log('📦 点击表情包管理...');
    await page.click('text=表情包管理');
    await page.waitForTimeout(2000);
    
    // 获取表情包数据
    console.log('🔍 获取表情包数据...');
    const emojiData = await page.evaluate(async () => {
      try {
        // 确保SDK已加载
        if (!window.cloudbase) {
          return { error: 'SDK未加载' };
        }
        
        // 初始化云开发
        const app = window.cloudbase.init({
          env: 'cloud1-5g6pvnpl88dc0142'
        });
        
        // 匿名登录
        await app.auth().signInAnonymously();
        
        // 调用云函数获取表情包列表
        const result = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 5 } }
        });
        
        console.log('表情包列表结果:', result);
        
        if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
          const firstEmoji = result.result.data[0];
          console.log('第一个表情包:', firstEmoji);
          
          // 测试获取详情
          const detailResult = await app.callFunction({
            name: 'dataAPI',
            data: { action: 'getEmojiDetail', data: { id: firstEmoji._id } }
          });
          
          console.log('表情包详情结果:', detailResult);
          
          return {
            listSuccess: true,
            listData: result.result.data,
            detailSuccess: detailResult.result && detailResult.result.success,
            detailData: detailResult.result ? detailResult.result.data : null,
            detailError: detailResult.result ? detailResult.result.message : null,
            firstEmojiId: firstEmoji._id
          };
        } else {
          return {
            listSuccess: false,
            listError: result.result ? result.result.message : '未知错误'
          };
        }
        
      } catch (error) {
        console.error('测试失败:', error);
        return { error: error.message };
      }
    });
    
    console.log('🔍 诊断结果:', JSON.stringify(emojiData, null, 2));
    
    // 分析结果
    if (emojiData.error) {
      console.error('❌ 基础测试失败:', emojiData.error);
    } else if (!emojiData.listSuccess) {
      console.error('❌ 表情包列表获取失败:', emojiData.listError);
    } else if (!emojiData.detailSuccess) {
      console.error('❌ 表情包详情获取失败:', emojiData.detailError);
      console.log('📋 建议检查:');
      console.log('  1. 云函数 dataAPI 中的 getEmojiDetail 方法');
      console.log('  2. 数据库中表情包数据的完整性');
      console.log('  3. 表情包ID的格式和有效性');
    } else {
      console.log('✅ 云函数接口测试通过!');
      console.log('📊 表情包详情数据:');
      console.log(`  - ID: ${emojiData.detailData._id || emojiData.detailData.id}`);
      console.log(`  - 标题: ${emojiData.detailData.title}`);
      console.log(`  - 图片: ${emojiData.detailData.imageUrl}`);
      console.log(`  - 分类: ${emojiData.detailData.category}`);
      console.log(`  - 标签: ${JSON.stringify(emojiData.detailData.tags)}`);
      
      // 测试小程序端路由
      console.log('🔗 测试小程序端路由...');
      const testId = emojiData.firstEmojiId;
      console.log(`📱 建议在微信开发者工具中测试路由: /pages/detail/detail?id=${testId}`);
    }
    
  } catch (error) {
    console.error('❌ 诊断失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行诊断
testDetailPageDiagnosis().catch(console.error);
