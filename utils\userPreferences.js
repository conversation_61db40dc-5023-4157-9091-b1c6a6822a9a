/**
 * 用户偏好设置管理器
 * 管理用户的个性化设置和偏好
 */

const UserPreferences = {
  // 默认设置
  _defaultSettings: {
    // 显示设置
    display: {
      theme: 'auto', // auto, light, dark
      gridColumns: 2, // 网格列数
      showPreview: true, // 是否显示预览
      animationEnabled: true, // 是否启用动画
      imageQuality: 'high' // low, medium, high
    },
    
    // 下载设置
    download: {
      autoSave: false, // 自动保存到相册
      saveFormat: 'original', // original, jpg, png
      compressionLevel: 80, // 压缩级别
      watermarkEnabled: false, // 是否添加水印
      downloadPath: 'default' // 下载路径
    },
    
    // 搜索设置
    search: {
      saveHistory: true, // 保存搜索历史
      maxHistoryCount: 20, // 最大历史记录数
      autoComplete: true, // 自动完成
      defaultSort: 'relevance', // 默认排序
      safeSearch: true // 安全搜索
    },
    
    // 通知设置
    notification: {
      newContent: true, // 新内容通知
      updates: true, // 更新通知
      recommendations: true, // 推荐通知
      vibration: true, // 震动反馈
      sound: false // 声音提醒
    },
    
    // 隐私设置
    privacy: {
      analytics: true, // 数据分析
      crashReports: true, // 崩溃报告
      personalizedAds: false, // 个性化广告
      locationAccess: false, // 位置访问
      cameraAccess: true // 相机访问
    },
    
    // 性能设置
    performance: {
      preloadImages: true, // 预加载图片
      cacheSize: 100, // 缓存大小(MB)
      autoCleanup: true, // 自动清理
      lowPowerMode: false, // 低功耗模式
      dataUsageLimit: 500 // 数据使用限制(MB)
    }
  },

  // 当前设置
  _currentSettings: {},

  // 设置变更监听器
  _listeners: [],

  // 存储键
  _storageKey: 'user_preferences',

  /**
   * 初始化用户偏好设置
   */
  init() {
    console.log('⚙️ 用户偏好设置管理器初始化')
    
    // 加载设置
    this.loadSettings()
    
    // 应用设置
    this.applySettings()
    
    // 监听系统主题变化
    this.setupThemeListener()
  },

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const savedSettings = wx.getStorageSync(this._storageKey)
      if (savedSettings) {
        // 合并默认设置和保存的设置
        this._currentSettings = this.mergeSettings(this._defaultSettings, savedSettings)
        console.log('📱 用户设置加载完成')
      } else {
        // 使用默认设置
        this._currentSettings = JSON.parse(JSON.stringify(this._defaultSettings))
        console.log('📱 使用默认设置')
      }
    } catch (error) {
      console.error('❌ 设置加载失败:', error)
      this._currentSettings = JSON.parse(JSON.stringify(this._defaultSettings))
    }
  },

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      wx.setStorageSync(this._storageKey, this._currentSettings)
      console.log('💾 用户设置保存完成')
    } catch (error) {
      console.error('❌ 设置保存失败:', error)
    }
  },

  /**
   * 合并设置
   * @param {Object} defaultSettings - 默认设置
   * @param {Object} userSettings - 用户设置
   * @returns {Object} 合并后的设置
   */
  mergeSettings(defaultSettings, userSettings) {
    const merged = {}
    
    for (const [category, defaultValues] of Object.entries(defaultSettings)) {
      merged[category] = {
        ...defaultValues,
        ...(userSettings[category] || {})
      }
    }
    
    return merged
  },

  /**
   * 获取设置值
   * @param {string} path - 设置路径，如 'display.theme'
   * @returns {*} 设置值
   */
  get(path) {
    const keys = path.split('.')
    let value = this._currentSettings
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return undefined
      }
    }
    
    return value
  },

  /**
   * 设置值
   * @param {string} path - 设置路径
   * @param {*} value - 设置值
   */
  set(path, value) {
    const keys = path.split('.')
    const lastKey = keys.pop()
    let target = this._currentSettings
    
    // 创建嵌套对象路径
    for (const key of keys) {
      if (!target[key] || typeof target[key] !== 'object') {
        target[key] = {}
      }
      target = target[key]
    }
    
    // 设置值
    const oldValue = target[lastKey]
    target[lastKey] = value
    
    // 保存设置
    this.saveSettings()
    
    // 通知监听器
    this.notifyListeners(path, value, oldValue)
    
    // 应用设置
    this.applySetting(path, value)
    
    console.log(`⚙️ 设置更新: ${path} = ${value}`)
  },

  /**
   * 获取整个分类的设置
   * @param {string} category - 分类名称
   * @returns {Object} 分类设置
   */
  getCategory(category) {
    return this._currentSettings[category] || {}
  },

  /**
   * 设置整个分类
   * @param {string} category - 分类名称
   * @param {Object} settings - 分类设置
   */
  setCategory(category, settings) {
    const oldSettings = this._currentSettings[category]
    this._currentSettings[category] = { ...settings }
    
    // 保存设置
    this.saveSettings()
    
    // 通知监听器
    this.notifyListeners(category, settings, oldSettings)
    
    // 应用设置
    this.applyCategory(category, settings)
    
    console.log(`⚙️ 分类设置更新: ${category}`)
  },

  /**
   * 重置设置
   * @param {string} category - 分类名称（可选）
   */
  reset(category = null) {
    if (category) {
      // 重置指定分类
      this._currentSettings[category] = JSON.parse(JSON.stringify(this._defaultSettings[category]))
      this.applyCategory(category, this._currentSettings[category])
    } else {
      // 重置所有设置
      this._currentSettings = JSON.parse(JSON.stringify(this._defaultSettings))
      this.applySettings()
    }
    
    this.saveSettings()
    console.log(`🔄 设置重置: ${category || '全部'}`)
  },

  /**
   * 应用所有设置
   */
  applySettings() {
    for (const [category, settings] of Object.entries(this._currentSettings)) {
      this.applyCategory(category, settings)
    }
  },

  /**
   * 应用分类设置
   * @param {string} category - 分类名称
   * @param {Object} settings - 设置对象
   */
  applyCategory(category, settings) {
    switch (category) {
      case 'display':
        this.applyDisplaySettings(settings)
        break
      case 'performance':
        this.applyPerformanceSettings(settings)
        break
      case 'notification':
        this.applyNotificationSettings(settings)
        break
    }
  },

  /**
   * 应用单个设置
   * @param {string} path - 设置路径
   * @param {*} value - 设置值
   */
  applySetting(path, value) {
    const [category, setting] = path.split('.')
    
    switch (path) {
      case 'display.theme':
        this.applyTheme(value)
        break
      case 'display.animationEnabled':
        this.applyAnimationSetting(value)
        break
      case 'performance.preloadImages':
        this.applyPreloadSetting(value)
        break
    }
  },

  /**
   * 应用显示设置
   */
  applyDisplaySettings(settings) {
    this.applyTheme(settings.theme)
    this.applyAnimationSetting(settings.animationEnabled)
  },

  /**
   * 应用性能设置
   */
  applyPerformanceSettings(settings) {
    this.applyPreloadSetting(settings.preloadImages)
    this.applyCacheSetting(settings.cacheSize)
  },

  /**
   * 应用通知设置
   */
  applyNotificationSettings(settings) {
    // 这里可以配置通知相关的设置
    console.log('🔔 通知设置已应用')
  },

  /**
   * 应用主题设置
   */
  applyTheme(theme) {
    // 这里可以实现主题切换逻辑
    console.log(`🎨 主题设置: ${theme}`)
  },

  /**
   * 应用动画设置
   */
  applyAnimationSetting(enabled) {
    // 这里可以控制全局动画开关
    console.log(`🎬 动画设置: ${enabled}`)
  },

  /**
   * 应用预加载设置
   */
  applyPreloadSetting(enabled) {
    try {
      const { LazyImageLoader } = require('./lazyImageLoader.js')
      if (LazyImageLoader && LazyImageLoader._options) {
        LazyImageLoader._options.preloadCount = enabled ? 8 : 0
      }
    } catch (error) {
      console.warn('⚠️ 预加载设置应用失败:', error)
    }
  },

  /**
   * 应用缓存设置
   */
  applyCacheSetting(cacheSize) {
    try {
      const { LazyImageLoader } = require('./lazyImageLoader.js')
      if (LazyImageLoader && LazyImageLoader._options) {
        LazyImageLoader._options.maxCacheSize = cacheSize * 2 // 转换为图片数量
      }
    } catch (error) {
      console.warn('⚠️ 缓存设置应用失败:', error)
    }
  },

  /**
   * 设置主题监听器
   */
  setupThemeListener() {
    // 监听系统主题变化
    if (typeof wx !== 'undefined' && wx.onThemeChange) {
      wx.onThemeChange((res) => {
        if (this.get('display.theme') === 'auto') {
          console.log('🌓 系统主题变化:', res.theme)
          this.applyTheme(res.theme)
        }
      })
    }
  },

  /**
   * 添加设置变更监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this._listeners.push(listener)
  },

  /**
   * 移除设置变更监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this._listeners.indexOf(listener)
    if (index > -1) {
      this._listeners.splice(index, 1)
    }
  },

  /**
   * 通知监听器
   * @param {string} path - 设置路径
   * @param {*} newValue - 新值
   * @param {*} oldValue - 旧值
   */
  notifyListeners(path, newValue, oldValue) {
    this._listeners.forEach(listener => {
      try {
        listener(path, newValue, oldValue)
      } catch (error) {
        console.error('❌ 设置监听器执行失败:', error)
      }
    })
  },

  /**
   * 获取所有设置
   */
  getAllSettings() {
    return JSON.parse(JSON.stringify(this._currentSettings))
  },

  /**
   * 导出设置
   */
  exportSettings() {
    return {
      version: '1.0.0',
      timestamp: Date.now(),
      settings: this.getAllSettings()
    }
  },

  /**
   * 导入设置
   * @param {Object} data - 导入的设置数据
   */
  importSettings(data) {
    try {
      if (data && data.settings) {
        this._currentSettings = this.mergeSettings(this._defaultSettings, data.settings)
        this.saveSettings()
        this.applySettings()
        console.log('📥 设置导入完成')
        return true
      }
    } catch (error) {
      console.error('❌ 设置导入失败:', error)
    }
    return false
  }
}

module.exports = UserPreferences
