// 简化版数据管理器 - 直接从后端获取数据，不使用降级逻辑
// 在微信开发者工具控制台中运行

console.log('📦 加载简化版数据管理器...')

const SimpleDataManager = {
  // 直接调用云函数，不使用降级逻辑
  async callCloudFunction(name, data) {
    console.log(`☁️ 直接调用云函数: ${name}`, data)
    
    try {
      const result = await wx.cloud.callFunction({ name, data })
      console.log(`✅ 云函数调用成功: ${name}`, result.result)
      return result.result
    } catch (error) {
      console.error(`❌ 云函数调用失败: ${name}`, error)
      throw error
    }
  },

  // 获取分类数据
  async getCategories() {
    return await this.callCloudFunction('dataAPI', { action: 'getCategories' })
  },

  // 获取表情包数据
  async getEmojis(category = 'all', page = 1, limit = 20) {
    return await this.callCloudFunction('dataAPI', { 
      action: 'getEmojis',
      data: { category, page, limit }
    })
  },

  // 获取轮播图数据
  async getBanners() {
    return await this.callCloudFunction('dataAPI', { action: 'getBanners' })
  },

  // 初始化测试数据
  async initTestData() {
    return await this.callCloudFunction('dataAPI', { action: 'initTestData' })
  },

  // 获取所有数据
  async getAllData() {
    console.log('🔄 获取所有后端数据...')
    
    try {
      const [categories, emojis, banners] = await Promise.all([
        this.getCategories(),
        this.getEmojis('all', 1, 20),
        this.getBanners()
      ])
      
      const result = {
        categories: categories.data || [],
        emojis: emojis.data || [],
        banners: banners.data || []
      }
      
      console.log('✅ 所有数据获取成功:', {
        categories: result.categories.length,
        emojis: result.emojis.length,
        banners: result.banners.length
      })
      
      return result
    } catch (error) {
      console.error('❌ 获取数据失败:', error)
      throw error
    }
  },

  // 更新首页数据
  async updateIndexPageData() {
    console.log('🔄 更新首页数据...')
    
    try {
      const data = await this.getAllData()
      
      // 获取当前页面实例
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      
      if (currentPage && currentPage.route === 'pages/index/index') {
        console.log('📄 找到首页，更新数据...')
        
        // 直接设置页面数据
        currentPage.setData({
          categories: data.categories,
          emojiList: data.emojis,
          bannerList: data.banners,
          loading: false,
          showEmptyState: false
        })
        
        console.log('✅ 首页数据更新完成')
        console.log('📊 页面数据状态:', {
          categories: currentPage.data.categories?.length || 0,
          emojiList: currentPage.data.emojiList?.length || 0,
          bannerList: currentPage.data.bannerList?.length || 0
        })
      } else {
        console.log('⚠️ 当前不在首页，无法更新页面数据')
      }
      
      return data
    } catch (error) {
      console.error('❌ 更新首页数据失败:', error)
      throw error
    }
  }
}

// 测试简化版数据管理器
const testSimpleDataManager = async () => {
  console.log('🧪 测试简化版数据管理器...\n')
  
  try {
    // 1. 测试云函数连接
    console.log('=== 测试1: 云函数连接 ===')
    const categories = await SimpleDataManager.getCategories()
    console.log('分类数据:', categories)
    
    // 2. 测试获取所有数据
    console.log('\n=== 测试2: 获取所有数据 ===')
    const allData = await SimpleDataManager.getAllData()
    console.log('所有数据:', allData)
    
    // 3. 更新首页数据
    console.log('\n=== 测试3: 更新首页数据 ===')
    await SimpleDataManager.updateIndexPageData()
    
    console.log('\n🎉 简化版数据管理器测试完成！')
    console.log('📋 请检查首页是否显示了后端的真实数据')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 导出到全局
window.SimpleDataManager = SimpleDataManager
window.testSimpleDataManager = testSimpleDataManager

console.log('✅ 简化版数据管理器加载完成')
console.log('🚀 运行 testSimpleDataManager() 开始测试')

// 自动运行测试
testSimpleDataManager()
