// 性能测试
const { describe, test, expect, beforeAll, afterAll } = require('@jest/globals');

// 性能测试工具类
class PerformanceTester {
  constructor() {
    this.metrics = {
      responseTime: [],
      throughput: [],
      memoryUsage: [],
      cpuUsage: [],
      errorRate: []
    };
  }

  // 测量响应时间
  async measureResponseTime(operation, iterations = 100) {
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = process.hrtime.bigint();
      
      try {
        await operation();
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒
        times.push(duration);
      } catch (error) {
        // 记录错误但继续测试
        console.warn(`操作失败 (第${i + 1}次):`, error.message);
      }
    }

    return {
      average: times.reduce((sum, time) => sum + time, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      p95: this.percentile(times, 95),
      p99: this.percentile(times, 99),
      samples: times.length
    };
  }

  // 测量吞吐量
  async measureThroughput(operation, duration = 10000) {
    const startTime = Date.now();
    let operationCount = 0;
    let errorCount = 0;

    while (Date.now() - startTime < duration) {
      try {
        await operation();
        operationCount++;
      } catch (error) {
        errorCount++;
      }
    }

    const actualDuration = Date.now() - startTime;
    const throughput = (operationCount / actualDuration) * 1000; // 每秒操作数

    return {
      operationsPerSecond: throughput,
      totalOperations: operationCount,
      totalErrors: errorCount,
      errorRate: errorCount / (operationCount + errorCount),
      duration: actualDuration
    };
  }

  // 测量内存使用
  measureMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      return {
        heapUsed: usage.heapUsed / 1024 / 1024, // MB
        heapTotal: usage.heapTotal / 1024 / 1024, // MB
        external: usage.external / 1024 / 1024, // MB
        rss: usage.rss / 1024 / 1024 // MB
      };
    }
    return null;
  }

  // 计算百分位数
  percentile(values, p) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }

  // 模拟云函数操作
  async simulateCloudFunction(complexity = 'simple') {
    const delays = {
      simple: () => Math.random() * 50 + 10,    // 10-60ms
      medium: () => Math.random() * 200 + 50,   // 50-250ms
      complex: () => Math.random() * 500 + 100  // 100-600ms
    };

    const delay = delays[complexity]();
    
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟偶发错误
        if (Math.random() < 0.02) { // 2%错误率
          reject(new Error('模拟云函数错误'));
        } else {
          resolve({ success: true, data: { id: Date.now() } });
        }
      }, delay);
    });
  }

  // 模拟数据库操作
  async simulateDatabaseOperation(type = 'read') {
    const delays = {
      read: () => Math.random() * 30 + 5,     // 5-35ms
      write: () => Math.random() * 100 + 20,  // 20-120ms
      update: () => Math.random() * 80 + 15,  // 15-95ms
      delete: () => Math.random() * 60 + 10   // 10-70ms
    };

    const delay = delays[type]();
    
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() < 0.01) { // 1%错误率
          reject(new Error('模拟数据库错误'));
        } else {
          resolve({ success: true, affected: 1 });
        }
      }, delay);
    });
  }

  // 模拟实时同步操作
  async simulateRealtimeSync() {
    const delay = Math.random() * 20 + 5; // 5-25ms
    
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ 
          success: true, 
          syncDelay: delay,
          timestamp: Date.now() 
        });
      }, delay);
    });
  }

  // 生成性能报告
  generateReport(testResults) {
    return {
      summary: {
        totalTests: Object.keys(testResults).length,
        timestamp: new Date().toISOString(),
        environment: 'test'
      },
      results: testResults,
      recommendations: this.generateRecommendations(testResults)
    };
  }

  // 生成性能建议
  generateRecommendations(results) {
    const recommendations = [];

    // 检查响应时间
    Object.entries(results).forEach(([testName, result]) => {
      if (result.responseTime) {
        if (result.responseTime.average > 1000) {
          recommendations.push({
            type: 'warning',
            test: testName,
            message: `平均响应时间过高: ${result.responseTime.average.toFixed(2)}ms`,
            suggestion: '考虑优化算法或增加缓存'
          });
        }

        if (result.responseTime.p99 > 3000) {
          recommendations.push({
            type: 'critical',
            test: testName,
            message: `P99响应时间过高: ${result.responseTime.p99.toFixed(2)}ms`,
            suggestion: '需要优化最坏情况下的性能'
          });
        }
      }

      // 检查吞吐量
      if (result.throughput) {
        if (result.throughput.operationsPerSecond < 10) {
          recommendations.push({
            type: 'warning',
            test: testName,
            message: `吞吐量过低: ${result.throughput.operationsPerSecond.toFixed(2)} ops/sec`,
            suggestion: '考虑并发优化或资源扩容'
          });
        }

        if (result.throughput.errorRate > 0.05) {
          recommendations.push({
            type: 'critical',
            test: testName,
            message: `错误率过高: ${(result.throughput.errorRate * 100).toFixed(2)}%`,
            suggestion: '需要改进错误处理和系统稳定性'
          });
        }
      }
    });

    return recommendations;
  }
}

describe('性能测试', () => {
  let perfTester;

  beforeAll(() => {
    perfTester = new PerformanceTester();
  });

  describe('T5.1 云函数性能测试', () => {
    test('T5.1.1 loginAPI响应时间测试', async () => {
      const result = await perfTester.measureResponseTime(
        () => perfTester.simulateCloudFunction('simple'),
        50
      );

      expect(result.average).toBeLessThan(200); // 平均响应时间小于200ms
      expect(result.p95).toBeLessThan(300);     // P95小于300ms
      expect(result.p99).toBeLessThan(500);     // P99小于500ms

      console.log('loginAPI性能指标:', result);
    }, 30000);

    test('T5.1.2 webAdminAPI吞吐量测试', async () => {
      const result = await perfTester.measureThroughput(
        () => perfTester.simulateCloudFunction('medium'),
        5000 // 5秒测试
      );

      expect(result.operationsPerSecond).toBeGreaterThan(5); // 每秒至少5次操作
      expect(result.errorRate).toBeLessThan(0.05);           // 错误率小于5%

      console.log('webAdminAPI吞吐量指标:', result);
    }, 10000);

    test('T5.1.3 dataAPI并发性能测试', async () => {
      const concurrentOperations = Array.from({ length: 20 }, () =>
        perfTester.simulateCloudFunction('simple')
      );

      const startTime = Date.now();
      const results = await Promise.allSettled(concurrentOperations);
      const duration = Date.now() - startTime;

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const successRate = successCount / results.length;

      expect(duration).toBeLessThan(1000);  // 20个并发操作在1秒内完成
      expect(successRate).toBeGreaterThan(0.9); // 成功率大于90%

      console.log('dataAPI并发性能:', {
        duration,
        successRate,
        successCount,
        totalOperations: results.length
      });
    }, 15000);
  });

  describe('T5.2 数据库性能测试', () => {
    test('T5.2.1 数据库读操作性能', async () => {
      const result = await perfTester.measureResponseTime(
        () => perfTester.simulateDatabaseOperation('read'),
        100
      );

      expect(result.average).toBeLessThan(50);  // 平均读取时间小于50ms
      expect(result.p95).toBeLessThan(100);     // P95小于100ms

      console.log('数据库读操作性能:', result);
    }, 20000);

    test('T5.2.2 数据库写操作性能', async () => {
      const result = await perfTester.measureResponseTime(
        () => perfTester.simulateDatabaseOperation('write'),
        50
      );

      expect(result.average).toBeLessThan(150); // 平均写入时间小于150ms
      expect(result.p95).toBeLessThan(300);     // P95小于300ms

      console.log('数据库写操作性能:', result);
    }, 15000);

    test('T5.2.3 数据库事务性能', async () => {
      // 模拟事务操作（包含多个数据库操作）
      const transactionOperation = async () => {
        await perfTester.simulateDatabaseOperation('read');
        await perfTester.simulateDatabaseOperation('write');
        await perfTester.simulateDatabaseOperation('update');
      };

      const result = await perfTester.measureResponseTime(transactionOperation, 30);

      expect(result.average).toBeLessThan(500); // 平均事务时间小于500ms
      expect(result.p99).toBeLessThan(1000);    // P99小于1秒

      console.log('数据库事务性能:', result);
    }, 20000);
  });

  describe('T5.3 实时同步性能测试', () => {
    test('T5.3.1 同步延迟测试', async () => {
      const result = await perfTester.measureResponseTime(
        () => perfTester.simulateRealtimeSync(),
        100
      );

      expect(result.average).toBeLessThan(50);  // 平均同步延迟小于50ms
      expect(result.p95).toBeLessThan(100);     // P95小于100ms
      expect(result.p99).toBeLessThan(200);     // P99小于200ms

      console.log('实时同步延迟:', result);
    }, 15000);

    test('T5.3.2 大量同步事件处理', async () => {
      const result = await perfTester.measureThroughput(
        () => perfTester.simulateRealtimeSync(),
        3000 // 3秒测试
      );

      expect(result.operationsPerSecond).toBeGreaterThan(50); // 每秒至少50次同步
      expect(result.errorRate).toBeLessThan(0.01);            // 错误率小于1%

      console.log('同步事件处理能力:', result);
    }, 10000);

    test('T5.3.3 多客户端同步性能', async () => {
      // 模拟多个客户端同时接收同步事件
      const clientCount = 50;
      const syncOperations = Array.from({ length: clientCount }, () =>
        perfTester.simulateRealtimeSync()
      );

      const startTime = Date.now();
      const results = await Promise.allSettled(syncOperations);
      const duration = Date.now() - startTime;

      const successCount = results.filter(r => r.status === 'fulfilled').length;

      expect(duration).toBeLessThan(500);       // 50个客户端同步在500ms内完成
      expect(successCount).toBe(clientCount);   // 所有客户端都成功同步

      console.log('多客户端同步性能:', {
        clientCount,
        duration,
        successCount,
        averagePerClient: duration / clientCount
      });
    }, 10000);
  });

  describe('T5.4 内存和资源使用测试', () => {
    test('T5.4.1 内存使用监控', async () => {
      const initialMemory = perfTester.measureMemoryUsage();
      
      // 执行一系列操作
      const operations = Array.from({ length: 100 }, () =>
        perfTester.simulateCloudFunction('medium')
      );
      
      await Promise.all(operations);
      
      const finalMemory = perfTester.measureMemoryUsage();

      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
        
        expect(memoryIncrease).toBeLessThan(50); // 内存增长小于50MB
        
        console.log('内存使用情况:', {
          initial: initialMemory,
          final: finalMemory,
          increase: memoryIncrease
        });
      }
    }, 15000);

    test('T5.4.2 长时间运行稳定性', async () => {
      const duration = 5000; // 5秒长时间测试
      const interval = 100;   // 每100ms执行一次操作
      
      const operations = [];
      const startTime = Date.now();
      
      while (Date.now() - startTime < duration) {
        operations.push(perfTester.simulateCloudFunction('simple'));
        await new Promise(resolve => setTimeout(resolve, interval));
      }
      
      const results = await Promise.allSettled(operations);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const successRate = successCount / results.length;
      
      expect(successRate).toBeGreaterThan(0.95); // 长时间运行成功率大于95%
      
      console.log('长时间运行稳定性:', {
        duration,
        totalOperations: results.length,
        successCount,
        successRate
      });
    }, 10000);
  });

  describe('T5.5 性能基准测试', () => {
    test('T5.5.1 系统整体性能基准', async () => {
      const testResults = {};

      // 云函数性能
      testResults.cloudFunction = await perfTester.measureResponseTime(
        () => perfTester.simulateCloudFunction('medium'),
        30
      );

      // 数据库性能
      testResults.database = await perfTester.measureResponseTime(
        () => perfTester.simulateDatabaseOperation('read'),
        30
      );

      // 实时同步性能
      testResults.realtimeSync = await perfTester.measureResponseTime(
        () => perfTester.simulateRealtimeSync(),
        30
      );

      // 生成性能报告
      const report = perfTester.generateReport({
        cloudFunction: { responseTime: testResults.cloudFunction },
        database: { responseTime: testResults.database },
        realtimeSync: { responseTime: testResults.realtimeSync }
      });

      console.log('性能基准报告:', JSON.stringify(report, null, 2));

      // 验证整体性能指标
      expect(testResults.cloudFunction.average).toBeLessThan(300);
      expect(testResults.database.average).toBeLessThan(100);
      expect(testResults.realtimeSync.average).toBeLessThan(50);

      // 验证没有严重的性能问题
      expect(report.recommendations.filter(r => r.type === 'critical')).toHaveLength(0);
    }, 20000);
  });

  afterAll(() => {
    // 输出最终性能总结
    console.log('性能测试完成');
    
    const finalMemory = perfTester.measureMemoryUsage();
    if (finalMemory) {
      console.log('最终内存使用:', finalMemory);
    }
  });
});
