/**
 * 云函数部署脚本
 * 自动检查和部署所有云函数
 */

const fs = require('fs')
const path = require('path')

class CloudFunctionDeployer {
  constructor() {
    this.projectRoot = process.cwd()
    this.cloudFunctionsDir = path.join(this.projectRoot, 'cloudfunctions')
  }

  /**
   * 检查云函数部署状态
   */
  checkDeploymentStatus() {
    console.log('☁️ 检查云函数部署状态...\n')
    
    if (!fs.existsSync(this.cloudFunctionsDir)) {
      console.log('❌ cloudfunctions 目录不存在')
      return false
    }

    const functions = fs.readdirSync(this.cloudFunctionsDir)
      .filter(item => {
        const itemPath = path.join(this.cloudFunctionsDir, item)
        return fs.statSync(itemPath).isDirectory()
      })

    console.log(`📋 发现 ${functions.length} 个云函数:`)
    
    const deploymentStatus = {}
    
    functions.forEach(funcName => {
      const funcDir = path.join(this.cloudFunctionsDir, funcName)
      const indexFile = path.join(funcDir, 'index.js')
      const packageFile = path.join(funcDir, 'package.json')
      const configFile = path.join(funcDir, 'config.json')
      
      const status = {
        hasIndex: fs.existsSync(indexFile),
        hasPackage: fs.existsSync(packageFile),
        hasConfig: fs.existsSync(configFile),
        ready: false
      }
      
      status.ready = status.hasIndex && status.hasPackage
      
      deploymentStatus[funcName] = status
      
      const statusIcon = status.ready ? '✅' : '❌'
      const configIcon = status.hasConfig ? '✅' : '⚠️'
      
      console.log(`   ${statusIcon} ${funcName}`)
      console.log(`      index.js: ${status.hasIndex ? '✅' : '❌'}`)
      console.log(`      package.json: ${status.hasPackage ? '✅' : '❌'}`)
      console.log(`      config.json: ${configIcon}`)
    })
    
    return deploymentStatus
  }

  /**
   * 创建缺失的配置文件
   */
  createMissingConfigs() {
    console.log('\n🔧 创建缺失的配置文件...')
    
    const functions = fs.readdirSync(this.cloudFunctionsDir)
      .filter(item => {
        const itemPath = path.join(this.cloudFunctionsDir, item)
        return fs.statSync(itemPath).isDirectory()
      })

    functions.forEach(funcName => {
      const funcDir = path.join(this.cloudFunctionsDir, funcName)
      const configFile = path.join(funcDir, 'config.json')
      
      if (!fs.existsSync(configFile)) {
        const defaultConfig = {
          permissions: {
            openapi: []
          },
          triggers: [],
          envVariables: {},
          runtime: "Nodejs16.14"
        }
        
        fs.writeFileSync(configFile, JSON.stringify(defaultConfig, null, 2))
        console.log(`   ✅ 创建了 ${funcName}/config.json`)
      }
    })
  }

  /**
   * 生成部署指令
   */
  generateDeploymentInstructions() {
    console.log('\n📋 云函数部署指令:\n')
    
    const deploymentStatus = this.checkDeploymentStatus()
    const readyFunctions = Object.entries(deploymentStatus)
      .filter(([name, status]) => status.ready)
      .map(([name]) => name)

    if (readyFunctions.length === 0) {
      console.log('❌ 没有可部署的云函数')
      return
    }

    console.log('🚀 在微信开发者工具中执行以下操作:')
    console.log('=' * 50)
    
    readyFunctions.forEach((funcName, index) => {
      console.log(`${index + 1}. 右键 cloudfunctions/${funcName}`)
      console.log(`   选择 "上传并部署: 云端安装依赖(不上传node_modules)"`)
      console.log('')
    })
    
    console.log('⚠️ 重要提示:')
    console.log('   - 确保已在微信开发者工具中开通云开发')
    console.log('   - 确保云环境ID配置正确')
    console.log('   - 部署时选择"云端安装依赖"选项')
    console.log('   - 等待每个云函数部署完成后再部署下一个')
    
    return readyFunctions
  }

  /**
   * 验证云函数部署
   */
  generateTestScript() {
    console.log('\n🧪 生成云函数测试脚本...')
    
    const testScript = `
// 在微信开发者工具控制台中运行此脚本来测试云函数

// 测试 dataAPI 云函数
wx.cloud.callFunction({
  name: 'dataAPI',
  data: {
    action: 'ping'
  }
}).then(res => {
  console.log('dataAPI 测试结果:', res)
}).catch(err => {
  console.error('dataAPI 测试失败:', err)
})

// 测试 syncAPI 云函数
wx.cloud.callFunction({
  name: 'syncAPI',
  data: {
    action: 'getVersions'
  }
}).then(res => {
  console.log('syncAPI 测试结果:', res)
}).catch(err => {
  console.error('syncAPI 测试失败:', err)
})

// 测试 login 云函数
wx.cloud.callFunction({
  name: 'login'
}).then(res => {
  console.log('login 测试结果:', res)
}).catch(err => {
  console.error('login 测试失败:', err)
})
`
    
    fs.writeFileSync(path.join(this.projectRoot, 'test-cloud-functions.js'), testScript)
    console.log('   ✅ 已生成 test-cloud-functions.js')
    console.log('   📋 在微信开发者工具控制台中运行此脚本来测试云函数')
  }

  /**
   * 检查数据库配置
   */
  checkDatabaseConfig() {
    console.log('\n🗄️ 检查数据库配置...')
    
    const requiredCollections = [
      'emojis',
      'categories', 
      'banners',
      'users',
      'user_actions',
      'data_versions'
    ]
    
    console.log('📋 需要创建的数据库集合:')
    requiredCollections.forEach((collection, index) => {
      console.log(`   ${index + 1}. ${collection}`)
    })
    
    console.log('\n🔧 数据库配置步骤:')
    console.log('   1. 在微信开发者工具中打开"云开发"控制台')
    console.log('   2. 进入"数据库"页面')
    console.log('   3. 创建上述集合')
    console.log('   4. 配置集合权限 (参考 database/permissions.json)')
    console.log('   5. 运行数据初始化 (调用 dataAPI 的 initTestData 方法)')
  }

  /**
   * 运行完整检查
   */
  runFullCheck() {
    console.log('🔍 云函数部署完整检查\n')
    console.log('=' * 60)
    
    // 1. 检查部署状态
    const deploymentStatus = this.checkDeploymentStatus()
    
    // 2. 创建缺失配置
    this.createMissingConfigs()
    
    // 3. 生成部署指令
    const readyFunctions = this.generateDeploymentInstructions()
    
    // 4. 生成测试脚本
    this.generateTestScript()
    
    // 5. 检查数据库配置
    this.checkDatabaseConfig()
    
    console.log('\n' + '=' * 60)
    console.log('🎯 下一步操作总结:')
    console.log('=' * 60)
    console.log('1. 📤 部署云函数 (按照上述指令)')
    console.log('2. 🗄️ 创建数据库集合')
    console.log('3. 🧪 运行测试脚本验证')
    console.log('4. 🚀 启动小程序测试')
    console.log('=' * 60)
    
    return {
      deploymentStatus,
      readyFunctions: readyFunctions || [],
      totalFunctions: Object.keys(deploymentStatus).length
    }
  }
}

// 运行检查
if (require.main === module) {
  const deployer = new CloudFunctionDeployer()
  deployer.runFullCheck()
}

module.exports = CloudFunctionDeployer
