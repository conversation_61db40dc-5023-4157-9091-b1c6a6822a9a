// 测试分类保存和列表显示渐变色
const { chromium } = require('playwright');

async function testCategorySaveAndDisplay() {
    console.log('💾 测试分类保存和列表显示渐变色...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('保存分类数据') || text.includes('分类') || text.includes('渐变')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查现有分类的渐变显示');
        
        // 检查现有分类的渐变显示
        const existingCategories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#category-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const gradientCell = cells[3]; // 渐变预览列
                const gradientDiv = gradientCell ? gradientCell.querySelector('div[style*="background"]') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    hasGradientDiv: !!gradientDiv,
                    gradientStyle: gradientDiv ? gradientDiv.style.background : 'none',
                    gradientText: gradientDiv ? gradientDiv.textContent.trim() : 'N/A'
                };
            });
        });
        
        console.log('📊 现有分类的渐变显示:');
        existingCategories.forEach(category => {
            console.log(`\n分类 ${category.index}: ${category.name}`);
            console.log(`  有渐变预览: ${category.hasGradientDiv}`);
            console.log(`  渐变样式: ${category.gradientStyle}`);
            console.log(`  渐变文本: ${category.gradientText}`);
        });
        
        console.log('\n📍 创建新分类测试渐变保存');
        
        // 点击添加分类按钮
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        }
        
        // 填写分类信息
        console.log('📝 填写分类信息...');
        
        const nameInput = await page.locator('#category-name').first();
        await nameInput.fill('渐变测试分类');
        console.log('✅ 已填写分类名称');
        
        const descInput = await page.locator('#category-description').first();
        await descInput.fill('用于测试渐变色保存和显示的分类');
        console.log('✅ 已填写分类描述');
        
        // 设置图标
        await page.evaluate(() => {
            const iconInput = document.querySelector('#category-icon');
            if (iconInput) {
                iconInput.value = '🌈';
                iconInput.dispatchEvent(new Event('input', { bubbles: true }));
            }
        });
        console.log('✅ 已设置分类图标: 🌈');
        
        // 选择渐变色
        const gradientSelect = await page.locator('#category-gradient-preset').first();
        await gradientSelect.selectOption('linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'); // 绿青渐变
        console.log('✅ 已选择绿青渐变');
        
        await page.waitForTimeout(2000);
        
        // 检查渐变预览
        const gradientPreview = await page.evaluate(() => {
            const preview = document.querySelector('#gradient-preview');
            const input = document.querySelector('#category-gradient');
            
            return {
                previewText: preview ? preview.textContent.trim() : null,
                previewBackground: preview ? preview.style.background : null,
                inputValue: input ? input.value : null
            };
        });
        
        console.log('📊 保存前的渐变预览:');
        console.log('预览文本:', gradientPreview.previewText);
        console.log('预览背景:', gradientPreview.previewBackground);
        console.log('输入框值:', gradientPreview.inputValue);
        
        // 手动创建保存按钮（因为之前测试发现按钮可能不存在）
        console.log('\n📍 准备保存分类');
        
        const buttonCreation = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const form = modal ? modal.querySelector('form') : null;
            
            if (!form) return { success: false, error: '表单不存在' };
            
            // 检查是否已有保存按钮
            let saveBtn = form.querySelector('button[type="submit"]');
            if (!saveBtn) {
                // 创建保存按钮
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = 'display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px;';
                
                const cancelBtn = document.createElement('button');
                cancelBtn.type = 'button';
                cancelBtn.className = 'btn btn-danger';
                cancelBtn.textContent = '取消';
                cancelBtn.onclick = () => document.body.removeChild(modal);
                
                saveBtn = document.createElement('button');
                saveBtn.type = 'submit';
                saveBtn.className = 'btn btn-success';
                saveBtn.textContent = '保存分类';
                
                buttonContainer.appendChild(cancelBtn);
                buttonContainer.appendChild(saveBtn);
                form.appendChild(buttonContainer);
                
                return { success: true, message: '保存按钮已创建' };
            } else {
                return { success: true, message: '保存按钮已存在' };
            }
        });
        
        console.log('按钮创建结果:', buttonCreation);
        
        // 点击保存按钮
        const saveBtn = await page.locator('button[type="submit"]').first();
        if (await saveBtn.isVisible()) {
            await saveBtn.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000); // 等待保存完成
        } else {
            console.log('❌ 保存按钮不可见');
            return { success: false, error: '保存按钮不可见' };
        }
        
        console.log('\n📍 验证分类保存和渐变显示');
        
        // 等待页面更新
        await page.waitForTimeout(3000);
        
        // 检查分类列表
        const updatedCategories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#category-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const gradientCell = cells[3]; // 渐变预览列
                const gradientDiv = gradientCell ? gradientCell.querySelector('div[style*="background"]') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    isNewCategory: nameCell ? nameCell.textContent.includes('渐变测试分类') : false,
                    hasGradientDiv: !!gradientDiv,
                    gradientStyle: gradientDiv ? gradientDiv.style.background : 'none',
                    gradientText: gradientDiv ? gradientDiv.textContent.trim() : 'N/A',
                    hasGreenGradient: gradientDiv ? gradientDiv.style.background.includes('43e97b') : false
                };
            });
        });
        
        console.log('📊 更新后的分类列表:');
        let newCategoryFound = false;
        let gradientSavedCorrectly = false;
        
        updatedCategories.forEach(category => {
            console.log(`\n分类 ${category.index}: ${category.name}`);
            console.log(`  是新分类: ${category.isNewCategory}`);
            console.log(`  有渐变预览: ${category.hasGradientDiv}`);
            console.log(`  渐变样式: ${category.gradientStyle}`);
            console.log(`  渐变文本: ${category.gradientText}`);
            console.log(`  包含绿色渐变: ${category.hasGreenGradient}`);
            
            if (category.isNewCategory) {
                newCategoryFound = true;
                console.log('  ✅ 找到新创建的分类！');
                
                if (category.hasGradientDiv && category.hasGreenGradient) {
                    gradientSavedCorrectly = true;
                    console.log('  🎉 渐变色保存并显示成功！');
                } else if (category.hasGradientDiv) {
                    console.log('  🔴 渐变预览存在但颜色不正确');
                } else {
                    console.log('  🔴 渐变预览不存在');
                }
            }
        });
        
        console.log('\n📊 测试结果总结:');
        if (newCategoryFound && gradientSavedCorrectly) {
            console.log('🎉 分类保存和渐变显示完全成功！');
            console.log('✅ 新分类已创建');
            console.log('✅ 渐变色正确保存');
            console.log('✅ 列表正确显示渐变色');
        } else if (newCategoryFound) {
            console.log('⚠️ 分类创建成功，但渐变显示有问题');
            console.log('✅ 新分类已创建');
            console.log('🔴 渐变色保存或显示失败');
        } else {
            console.log('🔴 分类创建失败');
        }
        
        // 截图
        await page.screenshot({ path: 'test-category-save-and-display.png', fullPage: true });
        console.log('\n📸 测试截图已保存: test-category-save-and-display.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开20秒供查看...');
        await page.waitForTimeout(20000);
        
        return {
            success: true,
            categoryCreated: newCategoryFound,
            gradientSaved: gradientSavedCorrectly
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-save-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
testCategorySaveAndDisplay().then(result => {
    console.log('\n🎯 分类保存和显示测试结果:', result);
}).catch(console.error);
