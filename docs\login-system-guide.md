# 微信小程序登录系统使用指南

## 🎯 功能概述

本登录系统为微信小程序提供了完整的用户认证和数据同步功能，包括：

- ✅ 微信授权登录流程
- ✅ 用户信息获取和存储
- ✅ 登录状态管理和持久化
- ✅ 自动登录和状态检查
- ✅ 用户数据云端同步
- ✅ 登录状态检查中间件
- ✅ 错误处理和重试机制
- ✅ 完整的测试工具

## 📁 文件结构

```
utils/
├── authManager.js      # 用户认证管理器（核心）
├── dataSync.js         # 用户数据云端同步
├── loginMiddleware.js  # 登录状态检查中间件
└── loginTest.js        # 登录功能测试工具

components/
└── login-modal/        # 登录弹窗组件
    ├── login-modal.js
    ├── login-modal.wxml
    ├── login-modal.wxss
    └── login-modal.json

cloudfunctions/
├── login/              # 用户登录云函数
└── dataSync/           # 数据同步云函数
```

## 🚀 快速开始

### 1. 基本配置

在 `app.js` 中配置云环境ID：

```javascript
wx.cloud.init({
  env: 'your-env-id', // 替换为你的云环境ID
  traceUser: true
})
```

### 2. 使用登录功能

```javascript
const { AuthManager } = require('./utils/authManager.js')

// 检查登录状态
const isLoggedIn = AuthManager.checkLoginStatus()

// 执行登录
const result = await AuthManager.login()
if (result.success) {
  console.log('登录成功:', result.userInfo.nickName)
} else {
  console.log('登录失败:', result.error)
}

// 获取当前用户信息
const currentUser = AuthManager.getCurrentUser()
```

### 3. 使用登录弹窗组件

在页面JSON中注册组件：

```json
{
  "usingComponents": {
    "login-modal": "../../components/login-modal/login-modal"
  }
}
```

在WXML中使用：

```xml
<login-modal 
  visible="{{showLoginModal}}" 
  bind:close="onLoginModalClose"
  bind:success="onLoginSuccess"
></login-modal>
```

### 4. 使用登录中间件

```javascript
const { LoginMiddleware } = require('./utils/loginMiddleware.js')

// 检查点赞功能权限
const canLike = await LoginMiddleware.requireLoginForLike()

// 检查多个功能权限
const permissions = await LoginMiddleware.checkMultipleFeatures(['like', 'collect'])
```

## 🔧 核心API

### AuthManager

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `init()` | 初始化认证管理器 | void |
| `login()` | 执行微信登录 | Promise<{success, userInfo, openid, error}> |
| `logout()` | 退出登录 | void |
| `checkLoginStatus()` | 检查登录状态 | boolean |
| `getCurrentUser()` | 获取当前用户信息 | {isLoggedIn, userInfo, openid} |
| `requireLogin()` | 检查是否需要登录 | Promise<boolean> |

### DataSync

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `init()` | 初始化数据同步 | void |
| `syncLocalDataToCloud()` | 同步本地数据到云端 | Promise<void> |
| `syncUserAction()` | 同步用户操作 | Promise<void> |
| `getSyncStatus()` | 获取同步状态 | {isSyncing, queueLength, isLoggedIn} |

### LoginMiddleware

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `requireLogin()` | 通用登录检查 | Promise<boolean> |
| `requireLoginForLike()` | 点赞功能登录检查 | Promise<boolean> |
| `requireLoginForCollect()` | 收藏功能登录检查 | Promise<boolean> |
| `checkFeatureAccess()` | 检查功能访问权限 | Promise<boolean> |

## 🧪 测试功能

系统提供了完整的测试工具，可以在个人中心页面使用：

1. **测试登录** - 测试基本登录流程
2. **完整测试** - 运行完整测试套件
3. **清理测试** - 清理所有测试数据

或者在代码中使用：

```javascript
const { LoginTest } = require('./utils/loginTest.js')

// 运行完整测试
const results = await LoginTest.runFullTest()

// 清理测试数据
LoginTest.cleanupTestData()
```

## 🔐 数据安全

### 本地存储

- `userInfo` - 用户基本信息
- `openid` - 用户唯一标识
- `loginTime` - 登录时间戳
- `likedEmojis` - 点赞记录
- `collectedEmojis` - 收藏记录
- `downloadedEmojis` - 下载记录

### 云端数据库

- `users` - 用户信息表
- `user_likes` - 用户点赞记录
- `user_collections` - 用户收藏记录
- `user_downloads` - 用户下载记录

## ⚠️ 注意事项

1. **云环境配置** - 确保在app.js中正确配置云环境ID
2. **云函数部署** - 需要部署login和dataSync云函数
3. **数据库权限** - 确保数据库权限配置正确
4. **网络处理** - 系统已内置网络错误处理和重试机制
5. **登录过期** - 登录状态7天后自动过期

## 🐛 常见问题

### Q: 登录失败怎么办？
A: 系统会自动重试3次，如果仍然失败，请检查网络连接和云函数配置。

### Q: 数据同步失败怎么办？
A: 数据同步采用队列机制，失败的操作会自动重试，最多重试3次。

### Q: 如何自定义登录弹窗？
A: 可以修改 `components/login-modal` 组件的样式和内容。

### Q: 如何添加新的用户操作同步？
A: 在 `dataSync.js` 中添加新的同步方法，并在云函数中添加对应处理逻辑。

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台日志输出
2. 运行完整测试套件检查功能状态
3. 检查云函数和数据库配置
4. 参考测试代码了解正确用法

---

*本登录系统遵循微信小程序开发规范，确保用户数据安全和隐私保护。*
