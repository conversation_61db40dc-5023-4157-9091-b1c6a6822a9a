# V1.0 运维文档

## 运维概述

本文档提供V1.0系统的日常运维指南，包括监控、维护、故障处理等操作说明。

## 系统架构

### 核心组件

- **云函数**: loginAPI, webAdminAPI, dataAPI
- **云数据库**: categories, emojis, banners, sync_notifications, admin_logs
- **静态网站**: 管理后台
- **实时同步**: CloudBase Watch

### 数据流向

```
小程序端 ←→ dataAPI ←→ 云数据库
管理后台 ←→ webAdminAPI ←→ 云数据库
管理后台 ←→ loginAPI ←→ JWT认证
```

## 监控指标

### 1. 云函数监控

#### 关键指标
- **调用次数**: 每分钟调用量
- **错误率**: 错误调用占比
- **响应时间**: 平均响应时间
- **内存使用**: 内存使用率
- **并发数**: 同时执行的函数实例数

#### 告警阈值
```javascript
{
  "error_rate": "> 5%",
  "response_time": "> 3000ms",
  "memory_usage": "> 80%",
  "concurrent_executions": "> 100"
}
```

#### 监控命令
```bash
# 查看函数调用统计
tcb fn stat loginAPI --env your-env-id --start 2023-01-01 --end 2023-01-02

# 查看实时日志
tcb fn log loginAPI --env your-env-id --tail

# 查看函数配置
tcb fn detail loginAPI --env your-env-id
```

### 2. 数据库监控

#### 关键指标
- **读写QPS**: 每秒查询数
- **存储容量**: 数据库大小
- **连接数**: 活跃连接数
- **慢查询**: 执行时间超过1秒的查询

#### 监控脚本
```javascript
// 数据库健康检查
async function checkDatabaseHealth() {
  const db = cloud.database();
  
  // 检查各集合数据量
  const collections = ['categories', 'emojis', 'banners'];
  const stats = {};
  
  for (const collection of collections) {
    const count = await db.collection(collection).count();
    stats[collection] = count.total;
  }
  
  console.log('数据库统计:', stats);
  return stats;
}
```

### 3. 业务监控

#### 关键指标
- **用户活跃度**: DAU/MAU
- **API调用分布**: 各接口调用量
- **缓存命中率**: 缓存效果
- **同步延迟**: 实时同步延迟

#### 业务指标查询
```javascript
// 获取业务统计
async function getBusinessMetrics() {
  const db = cloud.database();
  
  // 今日新增表情包
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const newEmojis = await db.collection('emojis')
    .where({
      createTime: db.command.gte(today)
    })
    .count();
  
  // 热门表情包
  const popularEmojis = await db.collection('emojis')
    .orderBy('downloads', 'desc')
    .limit(10)
    .get();
  
  return {
    newEmojisToday: newEmojis.total,
    popularEmojis: popularEmojis.data
  };
}
```

## 日常维护

### 1. 数据库维护

#### 数据清理
```bash
# 清理过期的同步通知（保留7天）
const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

db.collection('sync_notifications')
  .where({
    timestamp: db.command.lt(sevenDaysAgo.toISOString())
  })
  .remove();
```

#### 索引优化
```bash
# 检查索引使用情况
# 在云开发控制台查看慢查询日志
# 根据查询模式优化索引
```

#### 数据备份
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
BACKUP_DIR="/backup/$DATE"

mkdir -p $BACKUP_DIR

# 导出各集合数据
tcb db export --env your-env-id --collection categories --file "$BACKUP_DIR/categories.json"
tcb db export --env your-env-id --collection emojis --file "$BACKUP_DIR/emojis.json"
tcb db export --env your-env-id --collection banners --file "$BACKUP_DIR/banners.json"

# 压缩备份文件
tar -czf "$BACKUP_DIR.tar.gz" $BACKUP_DIR
rm -rf $BACKUP_DIR

echo "备份完成: $BACKUP_DIR.tar.gz"
```

### 2. 云函数维护

#### 性能优化
```javascript
// 云函数性能监控
class FunctionMonitor {
  static recordMetrics(functionName, startTime, success) {
    const duration = Date.now() - startTime;
    
    console.log(`[METRICS] ${functionName}:`, {
      duration,
      success,
      timestamp: new Date().toISOString()
    });
    
    // 记录到监控系统
    if (duration > 3000) {
      console.warn(`[SLOW] ${functionName} 执行缓慢: ${duration}ms`);
    }
  }
}
```

#### 版本管理
```bash
# 查看函数版本
tcb fn list --env your-env-id

# 发布新版本
tcb fn deploy loginAPI --env your-env-id

# 回滚版本
tcb fn rollback loginAPI --version 2 --env your-env-id
```

### 3. 缓存维护

#### 缓存清理
```javascript
// 清理过期缓存
async function cleanupCache() {
  const cache = require('./cache');
  
  // 清理所有过期缓存
  cache.cleanup();
  
  // 预热常用缓存
  await cache.warmup(['categories', 'banners']);
  
  console.log('缓存维护完成');
}
```

#### 缓存监控
```javascript
// 缓存命中率统计
class CacheMonitor {
  constructor() {
    this.stats = {
      hits: 0,
      misses: 0,
      total: 0
    };
  }
  
  recordHit() {
    this.stats.hits++;
    this.stats.total++;
  }
  
  recordMiss() {
    this.stats.misses++;
    this.stats.total++;
  }
  
  getHitRate() {
    return this.stats.total > 0 
      ? (this.stats.hits / this.stats.total * 100).toFixed(2) + '%'
      : '0%';
  }
}
```

## 故障处理

### 1. 常见故障

#### 云函数超时
**症状**: 函数执行超过配置的超时时间
**原因**: 
- 数据库查询慢
- 外部API调用慢
- 代码逻辑复杂

**解决方案**:
```bash
# 1. 查看函数日志
tcb fn log functionName --env your-env-id

# 2. 优化查询语句
# 3. 增加超时时间
tcb fn update functionName --timeout 30 --env your-env-id

# 4. 优化代码逻辑
```

#### 数据库连接失败
**症状**: 数据库操作返回连接错误
**原因**:
- 网络问题
- 权限配置错误
- 连接数超限

**解决方案**:
```javascript
// 添加重试机制
async function dbOperationWithRetry(operation, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      console.warn(`数据库操作失败，重试 ${i + 1}/${maxRetries}:`, error);
      
      if (i === maxRetries - 1) throw error;
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

#### 实时同步异常
**症状**: 数据变更没有实时同步到客户端
**原因**:
- Watch连接断开
- 同步通知创建失败
- 客户端监听器异常

**解决方案**:
```javascript
// 检查同步状态
async function checkSyncStatus() {
  const db = cloud.database();
  
  // 检查最近的同步通知
  const recentNotifications = await db.collection('sync_notifications')
    .orderBy('timestamp', 'desc')
    .limit(10)
    .get();
  
  console.log('最近同步通知:', recentNotifications.data);
  
  // 检查未处理的通知
  const unprocessed = await db.collection('sync_notifications')
    .where({ processed: false })
    .count();
  
  if (unprocessed.total > 100) {
    console.warn('存在大量未处理的同步通知:', unprocessed.total);
  }
}
```

### 2. 应急响应

#### 故障等级定义
- **P0**: 系统完全不可用
- **P1**: 核心功能异常
- **P2**: 部分功能异常
- **P3**: 性能问题

#### 应急处理流程
1. **故障发现** (5分钟内)
   - 监控告警
   - 用户反馈
   - 主动巡检

2. **故障确认** (10分钟内)
   - 复现问题
   - 确定影响范围
   - 评估故障等级

3. **应急处理** (30分钟内)
   - 启动应急预案
   - 实施临时修复
   - 通知相关人员

4. **根因分析** (2小时内)
   - 分析故障原因
   - 制定修复方案
   - 实施永久修复

5. **故障复盘** (24小时内)
   - 总结故障过程
   - 改进监控告警
   - 优化应急预案

### 3. 应急预案

#### 数据库故障
```bash
# 1. 切换到备用环境
# 2. 恢复最新备份
tcb db import --env backup-env-id --collection categories --file categories-backup.json

# 3. 更新DNS指向
# 4. 验证功能正常
```

#### 云函数故障
```bash
# 1. 回滚到上一个稳定版本
tcb fn rollback loginAPI --version 1 --env your-env-id

# 2. 重启函数实例
tcb fn restart loginAPI --env your-env-id

# 3. 扩容函数实例
tcb fn scale loginAPI --instances 10 --env your-env-id
```

## 性能调优

### 1. 云函数调优

#### 内存配置优化
```javascript
// 根据实际使用情况调整内存
const memoryConfig = {
  loginAPI: 256,    // 轻量级认证
  webAdminAPI: 512, // 复杂事务操作
  dataAPI: 256      // 数据查询
};
```

#### 冷启动优化
```javascript
// 函数预热
const warmer = require('./warmer');

exports.main = async (event, context) => {
  // 启动预热
  warmer.startWarmup();
  
  // 业务逻辑
  return await handleRequest(event);
};
```

### 2. 数据库调优

#### 查询优化
```javascript
// 使用索引优化查询
const optimizedQuery = db.collection('emojis')
  .where({
    status: 'published',  // 使用索引字段
    categoryId: 'cat_001' // 复合索引
  })
  .field({               // 只查询需要的字段
    id: true,
    title: true,
    imageUrl: true
  })
  .limit(20);           // 限制返回数量
```

#### 批量操作优化
```javascript
// 批量插入优化
async function batchInsert(data) {
  const batchSize = 100;
  const batches = [];
  
  for (let i = 0; i < data.length; i += batchSize) {
    batches.push(data.slice(i, i + batchSize));
  }
  
  for (const batch of batches) {
    await db.collection('emojis').add(batch);
  }
}
```

### 3. 缓存调优

#### 缓存策略优化
```javascript
// 多级缓存策略
class MultiLevelCache {
  constructor() {
    this.l1Cache = new Map(); // 内存缓存
    this.l2Cache = null;      // Redis缓存（可选）
  }
  
  async get(key) {
    // L1缓存
    if (this.l1Cache.has(key)) {
      return this.l1Cache.get(key);
    }
    
    // L2缓存
    if (this.l2Cache) {
      const value = await this.l2Cache.get(key);
      if (value) {
        this.l1Cache.set(key, value);
        return value;
      }
    }
    
    return null;
  }
}
```

## 安全运维

### 1. 访问控制

#### JWT令牌管理
```javascript
// 令牌黑名单机制
class TokenBlacklist {
  constructor() {
    this.blacklist = new Set();
  }
  
  addToBlacklist(token) {
    this.blacklist.add(token);
  }
  
  isBlacklisted(token) {
    return this.blacklist.has(token);
  }
  
  cleanup() {
    // 定期清理过期令牌
    this.blacklist.clear();
  }
}
```

#### 权限审计
```javascript
// 权限操作日志
function logPermissionCheck(adminId, action, resource, granted) {
  console.log(`[AUDIT] ${adminId} ${action} ${resource}: ${granted ? 'GRANTED' : 'DENIED'}`);
  
  // 记录到审计日志
  db.collection('audit_logs').add({
    data: {
      adminId,
      action,
      resource,
      granted,
      timestamp: new Date(),
      ip: context.clientIP
    }
  });
}
```

### 2. 数据安全

#### 敏感数据处理
```javascript
// 敏感信息过滤
function sanitizeData(data) {
  const sensitiveFields = ['password', 'token', 'secret'];
  
  if (typeof data === 'object' && data !== null) {
    const sanitized = { ...data };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    }
    
    return sanitized;
  }
  
  return data;
}
```

#### 数据加密
```javascript
// 敏感数据加密存储
const crypto = require('crypto');

function encryptSensitiveData(data, key) {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}
```

## 运维工具

### 1. 监控脚本

```bash
#!/bin/bash
# 系统健康检查脚本

echo "=== V1.0 系统健康检查 ==="

# 检查云函数状态
echo "检查云函数状态..."
tcb fn list --env your-env-id

# 检查数据库连接
echo "检查数据库连接..."
# 这里可以调用测试接口

# 检查静态网站
echo "检查静态网站..."
curl -I https://your-domain.com

echo "健康检查完成"
```

### 2. 自动化脚本

```javascript
// 自动化运维任务
class AutomationTasks {
  // 每日数据备份
  async dailyBackup() {
    const date = new Date().toISOString().split('T')[0];
    console.log(`执行每日备份: ${date}`);
    
    // 执行备份逻辑
    await this.backupDatabase();
    await this.cleanupOldBackups();
  }
  
  // 每周性能报告
  async weeklyReport() {
    console.log('生成每周性能报告...');
    
    const report = await this.generatePerformanceReport();
    await this.sendReport(report);
  }
  
  // 每月安全审计
  async monthlySecurityAudit() {
    console.log('执行每月安全审计...');
    
    await this.auditPermissions();
    await this.checkSecurityLogs();
    await this.updateSecurityPolicies();
  }
}
```

## 运维检查清单

### 日常检查 (每日)
- [ ] 检查系统监控指标
- [ ] 查看错误日志
- [ ] 验证备份完成
- [ ] 检查缓存命中率
- [ ] 确认实时同步正常

### 周度检查 (每周)
- [ ] 分析性能趋势
- [ ] 检查存储使用情况
- [ ] 清理过期数据
- [ ] 更新安全补丁
- [ ] 生成运维报告

### 月度检查 (每月)
- [ ] 容量规划评估
- [ ] 安全审计
- [ ] 灾备演练
- [ ] 成本分析
- [ ] 架构优化评估

## 联系信息

- **运维负责人**: [姓名]
- **紧急联系**: [电话]
- **邮箱**: [邮箱]
- **值班群**: [群号]
