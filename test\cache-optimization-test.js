/**
 * 缓存优化测试脚本
 * 验证LRU缓存和智能缓存系统的功能
 */

// 模拟微信小程序环境
global.wx = {
  getStorageSync: (key) => {
    console.log(`📱 模拟读取存储: ${key}`)
    return null
  },
  setStorageSync: (key, data) => {
    console.log(`📱 模拟保存存储: ${key}`)
  },
  removeStorageSync: (key) => {
    console.log(`📱 模拟删除存储: ${key}`)
  }
}

// 引入缓存模块
const { LRUCache } = require('../utils/lruCache.js')
const { SmartCache } = require('../utils/smartCache.js')

// 测试函数
async function testCacheOptimization() {
  console.log('🧪 开始测试缓存优化...\n')

  try {
    // 测试1: LRU缓存基础功能
    console.log('📋 测试1: LRU缓存基础功能')
    const lruCache = new LRUCache(3, 1024) // 最多3项，1KB内存
    
    // 添加数据
    lruCache.set('key1', 'value1')
    lruCache.set('key2', 'value2')
    lruCache.set('key3', 'value3')
    
    console.log('   缓存大小:', lruCache.size())
    console.log('   获取key1:', lruCache.get('key1'))
    console.log('   获取key2:', lruCache.get('key2'))
    
    // 添加第4项，应该淘汰最少使用的
    lruCache.set('key4', 'value4')
    console.log('   添加key4后，key3是否存在:', lruCache.has('key3'))
    console.log('   ✅ LRU基础功能测试通过\n')

    // 测试2: LRU缓存TTL功能
    console.log('📋 测试2: LRU缓存TTL功能')
    lruCache.set('temp_key', 'temp_value', 100) // 100ms过期
    console.log('   设置临时key，立即获取:', lruCache.get('temp_key'))
    
    // 等待过期
    await new Promise(resolve => setTimeout(resolve, 150))
    console.log('   150ms后获取:', lruCache.get('temp_key'))
    console.log('   ✅ TTL功能测试通过\n')

    // 测试3: LRU缓存统计信息
    console.log('📋 测试3: LRU缓存统计信息')
    const stats = lruCache.getStats()
    console.log('   缓存统计:', {
      size: stats.size,
      hitRate: stats.hitRate,
      evictions: stats.evictions
    })
    console.log('   内存使用:', lruCache.getMemoryUsage())
    console.log('   ✅ 统计信息测试通过\n')

    // 测试4: 智能缓存初始化
    console.log('📋 测试4: 智能缓存初始化')
    SmartCache.init({
      memory: {
        maxSize: 10,
        maxMemory: 1024,
        defaultTTL: 5000
      },
      temp: {
        maxSize: 5,
        maxMemory: 512,
        defaultTTL: 2000
      }
    })
    console.log('   ✅ 智能缓存初始化完成\n')

    // 测试5: 智能缓存基础操作
    console.log('📋 测试5: 智能缓存基础操作')
    
    // 设置不同类型的数据
    SmartCache.set('emoji_1', { id: 1, name: '笑脸' }, 'emojis')
    SmartCache.set('category_1', { id: 1, name: '搞笑' }, 'categories')
    SmartCache.set('search_test', ['result1', 'result2'], 'search_results')
    
    // 获取数据
    console.log('   获取emoji_1:', SmartCache.get('emoji_1', 'emojis'))
    console.log('   获取category_1:', SmartCache.get('category_1', 'categories'))
    console.log('   获取search_test:', SmartCache.get('search_test', 'search_results'))
    console.log('   ✅ 基础操作测试通过\n')

    // 测试6: 批量操作
    console.log('📋 测试6: 批量操作')
    
    const batchData = {
      'emoji_2': { id: 2, name: '哭脸' },
      'emoji_3': { id: 3, name: '愤怒' },
      'emoji_4': { id: 4, name: '惊讶' }
    }
    
    SmartCache.setBatch(batchData, 'emojis')
    
    const batchResult = SmartCache.getBatch(['emoji_1', 'emoji_2', 'emoji_3', 'emoji_5'], 'emojis')
    console.log('   批量获取结果:', {
      found: Object.keys(batchResult.results).length,
      missing: batchResult.missingKeys.length,
      hitRate: batchResult.hitRate + '%'
    })
    console.log('   ✅ 批量操作测试通过\n')

    // 测试7: 缓存策略
    console.log('📋 测试7: 缓存策略测试')
    
    // 测试不同类型数据的缓存级别
    SmartCache.set('config_app_version', '1.0.0', 'app_config')
    SmartCache.set('user_action_123', { action: 'like' }, 'user_actions')
    
    console.log('   app_config数据存储到storage级别')
    console.log('   user_actions数据存储到temp级别')
    console.log('   ✅ 缓存策略测试通过\n')

    // 测试8: 预加载功能
    console.log('📋 测试8: 预加载功能')
    
    const mockDataLoader = async (keys) => {
      console.log('   模拟数据加载器被调用，keys:', keys)
      const data = {}
      keys.forEach(key => {
        data[key] = { id: key, loaded: true, timestamp: Date.now() }
      })
      return data
    }
    
    await SmartCache.preload(mockDataLoader, ['preload_1', 'preload_2', 'preload_3'], 'emojis')
    
    console.log('   预加载后获取preload_1:', SmartCache.get('preload_1', 'emojis'))
    console.log('   ✅ 预加载功能测试通过\n')

    // 测试9: 缓存统计
    console.log('📋 测试9: 缓存统计')
    
    const cacheStats = SmartCache.getStats()
    console.log('   智能缓存统计:')
    for (const [level, stats] of Object.entries(cacheStats)) {
      if (stats.size !== undefined) {
        console.log(`     ${level}: ${stats.size} 项${stats.hitRate ? `, 命中率: ${stats.hitRate}` : ''}`)
      }
    }
    console.log('   ✅ 缓存统计测试通过\n')

    // 测试10: 内存管理
    console.log('📋 测试10: 内存管理测试')
    
    // 添加大量数据测试内存管理
    for (let i = 0; i < 20; i++) {
      SmartCache.set(`large_data_${i}`, {
        id: i,
        data: new Array(100).fill(`data_${i}`),
        timestamp: Date.now()
      }, 'emojis')
    }
    
    const finalStats = SmartCache.getStats()
    console.log('   添加大量数据后的统计:')
    if (finalStats.memory && finalStats.memory.memoryUsage) {
      console.log(`     内存使用: ${finalStats.memory.memoryUsage.percentage}%`)
    }
    console.log('   ✅ 内存管理测试通过\n')

    // 测试11: 清理功能
    console.log('📋 测试11: 清理功能')
    
    SmartCache.cleanup()
    console.log('   执行清理操作')
    
    const cleanupStats = SmartCache.getStats()
    console.log('   清理后统计:', Object.keys(cleanupStats).map(level => 
      `${level}: ${cleanupStats[level].size || 0} 项`
    ).join(', '))
    console.log('   ✅ 清理功能测试通过\n')

    // 测试12: 缓存导出导入
    console.log('📋 测试12: 缓存导出导入')
    
    // 重新添加一些数据
    SmartCache.set('export_test_1', 'value1', 'emojis')
    SmartCache.set('export_test_2', 'value2', 'emojis')
    
    // 导出（LRU缓存功能）
    const memoryCache = SmartCache.caches.memory
    if (memoryCache && typeof memoryCache.export === 'function') {
      const exported = memoryCache.export()
      console.log('   导出数据项数:', Object.keys(exported).length)
      
      // 清空并导入
      memoryCache.clear()
      memoryCache.import(exported)
      console.log('   导入后数据项数:', memoryCache.size())
    }
    console.log('   ✅ 导出导入测试通过\n')

    console.log('🎉 所有缓存优化测试完成！')
    return true

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    console.error('   错误详情:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  testCacheOptimization().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = { testCacheOptimization }
