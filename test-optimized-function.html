<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试优化后的云函数</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .timer { font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 测试优化后的云函数</h1>
        
        <div class="info">
            <h3>🎯 测试目标</h3>
            <p>测试云函数调用是否还会超时（之前超过3秒）</p>
            <p>优化方案：减少数据库查询次数，在内存中统计分类数量</p>
        </div>

        <div class="step">
            <h3>⚡ 快速测试</h3>
            <button onclick="testPing()">测试Ping（应该很快）</button>
            <div id="pingResult"></div>
        </div>

        <div class="step">
            <h3>📊 测试分类查询（之前超时的功能）</h3>
            <button onclick="testGetCategories()">测试getCategories</button>
            <div id="categoriesResult"></div>
        </div>

        <div class="step">
            <h3>🔄 连续测试</h3>
            <button onclick="runContinuousTest()">连续测试5次</button>
            <div id="continuousResult"></div>
        </div>

        <div class="step">
            <h3>📈 性能对比</h3>
            <div id="performanceResult"></div>
        </div>
    </div>

    <!-- 使用最新的云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let app = null;

        // 初始化云开发
        async function initCloudbase() {
            if (!app) {
                app = cloudbase.init({ env: ENV_ID });
                await app.auth().signInAnonymously();
            }
            return app;
        }

        // 测试Ping
        async function testPing() {
            const resultDiv = document.getElementById('pingResult');
            resultDiv.innerHTML = '<p>正在测试Ping...</p>';

            try {
                await initCloudbase();
                
                const startTime = Date.now();
                const result = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'ping' }
                });
                const endTime = Date.now();
                const duration = endTime - startTime;

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Ping测试成功</h4>
                        <p class="timer">响应时间: ${duration}ms</p>
                        <p>结果: ${result.result.message}</p>
                        <p>时间戳: ${result.result.timestamp}</p>
                    </div>
                `;

            } catch (error) {
                const endTime = Date.now();
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Ping测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试分类查询
        async function testGetCategories() {
            const resultDiv = document.getElementById('categoriesResult');
            resultDiv.innerHTML = '<p>正在测试分类查询...</p>';

            try {
                await initCloudbase();
                
                const startTime = Date.now();
                const result = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (result.result.success) {
                    const categories = result.result.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 分类查询成功</h4>
                            <p class="timer">响应时间: ${duration}ms</p>
                            <p>分类数量: ${categories.length}</p>
                            <p>总表情包数: ${categories.reduce((sum, cat) => sum + (cat.emojiCount || 0), 0)}</p>
                            
                            <h5>分类详情:</h5>
                            <ul>
                                ${categories.map(cat => 
                                    `<li>${cat.icon} ${cat.name}: ${cat.emojiCount || 0} 个表情包</li>`
                                ).join('')}
                            </ul>
                            
                            ${duration < 3000 ? 
                                '<p style="color: green; font-weight: bold;">🎉 没有超时！优化成功！</p>' :
                                '<p style="color: red; font-weight: bold;">⚠️ 仍然较慢，可能需要进一步优化</p>'
                            }
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 分类查询失败</h4>
                            <p class="timer">响应时间: ${duration}ms</p>
                            <p>错误: ${result.result.message}</p>
                        </div>
                    `;
                }

            } catch (error) {
                const endTime = Date.now();
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 分类查询失败</h4>
                        <p>错误: ${error.message}</p>
                        <pre>${JSON.stringify(error, null, 2)}</pre>
                    </div>
                `;
            }
        }

        // 连续测试
        async function runContinuousTest() {
            const resultDiv = document.getElementById('continuousResult');
            resultDiv.innerHTML = '<p>正在进行连续测试...</p>';

            const results = [];
            
            try {
                await initCloudbase();

                for (let i = 1; i <= 5; i++) {
                    resultDiv.innerHTML = `<p>正在进行第 ${i}/5 次测试...</p>`;
                    
                    const startTime = Date.now();
                    try {
                        const result = await app.callFunction({
                            name: 'dataAPI',
                            data: { action: 'getCategories' }
                        });
                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        results.push({
                            test: i,
                            success: result.result.success,
                            duration: duration,
                            categoriesCount: result.result.data?.length || 0,
                            error: null
                        });

                    } catch (error) {
                        const endTime = Date.now();
                        const duration = endTime - startTime;
                        
                        results.push({
                            test: i,
                            success: false,
                            duration: duration,
                            categoriesCount: 0,
                            error: error.message
                        });
                    }

                    // 等待1秒再进行下一次测试
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // 计算统计信息
                const successCount = results.filter(r => r.success).length;
                const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
                const maxDuration = Math.max(...results.map(r => r.duration));
                const minDuration = Math.min(...results.map(r => r.duration));

                resultDiv.innerHTML = `
                    <div class="${successCount === 5 ? 'success' : 'error'}">
                        <h4>${successCount === 5 ? '✅' : '⚠️'} 连续测试完成</h4>
                        <p><strong>成功率:</strong> ${successCount}/5 (${(successCount/5*100).toFixed(1)}%)</p>
                        <p><strong>平均响应时间:</strong> ${avgDuration.toFixed(0)}ms</p>
                        <p><strong>最快响应:</strong> ${minDuration}ms</p>
                        <p><strong>最慢响应:</strong> ${maxDuration}ms</p>
                        
                        <h5>详细结果:</h5>
                        <ul>
                            ${results.map(r => 
                                `<li>测试${r.test}: ${r.success ? '✅' : '❌'} ${r.duration}ms ${r.error ? `(${r.error})` : `(${r.categoriesCount}个分类)`}</li>`
                            ).join('')}
                        </ul>
                        
                        ${maxDuration < 3000 ? 
                            '<p style="color: green; font-weight: bold;">🎉 所有测试都没有超时！优化非常成功！</p>' :
                            '<p style="color: orange; font-weight: bold;">⚠️ 仍有超时情况，需要进一步优化</p>'
                        }
                    </div>
                `;

                // 更新性能对比
                updatePerformanceComparison(results);

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 连续测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 更新性能对比
        function updatePerformanceComparison(results) {
            const performanceDiv = document.getElementById('performanceResult');
            
            const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
            const successRate = results.filter(r => r.success).length / results.length * 100;

            performanceDiv.innerHTML = `
                <div class="info">
                    <h4>📈 性能对比分析</h4>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 8px; border: 1px solid #ddd;">指标</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">优化前</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">优化后</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">改善</th>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">响应时间</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">>3000ms (超时)</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${avgDuration.toFixed(0)}ms</td>
                            <td style="padding: 8px; border: 1px solid #ddd; color: green;">
                                ${avgDuration < 3000 ? '✅ 不再超时' : '❌ 仍需优化'}
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">成功率</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">0% (超时失败)</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${successRate.toFixed(1)}%</td>
                            <td style="padding: 8px; border: 1px solid #ddd; color: green;">
                                ${successRate > 80 ? '✅ 大幅改善' : '⚠️ 需要继续优化'}
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">数据库查询</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">19个分类 × 多次查询</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">2次查询 + 内存统计</td>
                            <td style="padding: 8px; border: 1px solid #ddd; color: green;">✅ 大幅减少</td>
                        </tr>
                    </table>
                    
                    <h5>🎯 结论:</h5>
                    ${avgDuration < 3000 && successRate > 80 ? 
                        '<p style="color: green; font-weight: bold;">🎉 优化成功！云函数性能大幅提升，不再超时！</p>' :
                        '<p style="color: orange; font-weight: bold;">⚠️ 有改善但仍需进一步优化，建议检查云函数部署状态。</p>'
                    }
                </div>
            `;
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🚀 云函数优化测试页面加载完成');
            console.log('环境ID:', ENV_ID);
        });
    </script>
</body>
</html>
