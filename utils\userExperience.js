/**
 * 用户体验增强模块
 * 提供更好的用户交互体验和友好提示
 */

const { ErrorHandler } = require('./errorHandler.js')

const UserExperience = {
  // 加载状态管理
  loadingStates: new Map(),
  
  // 提示消息队列
  messageQueue: [],
  
  // 当前显示的提示
  currentToast: null,

  /**
   * 显示加载状态
   */
  showLoading(title = '加载中...', options = {}) {
    const {
      mask = true,
      timeout = 10000,
      key = 'default'
    } = options

    try {
      // 记录加载状态
      this.loadingStates.set(key, {
        title,
        startTime: Date.now(),
        timeout
      })

      if (typeof wx !== 'undefined') {
        wx.showLoading({
          title,
          mask
        })

        // 设置超时自动隐藏
        setTimeout(() => {
          if (this.loadingStates.has(key)) {
            this.hideLoading(key)
            this.showMessage('操作超时，请重试', 'warning')
          }
        }, timeout)
      } else {
        console.log('📱 模拟显示加载:', title)
      }

    } catch (error) {
      console.error('❌ 显示加载状态失败:', error)
    }
  },

  /**
   * 隐藏加载状态
   */
  hideLoading(key = 'default') {
    try {
      if (this.loadingStates.has(key)) {
        const loadingInfo = this.loadingStates.get(key)
        const duration = Date.now() - loadingInfo.startTime
        
        console.log(`⏱️ 加载完成: ${loadingInfo.title} (${duration}ms)`)
        this.loadingStates.delete(key)
      }

      if (typeof wx !== 'undefined') {
        wx.hideLoading()
      } else {
        console.log('📱 模拟隐藏加载')
      }

    } catch (error) {
      console.error('❌ 隐藏加载状态失败:', error)
    }
  },

  /**
   * 显示成功消息
   */
  showSuccess(message, duration = 2000) {
    this.showMessage(message, 'success', duration)
  },

  /**
   * 显示警告消息
   */
  showWarning(message, duration = 3000) {
    this.showMessage(message, 'warning', duration)
  },

  /**
   * 显示错误消息
   */
  showError(message, duration = 3000) {
    this.showMessage(message, 'error', duration)
  },

  /**
   * 显示信息消息
   */
  showInfo(message, duration = 2000) {
    this.showMessage(message, 'info', duration)
  },

  /**
   * 统一的消息显示方法
   */
  showMessage(message, type = 'info', duration = 2000) {
    try {
      // 添加到消息队列
      this.messageQueue.push({
        message,
        type,
        duration,
        timestamp: Date.now()
      })

      // 如果当前没有显示提示，立即显示
      if (!this.currentToast) {
        this.processMessageQueue()
      }

    } catch (error) {
      console.error('❌ 显示消息失败:', error)
    }
  },

  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0 || this.currentToast) {
      return
    }

    const messageInfo = this.messageQueue.shift()
    this.currentToast = messageInfo

    const iconMap = {
      success: 'success',
      error: 'none',
      warning: 'none',
      info: 'none'
    }

    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: messageInfo.message,
        icon: iconMap[messageInfo.type],
        duration: messageInfo.duration,
        mask: true,
        success: () => {
          setTimeout(() => {
            this.currentToast = null
            this.processMessageQueue() // 处理下一个消息
          }, messageInfo.duration)
        }
      })
    } else {
      console.log(`📱 模拟显示${messageInfo.type}消息:`, messageInfo.message)
      setTimeout(() => {
        this.currentToast = null
        this.processMessageQueue()
      }, messageInfo.duration)
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirm(options = {}) {
    const {
      title = '提示',
      content = '确定要执行此操作吗？',
      confirmText = '确定',
      cancelText = '取消',
      confirmColor = '#007AFF',
      cancelColor = '#000000'
    } = options

    return new Promise((resolve) => {
      if (typeof wx !== 'undefined') {
        wx.showModal({
          title,
          content,
          showCancel: true,
          confirmText,
          cancelText,
          confirmColor,
          cancelColor,
          success: (res) => {
            resolve({
              confirm: res.confirm,
              cancel: res.cancel
            })
          },
          fail: () => {
            resolve({ confirm: false, cancel: true })
          }
        })
      } else {
        console.log('📱 模拟显示确认对话框:', title, content)
        // 模拟用户确认
        resolve({ confirm: true, cancel: false })
      }
    })
  },

  /**
   * 显示操作菜单
   */
  showActionSheet(options = {}) {
    const {
      itemList = [],
      itemColor = '#000000'
    } = options

    return new Promise((resolve) => {
      if (typeof wx !== 'undefined') {
        wx.showActionSheet({
          itemList,
          itemColor,
          success: (res) => {
            resolve({
              tapIndex: res.tapIndex,
              selectedItem: itemList[res.tapIndex]
            })
          },
          fail: () => {
            resolve({ tapIndex: -1, selectedItem: null })
          }
        })
      } else {
        console.log('📱 模拟显示操作菜单:', itemList)
        resolve({ tapIndex: 0, selectedItem: itemList[0] })
      }
    })
  },

  /**
   * 显示带重试的错误提示
   */
  showRetryError(message, retryCallback, options = {}) {
    const {
      title = '操作失败',
      maxRetries = 3,
      retryCount = 0
    } = options

    const content = retryCount > 0 
      ? `${message}\n\n已重试 ${retryCount} 次`
      : message

    return this.showConfirm({
      title,
      content,
      confirmText: retryCount < maxRetries ? '重试' : '确定',
      cancelText: '取消'
    }).then((result) => {
      if (result.confirm && retryCount < maxRetries && retryCallback) {
        console.log(`🔄 用户选择重试 (${retryCount + 1}/${maxRetries})`)
        return retryCallback(retryCount + 1)
      }
      return false
    })
  },

  /**
   * 显示网络错误提示
   */
  showNetworkError(retryCallback) {
    return this.showRetryError(
      '网络连接异常，请检查网络设置后重试',
      retryCallback,
      { title: '网络错误' }
    )
  },

  /**
   * 显示权限请求提示
   */
  showPermissionRequest(permissionType, callback) {
    const permissionMessages = {
      camera: '需要使用相机权限来拍照',
      album: '需要访问相册权限来选择图片',
      location: '需要获取位置权限来提供更好的服务',
      microphone: '需要使用麦克风权限来录音',
      writePhotosAlbum: '需要保存图片到相册的权限'
    }

    const message = permissionMessages[permissionType] || '需要相关权限来完成操作'

    return this.showConfirm({
      title: '权限申请',
      content: `${message}，是否前往设置页面开启权限？`,
      confirmText: '去设置',
      cancelText: '取消'
    }).then((result) => {
      if (result.confirm) {
        if (typeof wx !== 'undefined') {
          wx.openSetting({
            success: callback,
            fail: callback
          })
        } else {
          console.log('📱 模拟打开设置页面')
          if (callback) callback()
        }
      }
      return result.confirm
    })
  },

  /**
   * 显示功能介绍提示
   */
  showFeatureIntro(title, steps = [], options = {}) {
    const {
      showSkip = true,
      autoNext = true,
      stepDuration = 3000
    } = options

    let currentStep = 0

    const showStep = () => {
      if (currentStep >= steps.length) {
        return Promise.resolve()
      }

      const step = steps[currentStep]
      const isLastStep = currentStep === steps.length - 1

      return this.showConfirm({
        title: `${title} (${currentStep + 1}/${steps.length})`,
        content: step,
        confirmText: isLastStep ? '完成' : '下一步',
        cancelText: showSkip ? '跳过' : '取消'
      }).then((result) => {
        if (result.confirm) {
          currentStep++
          return showStep()
        } else {
          return Promise.resolve()
        }
      })
    }

    return showStep()
  },

  /**
   * 显示加载进度
   */
  showProgress(title = '处理中...', progress = 0) {
    const progressText = `${title} ${Math.round(progress * 100)}%`
    
    if (typeof wx !== 'undefined') {
      wx.showLoading({
        title: progressText,
        mask: true
      })
    } else {
      console.log('📱 模拟显示进度:', progressText)
    }
  },

  /**
   * 创建带用户体验增强的异步函数包装器
   */
  wrapWithUX(asyncFn, options = {}) {
    const {
      loadingTitle = '处理中...',
      successMessage = '操作成功',
      errorHandler = null,
      showSuccess = true,
      showError = true
    } = options

    return async (...args) => {
      const loadingKey = `wrap_${Date.now()}`
      
      try {
        this.showLoading(loadingTitle, { key: loadingKey })
        
        const result = await asyncFn(...args)
        
        this.hideLoading(loadingKey)
        
        if (showSuccess && successMessage) {
          this.showSuccess(successMessage)
        }
        
        return result
        
      } catch (error) {
        this.hideLoading(loadingKey)
        
        if (showError) {
          if (errorHandler) {
            errorHandler(error)
          } else {
            ErrorHandler.handleError({
              type: ErrorHandler.ERROR_TYPES.UNKNOWN,
              level: ErrorHandler.ERROR_LEVELS.ERROR,
              message: error.message || '操作失败',
              showToUser: true
            })
          }
        }
        
        throw error
      }
    }
  },

  /**
   * 清理资源
   */
  cleanup() {
    // 清理加载状态
    this.loadingStates.clear()
    
    // 清理消息队列
    this.messageQueue = []
    this.currentToast = null
    
    // 隐藏所有提示
    if (typeof wx !== 'undefined') {
      wx.hideLoading()
      wx.hideToast()
    }
    
    console.log('🧹 用户体验模块已清理')
  }
}

module.exports = {
  UserExperience
}
