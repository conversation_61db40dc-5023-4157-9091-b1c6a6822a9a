// 云函数入口文件 - 初始化数据库
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('开始初始化数据库...')
  
  try {
    const results = {
      categories: false,
      emojis: false,
      banners: false,
      errors: []
    }

    // 1. 创建并初始化分类数据
    try {
      console.log('创建分类数据...')
      
      const categories = [
        { name: '搞笑幽默', icon: '😂', sort: 1, description: '搞笑幽默类表情包' },
        { name: '可爱萌宠', icon: '🐱', sort: 2, description: '可爱萌宠类表情包' },
        { name: '情感表达', icon: '❤️', sort: 3, description: '情感表达类表情包' },
        { name: '节日庆典', icon: '🎉', sort: 4, description: '节日庆典类表情包' },
        { name: '网络热梗', icon: '🔥', sort: 5, description: '网络热梗类表情包' },
        { name: '动漫二次元', icon: '🎭', sort: 6, description: '动漫二次元类表情包' }
      ]

      for (const category of categories) {
        await db.collection('categories').add({
          data: {
            ...category,
            status: 'active',
            createTime: new Date(),
            updateTime: new Date()
          }
        })
        console.log(`分类创建成功: ${category.name}`)
      }
      
      results.categories = true
    } catch (error) {
      console.error('创建分类失败:', error)
      results.errors.push(`分类创建失败: ${error.message}`)
    }

    // 2. 创建并初始化表情包数据
    try {
      console.log('创建表情包数据...')
      
      // 先获取分类ID
      const categoryResult = await db.collection('categories').get()
      const categoryMap = {}
      categoryResult.data.forEach(cat => {
        categoryMap[cat.name] = cat._id
      })

      const emojis = [
        {
          title: '哈哈哈笑死我了',
          categoryId: categoryMap['搞笑幽默'],
          category: '搞笑幽默',
          imageUrl: 'https://picsum.photos/300/300?random=11',
          likes: 1200,
          collections: 850,
          downloads: 5600,
          tags: ['搞笑', '哈哈', '笑死'],
          description: '超级搞笑的表情包，让你笑到停不下来！',
          author: '表情包达人',
          status: 'published'
        },
        {
          title: '可爱小猫咪',
          categoryId: categoryMap['可爱萌宠'],
          category: '可爱萌宠',
          imageUrl: 'https://picsum.photos/300/300?random=12',
          likes: 987,
          collections: 654,
          downloads: 3200,
          tags: ['可爱', '猫咪', '萌宠'],
          description: '超级可爱的小猫咪表情包，萌化你的心！',
          author: '萌宠爱好者',
          status: 'published'
        },
        {
          title: '爱你么么哒',
          categoryId: categoryMap['情感表达'],
          category: '情感表达',
          imageUrl: 'https://picsum.photos/300/300?random=13',
          likes: 1456,
          collections: 1123,
          downloads: 4500,
          tags: ['爱情', '表白', '么么哒'],
          description: '表达爱意的甜蜜表情包！',
          author: '恋爱达人',
          status: 'published'
        },
        {
          title: '新年快乐',
          categoryId: categoryMap['节日庆典'],
          category: '节日庆典',
          imageUrl: 'https://picsum.photos/300/300?random=14',
          likes: 2345,
          collections: 1876,
          downloads: 8900,
          tags: ['新年', '快乐', '庆祝'],
          description: '新年祝福专用表情包！',
          author: '节日专家',
          status: 'published'
        },
        {
          title: '打工人',
          categoryId: categoryMap['网络热梗'],
          category: '网络热梗',
          imageUrl: 'https://picsum.photos/300/300?random=15',
          likes: 1789,
          collections: 1123,
          downloads: 7800,
          tags: ['打工人', '上班', '热梗'],
          description: '打工人专属表情包，上班族的心声！',
          author: '社畜代表',
          status: 'published'
        },
        {
          title: '二次元萌妹',
          categoryId: categoryMap['动漫二次元'],
          category: '动漫二次元',
          imageUrl: 'https://picsum.photos/300/300?random=16',
          likes: 823,
          collections: 567,
          downloads: 2469,
          tags: ['二次元', '萌妹', '可爱'],
          description: '可爱的二次元角色表情包！',
          author: '二次元爱好者',
          status: 'published'
        }
      ]

      for (const emoji of emojis) {
        await db.collection('emojis').add({
          data: {
            ...emoji,
            createTime: new Date(),
            updateTime: new Date()
          }
        })
        console.log(`表情包创建成功: ${emoji.title}`)
      }
      
      results.emojis = true
    } catch (error) {
      console.error('创建表情包失败:', error)
      results.errors.push(`表情包创建失败: ${error.message}`)
    }

    // 3. 创建并初始化横幅数据
    try {
      console.log('创建横幅数据...')
      
      const banners = [
        {
          title: '欢迎使用表情包小程序',
          imageUrl: 'https://picsum.photos/400/200?random=1',
          linkUrl: '/pages/index/index',
          status: 'active',
          sortOrder: 1,
          startTime: new Date(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
          impressions: 0,
          clicks: 0
        },
        {
          title: '精选表情包推荐',
          imageUrl: 'https://picsum.photos/400/200?random=2',
          linkUrl: '/pages/category/category',
          status: 'active',
          sortOrder: 2,
          startTime: new Date(),
          endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
          impressions: 0,
          clicks: 0
        }
      ]

      for (const banner of banners) {
        await db.collection('banners').add({
          data: {
            ...banner,
            createTime: new Date()
          }
        })
        console.log(`横幅创建成功: ${banner.title}`)
      }
      
      results.banners = true
    } catch (error) {
      console.error('创建横幅失败:', error)
      results.errors.push(`横幅创建失败: ${error.message}`)
    }

    const successCount = [results.categories, results.emojis, results.banners].filter(Boolean).length
    
    return {
      success: successCount > 0,
      message: `数据库初始化完成！成功创建 ${successCount}/3 个数据类型`,
      results,
      timestamp: new Date().toISOString()
    }

  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: '数据库初始化失败: ' + error.message,
      error: error.message
    }
  }
}
