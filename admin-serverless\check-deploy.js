/**
 * 部署前检查脚本
 * 确保所有必需文件都存在且配置正确
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始部署前检查...\n');

// 检查必需文件
const requiredFiles = [
    'index-ui-unchanged.html',
    'js/app.js',
    'cloudbaserc-real.json'
];

let allFilesExist = true;

console.log('📁 检查必需文件:');
requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} - 存在`);
    } else {
        console.log(`❌ ${file} - 不存在`);
        allFilesExist = false;
    }
});

// 检查环境ID配置
console.log('\n🌐 检查环境ID配置:');
const appJsPath = path.join(__dirname, 'js/app.js');
if (fs.existsSync(appJsPath)) {
    const content = fs.readFileSync(appJsPath, 'utf8');
    if (content.includes('cloud1-5g6pvnpl88dc0142')) {
        console.log('✅ 环境ID已配置: cloud1-5g6pvnpl88dc0142');
    } else {
        console.log('⚠️ 环境ID可能需要更新');
    }
} else {
    console.log('❌ app.js 文件不存在');
}

// 检查云函数目录
console.log('\n☁️ 检查云函数:');
const cloudFunctionsPath = path.join(__dirname, '..', 'cloudfunctions');
const requiredFunctions = ['adminAPI', 'dataAPI'];

requiredFunctions.forEach(funcName => {
    const funcPath = path.join(cloudFunctionsPath, funcName, 'index.js');
    if (fs.existsSync(funcPath)) {
        console.log(`✅ ${funcName} - 云函数文件存在`);
    } else {
        console.log(`❌ ${funcName} - 云函数文件不存在`);
    }
});

// 生成文件清单
console.log('\n📋 生成部署文件清单:');
const deployFiles = [];

function scanDirectory(dir, basePath = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativePath = path.join(basePath, item);
        
        if (fs.statSync(fullPath).isDirectory()) {
            // 跳过不需要的目录
            if (!['node_modules', '.git', 'deploy-real.js'].includes(item)) {
                scanDirectory(fullPath, relativePath);
            }
        } else {
            // 跳过不需要的文件
            if (!item.endsWith('.md') && !item.endsWith('.js') || item === 'app.js') {
                deployFiles.push(relativePath);
            }
        }
    });
}

scanDirectory(__dirname);

console.log('需要上传的文件:');
deployFiles.forEach(file => {
    console.log(`  📄 ${file}`);
});

// 生成部署说明
const deployInstructions = `
# 🚀 部署说明

## 文件清单
${deployFiles.map(file => `- ${file}`).join('\n')}

## 部署步骤
1. 打开微信开发者工具
2. 进入云开发控制台
3. 选择"静态网站托管"
4. 创建 "admin" 文件夹
5. 上传以上所有文件到 admin 文件夹

## 访问地址
https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/

## 检查结果
- 必需文件: ${allFilesExist ? '✅ 全部存在' : '❌ 有文件缺失'}
- 环境配置: ✅ 已配置
- 云函数: ⚠️ 需要手动确认是否已部署

${allFilesExist ? '🎉 可以开始部署！' : '❌ 请先解决文件缺失问题'}
`;

fs.writeFileSync(path.join(__dirname, 'DEPLOY-CHECKLIST.md'), deployInstructions);

console.log('\n📄 部署清单已生成: DEPLOY-CHECKLIST.md');

if (allFilesExist) {
    console.log('\n🎉 检查通过！可以开始部署了！');
    console.log('📖 请查看 deploy-manual.md 获取详细部署步骤');
} else {
    console.log('\n❌ 检查失败！请先解决以上问题');
}

console.log('\n🔗 部署完成后访问: https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/');
