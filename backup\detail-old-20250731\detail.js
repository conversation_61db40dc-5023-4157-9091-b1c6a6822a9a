// pages/detail/detail.js - 旧版本备份（已被 detail-new.js 替代）
const { DataManager } = require('../../utils/newDataManager.js')
const { AuthManager } = require('../../utils/authManager.js')
const { StateManager } = require('../../utils/stateManager.js')
const { withPageState, EmojiStateHelper } = require('../../utils/pageStateMixin.js')
const { DownloadManager } = require('../../utils/downloadManager.js')
const ShareManager = require('../../utils/shareManager.js')

const pageConfig = {
  data: {
    emojiData: null,
    isLiked: false,
    isCollected: false,
    relatedEmojis: [],
    likeLoading: false,
    collectLoading: false,
    // 分阶段加载控制
    contentLoaded: false,    // 内容是否加载完成
    buttonsLoaded: false,    // 按钮是否加载完成
    allLoaded: false         // 所有内容是否加载完成
  },



  onLoad(options) {
    console.log('🔄 详情页面加载，参数:', options)
    const emojiId = options.id

    if (!emojiId) {
      console.error('❌ 表情包ID为空')
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('📄 开始加载表情包详情:', emojiId)
    this.loadEmojiDetail(emojiId)
    this.loadRelatedEmojis(emojiId)
    this.recordRecentView(emojiId)
  },

  onShow() {
    // 不做任何操作，避免状态冲突
  },

  onHide() {
    // 定时器由资源管理器自动清理
  },

  onUnload() {
    // 定时器由资源管理器自动清理
  },

  async loadEmojiDetail(id) {
    console.log('🔄 开始加载表情包详情, ID:', id)

    // 3秒超时保护（使用资源管理器）
    const fallbackTimer = this.createTimer(() => {
      console.log('⏰ 加载超时，显示兜底内容')
      this.showFallbackContent(id)
    }, 3000)

    try {
      // 先尝试从缓存获取数据
      let emojiData = DataManager.getEmojiDataFromCache(id)

      if (!emojiData) {
        // 如果缓存中没有数据，尝试从云端获取
        emojiData = await DataManager.getEmojiData(id)
      }

      if (!emojiData) {
        // 如果还是没有数据，创建测试数据
        emojiData = this.createTestEmojiData(id)
      }

      // 清除兜底定时器
      this.clearTimer(fallbackTimer)

      if (emojiData) {
        // 添加格式化的浏览量
        emojiData.views = this.formatNumber(emojiData.views || Math.floor(Math.random() * 5000) + 1000)

        // 第一阶段：显示内容
        this.setData({
          emojiData: emojiData,
          contentLoaded: true
        })

        // 第二阶段：延迟加载按钮状态（使用资源管理器）
        this._loadingTimer = this.createTimer(() => {
          const emojiState = StateManager.getEmojiState(id)

          // 设置按钮状态
          this.setData({
            isLiked: emojiState.isLiked,
            isCollected: emojiState.isCollected,
            buttonsLoaded: true,
            allLoaded: true
          })

          console.log('✅ 表情包详情加载完成')
        }, 500)

      } else {
        console.error('❌ 表情包不存在，使用兜底数据')

        // 创建一个兜底的表情包数据
        const fallbackEmojiData = {
          id: id,
          title: '表情包加载失败',
          imageUrl: 'https://via.placeholder.com/300x200/FF5722/FFFFFF?text=Load+Failed',
          description: '抱歉，无法加载此表情包的详细信息',
          tags: ['加载失败'],
          likes: 0,
          collections: 0,
          views: '0',
          date: new Date().toISOString().split('T')[0],
          category: '未知分类'
        }

        this.setData({
          emojiData: fallbackEmojiData,
          contentLoaded: true,
          buttonsLoaded: true,
          allLoaded: true
        })

        wx.showToast({
          title: '加载失败，显示兜底内容',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('❌ 加载表情包详情失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({
        contentLoaded: true,
        buttonsLoaded: true,
        allLoaded: true
      })
    }
  },

  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },

  loadRelatedEmojis(currentId) {
    console.log('🔄 加载相关表情包，当前ID:', currentId)

    // 使用稳定的图片URL
    const allEmojis = [
      {
        id: '1',
        title: '哈哈哈笑死我了',
        imageUrl: 'https://picsum.photos/300/300?random=11',
        category: '搞笑幽默',
        likes: 1200,
        collections: 850
      },
      {
        id: '2',
        title: '可爱小猫咪',
        imageUrl: 'https://picsum.photos/300/300?random=12',
        category: '可爱萌宠',
        likes: 987,
        collections: 654
      },
      {
        id: '3',
        title: '爱你么么哒',
        imageUrl: 'https://picsum.photos/300/300?random=13',
        category: '情感表达',
        likes: 756,
        collections: 543
      },
      {
        id: '4',
        title: '新年快乐',
        imageUrl: 'https://picsum.photos/300/300?random=14',
        category: '节日庆典',
        likes: 1100,
        collections: 789
      },
      {
        id: '5',
        title: '加油打气',
        imageUrl: 'https://picsum.photos/300/300?random=15',
        category: '励志正能量',
        likes: 890,
        collections: 456
      }
    ]

    // 过滤掉当前表情包，显示其他相关表情包
    const filteredEmojis = allEmojis.filter(item => item.id !== currentId)

    // 添加点赞收藏状态和格式化数字
    const relatedEmojis = filteredEmojis.map(emoji => {
      const emojiState = StateManager.getEmojiState(emoji.id)
      return {
        ...emoji,
        isLiked: emojiState.isLiked,
        isCollected: emojiState.isCollected,
        likesText: this.formatNumber(emoji.likes),
        collectionsText: this.formatNumber(emoji.collections)
      }
    })

    console.log('✅ 相关表情包加载完成:', relatedEmojis.length, '个')
    this.setData({
      relatedEmojis: relatedEmojis
    })
  },

  updateActionStatus() {
    // 不需要单独更新状态，已经在loadEmojiDetail中处理
    console.log('updateActionStatus: 跳过，状态已在加载时设置')
  },

  onToggleLike(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 防重复点击
    if (this.data.likeLoading) {
      return
    }

    const emojiId = this.data.emojiData?.id
    if (!emojiId) {
      console.error('❌ 表情包ID不存在')
      return
    }

    // 获取当前状态
    const currentIsLiked = this.data.isLiked
    const currentLikes = parseInt(this.data.emojiData.likes) || 0
    
    // 计算新状态
    const newIsLiked = !currentIsLiked
    const newLikes = newIsLiked ? currentLikes + 1 : Math.max(0, currentLikes - 1)
    
    // 更新UI（乐观更新）
    const updatedEmojiData = {
      ...this.data.emojiData,
      likes: newLikes
    }

    // 一次性更新所有状态，避免闪动
    this.setData({
      isLiked: newIsLiked,
      emojiData: updatedEmojiData,
      likeLoading: true
    })

    try {
      // 使用StateManager更新状态
      StateManager.toggleLike(emojiId)

      // 更新全局数据
      DataManager.updateLikes(emojiId, newLikes)

      // 触觉反馈
      wx.vibrateShort({ type: 'light' })

    } catch (error) {
      console.error('❌ 点赞操作失败:', error)
      
      // 回滚状态
      this.setData({
        isLiked: currentIsLiked,
        emojiData: this.data.emojiData
      })
      
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'error',
        duration: 2000
      })
    } finally {
      // 延迟重置loading状态
      setTimeout(() => {
        this.setData({
          likeLoading: false
        })
      }, 200)
    }
  },

  onToggleCollect(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 防重复点击
    if (this.data.collectLoading) {
      return
    }

    const emojiId = this.data.emojiData?.id
    if (!emojiId) {
      console.error('❌ 表情包ID不存在')
      return
    }

    // 获取当前状态
    const currentIsCollected = this.data.isCollected
    const currentCollects = parseInt(this.data.emojiData.collections) || 0
    
    // 计算新状态
    const newIsCollected = !currentIsCollected
    const newCollects = newIsCollected ? currentCollects + 1 : Math.max(0, currentCollects - 1)
    
    // 更新UI（乐观更新）
    const updatedEmojiData = {
      ...this.data.emojiData,
      collections: newCollects
    }

    // 一次性更新所有状态，避免闪动
    this.setData({
      isCollected: newIsCollected,
      emojiData: updatedEmojiData,
      collectLoading: true
    })

    try {
      // 使用StateManager更新状态
      StateManager.toggleCollect(emojiId)

      // 更新全局数据
      DataManager.updateCollections(emojiId, newCollects)

      // 触觉反馈
      wx.vibrateShort({ type: 'light' })

    } catch (error) {
      console.error('❌ 收藏操作失败:', error)
      
      // 回滚状态
      this.setData({
        isCollected: currentIsCollected,
        emojiData: this.data.emojiData
      })
      
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'error',
        duration: 2000
      })
    } finally {
      // 延迟重置loading状态
      setTimeout(() => {
        this.setData({
          collectLoading: false
        })
      }, 200)
    }
  },





  onDownload() {
    const imageUrl = this.data.emojiData.imageUrl
    const emojiId = this.data.emojiData.id

    wx.showLoading({
      title: '保存中...'
    })

    wx.downloadFile({
      url: imageUrl,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.hideLoading()

            // 记录下载历史
            this.recordDownload(emojiId)

            wx.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: () => {
            wx.hideLoading()
            wx.showToast({
              title: '保存失败',
              icon: 'error'
            })
          }
        })
      },
      fail: () => {
        wx.hideLoading()
        wx.showToast({
          title: '下载失败',
          icon: 'error'
        })
      }
    })
  },

  // 记录下载历史
  recordDownload(emojiId) {
    try {
      // 使用StateManager记录下载
      StateManager.recordDownload(emojiId)

      console.log(`✅ 表情包 ${emojiId} 下载记录已保存`)
    } catch (error) {
      console.error('❌ 保存下载记录失败:', error)
    }
  },

  // 记录最近浏览
  recordRecentView(emojiId) {
    try {
      let recentEmojis = wx.getStorageSync('recentEmojis') || []

      // 移除已存在的记录（避免重复）
      recentEmojis = recentEmojis.filter(id => id !== emojiId)

      // 添加到开头
      recentEmojis.unshift(emojiId)

      // 只保留最近20个
      if (recentEmojis.length > 20) {
        recentEmojis = recentEmojis.slice(0, 20)
      }

      wx.setStorageSync('recentEmojis', recentEmojis)
      console.log(`表情包 ${emojiId} 浏览记录已保存`)
    } catch (error) {
      console.error('保存浏览记录失败:', error)
    }
  },

  onShare() {
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  onShareToFriend() {
    // 触发分享
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  onRelatedTap(e) {
    const emojiId = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/detail/detail-new?id=${emojiId}`
    })
  },

  // 推荐区域点赞功能 - 暂时禁用
  onRelatedLike(e) {
    console.log('推荐区域点赞 - 已禁用')
  },

  // 推荐区域收藏功能 - 暂时禁用
  onRelatedCollect(e) {
    console.log('推荐区域收藏 - 已禁用')
  },

  onShareAppMessage() {
    return {
      title: this.data.emojiData.title,
      path: `/pages/detail/detail?id=${this.data.emojiData.id}`,
      imageUrl: this.data.emojiData.imageUrl
    }
  },

  // ========== 状态管理相关方法 ==========

  /**
   * 表情包状态变更回调
   * @param {Object} data - 状态变更数据
   */
  onEmojiStateChange(data) {
    console.log('📄 详情页接收到状态变更:', data)

    // 如果是当前表情包的状态变更，更新UI
    if (data.emojiId === this.data.emojiData?.id) {
      switch (data.type) {
        case 'like':
          this.setData({ isLiked: data.isLiked })
          break
        case 'collect':
          this.setData({ isCollected: data.isCollected })
          break
        case 'download':
          // 可以添加下载成功的提示
          break
      }
    }
  },

  // 添加状态操作辅助方法
  ...EmojiStateHelper,

  // 重写下载方法以适配详情页
  async onDownloadClick(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    const emojiData = this.data.emojiData
    if (!emojiData) {
      wx.showToast({
        title: '表情包信息不完整',
        icon: 'none'
      })
      return
    }

    // 使用下载管理器下载
    const success = await DownloadManager.downloadEmoji(emojiData)

    if (success) {
      // 记录下载状态
      StateManager.recordDownload(emojiData.id)

      // 触觉反馈
      wx.vibrateShort()

      // 更新下载数量（可选）
      const currentDownloads = parseInt(emojiData.downloads) || 0
      this.setData({
        'emojiData.downloads': currentDownloads + 1
      })
    }
  },

  // 创建测试表情包数据
  createTestEmojiData(id) {
    console.log('🎯 创建测试表情包数据，ID:', id)

    const testData = {
      id: id,
      _id: id,
      title: `测试表情包 ${id}`,
      imageUrl: `https://picsum.photos/300/300?random=${10 + parseInt(id)}`,
      description: `这是ID为${id}的测试表情包，用于调试显示效果`,
      category: '测试分类',
      tags: ['测试', '调试', `ID${id}`],
      likes: Math.floor(Math.random() * 1000) + 100,
      collections: Math.floor(Math.random() * 500) + 50,
      downloads: Math.floor(Math.random() * 2000) + 200,
      date: new Date().toISOString().split('T')[0],
      views: Math.floor(Math.random() * 5000) + 1000
    }

    console.log('✅ 测试数据创建完成:', testData)
    return testData
  },

  // 显示兜底内容
  showFallbackContent(id) {
    console.log('🆘 显示兜底内容，ID:', id)

    const fallbackData = {
      id: id,
      _id: id,
      title: '表情包加载超时',
      imageUrl: 'https://picsum.photos/300/300?random=999',
      description: '表情包加载超时，请检查网络连接或重试',
      category: '系统提示',
      tags: ['超时', '重试'],
      likes: 0,
      collections: 0,
      downloads: 0,
      date: new Date().toISOString().split('T')[0],
      views: 0
    }

    this.setData({
      emojiData: fallbackData,
      contentLoaded: true,
      buttonsLoaded: true,
      allLoaded: true
    })

    wx.showToast({
      title: '加载超时，显示兜底内容',
      icon: 'none',
      duration: 2000
    })
  },

  /**
   * 分享表情包
   */
  async onShare() {
    if (!this.data.emojiData) {
      wx.showToast({
        title: '表情包数据未加载',
        icon: 'none'
      })
      return
    }

    try {
      await ShareManager.shareEmoji(this.data.emojiData, {
        generateImage: false // 使用原图
      })
    } catch (error) {
      console.error('❌ 分享失败:', error)
      wx.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  },

  /**
   * 页面分享配置
   */
  onShareAppMessage() {
    if (this.data.emojiData) {
      return {
        title: `${this.data.emojiData.title} - 超有趣的表情包`,
        desc: '来自表情包小程序，发现更多有趣表情',
        path: `/pages/detail/detail?id=${this.data.emojiData.id}&from=share`,
        imageUrl: this.data.emojiData.imageUrl || this.data.emojiData.url
      }
    }

    return {
      title: '发现有趣的表情包',
      desc: '海量表情包，让聊天更有趣',
      path: '/pages/index/index?from=share'
    }
  },

  /**
   * 分享到朋友圈
   */
  async onShareToMoments() {
    if (!this.data.emojiData) {
      wx.showToast({
        title: '表情包数据未加载',
        icon: 'none'
      })
      return
    }

    try {
      await ShareManager.shareToMoments({
        type: 'emoji',
        id: this.data.emojiData.id,
        title: this.data.emojiData.title,
        imageUrl: this.data.emojiData.imageUrl || this.data.emojiData.url
      })
    } catch (error) {
      console.error('❌ 朋友圈分享失败:', error)
      wx.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  }
}

// 使用页面状态混入增强页面配置
Page(withPageState(pageConfig))