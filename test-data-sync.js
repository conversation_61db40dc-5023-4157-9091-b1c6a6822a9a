#!/usr/bin/env node

/**
 * 数据同步测试脚本
 * 测试首页数据加载的各种场景
 */

console.log('🚀 开始数据同步测试...\n')

// 模拟微信小程序环境
const mockWx = {
  cloud: {
    callFunction: async (options) => {
      console.log(`📡 模拟云函数调用: ${options.name}`, options.data)
      
      // 模拟云函数调用失败（100%失败率来测试兜底机制）
      throw new Error(`CloudServiceUot not found - 模拟云函数调用失败`)
      
      // 模拟成功响应
      return {
        result: {
          success: true,
          data: generateMockData(options.name, options.data)
        }
      }
    }
  }
}

// 生成模拟数据
function generateMockData(functionName, params) {
  switch (functionName) {
    case 'dataAPI':
      if (params.action === 'getCategories') {
        return [
          { id: 'funny', name: '搞笑幽默', icon: '😂', emojiCount: 25 },
          { id: 'cute', name: '可爱萌宠', icon: '🐱', emojiCount: 18 },
          { id: 'emotion', name: '情感表达', icon: '❤️', emojiCount: 22 },
          { id: 'festival', name: '节日庆典', icon: '🎉', emojiCount: 15 }
        ]
      } else if (params.action === 'getBanners') {
        return [
          {
            id: 'banner1',
            title: '热门表情包',
            subtitle: '最新最火的表情包都在这里',
            buttonText: '立即查看',
            imageUrl: '/images/banner1.jpg'
          }
        ]
      } else if (params.action === 'getEmojis') {
        return [
          {
            id: 'emoji1',
            title: '搞笑表情包1',
            imageUrl: '/images/emoji-1.jpg',
            likes: 1234,
            collections: 567
          },
          {
            id: 'emoji2',
            title: '可爱表情包2',
            imageUrl: '/images/emoji-2.jpg',
            likes: 2345,
            collections: 678
          }
        ]
      }
      break
  }
  return []
}

// 模拟数据管理器
const mockDataManager = {
  initLocalTestData: () => {
    console.log('📦 DataManager: 初始化本地测试数据')
    return [
      {
        id: 'local1',
        title: '本地测试表情包1',
        imageUrl: '/images/emoji-1.jpg',
        likes: 100,
        collections: 50
      },
      {
        id: 'local2',
        title: '本地测试表情包2',
        imageUrl: '/images/emoji-2.jpg',
        likes: 200,
        collections: 75
      }
    ]
  },
  getAllEmojiData: () => {
    return mockDataManager.initLocalTestData()
  },
  formatNumber: (num) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  }
}

// 模拟状态管理器
const mockStateManager = {
  getEmojiState: (id) => ({
    isLiked: false,
    isCollected: false
  })
}

// 模拟页面对象
const mockPage = {
  data: {
    hotCategories: [],
    bannerList: [],
    emojiList: []
  },
  pageReady: true,
  
  setData: function(data) {
    Object.assign(this.data, data)
    console.log('📝 setData 调用:', Object.keys(data))
  },
  
  // 分类颜色映射
  getCategoryColor: function(categoryName) {
    const colorMap = {
      '搞笑幽默': '#FF6B6B',
      '可爱萌宠': '#4ECDC4',
      '情感表达': '#45B7D1',
      '节日庆典': '#96CEB4',
      '网络热梗': '#FFEAA7',
      '动漫二次元': '#DDA0DD'
    }
    return colorMap[categoryName] || '#999999'
  },
  
  // 数字格式化
  formatNumber: function(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },
  
  // 创建兜底数据
  createFallbackEmojiData: function() {
    return [
      {
        id: 'fallback1',
        title: '兜底表情包1',
        imageUrl: '/images/emoji-1.jpg',
        likes: 999,
        collections: 123
      },
      {
        id: 'fallback2',
        title: '兜底表情包2',
        imageUrl: '/images/emoji-2.jpg',
        likes: 888,
        collections: 234
      }
    ]
  }
}

// 测试分类数据加载
async function testLoadCategoryData() {
  console.log('🧪 测试分类数据加载...')
  
  try {
    // 模拟云函数调用
    let categories = []
    let dataSource = 'unknown'

    try {
      const result = await mockWx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })

      if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
        categories = result.result.data
        dataSource = 'cloud'
        console.log('✅ 从云函数获取到分类数据:', categories.length, '个')
      }
    } catch (cloudError) {
      console.warn('⚠️ 云函数调用失败，使用本地数据:', cloudError.message)
      
      // 使用本地测试数据
      categories = [
        { id: 'funny', name: '搞笑幽默', icon: '😂', emojiCount: 25 },
        { id: 'cute', name: '可爱萌宠', icon: '🐱', emojiCount: 18 },
        { id: 'emotion', name: '情感表达', icon: '❤️', emojiCount: 22 },
        { id: 'festival', name: '节日庆典', icon: '🎉', emojiCount: 15 }
      ]
      dataSource = 'local'
    }

    if (categories.length > 0) {
      const hotCategories = categories.slice(0, 4).map((category, index) => ({
        ...category,
        id: category._id || category.id || `category_${index}`,
        name: category.name || '未知分类',
        icon: category.icon || '📁',
        color: mockPage.getCategoryColor(category.name || category._id || category.id),
        count: category.emojiCount || 0,
        emojiCount: category.emojiCount || 0,
        dataSource
      }))

      mockPage.setData({ hotCategories })
      console.log(`✅ 分类数据加载成功 (${dataSource}):`, hotCategories.length, '个')
      console.log('分类详情:', hotCategories.map(c => `${c.name}(${c.count})`).join(', '))
    }
    
  } catch (error) {
    console.error('❌ 分类数据加载失败:', error.message)
  }
  
  console.log('')
}

// 测试表情包数据加载
async function testLoadEmojiData() {
  console.log('🧪 测试表情包数据加载...')
  
  try {
    let emojis = []
    let dataSource = 'unknown'

    // 首先尝试云函数
    try {
      const result = await mockWx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: { category: 'all', page: 1, limit: 8 }
        }
      })

      if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
        emojis = result.result.data
        dataSource = 'cloud'
        console.log('✅ 从云函数获取到表情包数据:', emojis.length, '个')
      }
    } catch (cloudError) {
      console.warn('⚠️ 云函数调用失败，尝试本地数据:', cloudError.message)
    }

    // 如果云函数失败，尝试从DataManager获取本地测试数据
    if (emojis.length === 0) {
      try {
        const localEmojis = mockDataManager.initLocalTestData()
        if (localEmojis && localEmojis.length > 0) {
          emojis = localEmojis.slice(0, 8)
          dataSource = 'dataManager'
          console.log('✅ 从DataManager获取到表情包数据:', emojis.length, '个')
        }
      } catch (dataManagerError) {
        console.warn('⚠️ DataManager获取失败:', dataManagerError.message)
      }
    }

    // 如果还是没有数据，使用兜底数据
    if (emojis.length === 0) {
      emojis = mockPage.createFallbackEmojiData()
      dataSource = 'fallback'
      console.log('✅ 使用兜底表情包数据:', emojis.length, '个')
    }

    if (emojis.length > 0) {
      const emojiList = emojis.map((emoji, index) => ({
        ...emoji,
        id: emoji._id || emoji.id || `emoji_${index}`,
        title: emoji.title || emoji.name || '未知表情包',
        imageUrl: emoji.imageUrl || emoji.url || '',
        likes: emoji.likes || 0,
        collections: emoji.collections || 0,
        likesText: mockPage.formatNumber(emoji.likes || 0),
        collectionsText: mockPage.formatNumber(emoji.collections || 0),
        isLiked: false,
        isCollected: false,
        dataSource
      }))

      mockPage.setData({ emojiList })
      console.log(`✅ 表情包数据加载成功 (${dataSource}):`, emojiList.length, '个')
      console.log('表情包详情:', emojiList.map(e => `${e.title}(👍${e.likesText})`).join(', '))
    }
    
  } catch (error) {
    console.error('❌ 表情包数据加载失败:', error.message)
  }
  
  console.log('')
}

// 运行测试
async function runTests() {
  console.log('='.repeat(50))
  console.log('🧪 数据同步功能测试')
  console.log('='.repeat(50))
  console.log('')
  
  await testLoadCategoryData()
  await testLoadEmojiData()
  
  console.log('='.repeat(50))
  console.log('📊 最终页面数据状态:')
  console.log('='.repeat(50))
  console.log('热门分类:', mockPage.data.hotCategories.length, '个')
  console.log('表情包列表:', mockPage.data.emojiList.length, '个')
  console.log('')
  console.log('✅ 测试完成！数据加载功能正常工作')
  console.log('💡 即使云函数调用失败，也能通过本地数据和兜底机制保证页面正常显示')
}

// 启动测试
runTests().catch(console.error)
