// 系统集成测试脚本
// 验证优化后的系统功能完整性

const fs = require('fs');
const path = require('path');

class SystemIntegrationTest {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  // 测试1: 验证小程序管理页面已删除
  testAdminPageRemoval() {
    console.log('🧪 测试1: 验证小程序管理页面已删除');
    
    const adminPagePath = 'pages/admin/admin.js';
    const adminWxmlPath = 'pages/admin/admin.wxml';
    const adminWxssPath = 'pages/admin/admin.wxss';
    
    const adminPageExists = fs.existsSync(adminPagePath);
    const adminWxmlExists = fs.existsSync(adminWxmlPath);
    const adminWxssExists = fs.existsSync(adminWxssPath);
    
    if (!adminPageExists && !adminWxmlExists && !adminWxssExists) {
      this.testResults.push('✅ 小程序管理页面已成功删除');
    } else {
      this.errors.push('❌ 小程序管理页面未完全删除');
    }
    
    // 检查app.json中的页面配置
    try {
      const appJsonContent = fs.readFileSync('app.json', 'utf8');
      const appJson = JSON.parse(appJsonContent);
      
      const hasAdminPage = appJson.pages.includes('pages/admin/admin');
      if (!hasAdminPage) {
        this.testResults.push('✅ app.json中的管理页面配置已删除');
      } else {
        this.errors.push('❌ app.json中仍存在管理页面配置');
      }
    } catch (error) {
      this.errors.push('❌ 无法读取app.json文件');
    }
  }

  // 测试2: 验证冗余初始化脚本已删除
  testInitScriptCleanup() {
    console.log('🧪 测试2: 验证冗余初始化脚本已删除');
    
    const deletedScripts = [
      'emergency-init.js',
      'init-data-simple.js',
      'utils/databaseInit.js'
    ];
    
    let allDeleted = true;
    deletedScripts.forEach(script => {
      if (fs.existsSync(script)) {
        this.errors.push(`❌ 冗余脚本未删除: ${script}`);
        allDeleted = false;
      }
    });
    
    if (allDeleted) {
      this.testResults.push('✅ 所有冗余初始化脚本已删除');
    }
    
    // 检查保留的核心脚本
    if (fs.existsSync('database-init.js')) {
      this.testResults.push('✅ 核心初始化脚本已保留');
      
      // 检查脚本内容是否已优化
      try {
        const scriptContent = fs.readFileSync('database-init.js', 'utf8');
        if (!scriptContent.includes('测试数据') && !scriptContent.includes('示例数据')) {
          this.testResults.push('✅ 初始化脚本已移除测试数据创建');
        } else {
          this.errors.push('❌ 初始化脚本仍包含测试数据创建');
        }
      } catch (error) {
        this.errors.push('❌ 无法读取database-init.js文件');
      }
    } else {
      this.errors.push('❌ 核心初始化脚本不存在');
    }
  }

  // 测试3: 验证全量替换云函数已删除
  testSyncFunctionCleanup() {
    console.log('🧪 测试3: 验证全量替换云函数已删除');
    
    const deletedFunctions = [
      'cloudfunctions/syncData',
      'cloudfunctions/syncAdminData'
    ];
    
    let allDeleted = true;
    deletedFunctions.forEach(func => {
      if (fs.existsSync(func)) {
        this.errors.push(`❌ 冗余云函数未删除: ${func}`);
        allDeleted = false;
      }
    });
    
    if (allDeleted) {
      this.testResults.push('✅ 所有冗余全量替换云函数已删除');
    }
  }

  // 测试4: 验证管理后台调用已更新
  testAdminBackendUpdates() {
    console.log('🧪 测试4: 验证管理后台调用已更新');
    
    try {
      const mainHtmlPath = 'admin-serverless/main.html';
      if (fs.existsSync(mainHtmlPath)) {
        const content = fs.readFileSync(mainHtmlPath, 'utf8');
        
        // 检查是否还有对syncData云函数的直接调用
        const hasSyncDataCalls = content.includes("'syncData'") && 
                                !content.includes("action: 'syncData'");
        
        if (!hasSyncDataCalls) {
          this.testResults.push('✅ 管理后台已更新为使用webAdminAPI');
        } else {
          this.errors.push('❌ 管理后台仍存在对syncData云函数的直接调用');
        }
        
        // 检查是否正确使用webAdminAPI
        const hasWebAdminAPICalls = content.includes("'webAdminAPI'") && 
                                   content.includes("action: 'syncData'");
        
        if (hasWebAdminAPICalls) {
          this.testResults.push('✅ 管理后台正确使用webAdminAPI进行数据同步');
        } else {
          this.errors.push('❌ 管理后台未正确配置webAdminAPI调用');
        }
      } else {
        this.errors.push('❌ 管理后台主文件不存在');
      }
    } catch (error) {
      this.errors.push('❌ 无法读取管理后台文件');
    }
  }

  // 测试5: 验证数据创建来源统一化
  testDataSourceUnification() {
    console.log('🧪 测试5: 验证数据创建来源统一化');
    
    // 检查是否只保留了Web管理后台作为数据创建来源
    const webAdminExists = fs.existsSync('admin-serverless/main.html');
    const adminPageDeleted = !fs.existsSync('pages/admin/admin.js');
    const redundantScriptsDeleted = !fs.existsSync('emergency-init.js') && 
                                   !fs.existsSync('init-data-simple.js');
    
    if (webAdminExists && adminPageDeleted && redundantScriptsDeleted) {
      this.testResults.push('✅ 数据创建来源已成功统一化为Web管理后台');
    } else {
      this.errors.push('❌ 数据创建来源统一化不完整');
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始系统集成测试...\n');
    
    this.testAdminPageRemoval();
    this.testInitScriptCleanup();
    this.testSyncFunctionCleanup();
    this.testAdminBackendUpdates();
    this.testDataSourceUnification();
    
    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(50));
    
    if (this.testResults.length > 0) {
      console.log('\n✅ 成功的测试:');
      this.testResults.forEach(result => console.log(`  ${result}`));
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 失败的测试:');
      this.errors.forEach(error => console.log(`  ${error}`));
    }
    
    const totalTests = this.testResults.length + this.errors.length;
    const successRate = ((this.testResults.length / totalTests) * 100).toFixed(1);
    
    console.log('\n📈 测试统计:');
    console.log(`  总测试数: ${totalTests}`);
    console.log(`  成功: ${this.testResults.length}`);
    console.log(`  失败: ${this.errors.length}`);
    console.log(`  成功率: ${successRate}%`);
    
    if (this.errors.length === 0) {
      console.log('\n🎉 所有测试通过！系统优化成功！');
      return true;
    } else {
      console.log('\n⚠️ 存在测试失败，需要进一步修复');
      return false;
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new SystemIntegrationTest();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = SystemIntegrationTest;
