<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单连接测试</title>
    <style>
        body { font-family: monospace; background: #000; color: #0f0; padding: 20px; }
        button { background: #333; color: #0f0; border: 1px solid #0f0; padding: 10px; margin: 5px; }
        #log { background: #111; border: 1px solid #333; padding: 10px; height: 300px; overflow-y: auto; }
        .success { color: #0f0; }
        .error { color: #f00; }
    </style>
</head>
<body>
    <h1>🔗 云开发连接测试</h1>
    <p>环境: cloud1-5g6pvnpl88dc0142</p>
    
    <button onclick="test1()">测试1: 基础连接</button>
    <button onclick="test2()">测试2: 获取统计</button>
    <button onclick="test3()">测试3: 获取分类</button>
    <button onclick="clearLog()">清空</button>
    
    <div id="log"></div>

    <script>
        const apiUrl = 'https://cloud1-5g6pvnpl88dc0142.service.tcloudbase.com/adminAPI';
        
        function log(msg, type = 'info') {
            const div = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            div.innerHTML += `<div class="${type}">[${time}] ${msg}</div>`;
            div.scrollTop = div.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function test1() {
            log('🔗 测试基础连接...');
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getStats' })
                });
                
                log(`状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 连接成功!`, 'success');
                    log(`数据: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`❌ 连接失败: ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 错误: ${error.message}`, 'error');
            }
        }
        
        async function test2() {
            log('📊 测试获取统计数据...');
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getStats' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 统计数据获取成功!`, 'success');
                    log(`结果: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 错误: ${error.message}`, 'error');
            }
        }
        
        async function test3() {
            log('📋 测试获取分类数据...');
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getCategoryList' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 分类数据获取成功!`, 'success');
                    log(`结果: ${JSON.stringify(result, null, 2)}`, 'success');
                    
                    if (result.data && Array.isArray(result.data)) {
                        log(`🎯 找到 ${result.data.length} 个分类`, 'success');
                    }
                } else {
                    log(`❌ 获取失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 错误: ${error.message}`, 'error');
            }
        }
        
        log('🌐 页面加载完成，请点击按钮测试');
    </script>
</body>
</html>
