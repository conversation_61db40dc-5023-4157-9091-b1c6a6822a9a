/**
 * 全局错误处理器
 * 统一处理应用中的各种错误，提供用户友好的错误提示和重试机制
 */

const ErrorHandler = {
  // 错误类型定义
  ERROR_TYPES: {
    NETWORK: 'network',
    AUTH: 'auth',
    DATA: 'data',
    PERMISSION: 'permission',
    VALIDATION: 'validation',
    SYSTEM: 'system',
    UNKNOWN: 'unknown'
  },

  // 错误级别定义
  ERROR_LEVELS: {
    INFO: 'info',
    WARNING: 'warning',
    ERROR: 'error',
    CRITICAL: 'critical'
  },

  // 错误统计
  _errorStats: {
    total: 0,
    byType: {},
    byLevel: {},
    recent: []
  },

  // 错误监听器
  _listeners: [],

  /**
   * 初始化错误处理器
   */
  init() {
    console.log('🛡️ 全局错误处理器初始化')
    
    // 监听小程序错误
    wx.onError((error) => {
      this.handleError({
        type: this.ERROR_TYPES.SYSTEM,
        level: this.ERROR_LEVELS.ERROR,
        message: error,
        source: 'wx.onError'
      })
    })

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      this.handleError({
        type: this.ERROR_TYPES.SYSTEM,
        level: this.ERROR_LEVELS.ERROR,
        message: res.reason,
        source: 'unhandledRejection'
      })
    })
  },

  /**
   * 处理错误
   * @param {Object} errorInfo - 错误信息
   */
  handleError(errorInfo) {
    const {
      type = this.ERROR_TYPES.UNKNOWN,
      level = this.ERROR_LEVELS.ERROR,
      message = '未知错误',
      source = 'unknown',
      data = {},
      showToUser = true,
      allowRetry = false,
      retryAction = null
    } = errorInfo

    // 记录错误
    this.logError({
      type,
      level,
      message,
      source,
      data,
      timestamp: new Date().toISOString()
    })

    // 更新错误统计
    this.updateErrorStats(type, level)

    // 通知监听器
    this.notifyListeners(errorInfo)

    // 显示用户提示
    if (showToUser) {
      this.showErrorToUser({
        type,
        level,
        message,
        allowRetry,
        retryAction
      })
    }
  },

  /**
   * 记录错误日志
   * @param {Object} errorLog - 错误日志
   */
  logError(errorLog) {
    console.error('❌ 错误记录:', errorLog)
    
    // 添加到最近错误列表
    this._errorStats.recent.unshift(errorLog)
    
    // 保持最近错误列表不超过100条
    if (this._errorStats.recent.length > 100) {
      this._errorStats.recent = this._errorStats.recent.slice(0, 100)
    }

    // 可以在这里添加错误上报逻辑
    this.reportErrorToServer(errorLog)
  },

  /**
   * 更新错误统计
   * @param {string} type - 错误类型
   * @param {string} level - 错误级别
   */
  updateErrorStats(type, level) {
    this._errorStats.total++
    this._errorStats.byType[type] = (this._errorStats.byType[type] || 0) + 1
    this._errorStats.byLevel[level] = (this._errorStats.byLevel[level] || 0) + 1
  },

  /**
   * 向用户显示错误
   * @param {Object} errorInfo - 错误信息
   */
  showErrorToUser(errorInfo) {
    const { type, level, message, allowRetry, retryAction } = errorInfo
    
    // 根据错误类型和级别决定显示方式
    const userMessage = this.getUserFriendlyMessage(type, message)
    
    if (level === this.ERROR_LEVELS.CRITICAL) {
      // 严重错误，显示模态框
      wx.showModal({
        title: '系统错误',
        content: userMessage,
        showCancel: allowRetry,
        confirmText: allowRetry ? '重试' : '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm && allowRetry && retryAction) {
            retryAction()
          }
        }
      })
    } else if (level === this.ERROR_LEVELS.ERROR) {
      // 一般错误，显示Toast
      wx.showToast({
        title: userMessage,
        icon: 'none',
        duration: 3000
      })
    } else if (level === this.ERROR_LEVELS.WARNING) {
      // 警告，显示短Toast
      wx.showToast({
        title: userMessage,
        icon: 'none',
        duration: 2000
      })
    }
  },

  /**
   * 获取用户友好的错误消息
   * @param {string} type - 错误类型
   * @param {string} originalMessage - 原始错误消息
   * @returns {string} 用户友好的消息
   */
  getUserFriendlyMessage(type, originalMessage) {
    const messageMap = {
      [this.ERROR_TYPES.NETWORK]: '网络连接异常，请检查网络设置',
      [this.ERROR_TYPES.AUTH]: '登录已过期，请重新登录',
      [this.ERROR_TYPES.DATA]: '数据加载失败，请稍后重试',
      [this.ERROR_TYPES.PERMISSION]: '权限不足，无法执行此操作',
      [this.ERROR_TYPES.VALIDATION]: '输入信息有误，请检查后重试',
      [this.ERROR_TYPES.SYSTEM]: '系统异常，请稍后重试',
      [this.ERROR_TYPES.UNKNOWN]: '操作失败，请稍后重试'
    }

    return messageMap[type] || originalMessage || '未知错误'
  },

  /**
   * 上报错误到服务器
   * @param {Object} errorLog - 错误日志
   */
  async reportErrorToServer(errorLog) {
    try {
      // 检查云开发是否已初始化
      const app = getApp()
      if (!app || !app.globalData.cloudInitialized) {
        console.warn('云开发未初始化，跳过错误上报')
        return
      }

      // 只上报严重错误和系统错误
      if (errorLog.level === this.ERROR_LEVELS.CRITICAL ||
          errorLog.type === this.ERROR_TYPES.SYSTEM) {

        // 检查是否存在reportError云函数
        if (!wx.cloud) {
          console.warn('wx.cloud 不可用，跳过错误上报')
          return
        }

        // 暂时禁用云函数错误上报，因为 reportError 云函数不存在
        console.log('📝 错误日志记录（本地）:', {
          level: errorLog.level,
          type: errorLog.type,
          message: errorLog.message,
          timestamp: errorLog.timestamp
        })

        // TODO: 当 reportError 云函数部署后，取消注释以下代码
        /*
        await wx.cloud.callFunction({
          name: 'reportError',
          data: {
            errorLog,
            userAgent: wx.getSystemInfoSync(),
            timestamp: Date.now()
          }
        })
        console.log('✅ 错误上报成功')
        */
      }
    } catch (error) {
      // 错误上报失败不应该影响正常功能，只记录日志
      console.warn('❌ 错误上报失败:', error.message || error)
    }
  },

  /**
   * 添加错误监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this._listeners.push(listener)
  },

  /**
   * 移除错误监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this._listeners.indexOf(listener)
    if (index > -1) {
      this._listeners.splice(index, 1)
    }
  },

  /**
   * 通知错误监听器
   * @param {Object} errorInfo - 错误信息
   */
  notifyListeners(errorInfo) {
    this._listeners.forEach(listener => {
      try {
        listener(errorInfo)
      } catch (error) {
        console.error('❌ 错误监听器执行失败:', error)
      }
    })
  },

  /**
   * 创建错误处理装饰器
   * @param {Object} options - 选项
   * @returns {Function} 装饰器函数
   */
  createErrorDecorator(options = {}) {
    const {
      type = this.ERROR_TYPES.UNKNOWN,
      level = this.ERROR_LEVELS.ERROR,
      showToUser = true,
      allowRetry = false
    } = options

    return (target, propertyKey, descriptor) => {
      const originalMethod = descriptor.value

      descriptor.value = async function(...args) {
        try {
          return await originalMethod.apply(this, args)
        } catch (error) {
          ErrorHandler.handleError({
            type,
            level,
            message: error.message,
            source: `${target.constructor.name}.${propertyKey}`,
            data: { args },
            showToUser,
            allowRetry,
            retryAction: allowRetry ? () => originalMethod.apply(this, args) : null
          })
          
          throw error
        }
      }

      return descriptor
    }
  },

  /**
   * 包装异步函数，添加错误处理
   * @param {Function} asyncFn - 异步函数
   * @param {Object} options - 错误处理选项
   * @returns {Function} 包装后的函数
   */
  wrapAsync(asyncFn, options = {}) {
    return async (...args) => {
      try {
        return await asyncFn(...args)
      } catch (error) {
        this.handleError({
          ...options,
          message: error.message,
          source: asyncFn.name || 'anonymous',
          data: { args }
        })
        throw error
      }
    }
  },

  /**
   * 获取错误统计信息
   * @returns {Object} 错误统计
   */
  getErrorStats() {
    return {
      ...this._errorStats,
      recent: this._errorStats.recent.slice(0, 10) // 只返回最近10条
    }
  },

  /**
   * 清除错误统计
   */
  clearErrorStats() {
    this._errorStats = {
      total: 0,
      byType: {},
      byLevel: {},
      recent: []
    }
    console.log('🗑️ 错误统计已清除')
  },

  /**
   * 销毁错误处理器
   */
  destroy() {
    this._listeners = []
    this.clearErrorStats()
    console.log('🗑️ 全局错误处理器已销毁')
  }
}

module.exports = {
  ErrorHandler
}
