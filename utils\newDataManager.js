// 全局数据管理器 - 统一从云数据库获取数据
// 用于在不同页面间同步表情包数据

const { RequestOptimizer } = require('./requestOptimizer.js')
const { SmartCache } = require('./smartCache.js')
const { syncStatusManager } = require('./syncStatusManager.js')
const { realtimePerformanceMonitor } = require('./realtimePerformanceMonitor.js')

// 分类ID映射
const categoryIdMap = {
  'funny': '搞笑幽默',
  'cute': '可爱萌宠',
  'emotion': '情感表达',
  'festival': '节日庆典',
  'hot': '网络热梗',
  '2d': '动漫二次元'
}

// 数据缓存（增强版）
let dataCache = {
  emojis: new Map(),
  categories: null,
  banners: null,
  searchResults: new Map(),
  metadata: new Map(),
  lastUpdate: null,
  version: 1
}

// 缓存配置
const CACHE_CONFIG = {
  EXPIRE_TIME: 15 * 60 * 1000,    // 15分钟过期时间
  MAX_CACHE_SIZE: 200,            // 最大缓存数量
  CLEANUP_INTERVAL: 5 * 60 * 1000, // 5分钟清理间隔
  PRELOAD_THRESHOLD: 0.8,         // 预加载阈值
  COMPRESSION_ENABLED: true       // 启用数据压缩
}

// 预加载队列
let preloadQueue = []

// 数据一致性检查
let consistencyCheck = {
  enabled: true,
  interval: 30 * 60 * 1000, // 30分钟检查一次
  lastCheck: 0,
  timer: null
}

// 实时监听配置
let realtimeConfig = {
  enabled: true,
  watchers: new Map(),
  isConnected: false,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  reconnectDelay: 1000,
  lastNotificationTime: null,
  // 稳定性增强配置
  heartbeatInterval: 30000, // 30秒心跳检测
  connectionTimeout: 10000, // 10秒连接超时
  notificationTimeout: 5000, // 5秒通知处理超时
  maxConcurrentNotifications: 3, // 最大并发通知处理数
  enableCircuitBreaker: true, // 启用熔断器
  circuitBreakerThreshold: 5, // 熔断器阈值
  circuitBreakerResetTime: 60000 // 熔断器重置时间
}

// 数据管理器
const DataManager = {
  // 智能缓存辅助方法

  /**
   * 从智能缓存获取数据
   */
  getFromSmartCache(key, type = null) {
    return SmartCache.get(key, type)
  },

  /**
   * 设置智能缓存数据
   */
  setToSmartCache(key, value, type = null, ttl = null) {
    SmartCache.set(key, value, type, ttl)
  },

  /**
   * 批量获取智能缓存数据
   */
  getBatchFromSmartCache(keys, type = null) {
    return SmartCache.getBatch(keys, type)
  },

  /**
   * 批量设置智能缓存数据
   */
  setBatchToSmartCache(data, type = null, ttl = null) {
    SmartCache.setBatch(data, type, ttl)
  },

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return SmartCache.getStats()
  },

  // 检查缓存是否过期
  isCacheExpired() {
    if (!dataCache.lastUpdate) return true
    return Date.now() - dataCache.lastUpdate > CACHE_CONFIG.EXPIRE_TIME
  },

  // 检查特定缓存项是否过期
  isCacheItemExpired(cacheKey, customTTL = null) {
    const cache = dataCache.metadata.get(cacheKey)
    if (!cache) return true

    const ttl = customTTL || CACHE_CONFIG.EXPIRE_TIME
    return Date.now() - cache.timestamp > ttl
  },

  // 获取缓存统计信息
  getCacheStats() {
    return {
      emojisCount: dataCache.emojis.size,
      searchResultsCount: dataCache.searchResults.size,
      metadataCount: dataCache.metadata.size,
      lastUpdate: dataCache.lastUpdate,
      version: dataCache.version,
      memoryUsage: this.estimateCacheMemoryUsage()
    }
  },

  // 估算缓存内存使用量
  estimateCacheMemoryUsage() {
    try {
      const emojisSize = JSON.stringify([...dataCache.emojis.values()]).length
      const searchSize = JSON.stringify([...dataCache.searchResults.values()]).length
      const metadataSize = JSON.stringify([...dataCache.metadata.values()]).length

      return Math.round((emojisSize + searchSize + metadataSize) / 1024) // KB
    } catch (error) {
      return 0
    }
  },

  // 从云数据库获取表情包数据
  async getEmojiData(id) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojiDetail',
          data: { id }
        }
      })

      if (result.result && result.result.success) {
        return result.result.data
      }
      return null
    } catch (error) {
      console.error('获取表情包详情失败:', error)
      return null
    }
  },

  // 从缓存中获取表情包数据（同步方法）
  getEmojiDataFromCache(id) {
    // 遍历所有缓存的表情包数据
    for (const [cacheKey, emojis] of dataCache.emojis) {
      const emoji = emojis.find(item => (item._id || item.id) === id)
      if (emoji) {
        // 确保数据完整性
        return {
          ...emoji,
          id: emoji._id || emoji.id,
          likes: emoji.likes || 0,
          collections: emoji.collections || 0,
          downloads: emoji.downloads || 0
        }
      }
    }

    // 如果缓存中没有找到，尝试从云数据库获取
    console.warn(`表情包 ${id} 在缓存中未找到，尝试从云数据库获取`)
    this.getEmojiDataFromDatabase(id)

    // 返回 null，让调用方处理
    return null
  },

  // 从云数据库异步获取单个表情包数据
  async getEmojiDataFromDatabase(id) {
    try {
      console.log(`🔍 从云数据库获取表情包: ${id}`)
      const result = await this.callCloudFunctionWithRetry('dataAPI', {
        action: 'getEmojiDetail',
        data: { id }
      })

      if (result && result.success && result.data) {
        console.log(`✅ 成功获取表情包数据: ${result.data.title}`)

        // 将数据添加到缓存中
        const cacheKey = 'single_emoji_cache'
        if (!dataCache.emojis.has(cacheKey)) {
          dataCache.emojis.set(cacheKey, [])
        }

        const cachedEmojis = dataCache.emojis.get(cacheKey)
        // 检查是否已存在，避免重复
        const existingIndex = cachedEmojis.findIndex(item => (item._id || item.id) === id)
        if (existingIndex >= 0) {
          cachedEmojis[existingIndex] = result.data
        } else {
          cachedEmojis.push(result.data)
        }

        return result.data
      } else {
        console.warn(`⚠️ 表情包 ${id} 不存在或已被删除`)
        return null
      }
    } catch (error) {
      console.error(`❌ 获取表情包 ${id} 失败:`, error)
      return null
    }
  },

  // 获取所有表情包数据
  async getAllEmojiData(category = 'all', page = 1, limit = 20, options = {}) {
    const { forceRefresh = false, retryCount = 3, timeout = 10000 } = options

    try {
      const cacheKey = `${category}_${page}_${limit}`

      // 检查缓存（除非强制刷新）
      if (!forceRefresh && !this.isCacheExpired() && dataCache.emojis.has(cacheKey)) {
        console.log('📦 使用缓存数据:', cacheKey)
        return dataCache.emojis.get(cacheKey)
      }

      console.log('☁️ 从云数据库获取数据:', cacheKey)

      // 使用重试机制调用云函数
      const result = await this.callCloudFunctionWithRetry('dataAPI', {
        action: 'getEmojis',
        data: { category, page, limit }
      }, { retryCount, timeout })

      if (result && result.success) {
        const emojis = result.data || []
        console.log('✅ 获取表情包数据成功:', emojis.length, '个')

        // 更新缓存
        dataCache.emojis.set(cacheKey, emojis)
        dataCache.lastUpdate = Date.now()
        return emojis
      }

      console.warn('⚠️ 获取表情包数据失败:', result?.message)
      // 不使用兜底数据，返回空数组，确保只显示真实数据
      return []
    } catch (error) {
      console.error('❌ 获取表情包数据失败:', error)
      console.log('❌ 云函数调用失败，返回空数据（不使用兜底数据）')
      // 不使用兜底数据，返回空数组，确保只显示真实数据
      return []
    }
  },

  /**
   * 带重试机制的云函数调用（直接调用）
   * @param {string} name - 云函数名称
   * @param {Object} data - 调用参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 调用结果
   */
  async callCloudFunctionWithRetry(name, data, options = {}) {
    const { retryCount = 3, timeout = 15000 } = options // 增加超时时间到15秒

    console.log(`☁️ 调用云函数: ${name}`, data)

    // 检查云开发是否已初始化
    if (typeof wx === 'undefined' || !wx.cloud) {
      console.error('❌ 云开发未初始化')
      return { success: false, message: '云开发未初始化', data: [] }
    }

    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        console.log(`🔄 第 ${attempt + 1} 次尝试调用云函数: ${name}`)

        const result = await Promise.race([
          wx.cloud.callFunction({ name, data }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error(`云函数 ${name} 调用超时 (${timeout}ms)`)), timeout)
          )
        ])

        console.log(`✅ 云函数调用成功: ${name}`, result.result)
        return result.result
      } catch (error) {
        console.error(`❌ 第 ${attempt + 1} 次调用失败:`, error.message || error)

        // 如果是云函数不存在的错误，直接返回错误
        if (error.errCode === -501000 || error.message?.includes('FunctionName parameter could not be found')) {
          console.log(`❌ 云函数 ${name} 不存在`)
          return { success: false, message: `云函数 ${name} 不存在`, data: [] }
        }

        if (attempt === retryCount) {
          // 最后一次重试失败，返回错误
          console.log(`❌ 云函数 ${name} 调用失败`)
          return { success: false, message: `云函数 ${name} 调用失败`, data: [] }
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)))
      }
    }
  },



  // 根据分类ID获取表情包数据
  async getEmojiDataByCategory(categoryId, page = 1, limit = 20) {
    return await this.getAllEmojiData(categoryId, page, limit)
  },

  // 获取横幅数据
  async getBannersData(options = {}) {
    const { forceRefresh = false, retryCount = 3, timeout = 10000 } = options

    try {
      // 检查缓存（除非强制刷新）
      if (!forceRefresh && !this.isCacheExpired() && dataCache.banners) {
        console.log('📦 使用缓存的横幅数据')
        return dataCache.banners
      }

      console.log('☁️ 从云数据库获取横幅数据')

      // 使用重试机制调用云函数
      const result = await this.callCloudFunctionWithRetry('dataAPI', { action: 'getBanners' }, { retryCount, timeout })

      if (result && result.success) {
        const banners = result.data || []
        console.log('✅ 获取横幅数据成功:', banners.length, '个')

        // 更新缓存
        dataCache.banners = banners
        dataCache.lastUpdate = Date.now()
        return banners
      }

      console.warn('⚠️ 获取横幅数据失败:', result?.message)
      // 不使用默认数据，返回空数组，确保只显示真实数据
      return []
    } catch (error) {
      console.error('❌ 获取横幅数据失败:', error)
      console.log('❌ 云函数调用失败，返回空数据（不使用默认数据）')
      // 不使用默认数据，返回空数组，确保只显示真实数据
      return []
    }
  },

  // 获取分类数据（带统计）
  async getCategoriesWithStats(options = {}) {
    const { forceRefresh = false, retryCount = 3, timeout = 10000 } = options

    try {
      // 检查缓存（除非强制刷新）
      if (!forceRefresh && !this.isCacheExpired() && dataCache.categories) {
        console.log('📦 使用缓存的分类数据')
        return dataCache.categories
      }

      console.log('☁️ 从云数据库获取分类数据')

      // 使用重试机制调用云函数
      const result = await this.callCloudFunctionWithRetry('dataAPI', {
        action: 'getCategories'
      }, { retryCount, timeout })

      if (result && result.success) {
        const categories = result.data || []
        console.log('✅ 获取分类数据成功:', categories.length, '个')

        // 更新缓存
        dataCache.categories = categories
        dataCache.lastUpdate = Date.now()
        return categories
      }

      console.warn('⚠️ 获取分类数据失败:', result?.message)
      // 不使用默认数据，返回空数组，确保只显示真实数据
      return []
    } catch (error) {
      console.error('❌ 获取分类数据失败:', error)
      console.log('❌ 云函数调用失败，返回空数据（不使用默认数据）')
      // 不使用默认数据，返回空数组，确保只显示真实数据
      return []
    }
  },

  // 获取默认分类数据（兜底）- 不再提供虚拟数据
  getDefaultCategories() {
    console.warn('⚠️ getDefaultCategories被调用，但不再提供虚拟数据');
    // 不再提供默认数据，返回空数组，确保只显示真实数据
    return [];
  },

  // 搜索表情包
  async searchEmojis(keyword, page = 1, limit = 20, options = {}) {
    const { retryCount = 3, timeout = 10000, sortBy = 'relevance' } = options

    try {
      if (!keyword || keyword.trim() === '') {
        console.warn('⚠️ 搜索关键词为空')
        return { data: [], total: 0, hasMore: false }
      }

      console.log(`🔍 搜索表情包: "${keyword}", page=${page}, limit=${limit}, sortBy=${sortBy}`)

      // 使用重试机制调用云函数
      const result = await this.callCloudFunctionWithRetry('dataAPI', {
        action: 'searchEmojis',
        data: { keyword: keyword.trim(), page, limit, sortBy }
      }, { retryCount, timeout })

      if (result && result.success) {
        console.log('✅ 搜索成功:', result.data?.length || 0, '个结果')
        return {
          data: result.data || [],
          total: result.total || 0,
          hasMore: result.hasMore || false,
          keyword: result.keyword,
          page: result.page,
          limit: result.limit
        }
      }

      console.warn('⚠️ 搜索表情包失败:', result?.message)
      // 降级到本地搜索
      return this.performLocalSearchWithPagination(keyword, page, limit, sortBy)
    } catch (error) {
      console.error('❌ 搜索表情包失败:', error)
      // 降级到本地搜索
      return this.performLocalSearchWithPagination(keyword, page, limit, sortBy)
    }
  },





  // 计算本地搜索相关性评分
  calculateLocalRelevanceScore(emoji, keyword) {
    let score = 0
    const lowerKeyword = keyword.toLowerCase()

    // 标题匹配
    const title = (emoji.title || '').toLowerCase()
    if (title === lowerKeyword) {
      score += 100
    } else if (title.includes(lowerKeyword)) {
      score += 50
    }

    // 标签匹配
    if (emoji.tags && Array.isArray(emoji.tags)) {
      const tagMatches = emoji.tags.filter(tag =>
        tag.toLowerCase().includes(lowerKeyword)
      ).length
      score += tagMatches * 20
    }

    // 分类匹配
    const category = (emoji.category || '').toLowerCase()
    if (category.includes(lowerKeyword)) {
      score += 30
    }

    // 热度加分
    score += (emoji.likes || 0) * 0.1
    score += (emoji.downloads || 0) * 0.05

    return score
  },

  // 搜索结果排序
  sortSearchResults(results, sortBy) {
    switch (sortBy) {
      case 'likes':
        results.sort((a, b) => (b.likes || 0) - (a.likes || 0))
        break
      case 'downloads':
        results.sort((a, b) => (b.downloads || 0) - (a.downloads || 0))
        break
      case 'newest':
        results.sort((a, b) => {
          const aTime = new Date(a.createTime || 0).getTime()
          const bTime = new Date(b.createTime || 0).getTime()
          return bTime - aTime
        })
        break
      case 'relevance':
      default:
        results.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
        break
    }
  },

  // 获取分类图标
  getCategoryIcon(categoryId) {
    const iconMap = {
      'funny': '😂',
      'cute': '🐱',
      'emotion': '❤️',
      'festival': '🎉',
      'hot': '🔥',
      '2d': '🎭'
    }
    return iconMap[categoryId] || '📁'
  },

  // 更新点赞数（本地缓存）
  updateLikes(id, newLikes) {
    // 清除相关缓存，强制下次重新获取
    this.clearCache()
    console.log(`表情包 ${id} 点赞数更新为 ${newLikes}`)
  },

  // 更新收藏数（本地缓存）
  updateCollections(id, newCollections) {
    // 清除相关缓存，强制下次重新获取
    this.clearCache()
    console.log(`表情包 ${id} 收藏数更新为 ${newCollections}`)
  },

  // 清除缓存
  clearCache(type = 'all') {
    switch (type) {
      case 'emojis':
        dataCache.emojis.clear()
        console.log('📦 表情包缓存已清除')
        break
      case 'categories':
        dataCache.categories = null
        console.log('📦 分类缓存已清除')
        break
      case 'banners':
        dataCache.banners = null
        console.log('📦 横幅缓存已清除')
        break
      case 'all':
      default:
        dataCache.emojis.clear()
        dataCache.categories = null
        dataCache.banners = null
        dataCache.lastUpdate = null
        console.log('📦 所有数据缓存已清除')
        break
    }
  },

  // 获取缓存状态
  getCacheStatus() {
    return {
      emojiCacheSize: dataCache.emojis.size,
      hasCategoriesCache: !!dataCache.categories,
      hasBannersCache: !!dataCache.banners,
      lastUpdate: dataCache.lastUpdate,
      isExpired: this.isCacheExpired(),
      cacheAge: dataCache.lastUpdate ? Date.now() - dataCache.lastUpdate : null
    }
  },

  // 预热缓存
  async warmupCache() {
    try {
      console.log('🔥 开始预热缓存...')

      // 并行预加载关键数据
      await Promise.all([
        this.getCategoriesWithStats({ forceRefresh: true }),
        this.getAllEmojiData('all', 1, 20, { forceRefresh: true })
      ])

      console.log('✅ 缓存预热完成')
      return true
    } catch (error) {
      console.error('❌ 缓存预热失败:', error)
      return false
    }
  },

  // ========== 版本管理功能 ==========

  /**
   * 检查数据版本
   * @param {string} dataType - 数据类型
   * @returns {Promise<boolean>} 是否需要更新
   */
  async checkDataVersion(dataType) {
    try {
      console.log(`🔍 检查数据版本: ${dataType}`)

      const result = await this.callCloudFunctionWithRetry('dataAPI', {
        action: 'getDataVersion',
        dataType
      })

      if (!result || !result.success) {
        console.warn('⚠️ 版本检查失败:', result?.message)
        return false
      }

      const serverVersion = result.version
      const localVersion = this.getLocalVersion(dataType)

      console.log(`📋 版本对比: 本地=${localVersion}, 服务器=${serverVersion}`)

      if (serverVersion !== localVersion) {
        console.log('🔄 检测到数据版本变更，需要更新')
        this.setLocalVersion(dataType, serverVersion)
        return true
      }

      return false
    } catch (error) {
      console.error('❌ 版本检查失败:', error)
      return false
    }
  },

  /**
   * 获取本地版本
   * @param {string} dataType - 数据类型
   * @returns {string} 版本号
   */
  getLocalVersion(dataType) {
    try {
      const versionKey = `dataVersion_${dataType}`
      return wx.getStorageSync(versionKey) || '0.0.0'
    } catch (error) {
      console.error('获取本地版本失败:', error)
      return '0.0.0'
    }
  },

  /**
   * 设置本地版本
   * @param {string} dataType - 数据类型
   * @param {string} version - 版本号
   */
  setLocalVersion(dataType, version) {
    try {
      const versionKey = `dataVersion_${dataType}`
      wx.setStorageSync(versionKey, version)
      console.log(`📋 本地版本已更新: ${dataType} -> ${version}`)
    } catch (error) {
      console.error('设置本地版本失败:', error)
    }
  },

  /**
   * 强制更新数据版本
   * @param {string} dataType - 数据类型
   */
  async forceUpdateVersion(dataType) {
    try {
      console.log(`🔄 强制更新数据版本: ${dataType}`)

      // 清除本地版本，强制下次检查时更新
      const versionKey = `dataVersion_${dataType}`
      wx.removeStorageSync(versionKey)

      // 清除相关缓存
      this.clearCache(dataType === 'all' ? 'all' : dataType)

      console.log('✅ 版本强制更新完成')
    } catch (error) {
      console.error('❌ 强制更新版本失败:', error)
    }
  },

  // 格式化数字显示
  formatNumber(num) {
    // 处理 undefined、null 或非数字类型
    if (num === undefined || num === null || isNaN(num)) {
      return '0'
    }

    // 确保是数字类型
    const number = Number(num)

    if (number > 10000) {
      return (number / 10000).toFixed(1) + 'w'
    }
    return number.toString()
  },

  // 获取缓存信息（调试用）
  getCacheInfo() {
    return {
      lastUpdate: dataCache.lastUpdate,
      categories: dataCache.categories ? dataCache.categories.length : 0,
      banners: dataCache.banners ? dataCache.banners.length : 0,
      emojis: dataCache.emojis ? dataCache.emojis.size : 0,
      isExpired: this.isCacheExpired()
    }
  },



  // 测试数据创建已禁用 - 防止创建虚拟数据
  async initTestData() {
    console.log('⚠️ 测试数据创建已禁用，请使用管理后台手动添加数据')
    wx.showToast({
      title: '请使用管理后台添加数据',
      icon: 'none',
      duration: 3000
    })
    return false
  },



  // 数据预加载机制
  async preloadData() {
    console.log('🔄 开始数据预加载')

    // 检查基础环境
    if (typeof wx === 'undefined' || !wx.cloud) {
      console.log('❌ 云开发环境不可用')
      return
    }

    // 检查应用状态
    const app = getApp()
    if (!app.globalData.cloudInitialized) {
      console.log('❌ 云开发未初始化')
      return
    }

    try {
      // 简单测试云函数连通性
      const testResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })

      if (testResult && testResult.result) {
        console.log('✅ 云函数连通正常，开始预加载')

        // 预加载分类数据
        await this.preloadCategories()

        console.log('✅ 数据预加载完成')
      } else {
        throw new Error('云函数响应异常')
      }

    } catch (error) {
      console.error('❌ 数据预加载失败:', error.message || error)
    }
  },

  // 预加载热门表情包
  async preloadPopularEmojis() {
    try {
      // 获取热门表情包ID列表
      const popularIds = ['1', '2', '3', '4', '5'] // 暂时使用固定ID

      // 批量预加载
      const preloadPromises = popularIds.map(id =>
        this.getEmojiData(id).catch(error => {
          console.warn(`⚠️ 预加载表情包 ${id} 失败:`, error)
          return null
        })
      )

      await Promise.all(preloadPromises)
      console.log('✅ 热门表情包预加载完成')
    } catch (error) {
      console.error('❌ 热门表情包预加载失败:', error)
    }
  },

  // 预加载分类数据
  async preloadCategories() {
    try {
      await this.getCategoriesWithStats()
      console.log('✅ 分类数据预加载完成')
    } catch (error) {
      console.error('❌ 分类数据预加载失败:', error)
    }
  },

  // 数据一致性检查
  async performConsistencyCheck() {
    if (!consistencyCheck.enabled) return

    const now = Date.now()
    if (now - consistencyCheck.lastCheck < consistencyCheck.interval) {
      return
    }

    console.log('🔍 开始数据一致性检查')

    try {
      // 检查缓存数据完整性
      await this.checkCacheIntegrity()

      // 清理过期缓存
      this.cleanupExpiredCache()

      consistencyCheck.lastCheck = now
      console.log('✅ 数据一致性检查完成')
    } catch (error) {
      console.error('❌ 数据一致性检查失败:', error)
    }
  },

  // 检查缓存完整性
  async checkCacheIntegrity() {
    const stats = this.getCacheStats()

    // 检查缓存大小是否超限
    if (stats.emojisCount > CACHE_CONFIG.MAX_CACHE_SIZE) {
      console.log('⚠️ 表情包缓存超限，开始清理')
      this.cleanupOldestCache('emojis')
    }

    if (stats.searchResultsCount > CACHE_CONFIG.MAX_CACHE_SIZE / 2) {
      console.log('⚠️ 搜索结果缓存超限，开始清理')
      this.cleanupOldestCache('searchResults')
    }
  },

  // 清理过期缓存
  cleanupExpiredCache() {
    const now = Date.now()
    let cleanedCount = 0

    // 清理过期的搜索结果
    for (const [key, result] of dataCache.searchResults.entries()) {
      if (this.isCacheItemExpired(key)) {
        dataCache.searchResults.delete(key)
        cleanedCount++
      }
    }

    // 清理过期的元数据
    for (const [key, metadata] of dataCache.metadata.entries()) {
      if (now - metadata.timestamp > CACHE_CONFIG.EXPIRE_TIME) {
        dataCache.metadata.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 清理过期缓存: ${cleanedCount} 个`)
    }
  },

  // 清理最旧的缓存
  cleanupOldestCache(cacheType) {
    const cache = dataCache[cacheType]
    if (!cache || typeof cache.entries !== 'function') return

    const entries = [...cache.entries()]
    const sortedEntries = entries.sort((a, b) => {
      const aTime = dataCache.metadata.get(a[0])?.timestamp || 0
      const bTime = dataCache.metadata.get(b[0])?.timestamp || 0
      return aTime - bTime
    })

    // 删除最旧的25%
    const deleteCount = Math.floor(entries.length * 0.25)
    for (let i = 0; i < deleteCount; i++) {
      const [key] = sortedEntries[i]
      cache.delete(key)
      dataCache.metadata.delete(key)
    }

    console.log(`🧹 清理最旧缓存: ${deleteCount} 个`)
  },

  // 启动数据管理器
  async init() {
    console.log('🔄 DataManager 初始化开始')

    // 初始化智能缓存系统
    SmartCache.init({
      memory: {
        maxSize: CACHE_CONFIG.MAX_CACHE_SIZE,
        maxMemory: 30 * 1024 * 1024, // 30MB
        defaultTTL: CACHE_CONFIG.EXPIRE_TIME
      },
      temp: {
        maxSize: 50,
        maxMemory: 10 * 1024 * 1024, // 10MB
        defaultTTL: 5 * 60 * 1000    // 5分钟
      }
    })

    // 延迟启动数据预加载（等待云开发和数据库初始化完成）
    setTimeout(() => {
      this.preloadData()
    }, 5000)

    // 启动一致性检查定时器
    if (consistencyCheck.enabled) {
      consistencyCheck.timer = setInterval(() => {
        this.performConsistencyCheck()
      }, consistencyCheck.interval)
    }

    // 延迟启动实时监听（等待云开发完全初始化）
    setTimeout(() => {
      this.initRealtimeWatchers()
    }, 8000)

    console.log('✅ DataManager 初始化完成')
  },

  // 销毁数据管理器
  destroy() {
    if (consistencyCheck.timer) {
      clearInterval(consistencyCheck.timer)
      consistencyCheck.timer = null
    }

    // 停止实时监听
    this.stopRealtimeWatchers()

    // 清理所有缓存
    dataCache.emojis.clear()
    dataCache.searchResults.clear()
    dataCache.metadata.clear()

    // 清理智能缓存
    SmartCache.clear()

    console.log('🗑️ DataManager 已销毁')
  },

  // 实时监听相关方法

  /**
   * 初始化实时监听器
   */
  async initRealtimeWatchers() {
    if (!realtimeConfig.enabled) {
      console.log('⚠️ 实时监听已禁用')
      return
    }

    try {
      console.log('🔄 启动小程序端实时监听...')

      // 开始性能监控
      realtimePerformanceMonitor.startMonitoring()
      realtimePerformanceMonitor.recordConnectionAttempt()

      // 检查云开发是否可用
      if (typeof wx === 'undefined' || !wx.cloud) {
        throw new Error('云开发环境不可用')
      }

      // 检查应用状态
      const app = getApp()
      if (!app.globalData.cloudInitialized) {
        throw new Error('云开发未初始化')
      }

      const db = wx.cloud.database()

      // 监听同步通知
      await this.watchSyncNotifications(db)

      realtimeConfig.isConnected = true
      realtimeConfig.reconnectAttempts = 0

      console.log('✅ 小程序端实时监听已启动')
      this.notifyConnectionStatus('connected')
      syncStatusManager.updateConnectionStatus('connected')
      realtimePerformanceMonitor.recordConnectionSuccess()

      // 启动心跳检测
      this.startHeartbeat()

    } catch (error) {
      console.error('❌ 初始化实时监听失败:', error)
      realtimeConfig.isConnected = false
      this.notifyConnectionStatus('failed')
      syncStatusManager.updateConnectionStatus('error', { error: error.message })
      realtimePerformanceMonitor.recordConnectionFailure(error)
      this.scheduleReconnect()
    }
  },

  /**
   * 监听同步通知
   */
  async watchSyncNotifications(db) {
    try {
      const watcher = db.collection('sync_notifications')
        .orderBy('timestamp', 'desc')
        .limit(20)
        .watch({
          onChange: (snapshot) => {
            this.handleSyncNotification(snapshot)
          },
          onError: (error) => {
            console.error('❌ 同步通知监听失败:', error)
            this.handleWatchError('sync_notifications', error)
          }
        })

      realtimeConfig.watchers.set('sync_notifications', watcher)
      console.log('✅ 同步通知监听已启动')

    } catch (error) {
      console.error('❌ 启动同步通知监听失败:', error)
      throw error
    }
  },

  /**
   * 处理同步通知
   */
  handleSyncNotification(snapshot) {
    const { docs, type, docChanges } = snapshot

    if (type === 'init') {
      console.log('📡 同步通知监听器初始化完成，当前通知数量:', docs.length)
      return
    }

    // 处理文档变更
    if (docChanges && docChanges.length > 0) {
      docChanges.forEach(change => {
        const { queueType, doc } = change

        if (queueType === 'enqueue') {
          // 新增的通知
          this.processNotification(doc)
        }
      })
    } else {
      // 兼容处理：检查新通知
      docs.forEach(notification => {
        if (this.isNewNotification(notification)) {
          this.processNotification(notification)
        }
      })
    }
  },

  /**
   * 处理具体通知
   */
  processNotification(notification) {
    const startTime = Date.now()
    const { type, operation, timestamp, details } = notification

    console.log(`🔔 收到同步通知: ${type} - ${operation}`, notification)

    // 记录通知接收
    realtimePerformanceMonitor.recordNotificationReceived()

    try {
      // 根据数据类型刷新对应的缓存
      switch (type) {
        case 'categories':
          this.refreshCategoriesCache()
          break
        case 'emojis':
          this.refreshEmojisCache()
          break
        case 'banners':
          this.refreshBannersCache()
          break
      }

      // 通知页面更新
      this.notifyPageUpdate(type, operation, details)

      // 更新最后处理的通知时间
      this.updateLastNotificationTime(timestamp)

      // 记录处理完成
      const processingTime = Date.now() - startTime
      realtimePerformanceMonitor.recordNotificationProcessed(processingTime)

    } catch (error) {
      console.error('❌ 处理通知失败:', error)
      realtimePerformanceMonitor.recordError('notification_processing', error)
      throw error
    }
  },

  /**
   * 检查是否为新通知
   */
  isNewNotification(notification) {
    if (!realtimeConfig.lastNotificationTime) return true

    const notificationTime = new Date(notification.timestamp).getTime()
    const lastTime = new Date(realtimeConfig.lastNotificationTime).getTime()

    return notificationTime > lastTime
  },

  /**
   * 刷新分类缓存
   */
  async refreshCategoriesCache() {
    console.log('🔄 刷新分类缓存...')
    try {
      syncStatusManager.updateSyncStatus('categories', 'start')
      realtimePerformanceMonitor.recordCacheRefresh('categories')

      // 清除分类缓存
      dataCache.categories = null
      SmartCache.delete('categories_all')

      // 重新获取分类数据
      await this.getCategories(true) // 强制刷新
      console.log('✅ 分类缓存刷新完成')

      syncStatusManager.updateSyncStatus('categories', 'complete')
    } catch (error) {
      console.error('❌ 刷新分类缓存失败:', error)
      syncStatusManager.updateSyncStatus('categories', 'error', { error: error.message })
      realtimePerformanceMonitor.recordError('cache_refresh', error)
    }
  },

  /**
   * 刷新表情包缓存
   */
  async refreshEmojisCache() {
    console.log('🔄 刷新表情包缓存...')
    try {
      syncStatusManager.updateSyncStatus('emojis', 'start')

      // 清除表情包缓存
      dataCache.emojis.clear()
      SmartCache.clear('emojis')

      console.log('✅ 表情包缓存刷新完成')
      syncStatusManager.updateSyncStatus('emojis', 'complete')
    } catch (error) {
      console.error('❌ 刷新表情包缓存失败:', error)
      syncStatusManager.updateSyncStatus('emojis', 'error', { error: error.message })
    }
  },

  /**
   * 刷新横幅缓存
   */
  async refreshBannersCache() {
    console.log('🔄 刷新横幅缓存...')
    try {
      syncStatusManager.updateSyncStatus('banners', 'start')

      // 清除横幅缓存
      dataCache.banners = null
      SmartCache.delete('banners_all')

      // 重新获取横幅数据
      await this.getBanners(true) // 强制刷新
      console.log('✅ 横幅缓存刷新完成')

      syncStatusManager.updateSyncStatus('banners', 'complete')
    } catch (error) {
      console.error('❌ 刷新横幅缓存失败:', error)
      syncStatusManager.updateSyncStatus('banners', 'error', { error: error.message })
    }
  },

  /**
   * 通知页面更新
   */
  notifyPageUpdate(type, operation, details) {
    try {
      // 获取当前页面
      const pages = getCurrentPages()
      if (pages.length === 0) return

      const currentPage = pages[pages.length - 1]

      // 调用页面的数据更新方法
      if (typeof currentPage.onRealtimeDataUpdate === 'function') {
        currentPage.onRealtimeDataUpdate({
          type,
          operation,
          details,
          timestamp: new Date()
        })
      }

      // 触发全局事件
      if (typeof getApp === 'function') {
        const app = getApp()
        if (app.globalData && typeof app.globalData.onRealtimeUpdate === 'function') {
          app.globalData.onRealtimeUpdate({
            type,
            operation,
            details
          })
        }
      }

      console.log(`📢 已通知页面更新: ${type} - ${operation}`)
    } catch (error) {
      console.error('❌ 通知页面更新失败:', error)
    }
  },

  /**
   * 更新最后通知时间
   */
  updateLastNotificationTime(timestamp) {
    realtimeConfig.lastNotificationTime = timestamp
    // 可以考虑持久化存储
    try {
      wx.setStorageSync('lastNotificationTime', timestamp)
    } catch (error) {
      console.warn('⚠️ 保存最后通知时间失败:', error)
    }
  },

  /**
   * 处理监听错误
   */
  handleWatchError(watcherName, error) {
    console.error(`❌ 监听器错误 [${watcherName}]:`, error)
    realtimeConfig.isConnected = false
    this.notifyConnectionStatus('error')
    this.scheduleReconnect()
  },

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (realtimeConfig.reconnectAttempts >= realtimeConfig.maxReconnectAttempts) {
      console.error('❌ 达到最大重连次数，停止重连')
      this.notifyConnectionStatus('failed')
      realtimePerformanceMonitor.recordError('max_reconnect_reached', new Error('达到最大重连次数'))
      return
    }

    realtimeConfig.reconnectAttempts++
    realtimePerformanceMonitor.recordReconnectAttempt()

    const delay = realtimeConfig.reconnectDelay * Math.pow(2, realtimeConfig.reconnectAttempts - 1)

    console.log(`🔄 ${delay}ms后尝试第${realtimeConfig.reconnectAttempts}次重连...`)
    this.notifyConnectionStatus('reconnecting')
    syncStatusManager.updateConnectionStatus('connecting', {
      attempt: realtimeConfig.reconnectAttempts,
      maxAttempts: realtimeConfig.maxReconnectAttempts
    })

    setTimeout(() => {
      this.initRealtimeWatchers()
    }, delay)
  },

  /**
   * 停止所有实时监听
   */
  stopRealtimeWatchers() {
    console.log('🛑 停止实时监听...')

    // 停止心跳检测
    this.stopHeartbeat()

    realtimeConfig.watchers.forEach((watcher, name) => {
      try {
        watcher.close()
        console.log(`✅ 监听器已停止: ${name}`)
      } catch (error) {
        console.error(`❌ 停止监听器失败 [${name}]:`, error)
      }
    })

    realtimeConfig.watchers.clear()
    realtimeConfig.isConnected = false
    this.notifyConnectionStatus('disconnected')
    syncStatusManager.updateConnectionStatus('disconnected')
  },

  /**
   * 通知连接状态
   */
  notifyConnectionStatus(status) {
    try {
      // 获取当前页面
      const pages = getCurrentPages()
      if (pages.length === 0) return

      const currentPage = pages[pages.length - 1]

      // 调用页面的连接状态更新方法
      if (typeof currentPage.onRealtimeConnectionChange === 'function') {
        currentPage.onRealtimeConnectionChange({
          status,
          isConnected: realtimeConfig.isConnected,
          reconnectAttempts: realtimeConfig.reconnectAttempts
        })
      }

      console.log(`📡 连接状态: ${status}`)
    } catch (error) {
      console.error('❌ 通知连接状态失败:', error)
    }
  },

  /**
   * 获取实时监听状态
   */
  getRealtimeStatus() {
    return {
      enabled: realtimeConfig.enabled,
      isConnected: realtimeConfig.isConnected,
      reconnectAttempts: realtimeConfig.reconnectAttempts,
      watchersCount: realtimeConfig.watchers.size,
      lastNotificationTime: realtimeConfig.lastNotificationTime
    }
  },

  /**
   * 启用/禁用实时监听
   */
  setRealtimeEnabled(enabled) {
    realtimeConfig.enabled = enabled
    console.log(`实时监听已${enabled ? '启用' : '禁用'}`)

    if (enabled && !realtimeConfig.isConnected) {
      this.initRealtimeWatchers()
    } else if (!enabled && realtimeConfig.isConnected) {
      this.stopRealtimeWatchers()
    }
  },

  // 稳定性增强方法

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    if (realtimeConfig.heartbeatTimer) {
      clearInterval(realtimeConfig.heartbeatTimer)
    }

    realtimeConfig.heartbeatTimer = setInterval(() => {
      this.performHeartbeat()
    }, realtimeConfig.heartbeatInterval)

    console.log('💓 心跳检测已启动')
  },

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (realtimeConfig.heartbeatTimer) {
      clearInterval(realtimeConfig.heartbeatTimer)
      realtimeConfig.heartbeatTimer = null
      console.log('💓 心跳检测已停止')
    }
  },

  /**
   * 执行心跳检测
   */
  async performHeartbeat() {
    try {
      if (!realtimeConfig.isConnected) return

      // 检查连接状态
      const lastNotificationTime = realtimeConfig.lastNotificationTime
      const now = new Date()

      if (lastNotificationTime) {
        const timeSinceLastNotification = now - new Date(lastNotificationTime)

        // 如果超过5分钟没有收到通知，可能连接有问题
        if (timeSinceLastNotification > 300000) {
          console.log('⚠️ 长时间未收到通知，检查连接状态')
          realtimePerformanceMonitor.recordError('heartbeat_timeout', new Error('长时间未收到通知'))
        }
      }

      // 记录心跳
      realtimePerformanceMonitor.recordPerformanceData('heartbeat', {
        timestamp: now,
        isConnected: realtimeConfig.isConnected,
        watchersCount: realtimeConfig.watchers.size
      })

      console.log('💓 心跳检测正常')

    } catch (error) {
      console.error('❌ 心跳检测失败:', error)
      realtimePerformanceMonitor.recordError('heartbeat_error', error)
    }
  },

  /**
   * 获取性能监控报告
   */
  getPerformanceReport() {
    return realtimePerformanceMonitor.generateReport()
  },

  /**
   * 导出监控数据
   */
  exportMonitoringData() {
    return realtimePerformanceMonitor.exportData()
  },

  /**
   * 重置性能监控
   */
  resetPerformanceMonitoring() {
    realtimePerformanceMonitor.resetMetrics()
    console.log('📊 性能监控已重置')
  }
}

module.exports = {
  DataManager
}
