<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包标签显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        /* 模拟小程序的表情包列表样式 */
        .emoji-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .emoji-item {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .emoji-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
        }
        
        .emoji-image-container {
            position: relative;
            width: 100%;
            height: 200px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
        }
        
        .emoji-info {
            padding: 24px;
        }
        
        .emoji-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .emoji-category {
            font-size: 12px;
            color: #8B5CF6;
            font-weight: 500;
            margin-bottom: 20px;
            display: block;
        }
        
        /* 标签区域样式 */
        .emoji-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
        }
        
        .tag-item {
            padding: 4px 12px;
            background: #f0f0f0;
            border-radius: 12px;
            font-size: 11px;
            color: #666;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        
        .tag-item:hover {
            background: #e0e0e0;
            transform: scale(0.95);
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; color: #856404; }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .test-button:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ 表情包标签显示测试</h1>
        
        <div class="test-section">
            <div class="test-title">测试控制</div>
            <button class="test-button" onclick="testTagsDisplay()">测试标签显示</button>
            <button class="test-button" onclick="testEmptyTags()">测试空标签</button>
            <button class="test-button" onclick="testManyTags()">测试多标签</button>
            <button class="test-button" onclick="clearTest()">清空测试</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试日志</div>
            <div id="testLog" class="log">等待测试...</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">表情包列表预览</div>
            <div id="emojiList" class="emoji-list">
                <!-- 表情包列表将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.innerHTML += `<span class="${type}">${logEntry}</span>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearTest() {
            document.getElementById('testLog').innerHTML = '测试日志已清空...\n';
            document.getElementById('emojiList').innerHTML = '';
        }

        function createEmojiItem(emoji) {
            return `
                <div class="emoji-item">
                    <div class="emoji-image-container">
                        ${emoji.icon || '😀'}
                    </div>
                    <div class="emoji-info">
                        <div class="emoji-title">${emoji.title}</div>
                        <div class="emoji-category">${emoji.category}</div>
                        ${emoji.tags && emoji.tags.length > 0 ? `
                            <div class="emoji-tags">
                                ${emoji.tags.map(tag => `<div class="tag-item">${tag}</div>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function testTagsDisplay() {
            log('🏷️ 开始测试标签显示功能...', 'info');
            
            const testEmojis = [
                {
                    title: '搞笑表情包1',
                    category: '搞笑幽默',
                    icon: '😂',
                    tags: ['搞笑', '幽默', '表情']
                },
                {
                    title: '可爱萌宠2',
                    category: '可爱萌宠',
                    icon: '😍',
                    tags: ['可爱', '萌宠', '小动物', '治愈']
                },
                {
                    title: '情感表达3',
                    category: '情感表达',
                    icon: '❤️',
                    tags: ['爱情', '表白', '情感']
                },
                {
                    title: '节日庆祝4',
                    category: '节日庆祝',
                    icon: '🎉',
                    tags: ['节日', '庆祝', '生日', '新年', '祝福']
                }
            ];
            
            const emojiListHtml = testEmojis.map(emoji => createEmojiItem(emoji)).join('');
            document.getElementById('emojiList').innerHTML = emojiListHtml;
            
            log(`✅ 成功渲染 ${testEmojis.length} 个带标签的表情包`, 'success');
            log('📝 每个表情包都显示了对应的标签', 'info');
        }

        function testEmptyTags() {
            log('🔍 测试空标签情况...', 'info');
            
            const testEmojis = [
                {
                    title: '无标签表情包1',
                    category: '测试分类',
                    icon: '🤔',
                    tags: []
                },
                {
                    title: '无标签表情包2',
                    category: '测试分类',
                    icon: '😐',
                    tags: null
                },
                {
                    title: '无标签表情包3',
                    category: '测试分类',
                    icon: '😑'
                    // 没有tags字段
                }
            ];
            
            const emojiListHtml = testEmojis.map(emoji => createEmojiItem(emoji)).join('');
            document.getElementById('emojiList').innerHTML = emojiListHtml;
            
            log(`✅ 成功处理 ${testEmojis.length} 个无标签的表情包`, 'success');
            log('📝 无标签的表情包不显示标签区域', 'info');
        }

        function testManyTags() {
            log('🏷️ 测试多标签情况...', 'info');
            
            const testEmojis = [
                {
                    title: '多标签表情包',
                    category: '综合测试',
                    icon: '🎭',
                    tags: ['标签1', '标签2', '标签3', '标签4', '标签5', '很长的标签名称', '超级长的标签名称测试']
                },
                {
                    title: '正常标签表情包',
                    category: '对比测试',
                    icon: '😊',
                    tags: ['正常', '标签']
                }
            ];
            
            const emojiListHtml = testEmojis.map(emoji => createEmojiItem(emoji)).join('');
            document.getElementById('emojiList').innerHTML = emojiListHtml;
            
            log(`✅ 成功测试多标签显示`, 'success');
            log('📝 标签自动换行，长标签正常显示', 'info');
        }

        // 页面加载完成后自动运行一次测试
        window.onload = function() {
            log('🚀 页面加载完成，准备测试标签显示功能', 'info');
            setTimeout(testTagsDisplay, 1000);
        };
    </script>
</body>
</html>
