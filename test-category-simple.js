// 简化的分类管理功能测试
const { chromium } = require('playwright');

async function testCategorySimple() {
    console.log('📂 简化测试分类管理功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类') || text.includes('渐变') || text.includes('加载')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：进入分类管理页面');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(5000);
        
        console.log('\n📍 第二步：检查分类数据加载');
        
        // 检查分类容器内容
        const containerInfo = await page.evaluate(() => {
            const container = document.querySelector('#category-content');
            if (!container) return { found: false };
            
            const table = container.querySelector('table');
            const emptyState = container.querySelector('.empty-state');
            
            return {
                found: true,
                hasTable: !!table,
                hasEmptyState: !!emptyState,
                innerHTML: container.innerHTML.substring(0, 300),
                tableRowCount: table ? table.querySelectorAll('tbody tr').length : 0
            };
        });
        
        console.log('📊 分类容器检查结果:');
        console.log(`容器存在: ${containerInfo.found}`);
        console.log(`有表格: ${containerInfo.hasTable}`);
        console.log(`有空状态: ${containerInfo.hasEmptyState}`);
        console.log(`表格行数: ${containerInfo.tableRowCount}`);
        console.log(`容器内容预览: ${containerInfo.innerHTML}`);
        
        console.log('\n📍 第三步：直接查询分类数据');
        
        // 直接查询数据库中的分类数据
        const databaseCategories = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('categories');
                
                if (result.success && result.data) {
                    return {
                        success: true,
                        count: result.data.length,
                        categories: result.data.map(cat => ({
                            _id: cat._id,
                            name: cat.name,
                            color: cat.color,
                            gradient: cat.gradient,
                            backgroundColor: cat.backgroundColor,
                            status: cat.status,
                            hasName: !!cat.name,
                            hasColor: !!cat.color,
                            hasGradient: !!(cat.gradient || cat.backgroundColor)
                        }))
                    };
                } else {
                    return {
                        success: false,
                        error: result.error || '查询失败'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据库分类查询结果:');
        if (databaseCategories.success) {
            console.log(`分类总数: ${databaseCategories.count}`);
            
            databaseCategories.categories.forEach((cat, index) => {
                console.log(`\n分类 ${index + 1}:`);
                console.log(`  ID: ${cat._id}`);
                console.log(`  名称: ${cat.name} (存在: ${cat.hasName})`);
                console.log(`  颜色: ${cat.color} (存在: ${cat.hasColor})`);
                console.log(`  渐变: ${cat.gradient}`);
                console.log(`  背景色: ${cat.backgroundColor}`);
                console.log(`  状态: ${cat.status}`);
                console.log(`  有渐变数据: ${cat.hasGradient}`);
            });
        } else {
            console.log(`查询失败: ${databaseCategories.error}`);
        }
        
        console.log('\n📍 第四步：测试创建新分类');
        
        // 创建新分类
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            await page.waitForTimeout(3000);
            
            // 填写分类信息
            await page.fill('#category-name', '渐变测试分类');
            await page.fill('#category-description', '测试渐变背景色保存功能');
            
            // 检查颜色输入框
            const colorInputs = await page.evaluate(() => {
                const colorInput = document.querySelector('#category-color');
                const gradientInput = document.querySelector('#category-gradient');
                const backgroundInput = document.querySelector('#category-background');
                
                return {
                    hasColor: !!colorInput,
                    hasGradient: !!gradientInput,
                    hasBackground: !!backgroundInput,
                    colorType: colorInput ? colorInput.type : 'N/A',
                    gradientType: gradientInput ? gradientInput.type : 'N/A'
                };
            });
            
            console.log('\n📊 颜色输入框检查:');
            console.log(`颜色输入框: ${colorInputs.hasColor} (类型: ${colorInputs.colorType})`);
            console.log(`渐变输入框: ${colorInputs.hasGradient} (类型: ${colorInputs.gradientType})`);
            console.log(`背景输入框: ${colorInputs.hasBackground}`);
            
            // 设置颜色
            if (colorInputs.hasColor) {
                await page.fill('#category-color', '#FF6B6B');
                console.log('✅ 已设置主颜色');
            }
            
            if (colorInputs.hasGradient) {
                await page.fill('#category-gradient', '#4ECDC4');
                console.log('✅ 已设置渐变颜色');
            }
            
            // 设置状态
            await page.selectOption('#category-status', 'active');
            console.log('✅ 已设置状态为启用');
            
            // 保存分类
            const saveResult = await page.evaluate(() => {
                const modal = document.querySelector('[style*="position: fixed"]');
                const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
                
                if (submitBtn) {
                    submitBtn.click();
                    return { success: true };
                } else {
                    return { success: false, error: '未找到提交按钮' };
                }
            });
            
            if (saveResult.success) {
                console.log('✅ 已点击保存按钮');
                await page.waitForTimeout(8000);
                
                // 重新查询数据库验证保存结果
                const updatedCategories = await page.evaluate(async () => {
                    try {
                        const result = await CloudAPI.database.get('categories');
                        if (result.success && result.data) {
                            const latest = result.data[result.data.length - 1];
                            return {
                                success: true,
                                count: result.data.length,
                                latestCategory: {
                                    name: latest.name,
                                    color: latest.color,
                                    gradient: latest.gradient,
                                    backgroundColor: latest.backgroundColor,
                                    status: latest.status,
                                    isTestCategory: latest.name && latest.name.includes('渐变测试')
                                }
                            };
                        }
                        return { success: false, error: '查询失败' };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                });
                
                console.log('\n📊 保存后验证结果:');
                if (updatedCategories.success) {
                    console.log(`分类总数: ${updatedCategories.count}`);
                    
                    const latest = updatedCategories.latestCategory;
                    if (latest.isTestCategory) {
                        console.log('\n✅ 找到新创建的测试分类:');
                        console.log(`  名称: ${latest.name}`);
                        console.log(`  颜色: ${latest.color}`);
                        console.log(`  渐变: ${latest.gradient}`);
                        console.log(`  背景色: ${latest.backgroundColor}`);
                        console.log(`  状态: ${latest.status}`);
                        
                        const hasGradientData = !!(latest.gradient || latest.backgroundColor);
                        console.log(`  渐变数据保存: ${hasGradientData ? '✅ 成功' : '🔴 失败'}`);
                        
                        if (hasGradientData) {
                            console.log('🎉 分类渐变背景色功能正常！');
                        }
                    } else {
                        console.log('🔴 未找到新创建的测试分类');
                    }
                } else {
                    console.log(`验证失败: ${updatedCategories.error}`);
                }
            }
        } else {
            console.log('🔴 未找到添加分类按钮');
        }
        
        // 截图
        await page.screenshot({ path: 'category-simple-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: category-simple-test.png');
        
        console.log('\n🎯 分类管理简化测试总结:');
        
        const hasValidData = databaseCategories.success && databaseCategories.count > 0;
        const hasWorkingUI = containerInfo.found && (containerInfo.hasTable || containerInfo.hasEmptyState);
        
        console.log(`数据库数据: ${hasValidData ? '✅ 正常' : '🔴 异常'}`);
        console.log(`界面显示: ${hasWorkingUI ? '✅ 正常' : '🔴 异常'}`);
        
        if (hasValidData && hasWorkingUI) {
            console.log('\n🎉 分类管理基础功能正常！');
            return { success: true, allWorking: true };
        } else {
            console.log('\n⚠️ 分类管理功能存在问题');
            return { success: true, allWorking: false };
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-simple-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testCategorySimple().then(result => {
    console.log('\n🎯 分类管理简化测试最终结果:', result);
}).catch(console.error);
