// 完整的登录+测试脚本
const { chromium } = require('playwright');

async function testWithLogin() {
    console.log('🔐 完整的登录+测试流程...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理分类数据') || text.includes('分类数据加载') || text.includes('Web SDK') || text.includes('CloudBase')) {
            console.log('🔧', text);
        }
    });
    
    try {
        console.log('📍 步骤1: 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        console.log('\n📍 步骤2: 检查是否需要登录');
        
        // 检查是否显示登录表单
        const loginForm = await page.locator('form, .login-form, input[type="password"]').first();
        const isLoginVisible = await loginForm.isVisible().catch(() => false);
        
        if (isLoginVisible) {
            console.log('🔐 发现登录表单，开始登录...');
            
            // 填写用户名
            const usernameInput = await page.locator('input[placeholder*="用户名"], input[name="username"], input[type="text"]').first();
            if (await usernameInput.isVisible()) {
                await usernameInput.fill('admin');
                console.log('✅ 已填写用户名: admin');
            }
            
            // 填写密码
            const passwordInput = await page.locator('input[placeholder*="密码"], input[name="password"], input[type="password"]').first();
            if (await passwordInput.isVisible()) {
                await passwordInput.fill('admin123');
                console.log('✅ 已填写密码');
            }
            
            // 点击登录按钮
            const loginButton = await page.locator('button:has-text("登录"), input[type="submit"], .login-btn').first();
            if (await loginButton.isVisible()) {
                await loginButton.click();
                console.log('✅ 已点击登录按钮');
                
                // 等待登录完成
                await page.waitForTimeout(5000);
            }
        } else {
            console.log('ℹ️ 未发现登录表单，可能已经登录或不需要登录');
        }
        
        console.log('\n📍 步骤3: 等待系统初始化');
        
        // 等待更长时间让系统完全初始化
        await page.waitForTimeout(8000);
        
        console.log('\n📍 步骤4: 检查CloudBase初始化状态');
        
        const cloudbaseStatus = await page.evaluate(() => {
            return {
                hasCloudbase: typeof window.cloudbase !== 'undefined',
                hasTcbApp: typeof window.tcbApp !== 'undefined',
                hasCloudAPI: typeof CloudAPI !== 'undefined',
                cloudConfigInitialized: typeof CloudConfig !== 'undefined' ? CloudConfig.initialized : false,
                hasAdminApp: typeof AdminApp !== 'undefined'
            };
        });
        
        console.log('☁️ CloudBase状态:', JSON.stringify(cloudbaseStatus, null, 2));
        
        if (!cloudbaseStatus.cloudConfigInitialized) {
            console.log('⚠️ CloudBase未初始化，尝试手动初始化...');
            
            const initResult = await page.evaluate(async () => {
                try {
                    if (typeof CloudAPI !== 'undefined' && CloudAPI.init) {
                        await CloudAPI.init();
                        return { success: true, initialized: CloudConfig.initialized };
                    } else {
                        return { success: false, error: 'CloudAPI未定义' };
                    }
                } catch (error) {
                    return { success: false, error: error.message };
                }
            });
            
            console.log('🔧 初始化结果:', JSON.stringify(initResult, null, 2));
            
            if (initResult.success) {
                // 再等待一下
                await page.waitForTimeout(3000);
            }
        }
        
        console.log('\n📍 步骤5: 测试分类数据加载');
        
        const categoryTestResult = await page.evaluate(async () => {
            try {
                console.log('=== 开始分类数据测试 ===');
                
                // 检查CloudAPI状态
                if (!CloudConfig.initialized || !window.tcbApp) {
                    return { success: false, error: 'CloudBase未正确初始化' };
                }
                
                // 直接调用数据库API
                const rawResult = await CloudAPI.database.get('categories');
                console.log('原始数据库查询结果:', rawResult);
                
                if (rawResult.success && rawResult.data) {
                    console.log(`获取到 ${rawResult.data.length} 条原始数据`);
                    
                    // 手动处理数据看看
                    const processedData = rawResult.data.map((category, index) => {
                        const categoryData = category.data || category;
                        console.log(`处理第 ${index + 1} 条数据:`, {
                            id: category._id,
                            originalName: category.name,
                            dataName: categoryData.name,
                            hasData: !!category.data
                        });
                        
                        return {
                            _id: category._id,
                            name: categoryData.name,
                            icon: categoryData.icon || '📁',
                            status: categoryData.status || 'show'
                        };
                    });
                    
                    console.log('处理后的数据:', processedData);
                    
                    // 调用loadCategories函数
                    await loadCategories();
                    
                    return {
                        success: true,
                        rawCount: rawResult.data.length,
                        processedCount: processedData.length,
                        finalCount: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                        sampleData: AdminApp.data.categories ? AdminApp.data.categories.slice(0, 2) : []
                    };
                } else {
                    return { success: false, error: '数据库查询失败', result: rawResult };
                }
                
            } catch (error) {
                return { success: false, error: error.message, stack: error.stack };
            }
        });
        
        console.log('📊 分类测试结果:', JSON.stringify(categoryTestResult, null, 2));
        
        if (categoryTestResult.success && categoryTestResult.finalCount > 0) {
            console.log('\n✅ 分类数据加载成功！');
            
            console.log('\n📍 步骤6: 测试分类管理界面');
            
            // 点击分类管理
            const categoryLink = await page.locator('text=分类管理').first();
            if (await categoryLink.isVisible()) {
                await categoryLink.click();
                console.log('✅ 已点击分类管理');
                await page.waitForTimeout(3000);
                
                // 检查表格
                const tableRows = await page.locator('table tbody tr').count();
                console.log('📋 分类表格行数:', tableRows);
                
                if (tableRows > 0) {
                    // 检查前几行数据
                    for (let i = 0; i < Math.min(tableRows, 3); i++) {
                        const rowData = await page.locator('table tbody tr').nth(i).locator('td').allTextContents();
                        console.log(`📋 第${i + 1}行数据:`, rowData);
                        
                        if (rowData.some(cell => cell.includes('undefined'))) {
                            console.log('❌ 发现undefined数据');
                        }
                    }
                    
                    console.log('\n✅ 分类管理界面测试完成');
                } else {
                    console.log('⚠️ 分类表格没有数据');
                }
            } else {
                console.log('❌ 未找到分类管理链接');
            }
        } else {
            console.log('\n❌ 分类数据加载失败');
            if (categoryTestResult.error) {
                console.log('错误信息:', categoryTestResult.error);
            }
        }
        
        // 最终截图
        await page.screenshot({ path: 'complete-test-result.png', fullPage: true });
        console.log('\n📸 完整测试截图已保存: complete-test-result.png');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'complete-test-error.png' });
    } finally {
        // 不要立即关闭，让我们看看结果
        console.log('\n⏸️ 测试完成，浏览器将保持打开5秒供查看...');
        await page.waitForTimeout(5000);
        await browser.close();
    }
}

// 运行完整测试
testWithLogin().catch(console.error);
