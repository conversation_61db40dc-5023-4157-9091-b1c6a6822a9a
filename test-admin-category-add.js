// 自动化测试管理后台分类添加功能
const { chromium } = require('playwright');

async function testCategoryAdd() {
    console.log('🚀 开始测试管理后台分类添加功能...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // 慢速执行，便于观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        // 1. 打开管理后台
        console.log('📂 打开管理后台...');
        await page.goto('file:///e:/表情包-BOLT-符合微信云开发规范-7.22号-数据打通版开始迭代-917版本12/admin-serverless/main.html');

        // 等待页面基本加载
        await page.waitForTimeout(2000);

        // 2. 等待导航菜单可见
        console.log('⏳ 等待导航菜单加载...');
        await page.waitForSelector('.nav-link', { timeout: 10000 });

        // 3. 等待云开发初始化（检查是否有初始化完成的标志）
        console.log('⏳ 等待云开发初始化...');
        await page.waitForTimeout(3000);

        // 4. 点击分类管理
        console.log('📋 点击分类管理...');
        // 先等待元素可见
        await page.waitForSelector('a[onclick="showPage(\'category-management\')"]', {
            state: 'visible',
            timeout: 10000
        });
        await page.click('a[onclick="showPage(\'category-management\')"]');
        await page.waitForTimeout(2000);
        
        // 4. 点击添加分类按钮
        console.log('➕ 点击添加分类按钮...');
        await page.click('button[onclick="addCategoryAdvanced()"]');
        await page.waitForTimeout(1000);
        
        // 5. 填写分类信息
        console.log('✏️ 填写分类信息...');
        await page.fill('#category-name', '测试分类_' + Date.now());
        await page.fill('#category-sort', '999');
        await page.fill('#category-icon', '🧪');
        await page.fill('#category-description', '这是一个自动化测试创建的分类');
        
        // 6. 提交表单
        console.log('📤 提交表单...');
        await page.click('button[type="submit"]');
        
        // 7. 等待结果并检查
        console.log('⏳ 等待处理结果...');
        await page.waitForTimeout(5000);
        
        // 8. 检查是否有错误信息
        const errorMessages = await page.$$eval('.notification.error, .error', elements => 
            elements.map(el => el.textContent)
        );
        
        if (errorMessages.length > 0) {
            console.log('❌ 发现错误信息:', errorMessages);
            return false;
        }
        
        // 9. 检查是否有成功信息
        const successMessages = await page.$$eval('.notification.success, .success', elements => 
            elements.map(el => el.textContent)
        );
        
        if (successMessages.length > 0) {
            console.log('✅ 发现成功信息:', successMessages);
        }
        
        // 10. 检查控制台错误
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });
        
        await page.waitForTimeout(2000);
        
        if (consoleErrors.length > 0) {
            console.log('❌ 控制台错误:', consoleErrors);
            
            // 检查是否有triggerAutoSync错误
            const hasTriggerAutoSyncError = consoleErrors.some(error => 
                error.includes('triggerAutoSync is not a function')
            );
            
            if (hasTriggerAutoSyncError) {
                console.log('❌ 发现triggerAutoSync错误，修复未生效');
                return false;
            }
        }
        
        console.log('🎉 分类添加测试完成');
        return true;
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        return false;
    } finally {
        await browser.close();
    }
}

// 运行测试
testCategoryAdd().then(success => {
    if (success) {
        console.log('✅ 测试通过：triggerAutoSync修复成功');
        process.exit(0);
    } else {
        console.log('❌ 测试失败：需要进一步修复');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
});
