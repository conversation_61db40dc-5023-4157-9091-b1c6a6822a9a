// 测试分类管理功能
const { chromium } = require('playwright');

async function testCategoryManagement() {
    console.log('📂 开始分类管理测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类') || text.includes('渐变') || text.includes('保存') || text.includes('ERROR')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        console.log('📍 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录完成');
            await page.waitForTimeout(8000);
        }
        
        console.log('\n📍 进入分类管理');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            console.log('✅ 已进入分类管理页面');
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查现有分类数据');
        
        // 检查分类表格数据
        const categoryData = await page.evaluate(() => {
            // 确保查找正确的分类表格
            const categoryContainer = document.querySelector('#category-content');
            if (!categoryContainer) {
                return { error: '未找到分类容器' };
            }

            const categoryTable = categoryContainer.querySelector('table');
            if (!categoryTable) {
                return { error: '未找到分类表格', containerHTML: categoryContainer.innerHTML.substring(0, 200) };
            }

            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                tableFound: true,
                rowCount: rows.length,
                rows: rows.map((row, index) => {
                    const cells = Array.from(row.querySelectorAll('td'));
                    const nameCell = cells[2]; // 分类名称在第3列
                    const gradientCell = cells[3]; // 渐变预览在第4列
                    const statusCell = cells[6]; // 状态在第7列

                    return {
                        index: index + 1,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        gradient: gradientCell ? gradientCell.innerHTML : 'N/A',
                        status: statusCell ? statusCell.textContent.trim() : 'N/A',
                        hasGradientPreview: gradientCell ? gradientCell.innerHTML.includes('gradient') : false,
                        hasUndefined: cells.some(cell => cell.textContent.includes('undefined'))
                    };
                })
            };
        });
        
        console.log('📋 分类表格数据:');
        let hasIssues = false;
        
        categoryData.forEach((row, index) => {
            console.log(`\n  分类 ${index + 1}:`);
            console.log(`    数据: [${row.data.slice(0, 5).join(', ')}...]`);
            console.log(`    有undefined: ${row.hasUndefined}`);
            console.log(`    有渐变预览: ${row.hasGradient}`);
            
            if (row.hasUndefined) {
                console.log('    🔴 发现undefined数据');
                hasIssues = true;
            }
            
            if (!row.hasGradient) {
                console.log('    🔴 缺少渐变预览');
                hasIssues = true;
            }
        });
        
        if (!hasIssues) {
            console.log('\n✅ 分类数据显示正常');
        } else {
            console.log('\n🔴 分类数据有问题');
        }
        
        console.log('\n📍 测试创建新分类');
        
        // 点击添加分类按钮
        const addCategoryBtn = await page.locator('text=添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(2000);
        } else {
            console.log('❌ 未找到添加分类按钮');
            return;
        }
        
        // 填写分类信息
        console.log('📝 填写分类信息...');
        
        const nameInput = await page.locator('input[name="name"], input[placeholder*="名称"]').first();
        if (await nameInput.isVisible()) {
            await nameInput.fill('测试分类-渐变修复版');
            console.log('✅ 已填写分类名称');
        }
        
        const descInput = await page.locator('textarea[name="description"], textarea[placeholder*="描述"]').first();
        if (await descInput.isVisible()) {
            await descInput.fill('这是一个测试分类，用于验证渐变预览功能');
            console.log('✅ 已填写分类描述');
        }
        
        // 选择渐变类型
        const gradientSelect = await page.locator('select[name="gradientType"], .gradient-type-select').first();
        if (await gradientSelect.isVisible()) {
            await gradientSelect.selectOption('sunset');
            console.log('✅ 已选择渐变类型: sunset');
            await page.waitForTimeout(1000);
        }
        
        // 检查渐变预览是否显示
        const gradientPreview = await page.locator('.gradient-preview').first();
        const hasPreview = await gradientPreview.isVisible();
        console.log(`渐变预览显示: ${hasPreview}`);
        
        // 保存分类
        const saveBtn = await page.locator('button:has-text("保存"), button:has-text("确定")').first();
        if (await saveBtn.isVisible()) {
            await saveBtn.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 验证分类创建结果');
        
        // 重新检查分类列表
        const updatedCategories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[1]; // 名称列
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent?.trim() : 'N/A',
                    isNewCategory: nameCell ? nameCell.textContent?.includes('测试分类-渐变修复版') : false,
                    hasGradient: !!row.querySelector('.gradient-preview')
                };
            });
        });
        
        console.log('📊 更新后的分类列表:');
        let foundNewCategory = false;
        
        updatedCategories.forEach(category => {
            console.log(`\n分类 ${category.index}:`);
            console.log(`  名称: "${category.name}"`);
            console.log(`  是新分类: ${category.isNewCategory}`);
            console.log(`  有渐变预览: ${category.hasGradient}`);
            
            if (category.isNewCategory) {
                foundNewCategory = true;
                console.log('  ✅ 找到新创建的分类！');
                
                if (!category.hasGradient) {
                    console.log('  🔴 新分类缺少渐变预览');
                }
            }
        });
        
        if (foundNewCategory) {
            console.log('\n✅ 分类创建成功！');
        } else {
            console.log('\n❌ 分类创建失败或未找到新分类');
        }
        
        // 截图
        await page.screenshot({ path: 'category-management-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: category-management-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testCategoryManagement().catch(console.error);
