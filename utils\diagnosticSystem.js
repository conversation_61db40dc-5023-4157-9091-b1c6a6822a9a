// utils/diagnosticSystem.js - 系统诊断工具

/**
 * 系统诊断工具
 * 用于检查小程序各项功能的运行状态
 */
const DiagnosticSystem = {
  
  /**
   * 执行完整的系统诊断
   */
  async runDiagnosis() {
    console.log('=====================================')
    console.log('🔍 开始系统诊断...')
    console.log('=====================================')
    
    const diagnosticResults = {
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      },
      details: [],
      error: null,
      timestamp: new Date().toISOString()
    }
    
    try {
      // 1. 检查基础环境
      await this.checkBasicEnvironment(diagnosticResults)
      
      // 2. 检查数据管理器
      await this.checkDataManagers(diagnosticResults)
      
      // 3. 检查登录系统
      await this.checkAuthSystem(diagnosticResults)
      
      // 4. 检查数据同步
      await this.checkDataSync(diagnosticResults)
      
      // 5. 生成诊断摘要
      this.generateSummary(diagnosticResults)
      
      console.log('=====================================')
      console.log('🎉 诊断完成，结果摘要:')
      console.log('   通过项目:', diagnosticResults.summary.passed)
      console.log('   失败项目:', diagnosticResults.summary.failed)
      console.log('   警告项目:', diagnosticResults.summary.warnings)
      console.log('=====================================')
      
      return diagnosticResults
      
    } catch (error) {
      console.error('❌ 诊断过程异常:', error)
      diagnosticResults.error = error.message
      return diagnosticResults
    }
  },

  /**
   * 检查基础环境
   */
  async checkBasicEnvironment(results) {
    console.log('📱 检查基础环境...')
    
    // 检查微信小程序API
    this.addResult(results, 'wx对象', wx ? 'PASS' : 'FAIL', 
      wx ? '微信小程序API可用' : '微信小程序API不可用')
    
    // 检查本地存储
    try {
      wx.setStorageSync('diagnostic_test', 'test')
      const testValue = wx.getStorageSync('diagnostic_test')
      wx.removeStorageSync('diagnostic_test')
      
      this.addResult(results, '本地存储', testValue === 'test' ? 'PASS' : 'FAIL',
        testValue === 'test' ? '本地存储功能正常' : '本地存储功能异常')
    } catch (error) {
      this.addResult(results, '本地存储', 'FAIL', `本地存储错误: ${error.message}`)
    }
    
    // 检查云开发
    this.addResult(results, '云开发', wx.cloud ? 'WARNING' : 'FAIL',
      wx.cloud ? '云开发API存在但环境未配置' : '云开发API不可用')
  },

  /**
   * 检查数据管理器
   */
  async checkDataManagers(results) {
    console.log('📊 检查数据管理器...')
    
    try {
      // 检查DataManager
      const { DataManager } = require('./dataManager.js')
      this.addResult(results, 'DataManager', 'PASS', 'DataManager加载成功')
      
      // 检查数据获取
      const testData = DataManager.getAllEmojiData()
      this.addResult(results, '表情包数据', testData && testData.length > 0 ? 'PASS' : 'FAIL',
        `表情包数据: ${testData ? testData.length : 0} 条`)
      
    } catch (error) {
      this.addResult(results, 'DataManager', 'FAIL', `DataManager错误: ${error.message}`)
    }
    
    try {
      // 检查StateManager
      const { StateManager } = require('./stateManager.js')
      this.addResult(results, 'StateManager', 'PASS', 'StateManager加载成功')
      
      // 检查状态数据
      const likedCount = StateManager.getLikedEmojis().length
      const collectedCount = StateManager.getCollectedEmojis().length
      
      this.addResult(results, '用户状态数据', 'PASS', 
        `点赞: ${likedCount}, 收藏: ${collectedCount}`)
      
    } catch (error) {
      this.addResult(results, 'StateManager', 'FAIL', `StateManager错误: ${error.message}`)
    }
  },

  /**
   * 检查登录系统
   */
  async checkAuthSystem(results) {
    console.log('🔐 检查登录系统...')
    
    try {
      const { AuthManager } = require('./authManager.js')
      this.addResult(results, 'AuthManager', 'PASS', 'AuthManager加载成功')
      
      // 检查登录状态
      const currentUser = AuthManager.getCurrentUser()
      this.addResult(results, '登录状态', currentUser.isLoggedIn ? 'PASS' : 'WARNING',
        currentUser.isLoggedIn ? `已登录: ${currentUser.userInfo?.nickName}` : '未登录')
      
    } catch (error) {
      this.addResult(results, 'AuthManager', 'FAIL', `AuthManager错误: ${error.message}`)
    }
  },

  /**
   * 检查数据同步
   */
  async checkDataSync(results) {
    console.log('🔄 检查数据同步...')
    
    try {
      const { CloudDataService } = require('./cloudDataService.js')
      this.addResult(results, 'CloudDataService', 'PASS', 'CloudDataService加载成功')
      
      // 检查云端数据
      const cloudData = CloudDataService.getAllCloudData()
      const userCount = Object.keys(cloudData).length
      
      this.addResult(results, '云端数据', 'PASS', `云端用户数据: ${userCount} 个用户`)
      
    } catch (error) {
      this.addResult(results, 'CloudDataService', 'FAIL', `CloudDataService错误: ${error.message}`)
    }
  },

  /**
   * 添加诊断结果
   */
  addResult(results, item, status, message) {
    results.details.push({
      item,
      status,
      message,
      timestamp: new Date().toISOString()
    })
    
    // 输出到控制台
    const icon = status === 'PASS' ? '✅' : status === 'WARNING' ? '⚠️' : '❌'
    console.log(`${icon} ${item}: ${message}`)
  },

  /**
   * 生成诊断摘要
   */
  generateSummary(results) {
    results.summary = {
      passed: 0,
      failed: 0,
      warnings: 0
    }
    
    results.details.forEach(detail => {
      switch (detail.status) {
        case 'PASS':
          results.summary.passed++
          break
        case 'FAIL':
          results.summary.failed++
          break
        case 'WARNING':
          results.summary.warnings++
          break
      }
    })
  },

  /**
   * 获取诊断报告
   */
  generateReport(results) {
    let report = '# 系统诊断报告\n\n'
    report += `**诊断时间**: ${results.timestamp}\n\n`
    report += `**结果摘要**:\n`
    report += `- ✅ 通过: ${results.summary.passed}\n`
    report += `- ❌ 失败: ${results.summary.failed}\n`
    report += `- ⚠️ 警告: ${results.summary.warnings}\n\n`
    
    report += `**详细结果**:\n`
    results.details.forEach(detail => {
      const icon = detail.status === 'PASS' ? '✅' : detail.status === 'WARNING' ? '⚠️' : '❌'
      report += `${icon} **${detail.item}**: ${detail.message}\n`
    })
    
    if (results.error) {
      report += `\n**错误信息**: ${results.error}\n`
    }
    
    return report
  },

  /**
   * 快速健康检查
   */
  async quickHealthCheck() {
    console.log('🏥 快速健康检查...')
    
    const checks = [
      { name: '本地存储', check: () => wx.getStorageSync },
      { name: 'DataManager', check: () => require('./dataManager.js').DataManager },
      { name: 'StateManager', check: () => require('./stateManager.js').StateManager },
      { name: 'AuthManager', check: () => require('./authManager.js').AuthManager }
    ]
    
    let healthy = true
    
    checks.forEach(({ name, check }) => {
      try {
        check()
        console.log(`✅ ${name}: 正常`)
      } catch (error) {
        console.log(`❌ ${name}: 异常 - ${error.message}`)
        healthy = false
      }
    })
    
    console.log(healthy ? '🎉 系统健康状态良好' : '⚠️ 系统存在问题')
    return healthy
  }
}

module.exports = {
  DiagnosticSystem
}
