# 🚨 遇到问题先看这里！

## ⚡ 快速导航

### 🎯 标准配置和开发规范
**文件位置**: [技术文档库/🎯标准配置和开发规范-必读.md](./技术文档库/🎯标准配置和开发规范-必读.md)

**包含内容**:
- ✅ 云开发SDK的正确配置方式
- ✅ 身份认证的标准解决方案  
- ✅ 数据库操作的推荐方法
- ✅ 常见问题的快速修复方案

---

## 🔥 紧急情况快速解决

### SDK加载失败
```javascript
// 立即使用这个配置
<script src="https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js"></script>
<script>
const app = window.cloudbase.init({
    env: 'cloud1-5g6pvnpl88dc0142',
    clientId: 'cloud1-5g6pvnpl88dc0142'
});
</script>
```

### ACCESS_TOKEN_DISABLED错误
```javascript
// 立即使用webAdminAPI云函数
const result = await app.callFunction({
    name: 'webAdminAPI',
    data: {
        action: 'getCategoryList',
        adminPassword: 'admin123456'
    }
});
```

### 数据库权限被拒绝
```javascript
// 不要直接操作数据库，使用云函数
await app.callFunction({
    name: 'webAdminAPI',
    data: {
        action: 'addEmoji',
        data: emojiData,
        adminPassword: 'admin123456'
    }
});
```

---

## 📋 问题解决标准流程

1. **第一步**: 查看 [🎯标准配置和开发规范-必读.md](./技术文档库/🎯标准配置和开发规范-必读.md)
2. **第二步**: 复制标准配置，不要自己修改
3. **第三步**: 测试功能是否正常
4. **第四步**: 如果还有问题，查看技术文档库中的详细文档

---

## 🎯 核心原则

**永远使用项目中验证过的配置，不要尝试"优化"或"改进"**

- 使用webAdminAPI云函数，不要直接操作数据库
- 使用SDK 2.0版本，包含clientId参数
- 使用多CDN备用策略，确保加载成功
- 遇到问题先查文档，不要盲目尝试

---

**记住：标准配置文档包含了所有正确答案！**
