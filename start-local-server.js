/**
 * 快速启动本地HTTP服务器
 * 解决CORS跨域问题
 */

const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 8080;

// 启用CORS
app.use(cors());

// 设置静态文件目录
app.use(express.static(__dirname));

// 特殊路由处理
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin', 'index.html'));
});

app.get('/test', (req, res) => {
    res.sendFile(path.join(__dirname, 'sdk-fix-verification.html'));
});

app.get('/cors-fix', (req, res) => {
    res.sendFile(path.join(__dirname, 'cors-fix-guide.html'));
});

app.get('/db-check', (req, res) => {
    res.sendFile(path.join(__dirname, 'quick-database-check.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 本地服务器已启动！`);
    console.log(`📍 访问地址:`);
    console.log(`   - 主页: http://localhost:${PORT}`);
    console.log(`   - 管理后台: http://localhost:${PORT}/admin`);
    console.log(`   - SDK测试: http://localhost:${PORT}/test`);
    console.log(`   - CORS修复指南: http://localhost:${PORT}/cors-fix`);
    console.log(`   - 数据库检查: http://localhost:${PORT}/db-check`);
    console.log(`\n💡 请在云开发控制台的安全域名中添加: localhost:${PORT}`);
    console.log(`🔗 云开发控制台: https://console.cloud.tencent.com/tcb`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n👋 服务器已关闭');
    process.exit(0);
});
