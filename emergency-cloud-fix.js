// 🚨 紧急云开发问题修复脚本
// 在微信开发者工具控制台运行此脚本

console.log('🔧 开始紧急云开发问题诊断...')

const emergencyFix = {
  // 当前配置的环境ID
  currentEnvId: 'cloud1-5g6pvnpl88dc0142',
  
  // 诊断步骤
  async diagnose() {
    console.log('📊 开始系统诊断...')
    
    // 1. 检查云开发SDK是否可用
    if (typeof wx === 'undefined' || !wx.cloud) {
      console.error('❌ 微信云开发SDK不可用')
      return false
    }
    console.log('✅ 微信云开发SDK可用')
    
    // 2. 尝试初始化云开发
    try {
      wx.cloud.init({
        env: this.currentEnvId,
        traceUser: true
      })
      console.log('✅ 云开发初始化成功')
    } catch (error) {
      console.error('❌ 云开发初始化失败:', error)
      return false
    }
    
    // 3. 测试云函数连接
    await this.testCloudFunctions()
    
    // 4. 测试数据库连接
    await this.testDatabase()
    
    return true
  },
  
  // 测试云函数
  async testCloudFunctions() {
    console.log('🔍 测试云函数连接...')
    
    const testFunctions = ['dataAPI', 'getCategories', 'getEmojiList']
    
    for (const funcName of testFunctions) {
      try {
        console.log(`   测试 ${funcName}...`)
        const result = await wx.cloud.callFunction({
          name: funcName,
          data: { action: 'ping' }
        })
        console.log(`   ✅ ${funcName} 连接成功:`, result.result)
      } catch (error) {
        console.error(`   ❌ ${funcName} 连接失败:`, error.message)
        
        if (error.errCode === -9998) {
          console.log(`   💡 ${funcName} 可能未部署，请在微信开发者工具中部署`)
        }
      }
    }
  },
  
  // 测试数据库连接和读取已有数据
  async testDatabase() {
    console.log('🗄️ 测试读取你已创建的数据...')

    try {
      const db = wx.cloud.database()

      // 测试读取分类数据
      const categories = await db.collection('categories').limit(5).get()
      console.log('✅ 读取到分类数据:', categories.data.length, '条')
      if (categories.data.length > 0) {
        console.log('   分类示例:', categories.data[0].name || categories.data[0])
      }

      // 测试读取表情包数据
      const emojis = await db.collection('emojis').limit(5).get()
      console.log('✅ 读取到表情包数据:', emojis.data.length, '条')
      if (emojis.data.length > 0) {
        console.log('   表情包示例:', emojis.data[0].name || emojis.data[0])
      }

    } catch (error) {
      console.error('❌ 数据库读取失败:', error.message)

      if (error.errCode === -9998) {
        console.log('💡 云函数可能未部署，请先部署 dataAPI 云函数')
      }
    }
  },
  
  // 快速修复 - 只测试读取你的数据
  async quickFix() {
    console.log('🔧 开始测试读取你已创建的数据...')

    // 1. 重新初始化云开发
    try {
      wx.cloud.init({
        env: this.currentEnvId,
        traceUser: true
      })
      console.log('✅ 重新初始化云开发成功')
    } catch (error) {
      console.error('❌ 重新初始化失败:', error)
      return
    }

    // 2. 测试读取分类数据
    try {
      console.log('📊 测试读取分类数据...')
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })
      console.log('✅ 分类数据读取成功:', result.result)
    } catch (error) {
      console.error('❌ 分类数据读取失败:', error.message)
    }

    // 3. 测试读取表情包数据
    try {
      console.log('📊 测试读取表情包数据...')
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getEmojis' }
      })
      console.log('✅ 表情包数据读取成功:', result.result)
    } catch (error) {
      console.error('❌ 表情包数据读取失败:', error.message)
    }
  },
  
  // 显示修复建议
  showFixSuggestions() {
    console.log(`
🔧 修复建议：

1. 【立即执行】在微信开发者工具中部署数据读取云函数：
   - 右键 cloudfunctions/dataAPI → "上传并部署：云端安装依赖"
   - 右键 cloudfunctions/getCategories → "上传并部署：云端安装依赖"
   - 右键 cloudfunctions/getEmojiList → "上传并部署：云端安装依赖"
   ⚠️ 不需要部署 initDatabase，你的数据已经存在！

2. 【检查环境】确认云环境 ${this.currentEnvId} 是否存在：
   - 点击微信开发者工具顶部"云开发"按钮
   - 查看环境列表中是否有此环境ID

3. 【如果环境不存在】创建新环境：
   - 在云开发控制台创建新环境
   - 记录新的环境ID
   - 修改 app.js 中的环境ID

4. 【重启小程序】部署完成后：
   - 在微信开发者工具中点击"编译"
   - 重新启动小程序

5. 【验证修复】运行验证脚本：
   emergencyFix.diagnose()
`)
  }
}

// 自动开始诊断
emergencyFix.diagnose().then(() => {
  emergencyFix.showFixSuggestions()
})

// 导出到全局，方便手动调用
window.emergencyFix = emergencyFix

console.log(`
🚀 紧急修复脚本已加载！

快速命令：
- emergencyFix.diagnose()     // 完整诊断
- emergencyFix.quickFix()     // 快速修复
- emergencyFix.testCloudFunctions()  // 测试云函数
- emergencyFix.testDatabase() // 测试数据库
`)
