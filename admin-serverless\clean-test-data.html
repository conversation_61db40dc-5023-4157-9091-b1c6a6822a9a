<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据清理工具</title>
    <script src="https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .test-data-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧹 数据清理工具</h1>
    
    <div class="section">
        <h2>📊 当前数据统计</h2>
        <div id="data-stats"></div>
        <button onclick="analyzeData()">分析数据</button>
    </div>

    <div class="section">
        <h2>🔍 测试数据识别</h2>
        <div id="test-data-analysis"></div>
        <button onclick="identifyTestData()">识别测试数据</button>
    </div>

    <div class="section">
        <h2>🗑️ 数据清理操作</h2>
        <div id="cleanup-status"></div>
        <button onclick="cleanupTestData()" class="danger">清理所有测试数据</button>
        <button onclick="cleanupDuplicates()" class="danger">清理重复数据</button>
        <button onclick="cleanupAll()" class="danger">清空所有数据</button>
    </div>

    <div class="section">
        <h2>🛡️ 防止重复创建</h2>
        <div id="prevention-status"></div>
        <button onclick="disableTestDataCreation()">禁用测试数据创建</button>
        <button onclick="enableTestDataCreation()">启用测试数据创建</button>
    </div>

    <script>
        let app = null;

        // 初始化云开发
        async function initCloudbase() {
            try {
                console.log('🚀 初始化云开发...');
                app = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                // 匿名登录
                const auth = app.auth();
                await auth.signInAnonymously();
                console.log('✅ 匿名登录成功');
                
                return true;
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                return false;
            }
        }

        // 分析数据
        async function analyzeData() {
            const statusDiv = document.getElementById('data-stats');
            statusDiv.innerHTML = '<div class="info">🔄 正在分析数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const db = app.database();
                
                // 获取各集合的数据统计
                const [categoriesResult, emojisResult] = await Promise.all([
                    db.collection('categories').get(),
                    db.collection('emojis').get()
                ]);

                const stats = {
                    categories: categoriesResult.data.length,
                    emojis: emojisResult.data.length,
                    totalSize: (categoriesResult.data.length + emojisResult.data.length)
                };

                let html = `
                    <div class="success">📊 数据统计完成</div>
                    <table class="data-table">
                        <tr><th>集合</th><th>数据量</th><th>详情</th></tr>
                        <tr><td>分类 (categories)</td><td>${stats.categories}</td><td>包含所有分类数据</td></tr>
                        <tr><td>表情包 (emojis)</td><td>${stats.emojis}</td><td>包含所有表情包数据</td></tr>
                        <tr><td><strong>总计</strong></td><td><strong>${stats.totalSize}</strong></td><td>所有数据项</td></tr>
                    </table>
                `;

                statusDiv.innerHTML = html;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 分析失败: ${error.message}</div>`;
            }
        }

        // 识别测试数据
        async function identifyTestData() {
            const statusDiv = document.getElementById('test-data-analysis');
            statusDiv.innerHTML = '<div class="info">🔄 正在识别测试数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const db = app.database();
                const emojisResult = await db.collection('emojis').get();
                
                // 识别测试数据的特征
                const testDataPatterns = [
                    /测试/,
                    /test/i,
                    /哈哈哈笑死我了/,
                    /可爱小猫咪/,
                    /爱你么么哒/,
                    /picsum\.photos/,
                    /example\.com/,
                    /random=/
                ];

                const testDataItems = [];
                const realDataItems = [];

                emojisResult.data.forEach(item => {
                    const isTestData = testDataPatterns.some(pattern => {
                        return pattern.test(item.title || '') || 
                               pattern.test(item.description || '') ||
                               pattern.test(item.imageUrl || '') ||
                               pattern.test(item.url || '');
                    });

                    if (isTestData) {
                        testDataItems.push(item);
                    } else {
                        realDataItems.push(item);
                    }
                });

                let html = `
                    <div class="warning">🔍 测试数据识别完成</div>
                    <div class="info">
                        <strong>识别结果：</strong><br>
                        • 测试数据：${testDataItems.length} 个<br>
                        • 真实数据：${realDataItems.length} 个
                    </div>
                `;

                if (testDataItems.length > 0) {
                    html += '<h4>🧪 识别到的测试数据：</h4>';
                    testDataItems.slice(0, 10).forEach(item => {
                        html += `
                            <div class="test-data-item">
                                <strong>${item.title || item.name}</strong><br>
                                <small>ID: ${item._id} | URL: ${(item.imageUrl || item.url || '').substring(0, 50)}...</small>
                            </div>
                        `;
                    });
                    if (testDataItems.length > 10) {
                        html += `<div class="info">... 还有 ${testDataItems.length - 10} 个测试数据项</div>`;
                    }
                }

                statusDiv.innerHTML = html;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 识别失败: ${error.message}</div>`;
            }
        }

        // 清理测试数据
        async function cleanupTestData() {
            if (!confirm('⚠️ 确定要清理所有测试数据吗？这个操作不可撤销！')) {
                return;
            }

            const statusDiv = document.getElementById('cleanup-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在清理测试数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                // 使用webAdminAPI云函数进行清理
                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'cleanupTestData',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 测试数据清理成功</div>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 清理失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 清理失败: ${error.message}</div>`;
            }
        }

        // 清理重复数据
        async function cleanupDuplicates() {
            if (!confirm('⚠️ 确定要清理重复数据吗？')) {
                return;
            }

            const statusDiv = document.getElementById('cleanup-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在清理重复数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'cleanupDuplicates',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 重复数据清理成功</div>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 清理失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 清理失败: ${error.message}</div>`;
            }
        }

        // 清空所有数据
        async function cleanupAll() {
            if (!confirm('🚨 警告：这将删除所有数据！确定要继续吗？')) {
                return;
            }
            if (!confirm('🚨 最后确认：真的要删除所有数据吗？这个操作不可撤销！')) {
                return;
            }

            const statusDiv = document.getElementById('cleanup-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在清空所有数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'cleanupAllData',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 所有数据清理成功</div>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 清理失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 清理失败: ${error.message}</div>`;
            }
        }

        // 禁用测试数据创建
        async function disableTestDataCreation() {
            const statusDiv = document.getElementById('prevention-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在禁用测试数据创建...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'setTestDataCreationFlag',
                        enabled: false,
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 测试数据创建已禁用</div>
                        <div class="info">现在不会再自动创建测试数据了</div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 操作失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 操作失败: ${error.message}</div>`;
            }
        }

        // 启用测试数据创建
        async function enableTestDataCreation() {
            const statusDiv = document.getElementById('prevention-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在启用测试数据创建...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'setTestDataCreationFlag',
                        enabled: true,
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 测试数据创建已启用</div>
                        <div class="warning">注意：现在可以创建测试数据了</div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 操作失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 操作失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动分析数据
        window.addEventListener('load', function() {
            setTimeout(analyzeData, 1000);
        });
    </script>
</body>
</html>
