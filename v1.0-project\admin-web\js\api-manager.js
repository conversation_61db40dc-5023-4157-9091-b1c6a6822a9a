// Web管理后台 - API调用管理器
class APIManager {
  constructor() {
    this.cloudApp = null;
    this.requestQueue = [];
    this.isProcessing = false;
    this.retryCount = 3;
    this.timeout = 10000; // 10秒超时
    
    console.log('📡 APIManager初始化');
  }

  // 初始化CloudBase
  async initCloudBase() {
    if (this.cloudApp) return this.cloudApp;

    try {
      if (typeof cloudbase === 'undefined') {
        throw new Error('CloudBase SDK未加载');
      }

      this.cloudApp = cloudbase.init({
        env: 'cloud1-5g6pvnpl88dc0142',
        clientId: 'cloud1-5g6pvnpl88dc0142'  // SDK 2.0必需参数
      });

      console.log('✅ APIManager CloudBase初始化成功');
      return this.cloudApp;
    } catch (error) {
      console.error('❌ APIManager CloudBase初始化失败:', error);
      throw error;
    }
  }

  // 通用API调用方法
  async callAPI(action, data = {}, options = {}) {
    try {
      // 确保已登录
      if (!window.authManager.isLoggedIn()) {
        throw new Error('未登录，请先登录');
      }

      // 确保CloudBase已初始化
      if (!this.cloudApp) {
        await this.initCloudBase();
      }

      console.log(`📡 调用API: ${action}`, data);

      // 获取认证头
      const authHeaders = window.authManager.getAuthHeaders();
      
      // 准备请求数据
      const requestData = {
        action,
        data,
        token: authHeaders.Authorization
      };

      // 调用云函数
      const result = await this.cloudApp.callFunction({
        name: 'webAdminAPI',
        data: requestData
      });

      console.log(`📡 API响应: ${action}`, result.result);

      // 处理认证错误
      if (!result.result.success && this.isAuthError(result.result.code)) {
        console.warn('🔒 认证失败，尝试重新登录');
        await window.authManager.logout();
        throw new Error('认证失败，请重新登录');
      }

      return result.result;

    } catch (error) {
      console.error(`❌ API调用失败: ${action}`, error);
      
      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || error.errCode === -1) {
        throw new Error('网络连接失败，请检查网络后重试');
      }
      
      // 处理云函数错误
      if (error.errCode) {
        throw new Error(`服务器错误: ${error.errMsg || error.message}`);
      }
      
      throw error;
    }
  }

  // 检查是否为认证错误
  isAuthError(code) {
    const authErrorCodes = [
      'MISSING_TOKEN',
      'TOKEN_EXPIRED', 
      'INVALID_TOKEN',
      'TOKEN_VERIFICATION_FAILED',
      'INSUFFICIENT_PERMISSION'
    ];
    return authErrorCodes.includes(code);
  }

  // 分类管理API
  async createCategory(categoryData) {
    return await this.callAPI('createCategory', categoryData);
  }

  async updateCategory(categoryId, updates) {
    return await this.callAPI('updateCategory', {
      id: categoryId,
      updates: updates
    });
  }

  async deleteCategory(categoryId) {
    return await this.callAPI('deleteCategory', {
      id: categoryId
    });
  }

  async getCategories(params = {}) {
    return await this.callAPI('getCategories', params);
  }

  // 表情包管理API
  async createEmoji(emojiData) {
    return await this.callAPI('createEmoji', emojiData);
  }

  async updateEmoji(emojiId, updates) {
    return await this.callAPI('updateEmoji', {
      id: emojiId,
      updates: updates
    });
  }

  async deleteEmoji(emojiId) {
    return await this.callAPI('deleteEmoji', {
      id: emojiId
    });
  }

  async getEmojis(params = {}) {
    return await this.callAPI('getEmojis', params);
  }

  // 横幅管理API
  async createBanner(bannerData) {
    return await this.callAPI('createBanner', bannerData);
  }

  async updateBanner(bannerId, updates) {
    return await this.callAPI('updateBanner', {
      id: bannerId,
      updates: updates
    });
  }

  async deleteBanner(bannerId) {
    return await this.callAPI('deleteBanner', {
      id: bannerId
    });
  }

  async getBanners(params = {}) {
    return await this.callAPI('getBanners', params);
  }

  // 系统管理API
  async getSystemStats() {
    return await this.callAPI('getStats');
  }

  async clearSyncNotifications() {
    return await this.callAPI('clearSyncNotifications');
  }

  // 批量操作
  async batchOperation(operations) {
    const results = [];
    
    for (const operation of operations) {
      try {
        const result = await this.callAPI(operation.action, operation.data);
        results.push({
          operation: operation,
          result: result,
          success: result.success
        });
      } catch (error) {
        results.push({
          operation: operation,
          result: { success: false, error: error.message },
          success: false
        });
      }
    }
    
    return results;
  }

  // 重试机制
  async callAPIWithRetry(action, data = {}, maxRetries = 3) {
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await this.callAPI(action, data);
      } catch (error) {
        lastError = error;
        
        // 如果是认证错误，不重试
        if (error.message.includes('认证失败') || error.message.includes('未登录')) {
          throw error;
        }
        
        // 如果不是最后一次重试，等待后重试
        if (i < maxRetries - 1) {
          const delay = Math.pow(2, i) * 1000; // 指数退避
          console.log(`⏳ API调用失败，${delay}ms后重试 (${i + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  }

  // 显示API错误
  showAPIError(error, action = '') {
    let message = error.message || error.error || '操作失败';
    
    // 友好的错误提示
    const friendlyMessages = {
      '网络连接失败': '网络连接不稳定，请检查网络后重试',
      '服务器错误': '服务器暂时无法响应，请稍后重试',
      '认证失败': '登录状态已过期，请重新登录',
      '权限不足': '您没有执行此操作的权限',
      '数据验证失败': '输入的数据格式不正确，请检查后重试'
    };

    // 查找匹配的友好提示
    for (const [key, friendlyMsg] of Object.entries(friendlyMessages)) {
      if (message.includes(key)) {
        message = friendlyMsg;
        break;
      }
    }

    console.error(`🚨 API错误 [${action}]:`, message);
    
    // 显示错误提示（可以集成具体的UI组件）
    if (typeof window !== 'undefined') {
      if (window.showToast) {
        window.showToast(message, 'error');
      } else {
        alert(`操作失败: ${message}`);
      }
    }
  }

  // 显示API成功
  showAPISuccess(message, action = '') {
    console.log(`✅ API成功 [${action}]:`, message);
    
    if (typeof window !== 'undefined') {
      if (window.showToast) {
        window.showToast(message, 'success');
      }
    }
  }

  // 获取API统计信息
  getAPIStats() {
    return {
      totalCalls: this.totalCalls || 0,
      successCalls: this.successCalls || 0,
      failedCalls: this.failedCalls || 0,
      averageResponseTime: this.averageResponseTime || 0
    };
  }

  // 重置API统计
  resetAPIStats() {
    this.totalCalls = 0;
    this.successCalls = 0;
    this.failedCalls = 0;
    this.averageResponseTime = 0;
  }
}

// 全局API管理器实例
window.apiManager = new APIManager();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', async () => {
  try {
    await window.apiManager.initCloudBase();
    console.log('✅ APIManager初始化完成');
  } catch (error) {
    console.error('❌ APIManager初始化失败:', error);
  }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = APIManager;
}
