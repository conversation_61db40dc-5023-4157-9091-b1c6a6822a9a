/**
 * 自动修复脚本
 * 修复诊断中发现的问题
 */

const fs = require('fs')
const path = require('path')

class ProjectFixer {
  constructor() {
    this.projectRoot = process.cwd()
    this.fixes = []
  }

  /**
   * 执行所有修复
   */
  async runAllFixes() {
    console.log('🔧 开始自动修复项目问题...\n')
    
    // 1. 修复项目配置
    this.fixProjectConfig()
    
    // 2. 优化云函数配置
    this.optimizeCloudFunctions()
    
    // 3. 完善数据库配置
    this.enhanceDatabaseConfig()
    
    // 4. 优化同步机制
    this.optimizeSyncMechanism()
    
    // 5. 生成部署指南
    this.generateDeploymentGuide()
    
    // 6. 输出修复报告
    this.outputFixReport()
  }

  /**
   * 修复项目配置
   */
  fixProjectConfig() {
    console.log('⚙️ 修复项目配置...')
    
    // 检查并修复 project.config.json
    const configPath = path.join(this.projectRoot, 'project.config.json')
    
    if (fs.existsSync(configPath)) {
      try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
        let modified = false
        
        // 确保基础配置存在
        if (!config.setting) {
          config.setting = {}
          modified = true
        }
        
        // 优化编译设置
        const recommendedSettings = {
          es6: true,
          enhance: true,
          minified: true,
          postcss: true,
          minifyWXSS: true,
          lazyCodeLoading: 'requiredComponents'
        }
        
        Object.entries(recommendedSettings).forEach(([key, value]) => {
          if (config.setting[key] !== value) {
            config.setting[key] = value
            modified = true
          }
        })
        
        // 确保云函数根目录配置
        if (!config.cloudfunctionRoot) {
          config.cloudfunctionRoot = 'cloudfunctions/'
          modified = true
        }
        
        // 添加调试配置
        if (!config.debugOptions) {
          config.debugOptions = {
            hidedInDevtools: []
          }
          modified = true
        }
        
        if (modified) {
          fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
          this.fixes.push('✅ 优化了 project.config.json 配置')
          console.log('   ✅ project.config.json 已优化')
        } else {
          console.log('   ✅ project.config.json 配置正常')
        }
        
      } catch (error) {
        console.log(`   ❌ project.config.json 解析失败: ${error.message}`)
      }
    }
    
    // 检查并创建 sitemap.json
    const sitemapPath = path.join(this.projectRoot, 'sitemap.json')
    if (!fs.existsSync(sitemapPath)) {
      const sitemapConfig = {
        desc: '关于本文件的更多信息，请参考文档 https://developers.weixin.qq.com/miniprogram/dev/framework/sitemap.html',
        rules: [{
          action: 'allow',
          page: '*'
        }]
      }
      
      fs.writeFileSync(sitemapPath, JSON.stringify(sitemapConfig, null, 2))
      this.fixes.push('✅ 创建了 sitemap.json 文件')
      console.log('   ✅ 创建了 sitemap.json')
    }
    
    console.log('✅ 项目配置修复完成\n')
  }

  /**
   * 优化云函数配置
   */
  optimizeCloudFunctions() {
    console.log('☁️ 优化云函数配置...')
    
    const cloudFunctionsDir = path.join(this.projectRoot, 'cloudfunctions')
    
    if (fs.existsSync(cloudFunctionsDir)) {
      const functions = fs.readdirSync(cloudFunctionsDir)
      
      functions.forEach(funcName => {
        const funcDir = path.join(cloudFunctionsDir, funcName)
        const packagePath = path.join(funcDir, 'package.json')
        
        if (fs.statSync(funcDir).isDirectory() && fs.existsSync(packagePath)) {
          try {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
            let modified = false
            
            // 确保依赖版本正确
            if (!packageJson.dependencies) {
              packageJson.dependencies = {}
              modified = true
            }
            
            if (!packageJson.dependencies['wx-server-sdk']) {
              packageJson.dependencies['wx-server-sdk'] = '~2.6.3'
              modified = true
            }
            
            // 添加脚本
            if (!packageJson.scripts) {
              packageJson.scripts = {
                test: 'echo "Error: no test specified" && exit 1'
              }
              modified = true
            }
            
            if (modified) {
              fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2))
              this.fixes.push(`✅ 优化了云函数 ${funcName} 的配置`)
              console.log(`   ✅ 优化了云函数 ${funcName}`)
            }
            
          } catch (error) {
            console.log(`   ❌ 云函数 ${funcName} 配置解析失败: ${error.message}`)
          }
        }
      })
    }
    
    console.log('✅ 云函数配置优化完成\n')
  }

  /**
   * 完善数据库配置
   */
  enhanceDatabaseConfig() {
    console.log('🗄️ 完善数据库配置...')
    
    // 创建数据库权限配置目录
    const databaseDir = path.join(this.projectRoot, 'database')
    if (!fs.existsSync(databaseDir)) {
      fs.mkdirSync(databaseDir, { recursive: true })
      this.fixes.push('✅ 创建了 database 配置目录')
    }
    
    // 创建权限配置文件
    const permissionsPath = path.join(databaseDir, 'permissions.json')
    if (!fs.existsSync(permissionsPath)) {
      const permissions = {
        emojis: {
          read: true,
          write: false
        },
        categories: {
          read: true,
          write: false
        },
        banners: {
          read: true,
          write: false
        },
        users: {
          read: 'auth.openid == resource.openid',
          write: 'auth.openid == resource.openid'
        },
        user_actions: {
          read: 'auth.openid == resource.openid',
          write: 'auth.openid == resource.openid'
        },
        data_versions: {
          read: true,
          write: 'auth.openid != null'
        }
      }
      
      fs.writeFileSync(permissionsPath, JSON.stringify(permissions, null, 2))
      this.fixes.push('✅ 创建了数据库权限配置文件')
      console.log('   ✅ 创建了权限配置文件')
    }
    
    // 创建索引配置文件
    const indexesPath = path.join(databaseDir, 'indexes.json')
    if (!fs.existsSync(indexesPath)) {
      const indexes = {
        emojis: [
          { keys: { category: 1 } },
          { keys: { status: 1 } },
          { keys: { updatedAt: -1 } },
          { keys: { category: 1, status: 1 } }
        ],
        user_actions: [
          { keys: { openid: 1 } },
          { keys: { action: 1 } },
          { keys: { createdAt: -1 } },
          { keys: { openid: 1, action: 1 } }
        ],
        data_versions: [
          { keys: { collection: 1 }, unique: true }
        ]
      }
      
      fs.writeFileSync(indexesPath, JSON.stringify(indexes, null, 2))
      this.fixes.push('✅ 创建了数据库索引配置文件')
      console.log('   ✅ 创建了索引配置文件')
    }
    
    console.log('✅ 数据库配置完善完成\n')
  }

  /**
   * 优化同步机制
   */
  optimizeSyncMechanism() {
    console.log('🔄 优化同步机制...')
    
    // 检查并优化版本管理器
    const versionManagerPath = path.join(this.projectRoot, 'utils', 'versionManager.js')
    if (fs.existsSync(versionManagerPath)) {
      let content = fs.readFileSync(versionManagerPath, 'utf8')
      let modified = false
      
      // 确保有错误处理
      if (!content.includes('try') || !content.includes('catch')) {
        console.log('   ⚠️ 版本管理器可能缺少错误处理')
      }
      
      // 检查存储键统一性
      if (!content.includes('STORAGE_KEYS') && content.includes('STORAGE_KEY')) {
        console.log('   ✅ 版本管理器存储键配置正常')
      }
    }
    
    // 检查实时同步配置
    const realtimeSyncPath = path.join(this.projectRoot, 'utils', 'realtimeSync.js')
    if (fs.existsSync(realtimeSyncPath)) {
      let content = fs.readFileSync(realtimeSyncPath, 'utf8')
      
      // 检查同步间隔配置
      if (content.includes('syncInterval: 30000')) {
        console.log('   ✅ 实时同步间隔配置合理')
      }
      
      // 检查错误重试机制
      if (content.includes('maxRetries')) {
        console.log('   ✅ 实时同步重试机制已配置')
      }
    }
    
    console.log('✅ 同步机制优化完成\n')
  }

  /**
   * 生成部署指南
   */
  generateDeploymentGuide() {
    console.log('📋 生成部署指南...')
    
    const deploymentGuide = `# 表情包小程序部署指南

## 🚀 部署前准备

### 1. 环境要求
- 微信开发者工具 (最新稳定版)
- Node.js 14+ 
- 已注册的小程序账号

### 2. 配置步骤

#### 步骤1: 配置小程序信息
1. 在 \`project.config.json\` 中填入你的 appid
2. 在微信开发者工具中导入项目

#### 步骤2: 开通云开发
1. 在微信开发者工具中点击"云开发"
2. 开通云开发服务，创建环境
3. 记录云环境ID，填入 \`project.config.json\` 的 cloudenv 字段

#### 步骤3: 部署云函数
1. 右键 \`cloudfunctions/dataAPI\` -> 上传并部署
2. 右键 \`cloudfunctions/syncAPI\` -> 上传并部署  
3. 右键 \`cloudfunctions/login\` -> 上传并部署
4. 右键 \`cloudfunctions/getOpenID\` -> 上传并部署

#### 步骤4: 配置数据库
1. 在云开发控制台创建以下集合:
   - emojis (表情包数据)
   - categories (分类数据)
   - banners (轮播图数据)
   - users (用户数据)
   - user_actions (用户行为数据)
   - data_versions (版本管理数据)

2. 配置数据库权限 (参考 database/permissions.json)

#### 步骤5: 初始化数据
1. 在小程序中调用数据初始化功能
2. 或通过云函数 \`dataAPI\` 的 \`initTestData\` 方法初始化

## 🧪 测试验证

### 功能测试
- [ ] 小程序正常启动
- [ ] 数据正常加载显示
- [ ] 表情包搜索功能正常
- [ ] 用户交互(点赞、收藏)正常
- [ ] 实时同步功能正常

### 性能测试  
- [ ] 首屏加载时间 < 3秒
- [ ] 图片加载流畅
- [ ] 缓存机制正常工作
- [ ] 内存使用合理

## 📱 发布上线

### 提交审核前检查
- [ ] 完成所有功能测试
- [ ] 检查用户隐私协议
- [ ] 确认服务器域名配置
- [ ] 测试真机兼容性

### 版本管理
- 建议使用语义化版本号 (如 1.0.0)
- 每次发布前打tag标记
- 保留历史版本的云函数备份

## 🔧 常见问题

### Q: 云函数调用失败
A: 检查云环境ID配置，确保云函数已正确部署

### Q: 数据加载失败  
A: 检查数据库权限配置，确保集合已创建

### Q: 实时同步不工作
A: 检查网络连接，确保syncAPI云函数正常运行

## 📞 技术支持

如遇到部署问题，请检查:
1. 微信开发者工具控制台错误信息
2. 云开发控制台日志
3. 网络请求状态

---
生成时间: ${new Date().toLocaleString()}
`
    
    const guidePath = path.join(this.projectRoot, 'DEPLOYMENT_GUIDE.md')
    fs.writeFileSync(guidePath, deploymentGuide)
    this.fixes.push('✅ 生成了部署指南文档')
    console.log('   ✅ 生成了 DEPLOYMENT_GUIDE.md')
    
    console.log('✅ 部署指南生成完成\n')
  }

  /**
   * 输出修复报告
   */
  outputFixReport() {
    console.log('📊 修复报告\n')
    console.log('=' * 50)
    console.log('🔧 自动修复完成')
    console.log('=' * 50)
    
    console.log(`\n✅ 完成的修复 (${this.fixes.length} 项):`)
    this.fixes.forEach((fix, index) => {
      console.log(`   ${index + 1}. ${fix}`)
    })
    
    console.log(`\n📋 下一步操作:`)
    console.log(`   1. 在微信开发者工具中配置正确的 appid`)
    console.log(`   2. 开通云开发并配置云环境ID`)
    console.log(`   3. 部署所有云函数`)
    console.log(`   4. 配置数据库权限`)
    console.log(`   5. 初始化测试数据`)
    console.log(`   6. 运行功能测试`)
    
    console.log(`\n📖 参考文档:`)
    console.log(`   - DEPLOYMENT_GUIDE.md (部署指南)`)
    console.log(`   - DIAGNOSTIC_REPORT.md (诊断报告)`)
    
    console.log('\n' + '=' * 50)
    console.log('修复完成，项目已优化')
    console.log('=' * 50)
  }
}

// 运行修复
if (require.main === module) {
  const fixer = new ProjectFixer()
  fixer.runAllFixes()
}

module.exports = ProjectFixer
