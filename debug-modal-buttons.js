// 调试分类创建弹窗按钮问题
const { chromium } = require('playwright');

async function debugModalButtons() {
    console.log('🔍 调试分类创建弹窗按钮问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 点击添加分类按钮');
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        }
        
        console.log('\n📍 详细检查弹窗内容');
        
        // 详细检查弹窗内容
        const modalAnalysis = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };
            
            const form = modal.querySelector('form');
            const allElements = Array.from(modal.querySelectorAll('*')).map(el => ({
                tagName: el.tagName,
                id: el.id,
                className: el.className,
                textContent: el.textContent?.trim().substring(0, 50),
                visible: el.offsetWidth > 0 && el.offsetHeight > 0
            }));
            
            const buttons = Array.from(modal.querySelectorAll('button')).map(btn => ({
                type: btn.type,
                className: btn.className,
                textContent: btn.textContent?.trim(),
                onclick: btn.onclick ? 'has onclick' : 'no onclick',
                visible: btn.offsetWidth > 0 && btn.offsetHeight > 0,
                style: btn.style.cssText,
                computedStyle: {
                    display: window.getComputedStyle(btn).display,
                    visibility: window.getComputedStyle(btn).visibility,
                    opacity: window.getComputedStyle(btn).opacity
                }
            }));
            
            return {
                modalExists: true,
                formExists: !!form,
                modalHTML: modal.innerHTML.length,
                allElementsCount: allElements.length,
                visibleElementsCount: allElements.filter(el => el.visible).length,
                buttons: buttons,
                modalOuterHTML: modal.outerHTML.substring(0, 2000)
            };
        });
        
        console.log('📊 弹窗详细分析:');
        console.log('弹窗存在:', modalAnalysis.modalExists);
        console.log('表单存在:', modalAnalysis.formExists);
        console.log('HTML长度:', modalAnalysis.modalHTML);
        console.log('所有元素数量:', modalAnalysis.allElementsCount);
        console.log('可见元素数量:', modalAnalysis.visibleElementsCount);
        console.log('按钮数量:', modalAnalysis.buttons.length);
        
        console.log('\n📋 按钮详细信息:');
        modalAnalysis.buttons.forEach((btn, index) => {
            console.log(`按钮 ${index + 1}:`);
            console.log(`  类型: ${btn.type}`);
            console.log(`  类名: ${btn.className}`);
            console.log(`  文本: "${btn.textContent}"`);
            console.log(`  点击事件: ${btn.onclick}`);
            console.log(`  可见: ${btn.visible}`);
            console.log(`  样式: ${btn.style}`);
            console.log(`  计算样式:`);
            console.log(`    display: ${btn.computedStyle.display}`);
            console.log(`    visibility: ${btn.computedStyle.visibility}`);
            console.log(`    opacity: ${btn.computedStyle.opacity}`);
        });
        
        console.log('\n📋 弹窗HTML预览:');
        console.log(modalAnalysis.modalOuterHTML);
        
        // 尝试手动创建按钮
        console.log('\n📍 尝试手动修复按钮');
        const buttonFix = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { success: false, error: '弹窗不存在' };
            
            const form = modal.querySelector('form');
            if (!form) return { success: false, error: '表单不存在' };
            
            // 检查是否已有按钮容器
            let buttonContainer = form.querySelector('[style*="justify-content: flex-end"]');
            if (!buttonContainer) {
                // 创建按钮容器
                buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = 'display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px;';
                
                // 创建取消按钮
                const cancelBtn = document.createElement('button');
                cancelBtn.type = 'button';
                cancelBtn.className = 'btn btn-danger';
                cancelBtn.textContent = '取消';
                cancelBtn.onclick = () => {
                    document.body.removeChild(modal);
                };
                
                // 创建保存按钮
                const saveBtn = document.createElement('button');
                saveBtn.type = 'submit';
                saveBtn.className = 'btn btn-success';
                saveBtn.textContent = '保存分类';
                
                buttonContainer.appendChild(cancelBtn);
                buttonContainer.appendChild(saveBtn);
                form.appendChild(buttonContainer);
                
                return { 
                    success: true, 
                    message: '按钮已手动创建',
                    buttonsCount: buttonContainer.querySelectorAll('button').length
                };
            } else {
                return { 
                    success: false, 
                    error: '按钮容器已存在',
                    buttonsInContainer: buttonContainer.querySelectorAll('button').length
                };
            }
        });
        
        console.log('按钮修复结果:', buttonFix);
        
        // 再次检查按钮
        await page.waitForTimeout(1000);
        const finalButtonCheck = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };
            
            const buttons = Array.from(modal.querySelectorAll('button'));
            return {
                modalExists: true,
                buttonsCount: buttons.length,
                buttons: buttons.map(btn => ({
                    type: btn.type,
                    text: btn.textContent?.trim(),
                    visible: btn.offsetWidth > 0 && btn.offsetHeight > 0
                }))
            };
        });
        
        console.log('\n📊 最终按钮检查:', finalButtonCheck);
        
        // 截图
        await page.screenshot({ path: 'debug-modal-buttons.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-modal-buttons.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugModalButtons().catch(console.error);
