<!-- 测试页面 -->
<view class="container">
  <view class="header">
    <text class="title">🧪 前后端打通测试</text>
    <text class="subtitle">验证云函数和数据同步状态</text>
  </view>

  <!-- 快速测试按钮 -->
  <view class="test-section">
    <button class="btn primary" bindtap="runAllTests">一键测试前后端打通</button>
    <button class="btn secondary" bindtap="initTestData">🔄 初始化测试数据</button>
    <button class="btn admin" bindtap="goToAdmin">🔧 进入管理后台</button>
  </view>

  <!-- 测试状态 -->
  <view class="status-section">
    <view class="status-title">📊 测试状态</view>
    <view class="status-item">
      <text class="status-label">☁️ dataAPI云函数</text>
      <text class="status-value">{{testStatus.dataAPI}}</text>
    </view>
    <view class="status-item">
      <text class="status-label">⚡ adminAPI云函数</text>
      <text class="status-value">{{testStatus.adminAPI}}</text>
    </view>
    <view class="status-item">
      <text class="status-label">📊 数据初始化</text>
      <text class="status-value">{{testStatus.dataInit}}</text>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="result-section">
    <view class="result-title">📋 测试结果</view>
    <view class="result-content">
      <text class="result-text">{{testResults}}</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="help-title">📖 使用说明</view>
    <view class="help-content">
      <text class="help-text">1. 点击"一键测试"按钮开始测试</text>
      <text class="help-text">2. 查看测试状态和详细结果</text>
      <text class="help-text">3. 如果通过率达到90%以上，说明前后端已完全打通</text>
      <text class="help-text">4. 可以安全地进行小程序上架</text>
    </view>
  </view>
</view>
