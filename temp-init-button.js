// 临时初始化按钮代码 - 添加到小程序首页

// 在 pages/index/index.wxml 中添加这个按钮（临时调试用）
/*
<view class="temp-init-section" wx:if="{{showInitButton}}">
  <view class="init-tips">
    <text>🚨 检测到数据库未初始化</text>
  </view>
  <button class="init-button" bindtap="initDatabaseNow" type="primary">
    🔧 立即初始化数据库
  </button>
  <button class="test-button" bindtap="testDatabaseStatus" type="default">
    🧪 测试数据库状态
  </button>
</view>
*/

// 在 pages/index/index.wxss 中添加样式
/*
.temp-init-section {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.3);
  z-index: 9999;
  text-align: center;
  width: 600rpx;
}

.init-tips {
  margin-bottom: 30rpx;
  color: #ff6b6b;
  font-size: 32rpx;
  font-weight: bold;
}

.init-button, .test-button {
  margin: 10rpx;
  width: 400rpx;
}
*/

// 在 pages/index/index.js 中添加这些方法

const tempInitMethods = {
  data: {
    showInitButton: false, // 初始不显示，检测到问题时显示
  },

  // 检查是否需要显示初始化按钮
  async checkNeedInit() {
    try {
      // 快速检查是否有数据
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      });
      
      if (!result.result.success || !result.result.data || result.result.data.length === 0) {
        console.log('🚨 检测到数据库需要初始化');
        this.setData({ showInitButton: true });
      }
    } catch (error) {
      console.log('🚨 检测到数据库访问错误，需要初始化');
      this.setData({ showInitButton: true });
    }
  },

  // 立即初始化数据库
  async initDatabaseNow() {
    try {
      wx.showLoading({ title: '正在初始化数据库...' });
      
      console.log('🚀 开始初始化数据库...');
      
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: {}
      });
      
      console.log('初始化结果:', result);
      
      if (result.result.success) {
        wx.hideLoading();
        wx.showToast({
          title: '数据库初始化成功!',
          icon: 'success',
          duration: 2000
        });
        
        console.log('✅ 数据库初始化成功!');
        console.log('详细结果:', result.result);
        
        // 隐藏初始化按钮
        this.setData({ showInitButton: false });
        
        // 等待一下然后重新加载数据
        setTimeout(() => {
          this.loadAllData();
        }, 2000);
        
      } else {
        wx.hideLoading();
        wx.showModal({
          title: '初始化失败',
          content: result.result.message || '未知错误',
          showCancel: false
        });
        console.error('❌ 数据库初始化失败:', result.result.message);
      }
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '初始化失败',
        content: error.message || '网络错误',
        showCancel: false
      });
      console.error('❌ 调用初始化函数失败:', error);
    }
  },

  // 测试数据库状态
  async testDatabaseStatus() {
    try {
      wx.showLoading({ title: '正在测试...' });
      
      console.log('🧪 开始测试数据库状态...');
      
      // 测试分类数据
      const categoriesResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      });
      console.log('分类数据测试:', categoriesResult);
      
      // 测试横幅数据
      const bannersResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      });
      console.log('横幅数据测试:', bannersResult);
      
      // 测试表情包数据
      const emojisResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 5 } }
      });
      console.log('表情包数据测试:', emojisResult);
      
      wx.hideLoading();
      
      // 显示测试结果
      const categoryCount = categoriesResult.result.success ? categoriesResult.result.data.length : 0;
      const bannerCount = bannersResult.result.success ? bannersResult.result.data.length : 0;
      const emojiCount = emojisResult.result.success ? emojisResult.result.data.length : 0;
      
      wx.showModal({
        title: '数据库状态测试',
        content: `分类: ${categoryCount}个\n横幅: ${bannerCount}个\n表情包: ${emojiCount}个\n\n详细信息请查看控制台`,
        showCancel: false
      });
      
    } catch (error) {
      wx.hideLoading();
      wx.showModal({
        title: '测试失败',
        content: error.message || '网络错误',
        showCancel: false
      });
      console.error('❌ 测试数据库状态失败:', error);
    }
  },

  // 重新加载所有数据
  async loadAllData() {
    try {
      console.log('🔄 重新加载所有数据...');
      
      // 重新加载分类数据
      await this.loadCategoryData();
      
      // 重新加载横幅数据
      await this.loadBannerData();
      
      // 重新加载表情包数据
      await this.loadEmojiData();
      
      console.log('✅ 所有数据重新加载完成');
      
    } catch (error) {
      console.error('❌ 重新加载数据失败:', error);
    }
  }
};

// 使用说明
console.log(`
🚀 临时初始化按钮使用说明：

1. 将上面注释中的 WXML 代码添加到 pages/index/index.wxml
2. 将上面注释中的 WXSS 代码添加到 pages/index/index.wxss  
3. 将 tempInitMethods 中的方法添加到 pages/index/index.js

4. 在 onLoad 或 onReady 中调用：
   this.checkNeedInit();

5. 如果数据库未初始化，页面会显示初始化按钮
6. 点击按钮即可初始化数据库

这是临时解决方案，初始化完成后可以移除这些代码。
`);

module.exports = tempInitMethods;
