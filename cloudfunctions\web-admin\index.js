const cloud = require('wx-server-sdk')
const fs = require('fs')
const path = require('path')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { path: requestPath, httpMethod } = event

  console.log('Web Admin Request:', { requestPath, httpMethod })

  // 处理静态文件请求
  if (httpMethod === 'GET') {
    try {
      let filePath = requestPath === '/' ? '/index-cloud.html' : requestPath
      
      // 安全检查
      if (filePath.includes('..')) {
        return {
          statusCode: 403,
          body: 'Forbidden'
        }
      }

      const fullPath = path.join(__dirname, filePath)
      
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8')
        const ext = path.extname(filePath)
        
        let contentType = 'text/plain'
        if (ext === '.html') contentType = 'text/html; charset=utf-8'
        if (ext === '.css') contentType = 'text/css; charset=utf-8'
        if (ext === '.js') contentType = 'application/javascript; charset=utf-8'
        
        return {
          statusCode: 200,
          headers: {
            'Content-Type': contentType,
            'Cache-Control': 'no-cache'
          },
          body: content
        }
      } else {
        return {
          statusCode: 404,
          body: 'File Not Found: ' + filePath
        }
      }
    } catch (error) {
      console.error('Error serving file:', error)
      return {
        statusCode: 500,
        body: 'Internal Server Error: ' + error.message
      }
    }
  }

  // 处理API请求
  if (httpMethod === 'POST' && requestPath === '/api') {
    try {
      const { action, data } = JSON.parse(event.body || '{}')
      
      console.log('API Request:', { action, data })

      // 根据action处理不同的请求
      switch (action) {
        case 'getStats':
          return await handleGetStats()
        
        case 'getEmojis':
          return await handleGetEmojis(data)
        
        case 'getCategoryStats':
          return await handleGetCategoryStats()
        
        case 'initTestData':
          return await handleInitTestData()
        
        case 'getSystemInfo':
          return await handleGetSystemInfo()
        
        default:
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              success: false,
              message: 'Unknown action: ' + action
            })
          }
      }
    } catch (error) {
      console.error('API Error:', error)
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          success: false,
          message: error.message
        })
      }
    }
  }

  return {
    statusCode: 404,
    body: 'Not Found'
  }
}

// 获取统计数据
async function handleGetStats() {
  try {
    // 这里应该从数据库获取真实数据
    // 暂时返回模拟数据
    const stats = {
      totalEmojis: 156,
      totalCategories: 8,
      totalUsers: 1234,
      totalDownloads: 5678
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        data: stats
      })
    }
  } catch (error) {
    throw new Error('获取统计数据失败: ' + error.message)
  }
}

// 获取表情包数据
async function handleGetEmojis(data = {}) {
  try {
    const { page = 1, limit = 20 } = data

    // 这里应该从数据库获取真实数据
    // 暂时返回模拟数据
    const emojis = [
      { _id: '1', title: '开心笑脸', category: '情感类', likes: 123, downloads: 456, status: '已发布' },
      { _id: '2', title: '可爱猫咪', category: '动物类', likes: 234, downloads: 567, status: '已发布' },
      { _id: '3', title: '工作加油', category: '生活类', likes: 345, downloads: 678, status: '已发布' },
      { _id: '4', title: '节日庆祝', category: '节日类', likes: 456, downloads: 789, status: '已发布' },
      { _id: '5', title: '运动健身', category: '运动类', likes: 567, downloads: 890, status: '已发布' }
    ]

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        data: emojis.slice((page - 1) * limit, page * limit)
      })
    }
  } catch (error) {
    throw new Error('获取表情包数据失败: ' + error.message)
  }
}

// 获取分类统计
async function handleGetCategoryStats() {
  try {
    // 这里应该从数据库获取真实数据
    // 暂时返回模拟数据
    const categories = [
      { id: 'emotion', name: '情感类', icon: '😊', count: 25 },
      { id: 'animal', name: '动物类', icon: '🐱', count: 18 },
      { id: 'life', name: '生活类', icon: '🏠', count: 32 },
      { id: 'festival', name: '节日类', icon: '🎉', count: 15 },
      { id: 'work', name: '工作类', icon: '💼', count: 22 },
      { id: 'sport', name: '运动类', icon: '⚽', count: 12 },
      { id: 'food', name: '美食类', icon: '🍕', count: 19 },
      { id: 'travel', name: '旅行类', icon: '✈️', count: 13 }
    ]

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        data: categories
      })
    }
  } catch (error) {
    throw new Error('获取分类统计失败: ' + error.message)
  }
}

// 初始化测试数据
async function handleInitTestData() {
  try {
    // 这里应该初始化数据库中的测试数据
    console.log('初始化测试数据...')

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        message: '测试数据初始化成功'
      })
    }
  } catch (error) {
    throw new Error('初始化测试数据失败: ' + error.message)
  }
}

// 获取系统信息
async function handleGetSystemInfo() {
  try {
    const systemInfo = {
      version: '2.0.0',
      environment: 'WeChat Cloud',
      nodeVersion: process.version,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    }

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        success: true,
        data: systemInfo
      })
    }
  } catch (error) {
    throw new Error('获取系统信息失败: ' + error.message)
  }
}
