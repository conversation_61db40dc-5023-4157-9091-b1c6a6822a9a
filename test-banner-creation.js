// 测试横幅创建功能
const { chromium } = require('playwright');

async function testBannerCreation() {
    console.log('🎨 测试横幅创建功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[CONSOLE] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 进入横幅管理页面');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            await page.waitForTimeout(3000);
        }
        
        console.log('📍 点击添加横幅按钮');
        const addBannerBtn = await page.locator('text=➕ 添加横幅').first();
        if (await addBannerBtn.isVisible()) {
            await addBannerBtn.click();
            await page.waitForTimeout(2000);
        } else {
            console.log('❌ 未找到添加横幅按钮');
            return;
        }
        
        console.log('📍 填写横幅信息');
        
        // 检查表单元素是否存在
        const titleInput = await page.locator('#banner-title').first();
        const priorityInput = await page.locator('#banner-priority').first();
        const statusSelect = await page.locator('#banner-status').first();
        const descInput = await page.locator('#banner-description').first();
        
        console.log('表单元素检查:');
        console.log('  标题输入框:', await titleInput.isVisible());
        console.log('  优先级输入框:', await priorityInput.isVisible());
        console.log('  状态选择框:', await statusSelect.isVisible());
        console.log('  描述输入框:', await descInput.isVisible());
        
        // 填写表单
        if (await titleInput.isVisible()) {
            await titleInput.fill('测试横幅标题-修复版');
            console.log('✅ 已填写标题: 测试横幅标题-修复版');
        }
        
        if (await priorityInput.isVisible()) {
            await priorityInput.fill('5');
            console.log('✅ 已设置优先级: 5');
        }
        
        if (await statusSelect.isVisible()) {
            await statusSelect.selectOption('show');
            console.log('✅ 已设置状态为显示');
        }
        
        if (await descInput.isVisible()) {
            await descInput.fill('这是一个修复后的测试横幅');
            console.log('✅ 已填写描述');
        }
        
        console.log('📍 保存横幅');
        
        // 点击保存按钮
        const saveBtn = await page.locator('button:has-text("保存横幅"), button:has-text("保存")').first();
        if (await saveBtn.isVisible()) {
            await saveBtn.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(5000);
        } else {
            console.log('❌ 未找到保存按钮');
        }
        
        console.log('📍 验证横幅创建结果');
        
        // 等待页面更新
        await page.waitForTimeout(3000);
        
        // 检查横幅列表
        const bannerData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#banner-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const titleCell = cells[2]; // 标题列
                const statusCell = cells[8]; // 状态列
                
                return {
                    index: index + 1,
                    title: titleCell ? titleCell.textContent?.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent?.trim() : 'N/A',
                    isNewBanner: titleCell ? titleCell.textContent?.includes('测试横幅标题-修复版') : false
                };
            });
        });
        
        console.log('📊 横幅列表验证:');
        let foundNewBanner = false;
        
        bannerData.forEach(banner => {
            console.log(`\n横幅 ${banner.index}:`);
            console.log(`  标题: "${banner.title}"`);
            console.log(`  状态: "${banner.status}"`);
            console.log(`  是新横幅: ${banner.isNewBanner}`);
            
            if (banner.isNewBanner) {
                foundNewBanner = true;
                console.log('  ✅ 找到新创建的横幅！');
            }
        });
        
        if (foundNewBanner) {
            console.log('\n✅ 横幅创建成功！标题正确保存！');
        } else {
            console.log('\n❌ 横幅创建失败或标题未正确保存');
        }
        
        // 截图
        await page.screenshot({ path: 'banner-creation-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: banner-creation-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testBannerCreation().catch(console.error);
