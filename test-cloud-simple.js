// 简单的云开发测试脚本 - 在小程序控制台中运行

// 测试1：检查云开发是否可用
function testCloudAvailable() {
  console.log('🧪 测试1：检查云开发是否可用');
  
  if (typeof wx === 'undefined') {
    console.log('❌ wx 对象不存在');
    return false;
  }
  
  if (!wx.cloud) {
    console.log('❌ wx.cloud 不存在，请检查基础库版本');
    return false;
  }
  
  console.log('✅ wx.cloud 存在');
  return true;
}

// 测试2：重新初始化云开发
function reinitCloud() {
  console.log('🧪 测试2：重新初始化云开发');
  
  try {
    wx.cloud.init({
      env: 'cloud1-5g6pvnpl88dc0142',
      traceUser: true
    });
    console.log('✅ 云开发重新初始化成功');
    return true;
  } catch (error) {
    console.log('❌ 云开发初始化失败:', error);
    return false;
  }
}

// 测试3：测试云函数调用
async function testCloudFunction() {
  console.log('🧪 测试3：测试云函数调用');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'ping' }
    });
    
    console.log('✅ 云函数调用成功:', result);
    return true;
  } catch (error) {
    console.log('❌ 云函数调用失败:', error);
    return false;
  }
}

// 测试4：测试数据库连接
async function testDatabase() {
  console.log('🧪 测试4：测试数据库连接');
  
  try {
    const db = wx.cloud.database();
    const result = await db.collection('categories').limit(1).get();
    
    console.log('✅ 数据库连接成功:', result);
    return true;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error);
    return false;
  }
}

// 综合测试函数
async function runAllTests() {
  console.log('🚀 开始云开发综合测试...');
  
  const results = {
    cloudAvailable: false,
    reinitSuccess: false,
    functionCall: false,
    databaseAccess: false
  };
  
  // 测试1
  results.cloudAvailable = testCloudAvailable();
  
  if (!results.cloudAvailable) {
    console.log('❌ 云开发不可用，停止测试');
    return results;
  }
  
  // 测试2
  results.reinitSuccess = reinitCloud();
  
  // 等待一下让初始化完成
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 测试3
  results.functionCall = await testCloudFunction();
  
  // 测试4
  results.databaseAccess = await testDatabase();
  
  // 显示结果
  console.log('📊 测试结果汇总:');
  console.log('   云开发可用:', results.cloudAvailable ? '✅' : '❌');
  console.log('   重新初始化:', results.reinitSuccess ? '✅' : '❌');
  console.log('   云函数调用:', results.functionCall ? '✅' : '❌');
  console.log('   数据库访问:', results.databaseAccess ? '✅' : '❌');
  
  const successCount = Object.values(results).filter(Boolean).length;
  console.log(`🎯 总体结果: ${successCount}/4 项测试通过`);
  
  if (successCount === 4) {
    console.log('🎉 所有测试通过！云开发工作正常');
    wx.showToast({ title: '云开发测试通过!', icon: 'success' });
  } else {
    console.log('⚠️ 部分测试失败，请检查配置');
    wx.showToast({ title: '云开发测试失败', icon: 'error' });
  }
  
  return results;
}

// 快速修复函数
async function quickFix() {
  console.log('🔧 开始快速修复...');
  
  // 1. 重新初始化
  reinitCloud();
  
  // 2. 等待
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 3. 尝试初始化数据库
  try {
    const result = await wx.cloud.callFunction({
      name: 'initDatabase',
      data: {}
    });
    
    if (result.result.success) {
      console.log('✅ 数据库初始化成功');
      wx.showToast({ title: '修复成功!', icon: 'success' });
    } else {
      console.log('❌ 数据库初始化失败:', result.result.message);
      wx.showToast({ title: '修复失败', icon: 'error' });
    }
  } catch (error) {
    console.log('❌ 修复过程出错:', error);
    wx.showToast({ title: '修复出错', icon: 'error' });
  }
}

// 使用说明
console.log(`
🚀 云开发测试脚本使用说明：

1. 综合测试（推荐）：
   runAllTests()

2. 快速修复：
   quickFix()

3. 单独测试：
   testCloudAvailable()    - 检查云开发可用性
   reinitCloud()          - 重新初始化
   testCloudFunction()    - 测试云函数
   testDatabase()         - 测试数据库

请在小程序开发者工具的控制台中运行这些函数。
`);

// 自动运行综合测试
setTimeout(() => {
  runAllTests();
}, 1000);
