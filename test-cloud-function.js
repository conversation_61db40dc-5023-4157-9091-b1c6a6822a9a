
/**
 * 云函数修复效果测试脚本
 */

console.log('🧪 测试云函数修复效果...\n')

// 模拟数据库查询结果
const mockDatabase = {
  // 管理后台创建的数据（使用category字段）
  emojis: [
    {
      _id: 'admin_emoji_1',
      title: '测试1',
      category: '测试1', // 管理后台格式
      status: 'published',
      createTime: new Date('2024-07-22')
    },
    {
      _id: 'admin_emoji_2',
      title: '测试2',
      category: '测试2', // 管理后台格式
      status: 'published',
      createTime: new Date('2024-07-22')
    },
    {
      _id: 'cloud_emoji_1',
      title: '云函数测试数据',
      categoryId: 'funny', // 云函数格式
      category: '搞笑幽默',
      status: 'published',
      createTime: new Date('2024-07-20')
    }
  ],
  categories: [
    {
      _id: 'admin_cat_1',
      name: '测试1',
      status: 'active',
      sort: 1
    },
    {
      _id: 'admin_cat_2',
      name: '测试2',
      status: 'active',
      sort: 2
    },
    {
      _id: 'funny',
      name: '搞笑幽默',
      status: 'active',
      sort: 3
    }
  ]
}

// 模拟修复后的查询逻辑
function testGetEmojis(category = 'all') {
  console.log(`📋 测试获取表情包 - 分类: ${category}`)

  let filteredEmojis = mockDatabase.emojis.filter(emoji => emoji.status === 'published')

  if (category !== 'all') {
    // 修复后的查询逻辑 - 兼容多种格式
    filteredEmojis = filteredEmojis.filter(emoji => {
      // 1. 按categoryId匹配
      if (emoji.categoryId === category) return true

      // 2. 按category字段匹配
      if (emoji.category === category) return true

      // 3. 按分类名称匹配
      const categoryInfo = mockDatabase.categories.find(cat => cat.name === category)
      if (categoryInfo) {
        if (emoji.categoryId === categoryInfo._id) return true
        if (emoji.category === categoryInfo._id) return true
      }

      return false
    })
  }

  console.log(`   结果: 找到 ${filteredEmojis.length} 个表情包`)
  filteredEmojis.forEach(emoji => {
    console.log(`   - ${emoji.title} (category: ${emoji.category || emoji.categoryId})`)
  })
  console.log('')

  return filteredEmojis
}

// 模拟修复后的分类统计逻辑
function testGetCategories() {
  console.log('📊 测试分类统计')

  const categoriesWithStats = mockDatabase.categories.map(category => {
    // 修复后的统计逻辑 - 兼容多种格式
    const matchingEmojis = mockDatabase.emojis.filter(emoji => {
      if (emoji.status !== 'published') return false

      // 多种匹配方式
      return emoji.categoryId === category._id ||
             emoji.category === category._id ||
             emoji.category === category.name
    })

    // 去重（避免重复计算）
    const uniqueEmojis = Array.from(new Set(matchingEmojis.map(e => e._id)))
      .map(id => matchingEmojis.find(e => e._id === id))

    const result = {
      ...category,
      emojiCount: uniqueEmojis.length
    }

    console.log(`   ${category.name}: ${result.emojiCount} 个表情包`)

    return result
  })

  console.log('')
  return categoriesWithStats
}

// 执行测试
console.log('=== 修复前的问题 ===')
console.log('管理后台创建的数据使用 category 字段存储分类名称')
console.log('云函数查询使用 categoryId 字段，导致查询不到数据')
console.log('小程序显示云函数初始化的测试数据\n')

console.log('=== 修复后的效果 ===')

// 测试获取所有表情包
testGetEmojis('all')

// 测试按分类名称查询（管理后台创建的数据）
testGetEmojis('测试1')
testGetEmojis('测试2')

// 测试按分类ID查询（云函数创建的数据）
testGetEmojis('funny')

// 测试分类统计
testGetCategories()

console.log('=== 测试结论 ===')
console.log('✅ 修复后的云函数能够:')
console.log('   1. 正确查询管理后台创建的表情包（category字段）')
console.log('   2. 兼容云函数创建的表情包（categoryId字段）')
console.log('   3. 正确统计各分类的表情包数量')
console.log('   4. 支持按分类名称和分类ID查询')
console.log('')
console.log('🎯 预期效果: 小程序将显示管理后台创建的真实数据')
