<!--pages/browse-history/browse-history.wxml-->
<view class="container">

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 浏览记录表情包信息流 - 和首页样式一致 -->
  <view class="emoji-section" wx:if="{{!loading && historyList.length > 0}}">
    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{historyList}}"
        wx:key="id"
        bindtap="onEmojiTap"
        data-emoji="{{item}}"
      >
        <!-- 表情包图片 -->
        <view class="emoji-image-container">
          <image
            class="emoji-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
        </view>

        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <text class="emoji-category">{{item.categoryName || item.category}}</text>

          <!-- 标签区域 -->
          <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <view
              class="tag-item"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >
              {{tag}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && isEmpty}}" class="empty-state">
    <text class="empty-icon">👀</text>
    <text class="empty-title">暂无浏览记录</text>
    <text class="empty-desc">去发现更多有趣的表情包吧</text>
    <button class="explore-btn" bindtap="onGoExplore">去探索</button>
  </view>

  <!-- 清空记录按钮 -->
  <view wx:if="{{!loading && historyList.length > 0}}" class="clear-section">
    <button class="clear-btn" bindtap="onClearHistory">清空浏览记录</button>
  </view>
</view>
