/* pages/collection/collection.wxss */

.collection-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  margin-bottom: 24rpx;
}

.title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-right: 16rpx;
}

.count {
  font-size: 28rpx;
  color: #666;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 0 48rpx 0 16rpx;
}

.search-input {
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.search-icon,
.clear-icon {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.clear-icon {
  color: #666;
  font-size: 40rpx;
  cursor: pointer;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: #007aff;
  color: white;
  border-radius: 20rpx;
  font-size: 26rpx;
}

.btn-icon {
  font-size: 24rpx;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.view-mode {
  display: flex;
  gap: 8rpx;
}

.mode-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s ease;
}

.mode-btn.active {
  border-color: #007aff;
  background: #e3f2fd;
  color: #007aff;
}

.mode-icon {
  font-size: 28rpx;
}

.grid-columns {
  display: flex;
  gap: 8rpx;
}

.column-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 24rpx;
  background: white;
  transition: all 0.3s ease;
}

.column-btn.active {
  border-color: #007aff;
  background: #007aff;
  color: white;
}

.sort-options {
  display: flex;
  gap: 16rpx;
  margin-left: auto;
}

.sort-btn {
  padding: 12rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  background: white;
  transition: all 0.3s ease;
}

.sort-btn.active {
  border-color: #007aff;
  background: #007aff;
  color: white;
}

/* 批量操作栏 */
.batch-actions {
  background: #fff3cd;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.batch-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.selected-count {
  font-size: 28rpx;
  color: #856404;
  font-weight: 600;
}

.select-all-btn {
  font-size: 26rpx;
  color: #007aff;
  padding: 8rpx 16rpx;
  border: 2rpx solid #007aff;
  border-radius: 12rpx;
}

.batch-buttons {
  display: flex;
  gap: 16rpx;
}

.batch-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.uncollect-btn {
  background: #dc3545;
  color: white;
}

.share-btn {
  background: #28a745;
  color: white;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 48rpx;
}

.empty-action {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 表情包网格 */
.emoji-grid {
  padding: 32rpx;
  display: grid;
  gap: 24rpx;
}

.emoji-grid.columns-1 {
  grid-template-columns: 1fr;
}

.emoji-grid.columns-2 {
  grid-template-columns: 1fr 1fr;
}

.emoji-grid.columns-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.emoji-item {
  position: relative;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.emoji-item.selection-mode {
  border: 4rpx solid transparent;
}

.emoji-item.selected {
  border-color: #007aff;
  transform: scale(0.95);
}

.selection-checkbox {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 10;
}

.checkbox {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid white;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007aff;
  border-color: #007aff;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.emoji-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.emoji-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.status-badges {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 8rpx;
}

.badge {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 列表模式 */
.emoji-list-view {
  padding: 32rpx;
}

.emoji-list-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
}

.emoji-list-item.selection-mode {
  border: 4rpx solid transparent;
}

.emoji-list-item.selected {
  border-color: #007aff;
  background: #f0f8ff;
}

.emoji-thumbnail {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
  margin-right: 24rpx;
}

.emoji-details {
  flex: 1;
}

.emoji-details .emoji-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.emoji-details .emoji-meta {
  margin-bottom: 8rpx;
}

.emoji-stats {
  margin-left: 16rpx;
}

.collect-info {
  font-size: 24rpx;
  color: #999;
}

/* 统计面板 */
.stats-panel {
  background: white;
  margin: 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .filter-bar {
    flex-wrap: wrap;
    gap: 16rpx;
  }
  
  .sort-options {
    margin-left: 0;
    width: 100%;
  }
  
  .emoji-grid.columns-3 {
    grid-template-columns: 1fr 1fr;
  }
}
