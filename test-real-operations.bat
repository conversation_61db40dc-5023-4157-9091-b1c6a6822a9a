@echo off
chcp 65001 >nul
echo ========================================
echo Testing Real Data Operations
echo ========================================

cd admin-unified

echo 1. Testing server health...
curl -s http://localhost:8001/health
echo.

echo 2. Getting current categories...
curl -s http://localhost:8001/api/categories
echo.
echo.

echo 3. Adding a new category via adminAPI...
curl -s -X POST http://localhost:8001/api/cloud-function ^
  -H "Content-Type: application/json" ^
  -d "{\"functionName\":\"adminAPI\",\"data\":{\"action\":\"createCategory\",\"collection\":\"categories\",\"data\":{\"name\":\"Real Test Category\",\"icon\":\"🧪\",\"status\":\"show\",\"sort\":99}}}"
echo.
echo.

echo 4. Getting categories again (should include new one)...
curl -s http://localhost:8001/api/categories
echo.
echo.

echo 5. Adding a new emoji via adminAPI...
curl -s -X POST http://localhost:8001/api/cloud-function ^
  -H "Content-Type: application/json" ^
  -d "{\"functionName\":\"adminAPI\",\"data\":{\"action\":\"addEmoji\",\"collection\":\"emojis\",\"data\":{\"title\":\"Real Test Emoji\",\"category\":\"Real Test Category\",\"status\":\"published\",\"likes\":0,\"downloads\":0}}}"
echo.
echo.

echo 6. Getting emojis (should include new one)...
curl -s http://localhost:8001/api/emojis
echo.
echo.

echo 7. Checking data files...
echo Categories file:
type data\categories.json
echo.
echo.
echo Emojis file:
type data\emojis.json
echo.

echo ========================================
echo Test Complete!
echo.
echo If you see the new category and emoji in both:
echo 1. API responses
echo 2. JSON files
echo.
echo Then the data persistence is working correctly!
echo ========================================

pause
