// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { keyword, page = 1, limit = 20 } = event
  
  try {
    if (!keyword || keyword.trim() === '') {
      return {
        success: false,
        error: '搜索关键词不能为空'
      }
    }
    
    // 使用正则表达式进行模糊搜索
    const searchRegex = new RegExp(keyword, 'i')
    
    const result = await db.collection('emojis')
      .where(db.command.or([
        {
          title: searchRegex
        },
        {
          tags: searchRegex
        },
        {
          category: searchRegex
        }
      ]))
      .orderBy('likes', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()
    
    return {
      success: true,
      data: result.data,
      total: result.data.length,
      keyword: keyword
    }
  } catch (error) {
    console.error('搜索表情包失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}