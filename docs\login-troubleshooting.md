# 登录功能故障排除指南

## 🚨 常见登录失败问题

### 问题1: 云函数调用失败

**症状**: 控制台显示 "云函数登录失败" 或 "cloud function not found"

**可能原因**:
1. 云函数未部署或部署失败
2. 云环境ID配置错误
3. 云开发服务未开通

**解决步骤**:

#### 步骤1: 检查云开发服务
1. 打开微信开发者工具
2. 点击工具栏中的"云开发"按钮
3. 如果提示开通云开发，按提示开通
4. 确保云环境已创建

#### 步骤2: 检查云环境ID
1. 在云开发控制台查看环境ID
2. 打开 `app.js` 文件
3. 确认 `wx.cloud.init()` 中的 `env` 参数正确
```javascript
wx.cloud.init({
  env: 'your-actual-env-id', // 替换为实际的环境ID
  traceUser: true
})
```

#### 步骤3: 部署云函数
1. 在微信开发者工具中找到 `cloudfunctions/login` 文件夹
2. 右键点击该文件夹
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成（可能需要几分钟）
5. 在云开发控制台验证云函数是否部署成功

#### 步骤4: 测试云函数
1. 在个人中心页面点击"测试云函数"按钮
2. 查看测试结果
3. 如果仍然失败，查看云开发控制台的云函数日志

### 问题2: 用户拒绝授权

**症状**: 弹出授权框后用户点击拒绝，登录失败

**解决方案**:
1. 系统会自动提示用户重新授权
2. 用户需要重新点击登录按钮
3. 在授权弹窗中点击"允许"

### 问题3: 网络连接问题

**症状**: 登录超时或网络错误

**解决步骤**:
1. 检查设备网络连接
2. 尝试切换网络（WiFi/移动数据）
3. 系统会自动重试3次
4. 如果仍然失败，稍后再试

### 问题4: 基础库版本过低

**症状**: 提示"请使用 2.2.3 或以上的基础库"

**解决方案**:
1. 在微信开发者工具中点击"详情"
2. 在"本地设置"中调整基础库版本到2.2.3或以上
3. 重新编译项目

## 🔧 快速诊断工具

### 使用内置诊断工具
1. 打开个人中心页面
2. 滚动到底部的测试区域
3. 点击"环境诊断"按钮
4. 查看诊断结果和修复建议

### 手动检查清单
- [ ] 云开发服务已开通
- [ ] 云环境ID配置正确
- [ ] login云函数已部署
- [ ] 网络连接正常
- [ ] 基础库版本≥2.2.3
- [ ] 小程序AppID配置正确

## 🛠️ 临时解决方案

如果云函数暂时不可用，系统会自动启用本地模拟登录：

1. **功能**: 基本登录功能正常
2. **限制**: 数据不会同步到云端
3. **识别**: 用户ID以"local_"开头
4. **恢复**: 修复云函数后重新登录即可

## 📞 获取帮助

### 查看详细日志
1. 打开微信开发者工具控制台
2. 查看详细的错误信息
3. 记录错误代码和描述

### 云开发控制台
1. 访问云开发控制台
2. 查看云函数运行日志
3. 检查数据库连接状态

### 常用调试命令
在控制台中运行以下命令进行调试：

```javascript
// 检查云开发初始化状态
console.log('云开发支持:', typeof wx.cloud !== 'undefined')

// 测试云函数连接
wx.cloud.callFunction({
  name: 'login',
  data: { test: true }
}).then(res => {
  console.log('云函数测试成功:', res)
}).catch(err => {
  console.error('云函数测试失败:', err)
})

// 检查网络状态
wx.getNetworkType({
  success: res => console.log('网络类型:', res.networkType)
})
```

## ✅ 验证修复

修复问题后，请按以下步骤验证：

1. **基础测试**:
   - 点击"测试登录"按钮
   - 确认登录流程正常

2. **完整测试**:
   - 点击"完整测试"按钮
   - 查看所有测试项目通过

3. **功能验证**:
   - 登录后查看用户信息显示
   - 测试点赞、收藏等功能
   - 确认数据同步正常

## 🔄 重置和清理

如果问题持续存在，可以尝试重置：

1. **清理本地数据**:
   - 点击"清理测试"按钮
   - 重启小程序

2. **重新部署云函数**:
   - 删除云函数后重新部署
   - 检查部署日志

3. **重置云环境**:
   - 在云开发控制台重置环境
   - 重新配置数据库和权限

---

*如果以上方法都无法解决问题，请检查微信开发者工具和云开发服务的最新文档。*
