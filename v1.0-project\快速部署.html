<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.0 快速部署向导</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }

        .step {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #374151;
        }

        .step-content {
            color: #6b7280;
            margin-left: 45px;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .warning {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .warning-title {
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 5px;
        }

        .success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .success-title {
            font-weight: 600;
            color: #16a34a;
            margin-bottom: 5px;
        }

        .input-group {
            margin: 15px 0;
        }

        .input-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #374151;
        }

        .input-field {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            margin-top: 10px;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn-copy {
            background: #10b981;
            font-size: 12px;
            padding: 5px 10px;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .checklist li:before {
            content: "☐ ";
            margin-right: 10px;
            font-size: 16px;
        }

        .checklist li.checked:before {
            content: "✅ ";
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 V1.0 快速部署向导</h1>
        <p>5步完成表情包小程序系统部署</p>
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
    </div>

    <!-- 步骤1: 环境准备 -->
    <div class="step">
        <div class="step-title">
            <span class="step-number">1</span>
            环境准备
        </div>
        <div class="step-content">
            <p>在开始部署前，请确保您已经准备好以下环境：</p>
            
            <ul class="checklist">
                <li onclick="toggleCheck(this)">腾讯云账号 (已注册并开通云开发服务)</li>
                <li onclick="toggleCheck(this)">微信小程序账号 (已获取AppID)</li>
                <li onclick="toggleCheck(this)">Node.js 14.0+ (已安装)</li>
                <li onclick="toggleCheck(this)">微信开发者工具 (已安装)</li>
            </ul>

            <div class="warning">
                <div class="warning-title">⚠️ 重要提醒</div>
                如果您还没有准备好上述环境，请先参考《部署指南.md》完成环境准备。
            </div>

            <button class="btn" onclick="nextStep(1)">环境已准备好，继续</button>
        </div>
    </div>

    <!-- 步骤2: 安装工具 -->
    <div class="step" id="step-2" style="display: none;">
        <div class="step-title">
            <span class="step-number">2</span>
            安装CloudBase CLI工具
        </div>
        <div class="step-content">
            <p>安装腾讯云CloudBase命令行工具：</p>
            
            <div class="code-block">
# 安装CloudBase CLI
npm install -g @cloudbase/cli

# 验证安装
tcb --version
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <p>安装完成后，登录您的腾讯云账号：</p>
            
            <div class="code-block">
# 登录CloudBase
tcb login
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <div class="success">
                <div class="success-title">✅ 验证方法</div>
                运行 <code>tcb env:list</code> 如果能看到您的环境列表，说明安装成功。
            </div>

            <button class="btn" onclick="nextStep(2)">工具已安装，继续</button>
        </div>
    </div>

    <!-- 步骤3: 创建云开发环境 -->
    <div class="step" id="step-3" style="display: none;">
        <div class="step-title">
            <span class="step-number">3</span>
            创建云开发环境
        </div>
        <div class="step-content">
            <p>创建一个新的云开发环境用于部署V1.0系统：</p>
            
            <div class="input-group">
                <label class="input-label">环境名称 (建议):</label>
                <input type="text" class="input-field" id="env-name" value="emoji-miniprogram-v1" readonly>
            </div>

            <div class="code-block">
# 创建云开发环境
tcb env:create emoji-miniprogram-v1 --alias "表情包小程序V1.0"
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <p>创建成功后，记录下环境ID：</p>
            
            <div class="input-group">
                <label class="input-label">您的环境ID (请填写):</label>
                <input type="text" class="input-field" id="env-id" placeholder="例如: cloud1-5g6pvnpl88dc0142">
            </div>

            <div class="warning">
                <div class="warning-title">📝 重要</div>
                请务必记录下环境ID，后续步骤需要使用。环境ID格式类似：cloud1-xxxxxxxxx
            </div>

            <button class="btn" onclick="nextStep(3)">环境已创建，继续</button>
        </div>
    </div>

    <!-- 步骤4: 配置项目 -->
    <div class="step" id="step-4" style="display: none;">
        <div class="step-title">
            <span class="step-number">4</span>
            配置项目文件
        </div>
        <div class="step-content">
            <p>根据您的环境ID，更新以下配置文件：</p>

            <h4>4.1 更新 config/production.js</h4>
            <div class="code-block" id="config-production">
module.exports = {
  ENV_ID: '<span class="highlight">your-env-id-here</span>',
  JWT_SECRET: 'emoji-admin-v1.0-super-secret-key-2025',
  ADMIN_CONFIG: {
    username: 'admin',
    password: '<span class="highlight">your-admin-password</span>' // 请修改默认密码
  }
};
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制配置</button>

            <h4>4.2 更新 miniprogram/app.js (第26行)</h4>
            <div class="code-block" id="miniprogram-config">
env: '<span class="highlight">your-env-id-here</span>'
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制配置</button>

            <h4>4.3 更新 admin-web/js/auth-manager.js (第26行)</h4>
            <div class="code-block" id="admin-config">
env: '<span class="highlight">your-env-id-here</span>'
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制配置</button>

            <button class="btn" onclick="generateConfigs()">自动生成配置</button>
            <button class="btn" onclick="nextStep(4)">配置已完成，继续</button>
        </div>
    </div>

    <!-- 步骤5: 执行部署 -->
    <div class="step" id="step-5" style="display: none;">
        <div class="step-title">
            <span class="step-number">5</span>
            执行部署
        </div>
        <div class="step-content">
            <p>现在可以开始部署V1.0系统了：</p>

            <h4>5.1 进入项目目录</h4>
            <div class="code-block">
cd v1.0-project
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <h4>5.2 安装依赖</h4>
            <div class="code-block">
# 安装云函数依赖
cd cloudfunctions/loginAPI && npm install && cd ../..
cd cloudfunctions/webAdminAPI && npm install && cd ../..
cd cloudfunctions/dataAPI && npm install && cd ../..

# 安装项目依赖
npm install
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <h4>5.3 执行自动化部署</h4>
            <div class="code-block" id="deploy-command">
# 执行部署脚本
node scripts/deploy.js
            </div>
            <button class="btn-copy" onclick="copyCode(this)">复制命令</button>

            <div class="success">
                <div class="success-title">🎉 部署完成</div>
                <p>如果看到"部署成功"的提示，说明V1.0系统已经成功部署！</p>
                <p>接下来可以：</p>
                <ul>
                    <li>打开 <code>链路打通验证.html</code> 验证系统状态</li>
                    <li>访问管理后台进行功能测试</li>
                    <li>在微信开发者工具中测试小程序</li>
                </ul>
            </div>

            <button class="btn" onclick="openVerification()">打开链路验证</button>
            <button class="btn" onclick="showSummary()">查看部署总结</button>
        </div>
    </div>

    <!-- 部署总结 -->
    <div class="step" id="summary" style="display: none;">
        <div class="step-title">
            🎯 部署总结
        </div>
        <div class="step-content">
            <h4>已完成的部署内容：</h4>
            <ul class="checklist">
                <li class="checked">云开发环境创建</li>
                <li class="checked">云函数部署 (loginAPI, webAdminAPI, dataAPI)</li>
                <li class="checked">数据库初始化</li>
                <li class="checked">管理后台部署</li>
                <li class="checked">配置文件更新</li>
            </ul>

            <h4>下一步操作：</h4>
            <ol>
                <li><strong>验证系统</strong>: 运行链路验证工具确保所有功能正常</li>
                <li><strong>测试功能</strong>: 登录管理后台测试各项功能</li>
                <li><strong>配置小程序</strong>: 在微信开发者工具中配置并上传小程序</li>
                <li><strong>生产发布</strong>: 确认测试无误后提交小程序审核</li>
            </ol>

            <h4>重要信息记录：</h4>
            <div class="code-block">
环境ID: <span id="final-env-id">请填写您的环境ID</span>
管理后台: https://<span id="final-env-id-2">your-env-id</span>.service.tcloudbase.com
管理员账号: admin
管理员密码: 您设置的密码
            </div>

            <div class="warning">
                <div class="warning-title">🔒 安全提醒</div>
                <p>请务必修改默认的管理员密码，并妥善保管您的环境ID和登录凭据。</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 5;

        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progress').style.width = progress + '%';
        }

        function nextStep(step) {
            if (step === 3) {
                const envId = document.getElementById('env-id').value.trim();
                if (!envId) {
                    alert('请先填写环境ID');
                    return;
                }
            }

            currentStep = step + 1;
            updateProgress();

            // 隐藏当前步骤
            document.getElementById(`step-${step}`).style.display = 'none';
            
            // 显示下一步骤
            if (step < totalSteps) {
                document.getElementById(`step-${step + 1}`).style.display = 'block';
            }
        }

        function toggleCheck(element) {
            element.classList.toggle('checked');
        }

        function copyCode(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent.trim();
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#10b981';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#10b981';
                }, 2000);
            });
        }

        function generateConfigs() {
            const envId = document.getElementById('env-id').value.trim();
            if (!envId) {
                alert('请先在步骤3中填写环境ID');
                return;
            }

            // 更新配置代码中的环境ID
            document.getElementById('config-production').innerHTML = `
module.exports = {
  ENV_ID: '<span class="highlight">${envId}</span>',
  JWT_SECRET: 'emoji-admin-v1.0-super-secret-key-2025',
  ADMIN_CONFIG: {
    username: 'admin',
    password: '<span class="highlight">请修改为您的密码</span>'
  }
};`;

            document.getElementById('miniprogram-config').innerHTML = `
env: '<span class="highlight">${envId}</span>'`;

            document.getElementById('admin-config').innerHTML = `
env: '<span class="highlight">${envId}</span>'`;

            alert('配置已自动生成，请复制到对应的文件中！');
        }

        function openVerification() {
            window.open('链路打通验证.html', '_blank');
        }

        function showSummary() {
            const envId = document.getElementById('env-id').value.trim();
            document.getElementById('final-env-id').textContent = envId;
            document.getElementById('final-env-id-2').textContent = envId;
            
            document.getElementById('step-5').style.display = 'none';
            document.getElementById('summary').style.display = 'block';
            
            currentStep = totalSteps;
            updateProgress();
        }

        // 初始化
        updateProgress();
    </script>
</body>
</html>
