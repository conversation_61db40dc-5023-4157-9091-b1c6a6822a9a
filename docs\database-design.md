# 数据库设计文档

## 1. 数据库概述

### 1.1 技术选型
- **主数据库**: MongoDB 6.0+
- **缓存数据库**: Redis 7.0+
- **搜索引擎**: Elasticsearch 8.0+ (可选)

### 1.2 设计原则
- 数据一致性优先
- 支持水平扩展
- 查询性能优化
- 数据安全保护

## 2. 集合设计

### 2.1 表情包集合 (emojis)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439011"),
  title: "哈哈哈笑死我了",                    // 表情包标题
  description: "超级搞笑的表情包",             // 描述
  slug: "hahaha-xiaosiwole",                // URL友好标识
  
  // 文件信息
  files: {
    original: {
      url: "https://cdn.example.com/emoji_001.jpg",
      size: 245760,                         // 文件大小（字节）
      format: "jpg",                        // 文件格式
      dimensions: {
        width: 500,
        height: 500
      }
    },
    thumbnail: {
      url: "https://cdn.example.com/emoji_001_thumb.jpg",
      size: 15360,
      dimensions: {
        width: 150,
        height: 150
      }
    },
    webp: {                                 // WebP格式（可选）
      url: "https://cdn.example.com/emoji_001.webp",
      size: 98304
    }
  },
  
  // 分类和标签
  category: {
    id: ObjectId("507f1f77bcf86cd799439012"),
    name: "搞笑幽默",
    slug: "funny"
  },
  tags: ["搞笑", "哈哈", "笑死", "表情"],      // 标签数组
  
  // 统计数据
  stats: {
    views: 25680,                           // 浏览次数
    likes: 12340,                           // 点赞次数
    downloads: 3702,                        // 下载次数
    collections: 1856,                      // 收藏次数
    shares: 890                             // 分享次数
  },
  
  // 状态和权重
  status: "published",                      // published/draft/archived/deleted
  featured: false,                          // 是否精选
  sortOrder: 0,                            // 排序权重
  
  // SEO信息
  seo: {
    keywords: ["搞笑表情包", "哈哈表情"],
    metaDescription: "超级搞笑的表情包，让你笑到停不下来"
  },
  
  // 审核信息
  moderation: {
    status: "approved",                     // pending/approved/rejected
    reviewedBy: ObjectId("507f1f77bcf86cd799439013"),
    reviewedAt: ISODate("2024-01-15T10:30:00Z"),
    notes: "内容健康，通过审核"
  },
  
  // 创建和更新信息
  createdBy: ObjectId("507f1f77bcf86cd799439013"),
  createdAt: ISODate("2024-01-15T00:00:00Z"),
  updatedAt: ISODate("2024-01-15T00:00:00Z"),
  
  // 索引字段
  searchText: "哈哈哈笑死我了 搞笑 哈哈 笑死 表情", // 用于全文搜索
}
```

**索引设计**:
```javascript
// 复合索引
db.emojis.createIndex({ "status": 1, "createdAt": -1 })
db.emojis.createIndex({ "category.id": 1, "sortOrder": 1 })
db.emojis.createIndex({ "stats.downloads": -1 })
db.emojis.createIndex({ "featured": 1, "stats.likes": -1 })

// 文本搜索索引
db.emojis.createIndex({ 
  "title": "text", 
  "description": "text", 
  "tags": "text",
  "searchText": "text"
})

// 地理位置索引（如果需要）
db.emojis.createIndex({ "location": "2dsphere" })
```

### 2.2 分类集合 (categories)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439012"),
  name: "搞笑幽默",                          // 分类名称
  slug: "funny",                           // URL友好标识
  icon: "😂",                              // 分类图标
  color: "#FF6B6B",                        // 主题色
  description: "搞笑幽默类表情包",           // 分类描述
  
  // 层级结构
  parentId: null,                          // 父分类ID，null表示顶级分类
  level: 0,                               // 层级深度
  path: "/funny",                         // 分类路径
  
  // 显示设置
  sortOrder: 1,                           // 排序权重
  isVisible: true,                        // 是否显示
  isDefault: false,                       // 是否默认分类
  
  // 统计信息
  stats: {
    emojiCount: 156,                      // 表情包数量
    totalDownloads: 50000,                // 总下载量
    totalViews: 200000                    // 总浏览量
  },
  
  // SEO信息
  seo: {
    title: "搞笑幽默表情包大全",
    keywords: ["搞笑表情包", "幽默表情"],
    description: "最新最全的搞笑幽默表情包"
  },
  
  // 状态
  status: "active",                       // active/inactive/deleted
  
  // 时间戳
  createdAt: ISODate("2024-01-01T00:00:00Z"),
  updatedAt: ISODate("2024-01-15T00:00:00Z")
}
```

**索引设计**:
```javascript
db.categories.createIndex({ "parentId": 1, "sortOrder": 1 })
db.categories.createIndex({ "slug": 1 }, { unique: true })
db.categories.createIndex({ "status": 1, "isVisible": 1 })
```

### 2.3 用户集合 (users)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439013"),
  
  // 微信信息
  wechat: {
    openid: "oxxxxxxxxxxxxxxxxxxxxxx",     // 微信openid
    unionid: "uxxxxxxxxxxxxxxxxxxxxxx",    // 微信unionid
    sessionKey: "encrypted_session_key"    // 加密的session_key
  },
  
  // 基本信息
  profile: {
    nickname: "张三",                      // 昵称
    avatar: "https://cdn.example.com/avatar_001.jpg",
    gender: 1,                            // 性别：0未知/1男/2女
    country: "中国",
    province: "广东",
    city: "深圳",
    language: "zh_CN"
  },
  
  // 联系信息
  contact: {
    email: "<EMAIL>",        // 邮箱
    phone: "13800138000",                 // 手机号
    isEmailVerified: true,                // 邮箱是否验证
    isPhoneVerified: false                // 手机号是否验证
  },
  
  // 权限角色
  auth: {
    role: "user",                         // admin/moderator/user
    permissions: ["emoji:read", "emoji:download"],
    status: "active",                     // active/inactive/banned/deleted
    banReason: null,                      // 封禁原因
    banExpiredAt: null                    // 封禁到期时间
  },
  
  // 用户偏好
  preferences: {
    language: "zh-CN",                    // 语言偏好
    theme: "auto",                        // 主题：light/dark/auto
    notifications: {
      email: true,                        // 邮件通知
      push: true,                         // 推送通知
      sms: false                          // 短信通知
    },
    privacy: {
      showProfile: true,                  // 是否公开个人资料
      showActivity: false                 // 是否公开活动记录
    }
  },
  
  // 统计数据
  stats: {
    likeCount: 156,                       // 点赞数
    collectCount: 89,                     // 收藏数
    downloadCount: 234,                   // 下载数
    shareCount: 45,                       // 分享数
    loginCount: 67,                       // 登录次数
    totalOnlineTime: 12600,               // 总在线时长（秒）
    level: 3,                             // 用户等级
    points: 1250                          // 积分
  },
  
  // 设备信息
  devices: [
    {
      deviceId: "device_xxx",
      platform: "ios",                   // ios/android/web
      version: "1.0.0",
      lastUsedAt: ISODate("2024-01-15T10:30:00Z")
    }
  ],
  
  // 时间记录
  timestamps: {
    firstLoginAt: ISODate("2024-01-01T00:00:00Z"),
    lastLoginAt: ISODate("2024-01-15T10:30:00Z"),
    lastActiveAt: ISODate("2024-01-15T10:35:00Z"),
    createdAt: ISODate("2024-01-01T00:00:00Z"),
    updatedAt: ISODate("2024-01-15T10:30:00Z")
  }
}
```

**索引设计**:
```javascript
db.users.createIndex({ "wechat.openid": 1 }, { unique: true })
db.users.createIndex({ "wechat.unionid": 1 }, { sparse: true })
db.users.createIndex({ "contact.email": 1 }, { sparse: true })
db.users.createIndex({ "auth.status": 1 })
db.users.createIndex({ "timestamps.lastActiveAt": -1 })
```

### 2.4 用户行为集合 (user_actions)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439014"),
  userId: ObjectId("507f1f77bcf86cd799439013"),
  action: "like",                         // like/unlike/collect/uncollect/download/share/view
  targetType: "emoji",                    // emoji/category/user
  targetId: ObjectId("507f1f77bcf86cd799439011"),
  
  // 行为详情
  details: {
    source: "homepage",                   // 来源页面
    position: 3,                          // 位置（如列表中的第几个）
    searchKeyword: "搞笑",                // 搜索关键词（如果适用）
    referrer: "category_funny"            // 引荐来源
  },
  
  // 设备信息
  device: {
    platform: "ios",
    version: "1.0.0",
    ip: "***********",
    userAgent: "Mozilla/5.0..."
  },
  
  // 时间戳
  createdAt: ISODate("2024-01-15T10:30:00Z"),
  
  // TTL索引，30天后自动删除
  expireAt: ISODate("2024-02-14T10:30:00Z")
}
```

**索引设计**:
```javascript
db.user_actions.createIndex({ "userId": 1, "action": 1, "createdAt": -1 })
db.user_actions.createIndex({ "targetType": 1, "targetId": 1, "action": 1 })
db.user_actions.createIndex({ "expireAt": 1 }, { expireAfterSeconds: 0 })
```

### 2.5 轮播图集合 (banners)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439015"),
  title: "新年表情包",                     // 标题
  subtitle: "龙年大吉，表情包拜年",         // 副标题
  
  // 图片信息
  image: {
    url: "https://cdn.example.com/banner_001.jpg",
    width: 1920,
    height: 600,
    size: 512000
  },
  
  // 链接信息
  link: {
    type: "category",                     // category/emoji/external/none
    value: "festival",                    // 链接值
    url: "/category/festival",            // 实际跳转URL
    openType: "navigate"                  // navigate/redirect/external
  },
  
  // 按钮设置
  button: {
    text: "点击查看详情",
    color: "#FFFFFF",
    backgroundColor: "rgba(0,0,0,0.5)"
  },
  
  // 显示设置
  display: {
    sortOrder: 1,                         // 排序
    position: "top",                      // 显示位置：top/middle/bottom
    showOnPages: ["home", "category"],    // 显示页面
    showOnDevices: ["mobile", "tablet"]   // 显示设备
  },
  
  // 时间设置
  schedule: {
    startTime: ISODate("2024-01-01T00:00:00Z"),
    endTime: ISODate("2024-02-01T00:00:00Z"),
    timezone: "Asia/Shanghai"
  },
  
  // 统计数据
  stats: {
    impressions: 50000,                   // 展示次数
    clicks: 1250,                         // 点击次数
    ctr: 0.025                           // 点击率
  },
  
  // 状态
  status: "active",                       // active/inactive/expired/deleted
  
  // 创建信息
  createdBy: ObjectId("507f1f77bcf86cd799439013"),
  createdAt: ISODate("2024-01-01T00:00:00Z"),
  updatedAt: ISODate("2024-01-15T00:00:00Z")
}
```

### 2.6 系统配置集合 (system_configs)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439016"),
  key: "upload_settings",                 // 配置键
  value: {
    maxFileSize: 10485760,                // 最大文件大小（字节）
    allowedFormats: ["jpg", "png", "gif", "webp"],
    imageQuality: 85,                     // 图片质量
    thumbnailSize: 150,                   // 缩略图尺寸
    watermark: {
      enabled: false,
      text: "表情包小程序",
      position: "bottom-right",
      opacity: 0.5
    }
  },
  description: "文件上传相关设置",
  category: "upload",                     // 配置分类
  isPublic: false,                        // 是否公开（前端可访问）
  version: 1,                             // 配置版本
  createdAt: ISODate("2024-01-01T00:00:00Z"),
  updatedAt: ISODate("2024-01-15T00:00:00Z")
}
```

### 2.7 操作日志集合 (operation_logs)

```javascript
{
  _id: ObjectId("507f1f77bcf86cd799439017"),
  
  // 操作信息
  operation: {
    type: "create",                       // create/update/delete/login/logout
    module: "emoji",                      // emoji/category/user/system
    action: "create_emoji",               // 具体操作
    description: "创建表情包：哈哈哈笑死我了"
  },
  
  // 操作者信息
  operator: {
    id: ObjectId("507f1f77bcf86cd799439013"),
    type: "admin",                        // admin/user/system
    nickname: "管理员",
    ip: "*************"
  },
  
  // 目标信息
  target: {
    type: "emoji",
    id: ObjectId("507f1f77bcf86cd799439011"),
    title: "哈哈哈笑死我了"
  },
  
  // 变更详情
  changes: {
    before: null,                         // 变更前的数据
    after: {                             // 变更后的数据
      title: "哈哈哈笑死我了",
      category: "funny",
      status: "published"
    }
  },
  
  // 请求信息
  request: {
    method: "POST",
    url: "/api/v1/emojis",
    userAgent: "Mozilla/5.0...",
    requestId: "req_xxxxxxxxxx"
  },
  
  // 结果信息
  result: {
    success: true,
    code: 200,
    message: "创建成功",
    duration: 150                         // 执行时长（毫秒）
  },
  
  // 时间戳
  createdAt: ISODate("2024-01-15T10:30:00Z"),
  
  // TTL索引，90天后自动删除
  expireAt: ISODate("2024-04-15T10:30:00Z")
}
```

## 3. 数据关系

### 3.1 关系图
```
Users (1) -----> (N) UserActions
Users (1) -----> (N) OperationLogs
Categories (1) -> (N) Emojis
Categories (1) -> (N) Categories (自关联)
Emojis (1) -----> (N) UserActions
```

### 3.2 引用完整性
- 使用 MongoDB 的引用字段存储关联ID
- 在应用层维护引用完整性
- 定期运行数据一致性检查脚本

## 4. 性能优化

### 4.1 索引策略
- 为常用查询字段创建索引
- 使用复合索引优化多字段查询
- 定期分析慢查询并优化索引

### 4.2 分片策略
```javascript
// 按用户ID分片用户行为数据
sh.shardCollection("emoji_db.user_actions", { "userId": 1 })

// 按创建时间分片操作日志
sh.shardCollection("emoji_db.operation_logs", { "createdAt": 1 })
```

### 4.3 缓存策略
- 热门表情包数据缓存（Redis）
- 分类列表缓存（Redis）
- 用户会话缓存（Redis）
- 统计数据缓存（Redis）

## 5. 数据安全

### 5.1 敏感数据加密
```javascript
// 用户敏感信息加密
{
  contact: {
    email: encrypt("<EMAIL>"),
    phone: encrypt("13800138000")
  },
  wechat: {
    sessionKey: encrypt("session_key_value")
  }
}
```

### 5.2 访问控制
- 数据库用户权限最小化
- 应用层权限验证
- 敏感操作审计日志

### 5.3 备份策略
- 每日全量备份
- 每小时增量备份
- 异地备份存储
- 定期恢复测试

## 6. 监控指标

### 6.1 性能指标
- 查询响应时间
- 索引使用率
- 连接池状态
- 内存使用率

### 6.2 业务指标
- 数据增长趋势
- 热门查询统计
- 错误率统计
- 用户活跃度

## 7. 维护脚本

### 7.1 数据清理脚本
```javascript
// 清理过期的用户行为数据
db.user_actions.deleteMany({
  createdAt: { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
})

// 清理软删除的数据
db.emojis.deleteMany({
  status: "deleted",
  updatedAt: { $lt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) }
})
```

### 7.2 统计数据更新脚本
```javascript
// 更新分类的表情包数量
db.categories.find().forEach(function(category) {
  var count = db.emojis.countDocuments({
    "category.id": category._id,
    status: "published"
  })
  
  db.categories.updateOne(
    { _id: category._id },
    { $set: { "stats.emojiCount": count } }
  )
})
```

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15