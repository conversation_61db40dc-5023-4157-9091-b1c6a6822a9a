<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页数据加载修复验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e1e8ed;
            border-radius: 15px;
            background: #f8fafc;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            background: white;
        }

        .test-result.success {
            border-left-color: #27ae60;
        }

        .test-result.warning {
            border-left-color: #f39c12;
        }

        .test-result.error {
            border-left-color: #e74c3c;
        }

        .data-preview {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 15px 0;
            overflow-x: auto;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e8ed;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .emoji-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: transform 0.2s ease;
        }

        .emoji-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .category-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s ease;
        }

        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
        }

        .summary h3 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .summary p {
            font-size: 1.1em;
            line-height: 1.6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 首页数据加载修复验证</h1>
            <p>验证云函数调用失败时的数据兜底机制</p>
        </div>

        <div class="content">
            <!-- 测试控制面板 -->
            <div class="test-section">
                <h2>🎮 测试控制面板</h2>
                <button class="btn" onclick="runAllTests()">运行完整测试</button>
                <button class="btn" onclick="testCloudSuccess()">测试云函数成功</button>
                <button class="btn" onclick="testCloudFailure()">测试云函数失败</button>
                <button class="btn" onclick="clearResults()">清空结果</button>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressBar"></div>
                </div>
                <div id="testStatus">准备开始测试...</div>
            </div>

            <!-- 分类数据测试 -->
            <div class="test-section">
                <h2>📂 分类数据加载测试</h2>
                <div id="categoryTestResult">等待测试...</div>
                <div class="category-grid" id="categoryGrid"></div>
            </div>

            <!-- 表情包数据测试 -->
            <div class="test-section">
                <h2>😊 表情包数据加载测试</h2>
                <div id="emojiTestResult">等待测试...</div>
                <div class="emoji-grid" id="emojiGrid"></div>
            </div>

            <!-- 横幅数据测试 -->
            <div class="test-section">
                <h2>🎯 横幅数据加载测试</h2>
                <div id="bannerTestResult">等待测试...</div>
                <div id="bannerGrid"></div>
            </div>

            <!-- 测试日志 -->
            <div class="test-section">
                <h2>📋 测试日志</h2>
                <div class="data-preview" id="testLog">等待测试开始...</div>
            </div>

            <!-- 总结 -->
            <div class="summary">
                <h3>✅ 修复完成总结</h3>
                <p>
                    通过实现多层数据兜底机制，确保即使在云函数调用失败的情况下，
                    首页仍能正常显示数据。修复包括：云函数调用 → 本地数据管理器 → 兜底数据的完整链路。
                </p>
            </div>
        </div>
    </div>

    <script>
        let testLog = [];
        let currentTest = 0;
        let totalTests = 3;

        // 日志记录函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('testLog');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        // 更新进度条
        function updateProgress(progress) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
        }

        // 更新测试状态
        function updateTestStatus(status, type = 'info') {
            const statusElement = document.getElementById('testStatus');
            statusElement.innerHTML = `<span class="status ${type}">${status}</span>`;
        }

        // 模拟云函数调用
        async function mockCloudFunction(name, data, shouldFail = false) {
            log(`📡 调用云函数: ${name}`);
            
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
            
            if (shouldFail) {
                throw new Error('CloudServiceUot not found - 模拟云函数调用失败');
            }
            
            // 返回模拟数据
            switch (name) {
                case 'getCategories':
                    return {
                        result: {
                            success: true,
                            data: [
                                { id: 'funny', name: '搞笑幽默', icon: '😂', emojiCount: 25 },
                                { id: 'cute', name: '可爱萌宠', icon: '🐱', emojiCount: 18 },
                                { id: 'emotion', name: '情感表达', icon: '❤️', emojiCount: 22 },
                                { id: 'festival', name: '节日庆典', icon: '🎉', emojiCount: 15 }
                            ]
                        }
                    };
                case 'getEmojis':
                    return {
                        result: {
                            success: true,
                            data: [
                                { id: 'emoji1', title: '搞笑表情包1', imageUrl: '/images/emoji-1.jpg', likes: 1234, collections: 567 },
                                { id: 'emoji2', title: '可爱表情包2', imageUrl: '/images/emoji-2.jpg', likes: 2345, collections: 678 },
                                { id: 'emoji3', title: '情感表情包3', imageUrl: '/images/emoji-3.jpg', likes: 3456, collections: 789 }
                            ]
                        }
                    };
                case 'getBanners':
                    return {
                        result: {
                            success: true,
                            data: [
                                { id: 'banner1', title: '热门表情包', subtitle: '最新最火的表情包都在这里', buttonText: '立即查看', backgroundColor: '#FF6B6B' },
                                { id: 'banner2', title: '节日专题', subtitle: '节日表情包，让聊天更有趣', buttonText: '查看更多', backgroundColor: '#4ECDC4' }
                            ]
                        }
                    };
            }
        }

        // 获取本地兜底数据
        function getLocalFallbackData(type) {
            switch (type) {
                case 'categories':
                    return [
                        { id: 'funny', name: '搞笑幽默', icon: '😂', emojiCount: 25 },
                        { id: 'cute', name: '可爱萌宠', icon: '🐱', emojiCount: 18 },
                        { id: 'emotion', name: '情感表达', icon: '❤️', emojiCount: 22 },
                        { id: 'festival', name: '节日庆典', icon: '🎉', emojiCount: 15 }
                    ];
                case 'emojis':
                    return [
                        { id: 'local1', title: '本地测试表情包1', imageUrl: '/images/emoji-1.jpg', likes: 100, collections: 50 },
                        { id: 'local2', title: '本地测试表情包2', imageUrl: '/images/emoji-2.jpg', likes: 200, collections: 75 },
                        { id: 'local3', title: '本地测试表情包3', imageUrl: '/images/emoji-3.jpg', likes: 300, collections: 100 }
                    ];
                case 'banners':
                    return [
                        { id: 'local_banner1', title: '本地横幅1', subtitle: '本地测试横幅描述', buttonText: '查看详情', backgroundColor: '#999999' },
                        { id: 'local_banner2', title: '本地横幅2', subtitle: '兜底数据展示', buttonText: '了解更多', backgroundColor: '#666666' }
                    ];
            }
        }

        // 测试分类数据加载
        async function testCategoryData(shouldFail = false) {
            log('🧪 开始测试分类数据加载...');
            
            let categories = [];
            let dataSource = 'unknown';
            
            try {
                // 尝试云函数调用
                const result = await mockCloudFunction('getCategories', {}, shouldFail);
                if (result.result && result.result.success && result.result.data) {
                    categories = result.result.data;
                    dataSource = 'cloud';
                    log('✅ 从云函数获取分类数据成功');
                }
            } catch (error) {
                log(`⚠️ 云函数调用失败: ${error.message}`);
                
                // 使用本地兜底数据
                categories = getLocalFallbackData('categories');
                dataSource = 'local';
                log('✅ 使用本地兜底数据');
            }
            
            // 渲染分类数据
            const categoryGrid = document.getElementById('categoryGrid');
            categoryGrid.innerHTML = '';
            
            categories.forEach(category => {
                const card = document.createElement('div');
                card.className = 'category-card fade-in';
                card.innerHTML = `
                    <div class="icon">${category.icon}</div>
                    <h4>${category.name}</h4>
                    <p>${category.emojiCount} 个表情包</p>
                    <small>数据源: ${dataSource}</small>
                `;
                categoryGrid.appendChild(card);
            });
            
            // 更新测试结果
            const resultElement = document.getElementById('categoryTestResult');
            resultElement.innerHTML = `
                <div class="test-result success">
                    ✅ 分类数据加载成功 (${dataSource}): ${categories.length} 个分类
                </div>
            `;
            
            log(`✅ 分类数据测试完成，共加载 ${categories.length} 个分类`);
        }

        // 测试表情包数据加载
        async function testEmojiData(shouldFail = false) {
            log('🧪 开始测试表情包数据加载...');
            
            let emojis = [];
            let dataSource = 'unknown';
            
            try {
                // 尝试云函数调用
                const result = await mockCloudFunction('getEmojis', {}, shouldFail);
                if (result.result && result.result.success && result.result.data) {
                    emojis = result.result.data;
                    dataSource = 'cloud';
                    log('✅ 从云函数获取表情包数据成功');
                }
            } catch (error) {
                log(`⚠️ 云函数调用失败: ${error.message}`);
                
                // 使用本地兜底数据
                emojis = getLocalFallbackData('emojis');
                dataSource = 'local';
                log('✅ 使用本地兜底数据');
            }
            
            // 渲染表情包数据
            const emojiGrid = document.getElementById('emojiGrid');
            emojiGrid.innerHTML = '';
            
            emojis.forEach(emoji => {
                const card = document.createElement('div');
                card.className = 'emoji-card fade-in';
                card.innerHTML = `
                    <h4>${emoji.title}</h4>
                    <p>👍 ${emoji.likes} | 💖 ${emoji.collections}</p>
                    <small>数据源: ${dataSource}</small>
                `;
                emojiGrid.appendChild(card);
            });
            
            // 更新测试结果
            const resultElement = document.getElementById('emojiTestResult');
            resultElement.innerHTML = `
                <div class="test-result success">
                    ✅ 表情包数据加载成功 (${dataSource}): ${emojis.length} 个表情包
                </div>
            `;
            
            log(`✅ 表情包数据测试完成，共加载 ${emojis.length} 个表情包`);
        }

        // 测试横幅数据加载
        async function testBannerData(shouldFail = false) {
            log('🧪 开始测试横幅数据加载...');
            
            let banners = [];
            let dataSource = 'unknown';
            
            try {
                // 尝试云函数调用
                const result = await mockCloudFunction('getBanners', {}, shouldFail);
                if (result.result && result.result.success && result.result.data) {
                    banners = result.result.data;
                    dataSource = 'cloud';
                    log('✅ 从云函数获取横幅数据成功');
                }
            } catch (error) {
                log(`⚠️ 云函数调用失败: ${error.message}`);
                
                // 使用本地兜底数据
                banners = getLocalFallbackData('banners');
                dataSource = 'local';
                log('✅ 使用本地兜底数据');
            }
            
            // 渲染横幅数据
            const bannerGrid = document.getElementById('bannerGrid');
            bannerGrid.innerHTML = '';
            
            banners.forEach(banner => {
                const card = document.createElement('div');
                card.className = 'emoji-card fade-in';
                card.style.backgroundColor = banner.backgroundColor || '#f8f9fa';
                card.style.color = banner.backgroundColor ? 'white' : 'black';
                card.innerHTML = `
                    <h4>${banner.title}</h4>
                    <p>${banner.subtitle}</p>
                    <button class="btn" style="background: rgba(255,255,255,0.2)">${banner.buttonText}</button>
                    <small>数据源: ${dataSource}</small>
                `;
                bannerGrid.appendChild(card);
            });
            
            // 更新测试结果
            const resultElement = document.getElementById('bannerTestResult');
            resultElement.innerHTML = `
                <div class="test-result success">
                    ✅ 横幅数据加载成功 (${dataSource}): ${banners.length} 个横幅
                </div>
            `;
            
            log(`✅ 横幅数据测试完成，共加载 ${banners.length} 个横幅`);
        }

        // 运行所有测试
        async function runAllTests(shouldFail = false) {
            log('🚀 开始运行完整测试套件...');
            updateTestStatus('测试进行中...', 'warning');
            
            currentTest = 0;
            updateProgress(0);
            
            try {
                // 测试分类数据
                currentTest++;
                updateProgress((currentTest / totalTests) * 100);
                await testCategoryData(shouldFail);
                
                // 测试表情包数据
                currentTest++;
                updateProgress((currentTest / totalTests) * 100);
                await testEmojiData(shouldFail);
                
                // 测试横幅数据
                currentTest++;
                updateProgress((currentTest / totalTests) * 100);
                await testBannerData(shouldFail);
                
                updateTestStatus('所有测试完成！', 'success');
                log('🎉 所有测试完成！数据加载功能正常工作');
                
            } catch (error) {
                updateTestStatus('测试失败！', 'error');
                log(`❌ 测试失败: ${error.message}`);
            }
        }

        // 测试云函数成功场景
        async function testCloudSuccess() {
            log('🌟 测试云函数成功场景...');
            await runAllTests(false);
        }

        // 测试云函数失败场景
        async function testCloudFailure() {
            log('⚠️ 测试云函数失败场景（验证兜底机制）...');
            await runAllTests(true);
        }

        // 清空测试结果
        function clearResults() {
            testLog = [];
            document.getElementById('testLog').textContent = '等待测试开始...';
            document.getElementById('categoryGrid').innerHTML = '';
            document.getElementById('emojiGrid').innerHTML = '';
            document.getElementById('bannerGrid').innerHTML = '';
            document.getElementById('categoryTestResult').textContent = '等待测试...';
            document.getElementById('emojiTestResult').textContent = '等待测试...';
            document.getElementById('bannerTestResult').textContent = '等待测试...';
            updateProgress(0);
            updateTestStatus('准备开始测试...', 'info');
            log('🧹 测试结果已清空');
        }

        // 页面加载完成后自动运行一次测试
        window.addEventListener('load', () => {
            log('📱 页面加载完成，准备开始测试');
            setTimeout(() => {
                testCloudFailure(); // 默认测试失败场景，验证兜底机制
            }, 1000);
        });
    </script>
</body>
</html>
