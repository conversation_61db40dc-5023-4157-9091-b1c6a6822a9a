// 最终横幅修复验证测试
const { chromium } = require('playwright');

async function finalBannerTest() {
    console.log('🎯 最终横幅修复验证测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 进入横幅管理页面');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('📍 验证横幅数据显示');
        
        // 检查表格数据
        const bannerTableData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#banner-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const titleCell = cells[2]; // 标题列
                const statusCell = cells[8]; // 状态列
                const imageCell = cells[1]; // 图片列
                
                return {
                    index: index + 1,
                    title: titleCell ? titleCell.textContent?.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent?.trim() : 'N/A',
                    hasImage: imageCell ? !!imageCell.querySelector('img') : false,
                    hasUndefined: cells.some(cell => cell.textContent?.includes('undefined')),
                    cellCount: cells.length
                };
            });
        });
        
        console.log('📊 横幅表格数据验证结果:');
        let allGood = true;
        
        bannerTableData.forEach(banner => {
            console.log(`\n横幅 ${banner.index}:`);
            console.log(`  标题: "${banner.title}"`);
            console.log(`  状态: "${banner.status}"`);
            console.log(`  有图片: ${banner.hasImage}`);
            console.log(`  有undefined: ${banner.hasUndefined}`);
            console.log(`  单元格数: ${banner.cellCount}`);
            
            // 检查问题
            if (banner.title === 'undefined' || banner.title.includes('undefined')) {
                console.log('  ❌ 标题显示undefined');
                allGood = false;
            } else if (banner.title && banner.title !== 'N/A') {
                console.log('  ✅ 标题显示正常');
            }
            
            if (banner.status === '草稿' || banner.status.includes('草稿')) {
                console.log('  ❌ 状态显示错误（草稿）');
                allGood = false;
            } else if (banner.status === '显示' || banner.status === '隐藏') {
                console.log('  ✅ 状态显示正常');
            }
            
            if (!banner.hasImage) {
                console.log('  ❌ 缺少图片预览');
                allGood = false;
            } else {
                console.log('  ✅ 图片预览正常');
            }
            
            if (banner.hasUndefined) {
                console.log('  ❌ 发现undefined数据');
                allGood = false;
            }
        });
        
        console.log('\n📊 总体评估:');
        if (allGood && bannerTableData.length > 0) {
            console.log('✅ 横幅管理功能修复成功！');
            console.log('✅ 所有数据显示正常');
            console.log('✅ 未发现undefined问题');
            console.log('✅ 状态显示正确');
            console.log('✅ 图片预览正常');
        } else if (bannerTableData.length === 0) {
            console.log('❌ 未找到横幅数据');
        } else {
            console.log('⚠️ 仍有部分问题需要修复');
        }
        
        // 截图
        await page.screenshot({ path: 'final-banner-test.png', fullPage: true });
        console.log('\n📸 最终测试截图已保存: final-banner-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
finalBannerTest().catch(console.error);
