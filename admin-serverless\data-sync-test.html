<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步测试 - 管理后台到小程序</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-button {
            background: #07c160;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #06ad56;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🔄 数据同步测试</h1>
    <p>测试管理后台数据是否能实时同步到微信小程序</p>

    <!-- SDK初始化测试 -->
    <div class="test-card">
        <div class="test-title">1. 🌤️ CloudBase SDK 2.17.5 初始化</div>
        <button class="test-button" onclick="testSDKInit()">初始化 CloudBase SDK</button>
        <div id="sdk-init-result" class="test-result"></div>
    </div>

    <!-- 匿名登录测试 -->
    <div class="test-card">
        <div class="test-title">2. 🔐 匿名登录测试</div>
        <button class="test-button" onclick="testAnonymousLogin()">测试匿名登录</button>
        <div id="login-result" class="test-result"></div>
    </div>

    <!-- 数据写入测试 -->
    <div class="test-card">
        <div class="test-title">3. 📝 数据写入测试</div>
        <button class="test-button" onclick="testDataWrite()">写入测试数据</button>
        <button class="test-button" onclick="testDataRead()">读取数据验证</button>
        <div id="data-result" class="test-result"></div>
    </div>

    <!-- 实时同步验证 -->
    <div class="test-card">
        <div class="test-title">4. 🔄 实时同步验证</div>
        <button class="test-button" onclick="startSyncTest()">开始同步测试</button>
        <button class="test-button" onclick="stopSyncTest()">停止测试</button>
        <div id="sync-result" class="test-result"></div>
    </div>

    <script>
        let app = null;
        let syncInterval = null;

        // 加载脚本的辅助函数
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // 1. 测试SDK初始化
        async function testSDKInit() {
            const resultDiv = document.getElementById('sdk-init-result');
            resultDiv.textContent = '正在初始化CloudBase SDK 2.17.5...\n';

            try {
                // 加载CloudBase SDK 2.17.5
                if (!window.cloudbase) {
                    resultDiv.textContent += '📦 加载CloudBase SDK 2.17.5...\n';
                    await loadScript('https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js');
                    resultDiv.textContent += '✅ SDK加载成功\n';
                }

                // 初始化应用 - CloudBase 2.x版本需要clientId
                app = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142' // 使用环境ID作为clientId
                });

                resultDiv.textContent += '✅ CloudBase SDK 2.17.5 初始化成功\n';
                resultDiv.textContent += `🔧 环境ID: cloud1-5g6pvnpl88dc0142\n`;
                
            } catch (error) {
                resultDiv.textContent += `❌ 初始化失败: ${error.message}\n`;
            }
        }

        // 2. 测试匿名登录
        async function testAnonymousLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.textContent = '正在测试匿名登录...\n';

            try {
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                // 详细检查auth对象
                const auth = app.auth();
                resultDiv.textContent += `🔍 Auth对象检查: ${auth ? '存在' : '不存在'}\n`;

                if (!auth) {
                    throw new Error('Auth对象初始化失败');
                }

                if (typeof auth.signInAnonymously !== 'function') {
                    throw new Error('signInAnonymously方法不存在');
                }

                resultDiv.textContent += '🔐 尝试匿名登录...\n';

                // 添加详细的错误处理
                const loginResult = await auth.signInAnonymously();
                resultDiv.textContent += `🔍 登录结果: ${loginResult ? '有返回值' : '无返回值'}\n`;

                const loginState = await auth.getLoginState();
                resultDiv.textContent += `🔍 登录状态: ${loginState ? '获取成功' : '获取失败'}\n`;

                if (loginState && loginState.user) {
                    resultDiv.textContent += `✅ 匿名登录成功\n`;
                    resultDiv.textContent += `👤 用户ID: ${loginState.user.uid}\n`;
                    resultDiv.textContent += `🔒 匿名用户: ${loginState.isAnonymous}\n`;
                } else {
                    throw new Error('登录状态获取失败');
                }

            } catch (error) {
                resultDiv.textContent += `❌ 登录失败: ${error.message}\n`;
                resultDiv.textContent += `🔍 错误详情: ${JSON.stringify(error, null, 2)}\n`;
                console.error('登录错误详情:', error);
            }
        }

        // 3. 测试数据写入
        async function testDataWrite() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.textContent = '正在写入测试数据...\n';

            try {
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const db = app.database();
                const testData = {
                    name: '测试表情包',
                    category: '测试分类',
                    url: 'https://example.com/test.jpg',
                    createTime: new Date(),
                    source: '管理后台数据同步测试'
                };

                const result = await db.collection('emojis').add(testData);
                resultDiv.textContent += `✅ 数据写入成功\n`;
                resultDiv.textContent += `📄 文档ID: ${result.id}\n`;
                resultDiv.textContent += `📝 写入数据: ${JSON.stringify(testData, null, 2)}\n`;
                
            } catch (error) {
                resultDiv.textContent += `❌ 数据写入失败: ${error.message}\n`;
            }
        }

        // 4. 测试数据读取
        async function testDataRead() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.textContent += '\n正在读取数据验证...\n';

            try {
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const db = app.database();
                const result = await db.collection('emojis')
                    .where({
                        source: '管理后台数据同步测试'
                    })
                    .get();

                resultDiv.textContent += `✅ 数据读取成功\n`;
                resultDiv.textContent += `📊 找到 ${result.data.length} 条测试数据\n`;
                
                if (result.data.length > 0) {
                    resultDiv.textContent += `📄 最新数据: ${JSON.stringify(result.data[0], null, 2)}\n`;
                }
                
            } catch (error) {
                resultDiv.textContent += `❌ 数据读取失败: ${error.message}\n`;
            }
        }

        // 5. 开始同步测试
        async function startSyncTest() {
            const resultDiv = document.getElementById('sync-result');
            resultDiv.textContent = '开始实时同步测试...\n';

            if (syncInterval) {
                clearInterval(syncInterval);
            }

            let testCount = 0;
            syncInterval = setInterval(async () => {
                testCount++;
                try {
                    if (!app) {
                        resultDiv.textContent += '❌ SDK未初始化\n';
                        return;
                    }

                    const db = app.database();
                    const testData = {
                        name: `同步测试-${testCount}`,
                        category: '同步测试',
                        createTime: new Date(),
                        testNumber: testCount
                    };

                    // 写入数据
                    const writeResult = await db.collection('emojis').add(testData);
                    
                    // 立即读取验证
                    const readResult = await db.collection('emojis').doc(writeResult.id).get();
                    
                    resultDiv.textContent += `✅ 第${testCount}次同步测试成功 - ${new Date().toLocaleTimeString()}\n`;
                    resultDiv.textContent += `   写入ID: ${writeResult.id}\n`;
                    resultDiv.textContent += `   读取验证: ${readResult.data ? '成功' : '失败'}\n\n`;
                    
                } catch (error) {
                    resultDiv.textContent += `❌ 第${testCount}次同步测试失败: ${error.message}\n`;
                }
            }, 3000); // 每3秒测试一次

            resultDiv.textContent += '🔄 同步测试已启动，每3秒执行一次...\n\n';
        }

        // 6. 停止同步测试
        function stopSyncTest() {
            if (syncInterval) {
                clearInterval(syncInterval);
                syncInterval = null;
                const resultDiv = document.getElementById('sync-result');
                resultDiv.textContent += '⏹️ 同步测试已停止\n';
            }
        }
    </script>
</body>
</html>
