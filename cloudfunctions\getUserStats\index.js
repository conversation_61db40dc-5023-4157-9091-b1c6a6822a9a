// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext()
  const { action = 'getStats' } = event
  
  try {
    // 根据 action 执行不同的操作
    switch (action) {
      case 'getStats':
        return await getUserStats(OPENID)
      case 'getAllUserData':
        return await getAllUserData(OPENID)
      default:
        return {
          success: false,
          error: '未知的操作类型'
        }
    }
  } catch (error) {
    console.error('getUserStats 云函数执行失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取用户基本统计数据
 */
async function getUserStats(openid) {
  try {
    // 获取用户点赞和收藏数量
    const [likeResult, collectResult] = await Promise.all([
      db.collection('user_likes').where({
        userId: openid
      }).count(),
      db.collection('user_collections').where({
        userId: openid
      }).count()
    ])
    
    return {
      success: true,
      data: {
        likeCount: likeResult.total,
        collectCount: collectResult.total
      }
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取用户完整数据（用于数据同步）
 */
async function getAllUserData(openid) {
  try {
    console.log('获取用户完整数据:', openid)
    
    // 从 users 集合获取用户数据
    const userResult = await db.collection('users').doc(openid).get()
    
    if (!userResult.data) {
      // 用户不存在，返回空数据
      console.log('用户不存在，返回空数据')
      return {
        success: true,
        data: {
          likedEmojis: [],
          collectedEmojis: [],
          downloadedEmojis: [],
          downloadTimes: {}
        }
      }
    }
    
    const userData = userResult.data
    
    // 确保数据格式正确
    const likedEmojis = userData.likedEmojis || []
    const collectedEmojis = userData.collectedEmojis || []
    const downloadedEmojis = userData.downloadedEmojis || []
    const downloadTimes = userData.downloadTimes || {}
    
    console.log('用户数据获取成功:', {
      liked: likedEmojis.length,
      collected: collectedEmojis.length,
      downloaded: downloadedEmojis.length
    })
    
    return {
      success: true,
      data: {
        likedEmojis,
        collectedEmojis,
        downloadedEmojis,
        downloadTimes
      }
    }
  } catch (error) {
    console.error('获取用户完整数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}