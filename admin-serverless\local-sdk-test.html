<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地SDK测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        .result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 本地SDK测试</h1>
    <p>使用内嵌的云开发SDK进行测试</p>

    <div class="card">
        <h3>📋 测试步骤</h3>
        <button class="btn" onclick="testLocalSDK()">测试本地SDK</button>
        <button class="btn" onclick="testCloudConnection()">测试云连接</button>
        <button class="btn" onclick="testDataWrite()">测试数据写入</button>
        <div id="result" class="result"></div>
    </div>

    <!-- 内嵌云开发SDK -->
    <script>
        // 简化版的云开发SDK模拟
        window.tcb = {
            init: function(config) {
                console.log('初始化云开发:', config);
                return {
                    auth: function() {
                        return {
                            signInAnonymously: async function() {
                                // 模拟匿名登录
                                await new Promise(resolve => setTimeout(resolve, 1000));
                                this.currentUser = {
                                    uid: 'test_user_' + Date.now(),
                                    isAnonymous: true
                                };
                                return this.currentUser;
                            },
                            currentUser: null
                        };
                    },
                    database: function() {
                        return {
                            collection: function(name) {
                                return {
                                    add: async function(data) {
                                        console.log(`模拟写入到集合 ${name}:`, data);
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                        return { _id: 'mock_id_' + Date.now() };
                                    },
                                    get: async function() {
                                        console.log(`模拟查询集合 ${name}`);
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                        return { data: [] };
                                    },
                                    count: async function() {
                                        console.log(`模拟统计集合 ${name}`);
                                        await new Promise(resolve => setTimeout(resolve, 500));
                                        return { total: 0 };
                                    }
                                };
                            }
                        };
                    },
                    callFunction: async function(options) {
                        console.log('模拟云函数调用:', options);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        // 模拟不同的云函数响应
                        if (options.name === 'dataAPI') {
                            if (options.data.action === 'getCategories') {
                                return {
                                    result: {
                                        success: true,
                                        data: [
                                            { id: 'test_funny', name: '测试搞笑', status: 'show' }
                                        ]
                                    }
                                };
                            } else if (options.data.action === 'getEmojis') {
                                return {
                                    result: {
                                        success: true,
                                        data: [
                                            { id: 'test_emoji_1', name: '测试表情1', status: 'published' }
                                        ]
                                    }
                                };
                            }
                        }
                        
                        return {
                            result: {
                                success: true,
                                message: '模拟调用成功'
                            }
                        };
                    }
                };
            }
        };

        function log(message) {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        // 测试本地SDK
        async function testLocalSDK() {
            log('🔧 开始测试本地SDK...');
            
            try {
                // 初始化
                const app = window.tcb.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });
                log('✅ SDK初始化成功');

                // 登录
                const auth = app.auth();
                await auth.signInAnonymously();
                log(`✅ 匿名登录成功，用户ID: ${auth.currentUser.uid}`);

                // 测试数据库
                const db = app.database();
                const result = await db.collection('test').get();
                log('✅ 数据库连接成功');

                log('🎉 本地SDK测试通过！');
                
            } catch (error) {
                log(`❌ 本地SDK测试失败: ${error.message}`);
            }
        }

        // 测试云连接（使用真实的fetch请求）
        async function testCloudConnection() {
            log('🌐 开始测试云连接...');
            
            try {
                // 测试腾讯云域名连接
                const testUrls = [
                    'https://tcb-api.tencentcloudapi.com',
                    'https://cloud.tencent.com',
                    'https://console.cloud.tencent.com'
                ];

                for (const url of testUrls) {
                    try {
                        log(`🔄 测试连接: ${url}`);
                        const response = await fetch(url, { 
                            method: 'HEAD', 
                            mode: 'no-cors',
                            timeout: 5000 
                        });
                        log(`✅ 连接成功: ${url}`);
                    } catch (error) {
                        log(`❌ 连接失败: ${url} - ${error.message}`);
                    }
                }

                log('🎯 云连接测试完成');
                
            } catch (error) {
                log(`❌ 云连接测试失败: ${error.message}`);
            }
        }

        // 测试数据写入
        async function testDataWrite() {
            log('📝 开始测试数据写入...');
            
            try {
                const app = window.tcb.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });

                const auth = app.auth();
                await auth.signInAnonymously();
                log('✅ 身份验证成功');

                const db = app.database();
                
                // 测试写入分类
                const categoryResult = await db.collection('categories').add({
                    data: {
                        id: 'test_category',
                        name: '测试分类',
                        status: 'show',
                        createTime: new Date()
                    }
                });
                log(`✅ 分类写入成功，ID: ${categoryResult._id}`);

                // 测试写入表情包
                const emojiResult = await db.collection('emojis').add({
                    data: {
                        id: 'test_emoji',
                        name: '测试表情',
                        categoryId: 'test_category',
                        status: 'published',
                        createTime: new Date()
                    }
                });
                log(`✅ 表情包写入成功，ID: ${emojiResult._id}`);

                // 测试云函数调用
                const funcResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                log(`✅ 云函数调用成功: ${JSON.stringify(funcResult.result)}`);

                log('🎉 数据写入测试通过！');
                
            } catch (error) {
                log(`❌ 数据写入测试失败: ${error.message}`);
            }
        }
    </script>
</body>
</html>
