/**
 * 云开发环境配置管理
 * 统一管理所有云开发相关的配置
 */

const CloudConfig = {
  // 环境配置
  environments: {
    development: {
      name: '开发环境',
      envId: 'cloud1-5g6pvnpl88dc0142',
      region: 'ap-shanghai',
      timeout: 10000,
      retryCount: 3
    },
    testing: {
      name: '测试环境', 
      envId: 'cloud1-5g6pvnpl88dc0142',
      region: 'ap-shanghai',
      timeout: 8000,
      retryCount: 2
    },
    production: {
      name: '生产环境',
      envId: 'cloud1-5g6pvnpl88dc0142',
      region: 'ap-shanghai',
      timeout: 5000,
      retryCount: 1
    }
  },

  // 当前环境（从环境配置中获取）
  getCurrentEnv() {
    const { EnvironmentConfig } = require('./environment.js')
    return EnvironmentConfig.currentEnv || 'development'
  },

  // 获取当前环境的云开发配置
  getCurrentConfig() {
    const currentEnv = this.getCurrentEnv()
    const config = this.environments[currentEnv]
    
    if (!config) {
      console.warn(`⚠️ 未找到环境 ${currentEnv} 的配置，使用开发环境配置`)
      return this.environments.development
    }
    
    return config
  },

  // 获取云环境ID
  getEnvId() {
    const config = this.getCurrentConfig()
    return config.envId
  },

  // 获取云开发初始化配置
  getInitConfig() {
    const config = this.getCurrentConfig()
    
    return {
      env: config.envId,
      traceUser: true,
      timeout: config.timeout
    }
  },

  // 验证配置有效性
  validateConfig() {
    const config = this.getCurrentConfig()
    const errors = []

    if (!config.envId) {
      errors.push('云环境ID不能为空')
    }

    if (!config.envId.startsWith('cloud1-')) {
      errors.push('云环境ID格式不正确')
    }

    if (config.timeout < 1000) {
      errors.push('超时时间不能小于1秒')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // 获取云函数配置
  getCloudFunctionConfig() {
    const config = this.getCurrentConfig()
    
    return {
      timeout: config.timeout,
      retryCount: config.retryCount,
      env: config.envId
    }
  },

  // 获取数据库配置
  getDatabaseConfig() {
    const config = this.getCurrentConfig()
    
    return {
      env: config.envId,
      timeout: config.timeout
    }
  },

  // 获取存储配置
  getStorageConfig() {
    const config = this.getCurrentConfig()
    
    return {
      env: config.envId,
      timeout: config.timeout,
      maxFileSize: 10 * 1024 * 1024 // 10MB
    }
  },

  // 打印当前配置信息
  printCurrentConfig() {
    const config = this.getCurrentConfig()
    const currentEnv = this.getCurrentEnv()
    
    console.log('☁️ 当前云开发配置:')
    console.log(`   环境: ${config.name} (${currentEnv})`)
    console.log(`   环境ID: ${config.envId}`)
    console.log(`   区域: ${config.region}`)
    console.log(`   超时: ${config.timeout}ms`)
    console.log(`   重试次数: ${config.retryCount}`)
  },

  // 检查云开发服务状态
  async checkCloudStatus() {
    try {
      const config = this.getInitConfig()
      
      // 尝试初始化云开发
      if (typeof wx !== 'undefined' && wx.cloud) {
        wx.cloud.init(config)
        
        // 测试云函数调用
        const result = await wx.cloud.callFunction({
          name: 'login',
          data: { action: 'ping' }
        })
        
        return {
          status: 'healthy',
          envId: config.env,
          message: '云开发服务正常'
        }
      } else {
        return {
          status: 'unavailable',
          message: '云开发服务不可用'
        }
      }
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        error: error
      }
    }
  }
}

module.exports = {
  CloudConfig
}
