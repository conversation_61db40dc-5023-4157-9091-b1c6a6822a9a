/**
 * 图片加载优化测试脚本
 * 验证图片优化器的各项功能
 */

// 模拟微信小程序环境
global.wx = {
  downloadFile: (options) => {
    console.log(`📱 模拟下载文件: ${options.url}`)
    setTimeout(() => {
      if (options.success) {
        options.success({
          tempFilePath: `/temp/${Date.now()}.jpg`,
          statusCode: 200
        })
      }
    }, Math.random() * 200 + 50)
  },
  
  createIntersectionObserver: (options) => {
    console.log('📱 模拟创建交叉观察器:', options)
    return {
      observe: (selector, callback) => {
        console.log(`📱 模拟观察元素: ${selector}`)
        // 模拟元素进入视口
        setTimeout(() => {
          callback({
            intersectionRatio: 0.5,
            target: {
              dataset: { src: 'https://example.com/lazy-image.jpg' },
              src: ''
            }
          })
        }, 100)
      },
      unobserve: (target) => {
        console.log('📱 模拟取消观察元素')
      },
      disconnect: () => {
        console.log('📱 模拟断开观察器')
      }
    }
  },

  getStorageSync: (key) => {
    console.log(`📱 模拟读取存储: ${key}`)
    return null
  },
  setStorageSync: (key, data) => {
    console.log(`📱 模拟保存存储: ${key}`)
  }
}

// 引入图片优化器
const { ImageOptimizer } = require('../utils/imageOptimizer.js')

// 测试函数
async function testImageOptimization() {
  console.log('🧪 开始测试图片加载优化...\n')

  try {
    // 测试1: 初始化图片优化器
    console.log('📋 测试1: 初始化图片优化器')
    ImageOptimizer.init({
      preload: {
        enabled: true,
        maxConcurrent: 2
      },
      cache: {
        enabled: true,
        maxSize: 50,
        ttl: 10000 // 10秒用于测试
      },
      lazyLoad: {
        enabled: true,
        rootMargin: '20px'
      }
    })
    
    console.log('   初始化状态:', ImageOptimizer.state.initialized)
    console.log('   ✅ 初始化测试通过\n')

    // 测试2: 单张图片加载
    console.log('📋 测试2: 单张图片加载')
    const singleImageUrl = 'https://example.com/test-image-1.jpg'
    
    const result1 = await ImageOptimizer.loadImage(singleImageUrl, {
      priority: 'high',
      onProgress: (progress) => {
        console.log(`   加载进度: ${(progress * 100).toFixed(1)}%`)
      }
    })
    
    console.log('   加载结果:', {
      success: result1.success,
      format: result1.data?.format,
      hasLocalPath: !!result1.data?.localPath
    })
    console.log('   ✅ 单张图片加载测试通过\n')

    // 测试3: 缓存功能
    console.log('📋 测试3: 缓存功能测试')
    
    // 再次加载同一张图片，应该命中缓存
    const result2 = await ImageOptimizer.loadImage(singleImageUrl)
    console.log('   第二次加载（应该命中缓存）')
    
    const stats1 = ImageOptimizer.getStats()
    console.log('   缓存统计:', {
      cacheHitRate: stats1.cacheHitRate,
      totalRequests: stats1.totalRequests,
      cacheHits: stats1.cacheHits
    })
    console.log('   ✅ 缓存功能测试通过\n')

    // 测试4: 批量预加载
    console.log('📋 测试4: 批量预加载测试')
    
    const preloadUrls = [
      'https://example.com/preload-1.jpg',
      'https://example.com/preload-2.jpg',
      'https://example.com/preload-3.jpg',
      'https://example.com/preload-4.jpg',
      'https://example.com/preload-5.jpg'
    ]
    
    const preloadResult = await ImageOptimizer.preloadImages(preloadUrls, {
      maxConcurrent: 2,
      onProgress: (progress, loaded, total) => {
        console.log(`   预加载进度: ${(progress * 100).toFixed(1)}% (${loaded}/${total})`)
      }
    })
    
    console.log('   预加载结果:', {
      total: preloadResult.total,
      success: preloadResult.success,
      failed: preloadResult.failed
    })
    console.log('   ✅ 批量预加载测试通过\n')

    // 测试5: 懒加载设置
    console.log('📋 测试5: 懒加载设置')
    
    ImageOptimizer.setupLazyLoad('.lazy-image', {
      rootMargin: '30px',
      threshold: 0.2,
      onLoad: (element, result) => {
        console.log('   懒加载回调被触发:', result.success)
      }
    })
    
    // 等待模拟的懒加载触发
    await new Promise(resolve => setTimeout(resolve, 200))
    console.log('   ✅ 懒加载设置测试通过\n')

    // 测试6: 预加载队列
    console.log('📋 测试6: 预加载队列测试')
    
    const queueUrls = [
      'https://example.com/queue-1.jpg',
      'https://example.com/queue-2.jpg',
      'https://example.com/queue-3.jpg'
    ]
    
    ImageOptimizer.addToPreloadQueue(queueUrls, 'high')
    
    const statsBeforeQueue = ImageOptimizer.getStats()
    console.log('   添加到队列前:', { queueSize: statsBeforeQueue.queueSize })
    
    // 等待队列处理
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const statsAfterQueue = ImageOptimizer.getStats()
    console.log('   队列处理后:', { queueSize: statsAfterQueue.queueSize })
    console.log('   ✅ 预加载队列测试通过\n')

    // 测试7: 并发加载控制
    console.log('📋 测试7: 并发加载控制')
    
    const concurrentUrls = [
      'https://example.com/concurrent-1.jpg',
      'https://example.com/concurrent-2.jpg',
      'https://example.com/concurrent-3.jpg',
      'https://example.com/concurrent-4.jpg'
    ]
    
    const startTime = Date.now()
    const concurrentPromises = concurrentUrls.map(url => 
      ImageOptimizer.loadImage(url, { priority: 'normal' })
    )
    
    const concurrentResults = await Promise.allSettled(concurrentPromises)
    const endTime = Date.now()
    
    const successCount = concurrentResults.filter(r => 
      r.status === 'fulfilled' && r.value.success
    ).length
    
    console.log('   并发加载结果:', {
      total: concurrentUrls.length,
      success: successCount,
      duration: `${endTime - startTime}ms`
    })
    console.log('   ✅ 并发加载控制测试通过\n')

    // 测试8: 错误处理
    console.log('📋 测试8: 错误处理测试')
    
    // 模拟加载失败的情况
    const invalidUrl = 'https://invalid-domain-12345.com/nonexistent.jpg'
    
    let errorHandled = false
    const errorResult = await ImageOptimizer.loadImage(invalidUrl, {
      onError: (error) => {
        console.log('   错误回调被触发:', error.message)
        errorHandled = true
      }
    })
    
    console.log('   错误处理结果:', {
      success: errorResult.success,
      hasPlaceholder: !!errorResult.placeholder,
      errorHandled
    })
    console.log('   ✅ 错误处理测试通过\n')

    // 测试9: 统计信息
    console.log('📋 测试9: 统计信息测试')
    
    const finalStats = ImageOptimizer.getStats()
    console.log('   最终统计信息:', {
      totalRequests: finalStats.totalRequests,
      cacheHitRate: finalStats.cacheHitRate,
      successRate: finalStats.successRate,
      avgLoadTime: `${finalStats.avgLoadTime.toFixed(1)}ms`,
      loadedCount: finalStats.loadedCount,
      failedCount: finalStats.failedCount
    })
    console.log('   ✅ 统计信息测试通过\n')

    // 测试10: 工具函数
    console.log('📋 测试10: 工具函数测试')
    
    // 测试图片格式识别
    const jpgFormat = ImageOptimizer.getImageFormat('test.jpg')
    const pngFormat = ImageOptimizer.getImageFormat('test.png')
    const unknownFormat = ImageOptimizer.getImageFormat('test')
    
    console.log('   格式识别:', { jpgFormat, pngFormat, unknownFormat })
    
    // 测试图片名称提取
    const imageName = ImageOptimizer.getImageName('https://example.com/path/to/image.jpg')
    console.log('   图片名称:', imageName)
    
    // 测试字符串哈希
    const hash1 = ImageOptimizer.hashString('test-string')
    const hash2 = ImageOptimizer.hashString('test-string')
    const hash3 = ImageOptimizer.hashString('different-string')
    
    console.log('   哈希测试:', {
      same: hash1 === hash2,
      different: hash1 !== hash3
    })
    
    // 测试数组分块
    const testArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    const chunks = ImageOptimizer.chunkArray(testArray, 3)
    console.log('   数组分块:', {
      original: testArray.length,
      chunks: chunks.length,
      firstChunk: chunks[0]
    })
    
    console.log('   ✅ 工具函数测试通过\n')

    // 测试11: 资源清理
    console.log('📋 测试11: 资源清理')
    
    ImageOptimizer.cleanup()
    
    const cleanupStats = ImageOptimizer.getStats()
    console.log('   清理后状态:', {
      queueSize: cleanupStats.queueSize,
      observersCleared: ImageOptimizer.state.observers.size === 0
    })
    console.log('   ✅ 资源清理测试通过\n')

    console.log('🎉 所有图片优化测试完成！')
    return true

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    console.error('   错误详情:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  testImageOptimization().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = { testImageOptimization }
