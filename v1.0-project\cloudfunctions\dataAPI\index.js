// V1.0 小程序数据API云函数 - 优化版本
const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 缓存配置
const CACHE_CONFIG = {
  categories: { ttl: 300000, key: 'categories_cache' }, // 5分钟
  emojis: { ttl: 180000, key: 'emojis_cache' },       // 3分钟
  banners: { ttl: 600000, key: 'banners_cache' },     // 10分钟
  stats: { ttl: 60000, key: 'stats_cache' }           // 1分钟
};

// 内存缓存
const memoryCache = new Map();

// 云函数入口
exports.main = async (event, context) => {
  console.log('📊 dataAPI调用:', event.action);
  
  try {
    const { action, params = {} } = event;
    
    switch (action) {
      // 分类相关
      case 'getCategories':
        return await getCategories(params);
      case 'getCategoryDetail':
        return await getCategoryDetail(params);
        
      // 表情包相关
      case 'getEmojis':
        return await getEmojis(params);
      case 'getEmojiDetail':
        return await getEmojiDetail(params);
      case 'searchEmojis':
        return await searchEmojis(params);
        
      // 横幅相关
      case 'getBanners':
        return await getBanners(params);
        
      // 统计相关
      case 'getStats':
        return await getStats(params);
        
      // 缓存管理
      case 'clearCache':
        return await clearCache(params);
        
      default:
        return {
          success: false,
          error: '未知的操作类型',
          code: 'UNKNOWN_ACTION'
        };
    }
    
  } catch (error) {
    console.error('❌ dataAPI执行失败:', error);
    
    return {
      success: false,
      error: error.message || '服务器内部错误',
      code: 'INTERNAL_ERROR'
    };
  }
};

// 获取分类列表 - 带缓存
async function getCategories(params) {
  try {
    console.log('📁 获取分类列表');
    
    const cacheKey = CACHE_CONFIG.categories.key;
    
    // 检查缓存
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('✅ 从缓存返回分类数据');
      return {
        success: true,
        data: cachedData,
        fromCache: true,
        timestamp: Date.now()
      };
    }
    
    // 从数据库查询
    const query = db.collection('categories')
      .where({
        status: 'active'
      })
      .orderBy('sort', 'asc')
      .orderBy('createTime', 'desc');
    
    const result = await query.get();
    
    if (result.data) {
      // 数据处理和优化
      const processedData = result.data.map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        description: category.description,
        sort: category.sort,
        emojiCount: category.emojiCount || 0,
        createTime: category.createTime
      }));
      
      // 存入缓存
      setToCache(cacheKey, processedData, CACHE_CONFIG.categories.ttl);
      
      console.log('✅ 分类数据查询完成，数量:', processedData.length);
      
      return {
        success: true,
        data: processedData,
        fromCache: false,
        timestamp: Date.now()
      };
    }
    
    return {
      success: true,
      data: [],
      fromCache: false,
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取分类列表失败:', error);
    throw error;
  }
}

// 获取分类详情
async function getCategoryDetail(params) {
  try {
    const { categoryId } = params;
    
    if (!categoryId) {
      throw new Error('分类ID不能为空');
    }
    
    console.log('📁 获取分类详情:', categoryId);
    
    // 查询分类信息
    const categoryResult = await db.collection('categories')
      .where({
        id: categoryId,
        status: 'active'
      })
      .get();
    
    if (categoryResult.data.length === 0) {
      throw new Error('分类不存在');
    }
    
    const category = categoryResult.data[0];
    
    // 查询该分类下的表情包
    const emojisResult = await db.collection('emojis')
      .where({
        categoryId: categoryId,
        status: 'published'
      })
      .orderBy('createTime', 'desc')
      .limit(50)
      .get();
    
    return {
      success: true,
      data: {
        category: {
          id: category.id,
          name: category.name,
          icon: category.icon,
          description: category.description,
          emojiCount: category.emojiCount || 0
        },
        emojis: emojisResult.data.map(emoji => ({
          id: emoji.id,
          title: emoji.title,
          imageUrl: emoji.imageUrl,
          tags: emoji.tags || [],
          likes: emoji.likes || 0,
          downloads: emoji.downloads || 0
        }))
      },
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取分类详情失败:', error);
    throw error;
  }
}

// 获取表情包列表 - 带分页和过滤
async function getEmojis(params) {
  try {
    const {
      page = 1,
      pageSize = 20,
      categoryId,
      tags,
      sortBy = 'createTime',
      sortOrder = 'desc'
    } = params;
    
    console.log('😀 获取表情包列表:', { page, pageSize, categoryId, tags });
    
    // 构建查询条件
    const whereConditions = { status: 'published' };
    
    if (categoryId) {
      whereConditions.categoryId = categoryId;
    }
    
    if (tags && tags.length > 0) {
      whereConditions.tags = db.command.in(tags);
    }
    
    // 构建查询
    let query = db.collection('emojis').where(whereConditions);
    
    // 排序
    const validSortFields = ['createTime', 'likes', 'downloads', 'title'];
    const validSortOrders = ['asc', 'desc'];
    
    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder)) {
      query = query.orderBy(sortBy, sortOrder);
    } else {
      query = query.orderBy('createTime', 'desc');
    }
    
    // 分页
    const skip = (page - 1) * pageSize;
    query = query.skip(skip).limit(pageSize);
    
    const result = await query.get();
    
    // 获取总数（用于分页）
    const countResult = await db.collection('emojis')
      .where(whereConditions)
      .count();
    
    const processedData = result.data.map(emoji => ({
      id: emoji.id,
      title: emoji.title,
      imageUrl: emoji.imageUrl,
      category: emoji.category,
      categoryId: emoji.categoryId,
      tags: emoji.tags || [],
      likes: emoji.likes || 0,
      downloads: emoji.downloads || 0,
      collections: emoji.collections || 0,
      createTime: emoji.createTime
    }));
    
    console.log('✅ 表情包数据查询完成，数量:', processedData.length);
    
    return {
      success: true,
      data: {
        list: processedData,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / pageSize)
        }
      },
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取表情包列表失败:', error);
    throw error;
  }
}

// 获取表情包详情
async function getEmojiDetail(params) {
  try {
    const { emojiId } = params;
    
    if (!emojiId) {
      throw new Error('表情包ID不能为空');
    }
    
    console.log('😀 获取表情包详情:', emojiId);
    
    const result = await db.collection('emojis')
      .where({
        id: emojiId,
        status: 'published'
      })
      .get();
    
    if (result.data.length === 0) {
      throw new Error('表情包不存在');
    }
    
    const emoji = result.data[0];
    
    // 增加查看次数（异步执行，不影响返回）
    db.collection('emojis')
      .doc(emoji._id)
      .update({
        data: {
          views: (emoji.views || 0) + 1
        }
      })
      .catch(error => {
        console.warn('更新查看次数失败:', error);
      });
    
    return {
      success: true,
      data: {
        id: emoji.id,
        title: emoji.title,
        imageUrl: emoji.imageUrl,
        category: emoji.category,
        categoryId: emoji.categoryId,
        tags: emoji.tags || [],
        description: emoji.description || '',
        likes: emoji.likes || 0,
        downloads: emoji.downloads || 0,
        collections: emoji.collections || 0,
        views: (emoji.views || 0) + 1,
        createTime: emoji.createTime
      },
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取表情包详情失败:', error);
    throw error;
  }
}

// 搜索表情包
async function searchEmojis(params) {
  try {
    const {
      keyword,
      page = 1,
      pageSize = 20
    } = params;
    
    if (!keyword || keyword.trim().length === 0) {
      throw new Error('搜索关键词不能为空');
    }
    
    console.log('🔍 搜索表情包:', keyword);
    
    // 使用正则表达式进行模糊搜索
    const searchRegex = new RegExp(keyword.trim(), 'i');
    
    const whereConditions = {
      status: 'published',
      $or: [
        { title: searchRegex },
        { tags: searchRegex },
        { description: searchRegex }
      ]
    };
    
    // 分页查询
    const skip = (page - 1) * pageSize;
    const result = await db.collection('emojis')
      .where(whereConditions)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 获取总数
    const countResult = await db.collection('emojis')
      .where(whereConditions)
      .count();
    
    const processedData = result.data.map(emoji => ({
      id: emoji.id,
      title: emoji.title,
      imageUrl: emoji.imageUrl,
      category: emoji.category,
      tags: emoji.tags || [],
      likes: emoji.likes || 0,
      downloads: emoji.downloads || 0
    }));
    
    console.log('✅ 搜索完成，找到:', processedData.length, '个结果');
    
    return {
      success: true,
      data: {
        keyword: keyword,
        list: processedData,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / pageSize)
        }
      },
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 搜索表情包失败:', error);
    throw error;
  }
}

// 获取横幅列表 - 带缓存
async function getBanners(params) {
  try {
    console.log('🎯 获取横幅列表');
    
    const cacheKey = CACHE_CONFIG.banners.key;
    
    // 检查缓存
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('✅ 从缓存返回横幅数据');
      return {
        success: true,
        data: cachedData,
        fromCache: true,
        timestamp: Date.now()
      };
    }
    
    // 从数据库查询
    const result = await db.collection('banners')
      .where({
        status: 'active'
      })
      .orderBy('sort', 'asc')
      .orderBy('createTime', 'desc')
      .get();
    
    if (result.data) {
      const processedData = result.data.map(banner => ({
        id: banner.id,
        title: banner.title,
        imageUrl: banner.imageUrl,
        linkUrl: banner.linkUrl || '',
        sort: banner.sort,
        clickCount: banner.clickCount || 0
      }));
      
      // 存入缓存
      setToCache(cacheKey, processedData, CACHE_CONFIG.banners.ttl);
      
      console.log('✅ 横幅数据查询完成，数量:', processedData.length);
      
      return {
        success: true,
        data: processedData,
        fromCache: false,
        timestamp: Date.now()
      };
    }
    
    return {
      success: true,
      data: [],
      fromCache: false,
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取横幅列表失败:', error);
    throw error;
  }
}

// 获取统计数据 - 带缓存
async function getStats(params) {
  try {
    console.log('📊 获取统计数据');
    
    const cacheKey = CACHE_CONFIG.stats.key;
    
    // 检查缓存
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      console.log('✅ 从缓存返回统计数据');
      return {
        success: true,
        data: cachedData,
        fromCache: true,
        timestamp: Date.now()
      };
    }
    
    // 并行查询各种统计数据
    const [
      categoriesCount,
      emojisCount,
      bannersCount,
      totalLikes,
      totalDownloads
    ] = await Promise.all([
      db.collection('categories').where({ status: 'active' }).count(),
      db.collection('emojis').where({ status: 'published' }).count(),
      db.collection('banners').where({ status: 'active' }).count(),
      db.collection('emojis').where({ status: 'published' }).field({ likes: true }).get(),
      db.collection('emojis').where({ status: 'published' }).field({ downloads: true }).get()
    ]);
    
    // 计算总点赞数和下载数
    const totalLikesCount = totalLikes.data.reduce((sum, emoji) => sum + (emoji.likes || 0), 0);
    const totalDownloadsCount = totalDownloads.data.reduce((sum, emoji) => sum + (emoji.downloads || 0), 0);
    
    const statsData = {
      categories: categoriesCount.total,
      emojis: emojisCount.total,
      banners: bannersCount.total,
      totalLikes: totalLikesCount,
      totalDownloads: totalDownloadsCount,
      lastUpdate: Date.now()
    };
    
    // 存入缓存
    setToCache(cacheKey, statsData, CACHE_CONFIG.stats.ttl);
    
    console.log('✅ 统计数据查询完成:', statsData);
    
    return {
      success: true,
      data: statsData,
      fromCache: false,
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error);
    throw error;
  }
}

// 清理缓存
async function clearCache(params) {
  try {
    const { cacheKeys } = params;
    
    if (cacheKeys && Array.isArray(cacheKeys)) {
      // 清理指定缓存
      cacheKeys.forEach(key => {
        memoryCache.delete(key);
      });
      console.log('✅ 清理指定缓存:', cacheKeys);
    } else {
      // 清理所有缓存
      memoryCache.clear();
      console.log('✅ 清理所有缓存');
    }
    
    return {
      success: true,
      message: '缓存清理完成',
      timestamp: Date.now()
    };
    
  } catch (error) {
    console.error('❌ 清理缓存失败:', error);
    throw error;
  }
}

// 缓存工具函数
function getFromCache(key) {
  const cached = memoryCache.get(key);
  if (cached && cached.expireTime > Date.now()) {
    return cached.data;
  }
  
  // 缓存过期，删除
  if (cached) {
    memoryCache.delete(key);
  }
  
  return null;
}

function setToCache(key, data, ttl) {
  memoryCache.set(key, {
    data: data,
    expireTime: Date.now() + ttl
  });
}

// 生成唯一ID
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
