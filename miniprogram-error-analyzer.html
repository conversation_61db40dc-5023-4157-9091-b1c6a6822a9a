<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序错误分析器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .analysis-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .analysis-section h2 {
            color: #666;
            margin-top: 0;
        }
        .error-card {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-card.warning {
            background: #fffbf0;
            border-color: #fbd38d;
        }
        .error-card.info {
            background: #f0f9ff;
            border-color: #7dd3fc;
        }
        .error-card h3 {
            margin-top: 0;
            color: #e53e3e;
        }
        .error-card.warning h3 {
            color: #d69e2e;
        }
        .error-card.info h3 {
            color: #3182ce;
        }
        .code-block {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .solution {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .solution h4 {
            color: #38a169;
            margin-top: 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-error {
            background-color: #e53e3e;
        }
        .status-warning {
            background-color: #d69e2e;
        }
        .status-success {
            background-color: #38a169;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 小程序错误分析器</h1>
        
        <div class="analysis-section">
            <h2>📋 常见错误分析</h2>
            <button onclick="analyzeCommonErrors()">分析常见错误</button>
            <button onclick="generateDiagnosticReport()">生成诊断报告</button>
            <button onclick="showFixSuggestions()">显示修复建议</button>
        </div>

        <div id="errorAnalysis"></div>
        
        <div class="analysis-section">
            <h2>🔧 快速诊断清单</h2>
            <ul class="checklist" id="diagnosticChecklist">
                <li><span class="status-indicator status-warning"></span>检查云开发环境配置</li>
                <li><span class="status-indicator status-warning"></span>验证云函数部署状态</li>
                <li><span class="status-indicator status-warning"></span>检查数据库集合是否存在</li>
                <li><span class="status-indicator status-warning"></span>验证数据库权限配置</li>
                <li><span class="status-indicator status-warning"></span>检查小程序版本兼容性</li>
                <li><span class="status-indicator status-warning"></span>验证网络连接状态</li>
                <li><span class="status-indicator status-warning"></span>检查错误日志记录</li>
            </ul>
        </div>

        <div class="analysis-section">
            <h2>📊 错误统计分析</h2>
            <div id="errorStats"></div>
        </div>
    </div>

    <script>
        // 分析常见错误
        function analyzeCommonErrors() {
            const analysisContainer = document.getElementById('errorAnalysis');
            
            const commonErrors = [
                {
                    type: 'error',
                    title: '云函数调用失败',
                    description: 'errCode: -501000 | errMsg: [100003] Param Invalid',
                    causes: [
                        '云开发环境ID配置错误',
                        '云函数未正确部署',
                        '参数格式不正确',
                        '权限配置问题'
                    ],
                    solutions: [
                        '检查app.js中的云环境ID配置',
                        '在微信开发者工具中重新部署云函数',
                        '验证云函数调用参数格式',
                        '检查云开发控制台的权限设置'
                    ]
                },
                {
                    type: 'error',
                    title: '数据库集合不存在',
                    description: 'database collection not exist | COLLECTION_NOT_EXIST',
                    causes: [
                        '数据库集合未创建',
                        '集合名称拼写错误',
                        '数据库权限不足'
                    ],
                    solutions: [
                        '在云开发控制台手动创建集合',
                        '使用create-test-data.html工具创建测试数据',
                        '检查数据库权限配置'
                    ]
                },
                {
                    type: 'warning',
                    title: 'getCurrentPages()返回空数组',
                    description: 'TabBar组件中页面路由获取失败',
                    causes: [
                        '小程序启动初期页面未完全加载',
                        '页面路由信息未初始化',
                        '自定义TabBar组件时序问题'
                    ],
                    solutions: [
                        '添加页面路由安全检查',
                        '延迟TabBar状态更新',
                        '使用try-catch包装路由获取逻辑'
                    ]
                },
                {
                    type: 'warning',
                    title: '数据管理器冲突',
                    description: '多个数据管理器并存导致数据不一致',
                    causes: [
                        'DataManager、newDataManager、SimpleDataManager同时存在',
                        '不同页面使用不同的数据管理器',
                        '缓存机制冲突'
                    ],
                    solutions: [
                        '统一使用一个数据管理器',
                        '清理无用的数据管理器代码',
                        '简化数据获取逻辑'
                    ]
                },
                {
                    type: 'info',
                    title: '版本兼容性问题',
                    description: '不同版本代码混合导致功能异常',
                    causes: [
                        'v1.0-project版本和主版本代码混合',
                        '不同版本的app.js逻辑冲突',
                        '页面组件版本不一致'
                    ],
                    solutions: [
                        '选择一个版本作为主版本',
                        '清理其他版本的代码',
                        '统一项目结构'
                    ]
                }
            ];

            let html = '<h3>🔍 常见错误分析结果</h3>';
            
            commonErrors.forEach((error, index) => {
                html += `
                    <div class="error-card ${error.type}">
                        <h3>${index + 1}. ${error.title}</h3>
                        <p><strong>错误描述：</strong>${error.description}</p>
                        
                        <h4>可能原因：</h4>
                        <ul>
                            ${error.causes.map(cause => `<li>${cause}</li>`).join('')}
                        </ul>
                        
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                ${error.solutions.map(solution => `<li>${solution}</li>`).join('')}
                            </ol>
                        </div>
                    </div>
                `;
            });

            analysisContainer.innerHTML = html;
        }

        // 生成诊断报告
        function generateDiagnosticReport() {
            const analysisContainer = document.getElementById('errorAnalysis');
            
            const diagnosticItems = [
                {
                    category: '云开发配置',
                    items: [
                        { name: '环境ID配置', status: 'warning', description: '检查app.js中的云环境ID是否正确' },
                        { name: '云函数部署', status: 'error', description: '验证dataAPI、webAdminAPI等云函数是否已部署' },
                        { name: '数据库权限', status: 'warning', description: '检查数据库集合的读写权限配置' }
                    ]
                },
                {
                    category: '数据库状态',
                    items: [
                        { name: 'categories集合', status: 'error', description: '分类数据集合可能不存在或为空' },
                        { name: 'emojis集合', status: 'error', description: '表情包数据集合可能不存在或为空' },
                        { name: 'banners集合', status: 'warning', description: '横幅数据集合可能不存在或为空' }
                    ]
                },
                {
                    category: '代码结构',
                    items: [
                        { name: '版本一致性', status: 'warning', description: '存在多个版本的代码，可能导致冲突' },
                        { name: '数据管理器', status: 'warning', description: '多个数据管理器并存，逻辑复杂' },
                        { name: '错误处理', status: 'success', description: '错误处理机制相对完善' }
                    ]
                },
                {
                    category: '页面功能',
                    items: [
                        { name: '首页加载', status: 'warning', description: '首页数据加载可能失败' },
                        { name: '分类页面', status: 'warning', description: '分类页面可能显示异常' },
                        { name: '搜索功能', status: 'info', description: '搜索功能使用模拟数据' }
                    ]
                }
            ];

            let html = '<h3>📊 诊断报告</h3>';
            
            diagnosticItems.forEach(category => {
                html += `
                    <div class="error-card info">
                        <h3>${category.category}</h3>
                        <ul class="checklist">
                `;
                
                category.items.forEach(item => {
                    html += `
                        <li>
                            <span class="status-indicator status-${item.status}"></span>
                            <strong>${item.name}:</strong> ${item.description}
                        </li>
                    `;
                });
                
                html += `
                        </ul>
                    </div>
                `;
            });

            analysisContainer.innerHTML = html;
        }

        // 显示修复建议
        function showFixSuggestions() {
            const analysisContainer = document.getElementById('errorAnalysis');
            
            const fixSuggestions = [
                {
                    priority: 'high',
                    title: '立即修复：创建测试数据',
                    description: '使用create-test-data.html工具创建基础测试数据',
                    steps: [
                        '打开create-test-data.html文件',
                        '点击"初始化云开发SDK"',
                        '点击"创建所有测试数据"',
                        '验证数据创建成功'
                    ]
                },
                {
                    priority: 'high',
                    title: '立即修复：统一数据管理器',
                    description: '选择一个数据管理器，清理其他冗余代码',
                    steps: [
                        '选择utils/newDataManager.js作为主数据管理器',
                        '修改所有页面使用统一的数据管理器',
                        '删除SimpleDataManager和其他数据管理器',
                        '简化数据获取逻辑'
                    ]
                },
                {
                    priority: 'medium',
                    title: '优化：清理版本冲突',
                    description: '选择一个版本作为主版本，清理其他版本代码',
                    steps: [
                        '选择主版本的app.js',
                        '删除v1.0-project版本的冲突代码',
                        '统一页面组件版本',
                        '测试功能完整性'
                    ]
                },
                {
                    priority: 'medium',
                    title: '优化：改进错误处理',
                    description: '统一错误处理机制，避免重复处理',
                    steps: [
                        '选择一个错误处理器',
                        '删除重复的错误处理代码',
                        '添加更详细的错误信息',
                        '改进用户体验'
                    ]
                },
                {
                    priority: 'low',
                    title: '优化：性能改进',
                    description: '优化数据加载和缓存机制',
                    steps: [
                        '简化缓存逻辑',
                        '减少不必要的云函数调用',
                        '优化页面加载速度',
                        '添加加载状态提示'
                    ]
                }
            ];

            let html = '<h3>🔧 修复建议</h3>';
            
            fixSuggestions.forEach((suggestion, index) => {
                const priorityClass = suggestion.priority === 'high' ? 'error' : 
                                   suggestion.priority === 'medium' ? 'warning' : 'info';
                
                html += `
                    <div class="error-card ${priorityClass}">
                        <h3>优先级${suggestion.priority.toUpperCase()}: ${suggestion.title}</h3>
                        <p>${suggestion.description}</p>
                        
                        <div class="solution">
                            <h4>修复步骤：</h4>
                            <ol>
                                ${suggestion.steps.map(step => `<li>${step}</li>`).join('')}
                            </ol>
                        </div>
                    </div>
                `;
            });

            analysisContainer.innerHTML = html;
        }

        // 页面加载时显示基本信息
        window.addEventListener('load', function() {
            const statsContainer = document.getElementById('errorStats');
            statsContainer.innerHTML = `
                <div class="error-card info">
                    <h3>📈 错误统计概览</h3>
                    <p><strong>检测到的主要问题：</strong></p>
                    <ul>
                        <li>🔴 高优先级问题：2个（数据库集合缺失、云函数调用失败）</li>
                        <li>🟡 中优先级问题：3个（版本冲突、数据管理器冲突、错误处理重复）</li>
                        <li>🔵 低优先级问题：2个（性能优化、用户体验改进）</li>
                    </ul>
                    <p><strong>建议修复顺序：</strong>先解决数据问题，再优化代码结构，最后进行性能优化。</p>
                </div>
            `;
        });
    </script>
</body>
</html>
