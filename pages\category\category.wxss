/* pages/category/category.wxss */
.container {
  padding: 20rpx;
  padding-top: 30rpx;
  padding-bottom: 120rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 分类网格 */
.category-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 0 5rpx;
}

.category-card {
  display: flex;
  align-items: center;
  padding: 35rpx 30rpx;
  margin: 0 10rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 120rpx;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.9;
  z-index: 1;
}

.category-card view {
  position: relative;
  z-index: 2;
}

.category-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 添加卡片动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.category-card {
  animation: fadeIn 0.5s ease forwards;
  animation-delay: calc(var(--index, 0) * 0.1s);
  opacity: 0;
}

.category-icon {
  width: 90rpx;
  height: 90rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 46rpx;
}

.category-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-name {
  font-size: 34rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
  letter-spacing: 1rpx;
}

.category-count {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.category-arrow {
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.arrow-icon {
  font-size: 26rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.4);
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
  .category-card {
    padding: 30rpx 25rpx;
    margin: 0 5rpx;
  }

  .category-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 25rpx;
  }

  .icon-text {
    font-size: 40rpx;
  }

  .category-name {
    font-size: 30rpx;
  }

  .category-count {
    font-size: 24rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 414px) {
  .container {
    padding: 30rpx 40rpx;
  }

  .category-grid {
    gap: 28rpx;
    padding: 0 10rpx;
  }

  .category-card {
    padding: 40rpx 35rpx;
    min-height: 140rpx;
  }

  .category-icon {
    width: 100rpx;
    height: 100rpx;
    margin-right: 35rpx;
  }

  .icon-text {
    font-size: 50rpx;
  }

  .category-name {
    font-size: 36rpx;
  }

  .category-count {
    font-size: 28rpx;
  }
}