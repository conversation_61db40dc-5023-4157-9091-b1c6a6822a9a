const { chromium } = require('playwright');

async function testEmojiIdFix() {
  console.log('🔧 测试表情包ID字段修复...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 1. 获取表情包数据并检查ID字段
    console.log('\n📋 步骤1：检查表情包数据结构');
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(8000);
    
    const emojiData = await page.evaluate(async () => {
      try {
        if (!window.cloudbase) return { error: 'SDK未加载' };
        
        const app = window.cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
        await app.auth().signInAnonymously();
        
        // 获取表情包列表
        const listResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 3 } }
        });
        
        if (!listResult.result?.success) {
          return { error: '获取表情包列表失败' };
        }
        
        const emojis = listResult.result.data;
        console.log('表情包数据:', emojis);
        
        // 检查每个表情包的ID字段
        const emojiInfo = emojis.map(emoji => ({
          hasId: !!emoji.id,
          has_id: !!emoji._id,
          title: emoji.title,
          actualId: emoji._id || emoji.id,
          idField: emoji.id,
          _idField: emoji._id
        }));
        
        // 测试详情接口
        const firstEmoji = emojis[0];
        const testId = firstEmoji._id || firstEmoji.id;
        
        console.log('测试详情接口，使用ID:', testId);
        
        const detailResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojiDetail', data: { id: testId } }
        });
        
        return {
          success: true,
          emojiCount: emojis.length,
          emojiInfo,
          testId,
          detailSuccess: detailResult.result?.success,
          detailData: detailResult.result?.data,
          detailError: detailResult.result?.message
        };
        
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (emojiData.error) {
      console.error('❌ 数据获取失败:', emojiData.error);
      return;
    }
    
    console.log('✅ 获取到表情包数据');
    console.log(`📊 表情包数量: ${emojiData.emojiCount}`);
    console.log('📋 ID字段检查:');
    
    emojiData.emojiInfo.forEach((info, index) => {
      console.log(`  表情包${index + 1}: ${info.title}`);
      console.log(`    - 有id字段: ${info.hasId}`);
      console.log(`    - 有_id字段: ${info.has_id}`);
      console.log(`    - id值: ${info.idField}`);
      console.log(`    - _id值: ${info._idField}`);
      console.log(`    - 实际使用ID: ${info.actualId}`);
    });
    
    console.log(`\n🎯 详情接口测试:`);
    console.log(`  - 测试ID: ${emojiData.testId}`);
    console.log(`  - 详情接口: ${emojiData.detailSuccess ? '成功' : '失败'}`);
    if (!emojiData.detailSuccess) {
      console.log(`  - 错误信息: ${emojiData.detailError}`);
    } else {
      console.log(`  - 详情标题: ${emojiData.detailData?.title}`);
    }
    
    // 2. 生成修复建议
    console.log('\n📋 步骤2：生成修复建议');
    
    const hasIdField = emojiData.emojiInfo.some(info => info.hasId);
    const has_idField = emojiData.emojiInfo.some(info => info.has_id);
    
    console.log('\n🔍 数据结构分析:');
    console.log(`  - 表情包数据包含id字段: ${hasIdField}`);
    console.log(`  - 表情包数据包含_id字段: ${has_idField}`);
    
    if (has_idField && !hasIdField) {
      console.log('\n💡 修复建议:');
      console.log('  1. 数据库使用_id字段，前端需要统一处理');
      console.log('  2. 在数据处理时将_id映射为id字段');
      console.log('  3. 确保所有页面的跳转都使用正确的ID');
    }
    
    // 3. 测试修复后的页面跳转
    console.log('\n📋 步骤3：测试页面跳转');
    
    const testUrl = `http://localhost:8080/pages/detail/detail-new?id=${emojiData.testId}`;
    console.log(`🔗 测试URL: ${testUrl}`);
    
    try {
      const detailPage = await context.newPage();
      await detailPage.goto(testUrl, { timeout: 10000 });
      await detailPage.waitForTimeout(5000);
      
      const pageStatus = await detailPage.evaluate(() => {
        return {
          title: document.title,
          hasContent: document.body.innerHTML.length > 100,
          hasError: document.body.innerHTML.includes('error') || document.body.innerHTML.includes('失败'),
          hasLoading: document.body.innerHTML.includes('loading'),
          bodyContent: document.body.innerHTML.substring(0, 500)
        };
      });
      
      console.log('📄 页面状态:');
      console.log(`  - 标题: ${pageStatus.title}`);
      console.log(`  - 有内容: ${pageStatus.hasContent}`);
      console.log(`  - 有错误: ${pageStatus.hasError}`);
      console.log(`  - 加载中: ${pageStatus.hasLoading}`);
      
      if (pageStatus.hasError) {
        console.log('⚠️ 页面内容预览:');
        console.log(pageStatus.bodyContent);
      }
      
      await detailPage.close();
      
    } catch (error) {
      console.log('ℹ️ 无法直接测试小程序页面，需要在微信开发者工具中验证');
    }
    
    // 4. 生成修复报告
    console.log('\n📋 步骤4：生成修复报告');
    
    const fixReport = {
      timestamp: new Date().toISOString(),
      issue: 'emoji_id_field_mismatch',
      analysis: {
        databaseUsesId: has_idField,
        frontendExpectsId: true,
        detailApiWorks: emojiData.detailSuccess
      },
      fixes: [
        '修复首页表情包数据处理，确保ID字段统一',
        '修复详情页参数传递，使用正确的ID字段',
        '确保所有页面的跳转链接使用统一的ID格式'
      ],
      testResults: {
        dataRetrieval: 'SUCCESS',
        idFieldMapping: hasIdField ? 'FIXED' : 'NEEDS_FIX',
        detailApi: emojiData.detailSuccess ? 'SUCCESS' : 'FAILED'
      },
      nextSteps: [
        '在微信开发者工具中测试首页点击表情包',
        '验证详情页能正常加载表情包信息',
        '检查控制台是否还有ID相关错误'
      ]
    };
    
    const fs = require('fs');
    fs.writeFileSync('emoji-id-fix-report.json', JSON.stringify(fixReport, null, 2));
    console.log('📄 修复报告已保存: emoji-id-fix-report.json');
    
    console.log('\n🎉 表情包ID字段修复测试完成！');
    
    if (emojiData.detailSuccess) {
      console.log('✅ 详情接口正常，修复应该生效');
    } else {
      console.log('❌ 详情接口仍有问题，需要进一步检查');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testEmojiIdFix().catch(console.error);
