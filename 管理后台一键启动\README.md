# Emoji Admin Panel Server Launcher

This folder contains scripts to quickly start the admin panel server for the Emoji Mini Program project.

## Files

- `start-admin-server.bat` - Windows Batch script (recommended for most users)
- `start-admin-server.ps1` - PowerShell script (alternative option)
- `README.md` - This documentation file

## Quick Start

### Method 1: Batch Script (Recommended)
1. Double-click `start-admin-server.bat`
2. The script will automatically:
   - Check Python installation
   - Navigate to the admin-serverless directory
   - Kill any existing servers on port 9001
   - Start HTTP server on port 9001
   - Open browser to http://localhost:9001/main.html

### Method 2: PowerShell Script
1. Right-click `start-admin-server.ps1`
2. Select "Run with PowerShell"
3. If you get execution policy errors, run this command first:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

## Requirements

- Python 3.x installed and available in PATH
- Project structure should be:
  ```
  Project Root/
  ├── admin-serverless/
  │   ├── main.html
  │   ├── index.html
  │   └── ...
  └── 管理后台一键启动/
      ├── start-admin-server.bat
      ├── start-admin-server.ps1
      └── README.md
  ```

## Access URLs

Once the server starts, you can access:

- **Main Admin Panel**: http://localhost:9001/main.html
- **Index Page**: http://localhost:9001/index.html  
- **Direct Access**: http://localhost:9001/

## Features

- ✅ Automatic Python installation check
- ✅ Automatic directory validation
- ✅ Kill existing servers on port 9001
- ✅ Auto-open browser
- ✅ Clear error messages and instructions
- ✅ UTF-8 encoding support
- ✅ Cross-platform compatibility

## Troubleshooting

### Python Not Found
- Install Python from https://www.python.org/
- Make sure Python is added to your system PATH

### Port 9001 Already in Use
- The script automatically kills existing processes on port 9001
- If issues persist, manually check: `netstat -ano | findstr :9001`

### Directory Not Found
- Make sure this script folder is in the correct location relative to admin-serverless
- Check that the project structure matches the requirements above

### Permission Issues (PowerShell)
- Run PowerShell as Administrator
- Or set execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

## Manual Alternative

If the scripts don't work, you can manually start the server:

1. Open Command Prompt or Terminal
2. Navigate to the admin-serverless directory:
   ```bash
   cd "path/to/your/project/admin-serverless"
   ```
3. Start Python HTTP server:
   ```bash
   python -m http.server 9001
   ```
4. Open browser to: http://localhost:9001/main.html

## Support

If you encounter any issues:
1. Check the error messages displayed by the script
2. Verify Python installation: `python --version`
3. Verify project directory structure
4. Try the manual alternative method above
