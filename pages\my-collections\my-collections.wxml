<!--pages/my-collections/my-collections.wxml-->
<view class="container">

  <view class="emoji-grid" wx:if="{{collectedEmojis.length > 0}}">
    <view
      class="emoji-item card"
      wx:for="{{collectedEmojis}}"
      wx:key="id"
      bindtap="onEmojiTap"
      data-emoji="{{item}}"
    >
      <image class="emoji-image" src="{{item.imageUrl}}" mode="aspectFill" />
      <view class="emoji-info">
        <text class="emoji-title">{{item.title}}</text>
        <text class="emoji-category">{{item.categoryName || item.category}}</text>
        <!-- 标签区域 -->
        <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view
            class="tag-item"
            wx:for="{{item.tags}}"
            wx:for-item="tag"
            wx:key="*this"
          >
            {{tag}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <text class="empty-icon">⭐</text>
    <text class="empty-title">还没有收藏的表情包</text>
    <text class="empty-desc">收藏喜欢的表情包，方便随时查看</text>
    <button class="explore-btn" bindtap="onGoExplore">去探索</button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>