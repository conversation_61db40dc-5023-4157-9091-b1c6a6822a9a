// 根据官方文档，Web端应使用 @cloudbase/node-sdk 而不是 wx-server-sdk
// 但为了兼容小程序端，我们需要同时支持两种方式

let cloud, db;

try {
  // 尝试使用 wx-server-sdk (小程序端)
  cloud = require('wx-server-sdk');
  cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
  });
  db = cloud.database();
  console.log('✅ 使用 wx-server-sdk 初始化成功');
} catch (error) {
  console.log('⚠️ wx-server-sdk 初始化失败，尝试使用 @cloudbase/node-sdk');
  try {
    // 备用方案：使用 @cloudbase/node-sdk (Web端兼容)
    const tcb = require('@cloudbase/node-sdk');
    cloud = tcb.init({
      env: process.env.TCB_ENV || 'cloud1-5g6pvnpl88dc0142'
    });
    db = cloud.database();
    console.log('✅ 使用 @cloudbase/node-sdk 初始化成功');
  } catch (nodeError) {
    console.error('❌ 所有SDK初始化都失败:', nodeError);
    // 使用原来的方式作为最后备用
    cloud = require('wx-server-sdk');
    cloud.init({
      env: cloud.DYNAMIC_CURRENT_ENV
    });
    db = cloud.database();
  }
}

// 内置权限验证函数
async function verifyAdmin(openid) {
  try {
    if (!openid) {
      return { isAdmin: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isAdmin: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isAdmin = user.auth && user.auth.role === 'admin' && user.auth.status === 'active'

    return {
      isAdmin,
      user,
      message: isAdmin ? '权限验证通过' : '权限不足'
    }
  } catch (error) {
    console.error('权限验证失败:', error)
    return { isAdmin: false, message: '权限验证失败' }
  }
}

// 记录操作日志
async function logOperation(operation, user, data = {}) {
  try {
    await db.collection('operation_logs').add({
      data: {
        operation,
        userId: user._id,
        userOpenid: user.openid,
        userName: user.profile?.nickname || '未知用户',
        operationData: data,
        timestamp: new Date(),
        ip: '',
        userAgent: ''
      }
    })
  } catch (error) {
    console.error('记录操作日志失败:', error)
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()

  // 调试信息：打印上下文信息
  console.log('🔍 云函数调用上下文:', {
    OPENID: wxContext.OPENID,
    APPID: wxContext.APPID,
    UNIONID: wxContext.UNIONID,
    SOURCE: wxContext.SOURCE,
    httpMethod: event.httpMethod,
    hasAdminPassword: !!event.adminPassword,
    isWebCall: !wxContext.OPENID && !event.httpMethod // Web端SDK调用的特征
  })

  // 处理HTTP请求
  if (event.httpMethod) {
    // 处理OPTIONS预检请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400'
        },
        body: ''
      }
    }

    // HTTP请求处理
    let requestData = {}

    if (event.httpMethod === 'POST') {
      try {
        requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body
      } catch (error) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            error: '请求数据格式错误'
          })
        }
      }
    } else if (event.httpMethod === 'GET') {
      requestData = event.queryStringParameters || {}
    }

    // 对于HTTP请求，暂时跳过权限验证（在生产环境中应该实现其他认证方式）
    console.log('HTTP请求 - 跳过微信权限验证')

    const { action, data } = requestData

    console.log('管理后台HTTP API请求:', {
      action,
      data,
      method: event.httpMethod
    })

    try {
      let result = await handleAction(action, data, null)

      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        },
        body: JSON.stringify(result)
      }
    } catch (error) {
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: false,
          error: error.message
        })
      }
    }
  }

  // 微信小程序调用处理
  const { action, data, adminPassword } = event

  // 调试信息：打印接收到的参数
  console.log('🔍 接收到的参数:', {
    action,
    hasData: !!data,
    adminPassword: adminPassword ? '***已提供***' : '未提供',
    eventKeys: Object.keys(event)
  })

  // 如果提供了管理员密码，使用密码验证（用于Web端管理后台）
  if (adminPassword) {
    console.log('🔑 检测到管理员密码，进行密码验证...');
    const ADMIN_PASSWORD = 'admin123456' // 简单的管理员密码
    if (adminPassword !== ADMIN_PASSWORD) {
      console.log('❌ 管理员密码错误:', adminPassword);
      return {
        success: false,
        error: '管理员密码错误',
        code: 403
      }
    }
    console.log('✅ Web端管理员密码验证通过，跳过OPENID检查');
    // Web端使用密码验证，不需要检查OPENID
  } else {
    // 验证管理员权限（小程序端）
    console.log('🔍 小程序端权限验证，OPENID:', wxContext.OPENID);

    if (!wxContext.OPENID) {
      return {
        success: false,
        error: '用户未登录 - 无法获取用户标识',
        code: 403
      }
    }

    const authResult = await verifyAdmin(wxContext.OPENID)
    if (!authResult.isAdmin) {
      return {
        success: false,
        error: authResult.message,
        code: 403
      }
    }
    // 将用户信息添加到event中
    event.currentUser = authResult.user
  }

  console.log('管理后台API请求:', {
    action,
    data,
    user: event.currentUser?.profile?.nickname || 'Web管理员'
  })

  return await handleAction(action, data, event.currentUser || null)
}

// 处理具体的操作
async function handleAction(action, data, currentUser) {
  console.log('🎯 进入handleAction函数:', {
    action,
    hasData: !!data,
    hasCurrentUser: !!currentUser,
    currentUserType: currentUser ? 'provided' : 'null'
  });

  try {
    switch (action) {
      case 'getStats':
        return await getStats()
      case 'getUsers':
        return await getUsers(data)
      case 'getEmojis':
        return await getEmojis(data)
      case 'createAdmin':
        return await createAdmin(data)
      case 'initDatabase':
        return await initDatabase()
      case 'testDatabase':
        return await testDatabase()
      case 'clearAllData':
        return await clearAllData()
      case 'deleteEmoji':
        return await deleteEmoji(data)
      case 'createCategory':
        return await createCategory(data)
      case 'updateCategory':
        return await updateCategory(data)
      case 'deleteCategory':
        return await deleteCategory(data)
      case 'getCategoryList':
        return await getCategoryList()
      case 'batchUpdateEmojiStatus':
        return await batchUpdateEmojiStatus(data)
      case 'batchDeleteEmojis':
        return await batchDeleteEmojis(data)
      case 'updateEmojiInfo':
        return await updateEmojiInfo(data)
      case 'updateEmojiStatus':
        return await updateEmojiStatus(data)
      case 'getEmojiList':
        return await getEmojiList(data)
      case 'addEmoji':
        return await addEmoji(data)
      case 'getBannerList':
        return await getBannerList(data)
      case 'addBanner':
        return await addBanner(data)
      case 'getUserList':
        return await getUserList(data)
      // initTestData 已删除 - 防止创建虚拟测试数据
      default:
        return { success: false, message: '未知操作' }
    }
  } catch (error) {
    console.error('管理后台API错误:', error)
    return { success: false, message: error.message }
  }
}

// 测试数据库连接
async function testDatabase() {
  try {
    console.log('测试数据库连接...')
    
    // 简单的数据库操作测试
    const testResult = await db.collection('users').limit(1).get()
    
    return {
      success: true,
      message: '数据库连接正常',
      data: {
        hasUsers: testResult.data.length > 0,
        userCount: testResult.data.length
      }
    }
  } catch (error) {
    console.error('数据库连接测试失败:', error)
    return {
      success: false,
      message: '数据库连接失败: ' + error.message
    }
  }
}

// 初始化数据库（创建集合并添加测试数据）
async function initDatabase() {
  try {
    console.log('开始初始化数据库...')
    
    let results = {
      users: false,
      emojis: false,
      categories: false,
      errors: []
    }
    
    // 创建测试用户
    try {
      console.log('创建测试用户...')
      const userResult = await db.collection('users').add({
        data: {
          openid: 'test_user_' + Date.now(),
          profile: {
            nickname: '测试用户',
            avatar: ''
          },
          auth: {
            role: 'user',
            status: 'active'
          },
          stats: {
            likeCount: 0,
            collectCount: 0,
            downloadCount: 0
          },
          createTime: new Date()
        }
      })
      console.log('users集合创建成功, ID:', userResult._id)
      results.users = true
    } catch (error) {
      console.error('创建用户失败:', error)
      results.errors.push('用户创建失败: ' + error.message)
    }
    
    // 创建测试表情包
    try {
      console.log('创建测试表情包...')
      const emojiResult = await db.collection('emojis').add({
        data: {
          title: '测试表情包',
          description: '这是一个测试表情包',
          category: '测试分类',
          tags: ['测试', '表情'],
          imageUrl: 'https://via.placeholder.com/150x150?text=Test',
          status: 'approved',
          createTime: new Date()
        }
      })
      console.log('emojis集合创建成功, ID:', emojiResult._id)
      results.emojis = true
    } catch (error) {
      console.error('创建表情失败:', error)
      results.errors.push('表情创建失败: ' + error.message)
    }
    
    // 创建测试分类
    try {
      console.log('创建测试分类...')
      const categoryResult = await db.collection('categories').add({
        data: {
          name: '测试分类',
          icon: '🧪',
          description: '这是一个测试分类',
          createTime: new Date(),
          status: 'active'
        }
      })
      console.log('categories集合创建成功, ID:', categoryResult._id)
      results.categories = true
    } catch (error) {
      console.error('创建分类失败:', error)
      results.errors.push('分类创建失败: ' + error.message)
    }
    
    // 等待一下确保数据已写入
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 验证创建结果
    const finalStats = await getStats()
    
    const successCount = [results.users, results.emojis, results.categories].filter(Boolean).length
    
    return {
      success: successCount > 0,
      message: `数据库初始化完成！成功创建 ${successCount}/3 个集合`,
      data: {
        results,
        errors: results.errors,
        finalStats: finalStats.data
      }
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return {
      success: false,
      message: '数据库初始化失败: ' + error.message
    }
  }
}

async function getStats() {
  try {
    console.log('📊 开始执行getStats函数...')
    console.log('📊 获取统计数据...')
    
    // 使用更安全的方式获取集合计数
    let usersCount = 0
    let emojisCount = 0
    let categoriesCount = 0
    
    try {
      const usersResult = await db.collection('users').count()
      usersCount = usersResult.total || 0
      console.log('用户数量:', usersCount)
    } catch (error) {
      console.log('users集合不存在或为空:', error.message)
    }
    
    try {
      const emojisResult = await db.collection('emojis').count()
      emojisCount = emojisResult.total || 0
      console.log('表情数量:', emojisCount)
    } catch (error) {
      console.log('emojis集合不存在或为空:', error.message)
    }
    
    try {
      const categoriesResult = await db.collection('categories').count()
      categoriesCount = categoriesResult.total || 0
      console.log('分类数量:', categoriesCount)
    } catch (error) {
      console.log('categories集合不存在或为空:', error.message)
    }
    
    return {
      success: true,
      data: {
        users: usersCount,
        emojis: emojisCount,
        categories: categoriesCount
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    return {
      success: true,
      data: { users: 0, emojis: 0, categories: 0 }
    }
  }
}

async function getUsers() {
  try {
    console.log('获取用户列表...')
    const result = await db.collection('users').limit(20).get()
    console.log('用户列表:', result.data.length, '条')
    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return { success: true, data: [] }
  }
}

async function getEmojis() {
  try {
    console.log('获取表情列表...')
    const result = await db.collection('emojis').limit(20).get()
    console.log('表情列表:', result.data.length, '条')
    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('获取表情列表失败:', error)
    return { success: true, data: [] }
  }
}

async function createAdmin(data) {
  try {
    console.log('创建管理员:', data)
    const { openid, nickname = '管理员' } = data
    
    // 检查是否已存在
    const existingUser = await db.collection('users').where({ openid }).get()
    
    if (existingUser.data.length > 0) {
      // 更新为管理员
      await db.collection('users').doc(existingUser.data[0]._id).update({
        data: {
          'auth.role': 'admin',
          'auth.status': 'active',
          lastLoginTime: new Date()
        }
      })
      return { success: true, message: '用户已设置为管理员' }
    } else {
      // 创建新管理员
      const result = await db.collection('users').add({
        data: {
          openid,
          profile: { nickname, avatar: '' },
          auth: { role: 'admin', status: 'active' },
          stats: {
            likeCount: 0,
            collectCount: 0,
            downloadCount: 0
          },
          createTime: new Date()
        }
      })
      console.log('管理员创建成功, ID:', result._id)
      return { success: true, message: '管理员创建成功' }
    }
  } catch (error) {
    console.error('创建管理员失败:', error)
    return { success: false, message: '创建失败: ' + error.message }
  }
}

// 创建分类
async function createCategory(data) {
  try {
    const { name, icon, description, sort = 0 } = data

    if (!name || !icon) {
      return { success: false, message: '分类名称和图标不能为空' }
    }

    const result = await db.collection('categories').add({
      data: {
        name,
        icon,
        description: description || '',
        sort,
        status: 'active',
        emojiCount: 0,
        createTime: new Date(),
        updateTime: new Date()
      }
    })

    return { success: true, message: '分类创建成功', id: result._id }
  } catch (error) {
    console.error('创建分类失败:', error)
    return { success: false, message: '创建失败: ' + error.message }
  }
}

// 更新分类
async function updateCategory(data) {
  try {
    const { id, name, icon, description, sort, status } = data

    if (!id) {
      return { success: false, message: '分类ID不能为空' }
    }

    const updateData = { updateTime: new Date() }
    if (name) updateData.name = name
    if (icon) updateData.icon = icon
    if (description !== undefined) updateData.description = description
    if (sort !== undefined) updateData.sort = sort
    if (status) updateData.status = status

    await db.collection('categories').doc(id).update({
      data: updateData
    })

    return { success: true, message: '分类更新成功' }
  } catch (error) {
    console.error('更新分类失败:', error)
    return { success: false, message: '更新失败: ' + error.message }
  }
}

// 删除分类
async function deleteCategory(data) {
  try {
    const { id } = data

    if (!id) {
      return { success: false, message: '分类ID不能为空' }
    }

    // 检查是否有表情包使用此分类
    const emojiCount = await db.collection('emojis').where({
      categoryId: id
    }).count()

    if (emojiCount.total > 0) {
      return { success: false, message: '该分类下还有表情包，无法删除' }
    }

    await db.collection('categories').doc(id).remove()

    return { success: true, message: '分类删除成功' }
  } catch (error) {
    console.error('删除分类失败:', error)
    return { success: false, message: '删除失败: ' + error.message }
  }
}

// 获取分类列表（带统计）
async function getCategoryList() {
  try {
    const categories = await db.collection('categories')
      .orderBy('sort', 'asc')
      .get()

    // 为每个分类统计表情包数量
    for (let category of categories.data) {
      const count = await db.collection('emojis').where({
        categoryId: category._id,
        status: 'published'
      }).count()

      category.emojiCount = count.total || 0
    }

    return { success: true, data: categories.data }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return { success: false, message: error.message }
  }
}

// 批量更新表情包状态
async function batchUpdateEmojiStatus(data) {
  try {
    const { ids, status } = data

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return { success: false, message: '请选择要操作的表情包' }
    }

    if (!status) {
      return { success: false, message: '请指定状态' }
    }

    // 使用批量操作
    const batch = db.batch()

    ids.forEach(id => {
      batch.update(db.collection('emojis').doc(id), {
        data: {
          status,
          updateTime: new Date()
        }
      })
    })

    await batch.commit()

    return { success: true, message: `批量更新${ids.length}个表情包状态成功` }
  } catch (error) {
    console.error('批量更新表情包状态失败:', error)
    return { success: false, message: '批量更新失败: ' + error.message }
  }
}

// 批量删除表情包
async function batchDeleteEmojis(data) {
  try {
    const { ids } = data

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return { success: false, message: '请选择要删除的表情包' }
    }

    // 使用批量操作
    const batch = db.batch()

    ids.forEach(id => {
      batch.remove(db.collection('emojis').doc(id))
    })

    await batch.commit()

    return { success: true, message: `批量删除${ids.length}个表情包成功` }
  } catch (error) {
    console.error('批量删除表情包失败:', error)
    return { success: false, message: '批量删除失败: ' + error.message }
  }
}

// 更新表情包信息
async function updateEmojiInfo(data) {
  try {
    const { id, title, description, tags, categoryId, status } = data

    if (!id) {
      return { success: false, message: '表情包ID不能为空' }
    }

    const updateData = { updateTime: new Date() }
    if (title) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (tags) updateData.tags = tags
    if (categoryId) updateData.categoryId = categoryId
    if (status) updateData.status = status

    await db.collection('emojis').doc(id).update({
      data: updateData
    })

    return { success: true, message: '表情包信息更新成功' }
  } catch (error) {
    console.error('更新表情包信息失败:', error)
    return { success: false, message: '更新失败: ' + error.message }
  }
}

// 更新表情包状态
async function updateEmojiStatus(data) {
  try {
    const { id, status } = data

    if (!id || !status) {
      return { success: false, message: '参数不完整' }
    }

    await db.collection('emojis').doc(id).update({
      data: {
        status,
        updateTime: new Date()
      }
    })

    return { success: true, message: '状态更新成功' }
  } catch (error) {
    console.error('更新表情包状态失败:', error)
    return { success: false, message: '更新失败: ' + error.message }
  }
}

// 删除表情包
// 清空所有数据
async function clearAllData() {
  try {
    // 清空表情包
    const emojis = await db.collection('emojis').get()
    const emojiDeletePromises = emojis.data.map(emoji =>
      db.collection('emojis').doc(emoji._id).remove()
    )

    // 清空分类
    const categories = await db.collection('categories').get()
    const categoryDeletePromises = categories.data.map(category =>
      db.collection('categories').doc(category._id).remove()
    )

    await Promise.all([...emojiDeletePromises, ...categoryDeletePromises])

    return {
      success: true,
      message: `已清空 ${emojis.data.length} 个表情包和 ${categories.data.length} 个分类`
    }
  } catch (error) {
    console.error('清空数据失败:', error)
    return { success: false, message: error.message }
  }
}

// 删除表情包
async function deleteEmoji(data) {
  try {
    const { id } = data

    if (!id) {
      return { success: false, message: '表情包ID不能为空' }
    }

    await db.collection('emojis').doc(id).remove()

    return { success: true, message: '删除成功' }
  } catch (error) {
    console.error('删除表情包失败:', error)
    return { success: false, message: '删除失败: ' + error.message }
  }
}

// 获取表情包列表
async function getEmojiList(data = {}) {
  try {
    const { page = 1, limit = 20, category, status } = data

    let query = db.collection('emojis')

    if (category) {
      query = query.where({
        category: category
      })
    }

    if (status) {
      query = query.where({
        status: status
      })
    }

    const result = await query
      .orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('获取表情包列表失败:', error)
    return { success: false, message: '获取失败: ' + error.message }
  }
}

// 添加表情包
async function addEmoji(data) {
  try {
    const { name, url, category, tags = [], status = 'published' } = data

    if (!name || !url || !category) {
      return { success: false, message: '表情包名称、URL和分类不能为空' }
    }

    const emojiData = {
      name,
      url,
      category,
      tags,
      status,
      createTime: new Date(),
      updateTime: new Date(),
      downloadCount: 0,
      likeCount: 0
    }

    const result = await db.collection('emojis').add({
      data: emojiData
    })

    return { success: true, data: { id: result._id, ...emojiData } }
  } catch (error) {
    console.error('添加表情包失败:', error)
    return { success: false, message: '添加失败: ' + error.message }
  }
}

// 获取横幅列表
async function getBannerList(data = {}) {
  try {
    const result = await db.collection('banners')
      .orderBy('priority', 'desc')
      .orderBy('createTime', 'desc')
      .get()

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('获取横幅列表失败:', error)
    return { success: false, message: '获取失败: ' + error.message }
  }
}

// 添加横幅
async function addBanner(data) {
  try {
    const { title, imageUrl, linkUrl, priority = 1, status = 'show' } = data

    if (!title || !imageUrl) {
      return { success: false, message: '横幅标题和图片URL不能为空' }
    }

    const bannerData = {
      title,
      imageUrl,
      linkUrl: linkUrl || '',
      priority,
      status,
      createTime: new Date(),
      updateTime: new Date()
    }

    const result = await db.collection('banners').add({
      data: bannerData
    })

    return { success: true, data: { id: result._id, ...bannerData } }
  } catch (error) {
    console.error('添加横幅失败:', error)
    return { success: false, message: '添加失败: ' + error.message }
  }
}

// 获取用户列表
async function getUserList(data = {}) {
  try {
    const { page = 1, limit = 20 } = data

    const result = await db.collection('users')
      .orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return { success: false, message: '获取失败: ' + error.message }
  }
}

// initTestData 函数已删除 - 防止创建虚拟测试数据
// 所有数据请通过管理后台手动添加