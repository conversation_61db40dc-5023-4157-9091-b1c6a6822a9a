# 🎯 标准配置和开发规范 - 必读文档

> **⚠️ 重要提示：遇到任何问题，必须先阅读此文档！**
> 
> 本文档包含项目中所有验证过的正确配置和开发方式，是解决问题的第一参考。

---

## 📋 文档使用说明

### 🔍 快速查找
- **SDK问题** → 查看"云开发SDK标准配置"
- **身份认证问题** → 查看"身份认证标准方案"  
- **数据库操作问题** → 查看"数据库操作标准方式"
- **部署问题** → 查看"部署和环境配置"

### ⚡ 紧急情况
如果遇到紧急问题，直接跳转到对应章节，复制粘贴标准配置即可。

---

## 🌐 云开发SDK标准配置

### ✅ 正确的SDK配置（已验证）

```html
<!-- 方案1：SDK 2.0版本（推荐用于新功能） -->
<script src="https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js"></script>
<script>
// 正确的初始化方式
const app = window.cloudbase.init({
    env: 'cloud1-5g6pvnpl88dc0142',
    clientId: 'cloud1-5g6pvnpl88dc0142'  // SDK 2.0必需参数
});
</script>

<!-- 方案2：SDK 1.x版本（用于兼容旧功能） -->
<script src="https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js"></script>
<script>
// 旧版本初始化方式
const app = window.tcb.init({
    env: 'cloud1-5g6pvnpl88dc0142'
});
</script>
```

### 🔄 多CDN备用方案

```javascript
// 标准的多CDN加载策略
const sdkSources = [
    'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js',  // 首选
    'https://unpkg.com/@cloudbase/js-sdk@2.17.5/dist/index.umd.js',            // 备用1
    'https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js'                      // 备用2（兼容）
];

async function loadSDK() {
    for (const url of sdkSources) {
        try {
            await loadScript(url);
            if (window.cloudbase || window.tcb) return true;
        } catch (error) {
            console.log(`CDN失败: ${url}`);
            continue;
        }
    }
    throw new Error('所有CDN都无法加载');
}
```

### ❌ 错误配置（避免使用）

```javascript
// ❌ 错误1：使用不稳定的CDN
'https://static.cloudbase.net/...'  // 经常不可用

// ❌ 错误2：SDK 2.0缺少clientId参数
window.cloudbase.init({ env: 'xxx' });  // 会导致ACCESS_TOKEN_DISABLED

// ❌ 错误3：混用不同版本的API
window.tcb.init() + window.cloudbase.auth()  // 版本不兼容
```

---

## 🔐 身份认证标准方案

### ✅ 推荐方案：webAdminAPI云函数

```javascript
// 标准的云函数调用方式（绕过身份认证）
async function callWebAdminAPI(action, data = {}) {
    const result = await app.callFunction({
        name: 'webAdminAPI',
        data: {
            action: action,
            data: data,
            adminPassword: 'admin123456'  // 管理员密码验证
        }
    });
    
    if (result.result && result.result.success) {
        return result.result.data;
    } else {
        throw new Error(result.result?.error || '云函数调用失败');
    }
}

// 使用示例
const categories = await callWebAdminAPI('getCategoryList');
const newEmoji = await callWebAdminAPI('addEmoji', emojiData);
```

### 🔄 备用方案：匿名登录

```javascript
// 仅在webAdminAPI不可用时使用
async function anonymousLogin() {
    try {
        const auth = app.auth();
        await auth.signInAnonymously();
        return true;
    } catch (error) {
        if (error.message.includes('ACCESS_TOKEN_DISABLED')) {
            throw new Error('需要在云开发控制台开启匿名登录');
        }
        throw error;
    }
}
```

### ❌ 错误做法（避免）

```javascript
// ❌ 错误1：直接使用数据库操作（权限问题）
const db = app.database();
await db.collection('emojis').add(data);  // 可能权限被拒绝

// ❌ 错误2：不处理身份认证失败
await auth.signInAnonymously();  // 没有错误处理
```

---

## 💾 数据库操作标准方式

### ✅ 推荐：通过webAdminAPI操作

```javascript
// 标准的数据操作方式
const dataOperations = {
    // 获取数据
    async getCategories() {
        return await callWebAdminAPI('getCategoryList');
    },
    
    // 添加数据
    async addEmoji(emojiData) {
        return await callWebAdminAPI('addEmoji', emojiData);
    },
    
    // 更新数据
    async updateEmoji(id, updateData) {
        return await callWebAdminAPI('updateEmoji', { id, ...updateData });
    },
    
    // 删除数据
    async deleteEmoji(id) {
        return await callWebAdminAPI('deleteEmoji', { id });
    }
};
```

### 🔄 备用：直接数据库操作

```javascript
// 仅在云函数不可用时使用
async function directDatabaseOperation() {
    await anonymousLogin();  // 先进行身份认证
    
    const db = app.database();
    const result = await db.collection('emojis').add({
        data: emojiData
    });
    
    return result;
}
```

---

## 🚀 部署和环境配置

### ✅ 标准环境配置

```javascript
// 环境配置
const CONFIG = {
    // 云开发环境ID
    cloudEnv: 'cloud1-5g6pvnpl88dc0142',
    
    // 管理员密码
    adminPassword: 'admin123456',
    
    // 本地服务器端口
    localPort: 9000,
    
    // 云函数列表
    cloudFunctions: [
        'webAdminAPI',      // 管理后台API
        'miniProgramAPI'    // 小程序API
    ]
};
```

### 📁 标准目录结构

```
项目根目录/
├── admin/                  # 管理后台（简化版）
│   ├── index.html         # 主页面
│   └── sync-test.html     # 同步测试工具
├── admin-serverless/      # 管理后台（完整版）
│   ├── index-ui-unchanged.html  # 主管理界面
│   └── js/               # JavaScript文件
├── miniprogram/          # 小程序源码
└── 技术文档库/           # 技术文档（重要！）
    ├── 🎯标准配置和开发规范-必读.md
    └── 其他技术文档...
```

### 🌐 本地开发服务器

```bash
# 启动本地服务器（标准命令）
cd 项目根目录
python -m http.server 9000

# 访问地址
http://localhost:9000/admin/                    # 简化版管理后台
http://localhost:9000/admin-serverless/        # 完整版管理后台
```

---

## 🔧 常见问题快速解决

### SDK加载失败
```javascript
// 立即使用这个配置
const sdkSources = [
    'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js'
];
// 如果失败，检查网络连接或使用备用CDN
```

### ACCESS_TOKEN_DISABLED错误
```javascript
// 立即切换到webAdminAPI方案
await callWebAdminAPI('getCategoryList');
// 不要尝试修复身份认证，直接使用云函数
```

### 数据库权限被拒绝
```javascript
// 立即使用云函数方案
await callWebAdminAPI('addEmoji', data);
// 不要直接操作数据库
```

### 网络连接问题
```javascript
// 使用多CDN备用策略
const cdnList = ['cdn1', 'cdn2', 'cdn3'];
// 自动尝试所有CDN直到成功
```

---

## 📝 开发规范

### ✅ 正确的开发流程

1. **遇到问题** → 先查看此文档
2. **选择方案** → 使用标准配置
3. **实施验证** → 测试功能是否正常
4. **记录经验** → 更新技术文档

### ✅ 代码规范

```javascript
// 标准的错误处理
try {
    const result = await callWebAdminAPI('action', data);
    // 处理成功结果
} catch (error) {
    console.error('操作失败:', error.message);
    // 显示用户友好的错误信息
}

// 标准的配置管理
const config = {
    env: 'cloud1-5g6pvnpl88dc0142',
    adminPassword: 'admin123456'
};
```

### ❌ 避免的做法

```javascript
// ❌ 不要硬编码配置
const app = tcb.init({ env: 'hardcoded-env-id' });

// ❌ 不要忽略错误处理
await someAsyncOperation();  // 没有try-catch

// ❌ 不要使用未验证的方案
// 看到网上的教程就直接复制，没有验证是否适用于本项目
```

---

## 🎯 核心原则

### 1. 配置优先级
```
项目验证过的配置 > 官方推荐配置 > 网上教程配置
```

### 2. 方案选择优先级
```
webAdminAPI云函数 > 匿名登录 > 直接数据库操作
```

### 3. 问题解决优先级
```
查看此文档 > 查看项目代码 > 搜索网络资料
```

---

## 🚨 紧急联系方式

如果此文档无法解决问题：

1. **检查项目中的成功案例**：`admin-serverless/index-ui-unchanged.html`
2. **查看详细技术文档**：`技术文档库/` 目录下的其他文档
3. **查看云函数实现**：了解webAdminAPI的具体功能

**记住：这个文档包含了所有验证过的正确做法，99%的问题都能在这里找到答案！**
