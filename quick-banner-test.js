// 快速横幅修复验证
const { chromium } = require('playwright');

async function quickBannerTest() {
    console.log('🚀 快速验证横幅修复...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理横幅数据') || text.includes('横幅数据加载') || text.includes('undefined')) {
            console.log('🔧', text);
        }
    });
    
    try {
        console.log('📍 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录完成');
            await page.waitForTimeout(8000);
        }
        
        console.log('\n📍 进入横幅管理');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            console.log('✅ 已进入横幅管理页面');
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查横幅数据显示');
        
        // 检查横幅表格数据
        const bannerData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                return {
                    index: index,
                    data: cells.map(cell => cell.textContent?.trim()),
                    hasUndefined: cells.some(cell => cell.textContent?.includes('undefined')),
                    hasImage: !!row.querySelector('img')
                };
            });
        });
        
        console.log('📋 横幅表格数据:');
        let hasIssues = false;
        
        bannerData.forEach((row, index) => {
            console.log(`\n  行 ${index + 1}:`);
            console.log(`    数据: [${row.data.slice(0, 5).join(', ')}...]`); // 只显示前5列
            console.log(`    有undefined: ${row.hasUndefined}`);
            console.log(`    有图片: ${row.hasImage}`);
            
            if (row.hasUndefined) {
                console.log('    🔴 发现undefined数据');
                hasIssues = true;
            }
        });
        
        if (!hasIssues) {
            console.log('\n✅ 横幅数据显示正常 - 未发现undefined问题');
        } else {
            console.log('\n🔴 横幅数据仍有问题');
        }
        
        // 检查AdminApp数据状态
        const adminAppData = await page.evaluate(() => {
            return {
                hasBanners: !!AdminApp.data.banners,
                bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                sampleBanner: AdminApp.data.banners && AdminApp.data.banners.length > 0 ? {
                    title: AdminApp.data.banners[0].title,
                    status: AdminApp.data.banners[0].status,
                    hasTitle: !!AdminApp.data.banners[0].title
                } : null
            };
        });
        
        console.log('\n📊 AdminApp横幅数据状态:');
        console.log('  有横幅数据:', adminAppData.hasBanners);
        console.log('  横幅数量:', adminAppData.bannersCount);
        if (adminAppData.sampleBanner) {
            console.log('  示例横幅:');
            console.log('    标题:', adminAppData.sampleBanner.title);
            console.log('    状态:', adminAppData.sampleBanner.status);
            console.log('    有标题:', adminAppData.sampleBanner.hasTitle);
        }
        
        // 截图
        await page.screenshot({ path: 'quick-banner-fix-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: quick-banner-fix-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
quickBannerTest().catch(console.error);
