const { chromium } = require('playwright');

async function testCloudFunction() {
  console.log('🧪 开始测试云函数返回结果...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 导航到管理后台
    console.log('📱 打开管理后台...');
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForTimeout(5000);
    
    // 在控制台中直接测试云函数
    console.log('☁️ 测试云函数 getCategories...');
    
    const result = await page.evaluate(async () => {
      try {
        // 确保SDK已加载
        if (!window.cloudbase) {
          return { error: 'SDK未加载' };
        }
        
        // 初始化云开发
        const app = window.cloudbase.init({
          env: 'cloud1-5g6pvnpl88dc0142'
        });
        
        // 调用云函数
        const result = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getCategories' }
        });
        
        console.log('云函数返回结果:', result);
        return result;
        
      } catch (error) {
        console.error('云函数调用失败:', error);
        return { error: error.message };
      }
    });
    
    console.log('🔍 云函数测试结果:', JSON.stringify(result, null, 2));
    
    // 检查结果
    if (result.error) {
      console.error('❌ 云函数调用失败:', result.error);
    } else if (result.result && result.result.success && result.result.data) {
      console.log('✅ 云函数调用成功!');
      console.log('📊 分类数据:');
      result.result.data.forEach(category => {
        console.log(`  - ${category.name}: ${category.emojiCount || 0} 个表情包`);
      });
    } else {
      console.log('⚠️ 云函数返回格式异常:', result);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testCloudFunction().catch(console.error);
