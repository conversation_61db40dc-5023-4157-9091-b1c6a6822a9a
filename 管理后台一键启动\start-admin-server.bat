@echo off
chcp 65001 >nul
title Emoji Admin Panel Server

echo.
echo ========================================
echo  Emoji Admin Panel Server Launcher
echo ========================================
echo.

REM Get current script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "ADMIN_DIR=%PROJECT_ROOT%\admin-serverless"

echo Script Directory: %SCRIPT_DIR%
echo Project Root: %PROJECT_ROOT%
echo Admin Directory: %ADMIN_DIR%
echo.

REM Check if admin-serverless directory exists
if not exist "%ADMIN_DIR%" (
    echo ERROR: admin-serverless directory not found
    echo Expected path: %ADMIN_DIR%
    echo.
    echo Please make sure this script is in the correct location:
    echo - Project root folder should contain admin-serverless directory
    echo - This script should be in a subfolder of the project root
    echo.
    pause
    exit /b 1
)

echo Admin directory found: %ADMIN_DIR%
echo.

REM Change to admin directory
cd /d "%ADMIN_DIR%"

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python from: https://www.python.org/
    echo.
    pause
    exit /b 1
)

echo Python installation verified
python --version
echo.

REM Check if main.html exists
if not exist "main.html" (
    echo ERROR: main.html not found in admin-serverless directory
    echo Current directory: %CD%
    echo.
    dir /b *.html
    echo.
    pause
    exit /b 1
)

echo main.html found
echo.

REM Kill any existing servers on port 9001
echo Checking for existing servers on port 9001...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :9001') do (
    echo Killing process %%a
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Starting HTTP server on port 9001...
echo.
echo Server URLs:
echo   Main Admin Panel: http://localhost:9001/main.html
echo   Index Page: http://localhost:9001/index.html
echo   Direct Access: http://localhost:9001/
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start browser automatically
start http://localhost:9001/main.html

REM Start Python HTTP server
python -m http.server 9001

echo.
echo Server stopped
pause
