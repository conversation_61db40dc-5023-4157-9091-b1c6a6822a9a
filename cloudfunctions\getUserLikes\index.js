// 云函数入口文件 - 获取用户点赞列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { page = 1, limit = 20 } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 获取用户点赞的表情包ID列表
    const likeResult = await db.collection('user_likes').where({
      userId: OPENID
    }).orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()
    
    if (likeResult.data.length === 0) {
      return {
        success: true,
        data: []
      }
    }
    
    // 获取表情包详情
    const emojiIds = likeResult.data.map(like => like.emojiId)
    const emojiResult = await db.collection('emojis').where({
      _id: db.command.in(emojiIds)
    }).get()
    
    // 按点赞时间排序
    const sortedEmojis = likeResult.data.map(like => {
      const emoji = emojiResult.data.find(e => e._id === like.emojiId)
      return emoji ? {
        ...emoji,
        id: emoji._id,
        likedAt: like.createTime
      } : null
    }).filter(Boolean)
    
    return {
      success: true,
      data: sortedEmojis
    }
  } catch (error) {
    console.error('获取用户点赞列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}