<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包管理后台 - Serverless版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 14px;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .nav-tab {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .content-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .content-panel.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #e53e3e;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .btn-success {
            background: #38a169;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f7fafc;
            font-weight: 600;
            color: #2d3748;
        }

        .data-table tr:hover {
            background: #f7fafc;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-published {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-draft {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-show {
            background: #bee3f8;
            color: #2a4365;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        }

        .notification.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }

        .notification.warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .notification.info {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .notification button {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .error {
            background: #fed7d7;
            color: #742a2a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .success {
            background: #c6f6d5;
            color: #22543d;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2d3748;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }

        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            min-height: 100px;
            resize: vertical;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .action-bar {
                flex-direction: column;
                align-items: stretch;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🎭 表情包管理后台</h1>
            <div class="subtitle">Serverless版 - 基于微信云开发</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('dashboard')">📊 仪表盘</button>
            <button class="nav-tab" onclick="switchTab('categories')">📁 分类管理</button>
            <button class="nav-tab" onclick="switchTab('emojis')">😊 表情包管理</button>
            <button class="nav-tab" onclick="switchTab('banners')">🎯 横幅管理</button>
            <button class="nav-tab" onclick="switchTab('users')">👥 用户管理</button>
        </div>

        <!-- 仪表盘 -->
        <div id="dashboard" class="content-panel active">
            <h2>📊 数据统计</h2>
            <div class="stats-grid" id="statsGrid">
                <div class="loading">正在加载统计数据...</div>
            </div>
            
            <div class="action-bar">
                <h3>🔧 系统操作</h3>
                <div>
                    <button class="btn" onclick="refreshStats()">🔄 刷新数据</button>
                    <button class="btn btn-success" onclick="initTestData()">📦 初始化测试数据</button>
                </div>
            </div>
        </div>

        <!-- 分类管理 -->
        <div id="categories" class="content-panel">
            <div class="action-bar">
                <h2>📁 分类管理</h2>
                <button class="btn" onclick="showAddCategoryModal()">➕ 添加分类</button>
            </div>
            <div id="categoriesContent">
                <div class="loading">正在加载分类数据...</div>
            </div>
        </div>

        <!-- 表情包管理 -->
        <div id="emojis" class="content-panel">
            <div class="action-bar">
                <h2>😊 表情包管理</h2>
                <button class="btn" onclick="showAddEmojiModal()">➕ 添加表情包</button>
            </div>
            <div id="emojisContent">
                <div class="loading">正在加载表情包数据...</div>
            </div>
        </div>

        <!-- 横幅管理 -->
        <div id="banners" class="content-panel">
            <div class="action-bar">
                <h2>🎯 横幅管理</h2>
                <button class="btn" onclick="showAddBannerModal()">➕ 添加横幅</button>
            </div>
            <div id="bannersContent">
                <div class="loading">正在加载横幅数据...</div>
            </div>
        </div>

        <!-- 用户管理 -->
        <div id="users" class="content-panel">
            <h2>👥 用户管理</h2>
            <div id="usersContent">
                <div class="loading">正在加载用户数据...</div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div id="modalContent"></div>
        </div>
    </div>

    <!-- 引入微信云开发SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        // 初始化云开发
        console.log('🔧 初始化云开发环境...');

        if (typeof wx !== 'undefined' && wx.cloud) {
            try {
                wx.cloud.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });
                console.log('✅ 云开发初始化成功');
                window.cloudInitialized = true;
            } catch (error) {
                console.error('❌ 云开发初始化失败:', error);
                window.cloudInitialized = false;
            }
        } else {
            console.warn('⚠️ 云开发环境不可用，将使用模拟数据');
            window.cloudInitialized = false;
        }
    </script>
    <script src="./js/app.js"></script>
</body>
</html>
