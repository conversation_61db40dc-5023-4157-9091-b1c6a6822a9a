<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 网络层诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .critical {
            background-color: #f8d7da;
            border: 2px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .network-test {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 网络层诊断工具</h1>
        
        <div class="critical">
            <h3>🎯 问题定位</h3>
            <p><strong>现象:</strong> 数据库查询返回 undefined</p>
            <p><strong>可能原因:</strong> 网络请求层面的问题</p>
            <p><strong>诊断策略:</strong> 逐层检查网络连通性</p>
        </div>
        
        <h3>🔍 诊断步骤</h3>
        <button onclick="runFullDiagnostic()">🚀 运行完整诊断</button>
        <button onclick="testBasicNetwork()">🌐 基础网络测试</button>
        <button onclick="testCloudbaseAPI()">☁️ 云开发API测试</button>
        <button onclick="testCORS()">🔒 CORS测试</button>
        <button onclick="openDevTools()">🛠️ 打开开发者工具</button>
        
        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }

        function openDevTools() {
            log('💡 请按 F12 打开开发者工具', 'info');
            log('💡 切换到 Network 标签页', 'info');
            log('💡 然后运行云开发测试，观察网络请求', 'info');
            log('💡 查看是否有失败的请求（红色）', 'warning');
        }

        async function testBasicNetwork() {
            log('=== 基础网络连通性测试 ===', 'info');
            
            const testUrls = [
                { name: '百度', url: 'https://www.baidu.com' },
                { name: '腾讯云', url: 'https://cloud.tencent.com' },
                { name: '云开发控制台', url: 'https://console.cloud.tencent.com' }
            ];
            
            for (const test of testUrls) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url, { 
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache'
                    });
                    const endTime = Date.now();
                    log(`✅ ${test.name} 连通正常 (${endTime - startTime}ms)`, 'success');
                } catch (error) {
                    log(`❌ ${test.name} 连接失败: ${error.message}`, 'error');
                }
            }
        }

        async function testCloudbaseAPI() {
            log('=== 云开发API端点测试 ===', 'info');
            
            const apiEndpoints = [
                'https://tcb-api.tencentcloudapi.com',
                'https://tcb.tencentcloudapi.com',
                'https://cloudbase.net'
            ];
            
            for (const endpoint of apiEndpoints) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(endpoint, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    const endTime = Date.now();
                    log(`✅ ${endpoint} 可访问 (${endTime - startTime}ms)`, 'success');
                } catch (error) {
                    log(`❌ ${endpoint} 不可访问: ${error.message}`, 'error');
                }
            }
        }

        async function testCORS() {
            log('=== CORS跨域测试 ===', 'info');
            
            try {
                // 测试一个已知支持CORS的API
                const response = await fetch('https://httpbin.org/get', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    log('✅ CORS功能正常', 'success');
                } else {
                    log(`⚠️ CORS测试响应异常: ${response.status}`, 'warning');
                }
            } catch (error) {
                log(`❌ CORS测试失败: ${error.message}`, 'error');
                log('💡 这可能表明浏览器的CORS策略过于严格', 'warning');
            }
        }

        async function runFullDiagnostic() {
            log('🚀 开始完整网络诊断...', 'info');
            
            // 清空之前的结果
            document.getElementById('results').innerHTML = '';
            
            // 1. 基础网络测试
            await testBasicNetwork();
            
            // 2. 云开发API测试
            await testCloudbaseAPI();
            
            // 3. CORS测试
            await testCORS();
            
            // 4. 浏览器环境检查
            log('=== 浏览器环境检查 ===', 'info');
            log(`用户代理: ${navigator.userAgent}`, 'info');
            log(`是否支持fetch: ${typeof fetch !== 'undefined' ? '是' : '否'}`, 'info');
            log(`是否支持Promise: ${typeof Promise !== 'undefined' ? '是' : '否'}`, 'info');
            log(`当前协议: ${window.location.protocol}`, 'info');
            log(`当前域名: ${window.location.hostname}`, 'info');
            
            // 5. 给出建议
            log('=== 诊断建议 ===', 'info');
            log('💡 如果所有网络测试都正常，问题可能在于:', 'warning');
            log('1. 云开发环境配置错误', 'warning');
            log('2. 云开发服务暂时不可用', 'warning');
            log('3. SDK版本与服务端不兼容', 'warning');
            log('4. 浏览器安全策略阻止了请求', 'warning');
            
            log('🔧 建议的解决步骤:', 'info');
            log('1. 检查云开发控制台中的环境状态', 'info');
            log('2. 尝试使用不同的浏览器', 'info');
            log('3. 检查网络防火墙设置', 'info');
            log('4. 联系云开发技术支持', 'info');
        }

        // 页面加载时显示说明
        window.addEventListener('DOMContentLoaded', function() {
            log('🌐 网络诊断工具已加载', 'success');
            log('💡 点击"运行完整诊断"开始检查网络连通性', 'info');
        });
    </script>
</body>
</html>
