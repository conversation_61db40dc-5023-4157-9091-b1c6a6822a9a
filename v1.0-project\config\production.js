// V1.0 生产环境配置
const PRODUCTION_CONFIG = {
  // 云开发环境配置
  cloudbase: {
    env: 'cloud1-5g6pvnpl88dc0142', // 生产环境ID
    region: 'ap-shanghai',
    timeout: 30000,
    retryTimes: 3
  },

  // 云函数配置
  cloudfunctions: {
    loginAPI: {
      timeout: 10000,
      memorySize: 256,
      environment: {
        JWT_SECRET: 'your-production-jwt-secret-key',
        JWT_EXPIRES_IN: '24h',
        ADMIN_USERNAME: 'admin',
        ADMIN_PASSWORD: 'your-production-admin-password'
      }
    },
    webAdminAPI: {
      timeout: 15000,
      memorySize: 512,
      environment: {
        JWT_SECRET: 'your-production-jwt-secret-key',
        MAX_TRANSACTION_RETRY: '3',
        CACHE_TTL: '300000'
      }
    },
    dataAPI: {
      timeout: 10000,
      memorySize: 256,
      environment: {
        CACHE_TTL_CATEGORIES: '300000',
        CACHE_TTL_EMOJIS: '180000',
        CACHE_TTL_BANNERS: '600000',
        CACHE_TTL_STATS: '60000',
        MAX_PAGE_SIZE: '50'
      }
    }
  },

  // 数据库配置
  database: {
    // 集合权限配置
    permissions: {
      categories: {
        read: true,
        write: false // 只允许云函数写入
      },
      emojis: {
        read: true,
        write: false
      },
      banners: {
        read: true,
        write: false
      },
      sync_notifications: {
        read: false,
        write: false // 完全禁止客户端访问
      },
      admin_logs: {
        read: false,
        write: false
      }
    },
    
    // 索引配置
    indexes: {
      categories: [
        { fields: [{ field: 'status', order: 'asc' }, { field: 'sort', order: 'asc' }] },
        { fields: [{ field: 'id', order: 'asc' }] }
      ],
      emojis: [
        { fields: [{ field: 'status', order: 'asc' }, { field: 'createTime', order: 'desc' }] },
        { fields: [{ field: 'categoryId', order: 'asc' }, { field: 'createTime', order: 'desc' }] },
        { fields: [{ field: 'tags', order: 'asc' }] },
        { fields: [{ field: 'id', order: 'asc' }] }
      ],
      banners: [
        { fields: [{ field: 'status', order: 'asc' }, { field: 'sort', order: 'asc' }] },
        { fields: [{ field: 'id', order: 'asc' }] }
      ],
      sync_notifications: [
        { fields: [{ field: 'timestamp', order: 'desc' }] },
        { fields: [{ field: 'dataType', order: 'asc' }, { field: 'timestamp', order: 'desc' }] },
        { fields: [{ field: 'processed', order: 'asc' }] }
      ]
    }
  },

  // 安全规则
  security: {
    // 访问频率限制
    rateLimit: {
      dataAPI: {
        windowMs: 60000, // 1分钟
        maxRequests: 100 // 每分钟最多100次请求
      },
      webAdminAPI: {
        windowMs: 60000,
        maxRequests: 50
      },
      loginAPI: {
        windowMs: 300000, // 5分钟
        maxRequests: 10 // 防止暴力破解
      }
    },

    // IP白名单（管理后台）
    adminWhitelist: [
      // '***********/24', // 内网IP段
      // '123.456.789.0'   // 特定IP
    ],

    // 数据验证规则
    validation: {
      category: {
        name: { required: true, maxLength: 50 },
        icon: { required: true, maxLength: 100 },
        description: { maxLength: 200 }
      },
      emoji: {
        title: { required: true, maxLength: 100 },
        imageUrl: { required: true, pattern: /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i },
        tags: { maxItems: 10, itemMaxLength: 20 }
      },
      banner: {
        title: { required: true, maxLength: 100 },
        imageUrl: { required: true, pattern: /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i },
        linkUrl: { pattern: /^https?:\/\/.+/ }
      }
    }
  },

  // 监控配置
  monitoring: {
    // 性能监控
    performance: {
      enabled: true,
      slowQueryThreshold: 1000, // 慢查询阈值（毫秒）
      memoryWarningThreshold: 80 // 内存使用警告阈值（百分比）
    },

    // 错误监控
    errorTracking: {
      enabled: true,
      sampleRate: 1.0, // 错误采样率
      ignoreErrors: [
        'Network Error',
        'Request timeout'
      ]
    },

    // 业务监控
    business: {
      enabled: true,
      metrics: [
        'user_login_count',
        'data_sync_count',
        'api_call_count',
        'cache_hit_rate'
      ]
    }
  },

  // 日志配置
  logging: {
    level: 'info', // debug, info, warn, error
    retention: 30, // 日志保留天数
    
    // 敏感信息过滤
    sensitiveFields: [
      'password',
      'token',
      'secret',
      'key'
    ]
  },

  // 缓存配置
  cache: {
    // Redis配置（如果使用）
    redis: {
      enabled: false,
      host: '',
      port: 6379,
      password: '',
      db: 0
    },

    // 内存缓存配置
    memory: {
      maxSize: 100, // 最大缓存项数
      ttl: {
        default: 300000, // 5分钟
        categories: 300000,
        emojis: 180000,
        banners: 600000,
        stats: 60000
      }
    }
  },

  // CDN配置
  cdn: {
    enabled: false,
    domain: '',
    
    // 静态资源配置
    staticResources: {
      images: {
        domain: '',
        path: '/images/',
        maxSize: 5242880, // 5MB
        allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp']
      }
    }
  },

  // 备份配置
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // 每天凌晨2点
    retention: 7, // 保留7天
    
    collections: [
      'categories',
      'emojis',
      'banners',
      'admin_logs'
    ]
  }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PRODUCTION_CONFIG;
} else if (typeof window !== 'undefined') {
  window.PRODUCTION_CONFIG = PRODUCTION_CONFIG;
}
