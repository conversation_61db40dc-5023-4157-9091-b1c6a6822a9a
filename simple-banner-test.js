// 简单的横幅测试
const { chromium } = require('playwright');

async function simpleBannerTest() {
    console.log('🎯 简单横幅测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('从弹窗获取横幅数据') || text.includes('横幅标题') || text.includes('ERROR')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 进入横幅管理');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            await page.waitForTimeout(3000);
        }
        
        console.log('📍 检查当前横幅数据');
        const currentBanners = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#banner-tbody tr'));
            return rows.map(row => {
                const cells = Array.from(row.querySelectorAll('td'));
                return cells[2] ? cells[2].textContent?.trim() : 'N/A';
            });
        });
        
        console.log('当前横幅标题:', currentBanners);
        
        // 截图
        await page.screenshot({ path: 'simple-banner-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: simple-banner-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开5秒供查看...');
        await page.waitForTimeout(5000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
simpleBannerTest().catch(console.error);
