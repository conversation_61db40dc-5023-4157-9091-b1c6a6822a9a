// 小程序页面功能综合测试
const { chromium } = require('playwright');

async function testMiniprogramPagesComprehensive() {
    console.log('📱 小程序页面功能综合测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('页面') || text.includes('数据') || text.includes('加载') || text.includes('错误')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        console.log('\n📍 第一步：分析小程序页面结构和功能');
        
        // 小程序页面结构分析
        const pageStructure = {
            mainPages: [
                { path: 'pages/index/index', name: '首页', tabBar: true, description: '展示横幅、分类、热门表情包' },
                { path: 'pages/search/search', name: '搜索页', tabBar: true, description: '搜索表情包功能' },
                { path: 'pages/category/category', name: '分类页', tabBar: true, description: '展示所有分类' },
                { path: 'pages/profile/profile', name: '我的页面', tabBar: true, description: '用户个人中心' }
            ],
            subPages: [
                { path: 'pages/detail/detail', name: '详情页', description: '表情包详情展示' },
                { path: 'pages/my-likes/my-likes', name: '我的点赞', description: '用户点赞的表情包' },
                { path: 'pages/my-collections/my-collections', name: '我的收藏', description: '用户收藏的表情包' },
                { path: 'pages/collection/collection', name: '收藏页', description: '收藏功能页面' },
                { path: 'pages/category-detail/category-detail', name: '分类详情', description: '特定分类的表情包列表' },
                { path: 'pages/download-history/download-history', name: '下载历史', description: '用户下载历史记录' },
                { path: 'pages/settings/settings', name: '设置页', description: '应用设置' }
            ],
            testPages: [
                { path: 'pages/test/test', name: '测试页', description: '开发测试页面' },
                { path: 'pages/debug/debug', name: '调试页', description: '调试功能页面' },
                { path: 'pages/offline-test/offline-test', name: '离线测试', description: '离线功能测试' },
                { path: 'pages/fix-sync/fix-sync', name: '同步修复', description: '数据同步修复页面' }
            ]
        };
        
        console.log('📊 小程序页面结构分析:');
        console.log(`主要页面 (TabBar): ${pageStructure.mainPages.length} 个`);
        console.log(`子页面: ${pageStructure.subPages.length} 个`);
        console.log(`测试页面: ${pageStructure.testPages.length} 个`);
        console.log(`总页面数: ${pageStructure.mainPages.length + pageStructure.subPages.length + pageStructure.testPages.length} 个`);
        
        console.log('\n📋 主要页面详情:');
        pageStructure.mainPages.forEach((page, index) => {
            console.log(`  ${index + 1}. ${page.name} (${page.path})`);
            console.log(`     功能: ${page.description}`);
        });
        
        console.log('\n📍 第二步：测试小程序数据获取逻辑');
        
        // 由于无法直接运行小程序UI，我们通过管理后台模拟小程序的数据获取逻辑
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        // 模拟小程序首页数据获取
        const indexPageDataTest = await page.evaluate(async () => {
            try {
                console.log('🔄 测试首页数据获取逻辑...');
                
                // 模拟小程序首页的数据获取逻辑
                // 1. 获取横幅数据
                const bannersResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                // 2. 获取分类数据
                const categoriesResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                // 3. 获取热门表情包数据
                const hotEmojisResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 10 }
                    }
                });
                
                return {
                    success: true,
                    banners: {
                        success: bannersResult.result?.success || false,
                        count: bannersResult.result?.data?.length || 0,
                        data: bannersResult.result?.data || []
                    },
                    categories: {
                        success: categoriesResult.result?.success || false,
                        count: categoriesResult.result?.data?.length || 0,
                        data: categoriesResult.result?.data || []
                    },
                    hotEmojis: {
                        success: hotEmojisResult.result?.success || false,
                        count: hotEmojisResult.result?.data?.length || 0,
                        data: hotEmojisResult.result?.data || []
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 首页数据获取测试结果:');
        if (indexPageDataTest.success) {
            console.log(`横幅数据: ${indexPageDataTest.banners.success ? '✅ 成功' : '🔴 失败'} (${indexPageDataTest.banners.count}条)`);
            console.log(`分类数据: ${indexPageDataTest.categories.success ? '✅ 成功' : '🔴 失败'} (${indexPageDataTest.categories.count}条)`);
            console.log(`热门表情包: ${indexPageDataTest.hotEmojis.success ? '✅ 成功' : '🔴 失败'} (${indexPageDataTest.hotEmojis.count}条)`);
            
            // 详细分析数据质量
            if (indexPageDataTest.banners.success && indexPageDataTest.banners.count > 0) {
                console.log('\n📋 横幅数据详情:');
                indexPageDataTest.banners.data.forEach((banner, index) => {
                    console.log(`  ${index + 1}. ${banner.title}`);
                    console.log(`     状态: ${banner.status}, 优先级: ${banner.priority || 0}`);
                    console.log(`     有图片: ${!!banner.imageUrl}`);
                });
            }
            
            if (indexPageDataTest.categories.success && indexPageDataTest.categories.count > 0) {
                console.log('\n📋 分类数据详情:');
                indexPageDataTest.categories.data.forEach((category, index) => {
                    console.log(`  ${index + 1}. ${category.name}`);
                    console.log(`     状态: ${category.status}, 表情包数量: ${category.emojiCount || 0}`);
                    console.log(`     有渐变: ${!!category.gradient}`);
                });
            }
            
            if (indexPageDataTest.hotEmojis.success && indexPageDataTest.hotEmojis.count > 0) {
                console.log('\n📋 热门表情包详情:');
                indexPageDataTest.hotEmojis.data.forEach((emoji, index) => {
                    console.log(`  ${index + 1}. ${emoji.title}`);
                    console.log(`     状态: ${emoji.status}, 分类: ${emoji.categoryId}`);
                    console.log(`     有图片: ${!!emoji.imageUrl}`);
                });
            }
        } else {
            console.log(`❌ 首页数据获取失败: ${indexPageDataTest.error}`);
        }
        
        console.log('\n📍 第三步：测试分类页面数据获取逻辑');
        
        // 模拟分类页面数据获取
        const categoryPageDataTest = await page.evaluate(async () => {
            try {
                console.log('🔄 测试分类页面数据获取逻辑...');
                
                // 1. 获取所有分类
                const categoriesResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                if (!categoriesResult.result?.success || !categoriesResult.result?.data?.length) {
                    return {
                        success: false,
                        error: '没有分类数据'
                    };
                }
                
                // 2. 获取每个分类下的表情包数量（模拟分类页面的统计功能）
                const categoryStats = [];
                for (const category of categoriesResult.result.data) {
                    const emojisResult = await window.tcbApp.callFunction({
                        name: 'dataAPI',
                        data: { 
                            action: 'getEmojis',
                            data: { category: category._id, page: 1, limit: 1 }
                        }
                    });
                    
                    categoryStats.push({
                        id: category._id,
                        name: category.name,
                        emojiCount: emojisResult.result?.pagination?.total || 0,
                        hasEmojis: (emojisResult.result?.pagination?.total || 0) > 0
                    });
                }
                
                return {
                    success: true,
                    totalCategories: categoriesResult.result.data.length,
                    categoryStats: categoryStats
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 分类页面数据获取测试结果:');
        if (categoryPageDataTest.success) {
            console.log(`✅ 分类页面数据获取成功`);
            console.log(`总分类数: ${categoryPageDataTest.totalCategories}`);
            
            console.log('\n📋 分类统计详情:');
            categoryPageDataTest.categoryStats.forEach((stat, index) => {
                console.log(`  ${index + 1}. ${stat.name}`);
                console.log(`     表情包数量: ${stat.emojiCount}`);
                console.log(`     有内容: ${stat.hasEmojis ? '✅ 是' : '🔴 否'}`);
            });
            
            const categoriesWithContent = categoryPageDataTest.categoryStats.filter(stat => stat.hasEmojis).length;
            console.log(`\n📊 有内容的分类: ${categoriesWithContent}/${categoryPageDataTest.totalCategories}`);
        } else {
            console.log(`❌ 分类页面数据获取失败: ${categoryPageDataTest.error}`);
        }
        
        console.log('\n📍 第四步：测试搜索页面功能逻辑');
        
        // 模拟搜索页面功能
        const searchPageTest = await page.evaluate(async () => {
            try {
                console.log('🔄 测试搜索页面功能逻辑...');
                
                // 1. 测试空搜索（获取所有表情包）
                const allEmojisResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 20 }
                    }
                });
                
                // 2. 模拟关键词搜索（由于云函数可能不支持搜索，我们测试数据结构）
                const searchableEmojis = allEmojisResult.result?.data || [];
                const searchResults = searchableEmojis.filter(emoji => 
                    emoji.title && emoji.title.includes('测试')
                );
                
                return {
                    success: true,
                    totalEmojis: searchableEmojis.length,
                    searchResults: searchResults.length,
                    sampleEmojis: searchableEmojis.slice(0, 3).map(emoji => ({
                        title: emoji.title,
                        hasTitle: !!emoji.title,
                        hasDescription: !!emoji.description,
                        hasImage: !!emoji.imageUrl,
                        categoryId: emoji.categoryId
                    }))
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 搜索页面功能测试结果:');
        if (searchPageTest.success) {
            console.log(`✅ 搜索页面功能测试成功`);
            console.log(`可搜索表情包总数: ${searchPageTest.totalEmojis}`);
            console.log(`包含"测试"关键词的表情包: ${searchPageTest.searchResults}`);
            
            console.log('\n📋 表情包数据样本:');
            searchPageTest.sampleEmojis.forEach((emoji, index) => {
                console.log(`  ${index + 1}. ${emoji.title}`);
                console.log(`     有标题: ${emoji.hasTitle ? '✅' : '🔴'}`);
                console.log(`     有描述: ${emoji.hasDescription ? '✅' : '🔴'}`);
                console.log(`     有图片: ${emoji.hasImage ? '✅' : '🔴'}`);
                console.log(`     分类ID: ${emoji.categoryId || 'N/A'}`);
            });
        } else {
            console.log(`❌ 搜索页面功能测试失败: ${searchPageTest.error}`);
        }
        
        console.log('\n📍 第五步：测试详情页面数据结构');
        
        // 模拟详情页面数据获取
        const detailPageTest = await page.evaluate(async () => {
            try {
                console.log('🔄 测试详情页面数据结构...');
                
                // 获取一个表情包作为详情页测试数据
                const emojisResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 1 }
                    }
                });
                
                if (!emojisResult.result?.success || !emojisResult.result?.data?.length) {
                    return {
                        success: false,
                        error: '没有表情包数据'
                    };
                }
                
                const emoji = emojisResult.result.data[0];
                
                // 分析详情页需要的数据字段
                const detailData = {
                    id: emoji._id,
                    title: emoji.title,
                    description: emoji.description,
                    imageUrl: emoji.imageUrl,
                    categoryId: emoji.categoryId,
                    likes: emoji.likes || 0,
                    collections: emoji.collections || 0,
                    downloads: emoji.downloads || 0,
                    views: emoji.views || 0,
                    tags: emoji.tags || [],
                    createTime: emoji.createTime,
                    fileSize: emoji.fileSize,
                    fileType: emoji.fileType
                };
                
                // 检查数据完整性
                const dataCompleteness = {
                    hasBasicInfo: !!(detailData.title && detailData.imageUrl),
                    hasStats: !!(detailData.likes >= 0 && detailData.collections >= 0),
                    hasMetadata: !!(detailData.createTime && detailData.fileType),
                    hasCategory: !!detailData.categoryId
                };
                
                return {
                    success: true,
                    detailData: detailData,
                    dataCompleteness: dataCompleteness
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 详情页面数据结构测试结果:');
        if (detailPageTest.success) {
            console.log(`✅ 详情页面数据结构测试成功`);
            
            const data = detailPageTest.detailData;
            const completeness = detailPageTest.dataCompleteness;
            
            console.log('\n📋 详情页数据字段:');
            console.log(`  标题: ${data.title || 'N/A'}`);
            console.log(`  描述: ${data.description || 'N/A'}`);
            console.log(`  图片URL: ${data.imageUrl ? '✅ 有' : '🔴 无'}`);
            console.log(`  分类ID: ${data.categoryId || 'N/A'}`);
            console.log(`  点赞数: ${data.likes}`);
            console.log(`  收藏数: ${data.collections}`);
            console.log(`  下载数: ${data.downloads}`);
            console.log(`  浏览数: ${data.views}`);
            console.log(`  标签: ${data.tags.length > 0 ? data.tags.join(', ') : '无'}`);
            console.log(`  文件大小: ${data.fileSize || 'N/A'}`);
            console.log(`  文件类型: ${data.fileType || 'N/A'}`);
            
            console.log('\n📊 数据完整性检查:');
            console.log(`  基础信息: ${completeness.hasBasicInfo ? '✅ 完整' : '🔴 缺失'}`);
            console.log(`  统计数据: ${completeness.hasStats ? '✅ 完整' : '🔴 缺失'}`);
            console.log(`  元数据: ${completeness.hasMetadata ? '✅ 完整' : '🔴 缺失'}`);
            console.log(`  分类关联: ${completeness.hasCategory ? '✅ 完整' : '🔴 缺失'}`);
        } else {
            console.log(`❌ 详情页面数据结构测试失败: ${detailPageTest.error}`);
        }
        
        console.log('\n📍 第六步：测试用户相关页面数据需求');
        
        // 分析用户相关页面的数据需求
        const userPagesAnalysis = {
            'my-likes': {
                name: '我的点赞',
                dataNeeds: ['用户点赞的表情包列表', '点赞时间', '表情包基本信息'],
                apiNeeds: ['getUserLikes', 'updateLikeStatus']
            },
            'my-collections': {
                name: '我的收藏',
                dataNeeds: ['用户收藏的表情包列表', '收藏时间', '表情包基本信息'],
                apiNeeds: ['getUserCollections', 'updateCollectionStatus']
            },
            'download-history': {
                name: '下载历史',
                dataNeeds: ['用户下载历史记录', '下载时间', '表情包信息'],
                apiNeeds: ['getUserDownloads', 'addDownloadRecord']
            },
            'settings': {
                name: '设置页',
                dataNeeds: ['用户设置信息', '应用配置'],
                apiNeeds: ['getUserSettings', 'updateUserSettings']
            }
        };
        
        console.log('📊 用户相关页面数据需求分析:');
        Object.entries(userPagesAnalysis).forEach(([key, page]) => {
            console.log(`\n📋 ${page.name}:`);
            console.log(`  数据需求: ${page.dataNeeds.join(', ')}`);
            console.log(`  API需求: ${page.apiNeeds.join(', ')}`);
        });
        
        console.log('\n🎯 小程序页面功能测试总结:');
        
        const testResults = {
            indexPage: indexPageDataTest.success,
            categoryPage: categoryPageDataTest.success,
            searchPage: searchPageTest.success,
            detailPage: detailPageTest.success
        };
        
        const successCount = Object.values(testResults).filter(result => result).length;
        const totalTests = Object.keys(testResults).length;
        
        console.log(`页面功能测试结果: ${successCount}/${totalTests} 通过`);
        console.log(`首页数据获取: ${testResults.indexPage ? '✅ 通过' : '🔴 失败'}`);
        console.log(`分类页面功能: ${testResults.categoryPage ? '✅ 通过' : '🔴 失败'}`);
        console.log(`搜索页面功能: ${testResults.searchPage ? '✅ 通过' : '🔴 失败'}`);
        console.log(`详情页面数据: ${testResults.detailPage ? '✅ 通过' : '🔴 失败'}`);
        
        const overallSuccess = successCount === totalTests;
        
        console.log(`\n🎯 总体测试结果: ${overallSuccess ? '🎉 全部通过' : '⚠️ 部分通过'}`);
        
        if (overallSuccess) {
            console.log('✅ 小程序各页面的数据获取逻辑和功能需求都能正常满足！');
        } else {
            console.log('⚠️ 部分页面功能存在问题，需要进一步修复。');
        }
        
        return {
            success: true,
            overallSuccess: overallSuccess,
            testResults: testResults,
            pageStructure: pageStructure,
            indexPageData: indexPageDataTest,
            categoryPageData: categoryPageDataTest,
            searchPageData: searchPageTest,
            detailPageData: detailPageTest,
            userPagesAnalysis: userPagesAnalysis
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'miniprogram-pages-comprehensive-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'miniprogram-pages-comprehensive.png', fullPage: true });
        console.log('\n📸 测试截图已保存: miniprogram-pages-comprehensive.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testMiniprogramPagesComprehensive().then(result => {
    console.log('\n🎯 小程序页面功能综合测试最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.overallSuccess) {
        console.log('🎉 小程序页面功能测试全部通过！');
    } else if (result.success) {
        console.log('⚠️ 小程序页面功能测试部分通过，需要关注问题点。');
    } else {
        console.log('❌ 小程序页面功能测试失败。');
    }
}).catch(console.error);
