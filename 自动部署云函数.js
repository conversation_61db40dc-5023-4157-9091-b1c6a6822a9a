/**
 * 自动化云函数部署脚本
 * 用于批量检查和准备云函数部署
 */

const fs = require('fs')
const path = require('path')

class CloudFunctionDeployer {
  constructor() {
    this.cloudFunctionsDir = 'cloudfunctions'
    this.envId = 'cloud1-5g6pvnpl88dc0142'
    
    // 云函数列表（按优先级排序）
    this.functions = [
      // 第一批：核心基础函数
      { name: 'login', priority: 1, description: '用户登录' },
      { name: 'getOpenID', priority: 1, description: '获取用户ID' },
      { name: 'initDatabase', priority: 1, description: '初始化数据库' },
      { name: 'systemConfig', priority: 1, description: '系统配置' },
      
      // 第二批：数据管理函数
      { name: 'dataAPI', priority: 2, description: '数据接口' },
      { name: 'getCategories', priority: 2, description: '获取分类' },
      { name: 'getEmojiList', priority: 2, description: '获取表情列表' },
      { name: 'getEmojiDetail', priority: 2, description: '获取表情详情' },
      { name: 'getBanners', priority: 2, description: '获取轮播图' },
      { name: 'searchEmojis', priority: 2, description: '搜索表情' },
      
      // 第三批：用户交互函数
      { name: 'toggleLike', priority: 3, description: '切换点赞' },
      { name: 'toggleCollect', priority: 3, description: '切换收藏' },
      { name: 'getUserLikes', priority: 3, description: '获取用户点赞' },
      { name: 'getUserCollections', priority: 3, description: '获取用户收藏' },
      { name: 'getUserStats', priority: 3, description: '获取用户统计' },
      { name: 'updateUserStats', priority: 3, description: '更新用户统计' },
      
      // 第四批：管理后台函数
      { name: 'admin', priority: 4, description: '管理后台API' },
      { name: 'adminAPI', priority: 4, description: '管理后台接口' },
      { name: 'webAdminAPI', priority: 4, description: 'Web管理API' },
      
      // 第五批：数据同步和工具函数
      { name: 'dataSync', priority: 5, description: '数据同步' },
      { name: 'syncAPI', priority: 5, description: '同步接口' },
      { name: 'uploadFile', priority: 5, description: '文件上传' },
      { name: 'trackAction', priority: 5, description: '行为追踪' },
      { name: 'initEmojiData', priority: 5, description: '初始化表情数据' },
      { name: 'testAPI', priority: 5, description: '测试接口' }
    ]
  }

  /**
   * 检查云函数目录结构
   */
  checkFunctionStructure() {
    console.log('🔍 检查云函数目录结构...\n')
    
    const results = {
      total: this.functions.length,
      existing: 0,
      missing: [],
      invalid: [],
      ready: []
    }

    this.functions.forEach(func => {
      const funcPath = path.join(this.cloudFunctionsDir, func.name)
      const indexPath = path.join(funcPath, 'index.js')
      const packagePath = path.join(funcPath, 'package.json')
      const configPath = path.join(funcPath, 'config.json')

      console.log(`📦 检查 ${func.name} (${func.description})`)

      if (!fs.existsSync(funcPath)) {
        console.log(`   ❌ 目录不存在: ${funcPath}`)
        results.missing.push(func.name)
        return
      }

      if (!fs.existsSync(indexPath)) {
        console.log(`   ❌ 缺少入口文件: index.js`)
        results.invalid.push(func.name)
        return
      }

      if (!fs.existsSync(packagePath)) {
        console.log(`   ❌ 缺少配置文件: package.json`)
        results.invalid.push(func.name)
        return
      }

      // 检查package.json内容
      try {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
        console.log(`   ✅ 配置正常 - 依赖数量: ${Object.keys(packageJson.dependencies || {}).length}`)
      } catch (error) {
        console.log(`   ⚠️  package.json格式错误`)
      }

      // 检查config.json
      if (fs.existsSync(configPath)) {
        console.log(`   ✅ 云函数配置文件存在`)
      }

      results.existing++
      results.ready.push(func.name)
      console.log(`   ✅ 准备就绪\n`)
    })

    return results
  }

  /**
   * 生成部署报告
   */
  generateDeployReport(checkResults) {
    console.log('📊 部署准备报告')
    console.log('=' .repeat(50))
    console.log(`总计云函数: ${checkResults.total}`)
    console.log(`准备就绪: ${checkResults.ready.length}`)
    console.log(`目录缺失: ${checkResults.missing.length}`)
    console.log(`配置错误: ${checkResults.invalid.length}`)
    console.log('=' .repeat(50))

    if (checkResults.missing.length > 0) {
      console.log('\n❌ 缺失的云函数:')
      checkResults.missing.forEach(name => {
        console.log(`   - ${name}`)
      })
    }

    if (checkResults.invalid.length > 0) {
      console.log('\n⚠️  配置错误的云函数:')
      checkResults.invalid.forEach(name => {
        console.log(`   - ${name}`)
      })
    }

    if (checkResults.ready.length > 0) {
      console.log('\n✅ 准备就绪的云函数:')
      checkResults.ready.forEach(name => {
        const func = this.functions.find(f => f.name === name)
        console.log(`   - ${name} (${func.description})`)
      })
    }

    console.log('\n')
  }

  /**
   * 生成部署指令
   */
  generateDeployInstructions(checkResults) {
    console.log('🚀 部署指令生成')
    console.log('=' .repeat(50))
    
    if (checkResults.ready.length === 0) {
      console.log('❌ 没有可部署的云函数，请先检查项目结构')
      return
    }

    console.log('请在微信开发者工具中按以下顺序部署云函数：\n')

    // 按优先级分组
    const groupedFunctions = {}
    checkResults.ready.forEach(name => {
      const func = this.functions.find(f => f.name === name)
      if (!groupedFunctions[func.priority]) {
        groupedFunctions[func.priority] = []
      }
      groupedFunctions[func.priority].push(func)
    })

    Object.keys(groupedFunctions).sort().forEach(priority => {
      console.log(`📋 第${priority}批 - 优先级${priority}:`)
      groupedFunctions[priority].forEach((func, index) => {
        console.log(`   ${index + 1}. ${func.name} - ${func.description}`)
        console.log(`      路径: cloudfunctions/${func.name}/`)
        console.log(`      操作: 右键 → 上传并部署：云端安装依赖`)
        console.log(`      状态: [ ] 完成\n`)
      })
      console.log('')
    })

    console.log('⚠️  部署注意事项:')
    console.log('   1. 请确保网络连接稳定')
    console.log('   2. 每个函数部署完成后检查状态')
    console.log('   3. 如果部署失败，请重试')
    console.log('   4. 建议按批次顺序部署，确保依赖关系正确')
  }

  /**
   * 创建部署检查清单
   */
  createDeployChecklist(checkResults) {
    const checklist = `# 🚀 云函数部署检查清单

## 📊 部署统计
- 总计: ${checkResults.total}个云函数
- 准备就绪: ${checkResults.ready.length}个
- 需要修复: ${checkResults.missing.length + checkResults.invalid.length}个

## 📋 部署进度追踪

${checkResults.ready.map((name, index) => {
  const func = this.functions.find(f => f.name === name)
  return `### ${index + 1}. ${func.name} - ${func.description}
- [ ] 部署完成
- [ ] 状态检查
- [ ] 功能测试
`
}).join('\n')}

## ✅ 部署完成确认
- [ ] 所有云函数部署成功
- [ ] 云开发控制台状态正常
- [ ] 测试调用成功

---
生成时间: ${new Date().toLocaleString()}
云环境ID: ${this.envId}
`

    fs.writeFileSync('云函数部署检查清单.md', checklist, 'utf8')
    console.log('📝 已生成部署检查清单: 云函数部署检查清单.md')
  }

  /**
   * 执行完整检查
   */
  run() {
    console.log('🎯 表情包小程序 - 云函数部署准备工具')
    console.log('=' .repeat(60))
    console.log(`云环境ID: ${this.envId}`)
    console.log(`云函数目录: ${this.cloudFunctionsDir}`)
    console.log(`待部署函数: ${this.functions.length}个\n`)

    // 检查目录结构
    const checkResults = this.checkFunctionStructure()

    // 生成报告
    this.generateDeployReport(checkResults)

    // 生成部署指令
    this.generateDeployInstructions(checkResults)

    // 创建检查清单
    this.createDeployChecklist(checkResults)

    console.log('\n🎉 准备工作完成！')
    console.log('📖 请按照上述指令在微信开发者工具中部署云函数')
    console.log('📝 使用生成的检查清单跟踪部署进度')
  }
}

// 运行部署准备工具
if (require.main === module) {
  const deployer = new CloudFunctionDeployer()
  deployer.run()
}

module.exports = CloudFunctionDeployer
