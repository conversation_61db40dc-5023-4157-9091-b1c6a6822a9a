<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 修复版数据库测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复版数据库测试</h1>
        <p>基于问题分析的完全修复版本</p>
        
        <div class="info-box">
            <h3>📋 基础信息</h3>
            <p><strong>环境ID:</strong> cloud1-5g6pvnpl88dc0142</p>
            <p><strong>修复内容:</strong> SDK版本兼容性 + 数据库初始化顺序</p>
        </div>
        
        <button onclick="runFixedTest()">🚀 运行修复版测试</button>
        <button onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <!-- 使用确认可用的SDK版本 -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let tcbApp = null;
        let db = null;
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runFixedTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status info">🚀 开始修复版测试...</div>';
            
            try {
                // 步骤1: 确保SDK加载
                log('=== 步骤1: SDK检查 ===', 'info');
                
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }
                
                log(`✅ SDK已加载，版本: ${window.cloudbase.version || '未知'}`, 'success');
                
                // 步骤2: 初始化应用
                log('=== 步骤2: 初始化应用 ===', 'info');
                
                // 使用项目完整配置进行初始化
                const initConfig = {
                    env: ENV_ID,
                    region: 'ap-shanghai',
                    timeout: 10000,      // 10秒超时
                    traceUser: true      // 启用用户追踪
                };

                log(`初始化配置: ${JSON.stringify(initConfig)}`, 'info');

                tcbApp = window.cloudbase.init(initConfig);

                // 验证初始化结果
                if (!tcbApp) {
                    throw new Error('云开发应用初始化失败');
                }

                log(`应用对象类型: ${typeof tcbApp}`, 'info');
                log(`应用对象属性: ${Object.keys(tcbApp).join(', ')}`, 'info');
                
                log('✅ 应用初始化成功', 'success');
                
                // 步骤3: 认证处理 (修复版)
                log('=== 步骤3: 身份认证 ===', 'info');
                
                const auth = tcbApp.auth();
                log(`Auth对象类型: ${typeof auth}`, 'info');
                
                // 检查当前登录状态
                const currentUser = auth.currentUser;
                log(`当前用户: ${currentUser ? '已登录' : '未登录'}`, 'info');
                
                // 尝试多种登录方式
                let loginSuccess = false;
                
                // 方式1: 检查是否已有登录状态
                const loginState = auth.hasLoginState();
                if (loginState) {
                    log('✅ 发现现有登录状态', 'success');
                    loginSuccess = true;
                } else {
                    // 方式2: 尝试匿名登录
                    if (typeof auth.anonymousAuthProvider === 'function') {
                        try {
                            log('尝试匿名登录...', 'info');
                            await auth.anonymousAuthProvider().signIn();
                            log('✅ 匿名登录成功', 'success');
                            loginSuccess = true;
                        } catch (authError) {
                            log(`匿名登录失败: ${authError.message}`, 'warning');
                        }
                    }
                    
                    // 方式3: 如果匿名登录失败，尝试其他方式
                    if (!loginSuccess) {
                        log('⚠️ 跳过登录，直接测试数据库访问', 'warning');
                    }
                }
                
                // 步骤4: 数据库测试 (修复版)
                log('=== 步骤4: 数据库测试 ===', 'info');
                
                // 确保数据库对象正确初始化
                db = tcbApp.database();
                log('✅ 数据库对象创建成功', 'success');
                
                // 测试基本查询 (终极诊断版)
                log('=== 开始终极数据库诊断 ===', 'info');

                // 诊断1: 检查数据库对象
                log(`数据库对象类型: ${typeof db}`, 'info');
                log(`数据库对象: ${db ? '存在' : '不存在'}`, 'info');

                if (!db) {
                    throw new Error('数据库对象未正确初始化');
                }

                // 诊断2: 检查collection方法
                log(`collection方法: ${typeof db.collection}`, 'info');

                // 诊断3: 尝试获取集合对象
                let collection;
                try {
                    collection = db.collection('emojis');
                    log(`✅ 集合对象创建成功: ${typeof collection}`, 'success');
                } catch (collError) {
                    log(`❌ 集合对象创建失败: ${collError.message}`, 'error');
                    throw collError;
                }

                // 诊断4: 检查查询方法
                log(`limit方法: ${typeof collection.limit}`, 'info');
                log(`get方法: ${typeof collection.get}`, 'info');

                // 诊断5: 执行查询并详细记录 (增强网络诊断)
                try {
                    log('正在执行数据库查询...', 'info');

                    // 检查网络连接
                    log('=== 网络连接诊断 ===', 'info');

                    // 1. 检查云开发环境连接
                    log(`环境ID: ${ENV_ID}`, 'info');
                    log(`tcbApp对象: ${typeof tcbApp}`, 'info');

                    // 2. 尝试直接调用云开发API
                    const queryPromise = collection.limit(1).get();
                    log(`查询Promise: ${typeof queryPromise}`, 'info');
                    log(`Promise状态检查: ${queryPromise instanceof Promise ? 'Promise对象' : '非Promise对象'}`, 'info');

                    // 3. 设置超时检测
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('查询超时 (10秒)')), 10000);
                    });

                    log('开始查询，设置10秒超时...', 'info');
                    const result = await Promise.race([queryPromise, timeoutPromise]);

                    // 详细分析查询结果
                    log(`查询结果类型: ${typeof result}`, 'info');
                    log(`查询结果: ${result ? '存在' : '不存在'}`, 'info');

                    if (result) {
                        log(`结果属性: ${Object.keys(result).join(', ')}`, 'info');

                        if (result.data) {
                            log(`✅ 数据库查询成功! 找到 ${result.data.length} 条记录`, 'success');

                            if (result.data.length > 0) {
                                log(`示例数据: ${JSON.stringify(result.data[0], null, 2)}`, 'info');
                            } else {
                                log('⚠️ 集合为空，尝试创建测试数据...', 'warning');
                                await createTestData(db);
                            }
                        } else {
                            log(`❌ 查询结果没有data属性`, 'error');
                        }
                    } else {
                        log(`❌ 查询返回null/undefined`, 'error');
                        throw new Error('数据库查询返回空结果');
                    }

                } catch (dbError) {
                    log(`❌ 数据库查询异常: ${dbError.message}`, 'error');
                    log(`错误类型: ${dbError.constructor.name}`, 'error');
                    log(`错误代码: ${dbError.code || '无'}`, 'error');
                    log(`错误堆栈: ${dbError.stack || '无'}`, 'error');

                    // 详细错误分析
                    if (dbError.message === 'undefined') {
                        log('🔍 检测到undefined错误，这通常表示:', 'warning');
                        log('1. 网络请求失败或被阻止', 'warning');
                        log('2. 云开发环境配置错误', 'warning');
                        log('3. SDK版本与云开发服务不兼容', 'warning');

                        // 尝试网络连通性测试
                        await testNetworkConnectivity();
                    }

                    // 尝试权限诊断
                    await diagnosePermissions(db, auth);
                }
                
                // 步骤5: 总结
                log('=== 测试完成 ===', 'info');
                log('✅ 修复版测试执行完毕', 'success');
                
            } catch (error) {
                log(`❌ 测试过程中出现错误: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }

        // 网络连通性测试
        async function testNetworkConnectivity() {
            log('=== 网络连通性测试 ===', 'info');

            try {
                // 测试1: 基本网络连接
                const response = await fetch('https://www.baidu.com', {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                log('✅ 基本网络连接正常', 'success');

                // 测试2: 云开发API端点
                const tcbEndpoint = 'https://tcb-api.tencentcloudapi.com';
                try {
                    const tcbResponse = await fetch(tcbEndpoint, {
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    log('✅ 云开发API端点可访问', 'success');
                } catch (tcbError) {
                    log(`❌ 云开发API端点不可访问: ${tcbError.message}`, 'error');
                }

                // 测试3: 检查浏览器控制台网络请求
                log('💡 请检查浏览器开发者工具的Network标签页', 'info');
                log('💡 查看是否有失败的网络请求', 'info');

            } catch (networkError) {
                log(`❌ 网络连接测试失败: ${networkError.message}`, 'error');
            }
        }

        // 创建测试数据
        async function createTestData(db) {
            try {
                log('🔧 尝试创建测试数据...', 'info');

                const testData = {
                    name: '测试表情包',
                    url: 'https://example.com/test.gif',
                    tags: ['测试'],
                    category: '测试分类',
                    uploadTime: new Date(),
                    createdBy: 'test-user'
                };

                const addResult = await db.collection('emojis').add(testData);
                log(`✅ 测试数据创建成功! ID: ${addResult.id}`, 'success');

                // 验证创建的数据
                const verifyResult = await db.collection('emojis').doc(addResult.id).get();
                log(`✅ 数据验证成功: ${JSON.stringify(verifyResult.data)}`, 'success');

            } catch (addError) {
                log(`❌ 创建测试数据失败: ${addError.message}`, 'error');
                log(`错误代码: ${addError.code || '无'}`, 'error');
            }
        }

        // 权限诊断
        async function diagnosePermissions(db, auth) {
            log('=== 权限诊断开始 ===', 'info');

            try {
                // 检查当前用户信息
                const currentUser = auth.currentUser;
                log(`当前用户: ${currentUser ? JSON.stringify({
                    uid: currentUser.uid,
                    loginType: currentUser.loginType,
                    openid: currentUser.openid
                }) : '无'}`, 'info');

                // 检查登录状态
                const loginState = auth.hasLoginState();
                log(`登录状态: ${loginState ? '有效' : '无效'}`, 'info');

                if (loginState) {
                    log(`登录类型: ${loginState.loginType}`, 'info');
                    log(`是否匿名: ${loginState.isAnonymousAuth}`, 'info');
                }

                // 尝试不同的集合
                const collections = ['categories', 'banners', 'users'];
                for (const collName of collections) {
                    try {
                        const result = await db.collection(collName).limit(1).get();
                        log(`✅ ${collName} 集合可访问，记录数: ${result.data.length}`, 'success');
                    } catch (error) {
                        log(`❌ ${collName} 集合访问失败: ${error.message}`, 'error');
                    }
                }

            } catch (diagError) {
                log(`权限诊断失败: ${diagError.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', function() {
            log('📱 修复版测试页面加载完成', 'success');
            log('💡 点击"运行修复版测试"开始测试', 'info');
        });
    </script>
</body>
</html>
