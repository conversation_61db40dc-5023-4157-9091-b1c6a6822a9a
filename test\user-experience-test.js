/**
 * 用户体验增强测试脚本
 * 验证用户体验模块的功能
 */

// 模拟微信小程序环境
global.wx = {
  showLoading: (options) => {
    console.log(`📱 模拟显示加载: ${options.title}`)
  },
  hideLoading: () => {
    console.log('📱 模拟隐藏加载')
  },
  showToast: (options) => {
    console.log(`📱 模拟显示提示: ${options.title} (${options.icon})`)
  },
  hideToast: () => {
    console.log('📱 模拟隐藏提示')
  },
  showModal: (options) => {
    console.log(`📱 模拟显示对话框: ${options.title} - ${options.content}`)
    // 模拟用户点击确定
    setTimeout(() => {
      if (options.success) {
        options.success({ confirm: true, cancel: false })
      }
    }, 100)
  },
  showActionSheet: (options) => {
    console.log(`📱 模拟显示操作菜单:`, options.itemList)
    // 模拟用户选择第一项
    setTimeout(() => {
      if (options.success) {
        options.success({ tapIndex: 0 })
      }
    }, 100)
  },
  navigateTo: (options) => {
    console.log(`📱 模拟页面跳转: ${options.url}`)
    setTimeout(() => {
      if (options.success) options.success()
    }, 50)
  },
  openSetting: (options) => {
    console.log('📱 模拟打开设置页面')
    setTimeout(() => {
      if (options.success) options.success()
    }, 50)
  }
}

// 引入用户体验模块
const { UserExperience } = require('../utils/userExperience.js')

// 测试函数
async function testUserExperience() {
  console.log('🧪 开始测试用户体验增强模块...\n')

  try {
    // 测试1: 加载状态管理
    console.log('📋 测试1: 加载状态管理')
    UserExperience.showLoading('测试加载中...', { key: 'test1' })
    
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 100))
    
    UserExperience.hideLoading('test1')
    console.log('   ✅ 加载状态管理测试通过\n')

    // 测试2: 消息提示
    console.log('📋 测试2: 消息提示')
    UserExperience.showSuccess('成功消息测试')
    UserExperience.showWarning('警告消息测试')
    UserExperience.showError('错误消息测试')
    UserExperience.showInfo('信息消息测试')
    
    // 等待消息队列处理
    await new Promise(resolve => setTimeout(resolve, 200))
    console.log('   ✅ 消息提示测试通过\n')

    // 测试3: 确认对话框
    console.log('📋 测试3: 确认对话框')
    const confirmResult = await UserExperience.showConfirm({
      title: '测试确认',
      content: '这是一个测试确认对话框',
      confirmText: '确定',
      cancelText: '取消'
    })
    console.log('   确认结果:', confirmResult)
    console.log('   ✅ 确认对话框测试通过\n')

    // 测试4: 操作菜单
    console.log('📋 测试4: 操作菜单')
    const actionResult = await UserExperience.showActionSheet({
      itemList: ['选项1', '选项2', '选项3']
    })
    console.log('   选择结果:', actionResult)
    console.log('   ✅ 操作菜单测试通过\n')

    // 测试5: 重试错误提示
    console.log('📋 测试5: 重试错误提示')
    let retryCount = 0
    const retryCallback = (count) => {
      retryCount = count
      console.log(`   重试回调被调用，次数: ${count}`)
      return Promise.resolve(true)
    }
    
    const retryResult = await UserExperience.showRetryError(
      '这是一个测试错误',
      retryCallback,
      { maxRetries: 2 }
    )
    console.log('   重试结果:', retryResult)
    console.log('   ✅ 重试错误提示测试通过\n')

    // 测试6: 网络错误提示
    console.log('📋 测试6: 网络错误提示')
    const networkRetryCallback = () => {
      console.log('   网络重试回调被调用')
      return Promise.resolve(true)
    }
    
    const networkResult = await UserExperience.showNetworkError(networkRetryCallback)
    console.log('   网络错误处理结果:', networkResult)
    console.log('   ✅ 网络错误提示测试通过\n')

    // 测试7: 权限请求提示
    console.log('📋 测试7: 权限请求提示')
    const permissionCallback = () => {
      console.log('   权限设置回调被调用')
    }
    
    const permissionResult = await UserExperience.showPermissionRequest(
      'camera',
      permissionCallback
    )
    console.log('   权限请求结果:', permissionResult)
    console.log('   ✅ 权限请求提示测试通过\n')

    // 测试8: 功能介绍
    console.log('📋 测试8: 功能介绍')
    const introSteps = [
      '欢迎使用表情包小程序',
      '您可以浏览各种有趣的表情包',
      '点击表情包可以查看详情',
      '长按可以保存到相册'
    ]
    
    const introResult = await UserExperience.showFeatureIntro(
      '功能介绍',
      introSteps,
      { showSkip: true }
    )
    console.log('   功能介绍完成')
    console.log('   ✅ 功能介绍测试通过\n')

    // 测试9: 进度显示
    console.log('📋 测试9: 进度显示')
    for (let i = 0; i <= 100; i += 25) {
      UserExperience.showProgress('处理中...', i / 100)
      await new Promise(resolve => setTimeout(resolve, 50))
    }
    UserExperience.hideLoading()
    console.log('   ✅ 进度显示测试通过\n')

    // 测试10: 异步函数包装器
    console.log('📋 测试10: 异步函数包装器')
    const testAsyncFunction = async (shouldFail = false) => {
      await new Promise(resolve => setTimeout(resolve, 100))
      if (shouldFail) {
        throw new Error('模拟异步操作失败')
      }
      return '异步操作成功'
    }
    
    // 测试成功情况
    const wrappedSuccess = UserExperience.wrapWithUX(testAsyncFunction, {
      loadingTitle: '处理中...',
      successMessage: '操作成功',
      showSuccess: true
    })
    
    const successResult = await wrappedSuccess(false)
    console.log('   成功结果:', successResult)
    
    // 测试失败情况
    const wrappedFail = UserExperience.wrapWithUX(testAsyncFunction, {
      loadingTitle: '处理中...',
      showError: false // 不显示错误，避免干扰测试
    })
    
    try {
      await wrappedFail(true)
    } catch (error) {
      console.log('   预期的错误被捕获:', error.message)
    }
    
    console.log('   ✅ 异步函数包装器测试通过\n')

    // 测试11: 资源清理
    console.log('📋 测试11: 资源清理')
    UserExperience.cleanup()
    console.log('   ✅ 资源清理测试通过\n')

    console.log('🎉 所有测试完成！用户体验增强模块工作正常')
    return true

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    console.error('   错误详情:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  testUserExperience().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = { testUserExperience }
