// 小程序首页 - 集成实时数据监听
const CloudDatabaseWatcher = require('../../utils/cloud-database-watcher');

Page({
  data: {
    categories: [],
    emojis: [],
    banners: [],
    loading: true,
    connectionStatus: 'connecting', // connecting, connected, error, failed
    lastUpdateTime: null
  },

  onLoad() {
    console.log('📱 首页加载');
    this.initDataWatcher();
    this.loadInitialData();
  },

  onShow() {
    // 页面显示时检查数据是否需要刷新
    this.checkDataFreshness();
  },

  onUnload() {
    // 页面卸载时清理资源
    this.cleanupDataWatcher();
  },

  // 初始化数据监听器
  initDataWatcher() {
    try {
      // 获取全局数据监听器实例
      const app = getApp();
      if (!app.globalData.dataWatcher) {
        app.globalData.dataWatcher = new CloudDatabaseWatcher();
      }
      
      this.dataWatcher = app.globalData.dataWatcher;
      
      // 启动监听器
      this.dataWatcher.initWatchers();
      
      console.log('✅ 数据监听器初始化完成');
      
    } catch (error) {
      console.error('❌ 数据监听器初始化失败:', error);
      this.setData({
        connectionStatus: 'error'
      });
    }
  },

  // 加载初始数据
  async loadInitialData() {
    try {
      console.log('📊 加载初始数据...');
      
      // 先从缓存加载数据
      const cachedCategories = wx.getStorageSync('categories_cache') || [];
      const cachedEmojis = wx.getStorageSync('emojis_cache') || [];
      const cachedBanners = wx.getStorageSync('banners_cache') || [];
      
      if (cachedCategories.length > 0 || cachedEmojis.length > 0 || cachedBanners.length > 0) {
        this.setData({
          categories: cachedCategories,
          emojis: cachedEmojis,
          banners: cachedBanners,
          loading: false,
          lastUpdateTime: this.formatTime(new Date())
        });
        console.log('📊 缓存数据加载完成');
      }
      
      // 如果没有缓存数据，手动刷新
      if (cachedCategories.length === 0 && cachedEmojis.length === 0 && cachedBanners.length === 0) {
        await this.refreshAllData();
      }
      
    } catch (error) {
      console.error('❌ 加载初始数据失败:', error);
      this.setData({
        loading: false
      });
    }
  },

  // 检查数据新鲜度
  checkDataFreshness() {
    const cacheTime = wx.getStorageSync('categories_cache_time');
    if (cacheTime) {
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;
      
      if (now - cacheTime > fiveMinutes) {
        console.log('📊 数据缓存过期，刷新数据');
        this.refreshAllData();
      }
    }
  },

  // 手动刷新所有数据
  async refreshAllData() {
    try {
      this.setData({ loading: true });
      
      if (this.dataWatcher) {
        const [categories, emojis, banners] = await Promise.all([
          this.dataWatcher.refreshData('categories'),
          this.dataWatcher.refreshData('emojis'),
          this.dataWatcher.refreshData('banners')
        ]);

        this.setData({
          categories: categories || [],
          emojis: emojis || [],
          banners: banners || [],
          loading: false,
          lastUpdateTime: this.formatTime(new Date())
        });
        
        console.log('✅ 手动刷新完成');
      }
      
    } catch (error) {
      console.error('❌ 手动刷新失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '数据刷新失败',
        icon: 'none'
      });
    }
  },

  // 数据更新回调 - 由CloudDatabaseWatcher调用
  onDataUpdate(collectionName, operation, data) {
    console.log(`📢 页面收到数据更新: ${collectionName} - ${operation}`);
    
    switch (collectionName) {
      case 'categories':
        this.setData({
          categories: data,
          lastUpdateTime: this.formatTime(new Date())
        });
        break;
      case 'emojis':
        this.setData({
          emojis: data,
          lastUpdateTime: this.formatTime(new Date())
        });
        break;
      case 'banners':
        this.setData({
          banners: data,
          lastUpdateTime: this.formatTime(new Date())
        });
        break;
    }

    // 显示更新提示
    if (operation !== 'init') {
      this.showUpdateNotification(collectionName, operation);
    }
  },

  // 连接状态变更回调
  onConnectionStatusChange(status) {
    console.log('📡 连接状态变更:', status);
    
    this.setData({
      connectionStatus: status
    });

    // 显示连接状态提示
    switch (status) {
      case 'connected':
        wx.showToast({
          title: '实时同步已连接',
          icon: 'success',
          duration: 1500
        });
        break;
      case 'error':
      case 'failed':
        wx.showToast({
          title: '实时同步连接失败',
          icon: 'none',
          duration: 2000
        });
        break;
      case 'reconnecting':
        wx.showToast({
          title: '正在重连...',
          icon: 'loading',
          duration: 1500
        });
        break;
    }
  },

  // 显示更新通知
  showUpdateNotification(collectionName, operation) {
    const typeNames = {
      'categories': '分类',
      'emojis': '表情包',
      'banners': '横幅'
    };
    
    const operationNames = {
      'update': '已更新',
      'create': '有新增',
      'delete': '已删除'
    };
    
    const message = `${typeNames[collectionName]}${operationNames[operation] || '已变更'}`;
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      await this.refreshAllData();
      wx.stopPullDownRefresh();
    } catch (error) {
      wx.stopPullDownRefresh();
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category;
    console.log('点击分类:', category);
    
    wx.navigateTo({
      url: `/pages/category/category?id=${category.id}&name=${category.name}`
    });
  },

  // 点击表情包
  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji;
    console.log('点击表情包:', emoji);
    
    wx.navigateTo({
      url: `/pages/emoji/emoji?id=${emoji.id}`
    });
  },

  // 点击横幅
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner;
    console.log('点击横幅:', banner);
    
    if (banner.linkUrl) {
      // 如果有链接，可以跳转到对应页面
      wx.navigateTo({
        url: banner.linkUrl
      });
    }
  },

  // 查看连接状态
  onConnectionStatusTap() {
    if (this.dataWatcher) {
      const status = this.dataWatcher.getConnectionStatus();
      
      wx.showModal({
        title: '实时同步状态',
        content: `连接状态: ${status.isConnected ? '已连接' : '未连接'}\n监听器数量: ${status.watchersCount}\n重连次数: ${status.reconnectAttempts}`,
        showCancel: false
      });
    }
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    return `${[year, month, day].map(this.formatNumber).join('/')} ${[hour, minute, second].map(this.formatNumber).join(':')}`;
  },

  formatNumber(n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 清理数据监听器
  cleanupDataWatcher() {
    // 注意：不要在页面卸载时销毁全局监听器
    // 因为其他页面可能还在使用
    console.log('📱 首页卸载，保持数据监听器运行');
  }
});
