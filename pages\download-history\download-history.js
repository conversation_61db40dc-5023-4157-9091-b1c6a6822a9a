// pages/download-history/download-history.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')

Page({
  data: {
    downloadedEmojis: [],
    loading: false,
    isEmpty: false
  },

  onLoad() {
    this.loadDownloadedEmojis()
  },

  onShow() {
    // 每次显示时刷新数据，确保与详情页同步
    this.loadDownloadedEmojis()
  },

  async loadDownloadedEmojis() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 从本地存储获取下载的表情包ID列表
      const downloadedEmojiIds = wx.getStorageSync('downloadedEmojis') || []
      
      if (downloadedEmojiIds.length === 0) {
        this.setData({
          downloadedEmojis: [],
          isEmpty: true,
          loading: false
        })
        return
      }

      // 从全局数据管理器获取完整的表情包信息
      const likedEmojis = wx.getStorageSync('likedEmojis') || []
      const collectedEmojis = wx.getStorageSync('collectedEmojis') || []

      const downloadedEmojis = []

      for (const emojiId of downloadedEmojiIds) {
        let emojiData = DataManager.getEmojiDataFromCache(emojiId)

        // 如果缓存中没有，尝试从数据库获取
        if (!emojiData) {
          try {
            emojiData = await DataManager.getEmojiDataFromDatabase(emojiId)
          } catch (error) {
            console.error(`获取表情包 ${emojiId} 失败:`, error)
            continue
          }
        }

        if (emojiData) {
          downloadedEmojis.push({
            ...emojiData,
            // 确保 likes 和 collections 有默认值
            likes: emojiData.likes || 0,
            collections: emojiData.collections || 0,
            likesText: DataManager.formatNumber(emojiData.likes || 0),
            collectionsText: DataManager.formatNumber(emojiData.collections || 0),
            isLiked: likedEmojis.includes(emojiId),
            isCollected: collectedEmojis.includes(emojiId),
            downloadTime: this.getDownloadTime(emojiId)
          })
        } else {
          console.warn(`表情包 ${emojiId} 可能已被删除，但保留下载记录`)
          // 下载历史不删除记录，只是显示为已删除状态
          downloadedEmojis.push({
            id: emojiId,
            title: '已删除的表情包',
            imageUrl: '',
            likes: 0,
            collections: 0,
            likesText: '0',
            collectionsText: '0',
            isLiked: false,
            isCollected: false,
            downloadTime: this.getDownloadTime(emojiId),
            isDeleted: true
          })
        }
      }

      // 按下载时间倒序排列
      downloadedEmojis.sort((a, b) => new Date(b.downloadTime) - new Date(a.downloadTime))

      this.setData({
        downloadedEmojis: downloadedEmojis,
        isEmpty: downloadedEmojis.length === 0,
        loading: false
      })

      console.log('我的下载记录:', downloadedEmojis)
    } catch (error) {
      console.error('加载下载记录失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({
        downloadedEmojis: [],
        isEmpty: true,
        loading: false
      })
    }
  },

  // 获取下载时间并格式化
  getDownloadTime(emojiId) {
    try {
      const downloadTimes = wx.getStorageSync('downloadTimes') || {}
      const timeStr = downloadTimes[emojiId] || new Date().toISOString()
      return this.formatTime(timeStr)
    } catch (error) {
      return this.formatTime(new Date().toISOString())
    }
  },

  // 格式化时间显示
  formatTime(timeStr) {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前'
    } else if (diff < 2592000000) { // 30天内
      return Math.floor(diff / 86400000) + '天前'
    } else {
      // 超过30天显示具体日期
      return date.toLocaleDateString()
    }
  },

  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    })
  },



  // 点赞功能
  onToggleLike(e) {
    e.stopPropagation() // 阻止事件冒泡
    const emojiId = e.currentTarget.dataset.id

    try {
      const isCurrentlyLiked = StateManager.getEmojiState(emojiId).isLiked
      const newIsLiked = StateManager.toggleLike(emojiId)

      // 更新当前页面数据
      this.updateEmojiStatus()

      wx.showToast({
        title: newIsLiked ? '点赞成功' : '已取消点赞',
        icon: 'success',
        duration: 1000
      })
    } catch (error) {
      console.error('点赞操作失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 收藏功能
  onToggleCollect(e) {
    e.stopPropagation() // 阻止事件冒泡
    const emojiId = e.currentTarget.dataset.id

    try {
      const isCurrentlyCollected = StateManager.getEmojiState(emojiId).isCollected
      const newIsCollected = StateManager.toggleCollect(emojiId)

      // 更新当前页面数据
      this.updateEmojiStatus()

      wx.showToast({
        title: newIsCollected ? '收藏成功' : '已取消收藏',
        icon: 'success',
        duration: 1000
      })
    } catch (error) {
      console.error('收藏操作失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 更新表情包状态
  updateEmojiStatus() {
    const likedEmojis = wx.getStorageSync('likedEmojis') || []
    const collectedEmojis = wx.getStorageSync('collectedEmojis') || []

    const updatedEmojis = this.data.downloadedEmojis.map(emoji => ({
      ...emoji,
      isLiked: likedEmojis.includes(emoji.id),
      isCollected: collectedEmojis.includes(emoji.id)
    }))

    this.setData({
      downloadedEmojis: updatedEmojis
    })
  },

  onPullDownRefresh() {
    this.loadDownloadedEmojis()
    wx.stopPullDownRefresh()
  },

  // 去探索更多表情包
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },


})
