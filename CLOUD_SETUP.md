# 微信小程序云开发配置指南

## 🚀 快速开始

### 第一步：开通云开发服务

1. **打开微信开发者工具**
   - 导入您的小程序项目
   - 确保已填入正确的 AppID

2. **开通云开发**
   - 点击工具栏中的"云开发"按钮
   - 如果是第一次使用，会提示开通云开发服务
   - 按照提示完成开通流程

3. **创建云环境**
   - 建议创建两个环境：
     - `emoji-dev-xxx`（开发环境）
     - `emoji-prod-xxx`（生产环境）
   - 记录下环境 ID，后续需要用到

### 第二步：配置环境 ID

1. **修改 app.js 文件**
   ```javascript
   wx.cloud.init({
     env: 'emoji-dev-xxx', // 替换为你的云环境ID
     traceUser: true,
   })
   ```

2. **获取环境 ID 的方法**
   - 在云开发控制台首页可以看到
   - 格式通常为：`项目名-随机字符`

### 第三步：创建数据库集合

在云开发控制台的数据库中创建以下集合：

#### 1. categories（分类表）
- 集合名称：`categories`
- 权限设置：所有用户可读，仅管理员可写

#### 2. emojis（表情包表）
- 集合名称：`emojis`
- 权限设置：所有用户可读，仅管理员可写

#### 3. users（用户表）
- 集合名称：`users`
- 权限设置：仅创建者可读写

#### 4. user_likes（点赞记录表）
- 集合名称：`user_likes`
- 权限设置：仅创建者可读写

#### 5. user_collections（收藏记录表）
- 集合名称：`user_collections`
- 权限设置：仅创建者可读写

#### 6. banners（轮播图表）
- 集合名称：`banners`
- 权限设置：所有用户可读，仅管理员可写

### 第四步：上传云函数

1. **右键点击云函数文件夹**
   - 在项目目录中找到 `cloudfunctions` 文件夹
   - 右键点击每个云函数文件夹（如 `login`、`getEmojiList` 等）
   - 选择"上传并部署：云端安装依赖"

2. **需要上传的云函数列表**
   - `login` - 用户登录
   - `getEmojiList` - 获取表情包列表
   - `getEmojiDetail` - 获取表情包详情
   - `toggleLike` - 点赞/取消点赞
   - `toggleCollect` - 收藏/取消收藏
   - `searchEmojis` - 搜索表情包
   - `getCategories` - 获取分类列表
   - `getUserStats` - 获取用户统计

### 第五步：配置数据库权限

参考 `database/permissions.md` 文件中的权限配置说明。

### 第六步：添加测试数据

1. **添加分类数据**
   - 在 `categories` 集合中手动添加分类数据
   - 参考 `database/init.sql` 中的示例数据

2. **添加表情包数据**
   - 在 `emojis` 集合中添加测试表情包
   - 图片可以先使用网络图片，后续上传到云存储

3. **添加轮播图数据**
   - 在 `banners` 集合中添加轮播图数据

## 📝 测试步骤

### 1. 测试云函数
```javascript
// 在小程序中测试云函数调用
wx.cloud.callFunction({
  name: 'getEmojiList',
  data: {},
  success: res => {
    console.log('云函数调用成功', res)
  },
  fail: err => {
    console.error('云函数调用失败', err)
  }
})
```

### 2. 测试数据库读写
- 尝试点赞功能
- 尝试收藏功能
- 检查数据是否正确写入数据库

### 3. 测试用户登录
- 查看控制台是否有登录成功的日志
- 检查 `users` 集合是否有用户数据

## 🔧 常见问题

### 1. 云函数调用失败
- 检查环境 ID 是否正确
- 确认云函数已正确上传
- 查看云函数日志排查错误

### 2. 数据库权限错误
- 检查集合权限设置是否正确
- 确认用户已正确登录

### 3. 图片显示问题
- 确认图片 URL 是否有效
- 考虑将图片上传到云存储

## 📊 数据结构说明

### 表情包数据结构
```json
{
  "_id": "emoji_001",
  "title": "表情包标题",
  "imageUrl": "图片URL",
  "category": "分类ID",
  "tags": ["标签1", "标签2"],
  "likes": 0,
  "collections": 0,
  "views": 0,
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### 分类数据结构
```json
{
  "_id": "funny",
  "name": "搞笑幽默",
  "icon": "😂",
  "color": "#FF6B6B",
  "sort": 1,
  "count": 0,
  "createTime": "创建时间"
}
```

## 🎯 下一步计划

1. **完善数据**
   - 上传更多表情包图片到云存储
   - 丰富分类和标签数据

2. **功能优化**
   - 添加分页加载
   - 实现图片懒加载
   - 添加缓存机制

3. **用户体验**
   - 添加加载动画
   - 优化错误提示
   - 完善分享功能

4. **数据分析**
   - 添加用户行为统计
   - 实现热门推荐算法
   - 添加数据报表

## 💡 提示

- 开发阶段建议使用开发环境，避免影响生产数据
- 定期备份重要数据
- 监控云函数调用量和数据库读写次数
- 优化查询性能，避免全表扫描

配置完成后，您的小程序就可以正常使用云开发功能了！