# 🗄️ 数据库重建指南 - 完整恢复方案

## 📋 数据库集合清单

### 🎯 需要创建的集合（共11个）

#### 1. emojis - 表情包数据
```json
权限配置：
{
  "read": true,
  "write": "auth != null"
}
```

#### 2. categories - 分类数据
```json
权限配置：
{
  "read": true,
  "write": "auth != null"
}
```

#### 3. users - 用户数据
```json
权限配置：
{
  "read": "doc.openid == auth.openid || auth != null",
  "write": "doc.openid == auth.openid || auth != null"
}
```

#### 4. user_likes - 用户点赞记录
```json
权限配置：
{
  "read": "doc.userId == auth.openid || auth != null",
  "write": "doc.userId == auth.openid || auth != null"
}
```

#### 5. user_collections - 用户收藏记录
```json
权限配置：
{
  "read": "doc.userId == auth.openid || auth != null",
  "write": "doc.userId == auth.openid || auth != null"
}
```

#### 6. user_actions - 用户行为记录
```json
权限配置：
{
  "read": "doc.userId == auth.openid || auth != null",
  "write": "doc.userId == auth.openid || auth != null"
}
```

#### 7. banners - 轮播图数据
```json
权限配置：
{
  "read": true,
  "write": "auth != null"
}
```

#### 8. operation_logs - 操作日志
```json
权限配置：
{
  "read": "auth != null",
  "write": "auth != null"
}
```

#### 9. upload_records - 上传记录
```json
权限配置：
{
  "read": "doc.uploader == auth.openid || auth != null",
  "write": "doc.uploader == auth.openid || auth != null"
}
```

#### 10. daily_reports - 日报数据
```json
权限配置：
{
  "read": "auth != null",
  "write": "auth != null"
}
```

#### 11. system_configs - 系统配置
```json
权限配置：
{
  "read": "auth != null",
  "write": "auth != null"
}
```

## 🔧 创建步骤

### 第一步：创建数据库集合

1. **打开云开发控制台**
   - 在微信开发者工具中点击"云开发"
   - 选择"数据库"标签

2. **逐一创建集合**
   - 点击"+"按钮
   - 输入集合名称（如：emojis）
   - 点击"确定"

### 第二步：配置权限规则

1. **进入权限设置**
   - 点击集合名称
   - 选择"权限设置"标签

2. **配置读写权限**
   - 将上述对应的权限规则复制到权限设置中
   - 点击"保存"

### 第三步：导入初始化数据

#### 3.1 分类数据（categories）
```json
[
  {
    "_id": "funny",
    "name": "搞笑幽默",
    "icon": "😂",
    "color": "#FF6B6B",
    "description": "搞笑幽默类表情包，让你笑到停不下来",
    "sortOrder": 1,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "cute",
    "name": "可爱萌宠",
    "icon": "😍",
    "color": "#FF69B4",
    "description": "可爱萌宠类表情包，萌化你的心",
    "sortOrder": 2,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "emotion",
    "name": "情感表达",
    "icon": "❤️",
    "color": "#FF4757",
    "description": "情感表达类表情包，传递你的心情",
    "sortOrder": 3,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "festival",
    "name": "节日庆典",
    "icon": "🎉",
    "color": "#5F27CD",
    "description": "节日庆典类表情包，庆祝每个特殊时刻",
    "sortOrder": 4,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "hot",
    "name": "网络热梗",
    "icon": "🔥",
    "color": "#FFA726",
    "description": "网络热梗类表情包，紧跟时代潮流",
    "sortOrder": 5,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "2d",
    "name": "动漫二次元",
    "icon": "🌟",
    "color": "#667EEA",
    "description": "动漫二次元类表情包，二次元的快乐",
    "sortOrder": 6,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  }
]
```

#### 3.2 轮播图数据（banners）
```json
[
  {
    "title": "新年表情包",
    "subtitle": "龙年大吉，表情包拜年",
    "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600",
    "linkType": "category",
    "linkValue": "festival",
    "buttonText": "点击查看详情",
    "sortOrder": 1,
    "status": "active",
    "startTime": "2024-01-01T00:00:00.000Z",
    "endTime": "2024-02-29T23:59:59.000Z",
    "clickCount": 1250,
    "impressions": 15600,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-15T00:00:00.000Z"
  },
  {
    "title": "热门推荐",
    "subtitle": "本周最火表情包",
    "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600",
    "linkType": "category",
    "linkValue": "hot",
    "buttonText": "立即查看",
    "sortOrder": 2,
    "status": "active",
    "startTime": "2024-01-01T00:00:00.000Z",
    "endTime": "2024-12-31T23:59:59.000Z",
    "clickCount": 890,
    "impressions": 12300,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-15T00:00:00.000Z"
  }
]
```

## 📊 创建进度追踪

```
数据库集合创建进度：
[ ] 1. emojis
[ ] 2. categories  
[ ] 3. users
[ ] 4. user_likes
[ ] 5. user_collections
[ ] 6. user_actions
[ ] 7. banners
[ ] 8. operation_logs
[ ] 9. upload_records
[ ] 10. daily_reports
[ ] 11. system_configs

权限配置进度：
[ ] 1. emojis
[ ] 2. categories
[ ] 3. users
[ ] 4. user_likes
[ ] 5. user_collections
[ ] 6. user_actions
[ ] 7. banners
[ ] 8. operation_logs
[ ] 9. upload_records
[ ] 10. daily_reports
[ ] 11. system_configs

初始化数据导入：
[ ] categories（分类数据）
[ ] banners（轮播图数据）
```

## ⚠️ 重要提示

1. **权限配置必须正确**：错误的权限配置会导致管理后台无法正常工作
2. **数据格式要严格**：JSON格式必须正确，日期格式使用ISO字符串
3. **集合名称不能错**：必须与代码中的集合名称完全一致

---
**下一步：查看《管理后台配置指南.md》**
