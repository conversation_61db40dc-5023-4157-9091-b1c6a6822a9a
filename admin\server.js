const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 9000;

// 云开发环境配置
const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
const ADMIN_API_URL = `https://${ENV_ID}.service.tcloudbase.com/adminAPI`;
const DATA_API_URL = `https://${ENV_ID}.service.tcloudbase.com/dataAPI`;

// 中间件
app.use(cors()); // 允许跨域
app.use(express.json()); // 解析JSON
app.use(express.static(__dirname)); // 静态文件服务

console.log('🚀 启动云开发代理服务器...');
console.log(`📡 AdminAPI URL: ${ADMIN_API_URL}`);
console.log(`📡 DataAPI URL: ${DATA_API_URL}`);

// 代理adminAPI云函数
app.post('/api/adminAPI', async (req, res) => {
    console.log('📡 代理adminAPI请求:', req.body);
    
    try {
        const response = await fetch(ADMIN_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        console.log('✅ adminAPI响应:', data);
        
        res.json(data);
    } catch (error) {
        console.error('❌ adminAPI代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 代理dataAPI云函数
app.post('/api/dataAPI', async (req, res) => {
    console.log('📡 代理dataAPI请求:', req.body);
    
    try {
        const response = await fetch(DATA_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        console.log('✅ dataAPI响应:', data);
        
        res.json(data);
    } catch (error) {
        console.error('❌ dataAPI代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        envId: ENV_ID
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🌐 代理服务器启动成功!`);
    console.log(`📍 本地地址: http://localhost:${PORT}`);
    console.log(`🔗 管理后台: http://localhost:${PORT}/index.html`);
    console.log(`🧪 测试页面: http://localhost:${PORT}/simple-test.html`);
    console.log('');
    console.log('💡 现在可以通过本地代理访问云开发API了!');
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
});
