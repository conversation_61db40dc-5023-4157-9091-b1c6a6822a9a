<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section card" wx:if="{{userInfo.nickName}}">
    <image class="user-avatar" src="{{userInfo.avatarUrl}}" />
    <view class="user-info">
      <text class="user-name">{{userInfo.nickName}}</text>
      <text class="user-desc">表情包爱好者</text>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-section">
    <view class="section-title">应用设置</view>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-title">自动下载</text>
        <text class="setting-desc">点击表情包时自动下载到本地</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.autoDownload}}" 
        bindchange="onToggleSetting"
        data-setting="autoDownload"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-title">震动反馈</text>
        <text class="setting-desc">操作时提供触觉反馈</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.vibration}}" 
        bindchange="onToggleSetting"
        data-setting="vibration"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-title">消息通知</text>
        <text class="setting-desc">接收新表情包推送通知</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.notification}}" 
        bindchange="onToggleSetting"
        data-setting="notification"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-title">深色模式</text>
        <text class="setting-desc">使用深色主题（开发中）</text>
      </view>
      <switch 
        class="setting-switch" 
        checked="{{settings.darkMode}}" 
        bindchange="onToggleSetting"
        data-setting="darkMode"
        disabled
      />
    </view>
  </view>

  <!-- 存储管理 -->
  <view class="storage-section">
    <view class="section-title">存储管理</view>
    
    <view class="storage-item" bindtap="onClearCache">
      <view class="storage-info">
        <text class="storage-title">清除缓存</text>
        <text class="storage-desc">当前缓存: {{cacheSize}}</text>
      </view>
      <text class="storage-arrow">→</text>
    </view>

    <view class="storage-item danger" bindtap="onResetAllData">
      <view class="storage-info">
        <text class="storage-title">重置所有数据</text>
        <text class="storage-desc">清除所有点赞、收藏等数据</text>
      </view>
      <text class="storage-arrow">→</text>
    </view>
  </view>

  <!-- 关于应用 -->
  <view class="about-section">
    <view class="section-title">关于应用</view>
    
    <view class="about-item" bindtap="onCheckUpdate">
      <text class="about-title">检查更新</text>
      <view class="about-right">
        <text class="about-version">v{{version}}</text>
        <text class="about-arrow">→</text>
      </view>
    </view>

    <view class="about-item" bindtap="onAbout">
      <text class="about-title">关于我们</text>
      <text class="about-arrow">→</text>
    </view>

    <view class="about-item" bindtap="onUserAgreement">
      <text class="about-title">用户协议</text>
      <text class="about-arrow">→</text>
    </view>

    <view class="about-item" bindtap="onPrivacyPolicy">
      <text class="about-title">隐私政策</text>
      <text class="about-arrow">→</text>
    </view>

    <view class="about-item" bindtap="onFeedback">
      <text class="about-title">意见反馈</text>
      <text class="about-arrow">→</text>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">表情包小程序 v{{version}}</text>
    <text class="version-desc">让表达更有趣</text>
  </view>
</view>
