<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 正确的安全规则配置</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .rule-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .correct {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .incorrect {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .step {
            background-color: #e7f3ff;
            border: 1px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        h3 {
            color: #333;
            margin-top: 25px;
        }
        .copy-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 正确的安全规则配置</h1>
        
        <div class="warning">
            <h3>⚠️ 您遇到的错误</h3>
            <p><strong>错误信息:</strong> rule invalid</p>
            <p><strong>原因:</strong> 安全规则语法不正确</p>
        </div>

        <h3>❌ 错误的规则语法</h3>
        <div class="rule-box incorrect">
{
  "read": "true",
  "write": "auth.loginType != 'ANONYMOUS'"
}
        </div>
        <p><strong>问题:</strong> 腾讯云开发的安全规则语法与此不同</p>

        <h3>✅ 正确的规则语法</h3>
        
        <h4>方案1: 允许所有用户读取，仅非匿名用户写入（推荐用于测试）</h4>
        <div class="rule-box correct">
{
  "read": true,
  "write": "auth.loginType != 'ANONYMOUS'"
}
        <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
        </div>

        <h4>方案2: 完全开放（仅用于开发测试）</h4>
        <div class="rule-box correct">
{
  "read": true,
  "write": true
}
        <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
        </div>

        <h4>方案3: 仅登录用户可访问</h4>
        <div class="rule-box correct">
{
  "read": "auth != null",
  "write": "auth != null"
}
        <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
        </div>

        <h4>方案4: 基于用户ID的权限控制</h4>
        <div class="rule-box correct">
{
  "read": "auth != null",
  "write": "auth != null && auth.uid == resource.data.userId"
}
        <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
        </div>

        <div class="success">
            <h3>🎯 推荐配置（用于您的表情包系统）</h3>
            <p>对于不同的集合，建议使用不同的规则：</p>
            
            <h4>📁 emojis 集合（表情包数据）</h4>
            <div class="rule-box correct">
{
  "read": true,
  "write": "auth != null"
}
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
            </div>
            <p>允许所有人查看表情包，仅登录用户可以添加/修改</p>

            <h4>📁 categories 集合（分类数据）</h4>
            <div class="rule-box correct">
{
  "read": true,
  "write": "auth != null"
}
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
            </div>

            <h4>📁 users 集合（用户数据）</h4>
            <div class="rule-box correct">
{
  "read": "auth != null && auth.uid == resource.data.userId",
  "write": "auth != null && auth.uid == resource.data.userId"
}
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
            </div>
            <p>用户只能访问自己的数据</p>

            <h4>📁 banners 集合（横幅数据）</h4>
            <div class="rule-box correct">
{
  "read": true,
  "write": "auth != null"
}
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.textContent)">复制</button>
            </div>
        </div>

        <div class="step">
            <h3>📝 配置步骤</h3>
            <ol>
                <li><strong>选择集合:</strong> 在左侧选择要配置的集合（如 emojis）</li>
                <li><strong>点击权限设置:</strong> 点击右侧的"权限设置"标签</li>
                <li><strong>复制规则:</strong> 复制上面推荐的规则</li>
                <li><strong>粘贴规则:</strong> 粘贴到规则编辑器中</li>
                <li><strong>保存:</strong> 点击保存按钮</li>
                <li><strong>等待生效:</strong> 通常几秒钟后生效</li>
            </ol>
        </div>

        <div class="warning">
            <h3>⚠️ 重要提醒</h3>
            <ul>
                <li><strong>语法要点:</strong> 
                    <ul>
                        <li>布尔值用 <code>true</code>/<code>false</code>，不要加引号</li>
                        <li>字符串比较用双引号，如 <code>"ANONYMOUS"</code></li>
                        <li>条件表达式不要加引号</li>
                    </ul>
                </li>
                <li><strong>测试建议:</strong> 先用完全开放的规则测试，确认功能正常后再收紧权限</li>
                <li><strong>生产环境:</strong> 不要使用完全开放的规则</li>
            </ul>
        </div>

        <div class="success">
            <h3>🚀 快速解决方案</h3>
            <p>为了立即解决您的问题，建议：</p>
            <ol>
                <li>对所有集合先使用方案2（完全开放）进行测试</li>
                <li>确认数据库查询正常工作后</li>
                <li>再逐个集合应用推荐的安全规则</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            // 清理文本，移除多余的空白字符
            const cleanText = text.trim();
            
            navigator.clipboard.writeText(cleanText).then(function() {
                // 创建临时提示
                const notification = document.createElement('div');
                notification.textContent = '✅ 已复制到剪贴板';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 4px;
                    z-index: 1000;
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 页面加载提示
        window.addEventListener('DOMContentLoaded', function() {
            console.log('🔒 安全规则配置指南已加载');
        });
    </script>
</body>
</html>
