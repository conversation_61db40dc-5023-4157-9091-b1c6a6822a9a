const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testOldCodeRemoval() {
  console.log('🧹 测试旧代码删除安全性...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 1. 验证备份文件存在
    console.log('\n📋 步骤1：验证备份文件');
    
    const backupDir = 'backup/detail-old-20250731';
    const backupFiles = [
      'detail.js',
      'detail.wxml', 
      'detail.wxss',
      'detail.json'
    ];
    
    let backupComplete = true;
    for (const file of backupFiles) {
      const backupPath = path.join(backupDir, file);
      if (fs.existsSync(backupPath)) {
        console.log(`✅ 备份文件存在: ${backupPath}`);
      } else {
        console.error(`❌ 备份文件缺失: ${backupPath}`);
        backupComplete = false;
      }
    }
    
    if (!backupComplete) {
      console.error('❌ 备份不完整，停止测试');
      return;
    }
    
    // 2. 验证旧文件已删除
    console.log('\n📋 步骤2：验证旧文件已删除');
    
    const oldFiles = [
      'pages/detail/detail.js',
      'pages/detail/detail.wxml',
      'pages/detail/detail.wxss', 
      'pages/detail/detail.json'
    ];
    
    let deletionComplete = true;
    for (const file of oldFiles) {
      if (!fs.existsSync(file)) {
        console.log(`✅ 旧文件已删除: ${file}`);
      } else {
        console.error(`❌ 旧文件仍存在: ${file}`);
        deletionComplete = false;
      }
    }
    
    if (!deletionComplete) {
      console.error('❌ 删除不完整');
      return;
    }
    
    // 3. 验证新文件完整
    console.log('\n📋 步骤3：验证新文件完整');
    
    const newFiles = [
      'pages/detail/detail-new.js',
      'pages/detail/detail-new.wxml',
      'pages/detail/detail-new.wxss',
      'pages/detail/detail-new.json'
    ];
    
    let newFilesComplete = true;
    for (const file of newFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ 新文件存在: ${file}`);
      } else {
        console.error(`❌ 新文件缺失: ${file}`);
        newFilesComplete = false;
      }
    }
    
    if (!newFilesComplete) {
      console.error('❌ 新文件不完整');
      return;
    }
    
    // 4. 验证app.json配置
    console.log('\n📋 步骤4：验证app.json配置');
    
    const appJsonContent = fs.readFileSync('app.json', 'utf8');
    const appConfig = JSON.parse(appJsonContent);
    
    const hasOldRoute = appConfig.pages.includes('pages/detail/detail');
    const hasNewRoute = appConfig.pages.includes('pages/detail/detail-new');
    
    if (!hasOldRoute) {
      console.log('✅ 旧路由已从app.json中移除');
    } else {
      console.error('❌ 旧路由仍在app.json中');
    }
    
    if (hasNewRoute) {
      console.log('✅ 新路由在app.json中存在');
    } else {
      console.error('❌ 新路由不在app.json中');
    }
    
    // 5. 测试新详情页功能
    console.log('\n📋 步骤5：测试新详情页功能');
    
    await page.goto('http://localhost:3000');
    await page.waitForTimeout(8000);
    
    const testResult = await page.evaluate(async () => {
      try {
        if (!window.cloudbase) return { error: 'SDK未加载' };
        
        const app = window.cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
        await app.auth().signInAnonymously();
        
        // 获取测试表情包ID
        const listResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 1 } }
        });
        
        if (!listResult.result?.success || !listResult.result.data?.length) {
          return { error: '无法获取测试数据' };
        }
        
        const testEmojiId = listResult.result.data[0]._id;
        
        // 测试详情接口
        const detailResult = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojiDetail', data: { id: testEmojiId } }
        });
        
        return {
          success: true,
          testEmojiId,
          detailSuccess: detailResult.result?.success,
          detailTitle: detailResult.result?.data?.title
        };
        
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (testResult.error) {
      console.error('❌ 功能测试失败:', testResult.error);
      return;
    }
    
    console.log('✅ 云函数接口正常');
    console.log(`🎯 测试表情包ID: ${testResult.testEmojiId}`);
    console.log(`📄 详情接口: ${testResult.detailSuccess ? '正常' : '异常'}`);
    
    if (testResult.detailSuccess) {
      console.log(`📝 表情包标题: ${testResult.detailTitle}`);
    }
    
    // 6. 生成删除报告
    console.log('\n📋 步骤6：生成删除报告');
    
    const removalReport = {
      timestamp: new Date().toISOString(),
      operation: 'old_code_removal',
      results: {
        backupCreated: backupComplete,
        oldFilesRemoved: deletionComplete,
        newFilesIntact: newFilesComplete,
        routingUpdated: !hasOldRoute && hasNewRoute,
        functionalityTested: testResult.success && testResult.detailSuccess
      },
      backupLocation: backupDir,
      removedFiles: oldFiles,
      preservedFiles: newFiles,
      testResults: testResult,
      rollbackInstructions: [
        '如需回滚，执行以下步骤：',
        '1. 复制备份文件回原位置',
        '2. 在app.json中重新添加旧路由',
        '3. 更新跳转链接指向旧页面'
      ]
    };
    
    fs.writeFileSync('old-code-removal-report.json', JSON.stringify(removalReport, null, 2));
    console.log('📄 删除报告已保存: old-code-removal-report.json');
    
    console.log('\n🎉 旧代码删除测试完成！');
    
    if (removalReport.results.functionalityTested) {
      console.log('✅ 删除安全，新版本功能正常');
    } else {
      console.log('⚠️ 需要进一步验证新版本功能');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testOldCodeRemoval().catch(console.error);
