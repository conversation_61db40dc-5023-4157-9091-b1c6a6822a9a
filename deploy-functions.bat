@echo off
echo 🚀 开始部署修复后的云函数...
echo ⚠️ 注意：这将部署已删除测试数据创建功能的云函数
echo.

echo 📦 部署 dataAPI 云函数（已删除 initTestData）...
cd cloudfunctions\dataAPI
call npm install
cd ..\..
echo ✅ dataAPI 依赖安装完成

echo.
echo 📦 部署 adminAPI 云函数（已删除 initTestData）...
cd cloudfunctions\adminAPI
call npm install
cd ..\..
echo ✅ adminAPI 依赖安装完成

echo.
echo 🚀 请在微信开发者工具中部署以下云函数：
echo.
echo 【必须部署】
echo 1. 右键点击 cloudfunctions/dataAPI 文件夹
echo 2. 选择"上传并部署：云端安装依赖"
echo 3. 右键点击 cloudfunctions/adminAPI 文件夹
echo 4. 选择"上传并部署：云端安装依赖"
echo.
echo 【可选部署】
echo 5. 如果有 webAdminAPI，也要部署
echo 6. 如果有 syncAPI，也要部署
echo.
echo 或者使用命令行部署：
echo tcb fn deploy dataAPI --envId cloud1-5g6pvnpl88dc0142
echo tcb fn deploy adminAPI --envId cloud1-5g6pvnpl88dc0142
echo.
echo ✅ 修复效果：
echo   - 已删除所有 initTestData 函数
echo   - 不会再创建虚拟测试数据
echo   - 只能通过管理后台手动添加数据
echo.

pause
