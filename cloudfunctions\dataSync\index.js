// 云函数入口文件 - 数据同步和维护
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'updateCategoryStats':
        return await updateAllCategoryStats()
      case 'cleanExpiredData':
        return await cleanExpiredData()
      case 'updateUserStats':
        return await updateAllUserStats()
      case 'generateDailyReport':
        return await generateDailyReport()
      case 'syncLikes':
        return await syncLikes(event.likedEmojis, OPENID)
      case 'syncCollections':
        return await syncCollections(event.collectedEmojis, OPENID)
      case 'syncDownloads':
        return await syncDownloads(event.downloadedEmojis, event.downloadTimes, OPENID)
      case 'syncUserAction':
        return await syncUserAction(event.operation, OPENID)
      default:
        return {
          success: false,
          error: '未知操作'
        }
    }
  } catch (error) {
    console.error('数据同步操作失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 更新所有分类统计
async function updateAllCategoryStats() {
  try {
    const categories = await db.collection('categories').where({
      status: 'active'
    }).get()
    
    const updatePromises = categories.data.map(async (category) => {
      const emojiCount = await db.collection('emojis').where({
        category: category._id,
        status: 'published'
      }).count()
      
      return db.collection('categories').doc(category._id).update({
        data: {
          emojiCount: emojiCount.total,
          updateTime: new Date()
        }
      })
    })
    
    await Promise.all(updatePromises)
    
    return {
      success: true,
      message: `更新了${categories.data.length}个分类的统计数据`
    }
  } catch (error) {
    throw new Error('更新分类统计失败: ' + error.message)
  }
}

// 清理过期数据
async function cleanExpiredData() {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
    
    // 清理过期的用户行为数据
    const expiredActions = await db.collection('user_actions').where({
      createTime: _.lt(thirtyDaysAgo)
    }).remove()
    
    // 清理软删除的表情包（90天后）
    const deletedEmojis = await db.collection('emojis').where({
      status: 'deleted',
      updateTime: _.lt(ninetyDaysAgo)
    }).remove()
    
    // 清理过期的操作日志
    const expiredLogs = await db.collection('operation_logs').where({
      createTime: _.lt(ninetyDaysAgo)
    }).remove()
    
    return {
      success: true,
      data: {
        cleanedActions: expiredActions.stats.removed,
        cleanedEmojis: deletedEmojis.stats.removed,
        cleanedLogs: expiredLogs.stats.removed
      }
    }
  } catch (error) {
    throw new Error('清理过期数据失败: ' + error.message)
  }
}

// 更新所有用户统计
async function updateAllUserStats() {
  try {
    const users = await db.collection('users').get()
    
    const updatePromises = users.data.map(async (user) => {
      const [likeCount, collectCount, downloadCount] = await Promise.all([
        db.collection('user_actions').where({
          userId: user.openid,
          action: 'like'
        }).count(),
        db.collection('user_actions').where({
          userId: user.openid,
          action: 'collect'
        }).count(),
        db.collection('user_actions').where({
          userId: user.openid,
          action: 'download'
        }).count()
      ])
      
      return db.collection('users').doc(user._id).update({
        data: {
          likeCount: likeCount.total,
          collectCount: collectCount.total,
          downloadCount: downloadCount.total,
          updateTime: new Date()
        }
      })
    })
    
    await Promise.all(updatePromises)
    
    return {
      success: true,
      message: `更新了${users.data.length}个用户的统计数据`
    }
  } catch (error) {
    throw new Error('更新用户统计失败: ' + error.message)
  }
}

// 生成日报
async function generateDailyReport() {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    // 获取今日数据
    const [
      newUsers,
      newEmojis,
      totalDownloads,
      totalViews,
      activeUsers
    ] = await Promise.all([
      db.collection('users').where({
        createTime: _.gte(today).and(_.lt(tomorrow))
      }).count(),
      db.collection('emojis').where({
        createTime: _.gte(today).and(_.lt(tomorrow))
      }).count(),
      db.collection('user_actions').where({
        action: 'download',
        createTime: _.gte(today).and(_.lt(tomorrow))
      }).count(),
      db.collection('user_actions').where({
        action: 'view',
        createTime: _.gte(today).and(_.lt(tomorrow))
      }).count(),
      db.collection('user_actions').where({
        createTime: _.gte(today).and(_.lt(tomorrow))
      }).field({ userId: true }).get()
    ])
    
    // 计算活跃用户数
    const uniqueActiveUsers = new Set(activeUsers.data.map(action => action.userId))
    
    const report = {
      date: today.toISOString().split('T')[0],
      newUsers: newUsers.total,
      newEmojis: newEmojis.total,
      totalDownloads: totalDownloads.total,
      totalViews: totalViews.total,
      activeUsers: uniqueActiveUsers.size,
      createTime: new Date()
    }
    
    // 保存日报
    await db.collection('daily_reports').add({
      data: report
    })
    
    return {
      success: true,
      data: report
    }
  } catch (error) {
    throw new Error('生成日报失败: ' + error.message)
  }
}

// 同步用户点赞数据
async function syncLikes(likedEmojis, userId) {
  try {
    console.log('同步用户点赞数据:', userId, likedEmojis.length)

    // 获取现有的点赞记录
    const existingLikes = await db.collection('user_likes').where({
      userId: userId
    }).get()

    const existingEmojiIds = existingLikes.data.map(item => item.emojiId)

    // 找出需要新增的点赞记录
    const newLikes = likedEmojis.filter(emojiId => !existingEmojiIds.includes(emojiId))

    // 批量添加新的点赞记录
    if (newLikes.length > 0) {
      const batch = db.batch()

      newLikes.forEach(emojiId => {
        batch.collection('user_likes').add({
          data: {
            userId: userId,
            emojiId: emojiId,
            createTime: new Date()
          }
        })
      })

      await batch.commit()
      console.log('新增点赞记录:', newLikes.length)
    }

    return {
      success: true,
      data: {
        total: likedEmojis.length,
        added: newLikes.length
      }
    }
  } catch (error) {
    throw new Error('同步点赞数据失败: ' + error.message)
  }
}

// 同步用户收藏数据
async function syncCollections(collectedEmojis, userId) {
  try {
    console.log('同步用户收藏数据:', userId, collectedEmojis.length)

    // 获取现有的收藏记录
    const existingCollections = await db.collection('user_collections').where({
      userId: userId
    }).get()

    const existingEmojiIds = existingCollections.data.map(item => item.emojiId)

    // 找出需要新增的收藏记录
    const newCollections = collectedEmojis.filter(emojiId => !existingEmojiIds.includes(emojiId))

    // 批量添加新的收藏记录
    if (newCollections.length > 0) {
      const batch = db.batch()

      newCollections.forEach(emojiId => {
        batch.collection('user_collections').add({
          data: {
            userId: userId,
            emojiId: emojiId,
            createTime: new Date()
          }
        })
      })

      await batch.commit()
      console.log('新增收藏记录:', newCollections.length)
    }

    return {
      success: true,
      data: {
        total: collectedEmojis.length,
        added: newCollections.length
      }
    }
  } catch (error) {
    throw new Error('同步收藏数据失败: ' + error.message)
  }
}

// 同步用户下载数据
async function syncDownloads(downloadedEmojis, downloadTimes, userId) {
  try {
    console.log('同步用户下载数据:', userId, downloadedEmojis.length)

    // 获取现有的下载记录
    const existingDownloads = await db.collection('user_downloads').where({
      userId: userId
    }).get()

    const existingEmojiIds = existingDownloads.data.map(item => item.emojiId)

    // 找出需要新增的下载记录
    const newDownloads = downloadedEmojis.filter(emojiId => !existingEmojiIds.includes(emojiId))

    // 批量添加新的下载记录
    if (newDownloads.length > 0) {
      const batch = db.batch()

      newDownloads.forEach(emojiId => {
        const downloadTime = downloadTimes[emojiId] ? new Date(downloadTimes[emojiId]) : new Date()

        batch.collection('user_downloads').add({
          data: {
            userId: userId,
            emojiId: emojiId,
            downloadTime: downloadTime,
            createTime: new Date()
          }
        })
      })

      await batch.commit()
      console.log('新增下载记录:', newDownloads.length)
    }

    return {
      success: true,
      data: {
        total: downloadedEmojis.length,
        added: newDownloads.length
      }
    }
  } catch (error) {
    throw new Error('同步下载数据失败: ' + error.message)
  }
}

// 同步用户操作
async function syncUserAction(operation, userId) {
  try {
    console.log('同步用户操作:', userId, operation.action)

    const { action, emojiId, data } = operation

    switch (action) {
      case 'like':
        await handleLikeAction(userId, emojiId, data.isLiked)
        break
      case 'collect':
        await handleCollectAction(userId, emojiId, data.isCollected)
        break
      case 'download':
        await handleDownloadAction(userId, emojiId, data)
        break
      default:
        console.warn('未知的用户操作:', action)
    }

    return {
      success: true,
      message: '用户操作同步成功'
    }
  } catch (error) {
    throw new Error('同步用户操作失败: ' + error.message)
  }
}

// 处理点赞操作
async function handleLikeAction(userId, emojiId, isLiked) {
  if (isLiked) {
    // 添加点赞记录
    await db.collection('user_likes').add({
      data: {
        userId: userId,
        emojiId: emojiId,
        createTime: new Date()
      }
    })
  } else {
    // 删除点赞记录
    await db.collection('user_likes').where({
      userId: userId,
      emojiId: emojiId
    }).remove()
  }
}

// 处理收藏操作
async function handleCollectAction(userId, emojiId, isCollected) {
  if (isCollected) {
    // 添加收藏记录
    await db.collection('user_collections').add({
      data: {
        userId: userId,
        emojiId: emojiId,
        createTime: new Date()
      }
    })
  } else {
    // 删除收藏记录
    await db.collection('user_collections').where({
      userId: userId,
      emojiId: emojiId
    }).remove()
  }
}

// 处理下载操作
async function handleDownloadAction(userId, emojiId, data) {
  // 添加下载记录
  await db.collection('user_downloads').add({
    data: {
      userId: userId,
      emojiId: emojiId,
      downloadTime: new Date(),
      createTime: new Date()
    }
  })
}