/**
 * 表情包管理后台 - Serverless版
 * 基于微信云开发的纯前端管理系统
 */

// 全局变量
let currentTab = 'dashboard';
let currentData = {};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 表情包管理后台启动 - Serverless版');

    // 检查云开发初始化状态
    if (typeof window.cloudInitialized === 'undefined') {
        console.log('⚠️ 云开发未初始化，将使用模拟数据模式');
        window.cloudInitialized = false;
    }

    // 加载仪表盘数据
    loadDashboard();

    // 绑定事件
    bindEvents();
});

// 绑定事件
function bindEvents() {
    // 模态框关闭事件
    document.getElementById('modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// 切换标签页
function switchTab(tabName) {
    // 更新导航状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 更新内容面板
    document.querySelectorAll('.content-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    currentTab = tabName;
    
    // 加载对应数据
    switch(tabName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'categories':
            loadCategories();
            break;
        case 'emojis':
            loadEmojis();
            break;
        case 'banners':
            loadBanners();
            break;
        case 'users':
            loadUsers();
            break;
    }
}

// 调用云函数
async function callCloudFunction(functionName, data) {
    console.log(`📡 调用云函数: ${functionName}`, data);

    try {
        let result;

        if (window.cloudInitialized && typeof wx !== 'undefined' && wx.cloud) {
            // 在微信环境中，直接调用云函数
            console.log('☁️ 使用微信云函数调用');
            const response = await wx.cloud.callFunction({
                name: functionName,
                data: data
            });
            result = response.result;
        } else {
            // 在浏览器环境中，通过HTTP API调用
            console.log('🌐 使用HTTP API调用');
            result = await callCloudFunctionViaHTTP(functionName, data);
        }

        console.log(`✅ 云函数调用成功: ${functionName}`, result);
        return result;
    } catch (error) {
        console.error(`❌ 云函数调用失败: ${functionName}`, error);

        // 降级到模拟数据
        console.warn('⚠️ 降级到模拟数据');
        return await mockCloudFunction(functionName, data);
    }
}

// 通过HTTP API调用云函数
async function callCloudFunctionViaHTTP(functionName, data) {
    // 使用云函数的HTTP触发器URL
    const response = await fetch(`https://cloud1-5g6pvnpl88dc0142-1367610204.tcloudbaseapp.com/adminAPI`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error(`HTTP调用失败: ${response.status}`);
    }

    const result = await response.json();
    return result;
}

// 模拟云函数调用（用于演示）
async function mockCloudFunction(functionName, data) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const mockData = {
        adminAPI: {
            getStats: {
                success: true,
                data: {
                    categories: 5,
                    emojis: 23,
                    banners: 3,
                    users: 156,
                    totalLikes: 1234,
                    totalDownloads: 5678,
                    totalCollections: 890,
                    updateTime: new Date()
                }
            },
            getCategoryList: {
                success: true,
                data: [
                    { _id: 'cat1', name: '情感表达', icon: '😊', status: 'show', count: 8, sort: 1, createTime: new Date() },
                    { _id: 'cat2', name: '动物萌宠', icon: '🐱', status: 'show', count: 6, sort: 2, createTime: new Date() },
                    { _id: 'cat3', name: '搞笑幽默', icon: '😂', status: 'show', count: 9, sort: 3, createTime: new Date() }
                ]
            },
            getEmojiList: {
                success: true,
                data: [
                    { _id: 'emoji1', title: '开心笑脸', category: '情感表达', status: 'published', likes: 156, downloads: 89, createTime: new Date() },
                    { _id: 'emoji2', title: '可爱小猫', category: '动物萌宠', status: 'published', likes: 234, downloads: 167, createTime: new Date() },
                    { _id: 'emoji3', title: '搞笑表情', category: '搞笑幽默', status: 'draft', likes: 345, downloads: 234, createTime: new Date() }
                ]
            },
            getBannerList: {
                success: true,
                data: [
                    { _id: 'banner1', title: '欢迎使用表情包', imageUrl: '/images/banner1.jpg', status: 'show', priority: 1, createTime: new Date() },
                    { _id: 'banner2', title: '新年快乐', imageUrl: '/images/banner2.jpg', status: 'show', priority: 2, createTime: new Date() }
                ]
            },
            getUserList: {
                success: true,
                data: [
                    { _id: 'user1', profile: { nickname: '用户1', avatar: '/images/avatar1.jpg' }, auth: { role: 'user', status: 'active' }, createTime: new Date() },
                    { _id: 'user2', profile: { nickname: '用户2', avatar: '/images/avatar2.jpg' }, auth: { role: 'admin', status: 'active' }, createTime: new Date() }
                ]
            }
        }
    };
    
    const functionData = mockData[functionName];
    if (functionData && functionData[data.action]) {
        return functionData[data.action];
    }
    
    // 默认成功响应
    return {
        success: true,
        message: `模拟执行 ${functionName}.${data.action} 成功`,
        data: data
    };
}

// 加载仪表盘
async function loadDashboard() {
    try {
        showLoading('statsGrid');

        // 检查云开发是否可用
        if (window.cloudInitialized && typeof wx !== 'undefined' && wx.cloud) {
            console.log('📡 尝试调用云函数获取真实数据...');

            try {
                const result = await callCloudFunction('adminAPI', { action: 'getStats' });

                if (result.success) {
                    console.log('✅ 获取真实数据成功');
                    renderStats(result.data);
                    hideLoading('statsGrid');
                    return;
                }
            } catch (cloudError) {
                console.warn('❌ 云函数调用失败，降级到模拟数据:', cloudError);
            }
        } else {
            console.log('⚠️ 云开发不可用，直接使用模拟数据');
        }

        // 使用模拟数据
        console.log('🎭 使用模拟数据');
        const mockStats = {
            users: 156,
            emojis: 89,
            categories: 12
        };

        renderStats(mockStats);
        hideLoading('statsGrid');

        // 显示提示信息
        if (!window.cloudInitialized) {
            showNotification('⚠️ 云开发环境不可用，当前显示模拟数据', 'warning');
        } else {
            showNotification('⚠️ 云函数调用失败，当前显示模拟数据', 'warning');
        }

    } catch (error) {
        console.error('❌ 加载仪表盘失败:', error);
        hideLoading('statsGrid');
        showError('加载统计数据失败: ' + error.message);
    }
}

// 渲染统计数据
function renderStats(stats) {
    const statsGrid = document.getElementById('statsGrid');
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${stats.categories}</div>
            <div class="stat-label">分类总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.emojis}</div>
            <div class="stat-label">表情包总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.banners}</div>
            <div class="stat-label">横幅总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.users}</div>
            <div class="stat-label">用户总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.totalLikes}</div>
            <div class="stat-label">总点赞数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.totalDownloads}</div>
            <div class="stat-label">总下载数</div>
        </div>
    `;
}

// 加载分类列表
async function loadCategories() {
    try {
        showLoading('categoriesContent');
        
        const result = await callCloudFunction('admin', { action: 'getCategoryList' });
        
        if (result.success) {
            currentData.categories = result.data;
            renderCategories(result.data);
        } else {
            showError('加载分类数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载分类数据失败: ' + error.message);
    }
}

// 渲染分类列表
function renderCategories(categories) {
    const content = document.getElementById('categoriesContent');
    
    if (categories.length === 0) {
        content.innerHTML = '<div class="loading">暂无分类数据</div>';
        return;
    }
    
    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>图标</th>
                    <th>名称</th>
                    <th>状态</th>
                    <th>表情包数量</th>
                    <th>排序</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${categories.map(category => `
                    <tr>
                        <td>${category.icon}</td>
                        <td>${category.name}</td>
                        <td><span class="status-badge status-${category.status}">${category.status === 'show' ? '显示' : '隐藏'}</span></td>
                        <td>${category.count || 0}</td>
                        <td>${category.sort || 0}</td>
                        <td>${formatDate(category.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editCategory('${category._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteCategory('${category._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    content.innerHTML = tableHTML;
}

// 加载表情包列表
async function loadEmojis() {
    try {
        showLoading('emojisContent');
        
        const result = await callCloudFunction('admin', { action: 'getEmojiList' });
        
        if (result.success) {
            renderEmojis(result.data);
        } else {
            showError('加载表情包数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载表情包数据失败: ' + error.message);
    }
}

// 渲染表情包列表
function renderEmojis(emojis) {
    const content = document.getElementById('emojisContent');
    
    if (emojis.length === 0) {
        content.innerHTML = '<div class="loading">暂无表情包数据</div>';
        return;
    }
    
    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>标题</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>点赞数</th>
                    <th>下载数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${emojis.map(emoji => `
                    <tr>
                        <td>${emoji.title}</td>
                        <td>${emoji.category}</td>
                        <td><span class="status-badge status-${emoji.status}">${emoji.status === 'published' ? '已发布' : '草稿'}</span></td>
                        <td>${emoji.likes || 0}</td>
                        <td>${emoji.downloads || 0}</td>
                        <td>${formatDate(emoji.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editEmoji('${emoji._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteEmoji('${emoji._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    content.innerHTML = tableHTML;
}

// 工具函数
function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="loading">正在加载...</div>';
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element && element.innerHTML.includes('loading')) {
        element.innerHTML = '';
    }
}

function showError(message) {
    console.error('❌', message);
    showNotification(message, 'error');
}

function showSuccess(message) {
    console.log('✅', message);
    showNotification(message, 'success');
}

function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

function formatDate(date) {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
}

// 模态框操作
function showModal(content) {
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('modal').classList.add('active');
}

function closeModal() {
    document.getElementById('modal').classList.remove('active');
}

// 刷新统计数据
function refreshStats() {
    loadDashboard();
}

// 初始化测试数据
async function initTestData() {
    if (confirm('确定要初始化测试数据吗？这将清空现有数据。')) {
        try {
            const result = await callCloudFunction('adminAPI', { action: 'initTestData' });
            if (result.success) {
                showSuccess('测试数据初始化成功');
                loadDashboard();
            } else {
                showError('初始化失败: ' + result.error);
            }
        } catch (error) {
            showError('初始化失败: ' + error.message);
        }
    }
}

// 分类管理函数
function showAddCategoryModal() {
    const content = `
        <h3>添加分类</h3>
        <form onsubmit="addCategory(event)">
            <div class="form-group">
                <label class="form-label">分类名称</label>
                <input type="text" class="form-input" name="name" required>
            </div>
            <div class="form-group">
                <label class="form-label">图标</label>
                <input type="text" class="form-input" name="icon" placeholder="输入emoji图标" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">排序</label>
                <input type="number" class="form-input" name="sort" value="0">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

async function addCategory(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        icon: formData.get('icon'),
        description: formData.get('description'),
        sort: parseInt(formData.get('sort')) || 0
    };
    
    try {
        const result = await callCloudFunction('admin', { action: 'createCategory', data });
        if (result.success) {
            showSuccess('分类添加成功');
            closeModal();
            loadCategories();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 删除分类
async function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？')) {
        try {
            const result = await callCloudFunction('admin', { action: 'deleteCategory', data: { id } });
            if (result.success) {
                showSuccess('分类删除成功');
                loadCategories();
            } else {
                showError('删除失败: ' + result.error);
            }
        } catch (error) {
            showError('删除失败: ' + error.message);
        }
    }
}

// 编辑分类
function editCategory(id) {
    // 找到要编辑的分类数据
    const category = currentData.categories?.find(c => c._id === id);
    if (!category) {
        showError('找不到分类数据');
        return;
    }

    const content = `
        <h3>编辑分类</h3>
        <form onsubmit="updateCategory(event, '${id}')">
            <div class="form-group">
                <label class="form-label">分类名称</label>
                <input type="text" class="form-input" name="name" value="${category.name}" required>
            </div>
            <div class="form-group">
                <label class="form-label">图标</label>
                <input type="text" class="form-input" name="icon" value="${category.icon}" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description">${category.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label class="form-label">排序</label>
                <input type="number" class="form-input" name="sort" value="${category.sort || 0}">
            </div>
            <div class="form-group">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="show" ${category.status === 'show' ? 'selected' : ''}>显示</option>
                    <option value="hide" ${category.status === 'hide' ? 'selected' : ''}>隐藏</option>
                </select>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">更新</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 更新分类
async function updateCategory(event, id) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        id,
        name: formData.get('name'),
        icon: formData.get('icon'),
        description: formData.get('description'),
        sort: parseInt(formData.get('sort')) || 0,
        status: formData.get('status')
    };

    try {
        const result = await callCloudFunction('admin', { action: 'updateCategory', data });
        if (result.success) {
            showSuccess('分类更新成功');
            closeModal();
            loadCategories();
        } else {
            showError('更新失败: ' + result.error);
        }
    } catch (error) {
        showError('更新失败: ' + error.message);
    }
}

// 表情包管理
function showAddEmojiModal() {
    const content = `
        <h3>添加表情包</h3>
        <form onsubmit="addEmoji(event)">
            <div class="form-group">
                <label class="form-label">标题</label>
                <input type="text" class="form-input" name="title" required>
            </div>
            <div class="form-group">
                <label class="form-label">分类</label>
                <select class="form-select" name="category" required>
                    <option value="">请选择分类</option>
                    <option value="情感表达">情感表达</option>
                    <option value="动物萌宠">动物萌宠</option>
                    <option value="搞笑幽默">搞笑幽默</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">图片URL</label>
                <input type="url" class="form-input" name="imageUrl" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">标签（用逗号分隔）</label>
                <input type="text" class="form-input" name="tags" placeholder="可爱,搞笑,表情">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 添加表情包
async function addEmoji(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const tags = formData.get('tags') ? formData.get('tags').split(',').map(tag => tag.trim()) : [];

    const data = {
        title: formData.get('title'),
        category: formData.get('category'),
        imageUrl: formData.get('imageUrl'),
        description: formData.get('description'),
        tags,
        status: 'published'
    };

    try {
        const result = await callCloudFunction('admin', { action: 'addEmoji', data });
        if (result.success) {
            showSuccess('表情包添加成功');
            closeModal();
            loadEmojis();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 删除表情包
async function deleteEmoji(id) {
    if (confirm('确定要删除这个表情包吗？')) {
        try {
            const result = await callCloudFunction('admin', { action: 'deleteEmoji', data: { id } });
            if (result.success) {
                showSuccess('表情包删除成功');
                loadEmojis();
            } else {
                showError('删除失败: ' + result.error);
            }
        } catch (error) {
            showError('删除失败: ' + error.message);
        }
    }
}

// 编辑表情包
function editEmoji(id) {
    console.log('编辑表情包:', id);
    // 这里可以实现编辑功能，类似编辑分类
}

// 横幅管理
function showAddBannerModal() {
    const content = `
        <h3>添加横幅</h3>
        <form onsubmit="addBanner(event)">
            <div class="form-group">
                <label class="form-label">标题</label>
                <input type="text" class="form-input" name="title" required>
            </div>
            <div class="form-group">
                <label class="form-label">图片URL</label>
                <input type="url" class="form-input" name="imageUrl" required>
            </div>
            <div class="form-group">
                <label class="form-label">链接</label>
                <input type="url" class="form-input" name="link">
            </div>
            <div class="form-group">
                <label class="form-label">优先级</label>
                <input type="number" class="form-input" name="priority" value="1">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 添加横幅
async function addBanner(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        title: formData.get('title'),
        imageUrl: formData.get('imageUrl'),
        link: formData.get('link'),
        priority: parseInt(formData.get('priority')) || 1,
        status: 'show'
    };

    try {
        const result = await callCloudFunction('admin', { action: 'addBanner', data });
        if (result.success) {
            showSuccess('横幅添加成功');
            closeModal();
            loadBanners();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 加载横幅列表
async function loadBanners() {
    try {
        showLoading('bannersContent');

        const result = await callCloudFunction('admin', { action: 'getBannerList' });

        if (result.success) {
            renderBanners(result.data);
        } else {
            showError('加载横幅数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载横幅数据失败: ' + error.message);
    }
}

// 渲染横幅列表
function renderBanners(banners) {
    const content = document.getElementById('bannersContent');

    if (banners.length === 0) {
        content.innerHTML = '<div class="loading">暂无横幅数据</div>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>标题</th>
                    <th>图片</th>
                    <th>状态</th>
                    <th>优先级</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${banners.map(banner => `
                    <tr>
                        <td>${banner.title}</td>
                        <td><img src="${banner.imageUrl}" alt="${banner.title}" style="width: 60px; height: 30px; object-fit: cover; border-radius: 4px;"></td>
                        <td><span class="status-badge status-${banner.status}">${banner.status === 'show' ? '显示' : '隐藏'}</span></td>
                        <td>${banner.priority || 0}</td>
                        <td>${formatDate(banner.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editBanner('${banner._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteBanner('${banner._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    content.innerHTML = tableHTML;
}

// 加载用户列表
async function loadUsers() {
    try {
        showLoading('usersContent');

        const result = await callCloudFunction('admin', { action: 'getUserList' });

        if (result.success) {
            renderUsers(result.data);
        } else {
            showError('加载用户数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载用户数据失败: ' + error.message);
    }
}

// 渲染用户列表
function renderUsers(users) {
    const content = document.getElementById('usersContent');

    if (users.length === 0) {
        content.innerHTML = '<div class="loading">暂无用户数据</div>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>头像</th>
                    <th>昵称</th>
                    <th>角色</th>
                    <th>状态</th>
                    <th>注册时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${users.map(user => `
                    <tr>
                        <td><img src="${user.profile?.avatar || '/images/default-avatar.png'}" alt="${user.profile?.nickname}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;"></td>
                        <td>${user.profile?.nickname || '未设置'}</td>
                        <td><span class="status-badge ${user.auth?.role === 'admin' ? 'status-show' : 'status-published'}">${user.auth?.role || 'user'}</span></td>
                        <td><span class="status-badge status-${user.auth?.status === 'active' ? 'published' : 'draft'}">${user.auth?.status === 'active' ? '正常' : '禁用'}</span></td>
                        <td>${formatDate(user.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editUser('${user._id}')">编辑</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    content.innerHTML = tableHTML;
}

// 占位函数
function editBanner(id) { console.log('编辑横幅:', id); }
function deleteBanner(id) { console.log('删除横幅:', id); }
function editUser(id) { console.log('编辑用户:', id); }
