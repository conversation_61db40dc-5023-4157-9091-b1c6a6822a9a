@echo off
cls

echo ========================================
echo    SIMPLE TEST - DEBUG VERSION
echo ========================================
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)
echo OK: Node.js found

REM Check directory
if not exist "admin-unified" (
    echo ERROR: admin-unified directory not found
    pause
    exit /b 1
)
echo OK: Directory found

cd /d admin-unified

REM Check if index-production.html exists
if not exist "index-production.html" (
    echo ERROR: index-production.html not found
    pause
    exit /b 1
)
echo OK: index-production.html found

REM Create simple server
echo Creating simple server...
echo const http = require('http'); > simple-server.js
echo const fs = require('fs'); >> simple-server.js
echo const path = require('path'); >> simple-server.js
echo const PORT = 8000; >> simple-server.js
echo. >> simple-server.js
echo const server = http.createServer((req, res) =^> { >> simple-server.js
echo   console.log('Request:', req.method, req.url); >> simple-server.js
echo   let filePath = req.url === '/' ? 'index-production.html' : req.url.substring(1); >> simple-server.js
echo   console.log('Serving file:', filePath); >> simple-server.js
echo   fs.readFile(filePath, (err, data) =^> { >> simple-server.js
echo     if (err) { >> simple-server.js
echo       console.log('File error:', err.message); >> simple-server.js
echo       res.writeHead(404); >> simple-server.js
echo       res.end('File not found: ' + filePath); >> simple-server.js
echo       return; >> simple-server.js
echo     } >> simple-server.js
echo     res.writeHead(200, {'Content-Type': 'text/html; charset=utf-8'}); >> simple-server.js
echo     res.end(data); >> simple-server.js
echo   }); >> simple-server.js
echo }); >> simple-server.js
echo. >> simple-server.js
echo server.listen(PORT, () =^> { >> simple-server.js
echo   console.log('Server started at http://localhost:' + PORT); >> simple-server.js
echo   console.log('Press Ctrl+C to stop'); >> simple-server.js
echo }); >> simple-server.js

echo.
echo Starting server...
echo URL: http://localhost:8000
echo.

start http://localhost:8000
node simple-server.js
