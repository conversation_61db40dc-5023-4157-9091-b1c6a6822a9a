/* 登录弹窗样式 */
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 10;
}

.close-icon {
  font-size: 36rpx;
  color: #666;
  line-height: 1;
}

.login-content {
  padding: 80rpx 60rpx 60rpx;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  border-radius: 24rpx;
}

.app-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.login-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.login-actions {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #07c160 0%, #38f9d7 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.login-btn:not(.loading):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.login-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.login-tips {
  text-align: center;
  line-height: 1.6;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.link-text {
  font-size: 24rpx;
  color: #07c160;
}

.error-message {
  background: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 8rpx;
  padding: 20rpx;
  text-align: center;
}

.error-text {
  font-size: 26rpx;
  color: #ff4d4f;
}
