/**
 * 智能缓存管理器
 * 整合LRU缓存，提供多层缓存策略
 */

const { LRUCache } = require('./lruCache.js')

const SmartCache = {
  // 缓存实例
  caches: {
    memory: null,      // 内存缓存 (LRU)
    storage: null,     // 持久化缓存
    temp: null         // 临时缓存
  },

  // 缓存配置
  config: {
    memory: {
      maxSize: 200,
      maxMemory: 30 * 1024 * 1024, // 30MB
      defaultTTL: 15 * 60 * 1000   // 15分钟
    },
    storage: {
      maxSize: 1000,
      defaultTTL: 24 * 60 * 60 * 1000 // 24小时
    },
    temp: {
      maxSize: 50,
      maxMemory: 10 * 1024 * 1024, // 10MB
      defaultTTL: 5 * 60 * 1000    // 5分钟
    }
  },

  // 缓存策略配置
  strategies: {
    emojis: {
      level: 'memory',
      ttl: 30 * 60 * 1000,    // 30分钟
      preload: true
    },
    categories: {
      level: 'memory',
      ttl: 60 * 60 * 1000,    // 1小时
      preload: true
    },
    banners: {
      level: 'memory',
      ttl: 60 * 60 * 1000,    // 1小时
      preload: false
    },
    user_actions: {
      level: 'temp',
      ttl: 10 * 60 * 1000,    // 10分钟
      preload: false
    },
    search_results: {
      level: 'temp',
      ttl: 5 * 60 * 1000,     // 5分钟
      preload: false
    },
    app_config: {
      level: 'storage',
      ttl: 24 * 60 * 60 * 1000, // 24小时
      preload: true
    }
  },

  /**
   * 初始化智能缓存
   */
  init(options = {}) {
    console.log('🧠 初始化智能缓存管理器...')

    // 合并配置
    this.config = { ...this.config, ...options }

    // 初始化各级缓存
    this.caches.memory = new LRUCache(
      this.config.memory.maxSize,
      this.config.memory.maxMemory
    )

    this.caches.temp = new LRUCache(
      this.config.temp.maxSize,
      this.config.temp.maxMemory
    )

    // 初始化持久化缓存
    this.initStorageCache()

    // 启动定期清理
    this.startCleanupTimer()

    console.log('✅ 智能缓存管理器初始化完成')
  },

  /**
   * 获取缓存数据
   */
  get(key, type = null) {
    const strategy = this.getStrategy(key, type)
    const cache = this.caches[strategy.level]

    if (cache) {
      const value = cache.get(key)
      if (value !== null) {
        console.log(`📦 缓存命中: ${key} (${strategy.level})`)
        return value
      }
    }

    // 尝试从其他缓存级别获取
    for (const [level, cacheInstance] of Object.entries(this.caches)) {
      if (level !== strategy.level && cacheInstance) {
        const value = cacheInstance.get(key)
        if (value !== null) {
          console.log(`📦 缓存命中: ${key} (${level} -> ${strategy.level})`)
          // 提升到目标缓存级别
          this.set(key, value, type, strategy.ttl)
          return value
        }
      }
    }

    console.log(`📦 缓存未命中: ${key}`)
    return null
  },

  /**
   * 设置缓存数据
   */
  set(key, value, type = null, ttl = null) {
    const strategy = this.getStrategy(key, type)
    const cache = this.caches[strategy.level]
    const finalTTL = ttl || strategy.ttl

    if (cache) {
      cache.set(key, value, finalTTL)
      console.log(`📦 缓存设置: ${key} (${strategy.level}, TTL: ${finalTTL}ms)`)
    }

    // 如果是重要数据，同时存储到持久化缓存
    if (strategy.preload && strategy.level !== 'storage') {
      this.setStorage(key, value, finalTTL)
    }
  },

  /**
   * 删除缓存数据
   */
  delete(key, type = null) {
    let deleted = false

    // 从所有缓存级别删除
    for (const [level, cache] of Object.entries(this.caches)) {
      if (cache && cache.delete(key)) {
        deleted = true
        console.log(`🗑️ 缓存删除: ${key} (${level})`)
      }
    }

    // 从持久化存储删除
    this.deleteStorage(key)

    return deleted
  },

  /**
   * 检查缓存是否存在
   */
  has(key, type = null) {
    const strategy = this.getStrategy(key, type)
    const cache = this.caches[strategy.level]

    if (cache && cache.has(key)) {
      return true
    }

    // 检查其他缓存级别
    for (const [level, cacheInstance] of Object.entries(this.caches)) {
      if (level !== strategy.level && cacheInstance && cacheInstance.has(key)) {
        return true
      }
    }

    return false
  },

  /**
   * 批量获取缓存数据
   */
  getBatch(keys, type = null) {
    const results = {}
    const missingKeys = []

    for (const key of keys) {
      const value = this.get(key, type)
      if (value !== null) {
        results[key] = value
      } else {
        missingKeys.push(key)
      }
    }

    return {
      results,
      missingKeys,
      hitRate: ((keys.length - missingKeys.length) / keys.length * 100).toFixed(2)
    }
  },

  /**
   * 批量设置缓存数据
   */
  setBatch(data, type = null, ttl = null) {
    const keys = Object.keys(data)
    
    for (const key of keys) {
      this.set(key, data[key], type, ttl)
    }

    console.log(`📦 批量缓存设置: ${keys.length} 项`)
  },

  /**
   * 预加载缓存数据
   */
  async preload(dataLoader, keys, type = null) {
    console.log(`🔥 开始预加载缓存: ${keys.length} 项`)
    
    const missingKeys = keys.filter(key => !this.has(key, type))
    
    if (missingKeys.length === 0) {
      console.log('✅ 所有数据已在缓存中')
      return
    }

    console.log(`🔄 需要加载: ${missingKeys.length} 项`)
    
    try {
      const data = await dataLoader(missingKeys)
      if (data) {
        this.setBatch(data, type)
        console.log(`✅ 预加载完成: ${Object.keys(data).length} 项`)
      }
    } catch (error) {
      console.error('❌ 预加载失败:', error.message)
    }
  },

  /**
   * 获取缓存策略
   */
  getStrategy(key, type) {
    // 根据key前缀或type确定策略
    const keyType = type || this.inferTypeFromKey(key)
    return this.strategies[keyType] || {
      level: 'memory',
      ttl: this.config.memory.defaultTTL,
      preload: false
    }
  },

  /**
   * 从key推断数据类型
   */
  inferTypeFromKey(key) {
    if (key.startsWith('emoji_')) return 'emojis'
    if (key.startsWith('category_')) return 'categories'
    if (key.startsWith('banner_')) return 'banners'
    if (key.startsWith('search_')) return 'search_results'
    if (key.startsWith('user_')) return 'user_actions'
    if (key.startsWith('config_')) return 'app_config'
    return 'default'
  },

  /**
   * 初始化持久化缓存
   */
  initStorageCache() {
    try {
      const stored = wx.getStorageSync('smart_cache_storage') || {}
      this.caches.storage = new Map()
      
      const now = Date.now()
      for (const [key, item] of Object.entries(stored)) {
        if (!item.ttl || now < item.ttl) {
          this.caches.storage.set(key, item)
        }
      }
      
      console.log(`📥 加载持久化缓存: ${this.caches.storage.size} 项`)
    } catch (error) {
      console.warn('⚠️ 持久化缓存加载失败:', error.message)
      this.caches.storage = new Map()
    }
  },

  /**
   * 设置持久化缓存
   */
  setStorage(key, value, ttl) {
    if (!this.caches.storage) return

    const item = {
      value,
      createdAt: Date.now(),
      ttl: ttl ? Date.now() + ttl : null
    }

    this.caches.storage.set(key, item)
    this.saveStorageCache()
  },

  /**
   * 删除持久化缓存
   */
  deleteStorage(key) {
    if (!this.caches.storage) return false

    const deleted = this.caches.storage.delete(key)
    if (deleted) {
      this.saveStorageCache()
    }
    return deleted
  },

  /**
   * 保存持久化缓存
   */
  saveStorageCache() {
    try {
      const data = {}
      for (const [key, item] of this.caches.storage) {
        data[key] = item
      }
      wx.setStorageSync('smart_cache_storage', data)
    } catch (error) {
      console.warn('⚠️ 持久化缓存保存失败:', error.message)
    }
  },

  /**
   * 启动定期清理
   */
  startCleanupTimer() {
    // 每5分钟清理一次
    setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  },

  /**
   * 清理过期缓存
   */
  cleanup() {
    console.log('🧹 开始缓存清理...')
    
    // 清理内存缓存
    for (const [level, cache] of Object.entries(this.caches)) {
      if (cache && typeof cache.cleanExpired === 'function') {
        cache.cleanExpired()
      }
    }

    // 清理持久化缓存
    if (this.caches.storage) {
      const now = Date.now()
      let cleaned = 0
      
      for (const [key, item] of this.caches.storage) {
        if (item.ttl && now > item.ttl) {
          this.caches.storage.delete(key)
          cleaned++
        }
      }
      
      if (cleaned > 0) {
        this.saveStorageCache()
        console.log(`🧹 清理持久化缓存: ${cleaned} 项`)
      }
    }
  },

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const stats = {}
    
    for (const [level, cache] of Object.entries(this.caches)) {
      if (cache && typeof cache.getStats === 'function') {
        stats[level] = cache.getStats()
      } else if (cache instanceof Map) {
        stats[level] = {
          size: cache.size,
          type: 'storage'
        }
      }
    }
    
    return stats
  },

  /**
   * 清空所有缓存
   */
  clear() {
    for (const [level, cache] of Object.entries(this.caches)) {
      if (cache && typeof cache.clear === 'function') {
        cache.clear()
      } else if (cache instanceof Map) {
        cache.clear()
      }
    }
    
    // 清空持久化存储
    try {
      wx.removeStorageSync('smart_cache_storage')
    } catch (error) {
      console.warn('⚠️ 清空持久化缓存失败:', error.message)
    }
    
    console.log('🧹 所有缓存已清空')
  }
}

module.exports = {
  SmartCache
}
