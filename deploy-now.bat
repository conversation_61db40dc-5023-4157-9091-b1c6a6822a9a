@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 表情包管理后台 - 云函数部署工具
echo ========================================
echo.

echo 📋 准备部署以下云函数:
echo    - adminAPI (管理后台API)
echo    - dataAPI (数据API)  
echo    - initDatabase (数据库初始化)
echo.

echo 🔧 环境ID: cloud1-5g6pvnpl88dc0142
echo.

pause

echo 📦 开始部署云函数...
echo.

echo [1/3] 部署 adminAPI...
cd cloudfunctions\adminAPI
tcb fn deploy adminAPI --envId cloud1-5g6pvnpl88dc0142
if %errorlevel% neq 0 (
    echo ❌ adminAPI 部署失败
    goto :error
)
echo ✅ adminAPI 部署成功
echo.

echo [2/3] 部署 dataAPI...
cd ..\dataAPI
tcb fn deploy dataAPI --envId cloud1-5g6pvnpl88dc0142
if %errorlevel% neq 0 (
    echo ❌ dataAPI 部署失败
    goto :error
)
echo ✅ dataAPI 部署成功
echo.

echo [3/3] 部署 initDatabase...
cd ..\initDatabase
tcb fn deploy initDatabase --envId cloud1-5g6pvnpl88dc0142
if %errorlevel% neq 0 (
    echo ❌ initDatabase 部署失败
    goto :error
)
echo ✅ initDatabase 部署成功
echo.

cd ..\..

echo ========================================
echo 🎉 所有云函数部署完成！
echo ========================================
echo.
echo 📝 接下来的步骤:
echo    1. 刷新管理后台页面
echo    2. 点击"初始化测试数据"按钮
echo    3. 等待数据初始化完成
echo    4. 开始使用真实数据管理功能
echo.
echo 🌐 管理后台地址:
echo    https://cloud1-5g6pvnpl88dc0142-1367610204.tcloudbaseapp.com/
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 部署失败！
echo ========================================
echo.
echo 💡 可能的解决方案:
echo    1. 确保已安装腾讯云CLI: npm install -g @cloudbase/cli
echo    2. 确保已登录: tcb login
echo    3. 检查网络连接
echo    4. 使用微信开发者工具手动部署
echo.
echo 📖 详细说明请查看: deploy-functions-manual.md
echo.

:end
pause
