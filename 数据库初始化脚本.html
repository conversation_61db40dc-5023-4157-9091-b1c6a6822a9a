<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化脚本 - 表情包小程序</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .step h3 {
            color: #495057;
            margin-top: 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .checklist {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .checklist h4 {
            margin-top: 0;
            color: #856404;
        }
        .checklist ul {
            margin: 10px 0;
        }
        .checklist li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库初始化脚本</h1>
            <p>表情包小程序 - 一键创建所有数据库集合和初始化数据</p>
            <div class="status info">
                <strong>云环境ID:</strong> cloud1-5g6pvnpl88dc0142<br>
                <strong>需要创建:</strong> 11个数据库集合<br>
                <strong>初始化数据:</strong> 分类、轮播图、系统配置
            </div>
        </div>

        <div class="checklist">
            <h4>📋 执行前检查清单</h4>
            <ul>
                <li>✅ 微信开发者工具已打开项目</li>
                <li>✅ 云开发服务已开通</li>
                <li>✅ 云环境ID确认为: cloud1-5g6pvnpl88dc0142</li>
                <li>✅ 网络连接正常</li>
            </ul>
        </div>

        <div class="step">
            <h3>🎯 第一步：创建数据库集合</h3>
            <p>在微信开发者工具的云开发控制台中，逐一创建以下集合：</p>
            
            <div class="code-block">
1. emojis - 表情包数据
2. categories - 分类数据  
3. users - 用户数据
4. user_likes - 用户点赞记录
5. user_collections - 用户收藏记录
6. user_actions - 用户行为记录
7. banners - 轮播图数据
8. operation_logs - 操作日志
9. upload_records - 上传记录
10. daily_reports - 日报数据
11. system_configs - 系统配置
            </div>
            
            <button class="btn" onclick="copyCollectionNames()">📋 复制集合名称列表</button>
            <div id="collection-status" class="status" style="display:none;"></div>
        </div>

        <div class="step">
            <h3>🔐 第二步：配置数据库权限</h3>
            <p>为每个集合配置正确的读写权限：</p>
            
            <button class="btn" onclick="showPermissionConfig()">🔧 显示权限配置</button>
            <div id="permission-config" style="display:none;">
                <h4>通用权限配置（适用于大部分集合）：</h4>
                <div class="code-block" id="common-permission">
{
  "read": true,
  "write": "auth != null"
}
                </div>
                <button class="btn btn-success" onclick="copyPermission('common')">复制通用权限</button>
                
                <h4>用户相关集合权限（users, user_likes, user_collections, user_actions）：</h4>
                <div class="code-block" id="user-permission">
{
  "read": "doc.openid == auth.openid || auth != null",
  "write": "doc.openid == auth.openid || auth != null"
}
                </div>
                <button class="btn btn-success" onclick="copyPermission('user')">复制用户权限</button>
            </div>
        </div>

        <div class="step">
            <h3>📊 第三步：导入初始化数据</h3>
            <p>导入基础数据，包括分类和轮播图：</p>
            
            <button class="btn" onclick="showInitData()">📥 显示初始化数据</button>
            <div id="init-data" style="display:none;">
                <h4>分类数据（categories集合）：</h4>
                <div class="code-block" id="categories-data" style="max-height: 300px; overflow-y: auto;">
[
  {
    "_id": "funny",
    "name": "搞笑幽默",
    "icon": "😂",
    "color": "#FF6B6B",
    "description": "搞笑幽默类表情包，让你笑到停不下来",
    "sortOrder": 1,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "cute",
    "name": "可爱萌宠",
    "icon": "😍",
    "color": "#FF69B4",
    "description": "可爱萌宠类表情包，萌化你的心",
    "sortOrder": 2,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "emotion",
    "name": "情感表达",
    "icon": "❤️",
    "color": "#FF4757",
    "description": "情感表达类表情包，传递你的心情",
    "sortOrder": 3,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "festival",
    "name": "节日庆典",
    "icon": "🎉",
    "color": "#5F27CD",
    "description": "节日庆典类表情包，庆祝每个特殊时刻",
    "sortOrder": 4,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "hot",
    "name": "网络热梗",
    "icon": "🔥",
    "color": "#FFA726",
    "description": "网络热梗类表情包，紧跟时代潮流",
    "sortOrder": 5,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  },
  {
    "_id": "2d",
    "name": "动漫二次元",
    "icon": "🌟",
    "color": "#667EEA",
    "description": "动漫二次元类表情包，二次元的快乐",
    "sortOrder": 6,
    "status": "active",
    "emojiCount": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  }
]
                </div>
                <button class="btn btn-success" onclick="copyInitData('categories')">复制分类数据</button>
                
                <h4>轮播图数据（banners集合）：</h4>
                <div class="code-block" id="banners-data" style="max-height: 200px; overflow-y: auto;">
[
  {
    "title": "欢迎使用表情包小程序",
    "subtitle": "海量表情包，随心下载",
    "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600",
    "linkType": "category",
    "linkValue": "hot",
    "buttonText": "立即体验",
    "sortOrder": 1,
    "status": "active",
    "startTime": "2024-01-01T00:00:00.000Z",
    "endTime": "2024-12-31T23:59:59.000Z",
    "clickCount": 0,
    "impressions": 0,
    "createTime": "2024-01-01T00:00:00.000Z",
    "updateTime": "2024-01-01T00:00:00.000Z"
  }
]
                </div>
                <button class="btn btn-success" onclick="copyInitData('banners')">复制轮播图数据</button>
            </div>
        </div>

        <div class="step">
            <h3>✅ 第四步：验证初始化结果</h3>
            <p>完成上述步骤后，请验证以下内容：</p>
            
            <div class="checklist">
                <h4>验证清单：</h4>
                <ul>
                    <li>□ 所有11个集合已创建</li>
                    <li>□ 权限配置已正确设置</li>
                    <li>□ 分类数据已导入（6条记录）</li>
                    <li>□ 轮播图数据已导入（1条记录）</li>
                    <li>□ 在云开发控制台可以看到数据</li>
                </ul>
            </div>
            
            <div class="status success">
                <strong>🎉 初始化完成后，你就可以：</strong><br>
                1. 启动管理后台进行内容管理<br>
                2. 在小程序中看到分类和轮播图<br>
                3. 开始添加表情包内容
            </div>
        </div>

        <div class="step">
            <h3>🚀 下一步操作</h3>
            <p>数据库初始化完成后，请继续：</p>
            <ol>
                <li>部署所有云函数（参考《云函数部署清单.md》）</li>
                <li>启动管理后台（参考《管理后台配置指南.md》）</li>
                <li>测试系统功能</li>
            </ol>
        </div>
    </div>

    <script>
        function copyCollectionNames() {
            const collections = `emojis
categories
users
user_likes
user_collections
user_actions
banners
operation_logs
upload_records
daily_reports
system_configs`;
            
            navigator.clipboard.writeText(collections).then(() => {
                document.getElementById('collection-status').style.display = 'block';
                document.getElementById('collection-status').className = 'status success';
                document.getElementById('collection-status').innerHTML = '✅ 集合名称列表已复制到剪贴板！';
            });
        }

        function showPermissionConfig() {
            const element = document.getElementById('permission-config');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }

        function copyPermission(type) {
            const permissions = {
                common: `{
  "read": true,
  "write": "auth != null"
}`,
                user: `{
  "read": "doc.openid == auth.openid || auth != null",
  "write": "doc.openid == auth.openid || auth != null"
}`
            };
            
            navigator.clipboard.writeText(permissions[type]).then(() => {
                alert('✅ 权限配置已复制到剪贴板！');
            });
        }

        function showInitData() {
            const element = document.getElementById('init-data');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }

        function copyInitData(type) {
            const elementId = type + '-data';
            const text = document.getElementById(elementId).textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                alert(`✅ ${type === 'categories' ? '分类' : '轮播图'}数据已复制到剪贴板！`);
            });
        }
    </script>
</body>
</html>
