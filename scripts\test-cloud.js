// 测试云开发功能的简单脚本
// 可以在微信开发者工具的调试器中运行

// 测试云开发初始化
console.log('🔄 测试云开发功能')

// 测试云函数调用
async function testCloudFunctions() {
  try {
    // 测试 getUserStats 云函数
    console.log('测试 getUserStats 云函数...')
    const statsRes = await wx.cloud.callFunction({
      name: 'getUserStats',
      data: {
        action: 'getAllUserData'
      }
    })
    console.log('getUserStats 结果:', statsRes)
    
    // 测试 updateUserStats 云函数
    console.log('测试 updateUserStats 云函数...')
    const updateRes = await wx.cloud.callFunction({
      name: 'updateUserStats',
      data: {
        action: 'updateLike',
        emojiId: 'test-emoji-1',
        isLiked: true
      }
    })
    console.log('updateUserStats 结果:', updateRes)
    
    console.log('✅ 云函数测试完成')
  } catch (error) {
    console.error('❌ 云函数测试失败:', error)
  }
}

// 在小程序中运行测试
// testCloudFunctions()