// pages/category-detail/category-detail.js
const { DataManager } = require('../../utils/newDataManager.js')

Page({
  data: {
    categoryId: '',
    categoryName: '',
    emojiList: []
  },

  async onLoad(options) {
    const { id, name } = options

    // 解码参数
    const categoryId = decodeURIComponent(id || '')
    const categoryName = decodeURIComponent(name || '')

    console.log('分类详情页面参数:', { id, name, categoryId, categoryName })

    this.setData({
      categoryId: categoryId,
      categoryName: categoryName
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: categoryName || '分类详情'
    })

    await this.loadCategoryEmojis(categoryId)
  },

  async onShow() {
    // 页面显示时刷新数据，确保统计数据是最新的
    if (this.data.categoryId) {
      await this.loadCategoryEmojis(this.data.categoryId)
    }
  },

  async loadCategoryEmojis(categoryId) {
    console.log(`🔄 加载分类 ${categoryId} 的表情包数据`)
    wx.showLoading({ title: '加载中...' })

    try {
      // 直接调用云函数，绕过 DataManager
      console.log(`☁️ 直接调用云函数获取分类 ${categoryId} 的表情包`)

      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: categoryId,
            page: 1,
            limit: 50
          }
        }
      })

      console.log(`📦 云函数返回结果:`, result)

      if (!result.result || !result.result.success) {
        console.error(`❌ 云函数调用失败:`, result.result?.message)
        this.setData({ emojiList: [] })
        wx.showToast({
          title: result.result?.message || '获取数据失败',
          icon: 'none'
        })
        return
      }

      const categoryEmojis = result.result.data || []
      console.log(`📦 获取到 ${categoryEmojis.length} 个表情包:`, categoryEmojis)

      if (categoryEmojis.length === 0) {
        console.warn(`⚠️ 分类 ${categoryId} 下没有找到表情包`)
        this.setData({ emojiList: [] })
        wx.showToast({
          title: '该分类暂无表情包',
          icon: 'none'
        })
        return
      }

      // 添加格式化的统计数据
      const formattedEmojis = categoryEmojis.map(emoji => ({
        ...emoji,
        id: emoji._id || emoji.id,
        name: emoji.title || emoji.name || '未知表情包',
        imageUrl: emoji.imageUrl || emoji.url || '',
        tags: emoji.tags || [], // 确保包含标签数据
        likesText: this.formatNumber(emoji.likes || 0),
        collectionsText: this.formatNumber(emoji.collections || 0),
        isLiked: false,
        isCollected: false
      }))

      this.setData({
        emojiList: formattedEmojis
      })

      console.log(`✅ 分类 ${categoryId} 加载了 ${formattedEmojis.length} 个表情包`)
    } catch (error) {
      console.error(`❌ 加载分类 ${categoryId} 表情包失败:`, error)
      this.setData({ emojiList: [] })
      wx.showToast({
        title: '加载失败: ' + (error.message || '未知错误'),
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 格式化数字显示
  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },

  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    })
  }
})