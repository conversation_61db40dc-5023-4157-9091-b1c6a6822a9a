<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - V1.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
            margin-right: 30px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            font-size: 14px;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .connection-status.success {
            background: #f0f9ff;
            color: #0369a1;
        }

        .connection-status.error {
            background: #fef2f2;
            color: #dc2626;
        }

        .connection-status.warning {
            background: #fffbeb;
            color: #d97706;
        }

        .connection-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            margin-right: 6px;
        }

        .connection-indicator.pulse {
            animation: pulse 1s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-info {
            font-size: 14px;
            color: #666;
        }

        .logout-btn {
            padding: 6px 12px;
            background: #f3f4f6;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            color: #374151;
            transition: background 0.2s;
        }

        .logout-btn:hover {
            background: #e5e7eb;
        }

        .main-container {
            margin-top: 60px;
            padding: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .section-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
        }

        .section-content {
            padding: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .data-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .data-item {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-item:hover {
            background: #f9fafb;
        }

        .data-info {
            flex: 1;
        }

        .data-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .data-meta {
            font-size: 12px;
            color: #6b7280;
        }

        .data-actions {
            display: flex;
            gap: 8px;
        }

        .toast {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: #10b981;
        }

        .toast.error {
            background: #ef4444;
        }

        .toast.info {
            background: #3b82f6;
        }

        .toast.warning {
            background: #f59e0b;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 3000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="header-left">
            <div class="logo">管理后台 V1.0</div>
            <div class="connection-status success" id="connectionStatus">
                <div class="connection-indicator" id="connectionIndicator"></div>
                已连接
            </div>
        </div>
        <div class="header-right">
            <div class="admin-info" id="adminInfo">
                加载中...
            </div>
            <button class="logout-btn" onclick="handleLogout()">登出</button>
        </div>
    </header>

    <!-- 主内容 -->
    <div class="main-container">
        <!-- 统计卡片 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="categoriesCount">-</div>
                <div class="stat-label">分类数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="emojisCount">-</div>
                <div class="stat-label">表情包数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="bannersCount">-</div>
                <div class="stat-label">横幅数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingCount">-</div>
                <div class="stat-label">待同步通知</div>
            </div>
        </div>

        <!-- 分类管理 -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">分类管理</h2>
                <div>
                    <button class="btn btn-secondary" onclick="refreshCategories()">刷新</button>
                    <button class="btn btn-primary" onclick="showCreateCategoryModal()">新增分类</button>
                </div>
            </div>
            <div class="section-content">
                <div class="data-list" id="categoriesList">
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        加载中...
                    </div>
                </div>
            </div>
        </div>

        <!-- 表情包管理 -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">表情包管理</h2>
                <div>
                    <button class="btn btn-secondary" onclick="refreshEmojis()">刷新</button>
                    <button class="btn btn-primary" onclick="showCreateEmojiModal()">新增表情包</button>
                </div>
            </div>
            <div class="section-content">
                <div class="data-list" id="emojisList">
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建分类模态框 -->
    <div class="modal" id="createCategoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">新增分类</h3>
                <button class="close-btn" onclick="closeModal('createCategoryModal')">&times;</button>
            </div>
            <form id="createCategoryForm">
                <div class="form-group">
                    <label class="form-label">分类名称</label>
                    <input type="text" class="form-input" id="categoryName" placeholder="请输入分类名称" required>
                </div>
                <div class="form-group">
                    <label class="form-label">分类图标</label>
                    <input type="text" class="form-input" id="categoryIcon" placeholder="请输入图标名称或URL">
                </div>
                <div class="form-group">
                    <label class="form-label">分类描述</label>
                    <input type="text" class="form-input" id="categoryDescription" placeholder="请输入分类描述">
                </div>
                <div class="form-group">
                    <label class="form-label">排序</label>
                    <input type="number" class="form-input" id="categorySort" value="0" min="0">
                </div>
            </form>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('createCategoryModal')">取消</button>
                <button class="btn btn-primary" onclick="createCategory()">创建</button>
            </div>
        </div>
    </div>

    <!-- Toast容器 -->
    <div id="toastContainer"></div>

    <!-- CloudBase Web SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.10.10/tcb.js"></script>
    
    <!-- 管理器脚本 -->
    <script src="./js/auth-manager.js"></script>
    <script src="./js/api-manager.js"></script>
    <script src="./js/realtime-manager.js"></script>

    <script>
        // 管理后台主页面逻辑
        class AdminDashboard {
            constructor() {
                this.data = {
                    categories: [],
                    emojis: [],
                    banners: [],
                    stats: {}
                };
                
                this.init();
            }

            async init() {
                console.log('🚀 管理后台初始化...');
                
                // 检查登录状态
                if (!window.authManager.isLoggedIn()) {
                    window.location.href = './login.html';
                    return;
                }

                // 显示管理员信息
                this.updateAdminInfo();
                
                // 加载初始数据
                await this.loadInitialData();
                
                // 设置事件监听
                this.setupEventListeners();
                
                console.log('✅ 管理后台初始化完成');
            }

            updateAdminInfo() {
                const admin = window.authManager.getCurrentAdmin();
                const adminInfoEl = document.getElementById('adminInfo');
                if (adminInfoEl) {
                    adminInfoEl.textContent = `${admin.adminId} (${admin.permissions.join(', ')})`;
                }
            }

            async loadInitialData() {
                try {
                    console.log('📊 加载初始数据...');
                    
                    // 并行加载所有数据
                    const [statsResult, categoriesResult, emojisResult] = await Promise.all([
                        window.apiManager.getSystemStats(),
                        window.apiManager.getCategories(),
                        window.apiManager.getEmojis()
                    ]);

                    // 更新统计数据
                    if (statsResult.success) {
                        this.updateStats(statsResult.data);
                    }

                    // 更新分类数据
                    if (categoriesResult.success) {
                        this.data.categories = categoriesResult.data;
                        this.renderCategories();
                    }

                    // 更新表情包数据
                    if (emojisResult.success) {
                        this.data.emojis = emojisResult.data;
                        this.renderEmojis();
                    }

                    console.log('✅ 初始数据加载完成');
                    
                } catch (error) {
                    console.error('❌ 加载初始数据失败:', error);
                    this.showToast('数据加载失败', 'error');
                }
            }

            setupEventListeners() {
                // 监听数据更新事件
                window.addEventListener('dataUpdate', (event) => {
                    const { dataType, data, operation } = event.detail;
                    this.handleDataUpdate(dataType, data, operation);
                });

                // 监听数据同步事件
                window.addEventListener('dataSync', (event) => {
                    const { dataType, operation } = event.detail;
                    console.log(`🔄 数据同步: ${dataType} - ${operation}`);
                });
            }

            handleDataUpdate(dataType, data, operation) {
                console.log(`📢 处理数据更新: ${dataType} - ${operation}`);
                
                switch (dataType) {
                    case 'categories':
                        this.data.categories = data;
                        this.renderCategories();
                        break;
                    case 'emojis':
                        this.data.emojis = data;
                        this.renderEmojis();
                        break;
                    case 'banners':
                        this.data.banners = data;
                        break;
                }

                // 刷新统计数据
                this.refreshStats();
            }

            updateStats(stats) {
                document.getElementById('categoriesCount').textContent = stats.categories || 0;
                document.getElementById('emojisCount').textContent = stats.emojis || 0;
                document.getElementById('bannersCount').textContent = stats.banners || 0;
                document.getElementById('pendingCount').textContent = stats.pendingNotifications || 0;
            }

            renderCategories() {
                const container = document.getElementById('categoriesList');
                
                if (this.data.categories.length === 0) {
                    container.innerHTML = '<div class="loading">暂无分类数据</div>';
                    return;
                }

                const html = this.data.categories.map(category => `
                    <div class="data-item">
                        <div class="data-info">
                            <div class="data-name">${category.name}</div>
                            <div class="data-meta">
                                ${category.icon ? `图标: ${category.icon} | ` : ''}
                                创建时间: ${new Date(category.createTime).toLocaleString()}
                            </div>
                        </div>
                        <div class="data-actions">
                            <button class="btn btn-secondary" onclick="editCategory('${category.id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteCategory('${category.id}')">删除</button>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            renderEmojis() {
                const container = document.getElementById('emojisList');
                
                if (this.data.emojis.length === 0) {
                    container.innerHTML = '<div class="loading">暂无表情包数据</div>';
                    return;
                }

                const html = this.data.emojis.map(emoji => `
                    <div class="data-item">
                        <div class="data-info">
                            <div class="data-name">${emoji.title || emoji.name || '未命名'}</div>
                            <div class="data-meta">
                                分类: ${emoji.category || '未分类'} | 
                                创建时间: ${new Date(emoji.createTime || Date.now()).toLocaleString()}
                            </div>
                        </div>
                        <div class="data-actions">
                            <button class="btn btn-secondary" onclick="editEmoji('${emoji.id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteEmoji('${emoji.id}')">删除</button>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = html;
            }

            async refreshStats() {
                try {
                    const result = await window.apiManager.getSystemStats();
                    if (result.success) {
                        this.updateStats(result.data);
                    }
                } catch (error) {
                    console.error('刷新统计数据失败:', error);
                }
            }

            showToast(message, type = 'info', duration = 3000) {
                const container = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;
                
                container.appendChild(toast);
                
                // 显示动画
                setTimeout(() => toast.classList.add('show'), 100);
                
                // 自动隐藏
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => container.removeChild(toast), 300);
                }, duration);
            }
        }

        // 全局函数
        async function handleLogout() {
            if (confirm('确定要登出吗？')) {
                await window.authManager.logout();
            }
        }

        async function refreshCategories() {
            try {
                const result = await window.apiManager.getCategories();
                if (result.success) {
                    window.dashboard.data.categories = result.data;
                    window.dashboard.renderCategories();
                    window.dashboard.showToast('分类数据刷新成功', 'success');
                }
            } catch (error) {
                window.dashboard.showToast('刷新失败', 'error');
            }
        }

        async function refreshEmojis() {
            try {
                const result = await window.apiManager.getEmojis();
                if (result.success) {
                    window.dashboard.data.emojis = result.data;
                    window.dashboard.renderEmojis();
                    window.dashboard.showToast('表情包数据刷新成功', 'success');
                }
            } catch (error) {
                window.dashboard.showToast('刷新失败', 'error');
            }
        }

        function showCreateCategoryModal() {
            document.getElementById('createCategoryModal').style.display = 'block';
        }

        function showCreateEmojiModal() {
            window.dashboard.showToast('表情包创建功能开发中', 'info');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        async function createCategory() {
            const name = document.getElementById('categoryName').value.trim();
            const icon = document.getElementById('categoryIcon').value.trim();
            const description = document.getElementById('categoryDescription').value.trim();
            const sort = parseInt(document.getElementById('categorySort').value) || 0;

            if (!name) {
                window.dashboard.showToast('请输入分类名称', 'error');
                return;
            }

            try {
                const result = await window.apiManager.createCategory({
                    name,
                    icon,
                    description,
                    sort
                });

                if (result.success) {
                    window.dashboard.showToast('分类创建成功', 'success');
                    closeModal('createCategoryModal');
                    
                    // 清空表单
                    document.getElementById('createCategoryForm').reset();
                    
                    // 刷新数据（实时监听会自动更新，这里是备用）
                    setTimeout(() => refreshCategories(), 1000);
                } else {
                    window.dashboard.showToast(result.error || '创建失败', 'error');
                }
            } catch (error) {
                window.dashboard.showToast('创建失败', 'error');
            }
        }

        function editCategory(categoryId) {
            window.dashboard.showToast('编辑功能开发中', 'info');
        }

        async function deleteCategory(categoryId) {
            if (!confirm('确定要删除这个分类吗？')) {
                return;
            }

            try {
                const result = await window.apiManager.deleteCategory(categoryId);
                if (result.success) {
                    window.dashboard.showToast('分类删除成功', 'success');
                } else {
                    window.dashboard.showToast(result.error || '删除失败', 'error');
                }
            } catch (error) {
                window.dashboard.showToast('删除失败', 'error');
            }
        }

        function editEmoji(emojiId) {
            window.dashboard.showToast('编辑功能开发中', 'info');
        }

        function deleteEmoji(emojiId) {
            window.dashboard.showToast('删除功能开发中', 'info');
        }

        // 全局Toast函数
        window.showToast = function(message, type, duration) {
            if (window.dashboard) {
                window.dashboard.showToast(message, type, duration);
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.dashboard = new AdminDashboard();
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (event) => {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
    </script>
</body>
</html>
