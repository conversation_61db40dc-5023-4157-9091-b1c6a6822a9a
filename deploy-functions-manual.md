# 手动部署云函数指南

## 方法1：使用微信开发者工具（推荐）

### 步骤：
1. **打开微信开发者工具**
2. **导入项目**：选择你的表情包项目文件夹
3. **点击"云开发"**
4. **选择"云函数"**
5. **部署以下云函数**：
   - 右键 `adminAPI` → "上传并部署"
   - 右键 `dataAPI` → "上传并部署"  
   - 右键 `initDatabase` → "上传并部署"

## 方法2：使用命令行工具

### 安装腾讯云CLI：
```bash
npm install -g @cloudbase/cli
```

### 登录：
```bash
tcb login
```

### 部署云函数：
```bash
cd cloudfunctions/adminAPI
tcb fn deploy adminAPI --envId cloud1-5g6pvnpl88dc0142

cd ../dataAPI  
tcb fn deploy dataAPI --envId cloud1-5g6pvnpl88dc0142

cd ../initDatabase
tcb fn deploy initDatabase --envId cloud1-5g6pvnpl88dc0142
```

## 方法3：批量部署脚本

创建 `deploy.bat` 文件：
```batch
@echo off
echo 部署云函数...

cd cloudfunctions\adminAPI
call tcb fn deploy adminAPI --envId cloud1-5g6pvnpl88dc0142

cd ..\dataAPI
call tcb fn deploy dataAPI --envId cloud1-5g6pvnpl88dc0142

cd ..\initDatabase  
call tcb fn deploy initDatabase --envId cloud1-5g6pvnpl88dc0142

echo 部署完成！
pause
```

## 验证部署成功

### 在云开发控制台检查：
1. 打开 https://console.cloud.tencent.com/tcb
2. 选择环境 `cloud1-5g6pvnpl88dc0142`
3. 点击"云函数"
4. 确认看到3个函数且状态为"正常"

### 测试云函数：
1. 在云函数列表中点击 `adminAPI`
2. 点击"测试"
3. 输入测试数据：
```json
{
  "action": "testDatabase"
}
```
4. 点击"执行"，应该返回成功结果

## 初始化数据库

部署完成后：
1. 刷新管理后台页面
2. 点击"初始化测试数据"按钮
3. 等待初始化完成
4. 刷新页面查看真实数据

## 常见问题

### Q: 部署失败怎么办？
A: 检查网络连接，确认环境ID正确，重试部署

### Q: 云函数显示"异常"状态？
A: 查看函数日志，通常是代码错误或权限问题

### Q: 初始化数据失败？
A: 检查数据库权限设置，确保云函数有读写权限

## 成功标志

✅ 云函数部署成功
✅ 管理后台显示真实统计数据  
✅ 可以正常增删改查数据
✅ 没有"模拟数据"提示
