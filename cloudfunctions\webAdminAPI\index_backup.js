const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// Web端专用管理API - 无需权限验证
exports.main = async (event, context) => {
  console.log('🌐 Web端管理API调用:', event)
  
  const { action, data, adminPassword } = event
  
  // 简单的密码验证
  if (adminPassword !== 'admin123456') {
    return {
      success: false,
      error: '管理员密码错误',
      code: 403
    }
  }
  
  console.log('✅ 密码验证通过，执行操作:', action)
  
  try {
    switch (action) {
      case 'getStats':
        return await getStats()
      case 'createCategory':
        return await createCategory(data)
      case 'getCategoryList':
        return await getCategoryList()
      case 'getCategories':
        return await getCategoryList() // 兼容性别名
      case 'addEmoji':
        return await addEmoji(data)
      case 'getEmojis':
        return await getEmojis(data)
      case 'deleteEmoji':
        return await deleteEmoji(data)
      case 'deleteCategory':
        return await deleteCategory(data)
      case 'syncData':
        return await syncData(data)
      case 'clearAllData':
        return await clearAllData()
      default:
        return { success: false, error: '未知操作: ' + action }
    }
  } catch (error) {
    console.error('操作失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取统计数据
async function getStats() {
  try {
    const usersResult = await db.collection('users').count()
    const emojisResult = await db.collection('emojis').count()
    const categoriesResult = await db.collection('categories').count()
    
    return {
      success: true,
      data: {
        usersCount: usersResult.total || 0,
        emojisCount: emojisResult.total || 0,
        categoriesCount: categoriesResult.total || 0,
        timestamp: new Date()
      }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 创建分类
async function createCategory(data) {
  try {
    const { name, icon, description, sort = 0 } = data
    
    const result = await db.collection('categories').add({
      data: {
        name,
        icon,
        description: description || '',
        sort,
        status: 'active',
        emojiCount: 0,
        createTime: new Date(),
        updateTime: new Date()
      }
    })
    
    return { success: true, message: '分类创建成功', id: result._id }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 获取分类列表
async function getCategoryList() {
  try {
    const categories = await db.collection('categories')
      .orderBy('sort', 'asc')
      .get()
    
    return { success: true, data: categories.data }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 添加表情包
async function addEmoji(data) {
  try {
    const {
      title, name,
      categoryId,
      category,
      tags,
      imageUrl, fileUrl,
      description,
      author
    } = data

    // 获取分类信息（如果没有提供category）
    let categoryName = category
    if (!categoryName && categoryId) {
      try {
        const categoryDoc = await db.collection('categories').doc(categoryId).get()
        if (categoryDoc.data) {
          categoryName = categoryDoc.data.name
        }
      } catch (error) {
        console.warn('获取分类名称失败:', error)
      }
    }

    const result = await db.collection('emojis').add({
      data: {
        title: title || name,           // 使用 title，兼容 name
        categoryId,
        category: categoryName || '未分类',
        imageUrl: imageUrl || fileUrl,  // 使用 imageUrl，兼容 fileUrl
        tags: tags || [],
        description: description || '',
        author: author || '管理员',
        status: 'published',
        likes: 0,                       // 使用 likes 而不是 likeCount
        collections: 0,
        downloads: 0,                   // 使用 downloads 而不是 downloadCount
        createTime: new Date(),
        updateTime: new Date()
      }
    })

    return { success: true, message: '表情包添加成功', id: result._id }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 获取表情包列表
async function getEmojis(data) {
  try {
    const { page = 1, limit = 20 } = data || {}
    const skip = (page - 1) * limit
    
    const emojis = await db.collection('emojis')
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()
    
    return { success: true, data: emojis.data }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 删除表情包
async function deleteEmoji(data) {
  try {
    const { id } = data
    await db.collection('emojis').doc(id).remove()
    return { success: true, message: '表情包删除成功' }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 删除分类
async function deleteCategory(data) {
  try {
    const { id } = data
    await db.collection('categories').doc(id).remove()
    return { success: true, message: '分类删除成功' }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 数据同步功能
async function syncData(data) {
  try {
    const { type, data: syncData } = data
    console.log('🔄 开始同步数据:', type, '数量:', syncData?.length || 0)

    switch (type) {
      case 'categories':
        return await syncCategories(syncData)
      case 'emojis':
        return await syncEmojis(syncData)
      case 'banners':
        return await syncBanners(syncData)
      default:
        return { success: false, error: '未知的同步类型: ' + type }
    }
  } catch (error) {
    console.error('同步失败:', error)
    return { success: false, error: error.message }
  }
}

// 同步分类数据 - 全量替换模式
async function syncCategories(categories) {
  try {
    console.log('🔄 开始全量同步分类数据，数量:', categories.length)

    // 第一步：清空云数据库中的所有分类数据
    const removeResult = await db.collection('categories').where({}).remove()
    console.log('✅ 清空云数据库分类数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (categories.length > 0) {
      for (const category of categories) {
        try {
          await db.collection('categories').add({
            data: {
              name: category.name,
              icon: category.icon || '',
              description: category.description || '',
              sort: category.sort || 0,
              status: 'active',
              emojiCount: 0,
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入分类失败:', category.name, error)
        }
      }
    }

    console.log('🎉 分类数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: categories.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步分类数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 同步表情包数据 - 全量替换模式
async function syncEmojis(emojis) {
  try {
    console.log('🔄 开始全量同步表情包数据，数量:', emojis.length)

    // 第一步：清空云数据库中的所有表情包数据
    const removeResult = await db.collection('emojis').where({}).remove()
    console.log('✅ 清空云数据库表情包数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (emojis.length > 0) {
      for (const emoji of emojis) {
        try {
          await db.collection('emojis').add({
            data: {
              title: emoji.title,
              category: emoji.category,
              imageUrl: emoji.imageUrl,
              tags: emoji.tags || [],
              description: emoji.description || '',
              status: 'published',
              likes: emoji.likes || 0,
              downloads: emoji.downloads || 0,
              collections: emoji.collections || 0,
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入表情包失败:', emoji.title, error)
        }
      }
    }

    console.log('🎉 表情包数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: emojis.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步表情包数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 同步横幅数据 - 全量替换模式
async function syncBanners(banners) {
  try {
    console.log('🔄 开始全量同步横幅数据，数量:', banners.length)

    // 第一步：清空云数据库中的所有横幅数据
    const removeResult = await db.collection('banners').where({}).remove()
    console.log('✅ 清空云数据库横幅数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (banners.length > 0) {
      for (const banner of banners) {
        try {
          await db.collection('banners').add({
            data: {
              title: banner.title,
              imageUrl: banner.imageUrl,
              linkUrl: banner.linkUrl || '',
              sort: banner.sort || 0,
              status: 'active',
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入横幅失败:', banner.title, error)
        }
      }
    }

    console.log('🎉 横幅数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: banners.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步横幅数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 清空所有数据
async function clearAllData() {
  try {
    console.log('开始清空所有数据...')

    // 清空表情包数据
    const emojisResult = await db.collection('emojis').where({}).remove()
    console.log('清空表情包数据:', emojisResult.stats.removed, '条')

    // 清空分类数据
    const categoriesResult = await db.collection('categories').where({}).remove()
    console.log('清空分类数据:', categoriesResult.stats.removed, '条')

    // 清空横幅数据
    const bannersResult = await db.collection('banners').where({}).remove()
    console.log('清空横幅数据:', bannersResult.stats.removed, '条')

    // 清空用户行为数据（可选）
    const actionsResult = await db.collection('user_actions').where({}).remove()
    console.log('清空用户行为数据:', actionsResult.stats.removed, '条')

    return {
      success: true,
      message: '数据清空完成',
      data: {
        emojis: emojisResult.stats.removed,
        categories: categoriesResult.stats.removed,
        banners: bannersResult.stats.removed,
        userActions: actionsResult.stats.removed
      }
    }
  } catch (error) {
    console.error('清空数据失败:', error)
    return { success: false, error: error.message }
  }
}
