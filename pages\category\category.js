// pages/category/category.js
const { DataManager } = require('../../utils/newDataManager.js')

Page({
  data: {
    categories: []
  },

  async onLoad() {
    wx.showLoading({ title: '加载中...' })
    try {
      await this.loadCategories()
    } catch (error) {
      console.error('分类页面加载失败:', error)
      wx.showToast({ title: '加载失败', icon: 'error' })
    } finally {
      wx.hideLoading()
    }
  },

  onShow() {
    // 同步自定义TabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  async loadCategories() {
    try {
      console.log('🔄 加载分类数据，包含真实统计')

      // 直接调用云函数，绕过 DataManager
      console.log('☁️ 直接调用云函数获取分类数据')

      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })

      console.log('📦 云函数返回结果:', result)

      if (!result.result || !result.result.success) {
        console.error('❌ 云函数调用失败:', result.result?.message)
        this.setData({ categories: this.getLocalFallbackCategories() })
        return
      }

      const categoriesWithStats = result.result.data || []
      console.log('📊 获取到的分类数据:', categoriesWithStats)

      if (categoriesWithStats.length === 0) {
        console.warn('⚠️ 没有获取到分类数据，使用本地兜底数据')
        this.setData({ categories: this.getLocalFallbackCategories() })
        return
      }

      // 修复字段映射：emojiCount -> count，并添加渐变色和图标
      const processedCategories = categoriesWithStats.map((category, index) => ({
        ...category,
        count: category.emojiCount || 0,  // 确保 count 字段存在
        id: category._id || category.id,
        _id: category._id || category.id,  // 确保 _id 字段存在
        gradient: category.gradient || this.getGradientByIndex(index),  // 添加渐变色
        icon: category.icon || this.getIconByName(category.name)  // 添加图标
      }))

      this.setData({
        categories: processedCategories
      })

      console.log('✅ 分类统计数据设置完成:', processedCategories.length, '个')
      console.log('✅ 处理后的分类数据:', processedCategories)
    } catch (error) {
      console.error('❌ 加载分类数据失败:', error)
      // 使用本地兜底数据
      this.setData({ categories: this.getLocalFallbackCategories() })
    }
  },

  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    console.log('点击分类:', category)

    // 跳转到分类详情页面，传递正确的参数
    wx.navigateTo({
      url: `/pages/category-detail/category-detail?id=${encodeURIComponent(category._id || category.id)}&name=${encodeURIComponent(category.name)}`
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新分类数据')
    this.loadCategories()

    // 模拟刷新延迟
    setTimeout(() => {
      wx.stopPullDownRefresh()
      wx.showToast({
        title: '刷新完成',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  onShareAppMessage() {
    return {
      title: '表情包分类大全',
      path: '/pages/category/category'
    }
  },

  // 根据索引获取渐变色
  getGradientByIndex(index) {
    const gradients = [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',  // 紫蓝色
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',  // 粉红色
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',  // 蓝青色
      'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',  // 绿青色
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',  // 粉黄色
      'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',  // 青粉色
      'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',  // 粉色系
      'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',  // 紫粉色
      'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)',  // 橙粉色
      'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'   // 橙黄色
    ]
    return gradients[index % gradients.length]
  },

  // 根据分类名称获取图标
  getIconByName(name) {
    const iconMap = {
      '搞笑幽默': '😂',
      '可爱萌宠': '🐱',
      '情感表达': '❤️',
      '节日庆典': '🎉',
      '网络热梗': '🔥',
      '动漫二次元': '🎭',
      '生活日常': '☀️',
      '工作学习': '📚',
      '运动健身': '⚽',
      '美食料理': '🍕'
    }
    return iconMap[name] || '😊'  // 默认图标
  },

  // 本地兜底分类数据
  getLocalFallbackCategories() {
    return [
      {
        id: 'funny',
        name: '搞笑幽默',
        icon: '😂',
        count: 28,
        description: '搞笑幽默相关的表情包',
        gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      },
      {
        id: 'cute',
        name: '可爱萌宠',
        icon: '🐱',
        count: 25,
        description: '可爱萌宠相关的表情包',
        gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      },
      {
        id: 'emotion',
        name: '情感表达',
        icon: '❤️',
        count: 56,
        description: '情感表达相关的表情包',
        gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      },
      {
        id: 'festival',
        name: '节日庆典',
        icon: '🎉',
        count: 26,
        description: '节日庆典相关的表情包',
        gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
      },
      {
        id: 'hot',
        name: '网络热梗',
        icon: '🔥',
        count: 42,
        description: '网络热梗相关的表情包',
        gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      },
      {
        id: '2d',
        name: '动漫二次元',
        icon: '🎭',
        count: 18,
        description: '动漫二次元相关的表情包',
        gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
      }
    ]
  }
})