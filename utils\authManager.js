// utils/authManager.js - 用户认证管理器
// 处理微信登录、用户信息管理、登录状态检查等功能

const AuthManager = {
  // 用户登录状态
  isLoggedIn: false,
  userInfo: null,
  openid: null,
  
  // 登录状态监听器
  loginListeners: [],
  
  /**
   * 初始化认证管理器
   */
  init() {
    console.log('AuthManager 初始化')
    this.checkLoginStatus()
  },
  
  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    try {
      // 从本地存储获取用户信息
      const storedUserInfo = wx.getStorageSync('userInfo')
      const storedOpenid = wx.getStorageSync('openid')
      const loginTime = wx.getStorageSync('loginTime')
      
      // 检查登录是否过期（7天）
      const now = Date.now()
      const sevenDays = 7 * 24 * 60 * 60 * 1000
      
      if (storedUserInfo && storedOpenid && loginTime && (now - loginTime < sevenDays)) {
        this.isLoggedIn = true
        this.userInfo = storedUserInfo
        this.openid = storedOpenid
        console.log('用户已登录:', this.userInfo.nickName)
        this.notifyLoginListeners(true)
        return true
      } else {
        this.clearLoginData()
        return false
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
      this.clearLoginData()
      return false
    }
  },
  
  /**
   * 微信登录（带重试机制）
   */
  async login(retryCount = 0) {
    const maxRetries = 3

    try {
      console.log('开始微信登录流程，重试次数:', retryCount)

      // 1. 调用 wx.login 获取 code
      const loginRes = await this.wxLoginWithTimeout()
      console.log('wx.login 成功:', loginRes.code)

      // 2. 获取用户信息
      const userProfile = await this.getUserProfileWithTimeout()
      console.log('获取用户信息成功:', userProfile.userInfo.nickName)

      // 3. 尝试调用云函数进行登录
      let cloudLoginRes
      try {
        cloudLoginRes = await this.cloudLoginWithTimeout(loginRes.code, userProfile.userInfo)
      } catch (cloudError) {
        console.warn('云函数登录失败，使用本地模拟登录:', cloudError.message)

        // 如果云函数失败，使用本地模拟登录
        cloudLoginRes = {
          success: true,
          openid: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          message: '本地模拟登录成功（云函数不可用）'
        }
      }

      if (cloudLoginRes.success) {
        // 4. 保存登录信息
        this.saveLoginData(cloudLoginRes.openid, userProfile.userInfo)

        console.log('登录成功:', userProfile.userInfo.nickName)
        return {
          success: true,
          userInfo: userProfile.userInfo,
          openid: cloudLoginRes.openid,
          isLocalLogin: cloudLoginRes.openid.startsWith('local_')
        }
      } else {
        throw new Error(cloudLoginRes.error || '云端登录失败')
      }

    } catch (error) {
      console.error('登录失败:', error, '重试次数:', retryCount)

      // 处理特定错误类型
      const errorInfo = this.handleLoginError(error)

      // 如果是网络错误且未达到最大重试次数，进行重试
      if (errorInfo.canRetry && retryCount < maxRetries) {
        console.log('登录失败，准备重试...')

        // 延迟后重试
        await this.delay(1000 * (retryCount + 1))
        return await this.login(retryCount + 1)
      }

      return {
        success: false,
        error: errorInfo.message,
        errorType: errorInfo.type
      }
    }
  },
  
  /**
   * 调用 wx.login（带超时）
   */
  wxLoginWithTimeout(timeout = 10000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('微信登录超时'))
      }, timeout)

      wx.login({
        success: (res) => {
          clearTimeout(timer)
          resolve(res)
        },
        fail: (error) => {
          clearTimeout(timer)
          reject(error)
        }
      })
    })
  },

  /**
   * 获取用户信息（带超时）
   */
  getUserProfileWithTimeout(timeout = 15000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('获取用户信息超时'))
      }, timeout)

      // 先尝试从本地存储获取用户信息
      const storedUserInfo = wx.getStorageSync('userInfo')
      if (storedUserInfo && storedUserInfo.nickName) {
        clearTimeout(timer)
        resolve({ userInfo: storedUserInfo })
        return
      }

      // 如果本地没有，则调用getUserProfile
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          clearTimeout(timer)
          resolve(res)
        },
        fail: (error) => {
          clearTimeout(timer)
          // 如果getUserProfile失败，使用模拟用户信息
          console.warn('getUserProfile失败，使用模拟用户信息:', error.errMsg)
          const mockUserInfo = {
            nickName: '测试用户',
            avatarUrl: '/images/default-avatar.png',
            gender: 0,
            country: '中国',
            province: '广东',
            city: '深圳',
            language: 'zh_CN'
          }
          resolve({ userInfo: mockUserInfo })
        }
      })
    })
  },
  
  /**
   * 云函数登录（带超时）
   */
  async cloudLoginWithTimeout(code, userInfo, timeout = 20000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('云函数登录超时'))
      }, timeout)

      wx.cloud.callFunction({
        name: 'login',
        data: {
          code: code,
          userInfo: userInfo
        }
      }).then(res => {
        clearTimeout(timer)
        resolve(res.result)
      }).catch(error => {
        clearTimeout(timer)
        console.error('云函数登录失败:', error)
        reject(error)
      })
    })
  },
  
  /**
   * 保存登录数据
   */
  saveLoginData(openid, userInfo) {
    try {
      this.isLoggedIn = true
      this.userInfo = userInfo
      this.openid = openid

      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo)
      wx.setStorageSync('openid', openid)
      wx.setStorageSync('loginTime', Date.now())

      // 通知监听器
      this.notifyLoginListeners(true)

      console.log('✅ 登录数据已保存')

      // 异步恢复用户数据，不阻塞登录流程
      setTimeout(() => {
        this.restoreUserDataFromCloud()
      }, 100)

    } catch (error) {
      console.error('❌ 保存登录数据失败:', error)
    }
  },

  /**
   * 从云端恢复用户数据
   */
  async restoreUserDataFromCloud() {
    try {
      console.log('🔄 开始恢复用户数据')

      // 动态导入StateManager避免循环依赖
      const { StateManager } = require('./stateManager.js')

      // 先从本地存储加载数据（快速显示）
      StateManager.loadFromLocalStorage()
      
      // 尝试从云端同步数据
      const cloudSyncSuccess = await this.syncFromCloudWithRetry()
      
      if (cloudSyncSuccess) {
        console.log('✅ 云端数据同步成功')
        wx.showToast({
          title: '数据同步成功',
          icon: 'success',
          duration: 1500
        })
      } else {
        console.log('⚠️ 云端同步失败，使用本地数据')
        
        // 如果云端同步失败且没有本地数据，初始化测试数据
        StateManager.initTestDataIfNeeded()
        
        wx.showToast({
          title: '使用本地数据',
          icon: 'none',
          duration: 1500
        })
      }

    } catch (error) {
      console.error('❌ 恢复用户数据失败:', error)

      // 确保至少从本地存储加载数据
      try {
        const { StateManager } = require('./stateManager.js')
        StateManager.loadFromLocalStorage()
        StateManager.initTestDataIfNeeded()
        console.log('✅ 已从本地存储恢复数据')
      } catch (localError) {
        console.error('❌ 从本地存储恢复数据也失败:', localError)
      }
    }
  },

  /**
   * 带重试机制的云端数据同步
   */
  async syncFromCloudWithRetry(maxRetries = 2) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        console.log(`📡 尝试云端数据同步 (${attempt + 1}/${maxRetries})`)
        
        // 检查网络状态
        const networkStatus = await this.checkNetworkStatus()
        if (!networkStatus.isConnected) {
          console.log('⚠️ 网络未连接，跳过云端同步')
          return false
        }

        // 获取用户云端数据
        const cloudData = await this.fetchUserCloudData()
        
        if (cloudData.success) {
          // 将云端数据更新到StateManager
          await this.updateLocalStateFromCloud(cloudData.data)
          return true
        } else {
          console.log(`⚠️ 云端数据获取失败 (${attempt + 1}/${maxRetries}):`, cloudData.error)
        }
        
      } catch (error) {
        console.error(`❌ 云端同步失败 (${attempt + 1}/${maxRetries}):`, error)
        
        // 最后一次尝试失败，不再重试
        if (attempt === maxRetries - 1) {
          return false
        }
        
        // 等待后重试
        await this.delay(1000 * (attempt + 1))
      }
    }
    
    return false
  },

  /**
   * 获取用户云端数据
   */
  async fetchUserCloudData() {
    try {
      // 检查云开发是否可用
      if (!this.isCloudAvailable()) {
        console.log('⚠️ 云开发不可用，跳过云端数据获取')
        return { success: false, error: '云开发不可用' }
      }

      // 调用云函数获取用户数据
      const res = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          action: 'getAllUserData'
        }
      })
      
      return res.result
    } catch (error) {
      console.error('❌ 获取云端用户数据失败:', error)
      return { success: false, error: error.message }
    }
  },

  /**
   * 检查云开发是否可用
   */
  isCloudAvailable() {
    try {
      // 检查wx.cloud是否存在
      if (!wx.cloud) {
        console.log('⚠️ wx.cloud 不存在')
        return false
      }

      // 简单的云开发可用性检查
      // 由于当前使用测试环境，暂时返回false使用本地模式
      console.log('⚠️ 云开发环境未正确配置，使用本地模式')
      return false

    } catch (error) {
      console.error('❌ 检查云开发可用性失败:', error)
      return false
    }
  },

  /**
   * 将云端数据更新到本地状态
   */
  async updateLocalStateFromCloud(cloudData) {
    try {
      const { StateManager } = require('./stateManager.js')
      
      // 更新StateManager的内存状态
      StateManager._state.likedEmojis = new Set(cloudData.likedEmojis || [])
      StateManager._state.collectedEmojis = new Set(cloudData.collectedEmojis || [])
      StateManager._state.downloadedEmojis = new Set(cloudData.downloadedEmojis || [])
      StateManager._state.downloadTimes = cloudData.downloadTimes || {}
      
      // 保存到本地存储
      StateManager.saveToLocalStorage()
      
      // 通知监听器数据已更新
      StateManager.notifyListeners('global', { type: 'sync', source: 'cloud' })
      
      console.log('✅ 云端数据已更新到本地状态', {
        liked: StateManager._state.likedEmojis.size,
        collected: StateManager._state.collectedEmojis.size,
        downloaded: StateManager._state.downloadedEmojis.size
      })
      
    } catch (error) {
      console.error('❌ 更新本地状态失败:', error)
      throw error
    }
  },
  
  /**
   * 清除登录数据
   */
  clearLoginData() {
    this.isLoggedIn = false
    this.userInfo = null
    this.openid = null
    
    try {
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('openid')
      wx.removeStorageSync('loginTime')
    } catch (error) {
      console.error('清除登录数据失败:', error)
    }
    
    this.notifyLoginListeners(false)
    console.log('登录数据已清除')
  },
  
  /**
   * 退出登录
   */
  logout() {
    this.clearLoginData()
    
    // 显示退出成功提示
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    })
    
    console.log('用户已退出登录')
  },
  
  /**
   * 添加登录状态监听器
   */
  addLoginListener(callback) {
    if (typeof callback === 'function') {
      this.loginListeners.push(callback)
    }
  },
  
  /**
   * 移除登录状态监听器
   */
  removeLoginListener(callback) {
    const index = this.loginListeners.indexOf(callback)
    if (index > -1) {
      this.loginListeners.splice(index, 1)
    }
  },
  
  /**
   * 通知登录状态监听器
   */
  notifyLoginListeners(isLoggedIn) {
    this.loginListeners.forEach(callback => {
      try {
        callback(isLoggedIn, this.userInfo)
      } catch (error) {
        console.error('登录监听器执行失败:', error)
      }
    })
  },
  
  /**
   * 检查是否需要登录
   * 如果未登录，显示登录提示
   */
  async requireLogin(showModal = true) {
    if (this.isLoggedIn) {
      return true
    }
    
    if (showModal) {
      return new Promise((resolve) => {
        wx.showModal({
          title: '需要登录',
          content: '此功能需要登录后才能使用，是否立即登录？',
          confirmText: '立即登录',
          cancelText: '取消',
          success: async (res) => {
            if (res.confirm) {
              const loginResult = await this.login()
              resolve(loginResult.success)
            } else {
              resolve(false)
            }
          }
        })
      })
    }
    
    return false
  },
  
  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return {
      isLoggedIn: this.isLoggedIn,
      userInfo: this.userInfo,
      openid: this.openid
    }
  },
  
  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    if (!this.isLoggedIn) {
      return false
    }

    try {
      const userProfile = await this.getUserProfileWithTimeout()
      this.userInfo = userProfile.userInfo
      wx.setStorageSync('userInfo', userProfile.userInfo)
      this.notifyLoginListeners(true)
      return true
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      return false
    }
  },

  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    const errorMessage = error.message || error.errMsg || '未知错误'

    // 网络相关错误
    if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
      return {
        type: 'timeout',
        message: '网络连接超时，请检查网络后重试',
        canRetry: true
      }
    }

    if (errorMessage.includes('network') || errorMessage.includes('网络')) {
      return {
        type: 'network',
        message: '网络连接失败，请检查网络设置',
        canRetry: true
      }
    }

    // 用户拒绝授权
    if (errorMessage.includes('auth deny') || errorMessage.includes('用户拒绝')) {
      return {
        type: 'auth_deny',
        message: '需要您的授权才能使用完整功能',
        canRetry: false
      }
    }

    // 云函数相关错误
    if (errorMessage.includes('cloud function') || errorMessage.includes('云函数')) {
      return {
        type: 'cloud_error',
        message: '服务暂时不可用，请稍后重试',
        canRetry: true
      }
    }

    // 微信登录相关错误
    if (errorMessage.includes('wx.login')) {
      return {
        type: 'wx_login_error',
        message: '微信登录失败，请重试',
        canRetry: true
      }
    }

    // 默认错误
    return {
      type: 'unknown',
      message: '登录失败，请重试',
      canRetry: true
    }
  },

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 测试登录（不需要用户手势）
   */
  async testLogin() {
    try {
      console.log('开始测试登录流程（模拟模式）')

      // 1. 调用 wx.login 获取 code
      const loginRes = await this.wxLoginWithTimeout()
      console.log('wx.login 成功:', loginRes.code)

      // 2. 使用模拟用户信息
      const mockUserInfo = {
        nickName: '测试用户_' + Date.now(),
        avatarUrl: '/images/default-avatar.png',
        gender: 0,
        country: '中国',
        province: '广东',
        city: '深圳',
        language: 'zh_CN'
      }

      console.log('使用模拟用户信息:', mockUserInfo.nickName)

      // 3. 尝试调用云函数进行登录
      let cloudLoginRes
      try {
        cloudLoginRes = await this.cloudLoginWithTimeout(loginRes.code, mockUserInfo)
      } catch (cloudError) {
        console.warn('云函数登录失败，使用本地模拟登录:', cloudError.message)

        // 如果云函数失败，使用本地模拟登录
        cloudLoginRes = {
          success: true,
          openid: 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          message: '测试模拟登录成功'
        }
      }

      if (cloudLoginRes.success) {
        // 4. 保存登录信息
        this.saveLoginData(cloudLoginRes.openid, mockUserInfo)

        console.log('测试登录成功:', mockUserInfo.nickName)
        return {
          success: true,
          userInfo: mockUserInfo,
          openid: cloudLoginRes.openid,
          isTestLogin: true
        }
      } else {
        throw new Error(cloudLoginRes.error || '云端登录失败')
      }

    } catch (error) {
      console.error('测试登录失败:', error)
      return {
        success: false,
        error: error.message || '测试登录失败'
      }
    }
  },

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            isConnected: res.networkType !== 'none',
            networkType: res.networkType
          })
        },
        fail: () => {
          resolve({
            isConnected: false,
            networkType: 'unknown'
          })
        }
      })
    })
  },

  /**
   * 显示网络错误提示
   */
  showNetworkError() {
    wx.showModal({
      title: '网络连接失败',
      content: '请检查网络设置后重试',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 显示登录错误提示
   */
  showLoginError(errorType, errorMessage) {
    const errorConfig = {
      timeout: {
        title: '连接超时',
        content: '网络连接超时，请检查网络后重试'
      },
      network: {
        title: '网络错误',
        content: '网络连接失败，请检查网络设置'
      },
      auth_deny: {
        title: '授权失败',
        content: '需要您的授权才能使用完整功能，请重新尝试登录'
      },
      cloud_error: {
        title: '服务异常',
        content: '服务暂时不可用，请稍后重试'
      },
      wx_login_error: {
        title: '登录失败',
        content: '微信登录失败，请重试'
      },
      unknown: {
        title: '登录失败',
        content: errorMessage || '登录失败，请重试'
      }
    }

    const config = errorConfig[errorType] || errorConfig.unknown

    wx.showModal({
      title: config.title,
      content: config.content,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // ========== 权限验证功能 ==========

  // 权限验证缓存
  _permissionCache: new Map(),
  _cacheExpire: 5 * 60 * 1000, // 5分钟缓存

  /**
   * 验证用户权限
   * @param {string} requiredRole - 需要的角色 ('user', 'admin')
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 验证结果
   */
  async validatePermission(requiredRole = 'user', options = {}) {
    const { useCache = true, timeout = 10000 } = options

    try {
      // 检查缓存
      if (useCache) {
        const cacheKey = `permission_${requiredRole}`
        const cached = this._permissionCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < this._cacheExpire) {
          console.log('📦 使用缓存的权限验证结果')
          return cached.result
        }
      }

      console.log(`🔐 验证权限: ${requiredRole}`)

      // 调用权限验证云函数
      const result = await Promise.race([
        wx.cloud.callFunction({
          name: 'login',
          data: {
            action: 'validatePermission',
            requiredRole
          }
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('权限验证超时')), timeout)
        )
      ])

      const validationResult = result.result || { isValid: false, message: '验证失败' }

      // 缓存结果
      if (useCache) {
        const cacheKey = `permission_${requiredRole}`
        this._permissionCache.set(cacheKey, {
          result: validationResult,
          timestamp: Date.now()
        })
      }

      console.log('✅ 权限验证完成:', validationResult.isValid ? '通过' : '失败')
      return validationResult
    } catch (error) {
      console.error('❌ 权限验证失败:', error)
      return {
        isValid: false,
        message: '权限验证失败: ' + error.message
      }
    }
  },

  /**
   * 验证用户权限
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 验证结果
   */
  async validateUser(options = {}) {
    return await this.validatePermission('user', options)
  },

  /**
   * 验证管理员权限
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 验证结果
   */
  async validateAdmin(options = {}) {
    return await this.validatePermission('admin', options)
  },

  /**
   * 清除权限缓存
   * @param {string} role - 角色，不传则清除所有
   */
  clearPermissionCache(role) {
    if (role) {
      this._permissionCache.delete(`permission_${role}`)
    } else {
      this._permissionCache.clear()
    }
    console.log('🗑️ 权限缓存已清除')
  },

  /**
   * 权限检查装饰器
   * @param {string} requiredRole - 需要的角色
   * @param {Function} handler - 处理函数
   * @returns {Function} 装饰后的函数
   */
  requirePermission(requiredRole, handler) {
    return async (...args) => {
      const validation = await this.validatePermission(requiredRole)

      if (!validation.isValid) {
        wx.showToast({
          title: validation.message || '权限不足',
          icon: 'none'
        })
        return false
      }

      return await handler.apply(this, args)
    }
  },

  /**
   * 需要用户登录的装饰器
   * @param {Function} handler - 处理函数
   * @returns {Function} 装饰后的函数
   */
  requireLogin(handler) {
    return async (...args) => {
      if (!this.isLoggedIn) {
        wx.showModal({
          title: '提示',
          content: '请先登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              // 跳转到登录页面或显示登录弹窗
              wx.navigateTo({
                url: '/pages/profile/profile'
              })
            }
          }
        })
        return false
      }

      return await handler.apply(this, args)
    }
  },

  /**
   * 需要管理员权限的装饰器
   * @param {Function} handler - 处理函数
   * @returns {Function} 装饰后的函数
   */
  requireAdmin(handler) {
    return this.requirePermission('admin', handler)
  },

  /**
   * 更新用户信息
   * @param {Object} newUserInfo - 新的用户信息
   */
  updateUserInfo(newUserInfo) {
    if (!this.isLoggedIn) {
      console.warn('用户未登录，无法更新用户信息')
      return false
    }

    try {
      // 合并用户信息
      const updatedUserInfo = {
        ...this.userInfo,
        ...newUserInfo
      }

      // 更新内存中的用户信息
      this.userInfo = updatedUserInfo

      // 保存到本地存储
      wx.setStorageSync('userInfo', updatedUserInfo)

      // 通知监听器用户信息已更新
      this.notifyLoginListeners(true)

      console.log('✅ 用户信息更新成功:', updatedUserInfo.nickName)
      return true

    } catch (error) {
      console.error('❌ 更新用户信息失败:', error)
      return false
    }
  },

  /**
   * 获取当前用户信息
   * @returns {Object} 包含登录状态和用户信息的对象
   */
  getCurrentUser() {
    return {
      isLoggedIn: this.isLoggedIn,
      userInfo: this.userInfo,
      openid: this.openid
    }
  }
}

module.exports = {
  AuthManager
}
