<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #666;
            margin-top: 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .result-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .result-card h3 {
            margin-top: 0;
            color: #495057;
        }
        .data-preview {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 云函数调用测试</h1>
        
        <div class="test-section">
            <h2>📋 初始化</h2>
            <button onclick="initCloudSDK()">初始化云开发SDK</button>
            <button onclick="clearLog()">清空日志</button>
            <div id="initStatus" class="log">等待初始化云开发SDK...</div>
        </div>

        <div class="test-section">
            <h2>🔧 云函数测试</h2>
            <button onclick="testDataAPI()" id="testDataAPIBtn" disabled>测试dataAPI云函数</button>
            <button onclick="testWebAdminAPI()" id="testWebAdminAPIBtn" disabled>测试webAdminAPI云函数</button>
            <button onclick="testAllFunctions()" id="testAllBtn" disabled>测试所有云函数</button>
        </div>

        <div class="test-section">
            <h2>📊 数据获取测试</h2>
            <button onclick="testGetCategories()" id="testCategoriesBtn" disabled>获取分类数据</button>
            <button onclick="testGetEmojis()" id="testEmojisBtn" disabled>获取表情包数据</button>
            <button onclick="testGetBanners()" id="testBannersBtn" disabled>获取横幅数据</button>
        </div>

        <div class="test-section">
            <h2>📝 测试结果</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📝 详细日志</h2>
            <div id="log" class="log">等待开始测试...</div>
        </div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;
        let isInitialized = false;
        let testResults = {};

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('testResults').innerHTML = '';
            testResults = {};
        }

        // 初始化云开发SDK
        async function initCloudSDK() {
            const statusElement = document.getElementById('initStatus');
            
            try {
                log('🚀 开始初始化云开发SDK...');
                statusElement.innerHTML = '<span class="info">正在初始化云开发SDK...</span>';
                
                if (typeof cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }

                tcbApp = cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });

                // 匿名登录
                log('🔐 进行匿名登录...');
                statusElement.innerHTML = '<span class="info">正在进行匿名登录...</span>';
                const auth = tcbApp.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功', 'success');

                // 测试数据库连接
                log('🔍 测试数据库连接...');
                statusElement.innerHTML = '<span class="info">正在测试数据库连接...</span>';
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();
                log('✅ 数据库连接测试成功', 'success');

                isInitialized = true;
                statusElement.innerHTML = '<span class="success">✅ 云开发SDK初始化完成</span>';
                
                // 启用按钮
                document.getElementById('testDataAPIBtn').disabled = false;
                document.getElementById('testWebAdminAPIBtn').disabled = false;
                document.getElementById('testAllBtn').disabled = false;
                document.getElementById('testCategoriesBtn').disabled = false;
                document.getElementById('testEmojisBtn').disabled = false;
                document.getElementById('testBannersBtn').disabled = false;
                
                log('✅ 云开发SDK初始化完成，可以开始测试', 'success');
                
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                statusElement.innerHTML = '<span class="error">❌ 初始化失败: ' + error.message + '</span>';
            }
        }

        // 测试dataAPI云函数
        async function testDataAPI() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('🧪 开始测试dataAPI云函数...');
                
                // 测试获取分类
                log('📋 测试获取分类数据...');
                const categoriesResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                log('✅ 分类数据获取成功', 'success');
                addTestResult('dataAPI-categories', categoriesResult.result, '获取分类数据');

                // 测试获取表情包
                log('📋 测试获取表情包数据...');
                const emojisResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 5 }
                    }
                });
                
                log('✅ 表情包数据获取成功', 'success');
                addTestResult('dataAPI-emojis', emojisResult.result, '获取表情包数据');

                // 测试获取横幅
                log('📋 测试获取横幅数据...');
                const bannersResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                log('✅ 横幅数据获取成功', 'success');
                addTestResult('dataAPI-banners', bannersResult.result, '获取横幅数据');

                log('🎉 dataAPI云函数测试完成', 'success');
                
            } catch (error) {
                log('❌ dataAPI云函数测试失败: ' + error.message, 'error');
                addTestResult('dataAPI-error', { error: error.message }, 'dataAPI测试失败');
            }
        }

        // 测试webAdminAPI云函数
        async function testWebAdminAPI() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('🧪 开始测试webAdminAPI云函数...');
                
                // 测试获取分类列表
                log('📋 测试获取分类列表...');
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: { action: 'getCategoryList' }
                });
                
                log('✅ webAdminAPI调用成功', 'success');
                addTestResult('webAdminAPI-categories', result.result, '获取分类列表');

                log('🎉 webAdminAPI云函数测试完成', 'success');
                
            } catch (error) {
                log('❌ webAdminAPI云函数测试失败: ' + error.message, 'error');
                addTestResult('webAdminAPI-error', { error: error.message }, 'webAdminAPI测试失败');
            }
        }

        // 单独测试获取分类数据
        async function testGetCategories() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('📋 单独测试获取分类数据...');
                
                const result = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                log('✅ 分类数据获取成功', 'success');
                log(`📊 获取到 ${result.result.data?.length || 0} 个分类`);
                
                addTestResult('categories-detail', result.result, '分类数据详情');
                
            } catch (error) {
                log('❌ 获取分类数据失败: ' + error.message, 'error');
            }
        }

        // 单独测试获取表情包数据
        async function testGetEmojis() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('📋 单独测试获取表情包数据...');
                
                const result = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 10 }
                    }
                });
                
                log('✅ 表情包数据获取成功', 'success');
                log(`📊 获取到 ${result.result.data?.length || 0} 个表情包`);
                
                addTestResult('emojis-detail', result.result, '表情包数据详情');
                
            } catch (error) {
                log('❌ 获取表情包数据失败: ' + error.message, 'error');
            }
        }

        // 单独测试获取横幅数据
        async function testGetBanners() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('📋 单独测试获取横幅数据...');
                
                const result = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                log('✅ 横幅数据获取成功', 'success');
                log(`📊 获取到 ${result.result.data?.length || 0} 个横幅`);
                
                addTestResult('banners-detail', result.result, '横幅数据详情');
                
            } catch (error) {
                log('❌ 获取横幅数据失败: ' + error.message, 'error');
            }
        }

        // 测试所有云函数
        async function testAllFunctions() {
            log('🚀 开始测试所有云函数...');
            
            await testDataAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testWebAdminAPI();
            
            log('🎉 所有云函数测试完成！', 'success');
        }

        // 添加测试结果
        function addTestResult(testId, result, description) {
            testResults[testId] = result;
            
            const resultsContainer = document.getElementById('testResults');
            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            
            const success = result.success !== false && !result.error;
            const statusIcon = success ? '✅' : '❌';
            const statusClass = success ? 'success' : 'error';
            
            resultCard.innerHTML = `
                <h3>${statusIcon} ${description}</h3>
                <p class="${statusClass}">
                    状态: ${success ? '成功' : '失败'}
                    ${result.message ? ` - ${result.message}` : ''}
                </p>
                <div class="data-preview">
                    ${JSON.stringify(result, null, 2)}
                </div>
            `;
            
            resultsContainer.appendChild(resultCard);
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            log('页面加载完成，请点击"初始化云开发SDK"按钮开始测试');
        });
    </script>
</body>
</html>
