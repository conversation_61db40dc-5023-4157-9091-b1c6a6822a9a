// webAdminAPI 单元测试
const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const { mockJWTToken, mockAdminInfo } = require('../mocks');

// 模拟云开发SDK
jest.mock('wx-server-sdk', () => ({
  init: jest.fn(),
  database: jest.fn(() => ({
    runTransaction: jest.fn(),
    collection: jest.fn(() => ({
      where: jest.fn(() => ({
        get: jest.fn(),
        count: jest.fn(),
        orderBy: jest.fn(() => ({
          get: jest.fn(),
          limit: jest.fn(() => ({
            get: jest.fn()
          }))
        }))
      })),
      add: jest.fn(),
      doc: jest.fn(() => ({
        update: jest.fn()
      }))
    }))
  }))
}));

// 模拟JWT库
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(() => mockAdminInfo())
}));

describe('webAdminAPI 单元测试', () => {
  let webAdminAPI;
  let mockDb;
  
  beforeEach(() => {
    jest.resetModules();
    webAdminAPI = require('../../cloudfunctions/webAdminAPI/index');
    
    const cloud = require('wx-server-sdk');
    mockDb = {
      runTransaction: jest.fn(),
      collection: jest.fn(() => ({
        where: jest.fn(() => ({
          get: jest.fn(() => ({ data: [] })),
          count: jest.fn(() => ({ total: 0 }))
        })),
        add: jest.fn(() => ({ _id: 'mock_id' })),
        doc: jest.fn(() => ({
          update: jest.fn()
        }))
      }))
    };
    cloud.database.mockReturnValue(mockDb);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('T2.1 分类管理测试', () => {
    test('T2.1.1 创建分类应该成功', async () => {
      // 模拟事务成功
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            add: jest.fn(() => ({ _id: 'mock_category_id' }))
          }))
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '测试分类',
        icon: 'https://example.com/icon.png',
        description: '测试分类描述'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.data).toHaveProperty('_id');
      expect(result.message).toBe('分类创建成功');
    });

    test('T2.1.2 创建分类时缺少必要参数应该失败', async () => {
      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '', // 空名称
        icon: 'https://example.com/icon.png'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('分类名称不能为空');
      expect(result.code).toBe('INVALID_DATA');
    });

    test('T2.1.3 更新分类应该成功', async () => {
      // 模拟分类存在
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            where: jest.fn(() => ({
              get: jest.fn(() => ({
                data: [{
                  _id: 'existing_category_id',
                  id: 'cat_001',
                  name: '原分类名'
                }]
              }))
            })),
            doc: jest.fn(() => ({
              update: jest.fn()
            })),
            add: jest.fn()
          }))
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'updateCategory',
        token: mockJWTToken(),
        id: 'cat_001',
        updates: {
          name: '更新后的分类名',
          description: '更新后的描述'
        }
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.message).toBe('分类更新成功');
    });

    test('T2.1.4 更新不存在的分类应该失败', async () => {
      // 模拟分类不存在
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            where: jest.fn(() => ({
              get: jest.fn(() => ({ data: [] })) // 空数组表示不存在
            }))
          }))
        };
        
        try {
          return await callback(mockTransaction);
        } catch (error) {
          throw error;
        }
      });

      const event = {
        action: 'updateCategory',
        token: mockJWTToken(),
        id: 'nonexistent_cat',
        updates: {
          name: '更新后的分类名'
        }
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('分类不存在');
    });

    test('T2.1.5 删除分类应该成功（软删除）', async () => {
      // 模拟分类存在
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            where: jest.fn(() => ({
              get: jest.fn(() => ({
                data: [{
                  _id: 'existing_category_id',
                  id: 'cat_001',
                  name: '要删除的分类'
                }]
              }))
            })),
            doc: jest.fn(() => ({
              update: jest.fn()
            })),
            add: jest.fn()
          }))
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'deleteCategory',
        token: mockJWTToken(),
        id: 'cat_001'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.message).toBe('分类删除成功');
    });
  });

  describe('T2.2 表情包管理测试', () => {
    test('T2.2.1 创建表情包应该成功', async () => {
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            add: jest.fn(() => ({ _id: 'mock_emoji_id' }))
          }))
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'createEmoji',
        token: mockJWTToken(),
        title: '测试表情包',
        imageUrl: 'https://example.com/emoji.png',
        categoryId: 'cat_001',
        tags: ['测试', '表情包']
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.message).toBe('表情包创建成功');
    });

    test('T2.2.2 创建表情包时缺少标题应该失败', async () => {
      const event = {
        action: 'createEmoji',
        token: mockJWTToken(),
        title: '', // 空标题
        imageUrl: 'https://example.com/emoji.png'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('表情包标题不能为空');
      expect(result.code).toBe('INVALID_DATA');
    });

    test('T2.2.3 创建表情包时缺少图片URL应该失败', async () => {
      const event = {
        action: 'createEmoji',
        token: mockJWTToken(),
        title: '测试表情包',
        imageUrl: '' // 空图片URL
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('表情包图片URL不能为空');
      expect(result.code).toBe('INVALID_DATA');
    });
  });

  describe('T2.3 横幅管理测试', () => {
    test('T2.3.1 创建横幅应该成功', async () => {
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn(() => ({
            add: jest.fn(() => ({ _id: 'mock_banner_id' }))
          }))
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'createBanner',
        token: mockJWTToken(),
        title: '测试横幅',
        imageUrl: 'https://example.com/banner.png',
        linkUrl: 'https://example.com/link'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('id');
      expect(result.message).toBe('横幅创建成功');
    });

    test('T2.3.2 创建横幅时缺少标题应该失败', async () => {
      const event = {
        action: 'createBanner',
        token: mockJWTToken(),
        title: '', // 空标题
        imageUrl: 'https://example.com/banner.png'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('横幅标题不能为空');
      expect(result.code).toBe('INVALID_DATA');
    });
  });

  describe('T2.4 权限验证测试', () => {
    test('T2.4.1 无效令牌应该被拒绝', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockImplementation(() => {
        throw new Error('invalid token');
      });

      const event = {
        action: 'createCategory',
        token: 'invalid.token.here',
        name: '测试分类'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('令牌无效');
      expect(result.code).toBe('INVALID_TOKEN');
    });

    test('T2.4.2 缺少写权限应该被拒绝', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockReturnValue({
        adminId: 'readonly_admin',
        permissions: ['read'] // 只有读权限
      });

      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '测试分类'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('权限不足');
      expect(result.code).toBe('INSUFFICIENT_PERMISSION');
    });

    test('T2.4.3 缺少删除权限应该被拒绝', async () => {
      const jwt = require('jsonwebtoken');
      jwt.verify.mockReturnValue({
        adminId: 'limited_admin',
        permissions: ['read', 'write'] // 没有删除权限
      });

      const event = {
        action: 'deleteCategory',
        token: mockJWTToken(),
        id: 'cat_001'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('权限不足');
      expect(result.code).toBe('INSUFFICIENT_PERMISSION');
    });
  });

  describe('T2.5 事务处理测试', () => {
    test('T2.5.1 事务失败应该回滚', async () => {
      // 模拟事务失败
      mockDb.runTransaction.mockImplementation(async (callback) => {
        throw new Error('Transaction failed');
      });

      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '测试分类',
        icon: 'https://example.com/icon.png'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('分类创建失败');
      expect(result.code).toBe('CREATE_CATEGORY_FAILED');
    });

    test('T2.5.2 同步通知应该在事务中创建', async () => {
      let transactionCalls = [];
      
      mockDb.runTransaction.mockImplementation(async (callback) => {
        const mockTransaction = {
          collection: jest.fn((collectionName) => {
            transactionCalls.push(collectionName);
            return {
              add: jest.fn(() => ({ _id: `mock_${collectionName}_id` })),
              where: jest.fn(() => ({
                get: jest.fn(() => ({ data: [] }))
              })),
              doc: jest.fn(() => ({
                update: jest.fn()
              }))
            };
          })
        };
        return await callback(mockTransaction);
      });

      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '测试分类',
        icon: 'https://example.com/icon.png'
      };

      await webAdminAPI.main(event, {});

      // 验证事务中同时操作了categories和sync_notifications集合
      expect(transactionCalls).toContain('categories');
      expect(transactionCalls).toContain('sync_notifications');
    });
  });

  describe('T2.6 数据验证测试', () => {
    test('T2.6.1 分类名称长度验证', async () => {
      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: 'a'.repeat(101), // 超长名称
        icon: 'https://example.com/icon.png'
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('分类名称过长');
    });

    test('T2.6.2 图片URL格式验证', async () => {
      const event = {
        action: 'createEmoji',
        token: mockJWTToken(),
        title: '测试表情包',
        imageUrl: 'invalid-url' // 无效URL
      };

      const result = await webAdminAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('图片URL格式无效');
    });
  });

  describe('T2.7 性能测试', () => {
    test('T2.7.1 创建操作应该在合理时间内完成', async () => {
      mockDb.runTransaction.mockImplementation(async (callback) => {
        // 模拟快速事务
        const mockTransaction = {
          collection: jest.fn(() => ({
            add: jest.fn(() => ({ _id: 'mock_id' }))
          }))
        };
        return await callback(mockTransaction);
      });

      const startTime = Date.now();

      const event = {
        action: 'createCategory',
        token: mockJWTToken(),
        name: '测试分类',
        icon: 'https://example.com/icon.png'
      };

      await webAdminAPI.main(event, {});

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(2000); // 应该在2秒内完成
    });
  });
});
