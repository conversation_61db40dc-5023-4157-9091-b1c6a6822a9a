/**
 * 健康检查和监控系统
 * 监控云函数、数据库和应用性能
 */

const { EnvironmentConfig } = require('../config/environment.js')

const HealthMonitor = {
  // 监控状态
  _monitoringActive: false,
  _checkInterval: 60000, // 1分钟检查一次
  _intervalId: null,
  
  // 健康状态
  _healthStatus: {
    overall: 'unknown',
    cloudFunctions: {},
    database: {},
    performance: {},
    lastCheck: null
  },
  
  // 监控配置
  _config: {
    enableCloudCheck: false, // 默认禁用云函数检查
    enableDatabaseCheck: false, // 默认禁用数据库检查
    cloudFunctions: [
      'dataAPI',
      'login',
      'toggleLike',
      'toggleCollect'
    ],
    collections: [
      // 暂时禁用所有集合检查，避免启动时报错
      // 'emojis',
      // 'users',
      // 'user_actions',
      // 'categories'
    ],
    performanceThresholds: {
      apiResponseTime: 3000, // 3秒
      imageLoadTime: 5000,   // 5秒
      memoryUsage: 100,      // 100MB
      errorRate: 0.05        // 5%
    }
  },

  /**
   * 初始化健康监控
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    this._config = { ...this._config, ...options }

    console.log('🏥 健康监控系统初始化')

    // 在开发环境中完全禁用监控，避免启动时的错误
    if (EnvironmentConfig.isDevelopment()) {
      console.log('⚠️ 开发环境：健康监控已禁用')
      return
    }

    // 只在生产环境启用自动监控
    if (EnvironmentConfig.isProduction()) {
      this.startMonitoring()
    }

    // 执行初始健康检查（异步，不阻塞启动）
    setTimeout(() => {
      this.performHealthCheck().catch(error => {
        console.log('⚠️ 初始健康检查失败，但不影响应用启动:', error.message)
      })
    }, 5000) // 延长到5秒，给云开发更多初始化时间
  },

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this._monitoringActive) {
      console.log('⚠️ 监控已经在运行中')
      return
    }
    
    this._monitoringActive = true
    this._intervalId = setInterval(() => {
      this.performHealthCheck()
    }, this._checkInterval)
    
    console.log(`⏰ 健康监控已启动，检查间隔: ${this._checkInterval}ms`)
  },

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this._intervalId) {
      clearInterval(this._intervalId)
      this._intervalId = null
    }
    
    this._monitoringActive = false
    console.log('⏹️ 健康监控已停止')
  },

  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    console.log('🔍 执行健康检查...')
    
    const startTime = Date.now()
    
    try {
      // 并行检查各个组件
      const [
        cloudFunctionStatus,
        databaseStatus,
        performanceStatus
      ] = await Promise.all([
        this.checkCloudFunctions(),
        this.checkDatabase(),
        this.checkPerformance()
      ])
      
      // 更新健康状态
      this._healthStatus = {
        overall: this.calculateOverallHealth(cloudFunctionStatus, databaseStatus, performanceStatus),
        cloudFunctions: cloudFunctionStatus,
        database: databaseStatus,
        performance: performanceStatus,
        lastCheck: new Date().toISOString(),
        checkDuration: Date.now() - startTime
      }
      
      // 记录健康状态
      this.logHealthStatus()
      
      // 检查是否需要告警
      this.checkAlerts()
      
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      this._healthStatus.overall = 'unhealthy'
    }
  },

  /**
   * 检查云函数健康状态
   * @returns {Object} 云函数状态
   */
  async checkCloudFunctions() {
    const status = {}

    // 如果云开发未初始化，跳过检查
    if (!wx.cloud || !this._config.enableCloudCheck) {
      console.log('⚠️ 云开发未启用或配置，跳过云函数健康检查')
      return status
    }

    for (const functionName of this._config.cloudFunctions) {
      try {
        const startTime = Date.now()

        // 调用云函数进行健康检查
        const result = await wx.cloud.callFunction({
          name: functionName,
          data: { action: 'healthCheck' }
        })

        const responseTime = Date.now() - startTime

        status[functionName] = {
          status: result.result?.success ? 'healthy' : 'unhealthy',
          responseTime,
          lastCheck: new Date().toISOString(),
          error: result.result?.success ? null : result.result?.message
        }

      } catch (error) {
        // 如果是函数不存在的错误，标记为未部署
        if (error.errCode === -501000 || error.message?.includes('FunctionName parameter could not be found')) {
          status[functionName] = {
            status: 'not_deployed',
            responseTime: null,
            lastCheck: new Date().toISOString(),
            error: '云函数未部署'
          }
          console.log(`⚠️ 云函数 ${functionName} 未部署，跳过检查`)
        } else {
          status[functionName] = {
            status: 'unhealthy',
            responseTime: null,
            lastCheck: new Date().toISOString(),
            error: error.message
          }
          console.error(`❌ 云函数 ${functionName} 健康检查失败:`, error)
        }
      }
    }

    return status
  },

  /**
   * 检查数据库健康状态
   * @returns {Object} 数据库状态
   */
  async checkDatabase() {
    const status = {}

    // 如果禁用了数据库检查，返回跳过状态
    if (!this._config.enableDatabaseCheck) {
      return {
        status: 'skipped',
        message: '数据库检查已禁用',
        lastCheck: new Date().toISOString()
      }
    }

    for (const collection of this._config.collections) {
      try {
        const startTime = Date.now()

        // 执行简单的数据库查询
        const result = await wx.cloud.database()
          .collection(collection)
          .limit(1)
          .get()

        const responseTime = Date.now() - startTime

        status[collection] = {
          status: 'healthy',
          responseTime,
          recordCount: result.data.length,
          lastCheck: new Date().toISOString()
        }

      } catch (error) {
        status[collection] = {
          status: 'unhealthy',
          responseTime: null,
          recordCount: 0,
          lastCheck: new Date().toISOString(),
          error: error.message
        }
      }
    }

    return status
  },

  /**
   * 检查性能指标
   * @returns {Object} 性能状态
   */
  async checkPerformance() {
    const performance = {
      memory: this.getMemoryUsage(),
      network: await this.checkNetworkPerformance(),
      errors: this.getErrorRate(),
      timestamp: new Date().toISOString()
    }
    
    // 评估性能状态
    const thresholds = this._config.performanceThresholds
    performance.status = 'healthy'
    
    if (performance.memory.used > thresholds.memoryUsage) {
      performance.status = 'warning'
    }
    
    if (performance.network.responseTime > thresholds.apiResponseTime) {
      performance.status = 'warning'
    }
    
    if (performance.errors.rate > thresholds.errorRate) {
      performance.status = 'unhealthy'
    }
    
    return performance
  },

  /**
   * 获取内存使用情况
   * @returns {Object} 内存使用信息
   */
  getMemoryUsage() {
    try {
      // 小程序环境下的内存检查 - 使用同步API
      const systemInfo = wx.getSystemInfoSync()

      return {
        used: 0, // 小程序无法直接获取内存使用
        total: 0,
        percentage: 0,
        platform: systemInfo.platform,
        version: systemInfo.version
      }
    } catch (error) {
      console.warn('⚠️ 获取系统信息失败:', error.message)
      return {
        used: 0,
        total: 0,
        percentage: 0,
        error: error.message
      }
    }
  },

  /**
   * 检查网络性能
   * @returns {Object} 网络性能信息
   */
  async checkNetworkPerformance() {
    // 如果禁用了云函数检查，跳过网络性能检查
    if (!this._config.enableCloudCheck) {
      return {
        responseTime: null,
        status: 'skipped',
        message: '网络性能检查已禁用（云函数检查已禁用）'
      }
    }

    try {
      const startTime = Date.now()

      // 执行一个简单的网络请求
      await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })

      const responseTime = Date.now() - startTime

      return {
        responseTime,
        status: responseTime < this._config.performanceThresholds.apiResponseTime ? 'good' : 'slow'
      }
    } catch (error) {
      // 不要抛出错误，只记录状态
      console.log('⚠️ 网络性能检查失败（这是正常的，如果云函数未部署）:', error.message)
      return {
        responseTime: null,
        status: 'unavailable',
        error: error.message
      }
    }
  },

  /**
   * 获取错误率
   * @returns {Object} 错误率信息
   */
  getErrorRate() {
    // 从错误处理器获取错误统计
    try {
      const { ErrorHandler } = require('./errorHandler.js')
      const stats = ErrorHandler.getErrorStats()
      
      const totalRequests = stats.total || 1
      const errorCount = stats.byLevel?.error || 0
      const rate = errorCount / totalRequests
      
      return {
        rate,
        errorCount,
        totalRequests,
        status: rate < this._config.performanceThresholds.errorRate ? 'good' : 'high'
      }
    } catch (error) {
      return {
        rate: 0,
        errorCount: 0,
        totalRequests: 0,
        status: 'unknown',
        error: error.message
      }
    }
  },

  /**
   * 计算整体健康状态
   * @param {Object} cloudFunctions - 云函数状态
   * @param {Object} database - 数据库状态
   * @param {Object} performance - 性能状态
   * @returns {string} 整体健康状态
   */
  calculateOverallHealth(cloudFunctions, database, performance) {
    const statuses = []
    
    // 检查云函数状态
    Object.values(cloudFunctions).forEach(func => {
      statuses.push(func.status)
    })
    
    // 检查数据库状态
    Object.values(database).forEach(db => {
      statuses.push(db.status)
    })
    
    // 检查性能状态
    statuses.push(performance.status)
    
    // 如果有任何不健康的组件，整体状态为不健康
    if (statuses.includes('unhealthy')) {
      return 'unhealthy'
    }
    
    // 如果有警告状态，整体状态为警告
    if (statuses.includes('warning')) {
      return 'warning'
    }
    
    // 否则为健康
    return 'healthy'
  },

  /**
   * 记录健康状态
   */
  logHealthStatus() {
    const status = this._healthStatus
    
    if (EnvironmentConfig.isDebugEnabled()) {
      console.log('🏥 健康检查结果:', {
        overall: status.overall,
        checkDuration: status.checkDuration + 'ms',
        timestamp: status.lastCheck
      })
    }
    
    // 在生产环境记录到日志系统
    if (EnvironmentConfig.isProduction()) {
      this.sendHealthReport(status)
    }
  },

  /**
   * 发送健康报告
   * @param {Object} status - 健康状态
   */
  async sendHealthReport(status) {
    try {
      // 发送到监控服务
      await wx.cloud.callFunction({
        name: 'reportHealth',
        data: {
          status,
          environment: EnvironmentConfig.currentEnv,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      console.error('❌ 健康报告发送失败:', error)
    }
  },

  /**
   * 检查告警条件
   */
  checkAlerts() {
    const status = this._healthStatus
    
    if (status.overall === 'unhealthy') {
      this.triggerAlert('critical', '系统健康状态异常', status)
    } else if (status.overall === 'warning') {
      this.triggerAlert('warning', '系统性能警告', status)
    }
  },

  /**
   * 触发告警
   * @param {string} level - 告警级别
   * @param {string} message - 告警消息
   * @param {Object} data - 告警数据
   */
  async triggerAlert(level, message, data) {
    console.warn(`🚨 ${level.toUpperCase()} 告警: ${message}`)

    // 暂时禁用云函数告警，只记录本地日志
    console.log('📝 告警详情:', {
      level,
      message,
      data,
      timestamp: new Date().toISOString()
    })

    // TODO: 当 sendAlert 云函数部署后，取消注释以下代码
    /*
    try {
      await wx.cloud.callFunction({
        name: 'sendAlert',
        data: {
          level,
          message,
          data,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      console.error('❌ 告警发送失败:', error)
    }
    */
  },

  /**
   * 获取健康状态
   * @returns {Object} 当前健康状态
   */
  getHealthStatus() {
    return { ...this._healthStatus }
  },

  /**
   * 获取监控统计
   * @returns {Object} 监控统计信息
   */
  getMonitoringStats() {
    return {
      isActive: this._monitoringActive,
      checkInterval: this._checkInterval,
      lastCheck: this._healthStatus.lastCheck,
      overallStatus: this._healthStatus.overall
    }
  },

  /**
   * 销毁监控系统
   */
  destroy() {
    this.stopMonitoring()
    this._healthStatus = {
      overall: 'unknown',
      cloudFunctions: {},
      database: {},
      performance: {},
      lastCheck: null
    }
    console.log('🗑️ 健康监控系统已销毁')
  }
}

module.exports = {
  HealthMonitor
}
