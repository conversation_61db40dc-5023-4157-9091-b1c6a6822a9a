// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 内置权限验证函数
async function verifyAdmin(openid) {
  try {
    if (!openid) {
      return { isAdmin: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isAdmin: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isAdmin = user.auth && user.auth.role === 'admin' && user.auth.status === 'active'

    return {
      isAdmin,
      user,
      message: isAdmin ? '权限验证通过' : '权限不足'
    }
  } catch (error) {
    console.error('权限验证失败:', error)
    return { isAdmin: false, message: '权限验证失败' }
  }
}

async function verifyUser(openid) {
  try {
    if (!openid) {
      return { isValid: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isValid: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isValid = user.auth && user.auth.status === 'active'

    return {
      isValid,
      user,
      message: isValid ? '用户验证通过' : '用户状态异常'
    }
  } catch (error) {
    console.error('用户验证失败:', error)
    return { isValid: false, message: '用户验证失败' }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { code, userInfo, action = 'login' } = event
  const wxContext = cloud.getWXContext()

  try {
    // 如果是权限验证请求
    if (action === 'validatePermission') {
      return await handlePermissionValidation(event, wxContext)
    }

    // 原有的登录逻辑
    console.log('用户登录请求:', { openid: wxContext.OPENID, userInfo: userInfo?.nickName })

    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: wxContext.OPENID
    }).get()

    // 构建用户数据
    let userData = {
      openid: wxContext.OPENID,
      unionid: wxContext.UNIONID,
      lastLoginTime: new Date(),
      appid: wxContext.APPID,
      env: wxContext.ENV
    }

    // 如果提供了用户信息，添加到用户数据中
    if (userInfo) {
      userData.profile = {
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        gender: userInfo.gender,
        country: userInfo.country,
        province: userInfo.province,
        city: userInfo.city,
        language: userInfo.language
      }
    }

    if (userResult.data.length === 0) {
      // 新用户，创建用户记录
      userData.createTime = new Date()
      userData.stats = {
        likeCount: 0,
        collectCount: 0,
        downloadCount: 0
      }
      userData.auth = {
        role: 'user',
        status: 'active'
      }

      const addResult = await db.collection('users').add({
        data: userData
      })

      console.log('新用户创建成功:', addResult._id)
    } else {
      // 老用户，更新登录时间和用户信息
      const updateData = {
        lastLoginTime: new Date()
      }

      // 如果提供了用户信息，更新用户资料
      if (userInfo) {
        updateData.profile = userData.profile
      }

      await db.collection('users').doc(userResult.data[0]._id).update({
        data: updateData
      })

      console.log('用户信息更新成功:', userResult.data[0]._id)
    }

    return {
      success: true,
      openid: wxContext.OPENID,
      unionid: wxContext.UNIONID,
      userInfo: userInfo,
      message: '登录成功'
    }
  } catch (error) {
    console.error('用户登录失败:', error)
    return {
      success: false,
      error: error.message,
      message: '登录失败，请重试'
    }
  }
}

/**
 * 处理权限验证请求
 * @param {Object} event - 事件参数
 * @param {Object} wxContext - 微信上下文
 * @returns {Object} 验证结果
 */
async function handlePermissionValidation(event, wxContext) {
  const { requiredRole = 'user' } = event

  console.log('权限验证请求:', { requiredRole, openid: wxContext.OPENID })

  try {
    let result

    switch (requiredRole) {
      case 'admin':
        result = await verifyAdmin(wxContext.OPENID)
        return {
          success: true,
          isValid: result.isAdmin,
          user: result.user,
          role: result.isAdmin ? 'admin' : 'user',
          message: result.message
        }

      case 'user':
      default:
        result = await verifyUser(wxContext.OPENID)
        return {
          success: true,
          isValid: result.isValid,
          user: result.user,
          role: result.user?.auth?.role || 'user',
          message: result.message
        }
    }
  } catch (error) {
    console.error('权限验证失败:', error)
    return {
      success: false,
      isValid: false,
      message: '权限验证失败: ' + error.message
    }
  }
}