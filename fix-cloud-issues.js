#!/usr/bin/env node

/**
 * 云开发问题修复脚本
 * 用于诊断和修复微信小程序云开发相关问题
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 云开发问题修复脚本')
console.log('=' .repeat(50))

class CloudIssueFixer {
  constructor() {
    this.issues = []
    this.fixes = []
    this.warnings = []
  }

  /**
   * 运行完整的诊断和修复流程
   */
  async run() {
    console.log('🚀 开始诊断云开发问题...\n')

    // 1. 检查项目配置
    this.checkProjectConfig()

    // 2. 检查云函数配置
    this.checkCloudFunctions()

    // 3. 检查app.js云开发初始化
    this.checkAppJsCloudInit()

    // 4. 检查API调用代码
    this.checkAPICallCode()

    // 5. 生成修复建议
    this.generateFixSuggestions()

    // 6. 输出结果
    this.outputResults()
  }

  /**
   * 检查项目配置
   */
  checkProjectConfig() {
    console.log('📋 检查项目配置...')

    const projectConfigPath = 'project.config.json'
    if (!fs.existsSync(projectConfigPath)) {
      this.issues.push({
        type: 'MISSING_PROJECT_CONFIG',
        severity: 'HIGH',
        message: 'project.config.json 文件不存在'
      })
      return
    }

    try {
      const config = JSON.parse(fs.readFileSync(projectConfigPath, 'utf8'))
      
      // 检查云开发配置
      if (!config.cloudfunctionRoot) {
        this.issues.push({
          type: 'MISSING_CLOUD_ROOT',
          severity: 'HIGH',
          message: 'project.config.json 中缺少 cloudfunctionRoot 配置'
        })
      } else {
        console.log(`   ✅ 云函数根目录: ${config.cloudfunctionRoot}`)
      }

      // 检查appid
      if (!config.appid || config.appid === 'touristappid') {
        this.warnings.push({
          type: 'INVALID_APPID',
          message: '请在project.config.json中配置正确的appid'
        })
      }

    } catch (error) {
      this.issues.push({
        type: 'INVALID_PROJECT_CONFIG',
        severity: 'HIGH',
        message: 'project.config.json 格式错误: ' + error.message
      })
    }
  }

  /**
   * 检查云函数配置
   */
  checkCloudFunctions() {
    console.log('☁️ 检查云函数配置...')

    const cloudFunctionDir = 'cloudfunctions'
    if (!fs.existsSync(cloudFunctionDir)) {
      this.issues.push({
        type: 'MISSING_CLOUD_DIR',
        severity: 'CRITICAL',
        message: 'cloudfunctions 目录不存在'
      })
      return
    }

    // 检查关键云函数
    const requiredFunctions = ['dataAPI', 'login', 'getCategories', 'getEmojiList']
    
    requiredFunctions.forEach(funcName => {
      const funcPath = path.join(cloudFunctionDir, funcName)
      
      if (!fs.existsSync(funcPath)) {
        this.issues.push({
          type: 'MISSING_CLOUD_FUNCTION',
          severity: 'HIGH',
          function: funcName,
          message: `云函数 ${funcName} 不存在`
        })
        return
      }

      // 检查index.js
      const indexPath = path.join(funcPath, 'index.js')
      if (!fs.existsSync(indexPath)) {
        this.issues.push({
          type: 'MISSING_FUNCTION_INDEX',
          severity: 'HIGH',
          function: funcName,
          message: `云函数 ${funcName} 缺少 index.js`
        })
      }

      // 检查package.json
      const packagePath = path.join(funcPath, 'package.json')
      if (!fs.existsSync(packagePath)) {
        this.issues.push({
          type: 'MISSING_FUNCTION_PACKAGE',
          severity: 'MEDIUM',
          function: funcName,
          message: `云函数 ${funcName} 缺少 package.json`
        })
      } else {
        // 检查依赖
        try {
          const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
          if (!packageJson.dependencies || !packageJson.dependencies['wx-server-sdk']) {
            this.issues.push({
              type: 'MISSING_WX_SDK',
              severity: 'HIGH',
              function: funcName,
              message: `云函数 ${funcName} 缺少 wx-server-sdk 依赖`
            })
          }
        } catch (error) {
          this.issues.push({
            type: 'INVALID_PACKAGE_JSON',
            severity: 'MEDIUM',
            function: funcName,
            message: `云函数 ${funcName} 的 package.json 格式错误`
          })
        }
      }

      console.log(`   ✅ 云函数 ${funcName} 基础文件检查完成`)
    })
  }

  /**
   * 检查app.js云开发初始化
   */
  checkAppJsCloudInit() {
    console.log('📱 检查app.js云开发初始化...')

    const appJsPath = 'app.js'
    if (!fs.existsSync(appJsPath)) {
      this.issues.push({
        type: 'MISSING_APP_JS',
        severity: 'CRITICAL',
        message: 'app.js 文件不存在'
      })
      return
    }

    const content = fs.readFileSync(appJsPath, 'utf8')
    
    // 检查云开发初始化
    if (!content.includes('wx.cloud.init')) {
      this.issues.push({
        type: 'MISSING_CLOUD_INIT',
        severity: 'CRITICAL',
        message: 'app.js 中缺少 wx.cloud.init() 调用'
      })
    }

    // 检查环境ID配置
    if (!content.includes('env:') && !content.includes('DYNAMIC_CURRENT_ENV')) {
      this.warnings.push({
        type: 'MISSING_ENV_CONFIG',
        message: '建议在云开发初始化时指定环境ID'
      })
    }

    console.log('   ✅ app.js 检查完成')
  }

  /**
   * 检查API调用代码
   */
  checkAPICallCode() {
    console.log('🔍 检查API调用代码...')

    // 检查首页代码
    const indexJsPath = 'pages/index/index.js'
    if (fs.existsSync(indexJsPath)) {
      const content = fs.readFileSync(indexJsPath, 'utf8')
      
      // 检查是否有错误处理
      if (content.includes('wx.cloud.callFunction') && !content.includes('catch')) {
        this.warnings.push({
          type: 'MISSING_ERROR_HANDLING',
          file: 'pages/index/index.js',
          message: '建议为云函数调用添加错误处理'
        })
      }
    }

    console.log('   ✅ API调用代码检查完成')
  }

  /**
   * 生成修复建议
   */
  generateFixSuggestions() {
    console.log('💡 生成修复建议...')

    this.issues.forEach(issue => {
      switch (issue.type) {
        case 'MISSING_CLOUD_INIT':
          this.fixes.push({
            issue: issue.type,
            action: '在app.js的onLaunch方法中添加云开发初始化代码',
            code: `
// 在app.js的onLaunch方法中添加
if (wx.cloud) {
  wx.cloud.init({
    env: 'your-env-id', // 替换为你的环境ID
    traceUser: true
  })
  console.log('✅ 云开发初始化成功')
} else {
  console.error('❌ 请使用 2.2.3 或以上的基础库以使用云能力')
}`
          })
          break

        case 'MISSING_WX_SDK':
          this.fixes.push({
            issue: issue.type,
            function: issue.function,
            action: `为云函数 ${issue.function} 添加 wx-server-sdk 依赖`,
            command: `cd cloudfunctions/${issue.function} && npm install wx-server-sdk@latest`
          })
          break

        case 'MISSING_CLOUD_ROOT':
          this.fixes.push({
            issue: issue.type,
            action: '在project.config.json中添加cloudfunctionRoot配置',
            code: `
// 在project.config.json中添加
{
  "cloudfunctionRoot": "cloudfunctions/",
  // ... 其他配置
}`
          })
          break
      }
    })
  }

  /**
   * 输出结果
   */
  outputResults() {
    console.log('\n' + '='.repeat(50))
    console.log('📊 诊断结果汇总')
    console.log('='.repeat(50))

    // 输出问题
    if (this.issues.length > 0) {
      console.log('\n❌ 发现的问题:')
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.severity}] ${issue.message}`)
        if (issue.function) {
          console.log(`   云函数: ${issue.function}`)
        }
      })
    }

    // 输出警告
    if (this.warnings.length > 0) {
      console.log('\n⚠️ 警告信息:')
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.message}`)
        if (warning.file) {
          console.log(`   文件: ${warning.file}`)
        }
      })
    }

    // 输出修复建议
    if (this.fixes.length > 0) {
      console.log('\n🔧 修复建议:')
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix.action}`)
        if (fix.command) {
          console.log(`   执行命令: ${fix.command}`)
        }
        if (fix.code) {
          console.log(`   代码示例:${fix.code}`)
        }
        console.log('')
      })
    }

    // 总结
    console.log('\n📋 总结:')
    console.log(`   发现问题: ${this.issues.length} 个`)
    console.log(`   警告信息: ${this.warnings.length} 个`)
    console.log(`   修复建议: ${this.fixes.length} 个`)

    if (this.issues.length === 0) {
      console.log('\n🎉 恭喜！没有发现严重问题')
    } else {
      console.log('\n💡 建议按照修复建议逐一解决问题')
    }

    console.log('\n📖 更多帮助:')
    console.log('   1. 确保在微信开发者工具中打开项目')
    console.log('   2. 确保云开发服务已开通')
    console.log('   3. 确保云函数已正确部署')
    console.log('   4. 检查网络连接和开发者工具版本')
  }
}

// 运行修复脚本
const fixer = new CloudIssueFixer()
fixer.run().catch(error => {
  console.error('❌ 修复脚本执行失败:', error)
  process.exit(1)
})
