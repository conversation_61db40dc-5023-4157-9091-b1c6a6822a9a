@echo off
echo ========================================
echo 🚀 启动表情包管理后台 - 实时同步版本
echo ========================================
echo.

echo 1. 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

echo.
echo 2. 启动管理后台服务器...
cd admin
start /B node server.js

echo.
echo 3. 等待服务器启动...
timeout /t 3 /nobreak >nul

echo.
echo 4. 打开管理后台...
start http://localhost:9000

echo.
echo ========================================
echo ✅ 管理后台已启动！
echo 📱 访问地址: http://localhost:9000
echo 🔄 实时同步: 已启用
echo 💾 数据库: 微信云开发
echo ========================================
echo.
echo 按任意键关闭此窗口...
pause >nul
