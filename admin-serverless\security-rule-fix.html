<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 安全规则问题修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .critical {
            background-color: #f8d7da;
            border: 2px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .solution {
            background-color: #d4edda;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #c82333; }
        .fix-btn {
            background-color: #28a745;
        }
        .fix-btn:hover {
            background-color: #218838;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 安全规则问题修复</h1>
        
        <div class="critical">
            <h3>🎯 问题确认</h3>
            <p><strong>根本原因:</strong> 数据库安全规则阻止匿名用户访问</p>
            <p><strong>现象:</strong> 匿名登录成功，但查询返回 undefined</p>
            <p><strong>解决方案:</strong> 修改数据库安全规则或使用正式登录</p>
        </div>
        
        <button onclick="testAuthStatus()">🔍 检查当前认证状态</button>
        <button onclick="testWithCustomAuth()" class="fix-btn">🔑 尝试自定义登录</button>
        <button onclick="showSecurityRuleFix()" class="fix-btn">📋 显示安全规则修复方案</button>
        
        <div id="results"></div>
        
        <div id="securityRuleFix" style="display: none;">
            <div class="solution">
                <h3>🛠️ 安全规则修复方案</h3>
                
                <h4>方案1: 允许匿名用户读取数据（推荐用于测试）</h4>
                <pre><code>{
  "read": "true",
  "write": "auth.loginType != 'ANONYMOUS'"
}</code></pre>
                
                <h4>方案2: 允许匿名用户读写数据（仅用于开发测试）</h4>
                <pre><code>{
  "read": "true",
  "write": "true"
}</code></pre>
                
                <h4>方案3: 基于集合的细粒度控制</h4>
                <pre><code>// emojis 集合 - 允许匿名读取
{
  "read": "true",
  "write": "auth.loginType != 'ANONYMOUS'"
}

// users 集合 - 仅登录用户
{
  "read": "auth.loginType != 'ANONYMOUS'",
  "write": "auth.loginType != 'ANONYMOUS'"
}</code></pre>
                
                <h4>📝 修改步骤：</h4>
                <ol>
                    <li>打开 <a href="https://console.cloud.tencent.com/tcb/database/security" target="_blank">云开发控制台 - 数据库安全规则</a></li>
                    <li>选择对应的集合（如 emojis, categories 等）</li>
                    <li>修改安全规则为上述方案之一</li>
                    <li>保存并等待生效（通常几秒钟）</li>
                    <li>重新测试数据库查询</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let tcbApp = null;
        let auth = null;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }

        async function initSDK() {
            if (tcbApp) return;
            
            tcbApp = window.cloudbase.init({
                env: ENV_ID,
                region: 'ap-shanghai',
                timeout: 10000,
                traceUser: true
            });
            
            auth = tcbApp.auth();
        }

        async function testAuthStatus() {
            log('🔍 检查当前认证状态...', 'info');
            document.getElementById('results').innerHTML = '';
            
            try {
                await initSDK();
                
                // 检查当前用户
                const currentUser = auth.currentUser;
                log(`当前用户: ${currentUser ? '已登录' : '未登录'}`, 'info');
                
                if (currentUser) {
                    log(`用户ID: ${currentUser.uid}`, 'info');
                    log(`登录类型: ${currentUser.loginType || '未知'}`, 'info');
                }
                
                // 检查登录范围
                try {
                    const loginScope = await auth.loginScope();
                    log(`登录范围: ${loginScope}`, 'info');
                    
                    if (loginScope === 'anonymous') {
                        log('🎯 确认：当前为匿名登录状态', 'warning');
                        log('💡 这解释了为什么数据库查询返回 undefined', 'warning');
                        log('💡 匿名用户默认被安全规则阻止访问数据库', 'warning');
                    }
                } catch (error) {
                    log(`获取登录范围失败: ${error.message}`, 'error');
                }
                
                // 尝试简单的数据库查询来验证权限
                log('=== 测试数据库访问权限 ===', 'info');
                const db = tcbApp.database();
                
                try {
                    const result = await db.collection('emojis').limit(1).get();
                    if (result && result.data) {
                        log(`✅ 数据库访问成功！查询到 ${result.data.length} 条记录`, 'success');
                    } else {
                        log(`❌ 数据库查询返回: ${typeof result}`, 'error');
                        log('🔒 这确认了安全规则阻止了访问', 'error');
                    }
                } catch (dbError) {
                    log(`❌ 数据库访问被拒绝: ${dbError.message}`, 'error');
                    log('🔒 确认：安全规则阻止了匿名用户访问', 'error');
                }
                
            } catch (error) {
                log(`❌ 认证检查失败: ${error.message}`, 'error');
            }
        }

        async function testWithCustomAuth() {
            log('🔑 尝试自定义登录方式...', 'info');
            
            try {
                await initSDK();
                
                // 尝试用户名密码登录（如果已配置）
                log('尝试用户名密码登录...', 'info');
                
                try {
                    // 这里需要您在云开发控制台配置用户名密码登录
                    await auth.signInWithUsernameAndPassword('test', 'test123');
                    log('✅ 用户名密码登录成功', 'success');
                } catch (loginError) {
                    log(`用户名密码登录失败: ${loginError.message}`, 'warning');
                    log('💡 可能需要在控制台开启用户名密码登录', 'info');
                }
                
                // 检查新的登录状态
                const currentUser = auth.currentUser;
                if (currentUser) {
                    log(`新登录状态 - 用户ID: ${currentUser.uid}`, 'info');
                    log(`登录类型: ${currentUser.loginType || '未知'}`, 'info');
                    
                    // 重新测试数据库访问
                    const db = tcbApp.database();
                    const result = await db.collection('emojis').limit(1).get();
                    
                    if (result && result.data) {
                        log(`🎉 正式登录后数据库访问成功！`, 'success');
                    } else {
                        log(`仍然无法访问数据库`, 'error');
                    }
                }
                
            } catch (error) {
                log(`❌ 自定义登录测试失败: ${error.message}`, 'error');
            }
        }

        function showSecurityRuleFix() {
            const fixDiv = document.getElementById('securityRuleFix');
            fixDiv.style.display = fixDiv.style.display === 'none' ? 'block' : 'none';
            
            if (fixDiv.style.display === 'block') {
                log('📋 已显示安全规则修复方案', 'success');
                log('💡 请按照步骤修改云开发控制台的安全规则', 'info');
            }
        }

        // 页面加载时的说明
        window.addEventListener('DOMContentLoaded', function() {
            log('🔒 安全规则诊断工具已加载', 'success');
            log('💡 点击"检查当前认证状态"开始诊断', 'info');
            log('🎯 预期结果：确认匿名登录状态和权限问题', 'info');
        });
    </script>
</body>
</html>
