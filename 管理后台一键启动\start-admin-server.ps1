# PowerShell script to start Emoji Admin Panel Server
# Encoding: UTF-8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Emoji Admin Panel Server Launcher" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get script directory and project paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$AdminDir = Join-Path $ProjectRoot "admin-serverless"

Write-Host "Script Directory: $ScriptDir" -ForegroundColor Gray
Write-Host "Project Root: $ProjectRoot" -ForegroundColor Gray
Write-Host "Admin Directory: $AdminDir" -ForegroundColor Gray
Write-Host ""

# Check if admin-serverless directory exists
if (-not (Test-Path $AdminDir)) {
    Write-Host "ERROR: admin-serverless directory not found" -ForegroundColor Red
    Write-Host "Expected path: $AdminDir" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please make sure this script is in the correct location:" -ForegroundColor Yellow
    Write-Host "- Project root folder should contain admin-serverless directory" -ForegroundColor Yellow
    Write-Host "- This script should be in a subfolder of the project root" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Admin directory found: $AdminDir" -ForegroundColor Green
Write-Host ""

# Change to admin directory
Set-Location $AdminDir

# Check Python installation
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "Python installation verified" -ForegroundColor Green
    Write-Host $pythonVersion -ForegroundColor Gray
    Write-Host ""
} catch {
    Write-Host "ERROR: Python not found" -ForegroundColor Red
    Write-Host "Please install Python from: https://www.python.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if main.html exists
if (-not (Test-Path "main.html")) {
    Write-Host "ERROR: main.html not found in admin-serverless directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Available HTML files:" -ForegroundColor Yellow
    Get-ChildItem -Name "*.html"
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "main.html found" -ForegroundColor Green
Write-Host ""

# Kill any existing servers on port 9001
Write-Host "Checking for existing servers on port 9001..." -ForegroundColor Yellow
try {
    $processes = Get-NetTCPConnection -LocalPort 9001 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
    foreach ($pid in $processes) {
        Write-Host "Killing process $pid" -ForegroundColor Yellow
        Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
    }
} catch {
    # Ignore errors if no processes found
}

Write-Host ""
Write-Host "Starting HTTP server on port 9001..." -ForegroundColor Green
Write-Host ""
Write-Host "Server URLs:" -ForegroundColor Cyan
Write-Host "  Main Admin Panel: http://localhost:9001/main.html" -ForegroundColor White
Write-Host "  Index Page: http://localhost:9001/index.html" -ForegroundColor White
Write-Host "  Direct Access: http://localhost:9001/" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Start browser automatically
Start-Process "http://localhost:9001/main.html"

# Start Python HTTP server
try {
    python -m http.server 9001
} catch {
    Write-Host ""
    Write-Host "Server stopped or failed to start" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}
