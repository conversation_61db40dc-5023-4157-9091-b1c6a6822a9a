// 强制调用loadCategories函数并调试
const { chromium } = require('playwright');

async function forceLoadCategoriesDebug() {
    console.log('🔧 强制调用loadCategories函数并调试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[BROWSER] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：检查AdminApp对象和loadCategories函数');
        
        // 检查AdminApp对象和loadCategories函数
        const appCheck = await page.evaluate(() => {
            return {
                hasAdminApp: typeof AdminApp !== 'undefined',
                hasAdminAppData: typeof AdminApp !== 'undefined' && !!AdminApp.data,
                hasLoadCategoriesFunction: typeof loadCategories !== 'undefined',
                adminAppDataKeys: typeof AdminApp !== 'undefined' && AdminApp.data ? Object.keys(AdminApp.data) : [],
                categoriesLength: typeof AdminApp !== 'undefined' && AdminApp.data && AdminApp.data.categories ? AdminApp.data.categories.length : 'N/A'
            };
        });
        
        console.log('📊 应用对象检查:');
        console.log(`AdminApp存在: ${appCheck.hasAdminApp}`);
        console.log(`AdminApp.data存在: ${appCheck.hasAdminAppData}`);
        console.log(`loadCategories函数存在: ${appCheck.hasLoadCategoriesFunction}`);
        console.log(`AdminApp.data的键: [${appCheck.adminAppDataKeys.join(', ')}]`);
        console.log(`当前categories长度: ${appCheck.categoriesLength}`);
        
        if (!appCheck.hasAdminApp || !appCheck.hasAdminAppData || !appCheck.hasLoadCategoriesFunction) {
            console.log('🔴 基础对象或函数缺失，无法继续测试');
            return { success: false, error: '基础对象或函数缺失' };
        }
        
        console.log('\n📍 第二步：强制调用loadCategories函数');
        
        // 强制调用loadCategories函数
        const loadResult = await page.evaluate(async () => {
            try {
                console.log('🔧 开始强制调用loadCategories函数...');
                
                // 调用loadCategories函数
                await loadCategories();
                
                console.log('🔧 loadCategories函数调用完成');
                
                // 检查调用后的状态
                return {
                    success: true,
                    categoriesLength: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                    categories: AdminApp.data.categories ? AdminApp.data.categories.map(cat => ({
                        _id: cat._id,
                        name: cat.name,
                        gradient: cat.gradient,
                        hasGradient: !!cat.gradient,
                        gradientPreview: cat.gradient ? cat.gradient.substring(0, 50) + '...' : 'N/A'
                    })) : []
                };
            } catch (error) {
                console.error('🔴 loadCategories函数调用失败:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 loadCategories调用结果:');
        if (loadResult.success) {
            console.log(`✅ 调用成功，分类数量: ${loadResult.categoriesLength}`);
            
            if (loadResult.categoriesLength > 0) {
                console.log('\n📋 加载的分类数据:');
                loadResult.categories.forEach((cat, index) => {
                    console.log(`  ${index + 1}. ${cat.name}`);
                    console.log(`     ID: ${cat._id}`);
                    console.log(`     有渐变: ${cat.hasGradient}`);
                    console.log(`     渐变预览: ${cat.gradientPreview}`);
                    console.log('');
                });
            } else {
                console.log('🔴 没有加载到分类数据');
            }
        } else {
            console.log(`🔴 调用失败: ${loadResult.error}`);
        }
        
        console.log('\n📍 第三步：手动调用renderCategoryTable函数');
        
        // 手动调用renderCategoryTable函数
        const renderResult = await page.evaluate(() => {
            try {
                if (!AdminApp.data.categories || AdminApp.data.categories.length === 0) {
                    return { success: false, error: '没有分类数据可供渲染' };
                }
                
                console.log('🔧 开始手动调用renderCategoryTable函数...');
                console.log('🔧 传入的分类数据:', AdminApp.data.categories);
                
                // 调用renderCategoryTable函数
                renderCategoryTable(AdminApp.data.categories);
                
                console.log('🔧 renderCategoryTable函数调用完成');
                
                // 检查渲染结果
                const categoryTable = document.querySelector('#category-content table');
                if (!categoryTable) {
                    return { success: false, error: '渲染后未找到表格' };
                }
                
                const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
                return {
                    success: true,
                    rowCount: rows.length,
                    gradientCells: rows.map((row, index) => {
                        const gradientCell = row.querySelector('td:nth-child(4)');
                        const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                        
                        return {
                            index: index,
                            gradientDivText: gradientDiv ? gradientDiv.textContent : 'N/A',
                            gradientDivStyle: gradientDiv ? gradientDiv.getAttribute('style') : 'N/A',
                            hasGradientStyle: gradientDiv ? gradientDiv.getAttribute('style').includes('gradient') : false
                        };
                    })
                };
            } catch (error) {
                console.error('🔴 renderCategoryTable函数调用失败:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 renderCategoryTable调用结果:');
        if (renderResult.success) {
            console.log(`✅ 渲染成功，表格行数: ${renderResult.rowCount}`);
            
            renderResult.gradientCells.forEach((cell) => {
                console.log(`\n行 ${cell.index + 1}:`);
                console.log(`  渐变div文本: ${cell.gradientDivText}`);
                console.log(`  渐变div样式: ${cell.gradientDivStyle.substring(0, 100)}...`);
                console.log(`  有渐变样式: ${cell.hasGradientStyle}`);
                
                if (cell.gradientDivText === '无渐变') {
                    console.log(`  🔴 问题: 仍显示"无渐变"`);
                } else if (cell.hasGradientStyle) {
                    console.log(`  ✅ 正常: 有渐变样式`);
                } else {
                    console.log(`  ⚠️ 异常: 渐变样式不正确`);
                }
            });
            
            // 统计结果
            const withGradient = renderResult.gradientCells.filter(cell => cell.hasGradientStyle).length;
            const withoutGradient = renderResult.gradientCells.filter(cell => cell.gradientDivText === '无渐变').length;
            
            console.log(`\n📊 渲染结果统计:`);
            console.log(`  有渐变样式: ${withGradient}/${renderResult.rowCount}`);
            console.log(`  显示"无渐变": ${withoutGradient}/${renderResult.rowCount}`);
            
            if (withGradient > 0 && withoutGradient === 0) {
                console.log(`  🎉 渐变显示完全正常！`);
            } else if (withGradient > 0) {
                console.log(`  ✅ 渐变显示部分正常`);
            } else {
                console.log(`  🔴 渐变显示完全异常`);
            }
        } else {
            console.log(`🔴 渲染失败: ${renderResult.error}`);
        }
        
        console.log('\n📍 第四步：问题根源分析');
        
        let problemSolved = false;
        let problemAnalysis = [];
        
        if (loadResult.success && loadResult.categoriesLength > 0) {
            problemAnalysis.push('✅ loadCategories函数工作正常');
            
            const categoriesWithGradient = loadResult.categories.filter(cat => cat.hasGradient).length;
            if (categoriesWithGradient > 0) {
                problemAnalysis.push('✅ 分类数据包含渐变信息');
                
                if (renderResult.success) {
                    const withGradient = renderResult.gradientCells.filter(cell => cell.hasGradientStyle).length;
                    if (withGradient > 0) {
                        problemAnalysis.push('✅ 表格渲染显示渐变');
                        problemSolved = true;
                    } else {
                        problemAnalysis.push('🔴 表格渲染不显示渐变 - renderCategoryTable函数有问题');
                    }
                } else {
                    problemAnalysis.push('🔴 表格渲染失败');
                }
            } else {
                problemAnalysis.push('🔴 分类数据不包含渐变信息 - 数据清理逻辑有问题');
            }
        } else {
            problemAnalysis.push('🔴 loadCategories函数失败或没有数据');
        }
        
        console.log('🔍 问题根源分析:');
        problemAnalysis.forEach(analysis => {
            console.log(`  ${analysis}`);
        });
        
        console.log(`\n🎯 问题状态: ${problemSolved ? '🎉 已解决' : '🔴 仍存在'}`);
        
        return {
            success: true,
            problemSolved: problemSolved,
            loadResult: loadResult,
            renderResult: renderResult,
            problemAnalysis: problemAnalysis
        };
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        await page.screenshot({ path: 'force-load-categories-debug-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'force-load-categories-debug.png', fullPage: true });
        console.log('\n📸 调试截图已保存: force-load-categories-debug.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行调试
forceLoadCategoriesDebug().then(result => {
    console.log('\n🎯 强制加载分类调试结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.problemSolved) {
        console.log('🎉 渐变显示问题已解决！');
    } else if (result.success) {
        console.log('⚠️ 问题仍存在，需要进一步修复。');
    } else {
        console.log('❌ 调试失败，无法确定问题状态。');
    }
}).catch(console.error);
