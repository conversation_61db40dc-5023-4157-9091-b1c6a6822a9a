// 小程序兼容性问题全面检查
const { chromium } = require('playwright');

async function testMiniprogramCompatibilityComprehensive() {
    console.log('🔍 小程序兼容性问题全面检查...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('兼容') || text.includes('版本') || text.includes('API') || text.includes('错误')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        console.log('\n📍 第一步：分析小程序基础配置兼容性');
        
        // 检查小程序基础配置
        const basicConfigCheck = await analyzeBasicConfig();
        console.log('📊 基础配置兼容性检查:');
        console.log(`微信小程序基础库版本要求: ${basicConfigCheck.minSdkVersion || '未设置'}`);
        console.log(`样式版本: ${basicConfigCheck.styleVersion}`);
        console.log(`云开发配置: ${basicConfigCheck.hasCloudConfig ? '✅ 已配置' : '🔴 未配置'}`);
        console.log(`自定义TabBar: ${basicConfigCheck.hasCustomTabBar ? '✅ 使用' : '🔴 未使用'}`);
        
        console.log('\n📍 第二步：检查API兼容性');
        
        // 通过管理后台模拟检查小程序API兼容性
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        // 检查云开发API兼容性
        const cloudApiCompatibility = await page.evaluate(async () => {
            const compatibilityResults = {
                cloudbase: {
                    available: typeof cloudbase !== 'undefined',
                    version: typeof cloudbase !== 'undefined' ? cloudbase.version || 'unknown' : 'N/A'
                },
                tcb: {
                    available: typeof tcb !== 'undefined',
                    version: typeof tcb !== 'undefined' ? tcb.version || 'unknown' : 'N/A'
                },
                database: {
                    available: false,
                    canQuery: false,
                    error: null
                },
                storage: {
                    available: false,
                    canUpload: false,
                    error: null
                },
                functions: {
                    available: false,
                    canCall: false,
                    error: null
                }
            };
            
            try {
                // 测试数据库API
                if (window.tcbApp) {
                    compatibilityResults.database.available = true;
                    
                    try {
                        const testQuery = await window.tcbApp.database().collection('categories').limit(1).get();
                        compatibilityResults.database.canQuery = true;
                    } catch (error) {
                        compatibilityResults.database.error = error.message;
                    }
                    
                    // 测试云函数API
                    compatibilityResults.functions.available = true;
                    
                    try {
                        const testFunction = await window.tcbApp.callFunction({
                            name: 'dataAPI',
                            data: { action: 'getCategories' }
                        });
                        compatibilityResults.functions.canCall = true;
                    } catch (error) {
                        compatibilityResults.functions.error = error.message;
                    }
                    
                    // 测试存储API
                    try {
                        const storage = window.tcbApp.uploadFile;
                        compatibilityResults.storage.available = !!storage;
                    } catch (error) {
                        compatibilityResults.storage.error = error.message;
                    }
                }
            } catch (error) {
                console.error('API兼容性检查错误:', error);
            }
            
            return compatibilityResults;
        });
        
        console.log('📊 云开发API兼容性检查:');
        console.log(`CloudBase SDK: ${cloudApiCompatibility.cloudbase.available ? '✅ 可用' : '🔴 不可用'} (版本: ${cloudApiCompatibility.cloudbase.version})`);
        console.log(`TCB SDK: ${cloudApiCompatibility.tcb.available ? '✅ 可用' : '🔴 不可用'} (版本: ${cloudApiCompatibility.tcb.version})`);
        console.log(`数据库API: ${cloudApiCompatibility.database.available ? '✅ 可用' : '🔴 不可用'}`);
        console.log(`  查询功能: ${cloudApiCompatibility.database.canQuery ? '✅ 正常' : '🔴 异常'}`);
        if (cloudApiCompatibility.database.error) {
            console.log(`  错误信息: ${cloudApiCompatibility.database.error}`);
        }
        console.log(`云函数API: ${cloudApiCompatibility.functions.available ? '✅ 可用' : '🔴 不可用'}`);
        console.log(`  调用功能: ${cloudApiCompatibility.functions.canCall ? '✅ 正常' : '🔴 异常'}`);
        if (cloudApiCompatibility.functions.error) {
            console.log(`  错误信息: ${cloudApiCompatibility.functions.error}`);
        }
        console.log(`存储API: ${cloudApiCompatibility.storage.available ? '✅ 可用' : '🔴 不可用'}`);
        if (cloudApiCompatibility.storage.error) {
            console.log(`  错误信息: ${cloudApiCompatibility.storage.error}`);
        }
        
        console.log('\n📍 第三步：检查小程序代码兼容性');
        
        // 分析小程序代码中的兼容性问题
        const codeCompatibilityCheck = await analyzeCodeCompatibility();
        
        console.log('📊 代码兼容性检查:');
        console.log(`ES6语法使用: ${codeCompatibilityCheck.es6Usage.count} 处`);
        if (codeCompatibilityCheck.es6Usage.count > 0) {
            console.log(`  主要使用: ${codeCompatibilityCheck.es6Usage.features.join(', ')}`);
        }
        
        console.log(`异步API使用: ${codeCompatibilityCheck.asyncApi.count} 处`);
        if (codeCompatibilityCheck.asyncApi.count > 0) {
            console.log(`  主要API: ${codeCompatibilityCheck.asyncApi.apis.join(', ')}`);
        }
        
        console.log(`新版本API使用: ${codeCompatibilityCheck.newVersionApi.count} 处`);
        if (codeCompatibilityCheck.newVersionApi.count > 0) {
            console.log(`  API列表: ${codeCompatibilityCheck.newVersionApi.apis.join(', ')}`);
        }
        
        console.log('\n📍 第四步：检查组件兼容性');
        
        // 检查小程序组件兼容性
        const componentCompatibility = await analyzeComponentCompatibility();
        
        console.log('📊 组件兼容性检查:');
        console.log(`自定义组件数量: ${componentCompatibility.customComponents.length}`);
        componentCompatibility.customComponents.forEach((comp, index) => {
            console.log(`  ${index + 1}. ${comp.name} (${comp.path})`);
            console.log(`     兼容性: ${comp.compatibility}`);
        });
        
        console.log(`第三方组件数量: ${componentCompatibility.thirdPartyComponents.length}`);
        componentCompatibility.thirdPartyComponents.forEach((comp, index) => {
            console.log(`  ${index + 1}. ${comp.name}`);
            console.log(`     版本要求: ${comp.versionRequirement}`);
        });
        
        console.log('\n📍 第五步：检查样式兼容性');
        
        // 检查样式兼容性
        const styleCompatibility = await analyzeStyleCompatibility();
        
        console.log('📊 样式兼容性检查:');
        console.log(`CSS3特性使用: ${styleCompatibility.css3Features.length} 种`);
        styleCompatibility.css3Features.forEach((feature, index) => {
            console.log(`  ${index + 1}. ${feature.name}: ${feature.compatibility}`);
        });
        
        console.log(`响应式设计: ${styleCompatibility.responsive ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`暗黑模式适配: ${styleCompatibility.darkMode ? '✅ 支持' : '🔴 不支持'}`);
        
        console.log('\n📍 第六步：检查网络兼容性');
        
        // 检查网络请求兼容性
        const networkCompatibility = await page.evaluate(async () => {
            const networkTests = {
                httpRequest: { supported: false, error: null },
                httpsRequest: { supported: false, error: null },
                websocket: { supported: false, error: null },
                uploadFile: { supported: false, error: null },
                downloadFile: { supported: false, error: null }
            };
            
            try {
                // 测试HTTP请求（通过云函数）
                const httpTest = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                networkTests.httpRequest.supported = true;
            } catch (error) {
                networkTests.httpRequest.error = error.message;
            }
            
            try {
                // 测试HTTPS请求
                networkTests.httpsRequest.supported = true; // 云开发默认HTTPS
            } catch (error) {
                networkTests.httpsRequest.error = error.message;
            }
            
            // 检查上传文件API
            try {
                networkTests.uploadFile.supported = !!window.tcbApp.uploadFile;
            } catch (error) {
                networkTests.uploadFile.error = error.message;
            }
            
            return networkTests;
        });
        
        console.log('📊 网络兼容性检查:');
        console.log(`HTTP请求: ${networkCompatibility.httpRequest.supported ? '✅ 支持' : '🔴 不支持'}`);
        if (networkCompatibility.httpRequest.error) {
            console.log(`  错误: ${networkCompatibility.httpRequest.error}`);
        }
        console.log(`HTTPS请求: ${networkCompatibility.httpsRequest.supported ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`文件上传: ${networkCompatibility.uploadFile.supported ? '✅ 支持' : '🔴 不支持'}`);
        
        console.log('\n📍 第七步：检查设备兼容性');
        
        // 检查设备兼容性
        const deviceCompatibility = await analyzeDeviceCompatibility();
        
        console.log('📊 设备兼容性检查:');
        console.log(`屏幕适配: ${deviceCompatibility.screenAdaptation ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`触摸事件: ${deviceCompatibility.touchEvents ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`相机功能: ${deviceCompatibility.camera ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`文件系统: ${deviceCompatibility.fileSystem ? '✅ 支持' : '🔴 不支持'}`);
        console.log(`本地存储: ${deviceCompatibility.localStorage ? '✅ 支持' : '🔴 不支持'}`);
        
        console.log('\n📍 第八步：检查版本兼容性');
        
        // 检查微信版本兼容性
        const versionCompatibility = await analyzeVersionCompatibility();
        
        console.log('📊 版本兼容性检查:');
        console.log(`最低支持版本: ${versionCompatibility.minVersion}`);
        console.log(`推荐版本: ${versionCompatibility.recommendedVersion}`);
        console.log(`已知兼容性问题: ${versionCompatibility.knownIssues.length} 个`);
        
        versionCompatibility.knownIssues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue.description}`);
            console.log(`     影响版本: ${issue.affectedVersions}`);
            console.log(`     解决方案: ${issue.solution}`);
        });
        
        console.log('\n🎯 兼容性问题检查总结:');
        
        // 综合评估兼容性
        const compatibilityScore = calculateCompatibilityScore({
            basicConfig: basicConfigCheck,
            cloudApi: cloudApiCompatibility,
            code: codeCompatibilityCheck,
            component: componentCompatibility,
            style: styleCompatibility,
            network: networkCompatibility,
            device: deviceCompatibility,
            version: versionCompatibility
        });
        
        console.log(`兼容性总分: ${compatibilityScore.total}/100`);
        console.log(`基础配置: ${compatibilityScore.basicConfig}/15`);
        console.log(`API兼容性: ${compatibilityScore.api}/20`);
        console.log(`代码兼容性: ${compatibilityScore.code}/15`);
        console.log(`组件兼容性: ${compatibilityScore.component}/10`);
        console.log(`样式兼容性: ${compatibilityScore.style}/10`);
        console.log(`网络兼容性: ${compatibilityScore.network}/15`);
        console.log(`设备兼容性: ${compatibilityScore.device}/10`);
        console.log(`版本兼容性: ${compatibilityScore.version}/5`);
        
        const compatibilityLevel = getCompatibilityLevel(compatibilityScore.total);
        console.log(`\n🎯 兼容性等级: ${compatibilityLevel.level} (${compatibilityLevel.description})`);
        
        if (compatibilityScore.total >= 80) {
            console.log('✅ 小程序兼容性良好，可以在大多数环境下正常运行！');
        } else if (compatibilityScore.total >= 60) {
            console.log('⚠️ 小程序兼容性一般，部分环境可能存在问题。');
        } else {
            console.log('🔴 小程序兼容性较差，需要重点优化。');
        }
        
        // 提供改进建议
        const improvements = generateImprovementSuggestions(compatibilityScore);
        if (improvements.length > 0) {
            console.log('\n📋 改进建议:');
            improvements.forEach((suggestion, index) => {
                console.log(`  ${index + 1}. ${suggestion}`);
            });
        }
        
        return {
            success: true,
            compatibilityScore: compatibilityScore,
            basicConfig: basicConfigCheck,
            cloudApi: cloudApiCompatibility,
            code: codeCompatibilityCheck,
            component: componentCompatibility,
            style: styleCompatibility,
            network: networkCompatibility,
            device: deviceCompatibility,
            version: versionCompatibility,
            improvements: improvements
        };
        
    } catch (error) {
        console.error('❌ 兼容性检查过程中出错:', error);
        await page.screenshot({ path: 'miniprogram-compatibility-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'miniprogram-compatibility-comprehensive.png', fullPage: true });
        console.log('\n📸 检查截图已保存: miniprogram-compatibility-comprehensive.png');
        
        console.log('\n⏸️ 检查完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 辅助函数：分析基础配置
async function analyzeBasicConfig() {
    const fs = require('fs').promises;
    
    try {
        // 读取app.json配置
        const appConfig = JSON.parse(await fs.readFile('app.json', 'utf8'));
        
        return {
            minSdkVersion: appConfig.minSdkVersion || '未设置',
            styleVersion: appConfig.style || 'v1',
            hasCloudConfig: !!appConfig.cloud,
            hasCustomTabBar: !!appConfig.tabBar?.custom,
            pages: appConfig.pages?.length || 0,
            subPackages: appConfig.subPackages?.length || 0
        };
    } catch (error) {
        return {
            error: error.message,
            minSdkVersion: '无法读取',
            styleVersion: '无法读取',
            hasCloudConfig: false,
            hasCustomTabBar: false
        };
    }
}

// 辅助函数：分析代码兼容性
async function analyzeCodeCompatibility() {
    const fs = require('fs').promises;
    const path = require('path');
    
    const codeAnalysis = {
        es6Usage: { count: 0, features: [] },
        asyncApi: { count: 0, apis: [] },
        newVersionApi: { count: 0, apis: [] }
    };
    
    try {
        // 分析主要页面的JS文件
        const pageDirs = ['pages/index', 'pages/search', 'pages/category', 'pages/profile'];
        
        for (const pageDir of pageDirs) {
            try {
                const jsFile = path.join(pageDir, `${path.basename(pageDir)}.js`);
                const content = await fs.readFile(jsFile, 'utf8');
                
                // 检查ES6特性
                if (content.includes('const ') || content.includes('let ')) {
                    codeAnalysis.es6Usage.count++;
                    if (!codeAnalysis.es6Usage.features.includes('const/let')) {
                        codeAnalysis.es6Usage.features.push('const/let');
                    }
                }
                
                if (content.includes('=>')) {
                    codeAnalysis.es6Usage.count++;
                    if (!codeAnalysis.es6Usage.features.includes('arrow functions')) {
                        codeAnalysis.es6Usage.features.push('arrow functions');
                    }
                }
                
                // 检查异步API
                if (content.includes('async ') || content.includes('await ')) {
                    codeAnalysis.asyncApi.count++;
                    if (!codeAnalysis.asyncApi.apis.includes('async/await')) {
                        codeAnalysis.asyncApi.apis.push('async/await');
                    }
                }
                
                if (content.includes('Promise')) {
                    codeAnalysis.asyncApi.count++;
                    if (!codeAnalysis.asyncApi.apis.includes('Promise')) {
                        codeAnalysis.asyncApi.apis.push('Promise');
                    }
                }
                
            } catch (error) {
                // 文件不存在或读取失败，跳过
            }
        }
    } catch (error) {
        // 目录不存在或其他错误
    }
    
    return codeAnalysis;
}

// 其他辅助函数将在下一个文件中继续...
