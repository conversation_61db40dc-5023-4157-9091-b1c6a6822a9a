// 云函数入口文件 - 获取轮播图
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const now = new Date()
    
    // 获取有效的轮播图
    const result = await db.collection('banners')
      .where({
        status: 'active',
        startTime: _.lte(now),
        endTime: _.gte(now)
      })
      .orderBy('sortOrder', 'asc')
      .get()
    
    // 增加展示次数统计
    const bannerIds = result.data.map(banner => banner._id)
    if (bannerIds.length > 0) {
      await Promise.all(
        bannerIds.map(id => 
          db.collection('banners').doc(id).update({
            data: {
              impressions: _.inc(1)
            }
          })
        )
      )
    }
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取轮播图失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}