/**
 * 表情包管理后台 - Serverless版
 * 基于微信云开发的纯前端管理系统
 */

// 全局变量
let currentTab = 'dashboard';
let currentData = {};

// 初始化应用 - 配合智能SDK加载器
window.initApp = async function() {
    console.log('🚀 表情包管理后台启动 - Serverless版');

    // 初始化云开发
    await initCloud();

    // 加载仪表盘数据
    loadDashboard();

    // 绑定事件
    bindEvents();
};

// 兼容原有的DOMContentLoaded事件（如果SDK已经加载）
document.addEventListener('DOMContentLoaded', async function() {
    // 检查SDK是否已经加载
    if (window.cloudbase || window.tcb) {
        console.log('🎯 SDK已加载，直接初始化应用');
        await window.initApp();
    } else {
        console.log('⏳ 等待SDK加载完成...');
        // SDK加载器会调用window.initApp()
    }
});

// 初始化云开发
async function initCloud() {
    // 云开发环境ID
    const envId = 'cloud1-5g6pvnpl88dc0142';

    try {
        // 检查是否在微信环境中
        if (typeof wx !== 'undefined' && wx.cloud) {
            // 在微信小程序环境中
            wx.cloud.init({
                env: envId,
                traceUser: true
            });
            console.log('✅ 微信云开发初始化成功:', envId);
            window.cloudInitialized = true;
        } else {
            // 在浏览器环境中，初始化Web SDK
            console.log('🌐 浏览器环境，初始化Web SDK...');
            await initWebSDK(envId);
        }
    } catch (error) {
        console.error('云开发初始化失败:', error);
        showError('云开发初始化失败，请检查环境配置');
        window.cloudInitialized = false;
    }
}

// 初始化云开发Web SDK - 优化版本（基于技术文档）
async function initWebSDK(envId) {
    try {
        console.log('🔍 检查SDK可用性...');
        console.log('  - typeof tcb:', typeof tcb);
        console.log('  - typeof cloudbase:', typeof cloudbase);
        console.log('  - window.tcb:', typeof window.tcb);
        console.log('  - window.cloudbase:', typeof window.cloudbase);

        // 强制使用CloudBase 2.0版本（根据技术文档）
        if (typeof window.cloudbase === 'undefined') {
            throw new Error('CloudBase SDK 2.0未加载，请检查网络连接或刷新页面');
        }

        console.log('🎯 使用CloudBase SDK 2.17.5版本（强制2.0版本）');

        // CloudBase 2.0初始化（根据技术文档）
        console.log('🚀 开始初始化CloudBase SDK 2.17.5...');

        try {
            // CloudBase 2.0版本必须提供clientId参数
            const initConfig = {
                env: envId,
                clientId: envId  // SDK 2.0必需参数
            };

            console.log('🔧 使用CloudBase 2.0配置（包含clientId）');
            window.tcbApp = window.cloudbase.init(initConfig);

            console.log('✅ CloudBase SDK 2.0初始化成功，连接真实数据库');
            console.log('🌐 环境ID:', envId);
            console.log('🔧 初始化配置:', initConfig);
            console.log('📱 SDK实例:', window.tcbApp);

            // 检查SDK实例的可用方法
            console.log('🔍 SDK可用方法:', Object.keys(window.tcbApp));

            // 测试关键方法是否存在
            if (window.tcbApp.callFunction) {
                console.log('✅ callFunction方法可用');
            } else {
                console.warn('⚠️ callFunction方法不可用');
            }

            if (window.tcbApp.database) {
                console.log('✅ database方法可用');
                try {
                    const testDb = window.tcbApp.database();
                    console.log('✅ 数据库对象创建成功:', !!testDb);
                } catch (dbError) {
                    console.warn('⚠️ 数据库对象创建失败:', dbError.message);
                }
            } else {
                console.warn('⚠️ database方法不可用');
            }

            window.cloudInitialized = true;

            // 尝试匿名登录以获取访问权限
            console.log('🔐 尝试匿名登录获取访问权限...');
            try {
                const auth = window.tcbApp.auth();
                console.log('🔍 认证对象:', auth);

                // 检查当前登录状态
                const loginState = await auth.getLoginState();
                console.log('🔍 当前登录状态:', loginState);

                if (!loginState) {
                    console.log('👤 执行匿名登录...');
                    await auth.anonymousAuthProvider().signIn();
                    console.log('✅ 匿名登录成功');

                    // 重新检查登录状态
                    const newLoginState = await auth.getLoginState();
                    console.log('✅ 新的登录状态:', newLoginState);
                } else {
                    console.log('✅ 已经登录，用户信息:', loginState);
                }
            } catch (authError) {
                console.warn('⚠️ 身份认证失败，尝试继续运行:', authError);
                // 不抛出错误，允许继续运行
            }

        } catch (initError) {
            console.error('❌ SDK初始化过程中出错:', initError);
            window.cloudInitialized = false;
            throw initError;
        }

        // 尝试直接初始化一些测试数据
        try {
            await initTestDataDirectly();
        } catch (error) {
            console.warn('⚠️ 测试数据初始化失败:', error.message);
            // 如果是权限问题，提供解决建议
            if (error.message.includes('PERMISSION_DENIED')) {
                console.log('💡 权限被拒绝的解决方案:');
                console.log('1. 检查云开发控制台的数据库安全规则');
                console.log('2. 确保环境ID正确');
                console.log('3. 检查云函数的权限配置');
            }
        }

    } catch (error) {
        console.error('❌ Web SDK初始化失败:', error);
        window.cloudInitialized = false;
        throw error;
    }
}

// 检查SDK状态
function checkSDKStatus() {
    const hasCloudbase = typeof window.cloudbase !== 'undefined';
    const hasTcb = typeof window.tcb !== 'undefined';

    console.log('📊 SDK状态检查:');
    console.log('  - cloudbase:', hasCloudbase ? '✅' : '❌');
    console.log('  - tcb:', hasTcb ? '✅' : '❌');

    return hasCloudbase || hasTcb;
}

// 绑定事件
function bindEvents() {
    // 模态框关闭事件
    document.getElementById('modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// 切换标签页
function switchTab(tabName) {
    // 更新导航状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 更新内容面板
    document.querySelectorAll('.content-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');
    
    currentTab = tabName;
    
    // 加载对应数据
    switch(tabName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'categories':
            loadCategories();
            break;
        case 'emojis':
            loadEmojis();
            break;
        case 'banners':
            loadBanners();
            break;
        case 'users':
            loadUsers();
            break;
    }
}

// 调用云函数 - 使用SDK方式并传递管理员密码
async function callCloudFunction(functionName, data) {
    console.log(`📡 调用云函数 (SDK方式): ${functionName}`, data);

    try {
        // 检查SDK初始化状态
        if (!window.cloudInitialized) {
            console.log('⚠️ SDK未初始化，尝试重新初始化...');
            await initCloud();
        }

        if (!window.tcbApp) {
            throw new Error('云开发SDK未初始化');
        }

        // 跳过身份认证 - 直接使用管理员密码调用云函数
        console.log('🔐 跳过匿名登录，使用管理员密码直接调用...');

        // 不进行匿名登录，直接调用云函数
        // 云函数内部会验证管理员密码

        // 直接调用云函数 - 不依赖身份认证
        console.log('☁️ 直接调用云函数 (管理员模式)');

        // 为Web端管理后台添加管理员密码
        const requestData = {
            ...data,
            adminPassword: 'admin123456', // 管理员密码
            webAdmin: true // 标识为Web管理端调用
        };

        console.log('📤 请求数据:', requestData);

        try {
            // 使用callFunction方法
            const response = await window.tcbApp.callFunction({
                name: functionName,
                data: requestData
            });

            console.log(`✅ 云函数调用成功: ${functionName}`, response);

            // 处理响应数据
            if (response && response.result) {
                return response.result;
            } else if (response) {
                return response;
            } else {
                throw new Error('云函数返回空响应');
            }
        } catch (callError) {
            console.error('❌ 云函数调用失败:', callError);
            throw new Error(`云函数调用失败: ${callError.message || callError}`);
        }

    } catch (error) {
        console.error(`❌ 云函数调用失败: ${functionName}`, error);
        throw error;
    }
}



// 直接访问数据库（当云函数不可用时）
async function callDatabaseDirectly(functionName, data) {
    console.log(`🗄️ 直接访问数据库: ${functionName}`, data);

    if (!window.tcbApp) {
        throw new Error('云开发SDK未初始化');
    }

    // 检查身份认证状态
    console.log('🔐 数据库访问前检查身份认证状态...');
    const auth = window.tcbApp.auth();
    const loginState = await auth.getLoginState();
    console.log('🔍 数据库访问时的登录状态:', loginState);

    if (!loginState) {
        console.log('👤 数据库访问需要身份认证，尝试匿名登录...');
        try {
            await auth.anonymousAuthProvider().signIn();
            console.log('✅ 数据库访问前匿名登录成功');
        } catch (authError) {
            console.warn('⚠️ 数据库访问前匿名登录失败:', authError);
        }
    }

    let db;
    try {
        if (typeof window.tcbApp.database !== 'function') {
            throw new Error('database方法不存在');
        }
        db = window.tcbApp.database();
        if (!db) {
            throw new Error('数据库对象为空');
        }
    } catch (error) {
        console.error('❌ 数据库访问失败:', error);
        return { success: false, error: '数据库访问失败: ' + error.message };
    }

    const { action } = data;

    try {
        switch (action) {
            case 'getStats':
                return await getStatsFromDB(db);
            case 'getCategoryList':
                return await getCategoriesFromDB(db);
            case 'getEmojiList':
                return await getEmojisFromDB(db);
            default:
                throw new Error(`不支持的数据库操作: ${action}`);
        }
    } catch (error) {
        console.error('❌ 数据库直接访问失败:', error);
        throw error;
    }
}

// 从数据库获取统计数据
async function getStatsFromDB(db) {
    console.log('📊 从数据库获取统计数据...');

    try {
        const [categoriesResult, emojisResult] = await Promise.all([
            db.collection('categories').count(),
            db.collection('emojis').count()
        ]);

        const stats = {
            totalCategories: categoriesResult.total || 0,
            totalEmojis: emojisResult.total || 0,
            totalDownloads: 0, // 需要从下载记录计算
            todayDownloads: 0
        };

        console.log('✅ 统计数据获取成功:', stats);
        return { success: true, data: stats };
    } catch (error) {
        console.error('❌ 获取统计数据失败:', error);
        return { success: false, error: error.message };
    }
}

// 从数据库获取分类列表
async function getCategoriesFromDB(db) {
    console.log('📂 从数据库获取分类列表...');

    try {
        const result = await db.collection('categories')
            .orderBy('sort', 'asc')
            .orderBy('created_at', 'desc')
            .get();

        console.log('✅ 分类数据获取成功:', result.data.length, '条记录');
        return { success: true, data: result.data };
    } catch (error) {
        console.error('❌ 获取分类数据失败:', error);
        return { success: false, error: error.message };
    }
}

// 从数据库获取表情包列表
async function getEmojisFromDB(db) {
    console.log('😀 从数据库获取表情包列表...');

    try {
        const result = await db.collection('emojis')
            .orderBy('created_at', 'desc')
            .limit(50)
            .get();

        console.log('✅ 表情包数据获取成功:', result.data.length, '条记录');
        return { success: true, data: result.data };
    } catch (error) {
        console.error('❌ 获取表情包数据失败:', error);
        return { success: false, error: error.message };
    }
}





// 管理员身份验证 - 简化版本
async function ensureAdminAuth() {
    if (!window.tcbApp) {
        throw new Error('云开发SDK未初始化');
    }

    console.log('🔐 检查身份验证状态...');

    try {
        const auth = window.tcbApp.auth();
        let user = auth.currentUser;

        if (!user) {
            console.log('👤 当前无用户，尝试匿名登录...');
            await adminLogin();
            user = auth.currentUser;
        }

        if (user && user.uid) {
            console.log('✅ 身份验证成功:', user.uid);
            return user;
        } else {
            console.log('⚠️ 跳过身份验证（开发模式）');
            return { uid: 'dev_user', isAnonymous: true };
        }

    } catch (error) {
        console.warn('⚠️ 身份验证失败，但继续执行（开发模式）:', error.message);
        // 在开发阶段，即使身份验证失败也继续执行
        return { uid: 'dev_user_fallback', isAnonymous: true };
    }
}

// 管理员登录 - 简化版本
async function adminLogin() {
    try {
        console.log('🔑 尝试匿名登录...');

        const auth = window.tcbApp.auth();
        const result = await auth.signInAnonymously();

        console.log('✅ 匿名登录成功');
        return result;

    } catch (error) {
        console.warn('⚠️ 匿名登录失败:', error.message);

        // 检查是否是ACCESS_TOKEN_DISABLED错误
        if (error.message && error.message.includes('ACCESS_TOKEN_DISABLED')) {
            console.log('💡 提示：需要在云开发控制台启用匿名登录');
            console.log('💡 路径：云开发控制台 -> 环境 -> 登录授权 -> 匿名登录');
        }

        // 不抛出错误，允许在无身份验证的情况下继续
        console.log('⚠️ 继续执行（无身份验证模式）');
    }
}

// 测试数据创建已禁用 - 防止创建虚拟数据
async function initTestDataDirectly() {
    console.log('⚠️ 测试数据创建已禁用，请手动添加数据');
    return;

    console.log('🔍 检查数据库访问权限...');

    let db;
    try {
        if (typeof window.tcbApp.database !== 'function') {
            console.warn('⚠️ database方法不存在，跳过数据库初始化');
            return;
        }
        db = window.tcbApp.database();
        if (!db) {
            console.warn('⚠️ 无法获取数据库对象，跳过初始化');
            return;
        }
        console.log('✅ 数据库对象获取成功');
    } catch (error) {
        console.warn('⚠️ 数据库访问失败:', error.message);
        return;
    }

    try {
        // 检查是否已有数据
        console.log('🔍 检查现有数据...');
        const categoriesResult = await db.collection('categories').limit(1).get();
        if (categoriesResult.data && categoriesResult.data.length > 0) {
            console.log('✅ 数据库已有数据，跳过初始化');
            return;
        }

        console.log('📝 开始初始化基础数据...');

        // 初始化分类数据
        const categories = [
            { name: '搞笑', description: '搞笑表情包', sort: 1, isActive: true },
            { name: '可爱', description: '可爱表情包', sort: 2, isActive: true },
            { name: '日常', description: '日常表情包', sort: 3, isActive: true }
        ];

        for (const category of categories) {
            try {
                await db.collection('categories').add({
                    data: {
                        ...category,
                        createTime: new Date(),
                        updateTime: new Date()
                    }
                });
                console.log(`✅ 添加分类: ${category.name}`);
            } catch (addError) {
                console.warn(`⚠️ 添加分类失败 ${category.name}:`, addError.message);
            }
        }

        console.log('✅ 基础数据初始化完成');

    } catch (error) {
        console.warn('⚠️ 直接数据库初始化失败:', error.message);
        console.warn('💡 这可能是因为数据库权限设置问题');
        // 不抛出错误，允许应用继续运行
    }
}

// 测试数据创建已禁用 - 防止创建虚拟数据
async function initTestDataViaDatabase() {
    console.log('⚠️ 测试数据创建已禁用，请手动添加数据');
    return {
        success: false,
        message: '测试数据创建已禁用，请手动添加数据'
    };
}

// 所有操作都通过真实云函数处理，确保与小程序数据同步

// 所有数据都来自真实的云开发数据库，不使用任何模拟数据

// 加载仪表盘
async function loadDashboard() {
    try {
        showLoading('statsGrid');

        // 先尝试调用云函数
        try {
            const result = await callCloudFunction('adminAPI', { action: 'getStats' });

            if (result.success) {
                renderStats(result.data);
                hideLoading('statsGrid');
                return;
            }
        } catch (cloudError) {
            console.warn('云函数调用失败，尝试直接数据库访问:', cloudError);
        }

        // 如果云函数失败，尝试直接从数据库获取数据
        try {
            const realStats = await getStatsFromDatabase();
            renderStats(realStats);
            hideLoading('statsGrid');
            showNotification('✅ 已连接真实数据库', 'success');
            return;
        } catch (dbError) {
            console.warn('数据库直接访问失败:', dbError);
        }

        // 不使用模拟数据，显示错误信息
        hideLoading('statsGrid');
        showError('无法连接到数据库，请检查云开发配置');

        // 显示详细的提示信息
        showNotification('⚠️ 无法连接数据库。可能原因：1) 数据库权限未配置 2) 云函数未部署', 'error');

    } catch (error) {
        hideLoading('statsGrid');
        showError('加载统计数据失败: ' + error.message);
    }
}

// 直接从数据库获取统计数据
async function getStatsFromDatabase() {
    console.log('📊 尝试从数据库获取统计数据...');

    if (!window.tcbApp) {
        throw new Error('SDK未初始化');
    }

    console.log('🔍 检查tcbApp对象:', window.tcbApp);
    console.log('🔍 检查可用方法:', Object.keys(window.tcbApp));

    // 优先使用云函数方式获取数据（更稳定）
    try {
        console.log('☁️ 优先通过云函数获取统计数据...');

        // 检查callFunction方法是否可用
        if (typeof window.tcbApp.callFunction !== 'function') {
            throw new Error('callFunction方法不可用');
        }

        const cloudResult = await window.tcbApp.callFunction({
            name: 'adminAPI',
            data: { action: 'getStats' }
        });

        console.log('☁️ 云函数原始返回:', cloudResult);

        if (cloudResult.result && cloudResult.result.success) {
            console.log('✅ 云函数返回统计数据:', cloudResult.result.data);
            return cloudResult.result.data;
        } else {
            console.warn('⚠️ 云函数返回错误，尝试直接数据库访问');
            console.warn('错误详情:', cloudResult.result?.error || '未知错误');
        }
    } catch (cloudError) {
        console.warn('⚠️ 云函数调用失败，尝试直接数据库访问:', cloudError);
        console.warn('错误堆栈:', cloudError.stack);
    }

    // 备用方案：尝试直接数据库访问
    let db;
    try {
        // 检查database方法是否存在
        if (typeof window.tcbApp.database !== 'function') {
            throw new Error('database方法不存在，SDK版本可能不兼容');
        }

        db = window.tcbApp.database();
        console.log('🗄️ 数据库对象:', db);

        if (!db) {
            throw new Error('数据库对象为空');
        }
    } catch (error) {
        console.error('❌ 获取数据库对象失败:', error);
        // 返回默认数据而不是抛出错误
        console.warn('⚠️ 使用默认统计数据');
        return {
            categories: 0,
            emojis: 0,
            users: 0
        };
    }

    try {
        console.log('📝 尝试查询categories集合...');

        // 获取分类数量
        let categoriesCount = 0;
        try {
            const categoriesResult = await db.collection('categories').count();
            categoriesCount = categoriesResult.total || 0;
            console.log('✅ 分类数量:', categoriesCount);
        } catch (error) {
            console.warn('⚠️ 查询分类失败:', error.message);
            categoriesCount = 3; // 默认值
        }

        // 获取表情包数量
        let emojisCount = 0;
        try {
            const emojisResult = await db.collection('emojis').count();
            emojisCount = emojisResult.total || 0;
            console.log('✅ 表情包数量:', emojisCount);
        } catch (error) {
            console.warn('⚠️ 查询表情包失败:', error.message);
            emojisCount = 0; // 默认值
        }

        // 获取用户数量（从真实数据库）
        let usersCount = 0;
        try {
            const usersResult = await db.collection('users').count();
            usersCount = usersResult.total || 0;
            console.log('👥 用户数量:', usersCount);
        } catch (error) {
            console.warn('⚠️ 查询用户数量失败:', error.message);
            usersCount = 0; // 默认值
        }

        const stats = {
            categories: categoriesCount,
            emojis: emojisCount,
            users: usersCount
        };

        console.log('📊 最终统计数据:', stats);
        return stats;

    } catch (error) {
        console.error('❌ 数据库统计查询失败:', error);
        throw error;
    }
}

// 渲染统计数据
function renderStats(stats) {
    const statsGrid = document.getElementById('statsGrid');
    
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${stats.categories}</div>
            <div class="stat-label">分类总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.emojis}</div>
            <div class="stat-label">表情包总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.banners}</div>
            <div class="stat-label">横幅总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.users}</div>
            <div class="stat-label">用户总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.totalLikes}</div>
            <div class="stat-label">总点赞数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${stats.totalDownloads}</div>
            <div class="stat-label">总下载数</div>
        </div>
    `;
}

// 加载分类列表
async function loadCategories() {
    try {
        showLoading('categoriesContent');
        
        const result = await callCloudFunction('adminAPI', { action: 'getCategoryList' });
        
        if (result.success) {
            currentData.categories = result.data;
            renderCategories(result.data);
        } else {
            showError('加载分类数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载分类数据失败: ' + error.message);
    }
}

// 渲染分类列表
function renderCategories(categories) {
    const content = document.getElementById('categoriesContent');
    
    if (categories.length === 0) {
        content.innerHTML = '<div class="loading">暂无分类数据</div>';
        return;
    }
    
    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>图标</th>
                    <th>名称</th>
                    <th>状态</th>
                    <th>表情包数量</th>
                    <th>排序</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${categories.map(category => `
                    <tr>
                        <td>${category.icon}</td>
                        <td>${category.name}</td>
                        <td><span class="status-badge status-${category.status}">${category.status === 'show' ? '显示' : '隐藏'}</span></td>
                        <td>${category.count || 0}</td>
                        <td>${category.sort || 0}</td>
                        <td>${formatDate(category.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editCategory('${category._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteCategory('${category._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    content.innerHTML = tableHTML;
}

// 加载表情包列表
async function loadEmojis() {
    try {
        showLoading('emojisContent');
        
        const result = await callCloudFunction('adminAPI', { action: 'getEmojiList' });
        
        if (result.success) {
            renderEmojis(result.data);
        } else {
            showError('加载表情包数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载表情包数据失败: ' + error.message);
    }
}

// 渲染表情包列表
function renderEmojis(emojis) {
    const content = document.getElementById('emojisContent');
    
    if (emojis.length === 0) {
        content.innerHTML = '<div class="loading">暂无表情包数据</div>';
        return;
    }
    
    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>标题</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>点赞数</th>
                    <th>下载数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${emojis.map(emoji => `
                    <tr>
                        <td>${emoji.title}</td>
                        <td>${emoji.category}</td>
                        <td><span class="status-badge status-${emoji.status}">${emoji.status === 'published' ? '已发布' : '草稿'}</span></td>
                        <td>${emoji.likes || 0}</td>
                        <td>${emoji.downloads || 0}</td>
                        <td>${formatDate(emoji.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editEmoji('${emoji._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteEmoji('${emoji._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    content.innerHTML = tableHTML;
}

// 工具函数
function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="loading">正在加载...</div>';
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element && element.innerHTML.includes('loading')) {
        element.innerHTML = '';
    }
}

function showError(message) {
    console.error('❌', message);
    showNotification(message, 'error');
}

function showSuccess(message) {
    console.log('✅', message);
    showNotification(message, 'success');
}

function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

function formatDate(date) {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
}

// 模态框操作
function showModal(content) {
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('modal').classList.add('active');
}

function closeModal() {
    document.getElementById('modal').classList.remove('active');
}

// 刷新统计数据
function refreshStats() {
    loadDashboard();
}

// 测试数据初始化已禁用 - 防止创建虚拟数据
async function initTestData() {
    showNotification('测试数据创建已禁用，请手动添加数据', 'warning');
    console.log('⚠️ 测试数据创建已禁用，请使用管理后台手动添加数据');
}

// 分类管理函数
function showAddCategoryModal() {
    const content = `
        <h3>添加分类</h3>
        <form onsubmit="addCategory(event)">
            <div class="form-group">
                <label class="form-label">分类名称</label>
                <input type="text" class="form-input" name="name" required>
            </div>
            <div class="form-group">
                <label class="form-label">图标</label>
                <input type="text" class="form-input" name="icon" placeholder="输入emoji图标" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">排序</label>
                <input type="number" class="form-input" name="sort" value="0">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

async function addCategory(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        icon: formData.get('icon'),
        description: formData.get('description'),
        sort: parseInt(formData.get('sort')) || 0
    };
    
    try {
        const result = await callCloudFunction('adminAPI', { action: 'createCategory', data });
        if (result.success) {
            showSuccess('分类添加成功');
            closeModal();
            loadCategories();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 删除分类
async function deleteCategory(id) {
    if (confirm('确定要删除这个分类吗？')) {
        try {
            const result = await callCloudFunction('adminAPI', { action: 'deleteCategory', data: { id } });
            if (result.success) {
                showSuccess('分类删除成功');
                loadCategories();
            } else {
                showError('删除失败: ' + result.error);
            }
        } catch (error) {
            showError('删除失败: ' + error.message);
        }
    }
}

// 编辑分类
function editCategory(id) {
    // 找到要编辑的分类数据
    const category = currentData.categories?.find(c => c._id === id);
    if (!category) {
        showError('找不到分类数据');
        return;
    }

    const content = `
        <h3>编辑分类</h3>
        <form onsubmit="updateCategory(event, '${id}')">
            <div class="form-group">
                <label class="form-label">分类名称</label>
                <input type="text" class="form-input" name="name" value="${category.name}" required>
            </div>
            <div class="form-group">
                <label class="form-label">图标</label>
                <input type="text" class="form-input" name="icon" value="${category.icon}" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description">${category.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label class="form-label">排序</label>
                <input type="number" class="form-input" name="sort" value="${category.sort || 0}">
            </div>
            <div class="form-group">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="show" ${category.status === 'show' ? 'selected' : ''}>显示</option>
                    <option value="hide" ${category.status === 'hide' ? 'selected' : ''}>隐藏</option>
                </select>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">更新</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 更新分类
async function updateCategory(event, id) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        id,
        name: formData.get('name'),
        icon: formData.get('icon'),
        description: formData.get('description'),
        sort: parseInt(formData.get('sort')) || 0,
        status: formData.get('status')
    };

    try {
        const result = await callCloudFunction('adminAPI', { action: 'updateCategory', data });
        if (result.success) {
            showSuccess('分类更新成功');
            closeModal();
            loadCategories();
        } else {
            showError('更新失败: ' + result.error);
        }
    } catch (error) {
        showError('更新失败: ' + error.message);
    }
}

// 表情包管理
function showAddEmojiModal() {
    const content = `
        <h3>添加表情包</h3>
        <form onsubmit="addEmoji(event)">
            <div class="form-group">
                <label class="form-label">标题</label>
                <input type="text" class="form-input" name="title" required>
            </div>
            <div class="form-group">
                <label class="form-label">分类</label>
                <select class="form-select" name="category" required>
                    <option value="">请选择分类</option>
                    <option value="情感表达">情感表达</option>
                    <option value="动物萌宠">动物萌宠</option>
                    <option value="搞笑幽默">搞笑幽默</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">图片URL</label>
                <input type="url" class="form-input" name="imageUrl" required>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-textarea" name="description"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">标签（用逗号分隔）</label>
                <input type="text" class="form-input" name="tags" placeholder="可爱,搞笑,表情">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 添加表情包
async function addEmoji(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const tags = formData.get('tags') ? formData.get('tags').split(',').map(tag => tag.trim()) : [];

    const data = {
        title: formData.get('title'),
        category: formData.get('category'),
        imageUrl: formData.get('imageUrl'),
        description: formData.get('description'),
        tags,
        status: 'published'
    };

    try {
        const result = await callCloudFunction('adminAPI', { action: 'addEmoji', data });
        if (result.success) {
            showSuccess('表情包添加成功');
            closeModal();
            loadEmojis();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 删除表情包
async function deleteEmoji(id) {
    if (confirm('确定要删除这个表情包吗？')) {
        try {
            const result = await callCloudFunction('adminAPI', { action: 'deleteEmoji', data: { id } });
            if (result.success) {
                showSuccess('表情包删除成功');
                loadEmojis();
            } else {
                showError('删除失败: ' + result.error);
            }
        } catch (error) {
            showError('删除失败: ' + error.message);
        }
    }
}

// 编辑表情包
function editEmoji(id) {
    console.log('编辑表情包:', id);
    // 这里可以实现编辑功能，类似编辑分类
}

// 横幅管理
function showAddBannerModal() {
    const content = `
        <h3>添加横幅</h3>
        <form onsubmit="addBanner(event)">
            <div class="form-group">
                <label class="form-label">标题</label>
                <input type="text" class="form-input" name="title" required>
            </div>
            <div class="form-group">
                <label class="form-label">图片URL</label>
                <input type="url" class="form-input" name="imageUrl" required>
            </div>
            <div class="form-group">
                <label class="form-label">链接</label>
                <input type="url" class="form-input" name="link">
            </div>
            <div class="form-group">
                <label class="form-label">优先级</label>
                <input type="number" class="form-input" name="priority" value="1">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal(content);
}

// 添加横幅
async function addBanner(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        title: formData.get('title'),
        imageUrl: formData.get('imageUrl'),
        link: formData.get('link'),
        priority: parseInt(formData.get('priority')) || 1,
        status: 'show'
    };

    try {
        const result = await callCloudFunction('adminAPI', { action: 'addBanner', data });
        if (result.success) {
            showSuccess('横幅添加成功');
            closeModal();
            loadBanners();
        } else {
            showError('添加失败: ' + result.error);
        }
    } catch (error) {
        showError('添加失败: ' + error.message);
    }
}

// 加载横幅列表
async function loadBanners() {
    try {
        showLoading('bannersContent');

        const result = await callCloudFunction('adminAPI', { action: 'getBannerList' });

        if (result.success) {
            renderBanners(result.data);
        } else {
            showError('加载横幅数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载横幅数据失败: ' + error.message);
    }
}

// 渲染横幅列表
function renderBanners(banners) {
    const content = document.getElementById('bannersContent');

    if (banners.length === 0) {
        content.innerHTML = '<div class="loading">暂无横幅数据</div>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>标题</th>
                    <th>图片</th>
                    <th>状态</th>
                    <th>优先级</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${banners.map(banner => `
                    <tr>
                        <td>${banner.title}</td>
                        <td><img src="${banner.imageUrl}" alt="${banner.title}" style="width: 60px; height: 30px; object-fit: cover; border-radius: 4px;"></td>
                        <td><span class="status-badge status-${banner.status}">${banner.status === 'show' ? '显示' : '隐藏'}</span></td>
                        <td>${banner.priority || 0}</td>
                        <td>${formatDate(banner.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editBanner('${banner._id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteBanner('${banner._id}')">删除</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    content.innerHTML = tableHTML;
}

// 加载用户列表
async function loadUsers() {
    try {
        showLoading('usersContent');

        const result = await callCloudFunction('adminAPI', { action: 'getUserList' });

        if (result.success) {
            renderUsers(result.data);
        } else {
            showError('加载用户数据失败: ' + result.error);
        }
    } catch (error) {
        showError('加载用户数据失败: ' + error.message);
    }
}

// 渲染用户列表
function renderUsers(users) {
    const content = document.getElementById('usersContent');

    if (users.length === 0) {
        content.innerHTML = '<div class="loading">暂无用户数据</div>';
        return;
    }

    const tableHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>头像</th>
                    <th>昵称</th>
                    <th>角色</th>
                    <th>状态</th>
                    <th>注册时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                ${users.map(user => `
                    <tr>
                        <td><img src="${user.profile?.avatar || '/images/default-avatar.png'}" alt="${user.profile?.nickname}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;"></td>
                        <td>${user.profile?.nickname || '未设置'}</td>
                        <td><span class="status-badge ${user.auth?.role === 'admin' ? 'status-show' : 'status-published'}">${user.auth?.role || 'user'}</span></td>
                        <td><span class="status-badge status-${user.auth?.status === 'active' ? 'published' : 'draft'}">${user.auth?.status === 'active' ? '正常' : '禁用'}</span></td>
                        <td>${formatDate(user.createTime)}</td>
                        <td>
                            <button class="btn" onclick="editUser('${user._id}')">编辑</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    content.innerHTML = tableHTML;
}

// 添加缺失的triggerAutoSync方法
function triggerAutoSync(dataType) {
    console.log(`🔄 触发自动同步: ${dataType}`);
    // 简化版本的自动同步，只记录日志
    // 实际的同步逻辑可以在后续版本中完善
}

// 占位函数
function editBanner(id) { console.log('编辑横幅:', id); }
function deleteBanner(id) { console.log('删除横幅:', id); }
function editUser(id) { console.log('编辑用户:', id); }
