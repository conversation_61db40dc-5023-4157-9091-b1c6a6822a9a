// 管理后台 JavaScript
class AdminPanel {
    constructor() {
        this.currentTab = 'emojis';
        this.data = {
            emojis: [],
            categories: [],
            stats: {}
        };
        this.init();
    }

    async init() {
        console.log('🔧 初始化管理后台');
        await this.loadAllData();
    }

    // 加载所有数据
    async loadAllData() {
        try {
            await Promise.all([
                this.loadStats(),
                this.loadEmojis(),
                this.loadCategories()
            ]);
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('数据加载失败: ' + error.message);
        }
    }

    // 加载统计数据
    async loadStats() {
        try {
            const response = await this.callCloudFunction('adminAPI', {
                action: 'getStats'
            });

            if (response.success) {
                this.data.stats = response.data;
                this.updateStatsDisplay();
            } else {
                // 使用默认统计数据
                this.data.stats = {
                    totalEmojis: this.data.emojis.length,
                    totalCategories: this.data.categories.length,
                    totalLikes: 8567,
                    totalDownloads: 25430
                };
                this.updateStatsDisplay();
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 加载表情包数据
    async loadEmojis() {
        const loadingEl = document.getElementById('emojis-loading');
        const listEl = document.getElementById('emojis-list');
        
        loadingEl.style.display = 'block';
        listEl.innerHTML = '';

        try {
            const response = await this.callCloudFunction('dataAPI', {
                action: 'getEmojis',
                data: { category: 'all', page: 1, limit: 100 }
            });

            if (response.success) {
                this.data.emojis = response.data;
                this.renderEmojisList();
            } else {
                this.showEmptyState('emojis-list', '📦', '暂无表情包数据');
            }
        } catch (error) {
            console.error('加载表情包失败:', error);
            this.showEmptyState('emojis-list', '❌', '加载失败: ' + error.message);
        } finally {
            loadingEl.style.display = 'none';
        }
    }

    // 加载分类数据
    async loadCategories() {
        const loadingEl = document.getElementById('categories-loading');
        const listEl = document.getElementById('categories-list');
        
        loadingEl.style.display = 'block';
        listEl.innerHTML = '';

        try {
            const response = await this.callCloudFunction('dataAPI', {
                action: 'getCategoryStats'
            });

            if (response.success) {
                this.data.categories = response.data;
                this.renderCategoriesList();
            } else {
                this.showEmptyState('categories-list', '📂', '暂无分类数据');
            }
        } catch (error) {
            console.error('加载分类失败:', error);
            this.showEmptyState('categories-list', '❌', '加载失败: ' + error.message);
        } finally {
            loadingEl.style.display = 'none';
        }
    }

    // 更新统计显示
    updateStatsDisplay() {
        document.getElementById('totalEmojis').textContent = this.data.stats.totalEmojis || 0;
        document.getElementById('totalCategories').textContent = this.data.stats.totalCategories || 0;
        document.getElementById('totalLikes').textContent = this.formatNumber(this.data.stats.totalLikes || 0);
        document.getElementById('totalDownloads').textContent = this.formatNumber(this.data.stats.totalDownloads || 0);
    }

    // 渲染表情包列表
    renderEmojisList() {
        const listEl = document.getElementById('emojis-list');
        
        if (this.data.emojis.length === 0) {
            this.showEmptyState('emojis-list', '📦', '暂无表情包数据');
            return;
        }

        const html = `
            <table class="table">
                <thead>
                    <tr>
                        <th>预览</th>
                        <th>标题</th>
                        <th>分类</th>
                        <th>点赞</th>
                        <th>收藏</th>
                        <th>下载</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.data.emojis.map(emoji => `
                        <tr>
                            <td>
                                <img src="${emoji.imageUrl}" alt="${emoji.title}" class="emoji-image" />
                            </td>
                            <td>
                                <strong>${emoji.title}</strong>
                                <br>
                                <small style="color: #666;">${emoji.author || '未知作者'}</small>
                            </td>
                            <td>${emoji.category}</td>
                            <td>${emoji.likes || 0}</td>
                            <td>${emoji.collections || 0}</td>
                            <td>${emoji.downloads || 0}</td>
                            <td>
                                <span class="status status-active">已发布</span>
                            </td>
                            <td>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" 
                                        onclick="admin.deleteEmoji('${emoji._id || emoji.id}', '${emoji.title}')">
                                    删除
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        listEl.innerHTML = html;
    }

    // 渲染分类列表
    renderCategoriesList() {
        const listEl = document.getElementById('categories-list');
        
        if (this.data.categories.length === 0) {
            this.showEmptyState('categories-list', '📂', '暂无分类数据');
            return;
        }

        const html = `
            <table class="table">
                <thead>
                    <tr>
                        <th>图标</th>
                        <th>名称</th>
                        <th>表情包数量</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.data.categories.map(category => `
                        <tr>
                            <td>
                                <div class="category-icon">${category.icon}</div>
                            </td>
                            <td>
                                <strong>${category.name}</strong>
                                <br>
                                <small style="color: #666;">ID: ${category.id}</small>
                            </td>
                            <td>${category.count || 0} 个</td>
                            <td>
                                <span class="status status-active">活跃</span>
                            </td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" 
                                        onclick="admin.editCategory('${category.id}')">
                                    编辑
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        listEl.innerHTML = html;
    }

    // 显示空状态
    showEmptyState(containerId, icon, message) {
        const container = document.getElementById(containerId);
        container.innerHTML = `
            <div class="empty">
                <div class="empty-icon">${icon}</div>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="admin.initTestData()">初始化测试数据</button>
            </div>
        `;
    }

    // 格式化数字
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    // 调用云函数
    async callCloudFunction(name, data) {
        try {
            // 这里需要根据实际的云函数调用方式来实现
            // 由于这是Web环境，需要通过HTTP API调用
            const response = await fetch(`/api/${name}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            return await response.json();
        } catch (error) {
            console.error('云函数调用失败:', error);
            throw error;
        }
    }

    // 显示错误信息
    showError(message) {
        alert('错误: ' + message);
    }

    // 显示成功信息
    showSuccess(message) {
        alert('成功: ' + message);
    }
}

// 全局函数
let admin;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    admin = new AdminPanel();
});

// 切换选项卡
function switchTab(tab) {
    // 更新选项卡样式
    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    event.target.classList.add('active');
    
    // 显示对应内容
    document.getElementById('emojis-content').style.display = tab === 'emojis' ? 'block' : 'none';
    document.getElementById('categories-content').style.display = tab === 'categories' ? 'block' : 'none';
    
    admin.currentTab = tab;
}

// 初始化测试数据
async function initTestData() {
    if (!confirm('确定要初始化测试数据吗？这将创建示例表情包和分类。')) {
        return;
    }
    
    try {
        const response = await admin.callCloudFunction('dataAPI', {
            action: 'initTestData'
        });
        
        if (response.success) {
            admin.showSuccess('测试数据初始化成功！');
            await admin.loadAllData();
        } else {
            admin.showError('初始化失败: ' + response.message);
        }
    } catch (error) {
        admin.showError('初始化失败: ' + error.message);
    }
}

// 刷新数据
async function refreshData() {
    await admin.loadAllData();
    admin.showSuccess('数据刷新完成！');
}

// 清空所有数据
async function clearAllData() {
    if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
        return;
    }
    
    try {
        const response = await admin.callCloudFunction('adminAPI', {
            action: 'clearAllData'
        });
        
        if (response.success) {
            admin.showSuccess('数据清空成功！');
            await admin.loadAllData();
        } else {
            admin.showError('清空失败: ' + response.message);
        }
    } catch (error) {
        admin.showError('清空失败: ' + error.message);
    }
}
