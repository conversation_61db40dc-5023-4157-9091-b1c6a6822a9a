// 调试小程序标签数据显示问题
console.log('🔍 开始调试标签数据显示问题...');

// 1. 检查当前页面数据
function checkCurrentPageData() {
    console.log('\n📊 检查当前页面数据:');
    
    const pages = getCurrentPages();
    if (pages.length === 0) {
        console.log('❌ 没有找到当前页面');
        return;
    }
    
    const currentPage = pages[pages.length - 1];
    console.log('📄 当前页面路由:', currentPage.route);
    
    const data = currentPage.data;
    console.log('📋 页面数据键:', Object.keys(data));
    
    // 检查表情包列表数据
    if (data.emojiList) {
        console.log(`📦 表情包列表长度: ${data.emojiList.length}`);
        
        if (data.emojiList.length > 0) {
            const firstEmoji = data.emojiList[0];
            console.log('\n🔍 第一个表情包数据:');
            console.log('完整数据:', JSON.stringify(firstEmoji, null, 2));
            
            // 重点检查标签数据
            console.log('\n🏷️ 标签数据检查:');
            console.log('tags字段存在:', 'tags' in firstEmoji);
            console.log('tags值:', firstEmoji.tags);
            console.log('tags类型:', typeof firstEmoji.tags);
            console.log('tags是数组:', Array.isArray(firstEmoji.tags));
            console.log('tags长度:', firstEmoji.tags ? firstEmoji.tags.length : 'N/A');
            
            // 检查条件渲染逻辑
            const hasTagsCondition = firstEmoji.tags && firstEmoji.tags.length > 0;
            console.log('wx:if条件满足:', hasTagsCondition);
        }
    }
    
    // 检查搜索结果数据
    if (data.searchResults) {
        console.log(`🔍 搜索结果长度: ${data.searchResults.length}`);
        
        if (data.searchResults.length > 0) {
            const firstResult = data.searchResults[0];
            console.log('\n🔍 第一个搜索结果的标签数据:');
            console.log('tags字段存在:', 'tags' in firstResult);
            console.log('tags值:', firstResult.tags);
            console.log('tags长度:', firstResult.tags ? firstResult.tags.length : 'N/A');
        }
    }
}

// 2. 检查云函数返回的原始数据
async function checkCloudFunctionData() {
    console.log('\n☁️ 检查云函数返回的原始数据:');
    
    try {
        const result = await wx.cloud.callFunction({
            name: 'dataAPI',
            data: {
                action: 'getEmojis',
                data: {
                    category: 'all',
                    page: 1,
                    limit: 3
                }
            }
        });
        
        console.log('☁️ 云函数调用成功');
        console.log('返回结果:', JSON.stringify(result.result, null, 2));
        
        if (result.result && result.result.success && result.result.data) {
            const emojis = result.result.data;
            console.log(`📦 获取到 ${emojis.length} 个表情包`);
            
            if (emojis.length > 0) {
                const firstEmoji = emojis[0];
                console.log('\n🔍 第一个表情包的标签数据:');
                console.log('tags字段存在:', 'tags' in firstEmoji);
                console.log('tags值:', firstEmoji.tags);
                console.log('tags类型:', typeof firstEmoji.tags);
                console.log('tags是数组:', Array.isArray(firstEmoji.tags));
                console.log('tags长度:', firstEmoji.tags ? firstEmoji.tags.length : 'N/A');
            }
        }
    } catch (error) {
        console.error('❌ 云函数调用失败:', error);
    }
}

// 3. 检查数据管理器中的数据
function checkDataManagerData() {
    console.log('\n📊 检查数据管理器中的数据:');
    
    try {
        // 检查是否有DataManager
        if (typeof DataManager !== 'undefined') {
            const allEmojis = DataManager.getAllEmojiData();
            console.log(`📦 DataManager中的表情包数量: ${allEmojis.length}`);
            
            if (allEmojis.length > 0) {
                const firstEmoji = allEmojis[0];
                console.log('\n🔍 DataManager中第一个表情包的标签数据:');
                console.log('tags字段存在:', 'tags' in firstEmoji);
                console.log('tags值:', firstEmoji.tags);
                console.log('tags长度:', firstEmoji.tags ? firstEmoji.tags.length : 'N/A');
            }
        } else {
            console.log('❌ DataManager未定义');
        }
    } catch (error) {
        console.error('❌ 检查DataManager失败:', error);
    }
}

// 4. 强制刷新页面数据
async function forceRefreshPageData() {
    console.log('\n🔄 强制刷新页面数据...');
    
    const pages = getCurrentPages();
    if (pages.length === 0) {
        console.log('❌ 没有找到当前页面');
        return;
    }
    
    const currentPage = pages[pages.length - 1];
    
    // 如果是首页，尝试重新加载数据
    if (currentPage.route === 'pages/index/index') {
        console.log('🏠 检测到首页，尝试重新加载数据...');
        
        try {
            // 调用云函数获取最新数据
            const result = await wx.cloud.callFunction({
                name: 'dataAPI',
                data: {
                    action: 'getEmojis',
                    data: {
                        category: 'all',
                        page: 1,
                        limit: 10
                    }
                }
            });
            
            if (result.result && result.result.success && result.result.data) {
                const emojis = result.result.data;
                console.log(`✅ 获取到最新数据: ${emojis.length} 个表情包`);
                
                // 更新页面数据
                currentPage.setData({
                    emojiList: emojis,
                    searchResults: []
                });
                
                console.log('✅ 页面数据已更新');
                
                // 再次检查数据
                setTimeout(() => {
                    checkCurrentPageData();
                }, 1000);
            }
        } catch (error) {
            console.error('❌ 刷新数据失败:', error);
        }
    }
}

// 5. 检查WXML模板渲染
function checkWXMLRendering() {
    console.log('\n🎨 检查WXML模板渲染:');
    
    // 这个函数需要在小程序环境中运行
    console.log('请在小程序开发者工具的控制台中运行此脚本');
    console.log('然后检查页面上是否有 .emoji-tags 元素');
}

// 主函数
async function debugTagsDisplay() {
    console.log('🚀 开始完整的标签显示调试...');
    
    // 1. 检查当前页面数据
    checkCurrentPageData();
    
    // 2. 检查云函数数据
    await checkCloudFunctionData();
    
    // 3. 检查数据管理器
    checkDataManagerData();
    
    // 4. 强制刷新数据
    await forceRefreshPageData();
    
    console.log('\n✅ 调试完成！');
    console.log('📋 请检查上面的输出，找出标签不显示的原因');
}

// 导出调试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        debugTagsDisplay,
        checkCurrentPageData,
        checkCloudFunctionData,
        checkDataManagerData,
        forceRefreshPageData
    };
}

// 如果在小程序环境中，自动运行调试
if (typeof wx !== 'undefined') {
    console.log('🔍 检测到小程序环境，开始调试...');
    debugTagsDisplay();
}

// 使用说明
console.log(`
📖 使用说明:
1. 在小程序开发者工具的控制台中粘贴并运行此脚本
2. 或者在页面的 onLoad 中调用 debugTagsDisplay()
3. 检查输出的调试信息，找出标签不显示的原因

🔧 可能的解决方案:
1. 如果数据中没有 tags 字段，需要确保云函数返回标签数据
2. 如果 tags 是空数组，需要在管理后台为表情包添加标签
3. 如果数据正常但不显示，可能需要重新编译小程序
4. 检查 WXML 模板中的条件渲染逻辑
`);
