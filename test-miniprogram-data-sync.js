// 深度测试微信小程序数据同步功能
const { chromium } = require('playwright');

async function testMiniprogramDataSync() {
    console.log('📱 深度测试微信小程序数据同步功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('云函数') || text.includes('数据') || text.includes('同步') || text.includes('分类') || text.includes('表情包')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        console.log('\n📍 第一步：验证管理后台数据创建');
        
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        // 检查管理后台的数据
        const adminData = await page.evaluate(async () => {
            try {
                // 获取分类数据
                const categoriesResult = await CloudAPI.database.get('categories');
                
                // 获取表情包数据
                const emojisResult = await CloudAPI.database.get('emojis');
                
                // 获取横幅数据
                const bannersResult = await CloudAPI.database.get('banners');
                
                return {
                    success: true,
                    categories: {
                        success: categoriesResult.success,
                        count: categoriesResult.success ? categoriesResult.data.length : 0,
                        data: categoriesResult.success ? categoriesResult.data.map(cat => ({
                            _id: cat._id,
                            name: cat.name,
                            status: cat.status,
                            gradient: cat.gradient
                        })) : []
                    },
                    emojis: {
                        success: emojisResult.success,
                        count: emojisResult.success ? emojisResult.data.length : 0,
                        data: emojisResult.success ? emojisResult.data.map(emoji => ({
                            _id: emoji._id,
                            title: emoji.title,
                            status: emoji.status,
                            categoryId: emoji.categoryId,
                            imageUrl: emoji.imageUrl ? emoji.imageUrl.substring(0, 50) + '...' : 'N/A'
                        })) : []
                    },
                    banners: {
                        success: bannersResult.success,
                        count: bannersResult.success ? bannersResult.data.length : 0,
                        data: bannersResult.success ? bannersResult.data.map(banner => ({
                            _id: banner._id,
                            title: banner.title,
                            status: banner.status,
                            imageUrl: banner.imageUrl ? banner.imageUrl.substring(0, 50) + '...' : 'N/A'
                        })) : []
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('\n📊 管理后台数据检查结果:');
        if (adminData.success) {
            console.log(`分类数据: ${adminData.categories.success ? '✅ 成功' : '🔴 失败'} (${adminData.categories.count}条)`);
            console.log(`表情包数据: ${adminData.emojis.success ? '✅ 成功' : '🔴 失败'} (${adminData.emojis.count}条)`);
            console.log(`横幅数据: ${adminData.banners.success ? '✅ 成功' : '🔴 失败'} (${adminData.banners.count}条)`);
            
            // 显示详细数据
            if (adminData.categories.count > 0) {
                console.log('\n📋 分类数据详情:');
                adminData.categories.data.forEach((cat, index) => {
                    console.log(`  ${index + 1}. ${cat.name} (状态: ${cat.status}, 有渐变: ${!!cat.gradient})`);
                });
            }
            
            if (adminData.emojis.count > 0) {
                console.log('\n📋 表情包数据详情:');
                adminData.emojis.data.forEach((emoji, index) => {
                    console.log(`  ${index + 1}. ${emoji.title} (状态: ${emoji.status}, 分类: ${emoji.categoryId})`);
                });
            }
            
            if (adminData.banners.count > 0) {
                console.log('\n📋 横幅数据详情:');
                adminData.banners.data.forEach((banner, index) => {
                    console.log(`  ${index + 1}. ${banner.title} (状态: ${banner.status})`);
                });
            }
        } else {
            console.log(`❌ 管理后台数据获取失败: ${adminData.error}`);
        }
        
        console.log('\n📍 第二步：模拟小程序云函数调用');
        
        // 模拟小程序调用云函数获取数据
        const miniprogramData = await page.evaluate(async () => {
            try {
                // 模拟小程序调用dataAPI云函数获取分类数据
                const categoriesResult = await new Promise((resolve, reject) => {
                    // 使用管理后台的云开发环境模拟小程序调用
                    const tcbApp = window.tcbApp;
                    if (!tcbApp) {
                        reject(new Error('云开发环境未初始化'));
                        return;
                    }
                    
                    // 模拟云函数调用
                    tcbApp.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getCategories' }
                    }).then(result => {
                        resolve(result);
                    }).catch(error => {
                        reject(error);
                    });
                });
                
                // 模拟小程序调用dataAPI云函数获取表情包数据
                const emojisResult = await new Promise((resolve, reject) => {
                    const tcbApp = window.tcbApp;
                    tcbApp.callFunction({
                        name: 'dataAPI',
                        data: { 
                            action: 'getEmojis',
                            data: { category: 'all', page: 1, limit: 10 }
                        }
                    }).then(result => {
                        resolve(result);
                    }).catch(error => {
                        reject(error);
                    });
                });
                
                // 模拟小程序调用getBanners云函数
                const bannersResult = await new Promise((resolve, reject) => {
                    const tcbApp = window.tcbApp;
                    tcbApp.callFunction({
                        name: 'dataAPI',
                        data: { action: 'getBanners' }
                    }).then(result => {
                        resolve(result);
                    }).catch(error => {
                        reject(error);
                    });
                });
                
                return {
                    success: true,
                    categories: categoriesResult,
                    emojis: emojisResult,
                    banners: bannersResult
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('\n📊 小程序云函数调用结果:');
        if (miniprogramData.success) {
            // 检查分类数据
            const categoriesSuccess = miniprogramData.categories && 
                miniprogramData.categories.result && 
                miniprogramData.categories.result.success;
            const categoriesCount = categoriesSuccess ? 
                miniprogramData.categories.result.data.length : 0;
            
            console.log(`分类云函数: ${categoriesSuccess ? '✅ 成功' : '🔴 失败'} (${categoriesCount}条)`);
            
            // 检查表情包数据
            const emojisSuccess = miniprogramData.emojis && 
                miniprogramData.emojis.result && 
                miniprogramData.emojis.result.success;
            const emojisCount = emojisSuccess ? 
                miniprogramData.emojis.result.data.length : 0;
            
            console.log(`表情包云函数: ${emojisSuccess ? '✅ 成功' : '🔴 失败'} (${emojisCount}条)`);
            
            // 检查横幅数据
            const bannersSuccess = miniprogramData.banners && 
                miniprogramData.banners.result && 
                miniprogramData.banners.result.success;
            const bannersCount = bannersSuccess ? 
                miniprogramData.banners.result.data.length : 0;
            
            console.log(`横幅云函数: ${bannersSuccess ? '✅ 成功' : '🔴 失败'} (${bannersCount}条)`);
            
            // 显示详细的云函数返回数据
            if (categoriesSuccess) {
                console.log('\n📋 小程序获取的分类数据:');
                miniprogramData.categories.result.data.forEach((cat, index) => {
                    console.log(`  ${index + 1}. ${cat.name} (表情包数量: ${cat.emojiCount || 0})`);
                });
            }
            
            if (emojisSuccess) {
                console.log('\n📋 小程序获取的表情包数据:');
                miniprogramData.emojis.result.data.forEach((emoji, index) => {
                    console.log(`  ${index + 1}. ${emoji.title} (分类: ${emoji.categoryId})`);
                });
            }
            
            if (bannersSuccess) {
                console.log('\n📋 小程序获取的横幅数据:');
                miniprogramData.banners.result.data.forEach((banner, index) => {
                    console.log(`  ${index + 1}. ${banner.title}`);
                });
            }
            
        } else {
            console.log(`❌ 小程序云函数调用失败: ${miniprogramData.error}`);
        }
        
        console.log('\n📍 第三步：数据一致性验证');
        
        // 对比管理后台数据和小程序获取的数据
        if (adminData.success && miniprogramData.success) {
            const categoriesConsistent = adminData.categories.count === 
                (miniprogramData.categories?.result?.data?.length || 0);
            const emojisConsistent = adminData.emojis.count === 
                (miniprogramData.emojis?.result?.data?.length || 0);
            const bannersConsistent = adminData.banners.count === 
                (miniprogramData.banners?.result?.data?.length || 0);
            
            console.log('📊 数据一致性检查:');
            console.log(`分类数据一致性: ${categoriesConsistent ? '✅ 一致' : '🔴 不一致'}`);
            console.log(`表情包数据一致性: ${emojisConsistent ? '✅ 一致' : '🔴 不一致'}`);
            console.log(`横幅数据一致性: ${bannersConsistent ? '✅ 一致' : '🔴 不一致'}`);
            
            if (!categoriesConsistent) {
                console.log(`  分类数量差异: 管理后台${adminData.categories.count}条 vs 小程序${miniprogramData.categories?.result?.data?.length || 0}条`);
            }
            
            if (!emojisConsistent) {
                console.log(`  表情包数量差异: 管理后台${adminData.emojis.count}条 vs 小程序${miniprogramData.emojis?.result?.data?.length || 0}条`);
            }
            
            if (!bannersConsistent) {
                console.log(`  横幅数量差异: 管理后台${adminData.banners.count}条 vs 小程序${miniprogramData.banners?.result?.data?.length || 0}条`);
            }
            
            // 总体评估
            const allConsistent = categoriesConsistent && emojisConsistent && bannersConsistent;
            
            console.log(`\n🎯 数据同步状态: ${allConsistent ? '🎉 完全同步' : '⚠️ 存在差异'}`);
            
            return {
                success: true,
                adminData: adminData,
                miniprogramData: miniprogramData,
                consistency: {
                    categories: categoriesConsistent,
                    emojis: emojisConsistent,
                    banners: bannersConsistent,
                    overall: allConsistent
                }
            };
        } else {
            console.log('❌ 无法进行数据一致性验证，因为数据获取失败');
            return {
                success: false,
                error: '数据获取失败'
            };
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'miniprogram-data-sync-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'miniprogram-data-sync-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: miniprogram-data-sync-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testMiniprogramDataSync().then(result => {
    console.log('\n🎯 微信小程序数据同步测试最终结果:', result);
    
    if (result.success && result.consistency) {
        if (result.consistency.overall) {
            console.log('🎉 数据同步测试完全通过！管理后台和小程序数据完全一致。');
        } else {
            console.log('⚠️ 数据同步测试部分通过，存在数据差异需要修复。');
        }
    } else {
        console.log('❌ 数据同步测试失败，需要检查云函数和数据库配置。');
    }
}).catch(console.error);
