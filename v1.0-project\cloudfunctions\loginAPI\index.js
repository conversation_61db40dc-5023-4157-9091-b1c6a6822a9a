// cloudfunctions/loginAPI/index.js
const cloud = require('wx-server-sdk');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// JWT密钥（生产环境应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'emoji-admin-v1.0-super-secret-key-2024';
const JWT_EXPIRES_IN = '24h'; // 令牌有效期24小时

exports.main = async (event, context) => {
  const { action, username, password, token } = event;
  const { OPENID, CLIENTIP, USERAGENT } = cloud.getWXContext();

  console.log(`🔐 LoginAPI调用: ${action}`, {
    username: username || 'N/A',
    ip: CLIENTIP,
    timestamp: new Date().toISOString()
  });

  try {
    switch (action) {
      case 'login':
        return await handleLogin(username, password, CLIENTIP, USERAGENT);
      case 'refreshToken':
        return await handleRefreshToken(token);
      case 'logout':
        return await handleLogout(token, CLIENTIP);
      case 'validateToken':
        return await handleValidateToken(token);
      default:
        return { 
          success: false, 
          error: '未知操作',
          code: 'UNKNOWN_ACTION'
        };
    }
  } catch (error) {
    console.error('❌ LoginAPI错误:', error);
    
    // 记录错误日志
    await logAdminActivity({
      action: 'error',
      error: error.message,
      ip: CLIENTIP,
      timestamp: new Date()
    });

    return { 
      success: false, 
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR'
    };
  }
};

// 处理登录
async function handleLogin(username, password, clientIP, userAgent) {
  try {
    console.log(`🔍 处理登录请求: ${username}`);

    // 验证输入参数
    if (!username || !password) {
      return {
        success: false,
        error: '用户名和密码不能为空',
        code: 'INVALID_INPUT'
      };
    }

    // 验证用户名密码
    const isValid = await validateCredentials(username, password);
    if (!isValid) {
      // 记录失败登录
      await logAdminActivity({
        adminId: username,
        action: 'login_failed',
        reason: 'invalid_credentials',
        ip: clientIP,
        userAgent: userAgent,
        timestamp: new Date()
      });

      return {
        success: false,
        error: '用户名或密码错误',
        code: 'INVALID_CREDENTIALS'
      };
    }

    // 生成JWT令牌
    const adminInfo = {
      adminId: username,
      loginTime: new Date().toISOString(),
      permissions: getAdminPermissions(username),
      ip: clientIP
    };

    const token = jwt.sign(adminInfo, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'emoji-admin-v1.0',
      subject: username,
      audience: 'admin-web'
    });

    // 记录成功登录
    await logAdminActivity({
      adminId: username,
      action: 'login_success',
      ip: clientIP,
      userAgent: userAgent,
      timestamp: new Date()
    });

    console.log(`✅ 登录成功: ${username}`);

    return {
      success: true,
      data: {
        token: token,
        adminInfo: {
          adminId: username,
          permissions: adminInfo.permissions,
          loginTime: adminInfo.loginTime
        },
        expiresIn: JWT_EXPIRES_IN
      },
      message: '登录成功'
    };

  } catch (error) {
    console.error('❌ 登录处理失败:', error);
    return {
      success: false,
      error: '登录失败，请重试',
      code: 'LOGIN_ERROR'
    };
  }
}

// 处理令牌刷新
async function handleRefreshToken(token) {
  try {
    if (!token) {
      return {
        success: false,
        error: '令牌不能为空',
        code: 'MISSING_TOKEN'
      };
    }

    // 验证现有令牌
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 生成新令牌
    const newAdminInfo = {
      adminId: decoded.adminId,
      loginTime: decoded.loginTime,
      refreshTime: new Date().toISOString(),
      permissions: decoded.permissions,
      ip: decoded.ip
    };

    const newToken = jwt.sign(newAdminInfo, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'emoji-admin-v1.0',
      subject: decoded.adminId,
      audience: 'admin-web'
    });

    // 记录令牌刷新
    await logAdminActivity({
      adminId: decoded.adminId,
      action: 'token_refresh',
      timestamp: new Date()
    });

    return {
      success: true,
      data: {
        token: newToken,
        expiresIn: JWT_EXPIRES_IN
      },
      message: '令牌刷新成功'
    };

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return {
        success: false,
        error: '令牌已过期，请重新登录',
        code: 'TOKEN_EXPIRED'
      };
    } else if (error.name === 'JsonWebTokenError') {
      return {
        success: false,
        error: '无效的令牌',
        code: 'INVALID_TOKEN'
      };
    } else {
      console.error('❌ 令牌刷新失败:', error);
      return {
        success: false,
        error: '令牌刷新失败',
        code: 'REFRESH_ERROR'
      };
    }
  }
}

// 处理登出
async function handleLogout(token, clientIP) {
  try {
    let adminId = 'unknown';
    
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        adminId = decoded.adminId;
      } catch (error) {
        // 令牌无效也允许登出
        console.warn('登出时令牌验证失败:', error.message);
      }
    }

    // 记录登出
    await logAdminActivity({
      adminId: adminId,
      action: 'logout',
      ip: clientIP,
      timestamp: new Date()
    });

    return {
      success: true,
      message: '登出成功'
    };

  } catch (error) {
    console.error('❌ 登出处理失败:', error);
    return {
      success: false,
      error: '登出失败',
      code: 'LOGOUT_ERROR'
    };
  }
}

// 处理令牌验证
async function handleValidateToken(token) {
  try {
    if (!token) {
      return {
        success: false,
        error: '令牌不能为空',
        code: 'MISSING_TOKEN'
      };
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    
    return {
      success: true,
      data: {
        adminId: decoded.adminId,
        permissions: decoded.permissions,
        loginTime: decoded.loginTime,
        isValid: true
      },
      message: '令牌有效'
    };

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return {
        success: false,
        error: '令牌已过期',
        code: 'TOKEN_EXPIRED'
      };
    } else {
      return {
        success: false,
        error: '无效的令牌',
        code: 'INVALID_TOKEN'
      };
    }
  }
}

// 验证用户凭据
async function validateCredentials(username, password) {
  try {
    // 管理员账户配置（生产环境应该从数据库获取）
    const adminAccounts = {
      'admin': {
        password: hashPassword('admin123456'),
        permissions: ['read', 'write', 'delete', 'admin']
      },
      'manager': {
        password: hashPassword('manager123456'),
        permissions: ['read', 'write']
      }
    };

    const account = adminAccounts[username];
    if (!account) {
      return false;
    }

    const hashedPassword = hashPassword(password);
    return account.password === hashedPassword;

  } catch (error) {
    console.error('❌ 凭据验证失败:', error);
    return false;
  }
}

// 获取管理员权限
function getAdminPermissions(username) {
  const permissionMap = {
    'admin': ['read', 'write', 'delete', 'admin'],
    'manager': ['read', 'write']
  };
  
  return permissionMap[username] || ['read'];
}

// 密码哈希
function hashPassword(password) {
  const salt = 'emoji-admin-salt-v1.0';
  return crypto.createHash('sha256').update(password + salt).digest('hex');
}

// 记录管理员活动日志
async function logAdminActivity(activity) {
  try {
    await db.collection('admin_logs').add({
      data: {
        ...activity,
        id: generateUniqueId(),
        timestamp: activity.timestamp || new Date()
      }
    });
  } catch (error) {
    console.warn('⚠️ 记录管理员日志失败:', error);
  }
}

// 生成唯一ID
function generateUniqueId() {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
