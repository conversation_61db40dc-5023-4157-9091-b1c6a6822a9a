@echo off
echo ========================================
echo Starting Emoji Admin Panel
echo ========================================
echo.
echo Choose admin panel type:
echo [1] Local Simulated Data (Demo)
echo [2] Cloud Database (Real Data)
echo.
set /p choice=Please enter your choice (1 or 2):

if "%choice%"=="1" (
    echo.
    echo Starting local admin panel...
    cd admin-unified

    echo Checking dependencies...
    if not exist node_modules (
        echo Installing dependencies...
        npm install
    )

    echo Starting server...
    echo.
    echo Admin panel will open in browser
    echo Access URL: http://localhost:8001
    echo Note: This uses simulated data
    echo.
    echo Press Ctrl+C to stop server
    echo ========================================

    start http://localhost:8001
    node fixed-server.js
) else if "%choice%"=="2" (
    echo.
    echo Starting cloud database admin panel...
    echo.
    echo Cloud admin panel access method:
    echo 1. Open project in WeChat Developer Tools
    echo 2. Deploy web-admin cloud function
    echo 3. Find web-admin function HTTP access URL in cloud console
    echo.
    echo Quick deployment command:
    echo    Right-click cloudfunctions/web-admin folder
    echo    Select "Upload and Deploy: Install Dependencies"
    echo.
) else (
    echo.
    echo Invalid choice, please run script again
)

pause
