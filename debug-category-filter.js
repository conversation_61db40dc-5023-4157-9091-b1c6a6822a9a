// 调试分类过滤问题
const { chromium } = require('playwright');

async function debugCategoryFilter() {
    console.log('🔍 调试分类过滤问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        await page.waitForTimeout(5000);
        
        console.log('📍 调试分类数据处理过程');
        
        const debugResult = await page.evaluate(async () => {
            try {
                // 直接获取原始数据
                const rawResult = await CloudAPI.database.get('categories');
                console.log('原始数据:', rawResult);
                
                if (!rawResult.success || !rawResult.data) {
                    return { error: '数据获取失败' };
                }
                
                const rawCategories = rawResult.data;
                console.log('原始分类数量:', rawCategories.length);
                
                // 逐步处理每个分类
                const processedCategories = [];
                const filterResults = [];
                
                for (let i = 0; i < rawCategories.length; i++) {
                    const category = rawCategories[i];
                    console.log(`\n处理分类 ${i + 1}:`, category);
                    
                    // 提取数据
                    const categoryData = category.data || category;
                    console.log('提取的数据:', categoryData);
                    
                    // 清理数据
                    const cleaned = {
                        _id: category._id || category.id,
                        name: categoryData.name,
                        icon: categoryData.icon || '📁',
                        status: categoryData.status || 'show',
                        sort: categoryData.sort || 0,
                        count: categoryData.count || 0,
                        description: categoryData.description || ''
                    };
                    
                    console.log('清理后的数据:', cleaned);
                    
                    // 检查过滤条件
                    const filterChecks = {
                        hasId: !!cleaned._id,
                        hasName: !!cleaned.name,
                        nameIsString: typeof cleaned.name === 'string',
                        nameNotEmpty: cleaned.name && cleaned.name.trim() !== '',
                        nameNotDefault: cleaned.name !== '未命名分类',
                        idNotTemp: !cleaned._id.startsWith('temp_')
                    };
                    
                    console.log('过滤检查:', filterChecks);
                    
                    const passesFilter = filterChecks.hasId && 
                                       filterChecks.hasName && 
                                       filterChecks.nameIsString && 
                                       filterChecks.nameNotEmpty && 
                                       filterChecks.nameNotDefault && 
                                       filterChecks.idNotTemp;
                    
                    console.log('通过过滤:', passesFilter);
                    
                    filterResults.push({
                        index: i,
                        original: category,
                        cleaned: cleaned,
                        filterChecks: filterChecks,
                        passesFilter: passesFilter
                    });
                    
                    if (passesFilter) {
                        processedCategories.push(cleaned);
                    }
                }
                
                return {
                    success: true,
                    rawCount: rawCategories.length,
                    processedCount: processedCategories.length,
                    filterResults: filterResults,
                    finalCategories: processedCategories
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    stack: error.stack
                };
            }
        });
        
        console.log('🔍 调试结果:');
        console.log('原始数量:', debugResult.rawCount);
        console.log('处理后数量:', debugResult.processedCount);
        
        if (debugResult.filterResults) {
            console.log('\n📋 详细过滤结果:');
            debugResult.filterResults.forEach((result, index) => {
                console.log(`\n分类 ${index + 1}:`);
                console.log('  原始名称:', result.original.data?.name || result.original.name);
                console.log('  清理后名称:', result.cleaned.name);
                console.log('  过滤检查:', result.filterChecks);
                console.log('  通过过滤:', result.passesFilter);
                
                if (!result.passesFilter) {
                    console.log('  ❌ 未通过过滤的原因:');
                    Object.entries(result.filterChecks).forEach(([key, value]) => {
                        if (!value) {
                            console.log(`    - ${key}: ${value}`);
                        }
                    });
                }
            });
        }
        
        if (debugResult.finalCategories && debugResult.finalCategories.length > 0) {
            console.log('\n✅ 最终分类数据:');
            debugResult.finalCategories.forEach((cat, index) => {
                console.log(`  ${index + 1}. ${cat.name} (${cat.icon}) - ${cat.status}`);
            });
        } else {
            console.log('\n❌ 没有分类通过过滤');
        }
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugCategoryFilter().catch(console.error);
