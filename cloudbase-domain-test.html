<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发域名测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .step { margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .domain-link { background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .copy-btn { background: #28a745; font-size: 12px; padding: 5px 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 云开发域名测试</h1>
        
        <div class="info">
            <h3>📋 发现的云开发域名</h3>
            <p>您的云开发环境有一个自定义域名，可以直接使用：</p>
            <div class="domain-link">
                <strong>域名：</strong> cloud1-5g6pvnpl88dc0142-1367610204.tcloudbaseapp.com
                <button class="copy-btn" onclick="copyDomain()">复制域名</button>
            </div>
            <p>这个域名已经配置了CORS，可以直接访问云开发服务。</p>
        </div>

        <div class="step">
            <h3>🧪 方案1: 使用云开发域名访问</h3>
            <p>将您的HTML文件上传到云开发静态网站托管，然后通过云开发域名访问：</p>
            
            <button onclick="generateUploadGuide()">生成上传指南</button>
            <div id="uploadGuideResult"></div>
        </div>

        <div class="step">
            <h3>🔧 方案2: 修改本地代码使用云开发域名</h3>
            <p>修改您的代码，直接通过云开发域名的API访问数据：</p>
            
            <button onclick="generateAPICode()">生成API调用代码</button>
            <div id="apiCodeResult"></div>
        </div>

        <div class="step">
            <h3>🚀 方案3: 本地开发环境配置</h3>
            <p>配置本地开发环境，使用代理访问云开发服务：</p>
            
            <button onclick="generateProxyConfig()">生成代理配置</button>
            <div id="proxyConfigResult"></div>
        </div>

        <div class="step">
            <h3>✅ 测试云开发连接</h3>
            <p>直接测试云开发服务是否可用：</p>
            
            <button onclick="testCloudbaseConnection()">测试连接</button>
            <div id="connectionTestResult"></div>
        </div>
    </div>

    <!-- 使用最新的云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        const CLOUDBASE_DOMAIN = 'cloud1-5g6pvnpl88dc0142-1367610204.tcloudbaseapp.com';
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';

        function copyDomain() {
            navigator.clipboard.writeText(CLOUDBASE_DOMAIN).then(() => {
                alert('域名已复制到剪贴板！');
            });
        }

        function generateUploadGuide() {
            const resultDiv = document.getElementById('uploadGuideResult');
            
            const guide = `
                <div class="success">
                    <h4>📤 静态网站托管上传指南</h4>
                    
                    <h5>步骤1: 开启静态网站托管</h5>
                    <ol>
                        <li>登录 <a href="https://console.cloud.tencent.com/tcb" target="_blank">腾讯云云开发控制台</a></li>
                        <li>选择环境：${ENV_ID}</li>
                        <li>进入 "静态网站托管" 功能</li>
                        <li>开启静态网站托管服务</li>
                    </ol>
                    
                    <h5>步骤2: 上传文件</h5>
                    <ol>
                        <li>将您的HTML文件（如 admin/index.html）上传到根目录</li>
                        <li>确保文件路径正确</li>
                        <li>等待部署完成</li>
                    </ol>
                    
                    <h5>步骤3: 访问网站</h5>
                    <p>访问地址：<strong>https://${CLOUDBASE_DOMAIN}</strong></p>
                    <p>管理后台：<strong>https://${CLOUDBASE_DOMAIN}/admin/index.html</strong></p>
                    
                    <div class="warning">
                        <p><strong>注意：</strong> 通过云开发域名访问时，不会有CORS问题，因为是同域访问。</p>
                    </div>
                </div>
            `;
            
            resultDiv.innerHTML = guide;
        }

        function generateAPICode() {
            const resultDiv = document.getElementById('apiCodeResult');
            
            const apiCode = `
                <div class="success">
                    <h4>🔌 API调用代码示例</h4>
                    
                    <h5>方法1: 直接HTTP请求（推荐）</h5>
                    <pre>
// 使用fetch直接调用云开发HTTP API
const API_BASE = 'https://${CLOUDBASE_DOMAIN}';

async function callCloudbaseAPI(action, data = {}) {
    try {
        const response = await fetch(\`\${API_BASE}/api/\${action}\`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API调用失败:', error);
        throw error;
    }
}

// 使用示例
async function getCategories() {
    const result = await callCloudbaseAPI('getCategories');
    console.log('分类数据:', result);
}

async function getEmojis(category = 'all', page = 1) {
    const result = await callCloudbaseAPI('getEmojis', {
        category: category,
        page: page,
        limit: 20
    });
    console.log('表情包数据:', result);
}
                    </pre>
                    
                    <h5>方法2: 使用云开发SDK（需要配置）</h5>
                    <pre>
// 配置云开发SDK使用自定义域名
const app = cloudbase.init({
    env: '${ENV_ID}',
    // 可以尝试配置自定义域名
    region: 'ap-shanghai'
});

// 正常使用SDK
async function testSDK() {
    try {
        await app.auth().signInAnonymously();
        const db = app.database();
        const result = await db.collection('categories').get();
        console.log('SDK调用成功:', result);
    } catch (error) {
        console.error('SDK调用失败:', error);
    }
}
                    </pre>
                </div>
            `;
            
            resultDiv.innerHTML = apiCode;
        }

        function generateProxyConfig() {
            const resultDiv = document.getElementById('proxyConfigResult');
            
            const proxyConfig = `
                <div class="success">
                    <h4>🔄 代理服务器配置</h4>
                    
                    <h5>创建代理服务器文件：proxy-cloudbase.js</h5>
                    <pre>
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 启用CORS
app.use(cors());

// 静态文件服务
app.use(express.static(__dirname));

// 代理云开发API请求
app.use('/cloudbase-api', createProxyMiddleware({
    target: 'https://${CLOUDBASE_DOMAIN}',
    changeOrigin: true,
    pathRewrite: {
        '^/cloudbase-api': ''
    },
    onProxyReq: (proxyReq, req, res) => {
        console.log('代理请求:', req.method, req.url);
    }
}));

app.listen(PORT, () => {
    console.log(\`代理服务器运行在 http://localhost:\${PORT}\`);
    console.log('云开发API代理地址: http://localhost:' + PORT + '/cloudbase-api');
});
                    </pre>
                    
                    <h5>安装依赖并启动：</h5>
                    <pre>
npm install express http-proxy-middleware cors
node proxy-cloudbase.js
                    </pre>
                    
                    <h5>在前端代码中使用代理：</h5>
                    <pre>
// 使用本地代理访问云开发
const API_BASE = 'http://localhost:3000/cloudbase-api';

async function callAPI(endpoint, data) {
    const response = await fetch(\`\${API_BASE}\${endpoint}\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });
    return response.json();
}
                    </pre>
                </div>
            `;
            
            resultDiv.innerHTML = proxyConfig;
        }

        async function testCloudbaseConnection() {
            const resultDiv = document.getElementById('connectionTestResult');
            resultDiv.innerHTML = '<p>正在测试云开发连接...</p>';

            try {
                // 方法1: 尝试直接SDK连接
                let sdkResult = null;
                try {
                    const app = cloudbase.init({ env: ENV_ID });
                    await app.auth().signInAnonymously();
                    const db = app.database();
                    const testQuery = await db.collection('categories').limit(1).get();
                    sdkResult = { success: true, data: testQuery.data };
                } catch (sdkError) {
                    sdkResult = { success: false, error: sdkError.message };
                }

                // 方法2: 尝试HTTP API调用
                let httpResult = null;
                try {
                    const response = await fetch(\`https://\${CLOUDBASE_DOMAIN}/api/test\`, {
                        method: 'GET',
                        mode: 'cors'
                    });
                    httpResult = { success: response.ok, status: response.status };
                } catch (httpError) {
                    httpResult = { success: false, error: httpError.message };
                }

                let html = \`
                    <div class="info">
                        <h4>🧪 连接测试结果</h4>
                        
                        <h5>SDK连接测试:</h5>
                        <p>状态: \${sdkResult.success ? '✅ 成功' : '❌ 失败'}</p>
                        \${sdkResult.success ? 
                            \`<p>数据条数: \${sdkResult.data.length}</p>\` : 
                            \`<p>错误: \${sdkResult.error}</p>\`
                        }
                        
                        <h5>HTTP API测试:</h5>
                        <p>状态: \${httpResult.success ? '✅ 成功' : '❌ 失败'}</p>
                        \${httpResult.success ? 
                            \`<p>HTTP状态: \${httpResult.status}</p>\` : 
                            \`<p>错误: \${httpResult.error}</p>\`
                        }
                        
                        <h5>建议:</h5>
                        \${sdkResult.success ? 
                            '<p style="color: green;">✅ SDK连接正常，可以直接使用云开发服务！</p>' : 
                            '<p style="color: orange;">⚠️ SDK连接失败，建议使用静态网站托管或代理方案</p>'
                        }
                    </div>
                \`;

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = \`
                    <div class="error">
                        <h4>❌ 测试失败</h4>
                        <p>错误: \${error.message}</p>
                        <p>建议使用静态网站托管方案</p>
                    </div>
                \`;
            }
        }

        // 页面加载时显示域名信息
        window.addEventListener('load', function() {
            console.log('云开发域名:', CLOUDBASE_DOMAIN);
            console.log('环境ID:', ENV_ID);
        });
    </script>
</body>
</html>
