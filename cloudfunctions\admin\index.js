// 云函数入口文件 - 后台管理系统API
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 验证管理员权限
    const adminCheck = await verifyAdmin(OPENID)
    if (!adminCheck.isAdmin) {
      return {
        success: false,
        error: '权限不足',
        code: 403
      }
    }

    // 路由处理
    switch (action) {
      case 'getOverview':
        return await getOverview()
      case 'getEmojiList':
        return await getEmojiList(data)
      case 'createEmoji':
        return await createEmoji(data, OPENID)
      case 'updateEmoji':
        return await updateEmoji(data, OPENID)
      case 'deleteEmoji':
        return await deleteEmoji(data, OPENID)
      case 'batchOperation':
        return await batchOperation(data, OPENID)
      case 'getCategoryList':
        return await getCategoryList()
      case 'createCategory':
        return await createCategory(data, OPENID)
      case 'updateCategory':
        return await updateCategory(data, OPENID)
      case 'deleteCategory':
        return await deleteCategory(data, OPENID)
      case 'getBannerList':
        return await getBannerList()
      case 'createBanner':
        return await createBanner(data, OPENID)
      case 'updateBanner':
        return await updateBanner(data, OPENID)
      case 'deleteBanner':
        return await deleteBanner(data, OPENID)
      case 'getUserList':
        return await getUserList(data)
      case 'updateUserStatus':
        return await updateUserStatus(data, OPENID)
      case 'getStatistics':
        return await getStatistics(data)
      case 'createCurrentUserAsAdmin':
        return await createCurrentUserAsAdmin(OPENID)
      case 'initTestData':
        return await initTestData(OPENID)
      case 'getStats':
        return await getOverview()
      default:
        return {
          success: false,
          error: '未知操作',
          code: 400
        }
    }
  } catch (error) {
    console.error('管理后台操作失败:', error)
    return {
      success: false,
      error: error.message,
      code: 500
    }
  }
}

// 验证管理员权限 - 适配正确的数据结构
async function verifyAdmin(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length > 0) {
      const user = userResult.data[0]
      // 检查用户的auth.role是否为admin，auth.status是否为active
      const isAdmin = user.auth && user.auth.role === 'admin' && user.auth.status === 'active'

      return {
        isAdmin: isAdmin,
        user: user
      }
    }

    return { isAdmin: false, user: null }
  } catch (error) {
    console.error('权限验证失败:', error)
    return { isAdmin: false, user: null }
  }
}

// 获取概览数据
async function getOverview() {
  try {
    const [emojiCount, categoryCount, userCount, downloadStats] = await Promise.all([
      db.collection('emojis').where({ status: 'published' }).count(),
      db.collection('categories').where({ status: 'active' }).count(),
      db.collection('users').count(),
      db.collection('user_actions').where({ action: 'download' }).count()
    ])

    // 获取今日数据
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const [todayUsers, todayDownloads, todayUploads] = await Promise.all([
      db.collection('users').where({
        createTime: _.gte(today)
      }).count(),
      db.collection('user_actions').where({
        action: 'download',
        createTime: _.gte(today)
      }).count(),
      db.collection('emojis').where({
        createTime: _.gte(today)
      }).count()
    ])

    return {
      success: true,
      data: {
        totalEmojis: emojiCount.total,
        totalCategories: categoryCount.total,
        totalUsers: userCount.total,
        totalDownloads: downloadStats.total,
        todayUsers: todayUsers.total,
        todayDownloads: todayDownloads.total,
        todayUploads: todayUploads.total
      }
    }
  } catch (error) {
    throw new Error('获取概览数据失败: ' + error.message)
  }
}

// 获取表情包列表
async function getEmojiList({ page = 1, limit = 20, keyword, category, status, sortBy = 'createTime', sortOrder = 'desc' }) {
  try {
    const skip = (page - 1) * limit
    let query = db.collection('emojis')
    
    // 构建查询条件
    const conditions = []
    
    if (keyword) {
      conditions.push(_.or([
        { title: new RegExp(keyword, 'i') },
        { tags: new RegExp(keyword, 'i') }
      ]))
    }
    
    if (category) {
      conditions.push({ category: category })
    }
    
    if (status) {
      conditions.push({ status: status })
    } else {
      conditions.push({ status: _.neq('deleted') })
    }
    
    if (conditions.length > 0) {
      query = query.where(_.and(conditions))
    }
    
    // 排序
    const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc'
    query = query.orderBy(sortBy, sortDirection)
    
    // 分页查询
    const [listResult, countResult] = await Promise.all([
      query.skip(skip).limit(limit).get(),
      query.count()
    ])
    
    // 获取分类信息
    const emojisWithCategory = await Promise.all(
      listResult.data.map(async (emoji) => {
        if (emoji.category) {
          const categoryResult = await db.collection('categories').doc(emoji.category).get()
          emoji.categoryInfo = categoryResult.data
        }
        return emoji
      })
    )

    return {
      success: true,
      data: emojisWithCategory,
      pagination: {
        page,
        limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }
  } catch (error) {
    throw new Error('获取表情包列表失败: ' + error.message)
  }
}

// 创建表情包
async function createEmoji(data, operatorId) {
  try {
    const { title, description, imageUrl, category, tags, status = 'published' } = data
    
    // 数据验证
    if (!title || !imageUrl || !category) {
      throw new Error('标题、图片和分类为必填项')
    }
    
    // 验证分类是否存在
    const categoryResult = await db.collection('categories').doc(category).get()
    if (!categoryResult.data) {
      throw new Error('指定的分类不存在')
    }
    
    const now = new Date()
    const emojiData = {
      title,
      description: description || '',
      imageUrl,
      category,
      tags: tags || [],
      status,
      likes: 0,
      collections: 0,
      views: 0,
      downloads: 0,
      createTime: now,
      updateTime: now,
      createdBy: operatorId
    }
    
    const result = await db.collection('emojis').add({
      data: emojiData
    })
    
    // 更新分类统计
    await updateCategoryStats(category)
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'create_emoji',
      target: result._id,
      details: { title }
    })
    
    return {
      success: true,
      data: { id: result._id, ...emojiData }
    }
  } catch (error) {
    throw new Error('创建表情包失败: ' + error.message)
  }
}

// 更新表情包
async function updateEmoji(data, operatorId) {
  try {
    const { id, ...updateData } = data
    
    if (!id) {
      throw new Error('表情包ID不能为空')
    }
    
    // 获取原数据
    const originalResult = await db.collection('emojis').doc(id).get()
    if (!originalResult.data) {
      throw new Error('表情包不存在')
    }
    
    const originalData = originalResult.data
    updateData.updateTime = new Date()
    
    await db.collection('emojis').doc(id).update({
      data: updateData
    })
    
    // 如果分类发生变化，更新相关统计
    if (updateData.category && updateData.category !== originalData.category) {
      await Promise.all([
        updateCategoryStats(originalData.category),
        updateCategoryStats(updateData.category)
      ])
    }
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'update_emoji',
      target: id,
      details: { 
        title: originalData.title,
        changes: updateData
      }
    })
    
    return {
      success: true,
      data: { id, ...originalData, ...updateData }
    }
  } catch (error) {
    throw new Error('更新表情包失败: ' + error.message)
  }
}

// 删除表情包
async function deleteEmoji(data, operatorId) {
  try {
    const { id } = data
    
    if (!id) {
      throw new Error('表情包ID不能为空')
    }
    
    // 获取表情包信息
    const emojiResult = await db.collection('emojis').doc(id).get()
    if (!emojiResult.data) {
      throw new Error('表情包不存在')
    }
    
    const emoji = emojiResult.data
    
    // 软删除
    await db.collection('emojis').doc(id).update({
      data: {
        status: 'deleted',
        updateTime: new Date()
      }
    })
    
    // 更新分类统计
    await updateCategoryStats(emoji.category)
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'delete_emoji',
      target: id,
      details: { title: emoji.title }
    })
    
    return {
      success: true,
      message: '删除成功'
    }
  } catch (error) {
    throw new Error('删除表情包失败: ' + error.message)
  }
}

// 批量操作
async function batchOperation(data, operatorId) {
  try {
    const { action, ids, params } = data
    
    if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error('批量操作参数错误')
    }
    
    const results = []
    
    for (const id of ids) {
      try {
        switch (action) {
          case 'delete':
            await deleteEmoji({ id }, operatorId)
            results.push({ id, success: true })
            break
          case 'updateStatus':
            await updateEmoji({ id, status: params.status }, operatorId)
            results.push({ id, success: true })
            break
          case 'updateCategory':
            await updateEmoji({ id, category: params.category }, operatorId)
            results.push({ id, success: true })
            break
          default:
            results.push({ id, success: false, error: '不支持的操作' })
        }
      } catch (error) {
        results.push({ id, success: false, error: error.message })
      }
    }
    
    const successCount = results.filter(r => r.success).length
    
    return {
      success: true,
      data: {
        total: ids.length,
        success: successCount,
        failed: ids.length - successCount,
        results
      }
    }
  } catch (error) {
    throw new Error('批量操作失败: ' + error.message)
  }
}

// 获取分类列表
async function getCategoryList() {
  try {
    const result = await db.collection('categories')
      .where({ status: _.neq('deleted') })
      .orderBy('sortOrder', 'asc')
      .get()
    
    // 计算每个分类的表情包数量
    const categoriesWithCount = await Promise.all(
      result.data.map(async (category) => {
        const emojiCount = await db.collection('emojis').where({
          category: category._id,
          status: 'published'
        }).count()
        
        return {
          ...category,
          emojiCount: emojiCount.total
        }
      })
    )
    
    return {
      success: true,
      data: categoriesWithCount
    }
  } catch (error) {
    throw new Error('获取分类列表失败: ' + error.message)
  }
}

// 创建分类
async function createCategory(data, operatorId) {
  try {
    const { name, icon, color, description, sortOrder = 0 } = data
    
    if (!name) {
      throw new Error('分类名称不能为空')
    }
    
    // 检查名称是否重复
    const existResult = await db.collection('categories').where({
      name,
      status: _.neq('deleted')
    }).get()
    
    if (existResult.data.length > 0) {
      throw new Error('分类名称已存在')
    }
    
    const now = new Date()
    const categoryData = {
      name,
      icon: icon || '📁',
      color: color || '#666666',
      description: description || '',
      sortOrder,
      status: 'active',
      createTime: now,
      updateTime: now,
      createdBy: operatorId
    }
    
    const result = await db.collection('categories').add({
      data: categoryData
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'create_category',
      target: result._id,
      details: { name }
    })
    
    return {
      success: true,
      data: { id: result._id, ...categoryData }
    }
  } catch (error) {
    throw new Error('创建分类失败: ' + error.message)
  }
}

// 更新分类
async function updateCategory(data, operatorId) {
  try {
    const { id, ...updateData } = data
    
    if (!id) {
      throw new Error('分类ID不能为空')
    }
    
    // 检查分类是否存在
    const categoryResult = await db.collection('categories').doc(id).get()
    if (!categoryResult.data) {
      throw new Error('分类不存在')
    }
    
    updateData.updateTime = new Date()
    
    await db.collection('categories').doc(id).update({
      data: updateData
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'update_category',
      target: id,
      details: { 
        name: categoryResult.data.name,
        changes: updateData
      }
    })
    
    return {
      success: true,
      data: { id, ...categoryResult.data, ...updateData }
    }
  } catch (error) {
    throw new Error('更新分类失败: ' + error.message)
  }
}

// 删除分类
async function deleteCategory(data, operatorId) {
  try {
    const { id } = data
    
    if (!id) {
      throw new Error('分类ID不能为空')
    }
    
    // 检查分类下是否有表情包
    const emojiCount = await db.collection('emojis').where({
      category: id,
      status: _.neq('deleted')
    }).count()
    
    if (emojiCount.total > 0) {
      throw new Error(`该分类下还有${emojiCount.total}个表情包，无法删除`)
    }
    
    // 获取分类信息
    const categoryResult = await db.collection('categories').doc(id).get()
    if (!categoryResult.data) {
      throw new Error('分类不存在')
    }
    
    // 软删除
    await db.collection('categories').doc(id).update({
      data: {
        status: 'deleted',
        updateTime: new Date()
      }
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'delete_category',
      target: id,
      details: { name: categoryResult.data.name }
    })
    
    return {
      success: true,
      message: '删除成功'
    }
  } catch (error) {
    throw new Error('删除分类失败: ' + error.message)
  }
}

// 获取轮播图列表
async function getBannerList() {
  try {
    const result = await db.collection('banners')
      .where({ status: _.neq('deleted') })
      .orderBy('sortOrder', 'asc')
      .get()
    
    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    throw new Error('获取轮播图列表失败: ' + error.message)
  }
}

// 创建轮播图
async function createBanner(data, operatorId) {
  try {
    const { title, subtitle, imageUrl, linkType, linkValue, buttonText, sortOrder = 0 } = data
    
    if (!title || !imageUrl) {
      throw new Error('标题和图片为必填项')
    }
    
    const now = new Date()
    const bannerData = {
      title,
      subtitle: subtitle || '',
      imageUrl,
      linkType: linkType || 'none',
      linkValue: linkValue || '',
      buttonText: buttonText || '查看详情',
      sortOrder,
      status: 'active',
      clickCount: 0,
      createTime: now,
      updateTime: now,
      createdBy: operatorId
    }
    
    const result = await db.collection('banners').add({
      data: bannerData
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'create_banner',
      target: result._id,
      details: { title }
    })
    
    return {
      success: true,
      data: { id: result._id, ...bannerData }
    }
  } catch (error) {
    throw new Error('创建轮播图失败: ' + error.message)
  }
}

// 更新轮播图
async function updateBanner(data, operatorId) {
  try {
    const { id, ...updateData } = data
    
    if (!id) {
      throw new Error('轮播图ID不能为空')
    }
    
    const bannerResult = await db.collection('banners').doc(id).get()
    if (!bannerResult.data) {
      throw new Error('轮播图不存在')
    }
    
    updateData.updateTime = new Date()
    
    await db.collection('banners').doc(id).update({
      data: updateData
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'update_banner',
      target: id,
      details: { 
        title: bannerResult.data.title,
        changes: updateData
      }
    })
    
    return {
      success: true,
      data: { id, ...bannerResult.data, ...updateData }
    }
  } catch (error) {
    throw new Error('更新轮播图失败: ' + error.message)
  }
}

// 删除轮播图
async function deleteBanner(data, operatorId) {
  try {
    const { id } = data
    
    if (!id) {
      throw new Error('轮播图ID不能为空')
    }
    
    const bannerResult = await db.collection('banners').doc(id).get()
    if (!bannerResult.data) {
      throw new Error('轮播图不存在')
    }
    
    await db.collection('banners').doc(id).update({
      data: {
        status: 'deleted',
        updateTime: new Date()
      }
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'delete_banner',
      target: id,
      details: { title: bannerResult.data.title }
    })
    
    return {
      success: true,
      message: '删除成功'
    }
  } catch (error) {
    throw new Error('删除轮播图失败: ' + error.message)
  }
}

// 获取用户列表
async function getUserList({ page = 1, limit = 20, keyword, role, status }) {
  try {
    const skip = (page - 1) * limit
    let query = db.collection('users')
    
    const conditions = []
    
    if (keyword) {
      conditions.push(_.or([
        { nickName: new RegExp(keyword, 'i') },
        { openid: new RegExp(keyword, 'i') }
      ]))
    }
    
    if (role) {
      conditions.push({ role })
    }
    
    if (status) {
      conditions.push({ status })
    }
    
    if (conditions.length > 0) {
      query = query.where(_.and(conditions))
    }
    
    const [listResult, countResult] = await Promise.all([
      query.orderBy('createTime', 'desc').skip(skip).limit(limit).get(),
      query.count()
    ])
    
    return {
      success: true,
      data: listResult.data,
      pagination: {
        page,
        limit,
        total: countResult.total,
        totalPages: Math.ceil(countResult.total / limit)
      }
    }
  } catch (error) {
    throw new Error('获取用户列表失败: ' + error.message)
  }
}

// 更新用户状态
async function updateUserStatus(data, operatorId) {
  try {
    const { userId, status, reason } = data
    
    if (!userId || !status) {
      throw new Error('用户ID和状态不能为空')
    }
    
    const userResult = await db.collection('users').doc(userId).get()
    if (!userResult.data) {
      throw new Error('用户不存在')
    }
    
    await db.collection('users').doc(userId).update({
      data: {
        status,
        updateTime: new Date()
      }
    })
    
    // 记录操作日志
    await logOperation({
      operator: operatorId,
      action: 'update_user_status',
      target: userId,
      details: { 
        nickname: userResult.data.nickName,
        status,
        reason
      }
    })
    
    return {
      success: true,
      message: '用户状态更新成功'
    }
  } catch (error) {
    throw new Error('更新用户状态失败: ' + error.message)
  }
}

// 获取统计数据
async function getStatistics({ type, period = '7d' }) {
  try {
    const now = new Date()
    let startDate = new Date()
    
    // 计算时间范围
    switch (period) {
      case '1d':
        startDate.setDate(now.getDate() - 1)
        break
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      default:
        startDate.setDate(now.getDate() - 7)
    }
    
    let result = {}
    
    switch (type) {
      case 'trends':
        result = await getTrendStatistics(startDate, now)
        break
      case 'popular':
        result = await getPopularStatistics(startDate, now)
        break
      case 'users':
        result = await getUserStatistics(startDate, now)
        break
      default:
        result = await getOverviewStatistics(startDate, now)
    }
    
    return {
      success: true,
      data: result
    }
  } catch (error) {
    throw new Error('获取统计数据失败: ' + error.message)
  }
}

// 获取趋势统计
async function getTrendStatistics(startDate, endDate) {
  // 这里可以实现具体的趋势统计逻辑
  // 由于云开发的限制，这里返回模拟数据
  return {
    labels: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
    datasets: [
      {
        label: '新增用户',
        data: [45, 52, 38, 67, 43, 58, 49]
      },
      {
        label: '活跃用户',
        data: [120, 135, 98, 156, 134, 167, 145]
      }
    ]
  }
}

// 获取热门统计
async function getPopularStatistics(startDate, endDate) {
  try {
    const popularEmojis = await db.collection('emojis')
      .where({
        status: 'published',
        createTime: _.gte(startDate)
      })
      .orderBy('downloads', 'desc')
      .limit(10)
      .get()
    
    return {
      popularEmojis: popularEmojis.data.map((emoji, index) => ({
        ...emoji,
        rank: index + 1
      }))
    }
  } catch (error) {
    throw error
  }
}

// 获取用户统计
async function getUserStatistics(startDate, endDate) {
  try {
    const [adminCount, userCount, activeCount, inactiveCount] = await Promise.all([
      db.collection('users').where({ role: 'admin' }).count(),
      db.collection('users').where({ role: 'user' }).count(),
      db.collection('users').where({ status: 'active' }).count(),
      db.collection('users').where({ status: 'inactive' }).count()
    ])
    
    return {
      roleDistribution: [
        { role: 'admin', count: adminCount.total },
        { role: 'user', count: userCount.total }
      ],
      statusDistribution: [
        { status: 'active', count: activeCount.total },
        { status: 'inactive', count: inactiveCount.total }
      ]
    }
  } catch (error) {
    throw error
  }
}

// 获取概览统计
async function getOverviewStatistics(startDate, endDate) {
  try {
    const [totalEmojis, totalUsers, totalDownloads] = await Promise.all([
      db.collection('emojis').where({ status: 'published' }).count(),
      db.collection('users').count(),
      db.collection('user_actions').where({ action: 'download' }).count()
    ])
    
    return {
      totalEmojis: totalEmojis.total,
      totalUsers: totalUsers.total,
      totalDownloads: totalDownloads.total
    }
  } catch (error) {
    throw error
  }
}

// 更新分类统计
async function updateCategoryStats(categoryId) {
  try {
    if (!categoryId) return
    
    const emojiCount = await db.collection('emojis').where({
      category: categoryId,
      status: 'published'
    }).count()
    
    await db.collection('categories').doc(categoryId).update({
      data: {
        emojiCount: emojiCount.total,
        updateTime: new Date()
      }
    })
  } catch (error) {
    console.error('更新分类统计失败:', error)
  }
}

// 记录操作日志
async function logOperation({ operator, action, target, details }) {
  try {
    await db.collection('operation_logs').add({
      data: {
        operator,
        action,
        target,
        details,
        createTime: new Date()
      }
    })
  } catch (error) {
    console.error('记录操作日志失败:', error)
  }
}

// 创建当前用户为管理员
async function createCurrentUserAsAdmin(openid) {
  try {
    console.log('创建当前用户为管理员:', openid)

    if (!openid) {
      return {
        success: false,
        error: '无法获取用户OpenID'
      }
    }

    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length > 0) {
      // 用户已存在，更新为管理员
      const userId = userResult.data[0]._id
      await db.collection('users').doc(userId).update({
        data: {
          'auth.role': 'admin',
          'auth.status': 'active',
          lastLoginTime: new Date()
        }
      })

      const user = userResult.data[0]
      return {
        success: true,
        openid: openid,
        nickname: user.profile?.nickname || '管理员',
        message: '用户已存在，已更新为管理员权限'
      }
    } else {
      // 用户不存在，创建新的管理员用户
      const adminData = {
        openid: openid,
        profile: {
          nickname: '管理员',
          avatar: ''
        },
        auth: {
          role: 'admin',
          status: 'active'
        },
        stats: {
          likeCount: 0,
          collectCount: 0,
          downloadCount: 0
        },
        createTime: new Date(),
        lastLoginTime: new Date()
      }

      await db.collection('users').add({
        data: adminData
      })

      return {
        success: true,
        openid: openid,
        nickname: '管理员',
        message: '管理员用户创建成功'
      }
    }
  } catch (error) {
    console.error('创建管理员失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 初始化测试数据
async function initTestData(adminOpenid) {
  try {
    // 清空现有数据
    await db.collection('categories').where({}).remove()
    await db.collection('emojis').where({}).remove()
    await db.collection('banners').where({}).remove()

    // 创建测试分类
    const categories = [
      { name: '搞笑', description: '搞笑表情包', sort: 1, status: 'active' },
      { name: '可爱', description: '可爱表情包', sort: 2, status: 'active' },
      { name: '愤怒', description: '愤怒表情包', sort: 3, status: 'active' }
    ]

    const categoryResults = []
    for (const category of categories) {
      const result = await db.collection('categories').add({
        data: {
          ...category,
          createTime: new Date(),
          updateTime: new Date(),
          creator: adminOpenid
        }
      })
      categoryResults.push({ ...category, _id: result._id })
    }

    // 创建测试表情包
    const emojis = [
      {
        name: '哈哈大笑',
        description: '开心大笑的表情',
        imageUrl: 'https://example.com/emoji1.jpg',
        categoryId: categoryResults[0]._id,
        tags: ['开心', '大笑'],
        status: 'active',
        sort: 1
      },
      {
        name: '可爱猫咪',
        description: '超级可爱的小猫',
        imageUrl: 'https://example.com/emoji2.jpg',
        categoryId: categoryResults[1]._id,
        tags: ['可爱', '猫咪'],
        status: 'active',
        sort: 2
      },
      {
        name: '生气脸',
        description: '愤怒的表情',
        imageUrl: 'https://example.com/emoji3.jpg',
        categoryId: categoryResults[2]._id,
        tags: ['愤怒', '生气'],
        status: 'active',
        sort: 3
      }
    ]

    for (const emoji of emojis) {
      await db.collection('emojis').add({
        data: {
          ...emoji,
          createTime: new Date(),
          updateTime: new Date(),
          creator: adminOpenid,
          viewCount: Math.floor(Math.random() * 100),
          likeCount: Math.floor(Math.random() * 50),
          downloadCount: Math.floor(Math.random() * 30)
        }
      })
    }

    return {
      success: true,
      message: '测试数据初始化成功',
      data: {
        categories: categoryResults.length,
        emojis: emojis.length
      }
    }
  } catch (error) {
    console.error('初始化测试数据失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}