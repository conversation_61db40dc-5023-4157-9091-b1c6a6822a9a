/**
 * 数据库诊断工具
 * 用于检查数据库连接、集合状态、权限配置等
 */

const DatabaseDiagnostic = {
  /**
   * 全面诊断数据库状态
   */
  async runFullDiagnostic() {
    console.log('🔍 开始数据库全面诊断...')
    
    const results = {
      cloudConnection: false,
      collections: {},
      permissions: {},
      dataIntegrity: {},
      errors: []
    }
    
    try {
      // 1. 检查云开发连接
      results.cloudConnection = await this.checkCloudConnection()
      
      // 2. 检查集合状态
      results.collections = await this.checkCollections()
      
      // 3. 检查数据完整性
      results.dataIntegrity = await this.checkDataIntegrity()
      
      // 4. 生成诊断报告
      const report = this.generateReport(results)
      console.log('📊 数据库诊断报告:', report)
      
      return results
      
    } catch (error) {
      console.error('❌ 数据库诊断失败:', error)
      results.errors.push(error.message)
      return results
    }
  },
  
  /**
   * 检查云开发连接
   */
  async checkCloudConnection() {
    try {
      // 尝试调用一个简单的云函数
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })
      
      console.log('✅ 云开发连接正常')
      return true
    } catch (error) {
      console.error('❌ 云开发连接失败:', error)
      return false
    }
  },
  
  /**
   * 检查数据库集合状态
   */
  async checkCollections() {
    const requiredCollections = ['categories', 'emojis', 'banners']
    const results = {}
    
    for (const collectionName of requiredCollections) {
      try {
        const result = await wx.cloud.callFunction({
          name: 'dataAPI',
          data: { 
            action: collectionName === 'categories' ? 'getCategories' :
                   collectionName === 'emojis' ? 'getEmojis' :
                   'getBanners',
            data: collectionName === 'emojis' ? { category: 'all', page: 1, limit: 1 } : undefined
          }
        })
        
        results[collectionName] = {
          exists: true,
          accessible: result.result?.success || false,
          count: result.result?.data?.length || 0,
          error: result.result?.success ? null : result.result?.message
        }
        
        console.log(`✅ 集合 ${collectionName}: 存在, 可访问, ${results[collectionName].count} 条数据`)
        
      } catch (error) {
        results[collectionName] = {
          exists: false,
          accessible: false,
          count: 0,
          error: error.message
        }
        console.error(`❌ 集合 ${collectionName}: 不存在或不可访问`, error.message)
      }
    }
    
    return results
  },
  
  /**
   * 检查数据完整性
   */
  async checkDataIntegrity() {
    const results = {
      hasCategories: false,
      hasEmojis: false,
      hasBanners: false,
      categoryCount: 0,
      emojiCount: 0,
      bannerCount: 0
    }
    
    try {
      // 检查分类数据
      const categoryResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })
      
      if (categoryResult.result?.success && categoryResult.result?.data) {
        results.hasCategories = categoryResult.result.data.length > 0
        results.categoryCount = categoryResult.result.data.length
      }
      
      // 检查表情包数据
      const emojiResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { 
          action: 'getEmojis',
          data: { category: 'all', page: 1, limit: 1 }
        }
      })
      
      if (emojiResult.result?.success && emojiResult.result?.data) {
        results.hasEmojis = emojiResult.result.data.length > 0
        results.emojiCount = emojiResult.result.data.length
      }
      
      // 检查横幅数据
      const bannerResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      })
      
      if (bannerResult.result?.success && bannerResult.result?.data) {
        results.hasBanners = bannerResult.result.data.length > 0
        results.bannerCount = bannerResult.result.data.length
      }
      
    } catch (error) {
      console.error('❌ 数据完整性检查失败:', error)
    }
    
    return results
  },
  
  /**
   * 生成诊断报告
   */
  generateReport(results) {
    const report = {
      summary: '数据库诊断报告',
      timestamp: new Date().toISOString(),
      status: 'unknown',
      issues: [],
      recommendations: []
    }
    
    // 分析云连接状态
    if (!results.cloudConnection) {
      report.issues.push('云开发连接失败')
      report.recommendations.push('检查网络连接和云开发环境配置')
    }
    
    // 分析集合状态
    const collections = results.collections
    Object.keys(collections).forEach(name => {
      const collection = collections[name]
      if (!collection.exists) {
        report.issues.push(`集合 ${name} 不存在`)
        report.recommendations.push(`在云开发控制台创建 ${name} 集合`)
      } else if (!collection.accessible) {
        report.issues.push(`集合 ${name} 不可访问`)
        report.recommendations.push(`检查 ${name} 集合的权限配置`)
      } else if (collection.count === 0) {
        report.issues.push(`集合 ${name} 无数据`)
        report.recommendations.push(`初始化 ${name} 集合的数据`)
      }
    })
    
    // 分析数据完整性
    const data = results.dataIntegrity
    if (!data.hasCategories) {
      report.issues.push('缺少分类数据')
      report.recommendations.push('运行数据初始化')
    }
    if (!data.hasEmojis) {
      report.issues.push('缺少表情包数据')
      report.recommendations.push('运行数据初始化')
    }
    if (!data.hasBanners) {
      report.issues.push('缺少横幅数据')
      report.recommendations.push('运行数据初始化')
    }
    
    // 确定整体状态
    if (report.issues.length === 0) {
      report.status = 'healthy'
    } else if (report.issues.length <= 2) {
      report.status = 'warning'
    } else {
      report.status = 'critical'
    }
    
    return report
  },
  
  /**
   * 显示诊断结果
   */
  showDiagnosticResult(results) {
    const report = this.generateReport(results)
    
    let message = `数据库状态: ${report.status}\n\n`
    
    if (report.issues.length > 0) {
      message += '发现问题:\n'
      report.issues.forEach((issue, index) => {
        message += `${index + 1}. ${issue}\n`
      })
      message += '\n建议:\n'
      report.recommendations.forEach((rec, index) => {
        message += `${index + 1}. ${rec}\n`
      })
    } else {
      message += '数据库状态正常！'
    }
    
    wx.showModal({
      title: '数据库诊断报告',
      content: message,
      showCancel: false,
      confirmText: '确定'
    })
  }
}

module.exports = {
  DatabaseDiagnostic
}
