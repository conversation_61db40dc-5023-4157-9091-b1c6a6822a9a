// 数据同步修复脚本
// 在微信开发者工具控制台中运行

console.log('🔧 开始修复数据同步问题...')

const DataSyncFixer = {
  // 步骤1: 检查云开发状态
  async checkCloudStatus() {
    console.log('=== 步骤1: 检查云开发状态 ===')
    
    if (!wx.cloud) {
      console.error('❌ wx.cloud 不可用')
      return false
    }
    
    try {
      // 重新初始化云开发
      wx.cloud.init({
        env: 'cloud1-5g6pvnpl88dc0142',
        traceUser: true
      })
      console.log('✅ 云开发重新初始化成功')
      return true
    } catch (error) {
      console.error('❌ 云开发初始化失败:', error)
      return false
    }
  },

  // 步骤2: 测试云函数连接
  async testCloudFunctions() {
    console.log('=== 步骤2: 测试云函数连接 ===')
    
    const functions = ['dataAPI', 'syncAPI', 'dataSync']
    const results = {}
    
    for (const funcName of functions) {
      try {
        console.log(`🔍 测试云函数: ${funcName}`)
        
        const result = await wx.cloud.callFunction({
          name: funcName,
          data: { action: 'test' }
        })
        
        results[funcName] = {
          status: 'success',
          result: result.result
        }
        console.log(`✅ ${funcName} 连接成功`)
        
      } catch (error) {
        results[funcName] = {
          status: 'error',
          error: error.message || error.errMsg
        }
        console.error(`❌ ${funcName} 连接失败:`, error.message || error.errMsg)
      }
    }
    
    return results
  },

  // 步骤3: 强制从后端获取真实数据
  async forceGetRealData() {
    console.log('=== 步骤3: 强制获取后端真实数据 ===')
    
    try {
      // 1. 获取分类数据
      console.log('📂 获取分类数据...')
      const categoriesResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })
      console.log('分类数据:', categoriesResult.result)
      
      // 2. 获取表情包数据
      console.log('😀 获取表情包数据...')
      const emojisResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { 
          action: 'getEmojis',
          data: { category: 'all', page: 1, limit: 20 }
        }
      })
      console.log('表情包数据:', emojisResult.result)
      
      // 3. 获取轮播图数据
      console.log('🎠 获取轮播图数据...')
      const bannersResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      })
      console.log('轮播图数据:', bannersResult.result)
      
      return {
        categories: categoriesResult.result,
        emojis: emojisResult.result,
        banners: bannersResult.result
      }
      
    } catch (error) {
      console.error('❌ 获取后端数据失败:', error)
      throw error
    }
  },

  // 步骤4: 清除本地缓存并强制刷新
  async clearCacheAndRefresh() {
    console.log('=== 步骤4: 清除缓存并强制刷新 ===')
    
    try {
      // 清除本地存储
      wx.clearStorageSync()
      console.log('✅ 本地存储已清除')
      
      // 获取当前页面实例
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      
      if (currentPage && currentPage.route === 'pages/index/index') {
        console.log('🔄 刷新首页数据...')
        
        // 强制重新加载数据
        if (currentPage.loadData) {
          await currentPage.loadData()
          console.log('✅ 首页数据重新加载完成')
        }
      }
      
    } catch (error) {
      console.error('❌ 清除缓存失败:', error)
    }
  },

  // 步骤5: 修复数据管理器的降级逻辑
  async fixDataManagerFallback() {
    console.log('=== 步骤5: 修复数据管理器降级逻辑 ===')
    
    try {
      // 检查是否有 DataManager
      if (typeof DataManager !== 'undefined') {
        console.log('📦 找到 DataManager，尝试强制刷新...')
        
        // 强制刷新所有数据
        if (DataManager.getCategoriesWithStats) {
          const categories = await DataManager.getCategoriesWithStats({ forceRefresh: true })
          console.log('✅ 分类数据强制刷新:', categories.length, '个')
        }
        
        if (DataManager.getAllEmojiData) {
          const emojis = await DataManager.getAllEmojiData('all', 1, 20, { forceRefresh: true })
          console.log('✅ 表情包数据强制刷新:', emojis.length, '个')
        }
        
        if (DataManager.getBannersData) {
          const banners = await DataManager.getBannersData({ forceRefresh: true })
          console.log('✅ 轮播图数据强制刷新:', banners.length, '个')
        }
      } else {
        console.log('⚠️ DataManager 未找到，跳过此步骤')
      }
      
    } catch (error) {
      console.error('❌ 修复数据管理器失败:', error)
    }
  },

  // 主修复流程
  async runFix() {
    console.log('🚀 开始数据同步修复流程...\n')
    
    try {
      // 步骤1: 检查云开发
      const cloudOk = await this.checkCloudStatus()
      if (!cloudOk) {
        console.error('❌ 云开发状态异常，无法继续')
        return
      }
      
      // 步骤2: 测试云函数
      const funcResults = await this.testCloudFunctions()
      console.log('云函数测试结果:', funcResults)
      
      // 步骤3: 获取真实数据
      const realData = await this.forceGetRealData()
      console.log('后端真实数据:', realData)
      
      // 步骤4: 清除缓存
      await this.clearCacheAndRefresh()
      
      // 步骤5: 修复数据管理器
      await this.fixDataManagerFallback()
      
      console.log('🎉 数据同步修复完成！')
      console.log('📋 请检查首页是否显示了后端的真实数据')
      
    } catch (error) {
      console.error('❌ 修复过程中出现错误:', error)
    }
  }
}

// 运行修复
DataSyncFixer.runFix()
