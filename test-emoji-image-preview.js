// 深度测试表情包图片预览功能
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testEmojiImagePreview() {
    console.log('🖼️ 深度测试表情包图片预览功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('图片') || text.includes('CORS') || text.includes('渲染表情包') || text.includes('云存储')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    // 监听网络请求
    page.on('request', request => {
        const url = request.url();
        if (url.includes('cos.ap-shanghai.myqcloud.com') || url.includes('tcb-api') || url.includes('image')) {
            console.log(`[REQUEST] ${request.method()} ${url}`);
        }
    });
    
    // 监听网络响应
    page.on('response', response => {
        const url = response.url();
        if (url.includes('cos.ap-shanghai.myqcloud.com') || url.includes('tcb-api') || url.includes('image')) {
            console.log(`[RESPONSE] ${response.status()} ${url}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：检查现有表情包的图片预览');
        
        // 进入表情包管理页面
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(5000);
        
        // 检查现有表情包的图片显示
        const existingImages = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-content tbody tr'));
            return rows.map((row, index) => {
                const imageCell = row.querySelector('td:nth-child(2)');
                const img = imageCell ? imageCell.querySelector('img') : null;
                const svg = imageCell ? imageCell.querySelector('svg') : null;
                
                return {
                    index: index + 1,
                    hasImage: !!img,
                    hasSvg: !!svg,
                    imageSrc: img ? img.src.substring(0, 100) + '...' : 'N/A',
                    imageComplete: img ? img.complete : false,
                    imageNaturalWidth: img ? img.naturalWidth : 0,
                    imageNaturalHeight: img ? img.naturalHeight : 0,
                    svgContent: svg ? svg.outerHTML.substring(0, 100) + '...' : 'N/A'
                };
            });
        });
        
        console.log('📊 现有表情包图片状态:');
        existingImages.forEach(item => {
            console.log(`\n表情包 ${item.index}:`);
            console.log(`  有图片: ${item.hasImage}`);
            console.log(`  有SVG占位符: ${item.hasSvg}`);
            if (item.hasImage) {
                console.log(`  图片源: ${item.imageSrc}`);
                console.log(`  图片加载完成: ${item.imageComplete}`);
                console.log(`  图片尺寸: ${item.imageNaturalWidth}x${item.imageNaturalHeight}`);
                
                if (item.imageComplete && item.imageNaturalWidth > 0) {
                    console.log(`  ✅ 图片预览正常`);
                } else {
                    console.log(`  🔴 图片预览异常`);
                }
            } else if (item.hasSvg) {
                console.log(`  ⚠️ 使用SVG占位符: ${item.svgContent}`);
            }
        });
        
        console.log('\n📍 第二步：测试新建表情包的图片上传和预览');
        
        // 创建新表情包
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        await addEmojiBtn.click();
        await page.waitForTimeout(3000);
        
        // 填写基本信息
        await page.fill('#emoji-title', '图片预览测试表情包');
        
        // 选择分类
        const categoryOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-category');
            if (!select || select.options.length <= 1) return [];
            return Array.from(select.options).slice(1).map(option => ({
                value: option.value,
                text: option.textContent
            }));
        });
        
        if (categoryOptions.length > 0) {
            await page.selectOption('#emoji-category', categoryOptions[0].value);
            console.log(`✅ 已选择分类: ${categoryOptions[0].text}`);
        }
        
        await page.selectOption('#emoji-status', 'published');
        console.log('✅ 已设置状态为已发布');
        
        // 创建测试图片（PNG格式）
        const testImagePath = path.join(__dirname, 'image-preview-test.png');
        const imageBuffer = await page.evaluate(() => {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');
                
                // 创建渐变背景
                const gradient = ctx.createLinearGradient(0, 0, 200, 200);
                gradient.addColorStop(0, '#FF6B6B');
                gradient.addColorStop(0.5, '#4ECDC4');
                gradient.addColorStop(1, '#45B7D1');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 200, 200);
                
                // 添加文字
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('图片', 100, 80);
                ctx.fillText('预览', 100, 120);
                ctx.fillText('测试', 100, 160);
                
                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const arrayBuffer = reader.result;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        resolve(Array.from(uint8Array));
                    };
                    reader.readAsArrayBuffer(blob);
                }, 'image/png');
            });
        });
        
        fs.writeFileSync(testImagePath, Buffer.from(imageBuffer));
        console.log('✅ 测试图片已创建');
        
        // 上传图片
        const fileInput = await page.locator('#emoji-image-file');
        await fileInput.setInputFiles(testImagePath);
        console.log('✅ 已上传测试图片');
        
        // 等待图片处理
        await page.waitForTimeout(3000);
        
        // 检查图片预览
        const previewResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const previewImg = modal ? modal.querySelector('img[id*="preview"], img[src*="data:image"], .image-preview img') : null;
            const fileInput = modal ? modal.querySelector('#emoji-image-file') : null;
            
            return {
                hasModal: !!modal,
                hasPreview: !!previewImg,
                previewSrc: previewImg ? previewImg.src.substring(0, 100) + '...' : 'N/A',
                previewComplete: previewImg ? previewImg.complete : false,
                previewWidth: previewImg ? previewImg.naturalWidth : 0,
                previewHeight: previewImg ? previewImg.naturalHeight : 0,
                fileInputValue: fileInput ? fileInput.value : 'N/A',
                fileInputFiles: fileInput ? fileInput.files.length : 0
            };
        });
        
        console.log('\n📊 图片预览检查结果:');
        console.log(`模态框存在: ${previewResult.hasModal}`);
        console.log(`预览图片存在: ${previewResult.hasPreview}`);
        console.log(`文件输入值: ${previewResult.fileInputValue}`);
        console.log(`文件数量: ${previewResult.fileInputFiles}`);
        
        if (previewResult.hasPreview) {
            console.log(`预览图片源: ${previewResult.previewSrc}`);
            console.log(`预览图片加载完成: ${previewResult.previewComplete}`);
            console.log(`预览图片尺寸: ${previewResult.previewWidth}x${previewResult.previewHeight}`);
            
            if (previewResult.previewComplete && previewResult.previewWidth > 0) {
                console.log('✅ 图片预览功能正常');
            } else {
                console.log('🔴 图片预览功能异常');
            }
        } else {
            console.log('🔴 没有找到预览图片');
        }
        
        console.log('\n📍 第三步：保存表情包并检查最终显示');
        
        // 保存表情包
        const saveResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveResult.success) {
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000);
            
            // 检查保存后的图片显示
            const savedImageResult = await page.evaluate(() => {
                const rows = Array.from(document.querySelectorAll('#emoji-content tbody tr'));
                const latestRow = rows[rows.length - 1];
                
                if (!latestRow) return { found: false };
                
                const nameCell = latestRow.querySelector('td:nth-child(3)');
                const imageCell = latestRow.querySelector('td:nth-child(2)');
                const img = imageCell ? imageCell.querySelector('img') : null;
                const svg = imageCell ? imageCell.querySelector('svg') : null;
                
                return {
                    found: true,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    hasImage: !!img,
                    hasSvg: !!svg,
                    imageSrc: img ? img.src.substring(0, 100) + '...' : 'N/A',
                    imageComplete: img ? img.complete : false,
                    imageWidth: img ? img.naturalWidth : 0,
                    imageHeight: img ? img.naturalHeight : 0,
                    isTestEmoji: nameCell ? nameCell.textContent.includes('图片预览测试') : false
                };
            });
            
            console.log('\n📊 保存后图片显示检查:');
            if (savedImageResult.found && savedImageResult.isTestEmoji) {
                console.log(`表情包名称: ${savedImageResult.name}`);
                console.log(`有图片: ${savedImageResult.hasImage}`);
                console.log(`有SVG占位符: ${savedImageResult.hasSvg}`);
                
                if (savedImageResult.hasImage) {
                    console.log(`图片源: ${savedImageResult.imageSrc}`);
                    console.log(`图片加载完成: ${savedImageResult.imageComplete}`);
                    console.log(`图片尺寸: ${savedImageResult.imageWidth}x${savedImageResult.imageHeight}`);
                    
                    if (savedImageResult.imageComplete && savedImageResult.imageWidth > 0) {
                        console.log('✅ 保存后图片显示正常');
                    } else {
                        console.log('🔴 保存后图片显示异常');
                    }
                } else if (savedImageResult.hasSvg) {
                    console.log('⚠️ 保存后使用SVG占位符，可能存在问题');
                }
            } else {
                console.log('🔴 未找到新创建的表情包或名称不匹配');
            }
        }
        
        console.log('\n📍 第四步：检查云存储配置和CORS问题');
        
        // 检查云存储相关配置
        const storageConfig = await page.evaluate(() => {
            // 检查是否有云存储相关的全局变量或配置
            const tcbApp = window.tcbApp;
            const cloudConfig = window.cloudConfig;
            
            return {
                hasTcbApp: !!tcbApp,
                hasCloudConfig: !!cloudConfig,
                tcbAppConfig: tcbApp ? {
                    config: tcbApp.config || 'N/A'
                } : null,
                cloudConfigData: cloudConfig || 'N/A'
            };
        });
        
        console.log('\n📊 云存储配置检查:');
        console.log(`TCB应用存在: ${storageConfig.hasTcbApp}`);
        console.log(`云配置存在: ${storageConfig.hasCloudConfig}`);
        console.log(`TCB配置: ${JSON.stringify(storageConfig.tcbAppConfig, null, 2)}`);
        console.log(`云配置数据: ${JSON.stringify(storageConfig.cloudConfigData, null, 2)}`);
        
        // 清理测试文件
        if (fs.existsSync(testImagePath)) {
            fs.unlinkSync(testImagePath);
        }
        
        // 截图
        await page.screenshot({ path: 'emoji-image-preview-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: emoji-image-preview-test.png');
        
        console.log('\n🎯 图片预览测试总结:');
        
        const hasWorkingImages = existingImages.some(img => img.hasImage && img.imageComplete && img.imageNaturalWidth > 0);
        const hasPreviewInModal = previewResult.hasPreview && previewResult.previewComplete;
        const hasSavedImageWorking = savedImageResult.found && savedImageResult.hasImage && savedImageResult.imageComplete;
        
        console.log(`现有图片预览: ${hasWorkingImages ? '✅ 正常' : '🔴 异常'}`);
        console.log(`模态框预览: ${hasPreviewInModal ? '✅ 正常' : '🔴 异常'}`);
        console.log(`保存后显示: ${hasSavedImageWorking ? '✅ 正常' : '🔴 异常'}`);
        
        if (hasWorkingImages && hasPreviewInModal && hasSavedImageWorking) {
            console.log('\n🎉 图片预览功能完全正常！');
            return { success: true, allWorking: true };
        } else {
            console.log('\n⚠️ 图片预览功能存在问题，需要进一步修复');
            return { success: true, allWorking: false, issues: {
                existingImages: !hasWorkingImages,
                modalPreview: !hasPreviewInModal,
                savedDisplay: !hasSavedImageWorking
            }};
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'emoji-image-preview-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testEmojiImagePreview().then(result => {
    console.log('\n🎯 图片预览测试最终结果:', result);
}).catch(console.error);
