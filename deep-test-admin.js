// 深度测试管理后台的Playwright脚本
const { chromium } = require('playwright');

async function deepTestAdmin() {
    console.log('🔍 开始深度测试管理后台...\n');
    
    const browser = await chromium.launch({ 
        headless: false,  // 显示浏览器窗口
        slowMo: 1000      // 每个操作间隔1秒，便于观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台错误
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('❌ 控制台错误:', msg.text());
        }
    });
    
    // 监听网络请求失败
    page.on('requestfailed', request => {
        console.log('🌐 网络请求失败:', request.url(), request.failure().errorText);
    });
    
    try {
        console.log('📍 步骤1: 访问管理后台页面');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 等待页面完全加载
        await page.waitForTimeout(3000);
        
        // 检查页面是否真的加载了
        const title = await page.title();
        console.log('📄 页面标题:', title);
        
        const bodyText = await page.textContent('body');
        if (bodyText.trim().length < 100) {
            console.log('❌ 页面内容过少，可能是空白页');
            console.log('页面内容长度:', bodyText.trim().length);
        } else {
            console.log('✅ 页面内容正常，长度:', bodyText.trim().length);
        }
        
        console.log('\n📍 步骤2: 检查分类管理页面');
        
        // 点击分类管理
        const categoryTab = await page.locator('text=分类管理').first();
        if (await categoryTab.isVisible()) {
            console.log('✅ 找到分类管理标签');
            await categoryTab.click();
            await page.waitForTimeout(2000);
        } else {
            console.log('❌ 未找到分类管理标签');
            // 截图保存
            await page.screenshot({ path: 'error-no-category-tab.png' });
        }
        
        console.log('\n📍 步骤3: 检查分类列表数据');
        
        // 检查是否有分类数据
        const categoryRows = await page.locator('table tbody tr').count();
        console.log('📊 分类行数:', categoryRows);
        
        if (categoryRows > 0) {
            // 检查每一行的数据
            for (let i = 0; i < Math.min(categoryRows, 5); i++) {
                const row = page.locator('table tbody tr').nth(i);
                const cells = await row.locator('td').allTextContents();
                console.log(`行 ${i + 1}:`, cells);
                
                // 检查是否有undefined
                if (cells.some(cell => cell.includes('undefined'))) {
                    console.log('❌ 发现undefined数据！');
                }
            }
        } else {
            console.log('⚠️ 没有分类数据');
        }
        
        console.log('\n📍 步骤4: 检查控制台错误');
        
        // 执行JavaScript检查数据状态
        const adminData = await page.evaluate(() => {
            return {
                hasAdminApp: typeof AdminApp !== 'undefined',
                hasCategories: typeof AdminApp !== 'undefined' && AdminApp.data && AdminApp.data.categories,
                categoriesLength: typeof AdminApp !== 'undefined' && AdminApp.data && AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                categoriesData: typeof AdminApp !== 'undefined' && AdminApp.data && AdminApp.data.categories ? AdminApp.data.categories.slice(0, 3) : null,
                hasCloudAPI: typeof CloudAPI !== 'undefined',
                cloudAPIStatus: typeof CloudAPI !== 'undefined' ? 'defined' : 'undefined'
            };
        });
        
        console.log('🔧 AdminApp状态:', adminData);
        
        console.log('\n📍 步骤5: 测试创建分类功能');
        
        // 点击添加分类按钮
        const addButton = await page.locator('text=添加分类').first();
        if (await addButton.isVisible()) {
            console.log('✅ 找到添加分类按钮');
            await addButton.click();
            await page.waitForTimeout(1000);
            
            // 检查是否打开了模态框
            const modal = await page.locator('.modal, .dialog, [role="dialog"]').first();
            if (await modal.isVisible()) {
                console.log('✅ 模态框已打开');
                
                // 填写表单
                await page.fill('input[name="name"], #category-name', '深度测试分类_' + Date.now());
                await page.fill('input[name="icon"], #category-icon', '🔍');
                await page.fill('textarea[name="description"], #category-description', '这是深度测试创建的分类');
                
                // 提交表单
                const submitButton = await page.locator('button:has-text("确定"), button:has-text("提交"), button:has-text("保存")').first();
                if (await submitButton.isVisible()) {
                    console.log('✅ 找到提交按钮');
                    await submitButton.click();
                    await page.waitForTimeout(3000);
                    
                    // 检查是否有成功提示
                    const successMessage = await page.textContent('body');
                    if (successMessage.includes('成功') || successMessage.includes('添加')) {
                        console.log('✅ 可能创建成功');
                    } else {
                        console.log('⚠️ 未发现成功提示');
                    }
                } else {
                    console.log('❌ 未找到提交按钮');
                }
            } else {
                console.log('❌ 模态框未打开');
            }
        } else {
            console.log('❌ 未找到添加分类按钮');
        }
        
        console.log('\n📍 步骤6: 刷新页面测试数据持久化');
        
        await page.reload({ waitUntil: 'networkidle' });
        await page.waitForTimeout(3000);
        
        // 重新检查分类数据
        const categoryRowsAfterRefresh = await page.locator('table tbody tr').count();
        console.log('🔄 刷新后分类行数:', categoryRowsAfterRefresh);
        
        if (categoryRowsAfterRefresh > 0) {
            const firstRowCells = await page.locator('table tbody tr').first().locator('td').allTextContents();
            console.log('🔄 刷新后第一行数据:', firstRowCells);
            
            if (firstRowCells.some(cell => cell.includes('undefined'))) {
                console.log('❌ 刷新后仍有undefined数据！这是严重问题！');
            }
        }
        
        // 最终截图
        await page.screenshot({ path: 'final-admin-state.png', fullPage: true });
        console.log('📸 已保存最终状态截图: final-admin-state.png');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'error-screenshot.png' });
    } finally {
        await browser.close();
    }
}

// 运行测试
deepTestAdmin().catch(console.error);
