// 测试修复后的云函数数据获取
const { chromium } = require('playwright');

async function testFixedCloudFunction() {
    console.log('🔧 测试修复后的云函数数据获取...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('修复后') || text.includes('查询结果') || text.includes('dataAPI')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：测试修复后的分类云函数');
        
        // 测试分类云函数
        const categoriesResult = await page.evaluate(async () => {
            try {
                const result = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                return {
                    success: true,
                    result: result.result,
                    requestId: result.requestId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 分类云函数测试结果:');
        if (categoriesResult.success && categoriesResult.result.success) {
            const categories = categoriesResult.result.data;
            console.log(`✅ 分类云函数调用成功，获取到 ${categories.length} 条数据`);
            
            categories.forEach((cat, index) => {
                console.log(`  ${index + 1}. ${cat.name} (状态: ${cat.status}, 表情包数量: ${cat.emojiCount || 0})`);
                console.log(`     渐变: ${cat.gradient ? cat.gradient.substring(0, 50) + '...' : 'N/A'}`);
            });
        } else {
            console.log(`🔴 分类云函数调用失败: ${categoriesResult.error || categoriesResult.result?.message}`);
        }
        
        console.log('\n📍 第二步：测试修复后的表情包云函数');
        
        // 测试表情包云函数
        const emojisResult = await page.evaluate(async () => {
            try {
                const result = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 10 }
                    }
                });
                
                return {
                    success: true,
                    result: result.result,
                    requestId: result.requestId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 表情包云函数测试结果:');
        if (emojisResult.success && emojisResult.result.success) {
            const emojis = emojisResult.result.data;
            console.log(`✅ 表情包云函数调用成功，获取到 ${emojis.length} 条数据`);
            
            emojis.forEach((emoji, index) => {
                console.log(`  ${index + 1}. ${emoji.title} (状态: ${emoji.status})`);
                console.log(`     分类ID: ${emoji.categoryId || 'N/A'}`);
                console.log(`     点赞: ${emoji.likes || 0}, 收藏: ${emoji.collections || 0}`);
            });
        } else {
            console.log(`🔴 表情包云函数调用失败: ${emojisResult.error || emojisResult.result?.message}`);
        }
        
        console.log('\n📍 第三步：测试修复后的横幅云函数');
        
        // 测试横幅云函数
        const bannersResult = await page.evaluate(async () => {
            try {
                const result = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                return {
                    success: true,
                    result: result.result,
                    requestId: result.requestId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 横幅云函数测试结果:');
        if (bannersResult.success && bannersResult.result.success) {
            const banners = bannersResult.result.data;
            console.log(`✅ 横幅云函数调用成功，获取到 ${banners.length} 条数据`);
            
            banners.forEach((banner, index) => {
                console.log(`  ${index + 1}. ${banner.title} (状态: ${banner.status})`);
                console.log(`     优先级: ${banner.priority || 0}`);
                console.log(`     链接: ${banner.link || 'N/A'}`);
            });
        } else {
            console.log(`🔴 横幅云函数调用失败: ${bannersResult.error || bannersResult.result?.message}`);
        }
        
        console.log('\n📍 第四步：对比修复前后的数据');
        
        // 获取管理后台数据进行对比
        const adminData = await page.evaluate(async () => {
            try {
                const categoriesResult = await CloudAPI.database.get('categories');
                const emojisResult = await CloudAPI.database.get('emojis');
                const bannersResult = await CloudAPI.database.get('banners');
                
                return {
                    success: true,
                    categories: categoriesResult.success ? categoriesResult.data.length : 0,
                    emojis: emojisResult.success ? emojisResult.data.length : 0,
                    banners: bannersResult.success ? bannersResult.data.length : 0
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据对比结果:');
        if (adminData.success) {
            const categoriesMatch = adminData.categories === (categoriesResult.result?.data?.length || 0);
            const emojisMatch = adminData.emojis === (emojisResult.result?.data?.length || 0);
            const bannersMatch = adminData.banners === (bannersResult.result?.data?.length || 0);
            
            console.log(`分类数据: 管理后台${adminData.categories}条 vs 云函数${categoriesResult.result?.data?.length || 0}条 ${categoriesMatch ? '✅ 一致' : '🔴 不一致'}`);
            console.log(`表情包数据: 管理后台${adminData.emojis}条 vs 云函数${emojisResult.result?.data?.length || 0}条 ${emojisMatch ? '✅ 一致' : '🔴 不一致'}`);
            console.log(`横幅数据: 管理后台${adminData.banners}条 vs 云函数${bannersResult.result?.data?.length || 0}条 ${bannersMatch ? '✅ 一致' : '🔴 不一致'}`);
            
            const allMatch = categoriesMatch && emojisMatch && bannersMatch;
            console.log(`\n🎯 修复结果: ${allMatch ? '🎉 完全修复成功！' : '⚠️ 部分修复成功'}`);
            
            return {
                success: true,
                fixed: allMatch,
                details: {
                    categories: { admin: adminData.categories, cloud: categoriesResult.result?.data?.length || 0, match: categoriesMatch },
                    emojis: { admin: adminData.emojis, cloud: emojisResult.result?.data?.length || 0, match: emojisMatch },
                    banners: { admin: adminData.banners, cloud: bannersResult.result?.data?.length || 0, match: bannersMatch }
                }
            };
        } else {
            console.log(`❌ 管理后台数据获取失败: ${adminData.error}`);
            return {
                success: false,
                error: adminData.error
            };
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'fixed-cloud-function-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'fixed-cloud-function-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: fixed-cloud-function-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testFixedCloudFunction().then(result => {
    console.log('\n🎯 云函数修复测试最终结果:', result);
    
    if (result.success && result.fixed) {
        console.log('🎉 云函数修复完全成功！数据同步问题已解决。');
    } else if (result.success) {
        console.log('⚠️ 云函数部分修复成功，仍有部分数据不一致。');
    } else {
        console.log('❌ 云函数修复测试失败。');
    }
}).catch(console.error);
