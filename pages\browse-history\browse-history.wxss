/* pages/browse-history/browse-history.wxss */
.container {
  padding: 0 20rpx 120rpx 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 20rpx 30rpx 20rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.page-desc {
  font-size: 26rpx;
  color: #666;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4facfe;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 表情包信息流 - 和首页样式一致 */
.emoji-section {
  margin: 40rpx 20rpx 0;
}

.emoji-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: scale(0.98);
}

.emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
}

.emoji-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .emoji-list {
    grid-template-columns: 1fr;
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.explore-btn {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
}

.explore-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.4);
}

/* 清空记录按钮 */
.clear-section {
  padding: 40rpx 20rpx;
  text-align: center;
}

.clear-btn {
  background: #fff;
  color: #ff6b6b;
  border: 2rpx solid #ff6b6b;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: #ff6b6b;
  color: white;
}

/* 卡片通用样式 */
.card {
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
