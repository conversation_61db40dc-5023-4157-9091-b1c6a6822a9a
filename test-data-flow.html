<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据流测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #666;
            margin-top: 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .flow-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .flow-step h3 {
            margin-top: 0;
            color: #495057;
        }
        .data-preview {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 11px;
            max-height: 150px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
        .status-pending {
            background-color: #ffc107;
        }
        .status-unknown {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 数据流测试</h1>
        
        <div class="test-section">
            <h2>📋 初始化</h2>
            <button onclick="initCloudSDK()">初始化云开发SDK</button>
            <button onclick="clearLog()">清空日志</button>
            <div id="initStatus" class="log">等待初始化云开发SDK...</div>
        </div>

        <div class="test-section">
            <h2>🔄 完整数据流测试</h2>
            <button onclick="testCompleteDataFlow()" id="testFlowBtn" disabled>测试完整数据流</button>
            <button onclick="testDataConsistency()" id="testConsistencyBtn" disabled>测试数据一致性</button>
            <button onclick="simulateMiniProgramFlow()" id="simulateBtn" disabled>模拟小程序数据流</button>
        </div>

        <div class="test-section">
            <h2>📊 数据流步骤</h2>
            <div id="flowSteps"></div>
        </div>

        <div class="test-section">
            <h2>📝 详细日志</h2>
            <div id="log" class="log">等待开始测试...</div>
        </div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;
        let isInitialized = false;
        let dataFlowResults = {};

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('flowSteps').innerHTML = '';
            dataFlowResults = {};
        }

        // 初始化云开发SDK
        async function initCloudSDK() {
            const statusElement = document.getElementById('initStatus');
            
            try {
                log('🚀 开始初始化云开发SDK...');
                statusElement.innerHTML = '<span class="info">正在初始化云开发SDK...</span>';
                
                if (typeof cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }

                tcbApp = cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });

                // 匿名登录
                log('🔐 进行匿名登录...');
                statusElement.innerHTML = '<span class="info">正在进行匿名登录...</span>';
                const auth = tcbApp.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功', 'success');

                // 测试数据库连接
                log('🔍 测试数据库连接...');
                statusElement.innerHTML = '<span class="info">正在测试数据库连接...</span>';
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();
                log('✅ 数据库连接测试成功', 'success');

                isInitialized = true;
                statusElement.innerHTML = '<span class="success">✅ 云开发SDK初始化完成</span>';
                
                // 启用按钮
                document.getElementById('testFlowBtn').disabled = false;
                document.getElementById('testConsistencyBtn').disabled = false;
                document.getElementById('simulateBtn').disabled = false;
                
                log('✅ 云开发SDK初始化完成，可以开始测试', 'success');
                
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
                statusElement.innerHTML = '<span class="error">❌ 初始化失败: ' + error.message + '</span>';
            }
        }

        // 添加流程步骤
        function addFlowStep(stepName, status, data, description) {
            const flowStepsContainer = document.getElementById('flowSteps');
            const stepElement = document.createElement('div');
            stepElement.className = 'flow-step';
            
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 
                               status === 'pending' ? 'status-pending' : 'status-unknown';
            
            stepElement.innerHTML = `
                <h3><span class="status-indicator ${statusClass}"></span>${stepName}</h3>
                <p>${description}</p>
                ${data ? `<div class="data-preview">${JSON.stringify(data, null, 2)}</div>` : ''}
            `;
            
            flowStepsContainer.appendChild(stepElement);
        }

        // 测试完整数据流
        async function testCompleteDataFlow() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('🔄 开始测试完整数据流...');
                clearFlowSteps();

                // 步骤1: 测试云数据库直接访问
                addFlowStep('步骤1: 云数据库直接访问', 'pending', null, '直接从云数据库获取数据');
                
                const db = tcbApp.database();
                const directCategories = await db.collection('categories').get();
                const directEmojis = await db.collection('emojis').limit(5).get();
                const directBanners = await db.collection('banners').get();
                
                addFlowStep('步骤1: 云数据库直接访问', 'success', {
                    categories: directCategories.data.length,
                    emojis: directEmojis.data.length,
                    banners: directBanners.data.length
                }, '直接数据库访问成功');

                // 步骤2: 测试云函数调用
                addFlowStep('步骤2: 云函数调用', 'pending', null, '通过dataAPI云函数获取数据');
                
                const cloudCategories = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                const cloudEmojis = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 5 }
                    }
                });
                
                const cloudBanners = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                addFlowStep('步骤2: 云函数调用', 'success', {
                    categories: cloudCategories.result.data?.length || 0,
                    emojis: cloudEmojis.result.data?.length || 0,
                    banners: cloudBanners.result.data?.length || 0
                }, '云函数调用成功');

                // 步骤3: 数据格式验证
                addFlowStep('步骤3: 数据格式验证', 'pending', null, '验证数据格式是否符合小程序期望');
                
                const formatValidation = validateDataFormat({
                    categories: cloudCategories.result.data,
                    emojis: cloudEmojis.result.data,
                    banners: cloudBanners.result.data
                });
                
                addFlowStep('步骤3: 数据格式验证', formatValidation.success ? 'success' : 'error', 
                    formatValidation, '数据格式验证完成');

                // 步骤4: 模拟小程序数据处理
                addFlowStep('步骤4: 小程序数据处理', 'pending', null, '模拟小程序端的数据处理逻辑');
                
                const processedData = simulateMiniprogramDataProcessing({
                    categories: cloudCategories.result.data,
                    emojis: cloudEmojis.result.data,
                    banners: cloudBanners.result.data
                });
                
                addFlowStep('步骤4: 小程序数据处理', 'success', processedData, '数据处理完成');

                log('🎉 完整数据流测试完成', 'success');
                
            } catch (error) {
                log('❌ 数据流测试失败: ' + error.message, 'error');
                addFlowStep('错误', 'error', { error: error.message }, '数据流测试失败');
            }
        }

        // 验证数据格式
        function validateDataFormat(data) {
            const validation = {
                success: true,
                issues: [],
                categories: { valid: true, issues: [] },
                emojis: { valid: true, issues: [] },
                banners: { valid: true, issues: [] }
            };

            // 验证分类数据
            if (data.categories && Array.isArray(data.categories)) {
                data.categories.forEach((category, index) => {
                    if (!category._id && !category.id) {
                        validation.categories.issues.push(`分类${index}: 缺少ID字段`);
                        validation.categories.valid = false;
                    }
                    if (!category.name) {
                        validation.categories.issues.push(`分类${index}: 缺少name字段`);
                        validation.categories.valid = false;
                    }
                });
            } else {
                validation.categories.issues.push('分类数据不是数组或为空');
                validation.categories.valid = false;
            }

            // 验证表情包数据
            if (data.emojis && Array.isArray(data.emojis)) {
                data.emojis.forEach((emoji, index) => {
                    if (!emoji._id && !emoji.id) {
                        validation.emojis.issues.push(`表情包${index}: 缺少ID字段`);
                        validation.emojis.valid = false;
                    }
                    if (!emoji.title) {
                        validation.emojis.issues.push(`表情包${index}: 缺少title字段`);
                        validation.emojis.valid = false;
                    }
                    if (!emoji.imageUrl) {
                        validation.emojis.issues.push(`表情包${index}: 缺少imageUrl字段`);
                        validation.emojis.valid = false;
                    }
                });
            } else {
                validation.emojis.issues.push('表情包数据不是数组或为空');
                validation.emojis.valid = false;
            }

            // 验证横幅数据
            if (data.banners && Array.isArray(data.banners)) {
                data.banners.forEach((banner, index) => {
                    if (!banner._id && !banner.id) {
                        validation.banners.issues.push(`横幅${index}: 缺少ID字段`);
                        validation.banners.valid = false;
                    }
                    if (!banner.title) {
                        validation.banners.issues.push(`横幅${index}: 缺少title字段`);
                        validation.banners.valid = false;
                    }
                });
            }

            validation.success = validation.categories.valid && validation.emojis.valid && validation.banners.valid;
            validation.issues = [
                ...validation.categories.issues,
                ...validation.emojis.issues,
                ...validation.banners.issues
            ];

            return validation;
        }

        // 模拟小程序数据处理
        function simulateMiniprogramDataProcessing(data) {
            const processed = {
                categories: [],
                emojis: [],
                banners: [],
                stats: {}
            };

            // 处理分类数据
            if (data.categories) {
                processed.categories = data.categories.map(category => ({
                    id: category._id || category.id,
                    name: category.name,
                    icon: category.icon || '📁',
                    count: category.emojiCount || category.count || 0,
                    gradient: category.gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                }));
            }

            // 处理表情包数据
            if (data.emojis) {
                processed.emojis = data.emojis.map(emoji => ({
                    id: emoji._id || emoji.id,
                    title: emoji.title,
                    imageUrl: emoji.imageUrl,
                    category: emoji.category,
                    likes: emoji.likes || 0,
                    downloads: emoji.downloads || 0,
                    collections: emoji.collections || 0
                }));
            }

            // 处理横幅数据
            if (data.banners) {
                processed.banners = data.banners.map(banner => ({
                    id: banner._id || banner.id,
                    title: banner.title,
                    imageUrl: banner.imageUrl,
                    linkUrl: banner.linkUrl || banner.link
                }));
            }

            // 统计信息
            processed.stats = {
                totalCategories: processed.categories.length,
                totalEmojis: processed.emojis.length,
                totalBanners: processed.banners.length,
                processedAt: new Date().toISOString()
            };

            return processed;
        }

        // 清空流程步骤
        function clearFlowSteps() {
            document.getElementById('flowSteps').innerHTML = '';
        }

        // 测试数据一致性
        async function testDataConsistency() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('🔍 开始测试数据一致性...');
                
                // 获取数据
                const result = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                if (result.result.success && result.result.data) {
                    const categories = result.result.data;
                    log(`📊 获取到 ${categories.length} 个分类`);
                    
                    // 检查每个分类的表情包数量
                    for (const category of categories) {
                        const emojiResult = await tcbApp.callFunction({
                            name: 'dataAPI',
                            data: { 
                                action: 'getEmojis',
                                data: { category: category._id || category.id, page: 1, limit: 100 }
                            }
                        });
                        
                        const actualCount = emojiResult.result.data?.length || 0;
                        const reportedCount = category.emojiCount || category.count || 0;
                        
                        if (actualCount !== reportedCount) {
                            log(`⚠️ 分类 ${category.name} 数量不一致: 实际${actualCount}, 报告${reportedCount}`, 'warning');
                        } else {
                            log(`✅ 分类 ${category.name} 数量一致: ${actualCount}`, 'success');
                        }
                    }
                }
                
                log('🎉 数据一致性测试完成', 'success');
                
            } catch (error) {
                log('❌ 数据一致性测试失败: ' + error.message, 'error');
            }
        }

        // 模拟小程序数据流
        async function simulateMiniProgramFlow() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('📱 开始模拟小程序数据流...');
                
                // 模拟小程序启动时的数据加载顺序
                log('1️⃣ 模拟首页加载分类数据...');
                const categoriesResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                log(`✅ 分类数据加载完成: ${categoriesResult.result.data?.length || 0} 个`);

                log('2️⃣ 模拟首页加载横幅数据...');
                const bannersResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                log(`✅ 横幅数据加载完成: ${bannersResult.result.data?.length || 0} 个`);

                log('3️⃣ 模拟首页加载表情包数据...');
                const emojisResult = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 20 }
                    }
                });
                log(`✅ 表情包数据加载完成: ${emojisResult.result.data?.length || 0} 个`);

                log('4️⃣ 模拟分类页面数据加载...');
                if (categoriesResult.result.data && categoriesResult.result.data.length > 0) {
                    const firstCategory = categoriesResult.result.data[0];
                    const categoryEmojisResult = await tcbApp.callFunction({
                        name: 'dataAPI',
                        data: { 
                            action: 'getEmojis',
                            data: { category: firstCategory._id || firstCategory.id, page: 1, limit: 20 }
                        }
                    });
                    log(`✅ 分类表情包数据加载完成: ${categoryEmojisResult.result.data?.length || 0} 个`);
                }

                log('🎉 小程序数据流模拟完成', 'success');
                
            } catch (error) {
                log('❌ 小程序数据流模拟失败: ' + error.message, 'error');
            }
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            log('页面加载完成，请点击"初始化云开发SDK"按钮开始测试');
        });
    </script>
</body>
</html>
