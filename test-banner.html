<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横幅功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .banner-preview {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .banner-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
        }
        .banner-thumbnail {
            width: 120px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.2s;
            border: 2px solid #e2e8f0;
        }
        .banner-thumbnail:hover {
            transform: scale(1.05);
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn:hover { opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 横幅功能测试页面</h1>
        
        <div class="test-section">
            <h3>1. 测试横幅数据</h3>
            <p>模拟一些横幅数据来测试渲染功能：</p>
            <div class="banner-preview" id="test-banners"></div>
            <button class="btn btn-primary" onclick="generateTestBanners()">生成测试横幅</button>
        </div>

        <div class="test-section">
            <h3>2. 图片URL处理测试</h3>
            <p>测试不同类型的图片URL处理：</p>
            <div id="url-test-results"></div>
            <button class="btn btn-success" onclick="testImageUrls()">测试图片URL</button>
        </div>

        <div class="test-section">
            <h3>3. 错误处理测试</h3>
            <p>测试图片加载失败的处理：</p>
            <div id="error-test-results"></div>
            <button class="btn btn-warning" onclick="testErrorHandling()">测试错误处理</button>
        </div>
    </div>

    <script>
        // 模拟横幅渲染函数（从主文件复制）
        function renderBannerImage(banner) {
            let imageUrl = banner.imageUrl || banner.image || '';
            const bannerId = banner._id || banner.id || '';
            const title = banner.title || '横幅图片';

            console.log(`🖼️ 渲染横幅图片: ${title}, imageUrl: ${imageUrl}`);

            if (!imageUrl || imageUrl === '/images/placeholder.png' || imageUrl === '/images/banner-placeholder.jpg' || imageUrl.includes('placeholder')) {
                const svgContent = `
                    <svg width="120" height="60" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#f3f4f6"/>
                        <text x="50%" y="40%" font-family="Arial" font-size="12" fill="#9ca3af" text-anchor="middle" dy=".3em">🎨</text>
                        <text x="50%" y="70%" font-family="Arial" font-size="8" fill="#6b7280" text-anchor="middle" dy=".3em">横幅图片</text>
                    </svg>
                `;
                imageUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
                console.log(`使用SVG占位符`);
            } else if (imageUrl.startsWith('data:image/')) {
                console.log(`使用base64横幅图片，长度: ${imageUrl.length}`);
            } else if (imageUrl.startsWith('/uploads/')) {
                imageUrl = `https://picsum.photos/400/200?random=${Math.floor(Math.random() * 1000)}`;
                console.log(`虚拟路径替换为测试横幅图片: ${imageUrl}`);
            } else if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                console.log(`使用外部横幅图片URL: ${imageUrl}`);
            } else {
                imageUrl = `https://picsum.photos/400/200?random=${Math.floor(Math.random() * 1000)}`;
                console.log(`未知URL格式，使用测试横幅图片: ${imageUrl}`);
            }

            return `
                <div class="banner-item">
                    <img src="${imageUrl}"
                         class="banner-thumbnail"
                         alt="${title}"
                         onclick="alert('预览: ${title}')"
                         onerror="handleBannerImageError(this, '${title}')"
                         loading="lazy"
                         title="点击预览大图 - ${title}">
                    <div style="margin-top: 8px; font-size: 12px; color: #666;">${title}</div>
                </div>
            `;
        }

        function handleBannerImageError(img, title) {
            console.log(`❌ 横幅图片加载失败: ${title}, 原始src: ${img.src}`);
            img.onerror = null;

            const svgContent = `
                <svg width="120" height="60" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#fee2e2" stroke="#fca5a5" stroke-width="1"/>
                    <text x="50%" y="35%" font-family="Arial" font-size="12" fill="#dc2626" text-anchor="middle" dy=".3em">❌</text>
                    <text x="50%" y="65%" font-family="Arial" font-size="8" fill="#dc2626" text-anchor="middle" dy=".3em">加载失败</text>
                </svg>
            `;

            const svgPlaceholder = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
            
            img.src = svgPlaceholder;
            img.style.opacity = '0.8';
            img.style.border = '2px dashed #fca5a5';
            img.title = `横幅图片加载失败: ${title}`;
        }

        function generateTestBanners() {
            const testBanners = [
                { _id: '1', title: '新年特惠', imageUrl: 'https://picsum.photos/400/200?random=1' },
                { _id: '2', title: '测试横幅', imageUrl: '/uploads/test.jpg' },
                { _id: '3', title: '第二个测试', imageUrl: '' },
                { _id: '4', title: '测试第三', imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNGY0NmU1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5UZXN0IEJhbm5lcjwvdGV4dD4KPC9zdmc+' }
            ];

            let html = '';
            testBanners.forEach(banner => {
                html += renderBannerImage(banner);
            });

            document.getElementById('test-banners').innerHTML = html;
        }

        function testImageUrls() {
            const testUrls = [
                { type: '外部URL', url: 'https://picsum.photos/400/200?random=100' },
                { type: '虚拟路径', url: '/uploads/banner.jpg' },
                { type: '空URL', url: '' },
                { type: '占位符', url: '/images/placeholder.png' },
                { type: 'Base64', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMTBiOTgxIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5CYXNlNjQ8L3RleHQ+Cjwvc3ZnPg==' }
            ];

            let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';
            testUrls.forEach((test, index) => {
                const banner = { _id: `test-${index}`, title: test.type, imageUrl: test.url };
                html += `<div style="text-align: center;">
                    <h4>${test.type}</h4>
                    ${renderBannerImage(banner)}
                </div>`;
            });
            html += '</div>';

            document.getElementById('url-test-results').innerHTML = html;
        }

        function testErrorHandling() {
            const errorBanners = [
                { _id: 'err1', title: '无效URL', imageUrl: 'https://invalid-url.com/image.jpg' },
                { _id: 'err2', title: '404图片', imageUrl: 'https://httpstat.us/404.jpg' },
                { _id: 'err3', title: '错误域名', imageUrl: 'https://nonexistent-domain-12345.com/image.jpg' }
            ];

            let html = '<div style="display: flex; gap: 20px; flex-wrap: wrap;">';
            errorBanners.forEach(banner => {
                html += renderBannerImage(banner);
            });
            html += '</div>';

            document.getElementById('error-test-results').innerHTML = html;
        }

        // 页面加载时自动生成测试数据
        window.onload = function() {
            generateTestBanners();
        };
    </script>
</body>
</html>
