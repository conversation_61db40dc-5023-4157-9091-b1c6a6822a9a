/**
 * 网络请求优化器
 * 提供请求去重、合并、缓存、重试等优化功能
 */

const NetworkOptimizer = {
  // 请求缓存
  _requestCache: new Map(),
  
  // 正在进行的请求
  _pendingRequests: new Map(),
  
  // 请求队列
  _requestQueue: [],
  
  // 并发控制
  _concurrentLimit: 5,
  _activeRequests: 0,
  
  // 配置选项
  config: {
    enableCache: true,
    cacheTimeout: 5 * 60 * 1000, // 5分钟
    enableDeduplication: true,
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    enableRequestMerging: true,
    mergingWindow: 100 // 100ms内的相同请求会被合并
  },

  /**
   * 初始化网络优化器
   */
  init(options = {}) {
    this.config = { ...this.config, ...options }
    console.log('🌐 网络请求优化器初始化')
    
    // 定期清理过期缓存
    this.startCacheCleanup()
    
    // 处理请求队列
    this.processRequestQueue()
  },

  /**
   * 优化的请求方法
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(options) {
    const requestKey = this.generateRequestKey(options)
    
    // 检查缓存
    if (this.config.enableCache) {
      const cachedResult = this.getCachedResult(requestKey)
      if (cachedResult) {
        console.log('📦 使用缓存结果:', requestKey)
        return cachedResult
      }
    }
    
    // 检查请求去重
    if (this.config.enableDeduplication) {
      const pendingRequest = this._pendingRequests.get(requestKey)
      if (pendingRequest) {
        console.log('🔄 合并重复请求:', requestKey)
        return pendingRequest
      }
    }
    
    // 创建请求Promise
    const requestPromise = this.executeRequest(options, requestKey)
    
    // 记录正在进行的请求
    if (this.config.enableDeduplication) {
      this._pendingRequests.set(requestKey, requestPromise)
    }
    
    try {
      const result = await requestPromise
      
      // 缓存结果
      if (this.config.enableCache && this.shouldCache(options, result)) {
        this.cacheResult(requestKey, result)
      }
      
      return result
    } finally {
      // 清理正在进行的请求记录
      this._pendingRequests.delete(requestKey)
    }
  },

  /**
   * 执行实际请求
   * @param {Object} options - 请求选项
   * @param {string} requestKey - 请求键
   * @returns {Promise} 请求结果
   */
  async executeRequest(options, requestKey) {
    // 并发控制
    if (this._activeRequests >= this._concurrentLimit) {
      console.log('⏳ 请求队列等待:', requestKey)
      await this.waitForSlot()
    }
    
    this._activeRequests++
    
    try {
      return await this.performRequestWithRetry(options)
    } finally {
      this._activeRequests--
      this.processNextInQueue()
    }
  },

  /**
   * 带重试的请求执行
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async performRequestWithRetry(options) {
    let lastError = null
    const maxRetries = this.config.enableRetry ? this.config.maxRetries : 0
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1)
          console.log(`🔄 重试请求 (${attempt}/${maxRetries}), 延迟: ${delay}ms`)
          await this.delay(delay)
        }
        
        return await this.performSingleRequest(options)
      } catch (error) {
        lastError = error
        
        // 检查是否应该重试
        if (!this.shouldRetry(error, attempt, maxRetries)) {
          break
        }
      }
    }
    
    throw lastError
  },

  /**
   * 执行单次请求
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  performSingleRequest(options) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      wx.request({
        ...options,
        success: (response) => {
          const duration = Date.now() - startTime
          console.log(`✅ 请求成功: ${options.url} (${duration}ms)`)
          
          // 记录性能数据
          this.recordPerformance(options, duration, true)
          
          if (response.statusCode >= 200 && response.statusCode < 300) {
            resolve(response)
          } else {
            reject(new Error(`HTTP ${response.statusCode}: ${response.data?.message || 'Request failed'}`))
          }
        },
        fail: (error) => {
          const duration = Date.now() - startTime
          console.error(`❌ 请求失败: ${options.url} (${duration}ms)`, error)
          
          // 记录性能数据
          this.recordPerformance(options, duration, false)
          
          reject(error)
        }
      })
    })
  },

  /**
   * 生成请求键
   * @param {Object} options - 请求选项
   * @returns {string} 请求键
   */
  generateRequestKey(options) {
    const key = `${options.method || 'GET'}_${options.url}_${JSON.stringify(options.data || {})}`
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '') // Base64编码并清理特殊字符
  },

  /**
   * 获取缓存结果
   * @param {string} requestKey - 请求键
   * @returns {Object|null} 缓存结果
   */
  getCachedResult(requestKey) {
    const cached = this._requestCache.get(requestKey)
    if (!cached) return null
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.config.cacheTimeout) {
      this._requestCache.delete(requestKey)
      return null
    }
    
    return cached.result
  },

  /**
   * 缓存结果
   * @param {string} requestKey - 请求键
   * @param {Object} result - 请求结果
   */
  cacheResult(requestKey, result) {
    this._requestCache.set(requestKey, {
      result: result,
      timestamp: Date.now()
    })
  },

  /**
   * 检查是否应该缓存
   * @param {Object} options - 请求选项
   * @param {Object} result - 请求结果
   * @returns {boolean} 是否应该缓存
   */
  shouldCache(options, result) {
    // GET请求且成功响应才缓存
    return (options.method || 'GET') === 'GET' && 
           result.statusCode >= 200 && 
           result.statusCode < 300
  },

  /**
   * 检查是否应该重试
   * @param {Error} error - 错误对象
   * @param {number} attempt - 当前尝试次数
   * @param {number} maxRetries - 最大重试次数
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error, attempt, maxRetries) {
    if (attempt >= maxRetries) return false
    
    // 网络错误或5xx服务器错误才重试
    if (error.errMsg && error.errMsg.includes('timeout')) return true
    if (error.errMsg && error.errMsg.includes('fail')) return true
    if (error.message && error.message.includes('HTTP 5')) return true
    
    return false
  },

  /**
   * 等待请求槽位
   * @returns {Promise} 等待Promise
   */
  waitForSlot() {
    return new Promise((resolve) => {
      this._requestQueue.push(resolve)
    })
  },

  /**
   * 处理队列中的下一个请求
   */
  processNextInQueue() {
    if (this._requestQueue.length > 0 && this._activeRequests < this._concurrentLimit) {
      const nextResolve = this._requestQueue.shift()
      nextResolve()
    }
  },

  /**
   * 处理请求队列
   */
  processRequestQueue() {
    setInterval(() => {
      this.processNextInQueue()
    }, 10) // 每10ms检查一次队列
  },

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 记录性能数据
   * @param {Object} options - 请求选项
   * @param {number} duration - 请求耗时
   * @param {boolean} success - 是否成功
   */
  recordPerformance(options, duration, success) {
    try {
      const PerformanceMonitor = require('./performanceMonitor.js')
      PerformanceMonitor.recordApiCall(options.url, duration, success)
    } catch (error) {
      // 忽略性能记录错误
    }
  },

  /**
   * 开始缓存清理
   */
  startCacheCleanup() {
    setInterval(() => {
      this.cleanupExpiredCache()
    }, 60000) // 每分钟清理一次
  },

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now()
    let cleanedCount = 0
    
    for (const [key, cached] of this._requestCache.entries()) {
      if (now - cached.timestamp > this.config.cacheTimeout) {
        this._requestCache.delete(key)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理过期缓存: ${cleanedCount} 个`)
    }
  },

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      cacheSize: this._requestCache.size,
      pendingRequests: this._pendingRequests.size,
      queueLength: this._requestQueue.length,
      activeRequests: this._activeRequests,
      config: this.config
    }
  },

  /**
   * 清理所有缓存
   */
  clearCache() {
    this._requestCache.clear()
    console.log('🧹 网络请求缓存已清理')
  }
}

module.exports = NetworkOptimizer
