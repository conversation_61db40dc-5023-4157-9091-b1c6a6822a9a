// 快速数据初始化脚本
// 在微信开发者工具控制台中执行

console.log('🚀 开始快速初始化数据...');

const db = wx.cloud.database();

// 1. 初始化分类数据
async function initCategories() {
  console.log('📋 初始化分类数据...');
  
  const categories = [
    {
      name: '搞笑幽默',
      icon: '😂',
      status: 'show',
      sort: 1,
      createTime: new Date()
    },
    {
      name: '可爱萌宠',
      icon: '🐱',
      status: 'show',
      sort: 2,
      createTime: new Date()
    },
    {
      name: '情感表达',
      icon: '❤️',
      status: 'show',
      sort: 3,
      createTime: new Date()
    },
    {
      name: '网络热梗',
      icon: '🔥',
      status: 'show',
      sort: 4,
      createTime: new Date()
    }
  ];

  for (const category of categories) {
    try {
      const result = await db.collection('categories').add({
        data: category
      });
      console.log(`✅ 分类创建成功: ${category.name} (${result._id})`);
    } catch (error) {
      console.error(`❌ 分类创建失败: ${category.name}`, error);
    }
  }
}

// 2. 初始化表情包数据
async function initEmojis() {
  console.log('😀 初始化表情包数据...');
  
  const emojis = [
    {
      title: '开心笑脸',
      imageUrl: 'https://picsum.photos/300/300?random=1',
      category: '搞笑幽默',
      status: 'published',
      likes: 123,
      collections: 45,
      createTime: new Date()
    },
    {
      title: '可爱小猫',
      imageUrl: 'https://picsum.photos/300/300?random=2',
      category: '可爱萌宠',
      status: 'published',
      likes: 89,
      collections: 32,
      createTime: new Date()
    },
    {
      title: '爱心表情',
      imageUrl: 'https://picsum.photos/300/300?random=3',
      category: '情感表达',
      status: 'published',
      likes: 156,
      collections: 78,
      createTime: new Date()
    },
    {
      title: '网络梗图',
      imageUrl: 'https://picsum.photos/300/300?random=4',
      category: '网络热梗',
      status: 'published',
      likes: 234,
      collections: 123,
      createTime: new Date()
    },
    {
      title: '搞笑表情',
      imageUrl: 'https://picsum.photos/300/300?random=5',
      category: '搞笑幽默',
      status: 'published',
      likes: 67,
      collections: 23,
      createTime: new Date()
    }
  ];

  for (const emoji of emojis) {
    try {
      const result = await db.collection('emojis').add({
        data: emoji
      });
      console.log(`✅ 表情包创建成功: ${emoji.title} (${result._id})`);
    } catch (error) {
      console.error(`❌ 表情包创建失败: ${emoji.title}`, error);
    }
  }
}

// 3. 执行初始化
async function quickInit() {
  try {
    // 清空现有数据（可选）
    console.log('🗑️ 清空现有数据...');
    await db.collection('categories').where({}).remove();
    await db.collection('emojis').where({}).remove();
    
    // 初始化新数据
    await initCategories();
    await initEmojis();
    
    console.log('🎉 数据初始化完成！');
    
    // 验证数据
    const categoryCount = await db.collection('categories').count();
    const emojiCount = await db.collection('emojis').count();
    
    console.log(`📊 验证结果: ${categoryCount.total} 个分类, ${emojiCount.total} 个表情包`);
    
  } catch (error) {
    console.error('❌ 初始化失败:', error);
  }
}

// 执行初始化
quickInit();

// 使用说明：
// 1. 复制整个脚本到微信开发者工具控制台
// 2. 按回车执行
// 3. 等待初始化完成
// 4. 重新测试dataAPI云函数
