@echo off
chcp 65001 >nul
title V1.0 表情包小程序一键部署

echo.
echo ========================================
echo 🚀 V1.0 表情包小程序一键部署
echo ========================================
echo.

:: 检查Node.js
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装！
    echo 💡 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js已安装

:: 检查npm
echo 🔍 检查npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm未安装！
    pause
    exit /b 1
)
echo ✅ npm已安装

:: 安装CloudBase CLI
echo.
echo 📦 安装CloudBase CLI工具...
npm install -g @cloudbase/cli
if errorlevel 1 (
    echo ❌ CloudBase CLI安装失败！
    echo 💡 请检查网络连接或手动安装: npm install -g @cloudbase/cli
    pause
    exit /b 1
)
echo ✅ CloudBase CLI安装完成

:: 检查登录状态
echo.
echo 🔍 检查腾讯云登录状态...
tcb env:list >nul 2>&1
if errorlevel 1 (
    echo ❌ 未登录腾讯云！
    echo.
    echo 💡 请先登录腾讯云：
    echo    1. 运行命令: tcb login
    echo    2. 在浏览器中完成登录
    echo    3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)
echo ✅ 已登录腾讯云

:: 运行智能部署脚本
echo.
echo 🚀 开始智能部署...
echo ========================================
node scripts/智能部署.js

:: 检查部署结果
if errorlevel 1 (
    echo.
    echo ❌ 部署失败！
    echo 💡 请查看上方错误信息或联系技术支持
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 部署完成！
echo ========================================
echo.
echo 📱 接下来的步骤：
echo 1. 打开管理后台测试功能
echo 2. 配置微信小程序
echo 3. 上传发布小程序
echo.

:: 询问是否打开管理后台
set /p choice="是否现在打开管理后台？(y/n): "
if /i "%choice%"=="y" (
    echo 🌐 正在打开管理后台...
    start 链路打通验证.html
)

echo.
echo 📄 部署日志已保存到: deploy.log
echo 💡 如有问题，请查看日志文件
echo.
pause
