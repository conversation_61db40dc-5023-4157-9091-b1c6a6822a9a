<!DOCTYPE html>
<html>
<head>
    <title>TabBar图标生成器 - 一键生成</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 5px;
            border-radius: 4px;
        }
        .download-btn {
            background: #8B5CF6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .download-btn:hover {
            background: #7C3AED;
        }
        .download-all {
            background: #10B981;
            font-size: 16px;
            padding: 15px 30px;
            margin: 20px 0;
        }
        h1 { color: #333; text-align: center; }
        h3 { color: #666; margin: 10px 0; }
        .preview { display: flex; justify-content: center; gap: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 TabBar图标生成器</h1>
        <p style="text-align: center; color: #666;">为你的微信小程序生成高质量的TabBar图标</p>
        
        <button class="download-btn download-all" onclick="downloadAll()">
            📦 一键下载所有图标
        </button>
        
        <div class="icon-grid" id="iconGrid"></div>
    </div>

    <script>
        // 图标配置
        const iconConfigs = {
            home: {
                name: '首页',
                normal: { color: '#6B7280', paths: ['M40.5 15L15 35V65H30V50H51V65H66V35L40.5 15Z'] },
                active: { color: '#8B5CF6', paths: ['M40.5 15L15 35V65H30V50H51V65H66V35L40.5 15Z'], fill: true }
            },
            search: {
                name: '搜索',
                normal: { color: '#6B7280', paths: ['M35 55C45.5 55 54 46.5 54 36S45.5 17 35 17S16 25.5 16 36S24.5 55 35 55Z', 'M49 49L65 65'] },
                active: { color: '#8B5CF6', paths: ['M35 55C45.5 55 54 46.5 54 36S45.5 17 35 17S16 25.5 16 36S24.5 55 35 55Z', 'M49 49L65 65'], fill: true }
            },
            category: {
                name: '分类',
                normal: { color: '#6B7280', paths: ['M15 15H35V35H15V15Z', 'M46 15H66V35H46V15Z', 'M15 46H35V66H15V46Z', 'M46 46H66V66H46V46Z'] },
                active: { color: '#8B5CF6', paths: ['M15 15H35V35H15V15Z', 'M46 15H66V35H46V15Z', 'M15 46H35V66H15V46Z', 'M46 46H66V66H46V66Z'], fill: true }
            },
            profile: {
                name: '我的',
                normal: { color: '#6B7280', paths: ['M40.5 35C46.8 35 52 29.8 52 23.5S46.8 12 40.5 12S29 17.2 29 23.5S34.2 35 40.5 35Z', 'M15 65C15 52 26 42 40.5 42S66 52 66 65'] },
                active: { color: '#8B5CF6', paths: ['M40.5 35C46.8 35 52 29.8 52 23.5S46.8 12 40.5 12S29 17.2 29 23.5S34.2 35 40.5 35Z', 'M15 65C15 52 26 42 40.5 42S66 52 66 65'], fill: true }
            }
        };

        // 创建Canvas图标
        function createIcon(config, size = 81) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 设置高质量渲染
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            
            // 设置样式
            ctx.strokeStyle = config.color;
            ctx.fillStyle = config.color;
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 绘制路径
            config.paths.forEach(pathStr => {
                const path = new Path2D(pathStr);
                if (config.fill) {
                    ctx.fill(path);
                }
                ctx.stroke(path);
            });
            
            return canvas;
        }

        // 下载Canvas为PNG
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png', 1.0);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 生成所有图标
        function generateIcons() {
            const grid = document.getElementById('iconGrid');
            
            Object.keys(iconConfigs).forEach(key => {
                const config = iconConfigs[key];
                const normalCanvas = createIcon(config.normal);
                const activeCanvas = createIcon(config.active);
                
                const item = document.createElement('div');
                item.className = 'icon-item';
                item.innerHTML = `
                    <h3>${config.name}</h3>
                    <div class="preview">
                        <div>
                            <div>普通状态</div>
                            ${normalCanvas.outerHTML}
                        </div>
                        <div>
                            <div>选中状态</div>
                            ${activeCanvas.outerHTML}
                        </div>
                    </div>
                    <div>
                        <button class="download-btn" onclick="downloadIcon('${key}', false)">
                            下载普通状态
                        </button>
                        <button class="download-btn" onclick="downloadIcon('${key}', true)">
                            下载选中状态
                        </button>
                    </div>
                `;
                
                grid.appendChild(item);
            });
        }

        // 下载单个图标
        function downloadIcon(key, isActive) {
            const config = iconConfigs[key];
            const canvas = createIcon(isActive ? config.active : config.normal);
            const filename = `${key}${isActive ? '-active' : ''}.png`;
            downloadCanvas(canvas, filename);
        }

        // 一键下载所有图标
        function downloadAll() {
            Object.keys(iconConfigs).forEach(key => {
                setTimeout(() => {
                    downloadIcon(key, false);
                    setTimeout(() => {
                        downloadIcon(key, true);
                    }, 100);
                }, Object.keys(iconConfigs).indexOf(key) * 200);
            });
            
            alert('🎉 开始下载所有图标！请将下载的8个PNG文件放入 images/ 目录下，然后重新编译小程序。');
        }

        // 页面加载完成后生成图标
        window.onload = function() {
            generateIcons();
        };
    </script>
</body>
</html>