// 修复数据库中的数据结构问题
const { chromium } = require('playwright');

async function fixDatabaseStructure() {
    console.log('🔧 修复数据库中的数据结构问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('修复') || text.includes('更新') || text.includes('成功')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：修复分类数据结构');
        
        // 修复分类数据结构
        const categoryFixResult = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                const result = await db.collection('categories').get();
                
                let fixedCount = 0;
                const fixResults = [];
                
                for (const category of result.data) {
                    // 检查是否需要修复（数据被包装在data字段中）
                    if (category.data && !category.name) {
                        console.log(`修复分类 ${category._id}:`, category.data.name);
                        
                        // 提取data字段中的数据到根级别
                        const updateData = {
                            name: category.data.name,
                            icon: category.data.icon || '📁',
                            status: category.data.status || 'show',
                            sort: category.data.sort || 0,
                            count: category.data.count || 0,
                            description: category.data.description || '',
                            gradient: category.data.gradient || '',
                            createTime: category.data.createTime,
                            updateTime: category.data.updateTime || new Date()
                        };
                        
                        // 更新数据库记录
                        const updateResult = await db.collection('categories')
                            .doc(category._id)
                            .update(updateData);
                        
                        if (updateResult.stats.updated > 0) {
                            fixedCount++;
                            fixResults.push({
                                id: category._id,
                                name: category.data.name,
                                success: true
                            });
                            console.log(`✅ 分类 ${category.data.name} 修复成功`);
                        } else {
                            fixResults.push({
                                id: category._id,
                                name: category.data.name,
                                success: false,
                                error: '更新失败'
                            });
                        }
                    } else {
                        fixResults.push({
                            id: category._id,
                            name: category.name || 'Unknown',
                            success: true,
                            skipped: true
                        });
                    }
                }
                
                return {
                    success: true,
                    totalCount: result.data.length,
                    fixedCount: fixedCount,
                    results: fixResults
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 分类数据修复结果:');
        if (categoryFixResult.success) {
            console.log(`总数: ${categoryFixResult.totalCount} 条`);
            console.log(`修复: ${categoryFixResult.fixedCount} 条`);
            
            categoryFixResult.results.forEach((result, index) => {
                if (result.skipped) {
                    console.log(`  ${index + 1}. ${result.name} - ⏭️ 跳过（已正常）`);
                } else if (result.success) {
                    console.log(`  ${index + 1}. ${result.name} - ✅ 修复成功`);
                } else {
                    console.log(`  ${index + 1}. ${result.name} - 🔴 修复失败: ${result.error}`);
                }
            });
        } else {
            console.log(`❌ 分类数据修复失败: ${categoryFixResult.error}`);
        }
        
        console.log('\n📍 第二步：修复表情包数据结构');
        
        // 修复表情包数据结构
        const emojiFixResult = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                const result = await db.collection('emojis').get();
                
                let fixedCount = 0;
                const fixResults = [];
                
                for (const emoji of result.data) {
                    // 检查是否需要修复（数据被包装在data字段中）
                    if (emoji.data && !emoji.title) {
                        console.log(`修复表情包 ${emoji._id}:`, emoji.data.title);
                        
                        // 提取data字段中的数据到根级别
                        const updateData = {
                            title: emoji.data.title,
                            description: emoji.data.description || '',
                            imageUrl: emoji.data.imageUrl,
                            status: emoji.data.status || 'published',
                            categoryId: emoji.data.categoryId || emoji.data.category,
                            likes: emoji.data.likes || 0,
                            collections: emoji.data.collections || 0,
                            downloads: emoji.data.downloads || 0,
                            views: emoji.data.views || 0,
                            tags: emoji.data.tags || [],
                            fileSize: emoji.data.fileSize,
                            fileType: emoji.data.fileType,
                            createTime: emoji.data.createTime,
                            updateTime: emoji.data.updateTime || new Date()
                        };
                        
                        // 更新数据库记录
                        const updateResult = await db.collection('emojis')
                            .doc(emoji._id)
                            .update(updateData);
                        
                        if (updateResult.stats.updated > 0) {
                            fixedCount++;
                            fixResults.push({
                                id: emoji._id,
                                title: emoji.data.title,
                                success: true
                            });
                            console.log(`✅ 表情包 ${emoji.data.title} 修复成功`);
                        } else {
                            fixResults.push({
                                id: emoji._id,
                                title: emoji.data.title,
                                success: false,
                                error: '更新失败'
                            });
                        }
                    } else {
                        fixResults.push({
                            id: emoji._id,
                            title: emoji.title || 'Unknown',
                            success: true,
                            skipped: true
                        });
                    }
                }
                
                return {
                    success: true,
                    totalCount: result.data.length,
                    fixedCount: fixedCount,
                    results: fixResults
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 表情包数据修复结果:');
        if (emojiFixResult.success) {
            console.log(`总数: ${emojiFixResult.totalCount} 条`);
            console.log(`修复: ${emojiFixResult.fixedCount} 条`);
            
            emojiFixResult.results.forEach((result, index) => {
                if (result.skipped) {
                    console.log(`  ${index + 1}. ${result.title} - ⏭️ 跳过（已正常）`);
                } else if (result.success) {
                    console.log(`  ${index + 1}. ${result.title} - ✅ 修复成功`);
                } else {
                    console.log(`  ${index + 1}. ${result.title} - 🔴 修复失败: ${result.error}`);
                }
            });
        } else {
            console.log(`❌ 表情包数据修复失败: ${emojiFixResult.error}`);
        }
        
        console.log('\n📍 第三步：修复横幅数据结构');
        
        // 修复横幅数据结构
        const bannerFixResult = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                const result = await db.collection('banners').get();
                
                let fixedCount = 0;
                const fixResults = [];
                
                for (const banner of result.data) {
                    // 检查是否需要修复（数据被包装在data字段中）
                    if (banner.data && !banner.title) {
                        console.log(`修复横幅 ${banner._id}:`, banner.data.title);
                        
                        // 提取data字段中的数据到根级别
                        const updateData = {
                            title: banner.data.title,
                            description: banner.data.description || '',
                            imageUrl: banner.data.imageUrl,
                            status: banner.data.status || 'show',
                            link: banner.data.link || '',
                            linkType: banner.data.linkType || 'none',
                            priority: banner.data.priority || 0,
                            fileSize: banner.data.fileSize,
                            fileType: banner.data.fileType,
                            createTime: banner.data.createTime,
                            updateTime: banner.data.updateTime || new Date()
                        };
                        
                        // 更新数据库记录
                        const updateResult = await db.collection('banners')
                            .doc(banner._id)
                            .update(updateData);
                        
                        if (updateResult.stats.updated > 0) {
                            fixedCount++;
                            fixResults.push({
                                id: banner._id,
                                title: banner.data.title,
                                success: true
                            });
                            console.log(`✅ 横幅 ${banner.data.title} 修复成功`);
                        } else {
                            fixResults.push({
                                id: banner._id,
                                title: banner.data.title,
                                success: false,
                                error: '更新失败'
                            });
                        }
                    } else {
                        fixResults.push({
                            id: banner._id,
                            title: banner.title || 'Unknown',
                            success: true,
                            skipped: true
                        });
                    }
                }
                
                return {
                    success: true,
                    totalCount: result.data.length,
                    fixedCount: fixedCount,
                    results: fixResults
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 横幅数据修复结果:');
        if (bannerFixResult.success) {
            console.log(`总数: ${bannerFixResult.totalCount} 条`);
            console.log(`修复: ${bannerFixResult.fixedCount} 条`);
            
            bannerFixResult.results.forEach((result, index) => {
                if (result.skipped) {
                    console.log(`  ${index + 1}. ${result.title} - ⏭️ 跳过（已正常）`);
                } else if (result.success) {
                    console.log(`  ${index + 1}. ${result.title} - ✅ 修复成功`);
                } else {
                    console.log(`  ${index + 1}. ${result.title} - 🔴 修复失败: ${result.error}`);
                }
            });
        } else {
            console.log(`❌ 横幅数据修复失败: ${bannerFixResult.error}`);
        }
        
        console.log('\n📍 第四步：验证修复效果');
        
        // 等待一下让数据库更新生效
        await page.waitForTimeout(3000);
        
        // 重新测试云函数调用
        const verifyResult = await page.evaluate(async () => {
            try {
                // 测试分类云函数
                const categoriesResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                // 测试表情包云函数
                const emojisResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 10 }
                    }
                });
                
                // 测试横幅云函数
                const bannersResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                return {
                    success: true,
                    categories: {
                        success: categoriesResult.result?.success || false,
                        count: categoriesResult.result?.data?.length || 0
                    },
                    emojis: {
                        success: emojisResult.result?.success || false,
                        count: emojisResult.result?.data?.length || 0
                    },
                    banners: {
                        success: bannersResult.result?.success || false,
                        count: bannersResult.result?.data?.length || 0
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 修复效果验证:');
        if (verifyResult.success) {
            console.log(`分类云函数: ${verifyResult.categories.success ? '✅ 成功' : '🔴 失败'} (${verifyResult.categories.count}条)`);
            console.log(`表情包云函数: ${verifyResult.emojis.success ? '✅ 成功' : '🔴 失败'} (${verifyResult.emojis.count}条)`);
            console.log(`横幅云函数: ${verifyResult.banners.success ? '✅ 成功' : '🔴 失败'} (${verifyResult.banners.count}条)`);
            
            const allWorking = verifyResult.categories.success && 
                              verifyResult.emojis.success && 
                              verifyResult.banners.success;
            
            console.log(`\n🎯 修复结果: ${allWorking ? '🎉 完全成功！' : '⚠️ 部分成功'}`);
            
            return {
                success: true,
                allFixed: allWorking,
                categoryFix: categoryFixResult,
                emojiFix: emojiFixResult,
                bannerFix: bannerFixResult,
                verification: verifyResult
            };
        } else {
            console.log(`❌ 验证失败: ${verifyResult.error}`);
            return {
                success: false,
                error: verifyResult.error
            };
        }
        
    } catch (error) {
        console.error('❌ 修复过程中出错:', error);
        await page.screenshot({ path: 'fix-database-structure-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'fix-database-structure.png', fullPage: true });
        console.log('\n📸 修复截图已保存: fix-database-structure.png');
        
        console.log('\n⏸️ 修复完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行修复
fixDatabaseStructure().then(result => {
    console.log('\n🎯 数据库结构修复最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.allFixed) {
        console.log('🎉 数据库结构修复完全成功！数据同步问题已解决。');
    } else if (result.success) {
        console.log('⚠️ 数据库结构部分修复成功。');
    } else {
        console.log('❌ 数据库结构修复失败。');
    }
}).catch(console.error);
