// 测试页面 - 可以在小程序中直接访问
Page({
  data: {
    testResults: '等待测试...',
    testStatus: {
      dataAPI: '待测试',
      adminAPI: '待测试',
      dataInit: '待测试'
    }
  },

  onLoad() {
    console.log('🧪 测试页面加载完成');
    this.setData({
      testResults: '测试页面已加载，点击按钮开始测试'
    });
  },

  // 初始化测试数据
  async initTestData() {
    wx.showLoading({
      title: '初始化数据中...'
    });

    this.setData({
      testResults: '🔄 正在初始化测试数据...\n'
    });

    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'initTestData' }
      });

      if (result.result && result.result.success) {
        this.setData({
          testResults: this.data.testResults + '✅ 测试数据初始化成功！\n' + result.result.message + '\n\n'
        });

        wx.showToast({
          title: '初始化成功',
          icon: 'success'
        });

        // 初始化完成后自动运行测试
        setTimeout(() => {
          this.runAllTests();
        }, 1000);
      } else {
        this.setData({
          testResults: this.data.testResults + '❌ 测试数据初始化失败：' + (result.result?.message || '未知错误') + '\n\n'
        });

        wx.showToast({
          title: '初始化失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('初始化测试数据失败:', error);
      this.setData({
        testResults: this.data.testResults + '❌ 测试数据初始化失败：' + error.message + '\n\n'
      });

      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 一键测试
  async runAllTests() {
    this.setData({
      testResults: '🚀 开始测试前后端打通状态...\n'
    });

    await this.testDataAPI();
    await this.testAdminAPI();
    await this.checkDataInit();

    this.generateSummary();
  },

  // 进入管理后台 - 已删除管理页面
  goToAdmin() {
    wx.showToast({
      title: '管理功能已移至Web后台',
      icon: 'none',
      duration: 2000
    })
  },

  // 测试dataAPI
  async testDataAPI() {
    this.addResult('🔧 测试dataAPI云函数...');

    try {
      const res = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getEmojis', data: { page: 1, limit: 5 } }
      });

      if (res.result && res.result.success) {
        this.addResult(`✅ dataAPI测试成功 - 获取到 ${res.result.data.length} 个表情包`);
        this.updateStatus('dataAPI', 'success');
      } else {
        this.addResult('⚠️ dataAPI返回异常 - 可能需要初始化数据');
        this.updateStatus('dataAPI', 'warning');
      }
    } catch (error) {
      this.addResult(`❌ dataAPI测试失败: ${error.message}`);
      this.updateStatus('dataAPI', 'fail');
    }
  },

  // 测试adminAPI
  async testAdminAPI() {
    this.addResult('⚡ 测试adminAPI云函数...');

    try {
      const res = await wx.cloud.callFunction({
        name: 'adminAPI',
        data: { action: 'getStats' }
      });

      if (res.result && res.result.success) {
        this.addResult('✅ adminAPI测试成功 - 管理接口正常');
        this.updateStatus('adminAPI', 'success');
      } else if (res.result && res.result.code === 403) {
        this.addResult('✅ adminAPI权限验证生效 - 需要管理员权限');
        this.updateStatus('adminAPI', 'success');
      } else {
        this.addResult('⚠️ adminAPI返回异常');
        this.updateStatus('adminAPI', 'warning');
      }
    } catch (error) {
      if (error.errMsg && (error.errMsg.includes('权限') || error.errMsg.includes('403'))) {
        this.addResult('✅ adminAPI权限验证生效');
        this.updateStatus('adminAPI', 'success');
      } else {
        this.addResult(`❌ adminAPI测试失败: ${error.message}`);
        this.updateStatus('adminAPI', 'fail');
      }
    }
  },

  // 检查数据初始化
  async checkDataInit() {
    this.addResult('📊 检查数据初始化状态...');

    try {
      const res = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategoryStats' }
      });

      if (res.result && res.result.success && res.result.data.length > 0) {
        this.addResult(`✅ 数据初始化完成 - ${res.result.data.length} 个分类`);
        this.updateStatus('dataInit', 'success');
        
        // 显示分类详情
        res.result.data.forEach(cat => {
          this.addResult(`   - ${cat.name} (${cat.icon}): ${cat.count} 个表情包`);
        });
      } else {
        this.addResult('⚠️ 数据未初始化，正在自动初始化...');
        await this.initTestData();
      }
    } catch (error) {
      this.addResult(`❌ 数据检查失败: ${error.message}`);
      this.updateStatus('dataInit', 'fail');
    }
  },

  // 初始化测试数据
  async initTestData() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'initTestData' }
      });

      if (res.result && res.result.success) {
        this.addResult('✅ 数据初始化成功');
        this.addResult(`   ${res.result.message}`);
        this.updateStatus('dataInit', 'success');
      } else {
        this.addResult('❌ 数据初始化失败');
        this.updateStatus('dataInit', 'fail');
      }
    } catch (error) {
      this.addResult(`❌ 数据初始化失败: ${error.message}`);
      this.updateStatus('dataInit', 'fail');
    }
  },

  // 生成测试总结
  generateSummary() {
    const status = this.data.testStatus;
    const successCount = Object.values(status).filter(s => s === 'success').length;
    const totalCount = Object.keys(status).length;
    const score = Math.round((successCount / totalCount) * 100);

    this.addResult('\n🎯 测试总结:');
    this.addResult('================================');
    this.addResult(`通过率: ${successCount}/${totalCount} (${score}%)`);

    if (score >= 90) {
      this.addResult('🎉 优秀！前后端完全打通，可以上架！');
    } else if (score >= 70) {
      this.addResult('✅ 良好！大部分功能正常');
    } else {
      this.addResult('⚠️ 需要改进！请修复问题后重新测试');
    }
  },

  // 添加测试结果
  addResult(message) {
    const current = this.data.testResults;
    this.setData({
      testResults: current + message + '\n'
    });
  },

  // 更新状态
  updateStatus(key, status) {
    const statusMap = {
      success: '✅ 通过',
      warning: '⚠️ 警告',
      fail: '❌ 失败'
    };

    this.setData({
      [`testStatus.${key}`]: statusMap[status] || status
    });
  }
});
