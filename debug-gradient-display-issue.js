// 深度调试分类列表渐变色预览显示问题
const { chromium } = require('playwright');

async function debugGradientDisplayIssue() {
    console.log('🔍 深度调试分类列表渐变色预览显示问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[BROWSER] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：进入分类管理页面并检查当前显示状态');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(5000);
        
        // 检查分类表格的实际HTML结构
        const tableStructure = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                found: true,
                totalRows: rows.length,
                tableHTML: categoryTable.outerHTML.substring(0, 1000) + '...',
                rows: rows.map((row, index) => {
                    const cells = Array.from(row.querySelectorAll('td'));
                    const nameCell = cells[2]; // 第3列是名称
                    const gradientCell = cells[3]; // 第4列是渐变预览
                    
                    return {
                        index: index,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        gradientCellHTML: gradientCell ? gradientCell.innerHTML : 'N/A',
                        gradientCellText: gradientCell ? gradientCell.textContent.trim() : 'N/A',
                        hasGradientDiv: gradientCell ? !!gradientCell.querySelector('div') : false,
                        gradientDivStyle: gradientCell && gradientCell.querySelector('div') ? 
                            gradientCell.querySelector('div').getAttribute('style') : 'N/A',
                        gradientDivText: gradientCell && gradientCell.querySelector('div') ? 
                            gradientCell.querySelector('div').textContent : 'N/A'
                    };
                })
            };
        });
        
        console.log('📊 分类表格结构检查:');
        if (tableStructure.found) {
            console.log(`表格总行数: ${tableStructure.totalRows}`);
            
            tableStructure.rows.forEach((row) => {
                console.log(`\n行 ${row.index + 1}:`);
                console.log(`  分类名称: ${row.name}`);
                console.log(`  渐变单元格HTML: ${row.gradientCellHTML.substring(0, 200)}...`);
                console.log(`  渐变单元格文本: ${row.gradientCellText}`);
                console.log(`  有渐变div: ${row.hasGradientDiv}`);
                console.log(`  渐变div样式: ${row.gradientDivStyle}`);
                console.log(`  渐变div文本: ${row.gradientDivText}`);
                
                if (row.gradientDivText === '无渐变') {
                    console.log(`  🔴 问题: 显示"无渐变"`);
                } else if (row.gradientDivStyle && row.gradientDivStyle.includes('gradient')) {
                    console.log(`  ✅ 正常: 有渐变样式`);
                } else {
                    console.log(`  ⚠️ 异常: 渐变样式不正确`);
                }
            });
        } else {
            console.log(`❌ 表格检查失败: ${tableStructure.error}`);
        }
        
        console.log('\n📍 第二步：检查AdminApp.data.categories中的数据');
        
        // 检查内存中的分类数据
        const memoryData = await page.evaluate(() => {
            const categories = window.AdminApp && window.AdminApp.data && window.AdminApp.data.categories;
            
            if (!categories) {
                return { error: '未找到AdminApp.data.categories' };
            }
            
            return {
                found: true,
                count: categories.length,
                categories: categories.map(cat => ({
                    _id: cat._id,
                    name: cat.name,
                    gradient: cat.gradient,
                    hasGradient: !!cat.gradient,
                    gradientLength: cat.gradient ? cat.gradient.length : 0,
                    gradientPreview: cat.gradient ? cat.gradient.substring(0, 50) + '...' : 'N/A',
                    allFields: Object.keys(cat)
                }))
            };
        });
        
        console.log('📊 内存数据检查:');
        if (memoryData.found) {
            console.log(`内存中分类数量: ${memoryData.count}`);
            
            memoryData.categories.forEach((cat, index) => {
                console.log(`\n内存分类 ${index + 1}:`);
                console.log(`  ID: ${cat._id}`);
                console.log(`  名称: ${cat.name}`);
                console.log(`  有渐变: ${cat.hasGradient}`);
                console.log(`  渐变长度: ${cat.gradientLength}`);
                console.log(`  渐变预览: ${cat.gradientPreview}`);
                console.log(`  所有字段: [${cat.allFields.join(', ')}]`);
                
                if (!cat.hasGradient) {
                    console.log(`  🔴 问题: 内存数据缺少渐变字段`);
                } else {
                    console.log(`  ✅ 正常: 内存数据有渐变字段`);
                }
            });
        } else {
            console.log(`❌ 内存数据检查失败: ${memoryData.error}`);
        }
        
        console.log('\n📍 第三步：检查renderCategoryTable函数的渐变显示逻辑');
        
        // 检查渲染函数的逻辑
        const renderLogicCheck = await page.evaluate(() => {
            // 获取renderCategoryTable函数的源码
            const renderFunction = window.renderCategoryTable;
            if (!renderFunction) {
                return { error: '未找到renderCategoryTable函数' };
            }
            
            const functionSource = renderFunction.toString();
            
            // 检查渐变预览相关的代码
            const hasGradientLogic = functionSource.includes('gradient');
            const hasGradientPreview = functionSource.includes('gradientPreview');
            const hasBackgroundStyle = functionSource.includes('background:');
            
            return {
                found: true,
                hasGradientLogic: hasGradientLogic,
                hasGradientPreview: hasGradientPreview,
                hasBackgroundStyle: hasBackgroundStyle,
                functionLength: functionSource.length,
                gradientRelatedCode: functionSource.split('\n').filter(line => 
                    line.includes('gradient') || line.includes('background')).join('\n')
            };
        });
        
        console.log('📊 渲染函数逻辑检查:');
        if (renderLogicCheck.found) {
            console.log(`有渐变逻辑: ${renderLogicCheck.hasGradientLogic}`);
            console.log(`有渐变预览: ${renderLogicCheck.hasGradientPreview}`);
            console.log(`有背景样式: ${renderLogicCheck.hasBackgroundStyle}`);
            console.log(`函数长度: ${renderLogicCheck.functionLength} 字符`);
            console.log(`渐变相关代码:`);
            console.log(renderLogicCheck.gradientRelatedCode);
        } else {
            console.log(`❌ 渲染函数检查失败: ${renderLogicCheck.error}`);
        }
        
        console.log('\n📍 第四步：手动调用renderCategoryTable函数测试');
        
        // 手动调用渲染函数进行测试
        const manualRenderTest = await page.evaluate(() => {
            try {
                const categories = window.AdminApp && window.AdminApp.data && window.AdminApp.data.categories;
                if (!categories || categories.length === 0) {
                    return { error: '没有分类数据可供测试' };
                }
                
                // 手动调用渲染函数
                renderCategoryTable(categories);
                
                // 等待一下让DOM更新
                setTimeout(() => {}, 100);
                
                // 再次检查表格
                const categoryTable = document.querySelector('#category-content table');
                if (!categoryTable) return { error: '渲染后未找到表格' };
                
                const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
                return {
                    success: true,
                    rowsAfterRender: rows.length,
                    gradientCells: rows.map((row, index) => {
                        const gradientCell = row.querySelector('td:nth-child(4)');
                        const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                        
                        return {
                            index: index,
                            hasGradientCell: !!gradientCell,
                            hasGradientDiv: !!gradientDiv,
                            gradientDivHTML: gradientDiv ? gradientDiv.outerHTML : 'N/A',
                            gradientDivStyle: gradientDiv ? gradientDiv.getAttribute('style') : 'N/A',
                            gradientDivText: gradientDiv ? gradientDiv.textContent : 'N/A'
                        };
                    })
                };
            } catch (error) {
                return {
                    error: error.message
                };
            }
        });
        
        console.log('📊 手动渲染测试结果:');
        if (manualRenderTest.success) {
            console.log(`渲染后行数: ${manualRenderTest.rowsAfterRender}`);
            
            manualRenderTest.gradientCells.forEach((cell) => {
                console.log(`\n渲染测试行 ${cell.index + 1}:`);
                console.log(`  有渐变单元格: ${cell.hasGradientCell}`);
                console.log(`  有渐变div: ${cell.hasGradientDiv}`);
                console.log(`  渐变div HTML: ${cell.gradientDivHTML.substring(0, 100)}...`);
                console.log(`  渐变div样式: ${cell.gradientDivStyle}`);
                console.log(`  渐变div文本: ${cell.gradientDivText}`);
                
                if (cell.gradientDivText === '无渐变') {
                    console.log(`  🔴 问题确认: 渲染后仍显示"无渐变"`);
                } else if (cell.gradientDivStyle && cell.gradientDivStyle.includes('gradient')) {
                    console.log(`  ✅ 正常: 渲染后有渐变样式`);
                } else {
                    console.log(`  ⚠️ 异常: 渲染后渐变样式不正确`);
                }
            });
        } else {
            console.log(`❌ 手动渲染测试失败: ${manualRenderTest.error}`);
        }
        
        console.log('\n📍 第五步：问题根源分析和解决方案');
        
        // 分析问题根源
        let problemAnalysis = [];
        let solutionNeeded = false;
        
        if (memoryData.found) {
            const categoriesWithGradient = memoryData.categories.filter(cat => cat.hasGradient).length;
            if (categoriesWithGradient === 0) {
                problemAnalysis.push('🔴 根本问题: 内存数据中没有渐变字段');
                solutionNeeded = true;
            } else if (categoriesWithGradient < memoryData.count) {
                problemAnalysis.push('⚠️ 部分问题: 部分内存数据缺少渐变字段');
                solutionNeeded = true;
            } else {
                problemAnalysis.push('✅ 内存数据: 所有分类都有渐变字段');
            }
        }
        
        if (tableStructure.found) {
            const rowsWithGradient = tableStructure.rows.filter(row => 
                row.gradientDivStyle && row.gradientDivStyle.includes('gradient')).length;
            if (rowsWithGradient === 0) {
                problemAnalysis.push('🔴 显示问题: 表格中没有渐变样式');
                solutionNeeded = true;
            } else if (rowsWithGradient < tableStructure.totalRows) {
                problemAnalysis.push('⚠️ 显示问题: 部分表格行缺少渐变样式');
                solutionNeeded = true;
            } else {
                problemAnalysis.push('✅ 显示正常: 所有表格行都有渐变样式');
            }
        }
        
        console.log('🔍 问题根源分析:');
        problemAnalysis.forEach(analysis => {
            console.log(`  ${analysis}`);
        });
        
        if (solutionNeeded) {
            console.log('\n🔧 需要修复的问题:');
            console.log('1. 检查loadCategories函数是否正确加载渐变数据');
            console.log('2. 检查renderCategoryTable函数是否正确显示渐变');
            console.log('3. 可能需要强制刷新分类数据');
        } else {
            console.log('\n✅ 渐变显示功能正常工作');
        }
        
        return {
            success: true,
            problemFound: solutionNeeded,
            tableStructure: tableStructure,
            memoryData: memoryData,
            renderLogicCheck: renderLogicCheck,
            manualRenderTest: manualRenderTest,
            problemAnalysis: problemAnalysis
        };
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        await page.screenshot({ path: 'debug-gradient-display-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'debug-gradient-display-issue.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-gradient-display-issue.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行调试
debugGradientDisplayIssue().then(result => {
    console.log('\n🎯 渐变显示问题调试结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.problemFound) {
        console.log('🔴 确认存在问题，需要进一步修复。');
    } else if (result.success) {
        console.log('✅ 渐变显示功能正常工作。');
    } else {
        console.log('❌ 调试失败，无法确定问题状态。');
    }
}).catch(console.error);
