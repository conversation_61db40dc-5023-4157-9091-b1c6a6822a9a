<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试虚拟数据删除</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 测试虚拟数据删除</h1>
        
        <div style="text-align: center;">
            <button onclick="testSystemLogs()">测试系统日志</button>
            <button onclick="testStatistics()">测试统计数据</button>
            <button onclick="testFileDownload()">测试文件下载</button>
            <button onclick="testAllFeatures()">测试所有功能</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;
        let testResults = [];

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            testResults = [];
        }

        function addTestResult(testName, passed, message) {
            testResults.push({ name: testName, passed, message });
            const resultClass = passed ? 'test-pass' : 'test-fail';
            const resultIcon = passed ? '✅' : '❌';
            log(`${resultIcon} ${testName}: ${message}`, passed ? 'success' : 'error');
        }

        // 初始化CloudBase
        async function initCloudBase() {
            try {
                if (tcbApp) return tcbApp;
                
                log('🚀 初始化CloudBase SDK...');
                tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                const auth = tcbApp.auth();
                await auth.signInAnonymously();
                log('✅ CloudBase初始化成功', 'success');
                return tcbApp;
            } catch (error) {
                log('❌ CloudBase初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        // 测试系统日志功能
        async function testSystemLogs() {
            log('🧪 开始测试系统日志功能...');
            
            try {
                await initCloudBase();
                
                // 测试1: 检查是否还有模拟日志
                log('📋 测试1: 检查代码中是否还有模拟日志');
                const response = await fetch('/main.html');
                const htmlContent = await response.text();
                
                const hasMockLogs = htmlContent.includes('mockLogs') || htmlContent.includes('模拟系统日志');
                addTestResult('模拟日志检查', !hasMockLogs, hasMockLogs ? '代码中仍包含模拟日志' : '代码中已删除模拟日志');
                
                // 测试2: 尝试查询真实日志
                log('📋 测试2: 查询真实系统日志');
                const db = tcbApp.database();
                const logResult = await db.collection('admin_logs').limit(5).get();
                
                addTestResult('真实日志查询', true, `成功查询到 ${logResult.data.length} 条日志记录`);
                
                if (logResult.data.length > 0) {
                    log('📊 最新日志记录示例:');
                    logResult.data.slice(0, 2).forEach((logItem, index) => {
                        log(`  ${index + 1}. [${logItem.level}] ${logItem.message} (${new Date(logItem.createTime).toLocaleString()})`);
                    });
                }
                
            } catch (error) {
                addTestResult('系统日志测试', false, `测试失败: ${error.message}`);
            }
        }

        // 测试统计数据功能
        async function testStatistics() {
            log('🧪 开始测试统计数据功能...');
            
            try {
                await initCloudBase();
                
                // 测试1: 检查是否还有随机数生成
                log('📋 测试1: 检查代码中是否还有随机数生成');
                const response = await fetch('/main.html');
                const htmlContent = await response.text();
                
                const hasRandomData = htmlContent.includes('Math.floor(Math.random()') && 
                                    htmlContent.includes('模拟统计数据');
                addTestResult('随机数据检查', !hasRandomData, hasRandomData ? '代码中仍包含随机数据生成' : '代码中已删除随机数据生成');
                
                // 测试2: 查询真实统计数据
                log('📋 测试2: 查询真实统计数据');
                const db = tcbApp.database();
                
                const categoryCount = await db.collection('categories').count();
                const emojiCount = await db.collection('emojis').count();
                const bannerCount = await db.collection('banners').count();
                
                addTestResult('真实统计查询', true, `分类: ${categoryCount.total}, 表情包: ${emojiCount.total}, 横幅: ${bannerCount.total}`);
                
            } catch (error) {
                addTestResult('统计数据测试', false, `测试失败: ${error.message}`);
            }
        }

        // 测试文件下载功能
        async function testFileDownload() {
            log('🧪 开始测试文件下载功能...');
            
            try {
                await initCloudBase();
                
                // 测试1: 检查是否还有模拟下载链接
                log('📋 测试1: 检查代码中是否还有模拟下载链接');
                const response = await fetch('/main.html');
                const htmlContent = await response.text();
                
                const hasMockDownload = htmlContent.includes('mock-cdn.com') || 
                                      htmlContent.includes('模拟获取下载链接');
                addTestResult('模拟下载检查', !hasMockDownload, hasMockDownload ? '代码中仍包含模拟下载链接' : '代码中已删除模拟下载链接');
                
                // 测试2: 测试真实文件下载API
                log('📋 测试2: 测试真实文件下载API');
                const storage = tcbApp.storage();
                
                // 尝试获取一个测试文件的下载链接（如果存在的话）
                try {
                    const testFileID = 'cloud://cloud1-5g6pvnpl88dc0142.636c-cloud1-5g6pvnpl88dc0142-1330048123/test.jpg';
                    const downloadURL = await storage.getDownloadURL(testFileID);
                    addTestResult('真实下载API', true, '文件下载API工作正常');
                } catch (error) {
                    if (error.message.includes('not found')) {
                        addTestResult('真实下载API', true, '文件下载API工作正常（测试文件不存在是正常的）');
                    } else {
                        addTestResult('真实下载API', false, `文件下载API错误: ${error.message}`);
                    }
                }
                
            } catch (error) {
                addTestResult('文件下载测试', false, `测试失败: ${error.message}`);
            }
        }

        // 测试所有功能
        async function testAllFeatures() {
            log('🚀 开始完整功能测试...');
            
            await testSystemLogs();
            await testStatistics();
            await testFileDownload();
            
            // 汇总结果
            log('📊 测试结果汇总:', 'info');
            const passedTests = testResults.filter(t => t.passed).length;
            const totalTests = testResults.length;
            
            if (passedTests === totalTests) {
                log(`🎉 所有测试通过! (${passedTests}/${totalTests})`, 'success');
                log('✅ 虚拟数据删除成功，所有功能已替换为真实数据源', 'success');
            } else {
                log(`⚠️ 部分测试失败: ${passedTests}/${totalTests} 通过`, 'warning');
                
                const failedTests = testResults.filter(t => !t.passed);
                if (failedTests.length > 0) {
                    log('❌ 失败的测试:', 'error');
                    failedTests.forEach(test => {
                        log(`  - ${test.name}: ${test.message}`, 'error');
                    });
                }
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，准备测试虚拟数据删除效果');
            log('💡 这个工具将验证所有虚拟数据是否已被真实数据替换');
        });
    </script>
</body>
</html>
