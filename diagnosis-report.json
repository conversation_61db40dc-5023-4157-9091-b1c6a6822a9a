{"timestamp": "2025-07-23T11:31:20.907Z", "results": {"fileAnalysis": {"admin": {"exists": true, "hasCloudbaseSDK": true, "sdkUrls": [], "environments": ["cloud1-5g6pvnpl88dc0142"], "hasDatabaseAdd": true, "hasCloudAPICall": true, "usesCategoryField": false, "usesCategoryIdField": true, "fileSize": 439925}, "cloudFunction": {"exists": true, "hasOrQuery": true, "hasCategoryCompatibility": true, "hasFixedGetEmojis": true, "hasGetCategories": true, "hasGetEmojis": true, "hasSearchEmojis": true, "fileSize": 33603}, "miniProgram": {"index": {"exists": true, "hasDataAPICall": true, "hasGetCategories": true, "hasGetEmojis": true}, "category": {"exists": true, "hasDataAPICall": true, "hasLoadData": false}}}, "configAnalysis": {"projectConfig": {"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": false, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "lazyCodeLoading": "requiredComponents"}, "compileType": "miniprogram", "libVersion": "3.8.11", "appid": "wxa343fb2b31f727a4", "projectname": "表情包小程序", "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "simulatorPluginLibVersion": {}, "editorSetting": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloudfunctionRoot": "cloudfunctions/", "cloudenv": "cloud1-5g6pvnpl88dc0142"}, "appConfig": {"pages": ["pages/index/index", "pages/search/search", "pages/category/category", "pages/profile/profile", "pages/detail/detail", "pages/my-likes/my-likes", "pages/my-collections/my-collections", "pages/collection/collection", "pages/category-detail/category-detail", "pages/download-history/download-history", "pages/settings/settings", "pages/test/test", "pages/admin/admin", "pages/debug/debug", "pages/offline-test/offline-test", "pages/fix-sync/fix-sync"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "表情包", "navigationBarTextStyle": "black"}, "tabBar": {"custom": true, "color": "#8A8A8A", "selectedColor": "#FF8C00", "backgroundColor": "#FFFFFF", "borderStyle": "white", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/search/search", "text": "搜索"}, {"pagePath": "pages/category/category", "text": "分类"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "style": "v2", "sitemapLocation": "sitemap.json"}, "hasCloudFunction": true, "cloudFunctionRoot": "cloudfunctions/"}, "codeAnalysis": {}, "recommendations": [], "diagnosis": {"issues": [], "recommendations": [], "rootCause": "未知问题", "severity": "low"}}, "summary": {"totalIssues": 0, "severity": "low", "rootCause": "未知问题"}}