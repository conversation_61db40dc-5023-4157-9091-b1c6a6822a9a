<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #007bff; height: 100%; border-radius: 4px; transition: width 0.3s; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🚀 数据库初始化工具</h1>
    
    <div class="panel">
        <h2>📋 初始化说明</h2>
        <div class="status info">
            <strong>目的：</strong>为表情包管理系统创建必要的测试数据<br>
            <strong>环境：</strong>cloud1-5g6pvnpl88dc0142<br>
            <strong>注意：</strong>请确保已在云开发控制台启用匿名登录的访问令牌
        </div>
    </div>

    <div class="panel">
        <h2>🎯 初始化操作</h2>
        <button onclick="initializeData()" id="initBtn">开始初始化数据</button>
        <button onclick="clearAllData()" id="clearBtn" style="background: #dc3545;">清空所有数据</button>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        
        <div id="initStatus"></div>
    </div>

    <div class="panel">
        <h2>📊 数据预览</h2>
        <button onclick="previewData()">查看当前数据</button>
        <div id="dataPreview"></div>
    </div>

    <div class="panel">
        <h2>📝 操作日志</h2>
        <div id="logContainer"></div>
    </div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/1.8.0/cloudbase.full.js"></script>
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let tcbApp = null;
        let db = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateProgress(percent, text) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const statusDiv = document.getElementById('initStatus');
            
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
            statusDiv.innerHTML = `<div class="status info">${text} (${percent}%)</div>`;
        }

        async function initSDK() {
            if (tcbApp) return true;
            
            log('初始化云开发SDK...', 'info');
            
            if (typeof window.cloudbase === 'undefined') {
                throw new Error('CloudBase SDK未加载');
            }
            
            tcbApp = window.cloudbase.init({
                env: ENV_ID
            });
            
            db = tcbApp.database();
            log('✅ SDK初始化成功', 'success');
            return true;
        }

        async function authenticateUser() {
            log('执行身份认证...', 'info');
            const auth = tcbApp.auth();
            
            try {
                const loginState = await auth.getLoginState();
                if (!loginState) {
                    await auth.anonymousAuthProvider().signIn();
                    log('✅ 匿名登录成功', 'success');
                } else {
                    log('✅ 已登录状态', 'success');
                }
                return true;
            } catch (error) {
                log(`❌ 身份认证失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 测试数据定义
        const testData = {
            categories: [
                { id: 'funny', name: '搞笑', icon: '😂', sort: 1, status: 'active' },
                { id: 'cute', name: '可爱', icon: '🥰', sort: 2, status: 'active' },
                { id: 'angry', name: '愤怒', icon: '😠', sort: 3, status: 'active' },
                { id: 'sad', name: '伤心', icon: '😢', sort: 4, status: 'active' },
                { id: 'surprise', name: '惊讶', icon: '😱', sort: 5, status: 'active' }
            ],
            emojis: [
                { 
                    name: '哈哈大笑', 
                    category: 'funny', 
                    tags: ['搞笑', '开心'], 
                    url: 'https://example.com/emoji1.gif',
                    status: 'active',
                    downloads: 0,
                    likes: 0
                },
                { 
                    name: '可爱猫咪', 
                    category: 'cute', 
                    tags: ['可爱', '猫咪'], 
                    url: 'https://example.com/emoji2.gif',
                    status: 'active',
                    downloads: 0,
                    likes: 0
                },
                { 
                    name: '生气脸', 
                    category: 'angry', 
                    tags: ['愤怒', '生气'], 
                    url: 'https://example.com/emoji3.gif',
                    status: 'active',
                    downloads: 0,
                    likes: 0
                }
            ],
            banners: [
                {
                    title: '欢迎使用表情包管理系统',
                    image: 'https://example.com/banner1.jpg',
                    link: '',
                    status: 'active',
                    sort: 1
                }
            ]
        };

        async function initializeData() {
            const initBtn = document.getElementById('initBtn');
            const clearBtn = document.getElementById('clearBtn');
            
            try {
                initBtn.disabled = true;
                clearBtn.disabled = true;
                
                updateProgress(10, '初始化SDK...');
                await initSDK();
                
                updateProgress(20, '执行身份认证...');
                await authenticateUser();
                
                updateProgress(30, '开始创建数据...');
                
                // 初始化分类数据
                updateProgress(40, '创建分类数据...');
                for (const category of testData.categories) {
                    try {
                        await db.collection('categories').add(category);
                        log(`✅ 创建分类: ${category.name}`, 'success');
                    } catch (error) {
                        if (error.message.includes('duplicate')) {
                            log(`⚠️ 分类已存在: ${category.name}`, 'warning');
                        } else {
                            throw error;
                        }
                    }
                }
                
                updateProgress(60, '创建表情包数据...');
                for (const emoji of testData.emojis) {
                    try {
                        await db.collection('emojis').add(emoji);
                        log(`✅ 创建表情包: ${emoji.name}`, 'success');
                    } catch (error) {
                        if (error.message.includes('duplicate')) {
                            log(`⚠️ 表情包已存在: ${emoji.name}`, 'warning');
                        } else {
                            throw error;
                        }
                    }
                }
                
                updateProgress(80, '创建轮播图数据...');
                for (const banner of testData.banners) {
                    try {
                        await db.collection('banners').add(banner);
                        log(`✅ 创建轮播图: ${banner.title}`, 'success');
                    } catch (error) {
                        if (error.message.includes('duplicate')) {
                            log(`⚠️ 轮播图已存在: ${banner.title}`, 'warning');
                        } else {
                            throw error;
                        }
                    }
                }
                
                updateProgress(100, '初始化完成！');
                
                document.getElementById('initStatus').innerHTML = `
                    <div class="status success">
                        ✅ 数据初始化完成！<br>
                        - 创建了 ${testData.categories.length} 个分类<br>
                        - 创建了 ${testData.emojis.length} 个表情包<br>
                        - 创建了 ${testData.banners.length} 个轮播图
                    </div>
                `;
                
                log('🎉 数据初始化完成！', 'success');
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
                document.getElementById('initStatus').innerHTML = `
                    <div class="status error">
                        ❌ 初始化失败: ${error.message}
                    </div>
                `;
            } finally {
                initBtn.disabled = false;
                clearBtn.disabled = false;
            }
        }

        async function previewData() {
            try {
                await initSDK();
                await authenticateUser();
                
                const collections = ['categories', 'emojis', 'banners'];
                let preview = '<h3>📊 当前数据统计</h3>';
                
                for (const collectionName of collections) {
                    try {
                        const result = await db.collection(collectionName).get();
                        preview += `<div class="status info"><strong>${collectionName}:</strong> ${result.data.length} 条记录</div>`;
                        
                        if (result.data.length > 0) {
                            preview += `<pre>${JSON.stringify(result.data[0], null, 2)}</pre>`;
                        }
                    } catch (error) {
                        preview += `<div class="status error"><strong>${collectionName}:</strong> 查询失败 - ${error.message}</div>`;
                    }
                }
                
                document.getElementById('dataPreview').innerHTML = preview;
                
            } catch (error) {
                document.getElementById('dataPreview').innerHTML = `
                    <div class="status error">预览失败: ${error.message}</div>
                `;
            }
        }

        async function clearAllData() {
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                await initSDK();
                await authenticateUser();
                
                const collections = ['categories', 'emojis', 'banners'];
                
                for (const collectionName of collections) {
                    try {
                        const result = await db.collection(collectionName).get();
                        for (const doc of result.data) {
                            await db.collection(collectionName).doc(doc._id).remove();
                        }
                        log(`✅ 清空集合: ${collectionName}`, 'success');
                    } catch (error) {
                        log(`❌ 清空失败 ${collectionName}: ${error.message}`, 'error');
                    }
                }
                
                log('🧹 数据清空完成', 'success');
                
            } catch (error) {
                log(`❌ 清空失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('数据初始化工具已加载', 'info');
            log('请先确保在云开发控制台启用了匿名登录的访问令牌', 'warning');
        });
    </script>
</body>
</html>
