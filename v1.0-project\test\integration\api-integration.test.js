// API集成测试
const { describe, test, expect, beforeAll, afterAll, beforeEach } = require('@jest/globals');
const supertest = require('supertest');

// 模拟云函数调用环境
class CloudFunctionSimulator {
  constructor() {
    this.functions = new Map();
    this.database = new Map();
    this.initMockData();
  }

  // 初始化模拟数据
  initMockData() {
    this.database.set('categories', [
      {
        _id: 'cat_id_001',
        id: 'cat_001',
        name: '搞笑',
        icon: 'https://example.com/icon1.png',
        status: 'active',
        sort: 1,
        createTime: new Date()
      }
    ]);

    this.database.set('emojis', [
      {
        _id: 'emoji_id_001',
        id: 'emoji_001',
        title: '哈哈',
        imageUrl: 'https://example.com/emoji1.png',
        categoryId: 'cat_001',
        status: 'published',
        likes: 100,
        downloads: 500,
        createTime: new Date()
      }
    ]);

    this.database.set('banners', [
      {
        _id: 'banner_id_001',
        id: 'banner_001',
        title: '活动横幅',
        imageUrl: 'https://example.com/banner1.png',
        linkUrl: 'https://example.com/activity',
        status: 'active',
        sort: 1,
        createTime: new Date()
      }
    ]);

    this.database.set('sync_notifications', []);
    this.database.set('admin_logs', []);
  }

  // 注册云函数
  registerFunction(name, handler) {
    this.functions.set(name, handler);
  }

  // 调用云函数
  async callFunction(name, event, context = {}) {
    const handler = this.functions.get(name);
    if (!handler) {
      throw new Error(`Function ${name} not found`);
    }

    // 模拟云函数环境
    const mockContext = {
      ...context,
      clientIP: '127.0.0.1',
      requestId: 'test-request-id'
    };

    return await handler.main(event, mockContext);
  }

  // 获取数据库集合
  getCollection(name) {
    return this.database.get(name) || [];
  }

  // 添加数据到集合
  addToCollection(name, data) {
    const collection = this.database.get(name) || [];
    const newItem = {
      ...data,
      _id: `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    collection.push(newItem);
    this.database.set(name, collection);
    return newItem;
  }

  // 更新集合中的数据
  updateInCollection(name, id, updates) {
    const collection = this.database.get(name) || [];
    const index = collection.findIndex(item => item.id === id || item._id === id);
    if (index !== -1) {
      collection[index] = { ...collection[index], ...updates };
      this.database.set(name, collection);
      return collection[index];
    }
    return null;
  }

  // 从集合中删除数据
  removeFromCollection(name, id) {
    const collection = this.database.get(name) || [];
    const index = collection.findIndex(item => item.id === id || item._id === id);
    if (index !== -1) {
      const removed = collection.splice(index, 1)[0];
      this.database.set(name, collection);
      return removed;
    }
    return null;
  }
}

describe('API集成测试', () => {
  let simulator;
  let adminToken;

  beforeAll(async () => {
    // 初始化云函数模拟器
    simulator = new CloudFunctionSimulator();

    // 注册云函数（这里需要实际的云函数代码）
    // 由于无法直接导入云函数，我们模拟其行为
    simulator.registerFunction('loginAPI', {
      main: async (event, context) => {
        if (event.action === 'login') {
          if (event.username === 'admin' && event.password === 'admin123456') {
            return {
              success: true,
              data: {
                token: 'mock.jwt.token.integration',
                adminId: 'admin',
                expiresIn: '24h',
                permissions: ['read', 'write', 'delete']
              },
              message: '登录成功'
            };
          } else {
            return {
              success: false,
              error: '用户名或密码错误',
              code: 'INVALID_CREDENTIALS'
            };
          }
        }
        return { success: false, error: '未知操作', code: 'UNKNOWN_ACTION' };
      }
    });

    simulator.registerFunction('webAdminAPI', {
      main: async (event, context) => {
        // 简化的权限验证
        if (!event.token || event.token !== 'mock.jwt.token.integration') {
          return { success: false, error: '令牌无效', code: 'INVALID_TOKEN' };
        }

        switch (event.action) {
          case 'createCategory':
            if (!event.name) {
              return { success: false, error: '分类名称不能为空', code: 'INVALID_DATA' };
            }
            const newCategory = simulator.addToCollection('categories', {
              id: `cat_${Date.now()}`,
              name: event.name,
              icon: event.icon || '',
              description: event.description || '',
              status: 'active',
              sort: event.sort || 0,
              createTime: new Date()
            });
            
            // 添加同步通知
            simulator.addToCollection('sync_notifications', {
              dataType: 'categories',
              operation: 'create',
              dataId: newCategory.id,
              timestamp: new Date()
            });

            return {
              success: true,
              data: { id: newCategory.id, _id: newCategory._id },
              message: '分类创建成功'
            };

          case 'updateCategory':
            if (!event.id) {
              return { success: false, error: '分类ID不能为空', code: 'INVALID_DATA' };
            }
            const updated = simulator.updateInCollection('categories', event.id, {
              ...event.updates,
              updateTime: new Date()
            });
            if (!updated) {
              return { success: false, error: '分类不存在', code: 'CATEGORY_NOT_FOUND' };
            }

            // 添加同步通知
            simulator.addToCollection('sync_notifications', {
              dataType: 'categories',
              operation: 'update',
              dataId: event.id,
              timestamp: new Date()
            });

            return { success: true, message: '分类更新成功' };

          case 'deleteCategory':
            const deleted = simulator.updateInCollection('categories', event.id, {
              status: 'deleted',
              deleteTime: new Date()
            });
            if (!deleted) {
              return { success: false, error: '分类不存在', code: 'CATEGORY_NOT_FOUND' };
            }

            // 添加同步通知
            simulator.addToCollection('sync_notifications', {
              dataType: 'categories',
              operation: 'delete',
              dataId: event.id,
              timestamp: new Date()
            });

            return { success: true, message: '分类删除成功' };

          default:
            return { success: false, error: '未知操作', code: 'UNKNOWN_ACTION' };
        }
      }
    });

    simulator.registerFunction('dataAPI', {
      main: async (event, context) => {
        switch (event.action) {
          case 'getCategories':
            const categories = simulator.getCollection('categories')
              .filter(cat => cat.status === 'active')
              .map(cat => ({
                id: cat.id,
                name: cat.name,
                icon: cat.icon,
                description: cat.description,
                sort: cat.sort,
                emojiCount: 0
              }));
            return {
              success: true,
              data: categories,
              fromCache: false,
              timestamp: Date.now()
            };

          case 'getEmojis':
            const { page = 1, pageSize = 20 } = event.params || {};
            const emojis = simulator.getCollection('emojis')
              .filter(emoji => emoji.status === 'published');
            
            const start = (page - 1) * pageSize;
            const paginatedEmojis = emojis.slice(start, start + pageSize);

            return {
              success: true,
              data: {
                list: paginatedEmojis.map(emoji => ({
                  id: emoji.id,
                  title: emoji.title,
                  imageUrl: emoji.imageUrl,
                  categoryId: emoji.categoryId,
                  likes: emoji.likes,
                  downloads: emoji.downloads
                })),
                pagination: {
                  page,
                  pageSize,
                  total: emojis.length,
                  totalPages: Math.ceil(emojis.length / pageSize)
                }
              }
            };

          case 'getBanners':
            const banners = simulator.getCollection('banners')
              .filter(banner => banner.status === 'active')
              .map(banner => ({
                id: banner.id,
                title: banner.title,
                imageUrl: banner.imageUrl,
                linkUrl: banner.linkUrl,
                sort: banner.sort
              }));
            return {
              success: true,
              data: banners,
              fromCache: false,
              timestamp: Date.now()
            };

          default:
            return { success: false, error: '未知操作', code: 'UNKNOWN_ACTION' };
        }
      }
    });
  });

  beforeEach(async () => {
    // 每个测试前重新登录获取令牌
    const loginResult = await simulator.callFunction('loginAPI', {
      action: 'login',
      username: 'admin',
      password: 'admin123456'
    });
    
    expect(loginResult.success).toBe(true);
    adminToken = loginResult.data.token;
  });

  describe('T2.1 完整的分类管理流程测试', () => {
    test('T2.1.1 创建-查询-更新-删除分类的完整流程', async () => {
      // 1. 创建分类
      const createResult = await simulator.callFunction('webAdminAPI', {
        action: 'createCategory',
        token: adminToken,
        name: '集成测试分类',
        icon: 'https://example.com/test-icon.png',
        description: '这是一个集成测试分类'
      });

      expect(createResult.success).toBe(true);
      expect(createResult.data).toHaveProperty('id');
      const categoryId = createResult.data.id;

      // 2. 查询分类列表，验证创建成功
      const getResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });

      expect(getResult.success).toBe(true);
      const createdCategory = getResult.data.find(cat => cat.id === categoryId);
      expect(createdCategory).toBeDefined();
      expect(createdCategory.name).toBe('集成测试分类');

      // 3. 更新分类
      const updateResult = await simulator.callFunction('webAdminAPI', {
        action: 'updateCategory',
        token: adminToken,
        id: categoryId,
        updates: {
          name: '更新后的集成测试分类',
          description: '这是更新后的描述'
        }
      });

      expect(updateResult.success).toBe(true);

      // 4. 再次查询验证更新成功
      const getUpdatedResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });

      const updatedCategory = getUpdatedResult.data.find(cat => cat.id === categoryId);
      expect(updatedCategory.name).toBe('更新后的集成测试分类');

      // 5. 删除分类
      const deleteResult = await simulator.callFunction('webAdminAPI', {
        action: 'deleteCategory',
        token: adminToken,
        id: categoryId
      });

      expect(deleteResult.success).toBe(true);

      // 6. 验证删除后查询不到该分类
      const getFinalResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });

      const deletedCategory = getFinalResult.data.find(cat => cat.id === categoryId);
      expect(deletedCategory).toBeUndefined();
    });

    test('T2.1.2 验证同步通知在整个流程中正确创建', async () => {
      const initialNotifications = simulator.getCollection('sync_notifications').length;

      // 创建分类
      const createResult = await simulator.callFunction('webAdminAPI', {
        action: 'createCategory',
        token: adminToken,
        name: '通知测试分类',
        icon: 'https://example.com/test-icon.png'
      });

      expect(createResult.success).toBe(true);
      const categoryId = createResult.data.id;

      // 更新分类
      await simulator.callFunction('webAdminAPI', {
        action: 'updateCategory',
        token: adminToken,
        id: categoryId,
        updates: { name: '更新后的通知测试分类' }
      });

      // 删除分类
      await simulator.callFunction('webAdminAPI', {
        action: 'deleteCategory',
        token: adminToken,
        id: categoryId
      });

      // 验证同步通知数量增加了3个（创建、更新、删除）
      const finalNotifications = simulator.getCollection('sync_notifications');
      expect(finalNotifications.length).toBe(initialNotifications + 3);

      // 验证通知内容
      const categoryNotifications = finalNotifications.filter(
        notif => notif.dataType === 'categories' && notif.dataId === categoryId
      );
      expect(categoryNotifications).toHaveLength(3);

      const operations = categoryNotifications.map(notif => notif.operation);
      expect(operations).toContain('create');
      expect(operations).toContain('update');
      expect(operations).toContain('delete');
    });
  });

  describe('T2.2 跨云函数数据一致性测试', () => {
    test('T2.2.1 管理后台操作与数据查询的一致性', async () => {
      // 通过管理后台创建数据
      const createResult = await simulator.callFunction('webAdminAPI', {
        action: 'createCategory',
        token: adminToken,
        name: '一致性测试分类',
        icon: 'https://example.com/consistency-icon.png'
      });

      expect(createResult.success).toBe(true);
      const categoryId = createResult.data.id;

      // 立即通过数据API查询
      const queryResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });

      expect(queryResult.success).toBe(true);
      const foundCategory = queryResult.data.find(cat => cat.id === categoryId);
      expect(foundCategory).toBeDefined();
      expect(foundCategory.name).toBe('一致性测试分类');
    });

    test('T2.2.2 并发操作的数据一致性', async () => {
      // 模拟并发创建多个分类
      const concurrentCreates = Array.from({ length: 5 }, (_, i) =>
        simulator.callFunction('webAdminAPI', {
          action: 'createCategory',
          token: adminToken,
          name: `并发测试分类${i + 1}`,
          icon: `https://example.com/concurrent-icon${i + 1}.png`
        })
      );

      const results = await Promise.all(concurrentCreates);

      // 验证所有创建都成功
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // 验证数据查询能找到所有创建的分类
      const queryResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });

      const createdIds = results.map(result => result.data.id);
      const foundCategories = queryResult.data.filter(cat => 
        createdIds.includes(cat.id)
      );

      expect(foundCategories).toHaveLength(5);
    });
  });

  describe('T2.3 错误处理和恢复测试', () => {
    test('T2.3.1 无效令牌的错误传播', async () => {
      const result = await simulator.callFunction('webAdminAPI', {
        action: 'createCategory',
        token: 'invalid.token.here',
        name: '测试分类'
      });

      expect(result.success).toBe(false);
      expect(result.code).toBe('INVALID_TOKEN');
    });

    test('T2.3.2 数据验证错误的正确处理', async () => {
      const result = await simulator.callFunction('webAdminAPI', {
        action: 'createCategory',
        token: adminToken,
        name: '', // 空名称应该失败
        icon: 'https://example.com/icon.png'
      });

      expect(result.success).toBe(false);
      expect(result.code).toBe('INVALID_DATA');
    });

    test('T2.3.3 不存在资源的错误处理', async () => {
      const result = await simulator.callFunction('webAdminAPI', {
        action: 'updateCategory',
        token: adminToken,
        id: 'nonexistent_category_id',
        updates: { name: '新名称' }
      });

      expect(result.success).toBe(false);
      expect(result.code).toBe('CATEGORY_NOT_FOUND');
    });
  });

  describe('T2.4 性能和负载测试', () => {
    test('T2.4.1 大量数据查询性能测试', async () => {
      // 创建大量测试数据
      const createPromises = Array.from({ length: 50 }, (_, i) =>
        simulator.callFunction('webAdminAPI', {
          action: 'createCategory',
          token: adminToken,
          name: `性能测试分类${i + 1}`,
          icon: `https://example.com/perf-icon${i + 1}.png`
        })
      );

      await Promise.all(createPromises);

      // 测试查询性能
      const startTime = Date.now();
      const queryResult = await simulator.callFunction('dataAPI', {
        action: 'getCategories'
      });
      const duration = Date.now() - startTime;

      expect(queryResult.success).toBe(true);
      expect(queryResult.data.length).toBeGreaterThanOrEqual(50);
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    test('T2.4.2 分页查询功能测试', async () => {
      // 测试表情包分页查询
      const page1Result = await simulator.callFunction('dataAPI', {
        action: 'getEmojis',
        params: { page: 1, pageSize: 10 }
      });

      expect(page1Result.success).toBe(true);
      expect(page1Result.data).toHaveProperty('list');
      expect(page1Result.data).toHaveProperty('pagination');
      expect(page1Result.data.pagination.page).toBe(1);
      expect(page1Result.data.pagination.pageSize).toBe(10);
    });
  });

  afterAll(async () => {
    // 清理测试数据
    simulator = null;
  });
});
