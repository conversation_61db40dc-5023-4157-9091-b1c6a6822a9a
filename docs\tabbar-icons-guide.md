# TabBar 图标准备指南

## 图标规格要求

### 尺寸规格
- **推荐尺寸**: 81px × 81px
- **格式**: PNG 格式
- **背景**: 透明背景
- **分辨率**: 2倍图 (Retina)

### 设计规范
- **线条粗细**: 2-3px
- **圆角**: 适度圆角，保持统一
- **风格**: 线性图标风格
- **颜色**: 
  - 未选中状态：#999999 (灰色)
  - 选中状态：#8B5CF6 (紫色)

## 需要准备的图标文件

请在 `images/tabbar/` 目录下准备以下 8 个图标文件：

### 1. 首页图标
- `home.png` - 未选中状态 (灰色)
- `home-active.png` - 选中状态 (紫色)
- **图标建议**: 房屋图标 🏠

### 2. 搜索图标
- `search.png` - 未选中状态 (灰色)
- `search-active.png` - 选中状态 (紫色)
- **图标建议**: 放大镜图标 🔍

### 3. 分类图标
- `category.png` - 未选中状态 (灰色)
- `category-active.png` - 选中状态 (紫色)
- **图标建议**: 网格图标或分类图标 📂

### 4. 我的图标
- `profile.png` - 未选中状态 (灰色)
- `profile-active.png` - 选中状态 (紫色)
- **图标建议**: 用户头像图标 👤

## 图标资源建议

### 在线图标资源
1. **阿里巴巴矢量图标库 (iconfont)**
   - 网址: https://www.iconfont.cn/
   - 搜索关键词: home, search, category, profile
   - 选择线性风格的图标

2. **Feather Icons**
   - 网址: https://feathericons.com/
   - 推荐图标: home, search, grid, user

3. **Heroicons**
   - 网址: https://heroicons.com/
   - 推荐图标: home, search, squares-2x2, user

### 制作步骤
1. 下载 SVG 格式图标
2. 使用设计软件（如 Sketch、Figma、Adobe XD）调整尺寸
3. 设置两种颜色状态
4. 导出为 PNG 格式（81px × 81px）
5. 放置到指定目录

## 图标预览效果

```
┌─────────────────────────────────────────────────┐
│                   TabBar                        │
├─────────┬─────────┬─────────┬─────────────────┤
│  🏠     │  🔍     │  📂     │      👤        │
│ 首页    │ 搜索    │ 分类    │     我的       │
│(选中)   │(未选中) │(未选中) │    (未选中)    │
└─────────┴─────────┴─────────┴─────────────────┘
```

## 临时解决方案

如果暂时没有图标，可以：

1. **使用 Emoji 字符**（临时）
   - 修改 app.json 中的 text 字段
   - 首页: "🏠 首页"
   - 搜索: "🔍 搜索"
   - 分类: "📂 分类"
   - 我的: "👤 我的"

2. **在线制作简单图标**
   - 使用 Canva、Figma 等在线工具
   - 创建简单的几何图形图标

## 测试验证

添加图标后，在微信开发者工具中：
1. 编译项目
2. 查看 TabBar 显示效果
3. 切换不同页面测试图标状态变化
4. 确认图标清晰度和对齐效果

## 注意事项

1. **图标文件大小**: 每个图标文件应小于 40KB
2. **命名规范**: 使用英文命名，不要使用中文
3. **路径正确**: 确保图标路径与配置文件中的路径一致
4. **图标质量**: 确保图标在不同设备上显示清晰
5. **版权问题**: 使用商业项目时注意图标版权

---

**提示**: 配置文件已经更新，现在只需要准备对应的图标文件即可完成 TabBar 图标的添加。