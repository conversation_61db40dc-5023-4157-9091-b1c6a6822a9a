<!--pages/debug/debug.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🔧 云函数调用调试</text>
    <text class="subtitle">直接测试云函数和数据管理器</text>
  </view>

  <view class="button-group">
    <button class="btn primary" bindtap="testCloudFunction">测试云函数调用</button>
    <button class="btn secondary" bindtap="testDataManager">测试数据管理器</button>
    <button class="btn warning" bindtap="clearLogs">清空日志</button>
    <button class="btn info" bindtap="copyLogs">复制日志</button>
  </view>

  <view class="log-container">
    <view class="log-header">调试日志</view>
    <scroll-view class="log-content" scroll-y="true">
      <view wx:for="{{logs}}" wx:key="index" class="log-item">{{item}}</view>
      <view wx:if="{{logs.length === 0}}" class="log-empty">暂无日志，点击上方按钮开始测试</view>
    </scroll-view>
  </view>
</view>
