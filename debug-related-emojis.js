// 调试相关推荐表情包数量问题
// 在表情包详情页的小程序控制台中运行

console.log('🔍 开始调试相关推荐数量问题...');

// 1. 检查当前页面数据
function checkCurrentPageData() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const data = currentPage.data;
    
    console.log('📄 当前页面:', currentPage.route);
    console.log('📦 当前表情包:', data.emojiData?.title);
    console.log('🏷️ 当前分类:', data.emojiData?.category);
    console.log('📋 相关推荐数量:', data.relatedEmojis?.length || 0);
    console.log('📋 相关推荐数据:', data.relatedEmojis);
    
    return {
        currentEmoji: data.emojiData,
        relatedEmojis: data.relatedEmojis,
        category: data.emojiData?.category
    };
}

// 2. 手动调用云函数检查同分类数据
async function checkCategoryData(category) {
    console.log(`\n☁️ 检查分类"${category}"的数据...`);
    
    try {
        const result = await wx.cloud.callFunction({
            name: 'dataAPI',
            data: {
                action: 'getEmojis',
                data: { 
                    category: category, 
                    page: 1, 
                    limit: 10  // 获取更多数据来检查
                }
            }
        });
        
        console.log('☁️ 云函数调用结果:', result.result);
        
        if (result.result && result.result.success) {
            const emojis = result.result.data;
            console.log(`📦 找到 ${emojis.length} 个同分类表情包`);
            
            emojis.forEach((emoji, index) => {
                console.log(`  ${index + 1}. ${emoji.title} (ID: ${emoji._id})`);
            });
            
            return emojis;
        } else {
            console.log('❌ 云函数调用失败:', result.result?.message);
            return [];
        }
    } catch (error) {
        console.error('❌ 云函数调用异常:', error);
        return [];
    }
}

// 3. 模拟相关推荐逻辑
function simulateRelatedLogic(allEmojis, currentEmojiId) {
    console.log(`\n🔄 模拟相关推荐逻辑...`);
    console.log(`当前表情包ID: ${currentEmojiId}`);
    
    const filtered = allEmojis.filter(item => item._id !== currentEmojiId);
    console.log(`过滤后剩余: ${filtered.length} 个`);
    
    const sliced = filtered.slice(0, 4);
    console.log(`取前4个: ${sliced.length} 个`);
    
    const mapped = sliced.map(item => ({
        id: item._id || item.id,
        title: item.title,
        imageUrl: item.imageUrl,
        likesText: formatNumber(item.likes || 0)
    }));
    
    console.log('最终相关推荐数据:', mapped);
    return mapped;
}

// 4. 格式化数字函数
function formatNumber(num) {
    if (typeof num !== 'number') return '0';
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
}

// 5. 强制刷新相关推荐
async function forceRefreshRelated() {
    console.log('\n🔄 强制刷新相关推荐...');
    
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const category = currentPage.data.emojiData?.category;
    
    if (!category) {
        console.log('❌ 无法获取当前分类');
        return;
    }
    
    // 调用页面的loadRelatedEmojis方法
    if (typeof currentPage.loadRelatedEmojis === 'function') {
        await currentPage.loadRelatedEmojis(category);
        console.log('✅ 已调用页面的loadRelatedEmojis方法');
    } else {
        console.log('❌ 页面没有loadRelatedEmojis方法');
    }
}

// 6. 创建测试数据
function createTestRelatedData() {
    console.log('\n🧪 创建测试相关推荐数据...');
    
    const testRelatedEmojis = [
        {
            id: 'test_related_1',
            title: '测试相关1',
            imageUrl: 'https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text=😂',
            likesText: '100'
        },
        {
            id: 'test_related_2',
            title: '测试相关2',
            imageUrl: 'https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=😍',
            likesText: '80'
        },
        {
            id: 'test_related_3',
            title: '测试相关3',
            imageUrl: 'https://via.placeholder.com/200x200/45B7D1/FFFFFF?text=😎',
            likesText: '60'
        },
        {
            id: 'test_related_4',
            title: '测试相关4',
            imageUrl: 'https://via.placeholder.com/200x200/96CEB4/FFFFFF?text=🤔',
            likesText: '40'
        }
    ];
    
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    currentPage.setData({
        relatedEmojis: testRelatedEmojis
    });
    
    console.log('✅ 测试数据已设置，应该显示4个相关推荐');
}

// 主调试函数
async function debugRelatedEmojis() {
    console.log('='.repeat(50));
    console.log('🔍 相关推荐数量调试');
    console.log('='.repeat(50));
    
    // 1. 检查当前页面数据
    const pageData = checkCurrentPageData();
    
    if (!pageData.currentEmoji) {
        console.log('❌ 没有找到当前表情包数据');
        return;
    }
    
    // 2. 检查同分类数据
    const categoryEmojis = await checkCategoryData(pageData.category);
    
    // 3. 模拟相关推荐逻辑
    if (categoryEmojis.length > 0) {
        const simulatedRelated = simulateRelatedLogic(categoryEmojis, pageData.currentEmoji.id);
        
        if (simulatedRelated.length < 4) {
            console.log(`⚠️ 同分类表情包数量不足，只有 ${simulatedRelated.length} 个`);
            console.log('💡 建议：添加更多同分类的表情包数据');
        }
    }
    
    // 4. 强制刷新
    await forceRefreshRelated();
    
    // 5. 如果还是只有1个，创建测试数据
    const updatedPageData = checkCurrentPageData();
    if (updatedPageData.relatedEmojis.length <= 1) {
        console.log('⚠️ 刷新后仍然只有1个相关推荐，创建测试数据...');
        createTestRelatedData();
    }
    
    console.log('\n✅ 调试完成！');
}

// 运行调试
debugRelatedEmojis();

// 导出函数供手动调用
window.debugRelatedEmojis = {
    debugRelatedEmojis,
    checkCurrentPageData,
    checkCategoryData,
    forceRefreshRelated,
    createTestRelatedData
};

console.log('💡 可以手动调用以下函数：');
console.log('- debugRelatedEmojis.debugRelatedEmojis() // 完整调试');
console.log('- debugRelatedEmojis.createTestRelatedData() // 创建测试数据');
console.log('- debugRelatedEmojis.forceRefreshRelated() // 强制刷新');
