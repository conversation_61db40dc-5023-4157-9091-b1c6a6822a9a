/**
 * 实时同步性能监控器
 * 监控实时同步功能的性能指标和稳定性
 */

class RealtimePerformanceMonitor {
  constructor() {
    this.metrics = {
      connectionAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      reconnectAttempts: 0,
      notificationsReceived: 0,
      notificationsProcessed: 0,
      cacheRefreshCount: 0,
      averageResponseTime: 0,
      lastConnectionTime: null,
      lastNotificationTime: null,
      errors: []
    }
    
    this.performanceData = []
    this.isMonitoring = false
    this.startTime = null
    
    console.log('📊 RealtimePerformanceMonitor 初始化')
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.startTime = new Date()
    this.resetMetrics()
    
    console.log('📊 开始实时同步性能监控')
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false
    console.log('📊 停止实时同步性能监控')
    
    return this.generateReport()
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    this.metrics = {
      connectionAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      reconnectAttempts: 0,
      notificationsReceived: 0,
      notificationsProcessed: 0,
      cacheRefreshCount: 0,
      averageResponseTime: 0,
      lastConnectionTime: null,
      lastNotificationTime: null,
      errors: []
    }
    
    this.performanceData = []
  }

  /**
   * 记录连接尝试
   */
  recordConnectionAttempt() {
    if (!this.isMonitoring) return

    this.metrics.connectionAttempts++
    console.log(`📊 连接尝试: ${this.metrics.connectionAttempts}`)
  }

  /**
   * 记录连接成功
   */
  recordConnectionSuccess() {
    if (!this.isMonitoring) return

    this.metrics.successfulConnections++
    this.metrics.lastConnectionTime = new Date()
    
    console.log(`📊 连接成功: ${this.metrics.successfulConnections}`)
  }

  /**
   * 记录连接失败
   */
  recordConnectionFailure(error) {
    if (!this.isMonitoring) return

    this.metrics.failedConnections++
    this.recordError('connection_failure', error)
    
    console.log(`📊 连接失败: ${this.metrics.failedConnections}`)
  }

  /**
   * 记录重连尝试
   */
  recordReconnectAttempt() {
    if (!this.isMonitoring) return

    this.metrics.reconnectAttempts++
    console.log(`📊 重连尝试: ${this.metrics.reconnectAttempts}`)
  }

  /**
   * 记录通知接收
   */
  recordNotificationReceived() {
    if (!this.isMonitoring) return

    this.metrics.notificationsReceived++
    this.metrics.lastNotificationTime = new Date()
    
    console.log(`📊 通知接收: ${this.metrics.notificationsReceived}`)
  }

  /**
   * 记录通知处理
   */
  recordNotificationProcessed(processingTime) {
    if (!this.isMonitoring) return

    this.metrics.notificationsProcessed++
    
    // 更新平均响应时间
    if (processingTime) {
      const currentAvg = this.metrics.averageResponseTime
      const count = this.metrics.notificationsProcessed
      this.metrics.averageResponseTime = ((currentAvg * (count - 1)) + processingTime) / count
    }
    
    console.log(`📊 通知处理: ${this.metrics.notificationsProcessed}, 处理时间: ${processingTime}ms`)
  }

  /**
   * 记录缓存刷新
   */
  recordCacheRefresh(type) {
    if (!this.isMonitoring) return

    this.metrics.cacheRefreshCount++
    
    this.performanceData.push({
      timestamp: new Date(),
      type: 'cache_refresh',
      dataType: type,
      count: this.metrics.cacheRefreshCount
    })
    
    console.log(`📊 缓存刷新: ${type}, 总计: ${this.metrics.cacheRefreshCount}`)
  }

  /**
   * 记录错误
   */
  recordError(type, error) {
    if (!this.isMonitoring) return

    const errorRecord = {
      timestamp: new Date(),
      type: type,
      message: error?.message || error,
      stack: error?.stack
    }
    
    this.metrics.errors.push(errorRecord)
    
    // 只保留最近50个错误
    if (this.metrics.errors.length > 50) {
      this.metrics.errors = this.metrics.errors.slice(-50)
    }
    
    console.log(`📊 错误记录: ${type} - ${errorRecord.message}`)
  }

  /**
   * 记录性能数据点
   */
  recordPerformanceData(type, data) {
    if (!this.isMonitoring) return

    this.performanceData.push({
      timestamp: new Date(),
      type: type,
      data: data
    })
    
    // 只保留最近100个数据点
    if (this.performanceData.length > 100) {
      this.performanceData = this.performanceData.slice(-100)
    }
  }

  /**
   * 获取连接成功率
   */
  getConnectionSuccessRate() {
    if (this.metrics.connectionAttempts === 0) return 0
    return (this.metrics.successfulConnections / this.metrics.connectionAttempts * 100).toFixed(2)
  }

  /**
   * 获取通知处理成功率
   */
  getNotificationProcessingRate() {
    if (this.metrics.notificationsReceived === 0) return 0
    return (this.metrics.notificationsProcessed / this.metrics.notificationsReceived * 100).toFixed(2)
  }

  /**
   * 获取运行时长
   */
  getUptime() {
    if (!this.startTime) return 0
    return new Date() - this.startTime
  }

  /**
   * 获取实时指标
   */
  getCurrentMetrics() {
    return {
      ...this.metrics,
      connectionSuccessRate: this.getConnectionSuccessRate() + '%',
      notificationProcessingRate: this.getNotificationProcessingRate() + '%',
      uptime: this.getUptime(),
      isMonitoring: this.isMonitoring
    }
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const uptime = this.getUptime()
    const report = {
      summary: {
        monitoringDuration: uptime,
        connectionSuccessRate: this.getConnectionSuccessRate() + '%',
        notificationProcessingRate: this.getNotificationProcessingRate() + '%',
        averageResponseTime: this.metrics.averageResponseTime.toFixed(2) + 'ms',
        totalErrors: this.metrics.errors.length
      },
      metrics: this.metrics,
      performanceData: this.performanceData,
      recommendations: this.generateRecommendations()
    }
    
    console.log('📊 性能报告生成完成:', report)
    return report
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = []
    
    // 连接成功率建议
    const successRate = parseFloat(this.getConnectionSuccessRate())
    if (successRate < 90) {
      recommendations.push({
        type: 'connection',
        priority: 'high',
        message: `连接成功率较低 (${successRate}%)，建议检查网络环境和云开发配置`
      })
    }
    
    // 响应时间建议
    if (this.metrics.averageResponseTime > 2000) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: `平均响应时间较长 (${this.metrics.averageResponseTime.toFixed(2)}ms)，建议优化数据处理逻辑`
      })
    }
    
    // 错误率建议
    if (this.metrics.errors.length > 10) {
      recommendations.push({
        type: 'stability',
        priority: 'high',
        message: `错误数量较多 (${this.metrics.errors.length})，建议检查错误日志并修复问题`
      })
    }
    
    // 重连建议
    if (this.metrics.reconnectAttempts > 5) {
      recommendations.push({
        type: 'stability',
        priority: 'medium',
        message: `重连次数较多 (${this.metrics.reconnectAttempts})，建议优化重连策略`
      })
    }
    
    return recommendations
  }

  /**
   * 导出监控数据
   */
  exportData() {
    return {
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      performanceData: this.performanceData,
      report: this.generateReport()
    }
  }
}

// 创建全局实例
const realtimePerformanceMonitor = new RealtimePerformanceMonitor()

module.exports = {
  RealtimePerformanceMonitor,
  realtimePerformanceMonitor
}
