// TabBar图标生成器
// 生成高质量的PNG图标数据

const IconGenerator = {
  // 创建SVG图标
  createSVG(path, size = 81, color = '#6B7280', strokeWidth = 3) {
    return `
      <svg width="${size}" height="${size}" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
        <path d="${path}" stroke="${color}" stroke-width="${strokeWidth}" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
  },

  // 创建填充版本的SVG图标
  createFilledSVG(path, size = 81, color = '#8B5CF6', strokeWidth = 2) {
    return `
      <svg width="${size}" height="${size}" viewBox="0 0 81 81" xmlns="http://www.w3.org/2000/svg">
        <path d="${path}" fill="${color}" stroke="${color}" stroke-width="${strokeWidth}" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;
  },

  // 图标路径定义
  icons: {
    home: 'M40.5 15L15 35V65H30V50H51V65H66V35L40.5 15Z',
    search: 'M35 15C26.7 15 20 21.7 20 30C20 38.3 26.7 45 35 45C38.1 45 40.9 44 43.2 42.3L58.9 58L65 51.9L49.3 36.2C51 33.9 52 31.1 52 28C52 19.7 45.3 13 37 13C28.7 13 22 19.7 22 28',
    category: 'M15 15H35V35H15V15ZM46 15H66V35H46V15ZM15 46H35V66H15V46ZM46 46H66V66H46V46Z',
    profile: 'M40.5 15C46.8 15 52 20.2 52 26.5C52 32.8 46.8 38 40.5 38C34.2 38 29 32.8 29 26.5C29 20.2 34.2 15 40.5 15ZM15 65C15 52 26 42 40.5 42C55 42 66 52 66 65'
  },

  // 生成所有图标的SVG代码
  generateAllIcons() {
    const result = {};
    
    Object.keys(this.icons).forEach(name => {
      const path = this.icons[name];
      result[name] = this.createSVG(path);
      result[`${name}-active`] = this.createFilledSVG(path);
    });
    
    return result;
  },

  // 输出图标代码供复制
  printIconCode() {
    const icons = this.generateAllIcons();
    
    console.log('=== TabBar图标SVG代码 ===');
    Object.keys(icons).forEach(name => {
      console.log(`\n${name}.svg:`);
      console.log(icons[name]);
    });
  }
};

// 导出
module.exports = { IconGenerator };

// 如果直接运行此文件，输出图标代码
if (typeof window === 'undefined') {
  IconGenerator.printIconCode();
}