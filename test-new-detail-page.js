const { chromium } = require('playwright');

async function testNewDetailPage() {
  console.log('🧪 测试新版表情包详情页...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 导航到管理后台
    console.log('📱 打开管理后台...');
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForTimeout(8000);
    
    // 获取第一个表情包的ID用于测试
    console.log('🔍 获取测试用的表情包ID...');
    const testEmojiId = await page.evaluate(async () => {
      try {
        // 确保SDK已加载
        if (!window.cloudbase) {
          return null;
        }
        
        // 初始化云开发
        const app = window.cloudbase.init({
          env: 'cloud1-5g6pvnpl88dc0142'
        });
        
        // 匿名登录
        await app.auth().signInAnonymously();
        
        // 调用云函数获取表情包列表
        const result = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 1 } }
        });
        
        if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
          return result.result.data[0]._id;
        }
        
        return null;
      } catch (error) {
        console.error('获取测试ID失败:', error);
        return null;
      }
    });
    
    if (!testEmojiId) {
      console.error('❌ 无法获取测试用的表情包ID');
      return;
    }
    
    console.log('✅ 获取到测试ID:', testEmojiId);
    
    // 构造新详情页的URL
    const detailUrl = `http://localhost:8080/pages/detail/detail-new?id=${testEmojiId}`;
    console.log('🔗 测试URL:', detailUrl);
    
    // 打开新标签页测试详情页
    const detailPage = await context.newPage();
    
    console.log('📱 导航到新版详情页...');
    await detailPage.goto(detailUrl);
    
    // 等待页面加载
    await detailPage.waitForTimeout(5000);
    
    // 检查页面是否正常加载
    console.log('🔍 检查页面加载状态...');
    
    const pageStatus = await detailPage.evaluate(() => {
      const body = document.body;
      const hasContent = body && body.innerHTML.length > 100;
      const hasError = body && body.innerHTML.includes('error');
      const hasLoading = body && body.innerHTML.includes('loading');
      
      return {
        hasContent,
        hasError,
        hasLoading,
        bodyLength: body ? body.innerHTML.length : 0,
        title: document.title || 'No title'
      };
    });
    
    console.log('📊 页面状态:', pageStatus);
    
    if (pageStatus.hasContent && !pageStatus.hasError) {
      console.log('✅ 新版详情页加载成功！');
      console.log('📄 页面标题:', pageStatus.title);
      console.log('📏 内容长度:', pageStatus.bodyLength);
      
      // 截图保存
      await detailPage.screenshot({ 
        path: 'new-detail-page-test.png',
        fullPage: true 
      });
      console.log('📸 页面截图已保存: new-detail-page-test.png');
      
    } else if (pageStatus.hasError) {
      console.error('❌ 新版详情页加载出错');
      
      // 获取错误信息
      const errorInfo = await detailPage.evaluate(() => {
        const errorElements = document.querySelectorAll('.error-message, .error-content, [class*="error"]');
        return Array.from(errorElements).map(el => el.textContent).join('; ');
      });
      
      console.error('🚨 错误信息:', errorInfo);
      
    } else if (pageStatus.hasLoading) {
      console.log('⏳ 页面仍在加载中...');
      
      // 等待更长时间
      await detailPage.waitForTimeout(10000);
      
      const finalStatus = await detailPage.evaluate(() => {
        return {
          hasLoading: document.body.innerHTML.includes('loading'),
          hasContent: document.body.innerHTML.length > 100
        };
      });
      
      if (finalStatus.hasContent && !finalStatus.hasLoading) {
        console.log('✅ 页面最终加载成功！');
      } else {
        console.error('❌ 页面加载超时');
      }
      
    } else {
      console.error('❌ 页面内容为空或加载失败');
    }
    
    // 测试页面交互
    console.log('🎯 测试页面交互功能...');
    
    try {
      // 检查是否有操作按钮
      const hasButtons = await detailPage.evaluate(() => {
        const buttons = document.querySelectorAll('.action-btn, button');
        return buttons.length > 0;
      });
      
      if (hasButtons) {
        console.log('✅ 发现操作按钮，页面交互功能正常');
      } else {
        console.log('⚠️ 未发现操作按钮，可能还在加载中');
      }
      
    } catch (error) {
      console.warn('⚠️ 交互测试失败:', error.message);
    }
    
    console.log('🎉 新版详情页测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testNewDetailPage().catch(console.error);
