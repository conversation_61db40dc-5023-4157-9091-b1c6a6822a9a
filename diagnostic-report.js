/**
 * 表情包小程序深度技术诊断报告
 * 检查编译错误和数据同步问题
 */

const fs = require('fs')
const path = require('path')

class ProjectDiagnostic {
  constructor() {
    this.issues = []
    this.warnings = []
    this.suggestions = []
    this.projectRoot = process.cwd()
  }

  /**
   * 执行完整诊断
   */
  async runFullDiagnostic() {
    console.log('🔍 开始深度技术诊断...\n')
    
    // 1. 检查项目结构
    this.checkProjectStructure()
    
    // 2. 检查配置文件
    this.checkConfigFiles()
    
    // 3. 检查JavaScript语法和依赖
    this.checkJavaScriptFiles()
    
    // 4. 检查云函数配置
    this.checkCloudFunctions()
    
    // 5. 检查数据同步相关文件
    this.checkSyncFiles()
    
    // 6. 检查小程序特有API使用
    this.checkWechatAPIs()
    
    // 7. 生成诊断报告
    this.generateReport()
  }

  /**
   * 检查项目结构
   */
  checkProjectStructure() {
    console.log('📁 检查项目结构...')
    
    const requiredFiles = [
      'app.js',
      'app.json',
      'app.wxss',
      'project.config.json',
      'sitemap.json'
    ]
    
    const requiredDirs = [
      'pages',
      'utils',
      'cloudfunctions',
      'config'
    ]
    
    // 检查必需文件
    requiredFiles.forEach(file => {
      if (!this.fileExists(file)) {
        this.issues.push({
          type: 'MISSING_FILE',
          severity: 'ERROR',
          file: file,
          message: `缺少必需文件: ${file}`
        })
      }
    })
    
    // 检查必需目录
    requiredDirs.forEach(dir => {
      if (!this.dirExists(dir)) {
        this.issues.push({
          type: 'MISSING_DIR',
          severity: 'ERROR',
          file: dir,
          message: `缺少必需目录: ${dir}`
        })
      }
    })
    
    console.log('✅ 项目结构检查完成')
  }

  /**
   * 检查配置文件
   */
  checkConfigFiles() {
    console.log('⚙️ 检查配置文件...')
    
    // 检查 app.json
    this.checkAppJson()
    
    // 检查 project.config.json
    this.checkProjectConfig()
    
    // 检查云开发配置
    this.checkCloudConfig()
    
    console.log('✅ 配置文件检查完成')
  }

  /**
   * 检查 app.json
   */
  checkAppJson() {
    const appJsonPath = path.join(this.projectRoot, 'app.json')
    
    if (!this.fileExists('app.json')) {
      return
    }
    
    try {
      const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'))
      
      // 检查页面路径
      if (!appJson.pages || !Array.isArray(appJson.pages)) {
        this.issues.push({
          type: 'CONFIG_ERROR',
          severity: 'ERROR',
          file: 'app.json',
          message: 'app.json 缺少 pages 配置或格式错误'
        })
      } else {
        // 检查页面文件是否存在
        appJson.pages.forEach(pagePath => {
          const jsFile = `${pagePath}.js`
          const wxmlFile = `${pagePath}.wxml`
          
          if (!this.fileExists(jsFile)) {
            this.issues.push({
              type: 'MISSING_PAGE_FILE',
              severity: 'ERROR',
              file: jsFile,
              message: `页面JS文件不存在: ${jsFile}`
            })
          }
          
          if (!this.fileExists(wxmlFile)) {
            this.issues.push({
              type: 'MISSING_PAGE_FILE',
              severity: 'ERROR',
              file: wxmlFile,
              message: `页面WXML文件不存在: ${wxmlFile}`
            })
          }
        })
      }
      
      // 检查tabBar配置
      if (appJson.tabBar && appJson.tabBar.custom) {
        if (!this.dirExists('custom-tab-bar')) {
          this.issues.push({
            type: 'MISSING_CUSTOM_TABBAR',
            severity: 'ERROR',
            file: 'custom-tab-bar',
            message: '使用了自定义tabBar但缺少custom-tab-bar目录'
          })
        }
      }
      
    } catch (error) {
      this.issues.push({
        type: 'JSON_PARSE_ERROR',
        severity: 'ERROR',
        file: 'app.json',
        message: `app.json 解析错误: ${error.message}`
      })
    }
  }

  /**
   * 检查项目配置
   */
  checkProjectConfig() {
    const configPath = path.join(this.projectRoot, 'project.config.json')
    
    if (!this.fileExists('project.config.json')) {
      return
    }
    
    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      
      // 检查appid
      if (!config.appid) {
        this.warnings.push({
          type: 'MISSING_APPID',
          severity: 'WARNING',
          file: 'project.config.json',
          message: '缺少appid配置，可能影响云开发功能'
        })
      }
      
      // 检查云函数根目录
      if (!config.cloudfunctionRoot) {
        this.warnings.push({
          type: 'MISSING_CLOUD_ROOT',
          severity: 'WARNING',
          file: 'project.config.json',
          message: '缺少cloudfunctionRoot配置'
        })
      }
      
      // 检查云环境ID
      if (!config.cloudenv) {
        this.warnings.push({
          type: 'MISSING_CLOUD_ENV',
          severity: 'WARNING',
          file: 'project.config.json',
          message: '缺少cloudenv配置，可能影响云开发功能'
        })
      }
      
    } catch (error) {
      this.issues.push({
        type: 'JSON_PARSE_ERROR',
        severity: 'ERROR',
        file: 'project.config.json',
        message: `project.config.json 解析错误: ${error.message}`
      })
    }
  }

  /**
   * 检查云开发配置
   */
  checkCloudConfig() {
    const cloudConfigPath = 'config/cloud.js'
    
    if (!this.fileExists(cloudConfigPath)) {
      this.warnings.push({
        type: 'MISSING_CLOUD_CONFIG',
        severity: 'WARNING',
        file: cloudConfigPath,
        message: '缺少云开发配置文件'
      })
      return
    }
    
    // 检查云配置文件内容
    try {
      const content = fs.readFileSync(path.join(this.projectRoot, cloudConfigPath), 'utf8')
      
      if (!content.includes('wx.cloud.init')) {
        this.warnings.push({
          type: 'INCOMPLETE_CLOUD_CONFIG',
          severity: 'WARNING',
          file: cloudConfigPath,
          message: '云配置文件可能不完整，缺少wx.cloud.init调用'
        })
      }
      
    } catch (error) {
      this.issues.push({
        type: 'FILE_READ_ERROR',
        severity: 'ERROR',
        file: cloudConfigPath,
        message: `读取云配置文件失败: ${error.message}`
      })
    }
  }

  /**
   * 检查JavaScript文件
   */
  checkJavaScriptFiles() {
    console.log('📝 检查JavaScript文件...')
    
    const jsFiles = [
      'app.js',
      'pages/index/index.js',
      'utils/newDataManager.js',
      'utils/realtimeSync.js',
      'utils/versionManager.js',
      'utils/incrementalSync.js'
    ]
    
    jsFiles.forEach(file => {
      this.checkJSFile(file)
    })
    
    console.log('✅ JavaScript文件检查完成')
  }

  /**
   * 检查单个JS文件
   */
  checkJSFile(filePath) {
    if (!this.fileExists(filePath)) {
      this.issues.push({
        type: 'MISSING_JS_FILE',
        severity: 'ERROR',
        file: filePath,
        message: `JavaScript文件不存在: ${filePath}`
      })
      return
    }
    
    try {
      const content = fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8')
      
      // 检查require语句
      this.checkRequireStatements(filePath, content)
      
      // 检查微信API使用
      this.checkWechatAPIUsage(filePath, content)
      
      // 检查语法问题
      this.checkSyntaxIssues(filePath, content)
      
    } catch (error) {
      this.issues.push({
        type: 'FILE_READ_ERROR',
        severity: 'ERROR',
        file: filePath,
        message: `读取文件失败: ${error.message}`
      })
    }
  }

  /**
   * 检查require语句
   */
  checkRequireStatements(filePath, content) {
    const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g
    let match
    
    while ((match = requireRegex.exec(content)) !== null) {
      const requiredPath = match[1]
      
      // 跳过Node.js内置模块
      if (this.isNodeBuiltinModule(requiredPath)) {
        continue
      }
      
      // 检查相对路径引用
      if (requiredPath.startsWith('./') || requiredPath.startsWith('../')) {
        const resolvedPath = this.resolveRelativePath(filePath, requiredPath)
        
        if (!this.fileExists(resolvedPath)) {
          this.issues.push({
            type: 'MISSING_REQUIRE_FILE',
            severity: 'ERROR',
            file: filePath,
            line: this.getLineNumber(content, match.index),
            message: `require引用的文件不存在: ${requiredPath} -> ${resolvedPath}`
          })
        }
      }
    }
  }

  /**
   * 检查微信API使用
   */
  checkWechatAPIUsage(filePath, content) {
    // 检查wx.cloud使用但未初始化
    if (content.includes('wx.cloud.') && !content.includes('wx.cloud.init')) {
      // 检查是否在app.js中初始化
      if (filePath !== 'app.js') {
        const appJsContent = this.getFileContent('app.js')
        if (appJsContent && !appJsContent.includes('wx.cloud.init')) {
          this.warnings.push({
            type: 'CLOUD_NOT_INITIALIZED',
            severity: 'WARNING',
            file: filePath,
            message: '使用了wx.cloud但可能未正确初始化'
          })
        }
      }
    }
    
    // 检查不兼容的API使用
    const incompatibleAPIs = [
      'localStorage',
      'sessionStorage',
      'document.',
      'window.',
      'XMLHttpRequest'
    ]
    
    incompatibleAPIs.forEach(api => {
      if (content.includes(api)) {
        this.issues.push({
          type: 'INCOMPATIBLE_API',
          severity: 'ERROR',
          file: filePath,
          message: `使用了不兼容的API: ${api}`
        })
      }
    })
  }

  /**
   * 检查语法问题
   */
  checkSyntaxIssues(filePath, content) {
    // 检查未闭合的括号
    const openBraces = (content.match(/\{/g) || []).length
    const closeBraces = (content.match(/\}/g) || []).length
    
    if (openBraces !== closeBraces) {
      this.issues.push({
        type: 'SYNTAX_ERROR',
        severity: 'ERROR',
        file: filePath,
        message: `括号不匹配: { ${openBraces} 个, } ${closeBraces} 个`
      })
    }
    
    // 检查常见语法错误
    if (content.includes('function(') && content.includes('=> {')) {
      this.warnings.push({
        type: 'MIXED_FUNCTION_SYNTAX',
        severity: 'WARNING',
        file: filePath,
        message: '混合使用了传统函数和箭头函数语法'
      })
    }
  }

  /**
   * 检查云函数
   */
  checkCloudFunctions() {
    console.log('☁️ 检查云函数配置...')
    
    const cloudFunctionsDir = 'cloudfunctions'
    
    if (!this.dirExists(cloudFunctionsDir)) {
      this.issues.push({
        type: 'MISSING_CLOUD_FUNCTIONS',
        severity: 'ERROR',
        file: cloudFunctionsDir,
        message: '缺少cloudfunctions目录'
      })
      return
    }
    
    // 检查关键云函数
    const requiredCloudFunctions = [
      'dataAPI',
      'syncAPI',
      'login',
      'getOpenID'
    ]
    
    requiredCloudFunctions.forEach(funcName => {
      const funcDir = path.join(cloudFunctionsDir, funcName)
      const indexFile = path.join(funcDir, 'index.js')
      const packageFile = path.join(funcDir, 'package.json')
      
      if (!this.dirExists(funcDir)) {
        this.issues.push({
          type: 'MISSING_CLOUD_FUNCTION',
          severity: 'ERROR',
          file: funcDir,
          message: `缺少云函数: ${funcName}`
        })
      } else {
        if (!this.fileExists(indexFile)) {
          this.issues.push({
            type: 'MISSING_CLOUD_FUNCTION_INDEX',
            severity: 'ERROR',
            file: indexFile,
            message: `云函数缺少index.js: ${funcName}`
          })
        }
        
        if (!this.fileExists(packageFile)) {
          this.issues.push({
            type: 'MISSING_CLOUD_FUNCTION_PACKAGE',
            severity: 'ERROR',
            file: packageFile,
            message: `云函数缺少package.json: ${funcName}`
          })
        }
      }
    })
    
    console.log('✅ 云函数检查完成')
  }

  /**
   * 检查数据同步相关文件
   */
  checkSyncFiles() {
    console.log('🔄 检查数据同步相关文件...')
    
    const syncFiles = [
      'utils/realtimeSync.js',
      'utils/versionManager.js',
      'utils/incrementalSync.js',
      'cloudfunctions/syncAPI/index.js'
    ]
    
    syncFiles.forEach(file => {
      if (!this.fileExists(file)) {
        this.issues.push({
          type: 'MISSING_SYNC_FILE',
          severity: 'ERROR',
          file: file,
          message: `缺少数据同步文件: ${file}`
        })
      } else {
        this.checkSyncFileContent(file)
      }
    })
    
    console.log('✅ 数据同步文件检查完成')
  }

  /**
   * 检查同步文件内容
   */
  checkSyncFileContent(filePath) {
    const content = this.getFileContent(filePath)
    if (!content) return
    
    // 检查syncAPI云函数
    if (filePath.includes('syncAPI')) {
      if (!content.includes('getVersions') || !content.includes('getIncrementalData')) {
        this.issues.push({
          type: 'INCOMPLETE_SYNC_API',
          severity: 'ERROR',
          file: filePath,
          message: 'syncAPI云函数缺少必要的方法'
        })
      }
    }
    
    // 检查实时同步管理器
    if (filePath.includes('realtimeSync')) {
      if (!content.includes('callSyncAPI')) {
        this.warnings.push({
          type: 'MISSING_SYNC_METHOD',
          severity: 'WARNING',
          file: filePath,
          message: '实时同步管理器可能缺少云函数调用方法'
        })
      }
    }
  }

  /**
   * 检查微信API使用
   */
  checkWechatAPIs() {
    console.log('📱 检查微信小程序API使用...')
    
    // 这个方法在checkJavaScriptFiles中已经部分实现
    // 这里可以添加更多特定的检查
    
    console.log('✅ 微信API检查完成')
  }

  /**
   * 生成诊断报告
   */
  generateReport() {
    console.log('\n📊 生成诊断报告...\n')
    
    console.log('=' * 60)
    console.log('🔍 表情包小程序深度技术诊断报告')
    console.log('=' * 60)
    
    // 统计信息
    console.log(`\n📈 统计信息:`)
    console.log(`   错误: ${this.issues.filter(i => i.severity === 'ERROR').length} 个`)
    console.log(`   警告: ${this.issues.filter(i => i.severity === 'WARNING').length + this.warnings.length} 个`)
    
    // 错误详情
    const errors = this.issues.filter(i => i.severity === 'ERROR')
    if (errors.length > 0) {
      console.log(`\n❌ 错误详情:`)
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. [${error.type}] ${error.file}`)
        console.log(`      ${error.message}`)
        if (error.line) {
          console.log(`      行号: ${error.line}`)
        }
      })
    }
    
    // 警告详情
    const allWarnings = [...this.issues.filter(i => i.severity === 'WARNING'), ...this.warnings]
    if (allWarnings.length > 0) {
      console.log(`\n⚠️ 警告详情:`)
      allWarnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. [${warning.type}] ${warning.file}`)
        console.log(`      ${warning.message}`)
      })
    }
    
    // 修复建议
    this.generateFixSuggestions()
    
    console.log('\n' + '=' * 60)
    console.log('诊断完成')
    console.log('=' * 60)
  }

  /**
   * 生成修复建议
   */
  generateFixSuggestions() {
    console.log(`\n🔧 修复建议:`)
    
    const errorTypes = [...new Set(this.issues.map(i => i.type))]
    
    errorTypes.forEach(type => {
      switch (type) {
        case 'MISSING_FILE':
          console.log(`   • 创建缺少的必需文件`)
          break
        case 'MISSING_REQUIRE_FILE':
          console.log(`   • 检查并修复require语句中的文件路径`)
          break
        case 'INCOMPATIBLE_API':
          console.log(`   • 替换不兼容的API为小程序API`)
          break
        case 'MISSING_CLOUD_FUNCTION':
          console.log(`   • 创建缺少的云函数并正确配置`)
          break
        case 'INCOMPLETE_SYNC_API':
          console.log(`   • 完善syncAPI云函数的实现`)
          break
        case 'SYNTAX_ERROR':
          console.log(`   • 修复JavaScript语法错误`)
          break
      }
    })
  }

  // 辅助方法
  fileExists(filePath) {
    return fs.existsSync(path.join(this.projectRoot, filePath))
  }

  dirExists(dirPath) {
    return fs.existsSync(path.join(this.projectRoot, dirPath)) && 
           fs.statSync(path.join(this.projectRoot, dirPath)).isDirectory()
  }

  getFileContent(filePath) {
    try {
      return fs.readFileSync(path.join(this.projectRoot, filePath), 'utf8')
    } catch (error) {
      return null
    }
  }

  resolveRelativePath(fromFile, relativePath) {
    const fromDir = path.dirname(fromFile)
    let resolved = path.join(fromDir, relativePath)
    
    // 添加.js扩展名如果没有
    if (!path.extname(resolved)) {
      resolved += '.js'
    }
    
    return resolved.replace(/\\/g, '/')
  }

  isNodeBuiltinModule(moduleName) {
    const builtinModules = [
      'fs', 'path', 'util', 'crypto', 'events', 'stream', 'buffer'
    ]
    return builtinModules.includes(moduleName)
  }

  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length
  }
}

// 运行诊断
if (require.main === module) {
  const diagnostic = new ProjectDiagnostic()
  diagnostic.runFullDiagnostic()
}

module.exports = ProjectDiagnostic
