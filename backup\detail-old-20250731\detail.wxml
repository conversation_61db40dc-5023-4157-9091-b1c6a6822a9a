<!--pages/detail/detail.wxml - 旧版本备份（已被 detail-new.wxml 替代）-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{!emojiData}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 详情内容 -->
  <view wx:if="{{emojiData}}">
    <!-- 第一阶段：表情包内容 -->
    <view class="emoji-container card">
      <image class="emoji-image" src="{{emojiData.imageUrl}}" mode="aspectFit" />
    </view>

    <!-- 第一阶段：表情包信息 -->
    <view class="emoji-info card">
      <view class="emoji-header">
        <text class="emoji-title">{{emojiData.title}}</text>
        <view class="emoji-meta">
          <text class="date-icon">📅</text>
          <text class="date-text">{{emojiData.date}}</text>
          <text class="view-count">👁 {{emojiData.views}}</text>
          <text class="hot-icon">🔥 热门</text>
        </view>

        <!-- 作者标签 -->
        <view class="author-tag">
          <text class="author-text">{{emojiData.category}}</text>
        </view>
      </view>

      <!-- 标签 -->
      <view class="tags-container">
        <view
          class="tag-item"
          wx:for="{{emojiData.tags}}"
          wx:key="*this"
        >
          <text class="tag-text">#{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 第二阶段：统计信息 - 只有在按钮加载完成后才显示 -->
    <view class="stats-container card" wx:if="{{buttonsLoaded}}">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-icon {{isLiked ? 'liked' : ''}}">{{isLiked ? '❤️' : '🤍'}}</text>
          <text class="stat-number">{{emojiData.likes}}</text>
          <text class="stat-label">点赞</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon {{isCollected ? 'collected' : ''}}">{{isCollected ? '⭐' : '☆'}}</text>
          <text class="stat-number">{{emojiData.collections}}</text>
          <text class="stat-label">收藏</text>
        </view>
      </view>
    </view>

    <!-- 第二阶段：操作按钮 - 只有在按钮加载完成后才显示 -->
    <view class="action-buttons card {{buttonsLoaded ? 'show' : 'hide'}}" wx:if="{{buttonsLoaded}}">
      <view class="button-row">
        <button
          class="action-btn like-btn {{isLiked ? 'liked' : ''}}"
          bindtap="onToggleLike"
          disabled="{{likeLoading}}"
        >
          <text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
          <text class="btn-text">{{likeLoading ? '处理中...' : (isLiked ? '已点赞' : '点赞')}}</text>
        </button>

        <button
          class="action-btn collect-btn {{isCollected ? 'collected' : ''}}"
          bindtap="onToggleCollect"
          disabled="{{collectLoading}}"
        >
          <text class="btn-icon">{{isCollected ? '⭐' : '☆'}}</text>
          <text class="btn-text">{{collectLoading ? '处理中...' : (isCollected ? '已收藏' : '收藏')}}</text>
        </button>
      </view>

      <view class="button-row">
        <button class="action-btn download-btn" bindtap="onDownload">
          <text class="btn-icon">⬇️</text>
          <text class="btn-text">下载保存</text>
        </button>

        <button class="action-btn share-btn" bindtap="onShareToFriend">
          <text class="btn-icon">🔗</text>
          <text class="btn-text">分享好友</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{allLoaded}}">
    <text class="section-title">相关推荐</text>
    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{relatedEmojis}}"
        wx:key="id"
        bindtap="onRelatedTap"
        data-id="{{item.id}}"
      >
        <!-- 表情包图片 -->
        <view class="emoji-image-container">
          <image
            class="emoji-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
        </view>

        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <text class="emoji-category">{{item.category}}</text>

          <!-- 数据统计区域 -->
          <view class="emoji-stats">
            <view class="stat-item" bindtap="onRelatedLike" data-id="{{item.id}}">
              <text class="stat-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
              <text class="stat-number">{{item.likesText}}</text>
            </view>
            <view class="stat-item" bindtap="onRelatedCollect" data-id="{{item.id}}">
              <text class="stat-icon">{{item.isCollected ? '⭐' : '☆'}}</text>
              <text class="stat-number">{{item.collectionsText}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>