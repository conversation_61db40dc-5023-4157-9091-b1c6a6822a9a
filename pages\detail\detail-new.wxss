/* pages/detail/detail-new.wxss - 重构版表情包详情页样式 */

.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* ========== 加载状态 ========== */
.loading-container {
  padding: 40rpx;
}

.loading-skeleton {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.skeleton-image {
  width: 100%;
  height: 500rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.skeleton-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skeleton-title {
  height: 60rpx;
  width: 60%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-meta {
  height: 40rpx;
  width: 80%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-tags {
  display: flex;
  gap: 20rpx;
}

.skeleton-tag {
  height: 50rpx;
  width: 120rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 25rpx;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ========== 错误状态 ========== */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40rpx;
}

.error-content {
  text-align: center;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.error-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #FF8C00;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* ========== 详情内容 ========== */
.detail-content {
  padding: 0 40rpx;
}

/* 表情包图片 */
.emoji-image-container {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500rpx;
}

.emoji-image {
  max-width: 100%;
  max-height: 500rpx;
  border-radius: 20rpx;
}

/* 表情包信息卡片 */
.emoji-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.emoji-header {
  margin-bottom: 30rpx;
}

/* 描述信息 */
.emoji-meta {
  margin-top: 16rpx;
}

.emoji-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 信息行 */
.info-row {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin: 24rpx 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-icon {
  font-size: 32rpx;
  width: 40rpx;
  text-align: center;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  min-width: 80rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.emoji-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.emoji-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
}

.meta-item {
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
}

.meta-item.hot {
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: white;
}

/* 分类和标签区域 - 按截图样式重新设计 */
.category-tags-section {
  margin-bottom: 30rpx;
}

/* 第一行：分类标签 - 紫色背景 */
.category-row {
  margin-bottom: 16rpx;
}

.category-tag {
  display: inline-block;
  background: #8B5CF6;
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  line-height: 1.2;
}

/* 第二行：标签 - 灰色边框 */
.hashtags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.hashtag-item {
  display: inline-block;
  background: white;
  border: 2rpx solid #E5E7EB;
  color: #6B7280;
  font-size: 24rpx;
  font-weight: 400;
  padding: 6rpx 12rpx;
  border-radius: 14rpx;
  line-height: 1.2;
  transition: all 0.2s ease;
}

.hashtag-item:active {
  background: #F9FAFB;
  border-color: #D1D5DB;
  transform: scale(0.98);
}

/* 统计数据 - 防抖动优化 */
.stats-container {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  border-top: 1rpx solid #f0f0f0;
  /* 使用CSS contain防止布局重排影响其他元素 */
  contain: layout style;
}

.stat-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  /* 固定宽度防止文本变化导致布局重排 */
  width: 120rpx;
  min-width: 120rpx;
}

.stat-icon {
  font-size: 32rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  /* 固定宽度和居中对齐，防止数字变化导致抖动 */
  width: 80rpx;
  text-align: center;
  /* 使用等宽字体确保数字宽度一致 */
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  /* 防止数字变化时的布局跳动 */
  min-height: 44rpx;
  line-height: 44rpx;
  /* 添加平滑过渡动画，让数字变化更自然 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 防止文本选择 */
  user-select: none;
  /* 硬件加速 */
  transform: translateZ(0);
  will-change: contents;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}

/* ========== 操作按钮 ========== */
.action-buttons {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 36rpx 24rpx;
  margin: 0 32rpx 40rpx 32rpx;
  box-shadow:
    0 12rpx 40rpx rgba(0, 0, 0, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  /* 防止上方统计数据变化影响按钮布局 */
  contain: layout style;
  /* 确保按钮容器位置稳定 */
  position: relative;
  transform: translateZ(0); /* <--- AI添加此行：隔离按钮容器的渲染层 */
}

/* ========== 新的下载操作按钮 ========== */
.download-actions {
  display: flex;
  gap: 20rpx;
  margin: 0 32rpx 40rpx 32rpx;
}

.download-btn, .share-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: transform 0.2s ease;
  position: relative;
  overflow: hidden;
}

.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.secondary-btn {
  background: white;
  color: #333;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.download-btn:hover, .share-btn:hover {
  transform: translateY(-4rpx);
}

.btn-hover {
  transform: scale(0.98);
  opacity: 0.8;
}

.action-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 28rpx 20rpx;
  margin: 0 6rpx;
  border-radius: 24rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s ease; /* <--- AI替换：优化按钮过渡动画 */
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.15);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 24rpx;
}

.action-btn:active {
  transform: scale(0.94) translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12), 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

.action-btn:active::before {
  opacity: 1;
}

.btn-icon {
  font-size: 36rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.action-btn:active .btn-icon {
  transform: scale(0.9);
}

.btn-text {
  font-size: 24rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 现代化按钮样式 */
.like-btn {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a80 50%, #ffab91 100%);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.like-btn.liked {
  background: linear-gradient(135deg, #e91e63 0%, #f44336 50%, #ff5722 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(233, 30, 99, 0.3), 0 2rpx 8rpx rgba(233, 30, 99, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.collect-btn {
  background: linear-gradient(135deg, #ffc107 0%, #ffb300 50%, #ffa000 100%);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.collect-btn.collected {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 50%, #ef6c00 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(255, 152, 0, 0.3), 0 2rpx 8rpx rgba(255, 152, 0, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.download-btn {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 50%, #03a9f4 100%);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.download-btn:active {
  background: linear-gradient(135deg, #0288d1 0%, #0277bd 50%, #01579b 100%);
}

.share-btn {
  background: linear-gradient(135deg, #ab47bc 0%, #9c27b0 50%, #8e24aa 100%);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.share-btn:active {
  background: linear-gradient(135deg, #7b1fa2 0%, #6a1b9a 50%, #4a148c 100%);
}

/* ========== 相关推荐 - 与首页样式完全一致 ========== */

/* 标题区域 */
.related-header {
  padding: 0;
  margin: 40rpx 0 30rpx 0;
}

.related-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 表情包列表 - 使用外层detail-content的40rpx间距 */
.related-emoji-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 0;
  margin-bottom: 40rpx;
}

.related-emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.related-emoji-item:active {
  transform: scale(0.98);
}

.related-emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
}

.related-emoji-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-emoji-info {
  padding: 24rpx;
}

.related-emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 标签区域样式 - 与首页一致 */
.related-emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.related-tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.related-tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}
