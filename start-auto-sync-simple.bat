@echo off
echo Starting Auto-Sync System...
echo.

echo Stopping existing processes...
taskkill /f /im node.exe 2>nul

echo Creating directories...
if not exist "admin-unified\data" mkdir "admin-unified\data"
if not exist "miniprogram-data" mkdir "miniprogram-data"

echo Starting admin server (port 8001)...
cd admin-unified
start /b node fixed-server.js
cd ..

echo Starting miniprogram data service (port 8003)...
start /b node miniprogram-data-service.js

echo Waiting for services to start...
timeout /t 5 /nobreak >nul

echo Opening admin panel...
start http://localhost:8001/index-fixed.html

echo.
echo ========================================
echo AUTO-SYNC SYSTEM STARTED!
echo ========================================
echo.
echo Admin Panel: http://localhost:8001/index-fixed.html
echo MiniProgram API: http://localhost:8003/wx-cloud-api
echo Data Stats: http://localhost:8003/api/stats
echo.
echo Data Flow:
echo Admin Operations -> JSON Files -> Auto Sync -> MiniProgram Format
echo.
echo Now you can:
echo 1. Add/Edit/Delete data in admin panel
echo 2. Data automatically syncs to miniprogram-data/ folder
echo 3. MiniProgram can read data via http://localhost:8003
echo.
echo Press any key to exit...
pause
