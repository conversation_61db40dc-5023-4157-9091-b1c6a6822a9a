<!-- 登录弹窗组件 -->
<view class="login-modal" wx:if="{{visible}}" catchtouchmove="preventTouchMove">
  <!-- 遮罩层 -->
  <view class="modal-mask" bindtap="onMaskTap"></view>
  
  <!-- 弹窗内容 -->
  <view class="modal-content">
    <!-- 关闭按钮 -->
    <view class="close-btn" bindtap="onClose">
      <text class="close-icon">×</text>
    </view>
    
    <!-- 登录内容 -->
    <view class="login-content">
      <!-- Logo和标题 -->
      <view class="login-header">
        <image class="app-logo" src="/images/logo.png" mode="aspectFit"></image>
        <text class="app-title">表情包小程序</text>
        <text class="login-subtitle">登录后可以点赞、收藏和下载表情包</text>
      </view>
      
      <!-- 登录按钮 -->
      <view class="login-actions">
        <button 
          class="login-btn {{isLoading ? 'loading' : ''}}" 
          bindtap="onLogin"
          disabled="{{isLoading}}"
        >
          <text wx:if="{{!isLoading}}">微信一键登录</text>
          <text wx:else>登录中...</text>
        </button>
        
        <!-- 登录说明 -->
        <view class="login-tips">
          <text class="tip-text">登录即表示同意</text>
          <text class="link-text" bindtap="onPrivacyTap">《隐私政策》</text>
          <text class="tip-text">和</text>
          <text class="link-text" bindtap="onTermsTap">《用户协议》</text>
        </view>
      </view>
      
      <!-- 错误提示 -->
      <view class="error-message" wx:if="{{errorMessage}}">
        <text class="error-text">{{errorMessage}}</text>
      </view>
    </view>
  </view>
</view>
