/**
 * 错误处理组件
 * 提供用户友好的错误提示和重试机制
 */

Component({
  properties: {
    // 错误类型
    errorType: {
      type: String,
      value: 'default'
    },
    // 错误消息
    errorMessage: {
      type: String,
      value: ''
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      value: true
    },
    // 是否显示详细信息按钮
    showDetails: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    // 是否显示详细信息
    showDetailInfo: false,
    // 重试次数
    retryCount: 0,
    // 错误图标映射
    errorIcons: {
      network: '📶',
      api: '🔧',
      javascript: '⚠️',
      user: '👤',
      system: '💻',
      validation: '📝',
      default: '❌'
    },
    // 错误标题映射
    errorTitles: {
      network: '网络连接异常',
      api: '服务异常',
      javascript: '应用异常',
      user: '操作失败',
      system: '系统异常',
      validation: '输入错误',
      default: '出现错误'
    },
    // 错误建议映射
    errorSuggestions: {
      network: [
        '检查网络连接是否正常',
        '尝试切换网络环境',
        '稍后重试'
      ],
      api: [
        '服务暂时不可用',
        '请稍后重试',
        '如问题持续，请联系客服'
      ],
      javascript: [
        '应用出现异常',
        '正在尝试自动恢复',
        '如问题持续，请重启应用'
      ],
      user: [
        '请检查操作是否正确',
        '确认输入信息无误',
        '重试操作'
      ],
      system: [
        '系统出现异常',
        '请重启应用',
        '如问题持续，请更新应用'
      ],
      validation: [
        '请检查输入信息',
        '确保所有必填项已填写',
        '检查格式是否正确'
      ],
      default: [
        '操作失败',
        '请重试',
        '如问题持续，请联系客服'
      ]
    }
  },

  methods: {
    /**
     * 重试操作
     */
    onRetry() {
      this.setData({
        retryCount: this.data.retryCount + 1
      })

      // 触发重试事件
      this.triggerEvent('retry', {
        retryCount: this.data.retryCount,
        errorType: this.properties.errorType
      })

      // 显示重试提示
      wx.showToast({
        title: '正在重试...',
        icon: 'loading',
        duration: 1500
      })
    },

    /**
     * 切换详细信息显示
     */
    toggleDetails() {
      this.setData({
        showDetailInfo: !this.data.showDetailInfo
      })
    },

    /**
     * 复制错误信息
     */
    copyErrorInfo() {
      const errorInfo = {
        type: this.properties.errorType,
        message: this.properties.errorMessage,
        timestamp: new Date().toISOString(),
        retryCount: this.data.retryCount
      }

      wx.setClipboardData({
        data: JSON.stringify(errorInfo, null, 2),
        success: () => {
          wx.showToast({
            title: '错误信息已复制',
            icon: 'success'
          })
        }
      })
    },

    /**
     * 联系客服
     */
    contactSupport() {
      // 触发联系客服事件
      this.triggerEvent('contact', {
        errorType: this.properties.errorType,
        errorMessage: this.properties.errorMessage
      })
    },

    /**
     * 关闭错误提示
     */
    onClose() {
      this.triggerEvent('close')
    },

    /**
     * 获取错误图标
     */
    getErrorIcon() {
      return this.data.errorIcons[this.properties.errorType] || this.data.errorIcons.default
    },

    /**
     * 获取错误标题
     */
    getErrorTitle() {
      return this.data.errorTitles[this.properties.errorType] || this.data.errorTitles.default
    },

    /**
     * 获取错误建议
     */
    getErrorSuggestions() {
      return this.data.errorSuggestions[this.properties.errorType] || this.data.errorSuggestions.default
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时的逻辑
      console.log('错误处理组件初始化:', this.properties.errorType)
    }
  }
})
