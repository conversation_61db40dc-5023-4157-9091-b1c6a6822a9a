/* pages/search/search.wxss */
.container {
  padding: 0;
  padding-bottom: 120rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 搜索框样式 - 与首页保持一致 */
.search-header {
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 40rpx;
}

.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 2rpx solid #e8e8e8;
  border-radius: 60rpx;
  padding: 20rpx 30rpx;
  margin-right: 24rpx;
  height: 80rpx;
  box-sizing: border-box;
}

.search-input-container:focus-within {
  border-color: #d0d0d0;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  color: #c0c0c0;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.search-input::placeholder {
  color: #c0c0c0;
}

.search-btn {
  background: #8B5CF6 !important;
  color: white !important;
  border-radius: 50rpx !important;
  border: none !important;
  padding: 0 !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  height: 80rpx !important;
  width: 120rpx !important;
  line-height: 80rpx !important;
  text-align: center !important;
  box-sizing: border-box !important;
  margin: 0 !important;
}

.search-btn::after {
  border: none !important;
}

/* 内容区域 */
.content-area {
  padding: 0 32rpx;
}

/* 热门标签 */
.hot-tags-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-tags-section .tag-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 30rpx;
  padding: 16rpx 32rpx;
  transition: all 0.3s ease;
}

.hot-tags-section .tag-item:active {
  transform: scale(0.95);
}

.tag-text {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}

/* 搜索结果 */
.search-results {
  margin-bottom: 40rpx;
  position: relative;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 0 32rpx;
}

.results-info {
  flex: 1;
}

.results-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.results-count {
  font-size: 24rpx;
  color: #666;
}

.search-time {
  color: #999;
  font-size: 22rpx;
}

/* 排序功能样式 */
.sort-container {
  position: relative;
  margin-left: 20rpx;
}

.sort-trigger {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  min-width: 160rpx;
  justify-content: center;
  transition: all 0.3s ease;
}

.sort-trigger:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.sort-text {
  font-size: 26rpx;
  color: #495057;
  margin-right: 8rpx;
}

.sort-icon {
  font-size: 20rpx;
  color: #6c757d;
  transition: transform 0.3s ease;
}

.sort-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid #e9ecef;
  min-width: 200rpx;
  z-index: 1000;
  opacity: 0;
  transform: translateY(-10rpx);
  transition: all 0.3s ease;
  pointer-events: none;
}

.sort-menu.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.sort-option {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f8f9fa;
  transition: background 0.2s ease;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option:active {
  background: #f8f9fa;
}

.sort-option.active {
  background: #e3f2fd;
  color: #1976d2;
}

.sort-option .sort-icon {
  margin-right: 16rpx;
  font-size: 28rpx;
}

.sort-option .sort-label {
  flex: 1;
  font-size: 28rpx;
}

.sort-option .sort-check {
  color: #1976d2;
  font-size: 24rpx;
  font-weight: bold;
}

.sort-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.sort-mask.show {
  opacity: 1;
  pointer-events: auto;
}

/* 表情包网格样式 */
/* 表情包列表样式 - 与首页保持一致 */
.emoji-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.emoji-item {
  display: flex;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
}

.emoji-image {
  width: 100%;
  height: 100%;
}



.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.emoji-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.emoji-category {
  font-size: 22rpx;
  font-weight: 500;
}

.emoji-stats {
  display: flex;
  align-items: center;
}

.heart-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
}

.stats-text {
  font-size: 22rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
}

/* 搜索历史 */
.search-history {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.clear-btn {
  font-size: 24rpx;
  color: #8B5CF6;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  font-size: 24rpx;
  margin-right: 16rpx;
  color: #666;
}

.history-text {
  font-size: 28rpx;
  color: #333;
}

/* 重写表情包样式以匹配首页 */
.emoji-list .emoji-image-container {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.emoji-list .emoji-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0;
}

.emoji-list .emoji-title {
  font-size: 32rpx;
  margin-bottom: 12rpx;
}

.emoji-list .emoji-category {
  font-size: 24rpx;
  color: #8B5CF6;
  margin-bottom: 20rpx;
}

/* 操作按钮区域 */
.emoji-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.active {
  background: #fff2f2;
}

.action-btn.active .action-icon {
  transform: scale(1.2);
}

.action-icon {
  font-size: 24rpx;
  transition: transform 0.3s ease;
}

.action-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}