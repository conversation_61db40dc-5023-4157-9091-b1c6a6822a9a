<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.0 一键部署助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 600;
        }

        .header p {
            margin: 15px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }

        .step-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .step-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            font-size: 18px;
            margin-right: 15px;
        }

        .step-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #374151;
        }

        .command-box {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 15px 0;
            position: relative;
            overflow-x: auto;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #48bb78;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .copy-btn:hover {
            background: #38a169;
        }

        .copy-btn.copied {
            background: #38a169;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #92400e;
        }

        .warning-box {
            background: #fef2f2;
            border: 2px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning-title {
            font-weight: 600;
            color: #dc2626;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .success-box {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .success-title {
            font-weight: 600;
            color: #16a34a;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .info-box {
            background: #eff6ff;
            border: 2px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .info-title {
            font-weight: 600;
            color: #1d4ed8;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .big-button {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }

        .big-button:hover {
            background: #5a67d8;
        }

        .big-button.success {
            background: #10b981;
        }

        .big-button.success:hover {
            background: #059669;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 10px 0;
            border-bottom: 1px solid #f3f4f6;
            font-size: 16px;
        }

        .checklist li:before {
            content: "☐ ";
            margin-right: 12px;
            font-size: 18px;
            color: #6b7280;
        }

        .checklist li.done:before {
            content: "✅ ";
        }

        .input-group {
            margin: 20px 0;
        }

        .input-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            font-size: 16px;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
        }

        .final-summary {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }

        .final-title {
            font-size: 24px;
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 V1.0 一键部署助手</h1>
        <p>只需复制粘贴4个命令，3分钟完成部署</p>
    </div>

    <!-- 准备工作 -->
    <div class="step-card">
        <div class="step-title">
            <span class="step-number">准备</span>
            开始前的准备工作
        </div>

        <div class="info-box">
            <div class="info-title">📋 您需要准备的账号</div>
            <ul class="checklist">
                <li onclick="toggleDone(this)">腾讯云账号 (免费注册: https://cloud.tencent.com/)</li>
                <li onclick="toggleDone(this)">微信小程序账号 (免费注册: https://mp.weixin.qq.com/)</li>
            </ul>
        </div>

        <div class="warning-box">
            <div class="warning-title">⚠️ 重要提醒</div>
            <p>如果您还没有这两个账号，请先注册。注册是免费的，只需要几分钟。</p>
            <p><strong>腾讯云</strong>: 用于部署后端服务</p>
            <p><strong>微信小程序</strong>: 用于发布小程序</p>
        </div>

        <button class="big-button" onclick="showStep1()">✅ 账号已准备好，开始部署</button>
    </div>

    <!-- 步骤1: 安装工具 -->
    <div class="step-card" id="step1" style="display: none;">
        <div class="step-title">
            <span class="step-number">1</span>
            安装部署工具 (1分钟)
        </div>

        <p style="font-size: 18px; color: #374151;">复制下面的命令，粘贴到您的<strong>命令行工具</strong>中运行：</p>

        <div class="info-box">
            <div class="info-title">💡 如何打开命令行工具？</div>
            <p><strong>Windows用户</strong>: 按 Win+R，输入 cmd，回车</p>
            <p><strong>Mac用户</strong>: 按 Cmd+空格，输入 terminal，回车</p>
        </div>

        <div class="command-box">
npm install -g @cloudbase/cli
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <p>安装完成后，运行这个命令验证安装：</p>

        <div class="command-box">
tcb --version
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <div class="success-box">
            <div class="success-title">✅ 验证成功</div>
            <p>如果看到版本号（比如 1.x.x），说明安装成功！</p>
        </div>

        <button class="big-button" onclick="showStep2()">工具已安装，继续下一步</button>
    </div>

    <!-- 步骤2: 登录腾讯云 -->
    <div class="step-card" id="step2" style="display: none;">
        <div class="step-title">
            <span class="step-number">2</span>
            登录腾讯云 (30秒)
        </div>

        <p style="font-size: 18px; color: #374151;">运行这个命令登录您的腾讯云账号：</p>

        <div class="command-box">
tcb login
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <div class="info-box">
            <div class="info-title">💡 登录过程说明</div>
            <p>1. 运行命令后会自动打开浏览器</p>
            <p>2. 在浏览器中登录您的腾讯云账号</p>
            <p>3. 授权成功后回到命令行</p>
        </div>

        <p>登录成功后，运行这个命令验证：</p>

        <div class="command-box">
tcb env:list
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <div class="success-box">
            <div class="success-title">✅ 登录成功</div>
            <p>如果看到环境列表（可能是空的），说明登录成功！</p>
        </div>

        <button class="big-button" onclick="showStep3()">已登录成功，继续下一步</button>
    </div>

    <!-- 步骤3: 创建环境 -->
    <div class="step-card" id="step3" style="display: none;">
        <div class="step-title">
            <span class="step-number">3</span>
            创建云开发环境 (1分钟)
        </div>

        <p style="font-size: 18px; color: #374151;">运行这个命令创建一个新的云开发环境：</p>

        <div class="command-box">
tcb env:create emoji-v1 --alias "表情包小程序V1.0"
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <div class="info-box">
            <div class="info-title">⏳ 创建过程</div>
            <p>创建环境需要1-2分钟，请耐心等待...</p>
            <p>创建成功后会显示环境ID，类似：<code>emoji-v1-xxxxx</code></p>
        </div>

        <p>创建完成后，记录下您的环境ID：</p>

        <div class="input-group">
            <label class="input-label">您的环境ID (请填写):</label>
            <input type="text" class="input-field" id="envId" placeholder="例如: emoji-v1-5g6pvnpl88dc0142">
        </div>

        <div class="warning-box">
            <div class="warning-title">📝 重要</div>
            <p>请务必记录下环境ID，下一步需要使用！</p>
        </div>

        <button class="big-button" onclick="showStep4()">环境已创建，继续下一步</button>
    </div>

    <!-- 步骤4: 一键部署 -->
    <div class="step-card" id="step4" style="display: none;">
        <div class="step-title">
            <span class="step-number">4</span>
            一键部署系统 (1分钟)
        </div>

        <p style="font-size: 18px; color: #374151;">现在我们来部署V1.0系统。首先进入项目目录：</p>

        <div class="command-box">
cd v1.0-project
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <p>然后运行一键部署命令：</p>

        <div class="command-box" id="deployCommand">
TCB_ENV=<span class="highlight">your-env-id</span> node scripts/deploy.js
<button class="copy-btn" onclick="copyCommand(this)">复制</button>
        </div>

        <button class="big-button" onclick="generateDeployCommand()">🔧 自动生成我的部署命令</button>

        <div class="info-box">
            <div class="info-title">🚀 部署过程</div>
            <p>部署过程包括：</p>
            <ul>
                <li>✅ 部署3个云函数 (loginAPI, webAdminAPI, dataAPI)</li>
                <li>✅ 初始化数据库</li>
                <li>✅ 部署管理后台</li>
                <li>✅ 配置安全规则</li>
            </ul>
            <p>整个过程大约需要1-2分钟。</p>
        </div>

        <div class="success-box">
            <div class="success-title">🎉 部署成功标志</div>
            <p>看到 <strong>"✅ V1.0系统部署完成！"</strong> 就说明部署成功了！</p>
        </div>

        <button class="big-button success" onclick="showFinal()">部署已完成，查看结果</button>
    </div>

    <!-- 最终结果 -->
    <div class="final-summary" id="final" style="display: none;">
        <div class="final-title">🎉 恭喜！V1.0系统部署成功</div>

        <div class="success-box">
            <div class="success-title">✅ 已完成的部署内容</div>
            <ul class="checklist">
                <li class="done">云开发环境创建</li>
                <li class="done">3个云函数部署</li>
                <li class="done">数据库初始化</li>
                <li class="done">管理后台部署</li>
                <li class="done">安全配置</li>
            </ul>
        </div>

        <div class="info-box">
            <div class="info-title">🔗 您的系统地址</div>
            <p><strong>管理后台地址</strong>: https://<span id="finalEnvId">your-env-id</span>.service.tcloudbase.com</p>
            <p><strong>管理员账号</strong>: admin</p>
            <p><strong>管理员密码</strong>: admin123456 (请登录后修改)</p>
        </div>

        <div class="warning-box">
            <div class="warning-title">🔒 安全提醒</div>
            <p>请立即登录管理后台修改默认密码！</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="big-button success" onclick="openAdmin()">🖥️ 打开管理后台</button>
            <button class="big-button" onclick="openVerification()">🔍 验证系统状态</button>
        </div>

        <div class="info-box">
            <div class="info-title">📱 下一步：配置小程序</div>
            <p>1. 打开微信开发者工具</p>
            <p>2. 导入 <code>miniprogram</code> 文件夹</p>
            <p>3. 在 <code>app.js</code> 第26行填入您的环境ID: <strong><span id="finalEnvId2">your-env-id</span></strong></p>
            <p>4. 预览测试后上传发布</p>
        </div>
    </div>

    <script>
        function toggleDone(element) {
            element.classList.toggle('done');
        }

        function showStep1() {
            document.getElementById('step1').style.display = 'block';
            document.getElementById('step1').scrollIntoView({ behavior: 'smooth' });
        }

        function showStep2() {
            document.getElementById('step2').style.display = 'block';
            document.getElementById('step2').scrollIntoView({ behavior: 'smooth' });
        }

        function showStep3() {
            document.getElementById('step3').style.display = 'block';
            document.getElementById('step3').scrollIntoView({ behavior: 'smooth' });
        }

        function showStep4() {
            const envId = document.getElementById('envId').value.trim();
            if (!envId) {
                alert('请先填写您的环境ID！');
                return;
            }
            document.getElementById('step4').style.display = 'block';
            document.getElementById('step4').scrollIntoView({ behavior: 'smooth' });
        }

        function generateDeployCommand() {
            const envId = document.getElementById('envId').value.trim();
            if (!envId) {
                alert('请先在步骤3中填写您的环境ID！');
                return;
            }

            const deployCommand = document.getElementById('deployCommand');
            deployCommand.innerHTML = `TCB_ENV=${envId} node scripts/deploy.js
<button class="copy-btn" onclick="copyCommand(this)">复制</button>`;

            alert('✅ 部署命令已生成！请复制运行。');
        }

        function showFinal() {
            const envId = document.getElementById('envId').value.trim();
            document.getElementById('finalEnvId').textContent = envId;
            document.getElementById('finalEnvId2').textContent = envId;
            
            document.getElementById('final').style.display = 'block';
            document.getElementById('final').scrollIntoView({ behavior: 'smooth' });
        }

        function copyCommand(button) {
            const commandBox = button.parentElement;
            const text = commandBox.textContent.replace('复制', '').trim();
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.classList.add('copied');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(() => {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                button.textContent = '已复制!';
                setTimeout(() => {
                    button.textContent = '复制';
                }, 2000);
            });
        }

        function openAdmin() {
            const envId = document.getElementById('envId').value.trim();
            if (envId) {
                window.open(`https://${envId}.service.tcloudbase.com`, '_blank');
            }
        }

        function openVerification() {
            window.open('链路打通验证.html', '_blank');
        }
    </script>
</body>
</html>
