# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WeChat Mini Program for emoji/sticker sharing built with WeChat Cloud Development. The project includes:

- **Frontend**: WeChat Mini Program (WXML, WXSS, JS)
- **Backend**: WeChat Cloud Functions (Node.js)
- **Database**: WeChat Cloud Database (MongoDB)
- **Storage**: WeChat Cloud Storage
- **Admin Panel**: Web-based management dashboard for content and user management

## Development Commands

Since this is a WeChat Mini Program, development is done through WeChat Developer Tools:

```bash
# Note: There are no npm scripts for build/dev
# Development is done through WeChat Developer Tools
npm run dev   # Shows message to use WeChat Developer Tools
npm run build # Shows message to use WeChat Developer Tools
```

**Development Process:**
1. Open project in WeChat Developer Tools
2. Configure cloud environment ID in `app.js:53`
3. Deploy cloud functions via right-click → "Upload and Deploy"
4. Use WeChat Developer Tools' preview and debugging features

**Admin Panel Setup:**
1. Access admin panel via `admin/index.html` or `admin-panel-standalone/index.html`
2. Configure cloud environment ID in admin config files
3. Set up admin permissions using admin management tools
4. Deploy admin panel to web server for remote access

## Architecture

### Core Application Structure

**Entry Point**: `app.js`
- Initializes cloud development environment
- Sets up global state management via `StateManager`
- Configures authentication via `AuthManager`
- Handles data synchronization via `DataSync`
- Sets up performance monitoring via `PerformanceMonitor`

**Key Configuration**:
- `app.json`: Mini Program configuration, pages, tabBar
- `project.config.json`: WeChat Developer Tools configuration
- Cloud Environment ID: `cloud1-5g6pvnpl88dc0142` (configured in `app.js:54`)

### Page Structure

**Main Pages:**
- `pages/index/` - Home page with emoji list
- `pages/search/` - Search functionality
- `pages/category/` - Category browsing
- `pages/profile/` - User profile and settings
- `pages/detail/` - Emoji detail view
- `pages/my-likes/` - User's liked emojis
- `pages/my-collections/` - User's collected emojis
- `pages/download-history/` - Download history

### Cloud Functions (`cloudfunctions/`)

**Authentication & User Management:**
- `login/` - User login handling
- `getUserStats/` - User statistics
- `getUserLikes/` - User liked items
- `getUserCollections/` - User collections

**Content Management:**
- `getEmojiList/` - Fetch emoji list with pagination
- `getEmojiDetail/` - Get specific emoji details
- `getCategories/` - Category data
- `getBanners/` - Banner/carousel data
- `searchEmojis/` - Search functionality

**User Actions:**
- `toggleLike/` - Like/unlike functionality
- `toggleCollect/` - Collect/uncollect functionality
- `trackAction/` - User behavior tracking

**System:**
- `admin/` - Admin management API
- `dataSync/` - Data synchronization
- `uploadFile/` - File upload handling

### Admin Panel Management (`admin/` & `admin-panel-standalone/`)

**Admin Panel Features:**
- `index.html` - Main admin dashboard interface
- `config.js` - Admin panel configuration
- `setup-admin.html` - Admin setup tools
- `get-openid.html` - OpenID retrieval tool
- `create-admin-directly.html` - Direct admin creation

**Admin Functions:**
- 📊 Data overview and statistics dashboard
- ✅ Content review and approval system
- 😊 Emoji/sticker management (add, edit, delete)
- 📁 Category management with custom icons
- 👥 User management and permissions
- 🔐 Role-based access control
- 📱 Mobile-responsive design
- 🔧 System configuration tools

**Admin Documentation:**
- `README.md` - Admin panel overview
- `DEPLOYMENT.md` - Deployment guide
- `QUICK-START.md` - Quick start guide
- `setup-admin.md` - Admin setup instructions
- `docs/` - Detailed documentation

### Utility Modules (`utils/`)

**Core Managers:**
- `authManager.js` - User authentication and login state management
- `dataSync.js` - Cloud data synchronization (likes, collections, downloads)
- `stateManager.js` - Global state management
- `performanceMonitor.js` - Performance monitoring and optimization

**Data & Cloud Services:**
- `cloudDataService.js` - Cloud database operations
- `dataManager.js` - Local data management
- `cloudDiagnostic.js` - Cloud service diagnostics

**Testing & Diagnostics:**
- `loginTest.js` - Login functionality testing
- `diagnosticSystem.js` - System diagnostic tools
- `testData.js` - Test data generation

### Data Architecture

**Local Storage Strategy:**
- User preferences and temporary data stored locally
- Automatic sync to cloud when user logs in
- Offline-first approach with cloud backup

**Cloud Database Collections:**
- `emojis` - Emoji/sticker metadata
- `categories` - Category definitions
- `user_likes` - User like records
- `user_collections` - User collection records
- `users` - User profile data

**State Management:**
- Global state via `app.js` globalData
- Local state managers for complex operations
- Reactive updates via listener patterns

## Key Implementation Details

### Authentication Flow
1. User authentication handled by `AuthManager` (authManager.js:4-50)
2. Login state persists for 7 days in local storage
3. Cloud synchronization triggers automatically on login
4. Global login listeners notify app components of state changes

### Data Synchronization
1. Local-first storage for offline capability
2. Background sync via `DataSync` (dataSync.js:6-50)
3. Conflict resolution prioritizes cloud data
4. Queued operations for reliable sync

### Performance Optimizations
1. Performance monitoring via `PerformanceMonitor`
2. Lazy loading for emoji lists
3. Image optimization and caching
4. Efficient state update patterns

## Cloud Development Setup

**Environment Configuration:**
```javascript
// app.js - Update this with your cloud environment ID
wx.cloud.init({
  env: 'cloud1-0g0ib8nh0a0e5c5f', // Replace with your env ID
  traceUser: true
})
```

**Cloud Function Deployment:**
- Right-click each function folder in `cloudfunctions/`
- Select "Upload and Deploy: Install Dependencies"
- Wait for deployment completion

**Database Setup:**
- Create collections via WeChat Cloud Console
- Configure database permissions per collection
- Import initial data if needed

## Testing

**Manual Testing:**
- Use WeChat Developer Tools simulator
- Test on real devices via preview QR code
- Check cloud function logs in console

**Diagnostic Tools:**
- `utils/diagnosticSystem.js` - System health checks
- `utils/loginTest.js` - Authentication testing
- `utils/cloudDiagnostic.js` - Cloud service testing

## Admin Panel Usage

### Quick Start
1. **Access Admin Panel**: Open `admin/index.html` in browser
2. **Configure Environment**: Update cloud environment ID in config files
3. **Set Admin Permissions**: Use admin setup tools to configure permissions
4. **Start Managing**: Access dashboard to manage content and users

### Admin Features
- **Dashboard**: Real-time statistics and system overview
- **Content Management**: Approve/reject emojis, manage categories
- **User Management**: View users, manage permissions and roles
- **System Tools**: Configuration, diagnostics, and maintenance

### Deployment Options
- **Local Development**: Direct file access via browser
- **Web Server**: Deploy to any web server with HTTPS
- **Standalone Version**: Use `admin-panel-standalone/` for independent deployment

### Security
- Role-based access control (Super Admin, Admin, Moderator)
- OpenID-based authentication
- Session management and timeout
- Secure API communication with cloud functions

## Important Notes

- This project uses WeChat Mini Program native framework (not uni-app or other frameworks)
- All cloud operations require proper WeChat Cloud Development setup
- User authentication uses WeChat's built-in login system
- File uploads go through WeChat Cloud Storage
- Database operations use WeChat Cloud Database (MongoDB-like)
- Admin panel requires proper admin permissions setup via OpenID configuration
- Admin panel can be deployed independently of the mini program