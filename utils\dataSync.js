// utils/dataSync.js - 用户数据云端同步管理器
// 处理用户点赞、收藏、下载记录的云端同步

const { AuthManager } = require('./authManager.js')

const DataSync = {
  // 同步状态
  isSyncing: false,
  syncQueue: [],
  
  /**
   * 初始化数据同步
   */
  init() {
    console.log('DataSync 初始化')
    
    // 监听登录状态变化
    AuthManager.addLoginListener((isLoggedIn) => {
      if (isLoggedIn) {
        // 登录后尝试同步数据
        this.syncCloudDataToLocal()
      }
    })
  },
  
  /**
   * 同步本地数据到云端
   */
  async syncLocalDataToCloud() {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化，跳过数据同步到云端')
      return false
    }
    
    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化，跳过数据同步到云端')
      return false
    }

    if (!app.globalData.isLoggedIn) {
      console.log('💾 用户未登录，跳过数据同步到云端')
      return false
    }

    try {
      // 获取本地数据
      const likedEmojis = wx.getStorageSync('likedEmojis') || []
      const collectedEmojis = wx.getStorageSync('collectedEmojis') || []
      const downloadedEmojis = wx.getStorageSync('downloadedEmojis') || []
      const downloadTimes = wx.getStorageSync('downloadTimes') || {}

      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          likedEmojis: likedEmojis,
          collectedEmojis: collectedEmojis,
          downloadedEmojis: downloadedEmojis,
          downloadTimes: downloadTimes
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 本地数据同步到云端成功')
        return true
      } else {
        console.log('❌ 本地数据同步到云端失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 本地数据同步到云端异常:', error)
      return false
    }
  },
  
  /**
   * 从云端同步数据到本地
   */
  async syncCloudDataToLocal() {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化，跳过从云端同步数据')
      return false
    }
    
    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化，跳过从云端同步数据')
      return false
    }

    if (!app.globalData.isLoggedIn) {
      console.log('💾 用户未登录，跳过从云端同步数据')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          action: 'getAllUserData'
        }
      })

      if (res.result && res.result.success) {
        const userData = res.result.data
        
        // 保存到本地存储
        if (userData.likedEmojis) {
          wx.setStorageSync('likedEmojis', userData.likedEmojis)
        }
        if (userData.collectedEmojis) {
          wx.setStorageSync('collectedEmojis', userData.collectedEmojis)
        }
        if (userData.downloadedEmojis) {
          wx.setStorageSync('downloadedEmojis', userData.downloadedEmojis)
        }
        if (userData.downloadTimes) {
          wx.setStorageSync('downloadTimes', userData.downloadTimes)
        }
        
        console.log('✅ 云端数据同步到本地成功')
        return true
      } else {
        console.log('❌ 云端数据同步到本地失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 云端数据同步到本地异常:', error)
      return false
    }
  },
  
  /**
   * 同步点赞数据
   */
  async syncLikedData(likedEmojis) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log('💾 云端不可用，跳过点赞数据同步')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'syncLikedData',
          likedEmojis: likedEmojis
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 点赞数据同步成功')
        return true
      } else {
        console.log('❌ 点赞数据同步失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 点赞数据同步异常:', error)
      return false
    }
  },
  
  /**
   * 同步收藏数据
   */
  async syncCollectedData(collectedEmojis) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log('💾 云端不可用，跳过收藏数据同步')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'syncCollectedData',
          collectedEmojis: collectedEmojis
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 收藏数据同步成功')
        return true
      } else {
        console.log('❌ 收藏数据同步失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 收藏数据同步异常:', error)
      return false
    }
  },
  
  /**
   * 同步下载数据
   */
  async syncDownloadData(downloadedEmojis, downloadTimes) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log('💾 云端不可用，跳过下载数据同步')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'syncDownloadData',
          downloadedEmojis: downloadedEmojis,
          downloadTimes: downloadTimes
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 下载数据同步成功')
        return true
      } else {
        console.log('❌ 下载数据同步失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 下载数据同步异常:', error)
      return false
    }
  },
  
  /**
   * 获取云端用户数据
   */
  async getCloudUserData() {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化')
      return { success: false, error: 'App 未初始化' }
    }
    
    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化')
      return { success: false, error: '云开发未初始化' }
    }

    if (!app.globalData.isLoggedIn) {
      console.log('💾 用户未登录')
      return { success: false, error: '用户未登录' }
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          action: 'getAllUserData'
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 获取云端用户数据成功')
        return { success: true, data: res.result.data }
      } else {
        console.log('❌ 获取云端用户数据失败:', res.result?.error)
        return { success: false, error: res.result?.error }
      }
    } catch (error) {
      console.error('❌ 获取云端用户数据异常:', error)
      return { success: false, error: error.message }
    }
  },
  
  /**
   * 实时同步用户操作
   */
  async syncUserAction(action, emojiId, data = {}) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log(`💾 云端不可用，跳过实时同步 ${action} ${emojiId}`)
      return false
    }

    // 添加到同步队列
    this.syncQueue.push({ action, emojiId, data, timestamp: Date.now() })
    
    // 处理同步队列
    if (!this.isSyncing) {
      this.processSyncQueue()
    }
    
    return true
  },
  
  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return false
    }

    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log('💾 云端不可用，清空同步队列')
      this.syncQueue = []
      return false
    }

    this.isSyncing = true
    
    try {
      // 批量处理同步操作
      const operations = this.syncQueue.splice(0, 10) // 每次处理最多10个操作
      
      for (const operation of operations) {
        await this.processOperation(operation)
      }
      
      console.log(`✅ 同步队列处理完成，处理了 ${operations.length} 个操作`)
      return true
    } catch (error) {
      console.error('❌ 同步队列处理失败:', error)
      return false
    } finally {
      this.isSyncing = false
      
      // 如果还有未处理的操作，继续处理
      if (this.syncQueue.length > 0) {
        setTimeout(() => this.processSyncQueue(), 1000)
      }
    }
  },
  
  /**
   * 处理单个同步操作
   */
  async processOperation(operation) {
    const { action, emojiId, data } = operation
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: action,
          emojiId: emojiId,
          ...data
        }
      })

      if (res.result && res.result.success) {
        console.log(`✅ 同步操作成功 ${action} ${emojiId}`)
        return true
      } else {
        console.log(`❌ 同步操作失败 ${action} ${emojiId}:`, res.result?.error)
        return false
      }
    } catch (error) {
      console.error(`❌ 同步操作异常 ${action} ${emojiId}:`, error)
      return false
    }
  },
  
  /**
   * 清除本地数据
   */
  clearLocalData() {
    try {
      wx.removeStorageSync('likedEmojis')
      wx.removeStorageSync('collectedEmojis')
      wx.removeStorageSync('downloadedEmojis')
      wx.removeStorageSync('downloadTimes')
      wx.removeStorageSync('recentEmojis')
      
      console.log('本地数据已清除')
    } catch (error) {
      console.error('清除本地数据失败:', error)
    }
  },
  
  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isSyncing: this.isSyncing,
      queueLength: this.syncQueue.length,
      isLoggedIn: AuthManager.isLoggedIn
    }
  }
}

module.exports = {
  DataSync
}
