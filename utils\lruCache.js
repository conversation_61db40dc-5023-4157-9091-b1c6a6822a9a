/**
 * LRU (Least Recently Used) 缓存管理器
 * 实现智能的缓存淘汰策略，提升内存使用效率
 */

class LRUCache {
  constructor(maxSize = 100, maxMemory = 50 * 1024 * 1024) { // 默认50MB
    this.maxSize = maxSize          // 最大缓存条目数
    this.maxMemory = maxMemory      // 最大内存使用量(字节)
    this.cache = new Map()          // 缓存数据
    this.accessOrder = []           // 访问顺序记录
    this.memoryUsage = 0            // 当前内存使用量
    this.stats = {
      hits: 0,                      // 命中次数
      misses: 0,                    // 未命中次数
      evictions: 0,                 // 淘汰次数
      memoryEvictions: 0            // 内存淘汰次数
    }
  }

  /**
   * 获取缓存项
   */
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序
      this.updateAccessOrder(key)
      this.stats.hits++
      
      const item = this.cache.get(key)
      
      // 检查是否过期
      if (this.isExpired(item)) {
        this.delete(key)
        this.stats.misses++
        return null
      }
      
      return item.value
    }
    
    this.stats.misses++
    return null
  }

  /**
   * 设置缓存项
   */
  set(key, value, ttl = null) {
    const size = this.calculateSize(value)
    const now = Date.now()
    
    const item = {
      value,
      size,
      createdAt: now,
      lastAccessed: now,
      accessCount: 1,
      ttl: ttl ? now + ttl : null
    }

    // 如果key已存在，先删除旧的
    if (this.cache.has(key)) {
      this.delete(key)
    }

    // 检查是否需要清理空间
    this.ensureSpace(size)

    // 添加新项
    this.cache.set(key, item)
    this.accessOrder.unshift(key)
    this.memoryUsage += size

    console.log(`📦 LRU缓存添加: ${key} (${this.formatSize(size)})`)
  }

  /**
   * 删除缓存项
   */
  delete(key) {
    if (this.cache.has(key)) {
      const item = this.cache.get(key)
      this.cache.delete(key)
      this.memoryUsage -= item.size
      
      // 从访问顺序中移除
      const index = this.accessOrder.indexOf(key)
      if (index > -1) {
        this.accessOrder.splice(index, 1)
      }
      
      console.log(`🗑️ LRU缓存删除: ${key}`)
      return true
    }
    return false
  }

  /**
   * 检查缓存项是否存在
   */
  has(key) {
    if (this.cache.has(key)) {
      const item = this.cache.get(key)
      if (this.isExpired(item)) {
        this.delete(key)
        return false
      }
      return true
    }
    return false
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
    this.accessOrder = []
    this.memoryUsage = 0
    console.log('🧹 LRU缓存已清空')
  }

  /**
   * 获取缓存大小
   */
  size() {
    return this.cache.size
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    return {
      used: this.memoryUsage,
      max: this.maxMemory,
      percentage: (this.memoryUsage / this.maxMemory * 100).toFixed(2)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      size: this.cache.size,
      maxSize: this.maxSize,
      memoryUsage: this.getMemoryUsage()
    }
  }

  /**
   * 更新访问顺序
   */
  updateAccessOrder(key) {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      // 移动到最前面
      this.accessOrder.splice(index, 1)
      this.accessOrder.unshift(key)
    }
    
    // 更新访问信息
    const item = this.cache.get(key)
    if (item) {
      item.lastAccessed = Date.now()
      item.accessCount++
    }
  }

  /**
   * 检查是否过期
   */
  isExpired(item) {
    return item.ttl && Date.now() > item.ttl
  }

  /**
   * 确保有足够空间
   */
  ensureSpace(newItemSize) {
    // 清理过期项
    this.cleanExpired()

    // 检查数量限制
    while (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    // 检查内存限制
    while (this.memoryUsage + newItemSize > this.maxMemory && this.cache.size > 0) {
      this.evictLRU(true)
    }
  }

  /**
   * 淘汰最少使用的项
   */
  evictLRU(isMemoryEviction = false) {
    if (this.accessOrder.length === 0) return

    const keyToEvict = this.accessOrder[this.accessOrder.length - 1]
    this.delete(keyToEvict)
    
    this.stats.evictions++
    if (isMemoryEviction) {
      this.stats.memoryEvictions++
    }
    
    console.log(`🔄 LRU淘汰: ${keyToEvict} (${isMemoryEviction ? '内存' : '数量'}限制)`)
  }

  /**
   * 清理过期项
   */
  cleanExpired() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, item] of this.cache) {
      if (this.isExpired(item)) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    if (expiredKeys.length > 0) {
      console.log(`🧹 清理过期缓存: ${expiredKeys.length} 项`)
    }
  }

  /**
   * 计算数据大小
   */
  calculateSize(value) {
    if (value === null || value === undefined) {
      return 8 // 基础大小
    }

    if (typeof value === 'string') {
      return value.length * 2 // Unicode字符
    }

    if (typeof value === 'number') {
      return 8
    }

    if (typeof value === 'boolean') {
      return 4
    }

    if (Array.isArray(value)) {
      return value.reduce((size, item) => size + this.calculateSize(item), 24) // 数组开销
    }

    if (typeof value === 'object') {
      let size = 24 // 对象开销
      for (const key in value) {
        if (value.hasOwnProperty(key)) {
          size += this.calculateSize(key) + this.calculateSize(value[key])
        }
      }
      return size
    }

    return 16 // 其他类型的估算大小
  }

  /**
   * 格式化大小显示
   */
  formatSize(bytes) {
    if (bytes < 1024) return `${bytes}B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`
  }

  /**
   * 获取热点数据
   */
  getHotData(limit = 10) {
    const items = Array.from(this.cache.entries())
      .map(([key, item]) => ({
        key,
        accessCount: item.accessCount,
        lastAccessed: item.lastAccessed,
        size: item.size
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit)

    return items
  }

  /**
   * 预热缓存
   */
  async warmup(dataLoader, keys) {
    console.log(`🔥 开始预热缓存: ${keys.length} 项`)
    
    for (const key of keys) {
      if (!this.has(key)) {
        try {
          const data = await dataLoader(key)
          if (data) {
            this.set(key, data)
          }
        } catch (error) {
          console.warn(`⚠️ 预热失败 ${key}:`, error.message)
        }
      }
    }
    
    console.log('🔥 缓存预热完成')
  }

  /**
   * 导出缓存状态
   */
  export() {
    const data = {}
    for (const [key, item] of this.cache) {
      if (!this.isExpired(item)) {
        data[key] = {
          value: item.value,
          createdAt: item.createdAt,
          ttl: item.ttl
        }
      }
    }
    return data
  }

  /**
   * 导入缓存状态
   */
  import(data) {
    this.clear()
    const now = Date.now()
    
    for (const [key, item] of Object.entries(data)) {
      if (!item.ttl || now < item.ttl) {
        this.set(key, item.value, item.ttl ? item.ttl - now : null)
      }
    }
    
    console.log(`📥 导入缓存: ${Object.keys(data).length} 项`)
  }
}

module.exports = {
  LRUCache
}
