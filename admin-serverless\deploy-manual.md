# 🚀 手动部署指南 - 使用微信开发者工具

## 📋 部署步骤

### 第1步：准备文件
确保以下文件存在：
- ✅ index.html
- ✅ js/app.js  
- ✅ cloudbaserc-real.json

### 第2步：打开微信开发者工具
1. 打开微信开发者工具
2. 选择你的小程序项目
3. 点击"云开发"按钮

### 第3步：进入静态网站托管
1. 在云开发控制台中，点击"静态网站托管"
2. 如果没有开通，先开通静态网站托管服务

### 第4步：上传文件
1. 点击"文件管理"
2. 创建一个新文件夹叫 "admin"
3. 进入 admin 文件夹
4. 上传以下文件：
   - index.html
   - js/app.js
   - 其他相关文件

### 第5步：设置默认文档
1. 在静态网站托管设置中
2. 设置默认文档为 "index.html"
3. 设置错误文档为 "index.html"

### 第6步：获取访问地址
部署完成后，访问地址为：
```
https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/
```

## 🧪 测试部署

### 测试步骤：
1. 打开上面的访问地址
2. 检查页面是否正常加载
3. 测试各个功能模块
4. 查看浏览器控制台是否有错误

### 预期结果：
- ✅ 页面正常显示
- ✅ 数据能够加载（可能是模拟数据）
- ✅ 各个标签页可以切换
- ✅ 模态框可以正常打开

## 🔧 故障排除

### 如果页面无法访问：
1. 检查静态网站托管是否开通
2. 检查文件是否上传成功
3. 检查域名配置是否正确

### 如果功能异常：
1. 打开浏览器开发者工具
2. 查看Console错误信息
3. 检查Network请求状态
4. 确认云函数是否部署

## 📝 注意事项

1. **环境ID确认**：确保 js/app.js 中的环境ID正确
2. **云函数部署**：确保 adminAPI 和 dataAPI 云函数已部署
3. **权限设置**：可能需要设置数据库权限
4. **首次使用**：可能需要初始化测试数据

## 🎯 成功标志

部署成功后，你应该能够：
- ✅ 访问管理后台页面
- ✅ 看到统计数据（可能是模拟数据）
- ✅ 切换不同的管理模块
- ✅ 打开添加/编辑对话框

如果以上都正常，说明部署成功！
