<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAdminAPI诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebAdminAPI诊断工具</h1>
        
        <div style="text-align: center;">
            <button onclick="diagnoseWebAdminAPI()">诊断WebAdminAPI</button>
            <button onclick="testDirectCall()">测试直接调用</button>
            <button onclick="testWithAuth()">测试带认证调用</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始诊断...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 初始化SDK
        async function initSDK() {
            if (tcbApp) return tcbApp;
            
            try {
                log('🚀 初始化CloudBase SDK...');
                
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }

                tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                log('✅ SDK初始化成功', 'success');
                return tcbApp;
                
            } catch (error) {
                log('❌ SDK初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        // 诊断WebAdminAPI
        async function diagnoseWebAdminAPI() {
            try {
                log('🔍 开始诊断WebAdminAPI...');

                await initSDK();

                // 步骤1: 尝试匿名登录
                log('📋 步骤1: 尝试匿名登录');
                try {
                    const auth = tcbApp.auth();
                    await auth.signInAnonymously();
                    log('✅ 匿名登录成功', 'success');
                } catch (authError) {
                    log('⚠️ 匿名登录失败: ' + authError.message, 'warning');
                    log('  - 这可能是导致云函数调用失败的原因', 'warning');
                }

                // 步骤2: 检查云函数是否存在
                log('📋 步骤2: 检查云函数是否存在');
                try {
                    const result = await tcbApp.callFunction({
                        name: 'webAdminAPI',
                        data: {
                            action: 'getStats',
                            adminPassword: 'admin123456'
                        }
                    });

                    log('✅ 云函数存在并可调用', 'success');
                    log('  - 返回结果: ' + JSON.stringify(result, null, 2));
                } catch (error) {
                    log('❌ 云函数调用失败: ' + error.message, 'error');
                    log('  - 错误类型: ' + typeof error, 'error');
                    log('  - 错误对象: ' + JSON.stringify(error, null, 2), 'error');

                    // 检查是否是认证错误
                    if (error.message && error.message.includes('auth')) {
                        log('💡 这是认证错误，可能的原因:', 'warning');
                        log('  1. 云函数没有正确部署', 'warning');
                        log('  2. 云开发环境权限配置问题', 'warning');
                        log('  3. 需要先进行身份认证才能调用云函数', 'warning');
                        log('  4. 匿名登录被禁用，需要在控制台启用', 'warning');
                    }

                    // 尝试其他云函数
                    log('📋 步骤2.1: 尝试调用其他云函数');
                    try {
                        const dataAPIResult = await tcbApp.callFunction({
                            name: 'dataAPI',
                            data: { action: 'getCategories' }
                        });
                        log('✅ dataAPI云函数可以调用', 'success');
                        log('  - 说明问题可能特定于webAdminAPI', 'info');
                    } catch (dataAPIError) {
                        log('❌ dataAPI云函数也无法调用: ' + dataAPIError.message, 'error');
                        log('  - 说明这是通用的认证问题', 'warning');
                    }
                }
                
                // 步骤2: 检查环境配置
                log('📋 步骤2: 检查环境配置');
                log('  - 环境ID: cloud1-5g6pvnpl88dc0142');
                log('  - SDK版本: CloudBase 2.17.5');
                log('  - clientId: cloud1-5g6pvnpl88dc0142');
                
                // 步骤3: 检查SDK状态
                log('📋 步骤3: 检查SDK状态');
                log('  - tcbApp对象: ' + (tcbApp ? '存在' : '不存在'));
                if (tcbApp) {
                    log('  - 可用方法: ' + Object.keys(tcbApp).join(', '));
                    log('  - callFunction方法: ' + (tcbApp.callFunction ? '存在' : '不存在'));
                }
                
                log('🎉 诊断完成', 'success');
                
            } catch (error) {
                log('❌ 诊断失败: ' + error.message, 'error');
            }
        }

        // 测试直接调用
        async function testDirectCall() {
            try {
                log('🧪 测试直接调用webAdminAPI...');
                
                await initSDK();
                
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getStats'
                    }
                });
                
                log('📊 直接调用结果:');
                log('  - 调用成功: ' + (result ? '是' : '否'));
                if (result) {
                    log('  - 结果详情: ' + JSON.stringify(result, null, 2));
                }
                
            } catch (error) {
                log('❌ 直接调用失败: ' + error.message, 'error');
                log('📋 错误详情: ' + JSON.stringify(error, null, 2), 'error');
            }
        }

        // 测试带认证调用
        async function testWithAuth() {
            try {
                log('🔐 测试带认证调用webAdminAPI...');
                
                await initSDK();
                
                // 先尝试匿名登录
                log('🔐 尝试匿名登录...');
                try {
                    const auth = tcbApp.auth();
                    await auth.signInAnonymously();
                    log('✅ 匿名登录成功', 'success');
                } catch (authError) {
                    log('⚠️ 匿名登录失败: ' + authError.message, 'warning');
                    log('继续测试云函数调用...', 'info');
                }
                
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getStats',
                        adminPassword: 'admin123456'
                    }
                });
                
                log('📊 带认证调用结果:');
                log('  - 调用成功: ' + (result ? '是' : '否'));
                if (result) {
                    log('  - 结果详情: ' + JSON.stringify(result, null, 2));
                }
                
            } catch (error) {
                log('❌ 带认证调用失败: ' + error.message, 'error');
                log('📋 错误详情: ' + JSON.stringify(error, null, 2), 'error');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，开始诊断WebAdminAPI问题');
            log('💡 这个工具将帮助诊断webAdminAPI云函数的认证问题');
        });
    </script>
</body>
</html>
