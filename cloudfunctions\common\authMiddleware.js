const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 验证管理员权限
async function verifyAdmin(openid) {
  try {
    if (!openid) {
      return { isAdmin: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isAdmin: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isAdmin = user.auth && user.auth.role === 'admin' && user.auth.status === 'active'

    return {
      isAdmin,
      user,
      message: isAdmin ? '权限验证通过' : '权限不足'
    }
  } catch (error) {
    console.error('权限验证失败:', error)
    return { isAdmin: false, message: '权限验证失败' }
  }
}

// 权限验证装饰器
function requireAdmin(handler) {
  return async (event, context) => {
    const wxContext = cloud.getWXContext()
    const authResult = await verifyAdmin(wxContext.OPENID)

    if (!authResult.isAdmin) {
      return {
        success: false,
        error: authResult.message,
        code: 403
      }
    }

    // 将用户信息添加到event中
    event.currentUser = authResult.user
    
    return await handler(event, context)
  }
}

// 验证用户权限（普通用户）
async function verifyUser(openid) {
  try {
    if (!openid) {
      return { isValid: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isValid: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isValid = user.auth && user.auth.status === 'active'

    return {
      isValid,
      user,
      message: isValid ? '用户验证通过' : '用户状态异常'
    }
  } catch (error) {
    console.error('用户验证失败:', error)
    return { isValid: false, message: '用户验证失败' }
  }
}

// 用户权限验证装饰器
function requireUser(handler) {
  return async (event, context) => {
    const wxContext = cloud.getWXContext()
    const authResult = await verifyUser(wxContext.OPENID)

    if (!authResult.isValid) {
      return {
        success: false,
        error: authResult.message,
        code: 401
      }
    }

    // 将用户信息添加到event中
    event.currentUser = authResult.user
    
    return await handler(event, context)
  }
}

// 记录操作日志
async function logOperation(operation, user, data = {}) {
  try {
    await db.collection('operation_logs').add({
      data: {
        operation,
        userId: user._id,
        userOpenid: user.openid,
        userName: user.profile?.nickname || '未知用户',
        operationData: data,
        timestamp: new Date(),
        ip: '', // 可以从context中获取
        userAgent: '' // 可以从context中获取
      }
    })
  } catch (error) {
    console.error('记录操作日志失败:', error)
  }
}

module.exports = {
  verifyAdmin,
  requireAdmin,
  verifyUser,
  requireUser,
  logOperation
}
