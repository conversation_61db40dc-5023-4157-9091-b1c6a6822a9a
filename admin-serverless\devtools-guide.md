# 🛠️ 微信开发者工具本地调试方案

## 问题说明
由于微信云开发的HTTP服务需要付费套餐，免费套餐无法直接通过HTTP API访问云函数。

## 解决方案：使用微信开发者工具本地调试

### 步骤1：打开微信开发者工具
1. 打开微信开发者工具
2. 导入小程序项目：`e:\表情包-BOLT-符合微信云开发规范-7.17号`
3. 确保云开发环境已连接：`cloud1-5g6pvnpl88dc0142`

### 步骤2：启用云函数本地调试
1. 在微信开发者工具中，点击 **云开发** 标签
2. 选择 **云函数** 
3. 找到 `adminAPI` 云函数
4. 右键点击 → **本地调试**
5. 这会在本地启动云函数调试服务

### 步骤3：获取本地调试端口
本地调试启动后，会显示类似：
```
云函数本地调试服务已启动，端口：54321
```

### 步骤4：修改管理后台配置
将管理后台的API地址改为本地调试地址：
```javascript
const LOCAL_DEBUG_URL = 'http://localhost:54321';
```

## 优势
- ✅ 无需付费套餐
- ✅ 可以直接调试云函数
- ✅ 支持断点调试
- ✅ 实时查看日志

## 注意事项
- 需要保持微信开发者工具打开
- 每次重启需要重新启动本地调试
- 端口号可能会变化
