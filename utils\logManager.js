/**
 * 日志管理系统
 * 统一管理应用日志，支持分级、过滤和上报
 */

const { EnvironmentConfig } = require('../config/environment.js')

const LogManager = {
  // 日志级别
  LOG_LEVELS: {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    CRITICAL: 4
  },

  // 日志缓存
  _logBuffer: [],
  _maxBufferSize: 1000,
  _flushInterval: 30000, // 30秒
  _flushTimer: null,

  // 配置
  _config: {
    enableConsole: true,
    enableBuffer: true,
    enableUpload: false,
    maxLogSize: 10000, // 单条日志最大字符数
    uploadBatchSize: 50,
    retentionDays: 7
  },

  /**
   * 初始化日志管理器
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    this._config = { ...this._config, ...options }
    
    // 根据环境调整配置
    if (EnvironmentConfig.isProduction()) {
      this._config.enableUpload = true
      this._config.enableConsole = false
    } else if (EnvironmentConfig.isDevelopment()) {
      this._config.enableConsole = true
      this._config.enableUpload = false
    }

    console.log('📝 日志管理系统初始化')
    
    // 启动定时上传
    if (this._config.enableUpload) {
      this.startPeriodicFlush()
    }

    // 监听应用生命周期
    this.setupLifecycleListeners()
  },

  /**
   * 记录调试日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  debug(message, data = null) {
    this.log('DEBUG', message, data)
  },

  /**
   * 记录信息日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  info(message, data = null) {
    this.log('INFO', message, data)
  },

  /**
   * 记录警告日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  warn(message, data = null) {
    this.log('WARN', message, data)
  },

  /**
   * 记录错误日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  error(message, data = null) {
    this.log('ERROR', message, data)
  },

  /**
   * 记录严重错误日志
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  critical(message, data = null) {
    this.log('CRITICAL', message, data)
  },

  /**
   * 记录日志
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   */
  log(level, message, data = null) {
    const logLevel = this.LOG_LEVELS[level] || this.LOG_LEVELS.INFO
    const configLevel = this.LOG_LEVELS[EnvironmentConfig.getLogLevel().toUpperCase()] || this.LOG_LEVELS.INFO

    // 检查日志级别
    if (logLevel < configLevel) {
      return
    }

    const logEntry = this.createLogEntry(level, message, data)

    // 输出到控制台
    if (this._config.enableConsole) {
      this.outputToConsole(logEntry)
    }

    // 添加到缓存
    if (this._config.enableBuffer) {
      this.addToBuffer(logEntry)
    }

    // 立即上传严重错误
    if (level === 'CRITICAL' && this._config.enableUpload) {
      this.uploadLogs([logEntry])
    }
  },

  /**
   * 创建日志条目
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} data - 附加数据
   * @returns {Object} 日志条目
   */
  createLogEntry(level, message, data) {
    const timestamp = new Date().toISOString()
    const entry = {
      timestamp,
      level,
      message: this.truncateMessage(message),
      environment: EnvironmentConfig.currentEnv,
      sessionId: this.getSessionId(),
      userId: this.getUserId(),
      page: this.getCurrentPage(),
      userAgent: this.getUserAgent()
    }

    // 添加附加数据
    if (data !== null) {
      try {
        entry.data = typeof data === 'string' ? data : JSON.stringify(data)
        if (entry.data.length > this._config.maxLogSize) {
          entry.data = entry.data.substring(0, this._config.maxLogSize) + '...[truncated]'
        }
      } catch (error) {
        entry.data = '[无法序列化的数据]'
      }
    }

    return entry
  },

  /**
   * 截断消息
   * @param {string} message - 原始消息
   * @returns {string} 截断后的消息
   */
  truncateMessage(message) {
    if (typeof message !== 'string') {
      message = String(message)
    }
    
    if (message.length > this._config.maxLogSize) {
      return message.substring(0, this._config.maxLogSize) + '...[truncated]'
    }
    
    return message
  },

  /**
   * 输出到控制台
   * @param {Object} logEntry - 日志条目
   */
  outputToConsole(logEntry) {
    const { level, timestamp, message, data } = logEntry
    const timeStr = new Date(timestamp).toLocaleTimeString()
    const prefix = `[${timeStr}] ${level}:`

    switch (level) {
      case 'DEBUG':
        console.log(prefix, message, data || '')
        break
      case 'INFO':
        console.info(prefix, message, data || '')
        break
      case 'WARN':
        console.warn(prefix, message, data || '')
        break
      case 'ERROR':
      case 'CRITICAL':
        console.error(prefix, message, data || '')
        break
      default:
        console.log(prefix, message, data || '')
    }
  },

  /**
   * 添加到缓存
   * @param {Object} logEntry - 日志条目
   */
  addToBuffer(logEntry) {
    this._logBuffer.push(logEntry)

    // 检查缓存大小
    if (this._logBuffer.length > this._maxBufferSize) {
      // 移除最旧的日志
      this._logBuffer.shift()
    }
  },

  /**
   * 开始定期上传
   */
  startPeriodicFlush() {
    if (this._flushTimer) {
      clearInterval(this._flushTimer)
    }

    this._flushTimer = setInterval(() => {
      this.flushLogs()
    }, this._flushInterval)

    console.log('⏰ 日志定期上传已启动')
  },

  /**
   * 停止定期上传
   */
  stopPeriodicFlush() {
    if (this._flushTimer) {
      clearInterval(this._flushTimer)
      this._flushTimer = null
    }
  },

  /**
   * 刷新日志（上传到服务器）
   */
  async flushLogs() {
    if (this._logBuffer.length === 0) {
      return
    }

    const logsToUpload = this._logBuffer.splice(0, this._config.uploadBatchSize)
    
    try {
      await this.uploadLogs(logsToUpload)
      console.log(`📤 日志上传成功: ${logsToUpload.length} 条`)
    } catch (error) {
      console.error('❌ 日志上传失败:', error)
      // 将失败的日志重新加入缓存
      this._logBuffer.unshift(...logsToUpload)
    }
  },

  /**
   * 上传日志到服务器
   * @param {Array} logs - 日志数组
   */
  async uploadLogs(logs) {
    if (!this._config.enableUpload || logs.length === 0) {
      return
    }

    try {
      await wx.cloud.callFunction({
        name: 'uploadLogs',
        data: {
          logs,
          environment: EnvironmentConfig.currentEnv,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      throw new Error(`日志上传失败: ${error.message}`)
    }
  },

  /**
   * 获取会话ID
   * @returns {string} 会话ID
   */
  getSessionId() {
    try {
      let sessionId = wx.getStorageSync('sessionId')
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        wx.setStorageSync('sessionId', sessionId)
      }
      return sessionId
    } catch (error) {
      return 'unknown_session'
    }
  },

  /**
   * 获取用户ID
   * @returns {string} 用户ID
   */
  getUserId() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      return userInfo?.openid || 'anonymous'
    } catch (error) {
      return 'unknown_user'
    }
  },

  /**
   * 获取当前页面
   * @returns {string} 当前页面路径
   */
  getCurrentPage() {
    try {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        return pages[pages.length - 1].route
      }
      return 'unknown_page'
    } catch (error) {
      return 'unknown_page'
    }
  },

  /**
   * 获取用户代理信息
   * @returns {string} 用户代理信息
   */
  getUserAgent() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      return `${systemInfo.platform} ${systemInfo.system} WeChat/${systemInfo.version}`
    } catch (error) {
      console.warn('⚠️ 获取用户代理信息失败:', error.message)
      return 'unknown_agent'
    }
  },

  /**
   * 设置生命周期监听器
   */
  setupLifecycleListeners() {
    try {
      // 检查是否在小程序环境中
      if (typeof wx !== 'undefined' && wx.onAppHide && wx.onAppShow) {
        // 应用隐藏时上传日志
        wx.onAppHide(() => {
          this.info('应用进入后台')
          if (this._config.enableUpload) {
            this.flushLogs()
          }
        })

        // 应用显示时记录日志
        wx.onAppShow(() => {
          this.info('应用进入前台')
        })
      } else {
        console.log('⚠️ 非小程序环境，跳过生命周期监听器设置')
      }
    } catch (error) {
      console.warn('⚠️ 生命周期监听器设置失败:', error.message)
    }
  },

  /**
   * 获取日志统计
   * @returns {Object} 日志统计信息
   */
  getLogStats() {
    const stats = {
      bufferSize: this._logBuffer.length,
      maxBufferSize: this._maxBufferSize,
      config: { ...this._config }
    }

    // 按级别统计
    const levelCounts = {}
    this._logBuffer.forEach(log => {
      levelCounts[log.level] = (levelCounts[log.level] || 0) + 1
    })
    stats.levelCounts = levelCounts

    return stats
  },

  /**
   * 清空日志缓存
   */
  clearBuffer() {
    this._logBuffer = []
    console.log('🗑️ 日志缓存已清空')
  },

  /**
   * 导出日志
   * @param {Object} options - 导出选项
   * @returns {Array} 日志数组
   */
  exportLogs(options = {}) {
    const { level, startTime, endTime, limit } = options
    let logs = [...this._logBuffer]

    // 按级别过滤
    if (level) {
      const levelValue = this.LOG_LEVELS[level.toUpperCase()]
      logs = logs.filter(log => this.LOG_LEVELS[log.level] >= levelValue)
    }

    // 按时间过滤
    if (startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= new Date(startTime))
    }
    if (endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= new Date(endTime))
    }

    // 限制数量
    if (limit && limit > 0) {
      logs = logs.slice(-limit)
    }

    return logs
  },

  /**
   * 销毁日志管理器
   */
  destroy() {
    this.stopPeriodicFlush()
    
    // 最后一次上传
    if (this._config.enableUpload && this._logBuffer.length > 0) {
      this.flushLogs()
    }
    
    this.clearBuffer()
    console.log('🗑️ 日志管理系统已销毁')
  }
}

module.exports = {
  LogManager
}
