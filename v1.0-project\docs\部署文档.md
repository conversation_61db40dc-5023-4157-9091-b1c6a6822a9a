# V1.0 部署文档

## 部署概述

V1.0系统基于腾讯云开发平台，包含云函数、云数据库、静态网站托管等服务。本文档详细说明了完整的部署流程。

## 环境要求

### 开发环境
- Node.js >= 14.0.0
- npm >= 6.0.0
- 微信开发者工具 >= 1.05.0
- @cloudbase/cli >= 1.0.0

### 云开发环境
- 腾讯云开发环境
- 云函数服务
- 云数据库服务
- 静态网站托管服务

## 部署前准备

### 1. 安装依赖工具

```bash
# 安装云开发CLI
npm install -g @cloudbase/cli

# 验证安装
tcb --version
```

### 2. 配置云开发环境

```bash
# 登录云开发
tcb login

# 创建云开发环境（如果还没有）
tcb env create --name v1-production --region ap-shanghai
```

### 3. 配置环境变量

创建 `.env` 文件：
```bash
# 云开发环境ID
CLOUDBASE_ENV_ID=your-env-id

# 云开发密钥（可选，用于CI/CD）
CLOUDBASE_SECRET_ID=your-secret-id
CLOUDBASE_SECRET_KEY=your-secret-key

# JWT密钥
JWT_SECRET=your-jwt-secret-key

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-admin-password
```

## 自动化部署

### 使用部署脚本

```bash
# 进入项目目录
cd v1.0-project

# 执行自动化部署
node scripts/deploy.js

# 或指定环境
node scripts/deploy.js --env your-env-id

# 试运行（只检查不部署）
node scripts/deploy.js --dry-run
```

### 部署脚本功能

部署脚本会自动执行以下步骤：

1. **部署前检查**
   - Node.js版本检查
   - 云开发工具检查
   - 项目结构检查
   - 环境变量检查

2. **云函数部署**
   - 安装依赖
   - 部署loginAPI
   - 部署webAdminAPI
   - 部署dataAPI

3. **数据库配置**
   - 应用安全规则
   - 创建索引
   - 配置权限

4. **静态网站部署**
   - 部署管理后台

5. **部署验证**
   - 验证云函数状态
   - 验证数据库连接
   - 运行冒烟测试

## 手动部署

### 1. 部署云函数

#### 1.1 部署loginAPI

```bash
cd cloudfunctions/loginAPI

# 安装依赖
npm install

# 部署云函数
tcb fn deploy loginAPI --env your-env-id
```

#### 1.2 部署webAdminAPI

```bash
cd cloudfunctions/webAdminAPI

# 安装依赖
npm install

# 部署云函数
tcb fn deploy webAdminAPI --env your-env-id
```

#### 1.3 部署dataAPI

```bash
cd cloudfunctions/dataAPI

# 安装依赖
npm install

# 部署云函数
tcb fn deploy dataAPI --env your-env-id
```

### 2. 配置数据库

#### 2.1 创建集合

在云开发控制台创建以下集合：

- `categories` - 分类数据
- `emojis` - 表情包数据
- `banners` - 横幅数据
- `sync_notifications` - 同步通知
- `admin_logs` - 管理员日志

#### 2.2 配置权限

```json
{
  "read": "auth != null",
  "write": false
}
```

#### 2.3 创建索引

参考 `config/database-rules.json` 中的索引配置。

### 3. 部署静态网站

```bash
# 部署管理后台
tcb hosting deploy admin-web --env your-env-id
```

## 配置管理

### 1. 生产环境配置

编辑 `config/production.js`：

```javascript
const PRODUCTION_CONFIG = {
  cloudbase: {
    env: 'your-production-env-id',
    region: 'ap-shanghai'
  },
  
  cloudfunctions: {
    loginAPI: {
      timeout: 10000,
      memorySize: 256,
      environment: {
        JWT_SECRET: 'your-production-jwt-secret',
        ADMIN_PASSWORD: 'your-production-password'
      }
    }
    // ... 其他配置
  }
};
```

### 2. 数据库安全规则

参考 `config/database-rules.json` 配置数据库权限和索引。

### 3. 云函数环境变量

在云开发控制台为每个云函数配置环境变量：

**loginAPI**:
- `JWT_SECRET`: JWT密钥
- `JWT_EXPIRES_IN`: 令牌有效期
- `ADMIN_USERNAME`: 管理员用户名
- `ADMIN_PASSWORD`: 管理员密码

**webAdminAPI**:
- `JWT_SECRET`: JWT密钥（与loginAPI保持一致）

**dataAPI**:
- `CACHE_TTL_CATEGORIES`: 分类缓存时间
- `CACHE_TTL_EMOJIS`: 表情包缓存时间

## 域名配置

### 1. 静态网站域名

在云开发控制台配置自定义域名：

1. 进入静态网站托管
2. 添加自定义域名
3. 配置SSL证书
4. 设置CNAME记录

### 2. API域名

云函数会自动分配域名，格式为：
```
https://your-env-id.service.tcloudbase.com/your-function-name
```

## 监控配置

### 1. 云函数监控

在云开发控制台配置：

- 函数调用监控
- 错误率监控
- 响应时间监控
- 内存使用监控

### 2. 数据库监控

配置数据库监控：

- 读写QPS监控
- 存储容量监控
- 慢查询监控

### 3. 告警配置

设置告警规则：

- 错误率超过5%
- 响应时间超过3秒
- 内存使用率超过80%

## 备份策略

### 1. 数据库备份

```bash
# 导出数据
tcb db export --env your-env-id --collection categories --file categories.json
tcb db export --env your-env-id --collection emojis --file emojis.json
tcb db export --env your-env-id --collection banners --file banners.json
```

### 2. 代码备份

- 使用Git进行版本控制
- 定期推送到远程仓库
- 创建发布标签

### 3. 配置备份

- 导出云函数配置
- 备份环境变量
- 保存数据库规则

## 回滚策略

### 1. 云函数回滚

```bash
# 查看版本历史
tcb fn list --env your-env-id

# 回滚到指定版本
tcb fn rollback loginAPI --version 2 --env your-env-id
```

### 2. 数据库回滚

```bash
# 导入备份数据
tcb db import --env your-env-id --collection categories --file categories-backup.json
```

### 3. 静态网站回滚

```bash
# 重新部署之前的版本
tcb hosting deploy admin-web-backup --env your-env-id
```

## 性能优化

### 1. 云函数优化

- 配置合适的内存大小
- 启用函数预热
- 优化代码和依赖

### 2. 数据库优化

- 创建合适的索引
- 使用查询缓存
- 限制查询数量

### 3. 静态资源优化

- 启用CDN加速
- 压缩静态文件
- 配置缓存策略

## 故障排查

### 1. 云函数问题

```bash
# 查看函数日志
tcb fn log loginAPI --env your-env-id

# 查看函数详情
tcb fn detail loginAPI --env your-env-id
```

### 2. 数据库问题

- 检查权限配置
- 查看慢查询日志
- 验证索引使用

### 3. 网络问题

- 检查域名解析
- 验证SSL证书
- 测试API连通性

## 安全检查

### 1. 访问控制

- 验证JWT认证
- 检查权限配置
- 测试API安全性

### 2. 数据安全

- 验证数据库权限
- 检查敏感信息过滤
- 测试SQL注入防护

### 3. 网络安全

- 启用HTTPS
- 配置CORS策略
- 设置访问频率限制

## 部署检查清单

- [ ] 云函数部署成功
- [ ] 数据库配置正确
- [ ] 静态网站可访问
- [ ] API接口正常
- [ ] 认证功能正常
- [ ] 实时同步正常
- [ ] 监控告警配置
- [ ] 备份策略执行
- [ ] 性能指标正常
- [ ] 安全检查通过

## 联系支持

如遇到部署问题，请：

1. 查看部署日志
2. 检查配置文件
3. 参考故障排查文档
4. 联系技术支持
