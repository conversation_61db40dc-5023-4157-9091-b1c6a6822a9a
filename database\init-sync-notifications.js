/**
 * 初始化 sync_notifications 数据库集合
 * 用于实时同步通知功能
 */

// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    console.log('🔄 开始初始化 sync_notifications 集合...')

    // 1. 创建集合（如果不存在）
    try {
      await db.createCollection('sync_notifications')
      console.log('✅ sync_notifications 集合创建成功')
    } catch (error) {
      if (error.errCode === -1 && error.errMsg.includes('collection already exists')) {
        console.log('ℹ️ sync_notifications 集合已存在')
      } else {
        throw error
      }
    }

    // 2. 创建索引以优化查询性能
    const collection = db.collection('sync_notifications')
    
    // 按时间戳索引（用于按时间排序）
    try {
      await collection.createIndex({
        keys: { timestamp: -1 },
        name: 'timestamp_desc'
      })
      console.log('✅ timestamp 索引创建成功')
    } catch (error) {
      console.log('ℹ️ timestamp 索引可能已存在:', error.errMsg)
    }

    // 按类型和状态索引（用于查询特定类型的通知）
    try {
      await collection.createIndex({
        keys: { type: 1, status: 1 },
        name: 'type_status'
      })
      console.log('✅ type_status 索引创建成功')
    } catch (error) {
      console.log('ℹ️ type_status 索引可能已存在:', error.errMsg)
    }

    // 按操作类型索引
    try {
      await collection.createIndex({
        keys: { operation: 1 },
        name: 'operation'
      })
      console.log('✅ operation 索引创建成功')
    } catch (error) {
      console.log('ℹ️ operation 索引可能已存在:', error.errMsg)
    }

    // 3. 插入示例数据结构说明
    const sampleNotification = {
      type: 'emojis',           // 数据类型: 'emojis', 'categories', 'banners'
      operation: 'sync',        // 操作类型: 'create', 'update', 'delete', 'sync'
      timestamp: new Date(),    // 通知时间戳
      dataCount: 0,            // 影响的数据条数
      status: 'completed',     // 状态: 'pending', 'completed', 'failed'
      details: {
        affectedIds: [],       // 受影响的数据ID列表
        summary: 'Sample notification structure',  // 操作摘要
        source: 'admin'        // 数据来源: 'admin', 'miniprogram'
      },
      metadata: {
        version: '1.0',        // 数据版本
        environment: cloud.DYNAMIC_CURRENT_ENV,  // 环境信息
        createdBy: 'system'    // 创建者
      }
    }

    // 检查是否已有示例数据
    const existingSample = await collection.where({
      'details.summary': 'Sample notification structure'
    }).get()

    if (existingSample.data.length === 0) {
      await collection.add({
        data: sampleNotification
      })
      console.log('✅ 示例通知数据插入成功')
    } else {
      console.log('ℹ️ 示例通知数据已存在')
    }

    // 4. 验证集合和索引
    const stats = await collection.count()
    console.log(`📊 sync_notifications 集合统计: ${stats.total} 条记录`)

    return {
      success: true,
      message: 'sync_notifications 集合初始化完成',
      details: {
        collectionExists: true,
        indexesCreated: ['timestamp_desc', 'type_status', 'operation'],
        recordCount: stats.total,
        sampleDataStructure: sampleNotification
      }
    }

  } catch (error) {
    console.error('❌ 初始化 sync_notifications 集合失败:', error)
    return {
      success: false,
      error: error.message,
      details: error
    }
  }
}

/**
 * 数据结构说明：
 * 
 * sync_notifications 集合用于存储实时同步通知
 * 
 * 字段说明：
 * - type: 数据类型 (emojis/categories/banners)
 * - operation: 操作类型 (create/update/delete/sync)
 * - timestamp: 通知时间戳
 * - dataCount: 影响的数据条数
 * - status: 通知状态 (pending/completed/failed)
 * - details: 详细信息
 *   - affectedIds: 受影响的数据ID列表
 *   - summary: 操作摘要
 *   - source: 数据来源
 * - metadata: 元数据信息
 *   - version: 数据版本
 *   - environment: 环境信息
 *   - createdBy: 创建者
 * 
 * 索引说明：
 * - timestamp_desc: 按时间倒序，用于获取最新通知
 * - type_status: 按类型和状态，用于查询特定状态的通知
 * - operation: 按操作类型，用于统计不同操作
 */
