/**
 * 图片加载优化管理器
 * 提供图片预加载、懒加载、缓存和压缩等功能
 */

const { SmartCache } = require('./smartCache.js')
const { UserExperience } = require('./userExperience.js')

const ImageOptimizer = {
  // 配置选项
  config: {
    // 预加载配置
    preload: {
      enabled: true,
      maxConcurrent: 3,        // 最大并发预加载数
      priority: ['banner', 'emoji', 'avatar'], // 预加载优先级
      threshold: 0.8           // 滚动阈值
    },
    
    // 懒加载配置
    lazyLoad: {
      enabled: true,
      rootMargin: '50px',      // 提前加载距离
      threshold: 0.1,          // 可见性阈值
      placeholder: '/images/placeholder.png'
    },
    
    // 缓存配置
    cache: {
      enabled: true,
      maxSize: 100,            // 最大缓存图片数
      maxMemory: 20 * 1024 * 1024, // 20MB
      ttl: 30 * 60 * 1000     // 30分钟
    },
    
    // 压缩配置
    compression: {
      enabled: true,
      quality: 0.8,            // 压缩质量
      maxWidth: 750,           // 最大宽度
      maxHeight: 750,          // 最大高度
      format: 'webp'           // 优先格式
    }
  },

  // 状态管理
  state: {
    initialized: false,
    loadingQueue: new Map(),     // 加载队列
    preloadQueue: [],            // 预加载队列
    loadedImages: new Set(),     // 已加载图片
    failedImages: new Set(),     // 加载失败图片
    observers: new Map()         // 懒加载观察器
  },

  // 统计信息
  stats: {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    loadSuccess: 0,
    loadFailure: 0,
    avgLoadTime: 0,
    totalLoadTime: 0
  },

  /**
   * 初始化图片优化器
   */
  init(options = {}) {
    console.log('🖼️ 初始化图片优化器...')
    
    // 合并配置
    this.config = this.mergeConfig(this.config, options)
    
    // 初始化图片缓存
    if (this.config.cache.enabled) {
      this.initImageCache()
    }
    
    // 启动预加载服务
    if (this.config.preload.enabled) {
      this.startPreloadService()
    }
    
    this.state.initialized = true
    console.log('✅ 图片优化器初始化完成')
  },

  /**
   * 优化图片加载
   */
  async loadImage(src, options = {}) {
    const {
      priority = 'normal',
      useCache = true,
      placeholder = this.config.lazyLoad.placeholder,
      onProgress = null,
      onError = null
    } = options

    this.stats.totalRequests++
    const startTime = Date.now()

    try {
      // 检查缓存
      if (useCache && this.config.cache.enabled) {
        const cached = this.getCachedImage(src)
        if (cached) {
          this.stats.cacheHits++
          console.log(`📦 图片缓存命中: ${this.getImageName(src)}`)
          return cached
        }
        this.stats.cacheMisses++
      }

      // 检查是否正在加载
      if (this.state.loadingQueue.has(src)) {
        console.log(`⏳ 图片加载中，等待完成: ${this.getImageName(src)}`)
        return await this.state.loadingQueue.get(src)
      }

      // 开始加载
      const loadPromise = this.performImageLoad(src, {
        priority,
        placeholder,
        onProgress,
        onError
      })

      this.state.loadingQueue.set(src, loadPromise)

      const result = await loadPromise
      
      // 记录统计
      const loadTime = Date.now() - startTime
      this.stats.totalLoadTime += loadTime
      this.stats.avgLoadTime = this.stats.totalLoadTime / this.stats.totalRequests
      this.stats.loadSuccess++

      // 缓存结果
      if (useCache && this.config.cache.enabled && result.success) {
        this.cacheImage(src, result.data, loadTime)
      }

      this.state.loadingQueue.delete(src)
      this.state.loadedImages.add(src)

      console.log(`✅ 图片加载完成: ${this.getImageName(src)} (${loadTime}ms)`)
      return result

    } catch (error) {
      this.stats.loadFailure++
      this.state.loadingQueue.delete(src)
      this.state.failedImages.add(src)

      console.error(`❌ 图片加载失败: ${this.getImageName(src)}`, error.message)
      
      if (onError) {
        onError(error)
      }

      return {
        success: false,
        error: error.message,
        placeholder: placeholder
      }
    }
  },

  /**
   * 执行图片加载
   */
  performImageLoad(src, options) {
    return new Promise((resolve, reject) => {
      // 在小程序环境中使用 wx.downloadFile 或直接返回 URL
      if (typeof wx !== 'undefined') {
        // 小程序环境：直接使用图片URL，让image组件处理
        resolve({
          success: true,
          data: {
            src: src,
            localPath: src, // 在小程序中通常直接使用网络URL
            size: 0,
            format: this.getImageFormat(src)
          }
        })
      } else {
        // 测试环境：模拟加载
        setTimeout(() => {
          resolve({
            success: true,
            data: {
              src: src,
              localPath: src,
              size: Math.random() * 100000, // 模拟文件大小
              format: this.getImageFormat(src)
            }
          })
        }, Math.random() * 500 + 100) // 模拟加载时间
      }
    })
  },

  /**
   * 批量预加载图片
   */
  async preloadImages(urls, options = {}) {
    const {
      priority = 'low',
      maxConcurrent = this.config.preload.maxConcurrent,
      onProgress = null
    } = options

    console.log(`🔥 开始预加载图片: ${urls.length} 张`)

    const results = []
    const chunks = this.chunkArray(urls, maxConcurrent)

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      const chunkPromises = chunk.map(url => 
        this.loadImage(url, { priority, useCache: true })
      )

      const chunkResults = await Promise.allSettled(chunkPromises)
      results.push(...chunkResults)

      if (onProgress) {
        const progress = (i + 1) / chunks.length
        onProgress(progress, results.length, urls.length)
      }
    }

    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length
    console.log(`✅ 预加载完成: ${successCount}/${urls.length} 张图片`)

    return {
      total: urls.length,
      success: successCount,
      failed: urls.length - successCount,
      results
    }
  },

  /**
   * 设置懒加载
   */
  setupLazyLoad(selector, options = {}) {
    if (!this.config.lazyLoad.enabled) {
      console.log('⚠️ 懒加载已禁用')
      return
    }

    const {
      rootMargin = this.config.lazyLoad.rootMargin,
      threshold = this.config.lazyLoad.threshold,
      onLoad = null
    } = options

    console.log(`👁️ 设置懒加载: ${selector}`)

    // 在小程序环境中，使用 IntersectionObserver
    if (typeof wx !== 'undefined') {
      const observer = wx.createIntersectionObserver({
        rootMargin,
        threshold
      })

      observer.observe(selector, (res) => {
        if (res.intersectionRatio > 0) {
          // 元素进入视口，开始加载图片
          this.loadLazyImage(res.target, onLoad)
          observer.unobserve(res.target)
        }
      })

      this.state.observers.set(selector, observer)
    } else {
      // 测试环境：模拟懒加载
      console.log(`📱 模拟设置懒加载观察器: ${selector}`)
    }
  },

  /**
   * 加载懒加载图片
   */
  async loadLazyImage(element, onLoad) {
    const src = element.dataset?.src || element.src
    if (!src) return

    try {
      const result = await this.loadImage(src, { priority: 'normal' })
      
      if (result.success) {
        // 更新图片源
        if (element.src !== undefined) {
          element.src = result.data.src
        }
        
        if (onLoad) {
          onLoad(element, result)
        }
        
        console.log(`👁️ 懒加载图片完成: ${this.getImageName(src)}`)
      }
    } catch (error) {
      console.error(`❌ 懒加载图片失败: ${this.getImageName(src)}`, error)
    }
  },

  /**
   * 初始化图片缓存
   */
  initImageCache() {
    // 使用智能缓存系统
    SmartCache.strategies.images = {
      level: 'memory',
      ttl: this.config.cache.ttl,
      preload: false
    }
    
    console.log('📦 图片缓存已初始化')
  },

  /**
   * 获取缓存图片
   */
  getCachedImage(src) {
    const cacheKey = `image_${this.hashString(src)}`
    return SmartCache.get(cacheKey, 'images')
  },

  /**
   * 缓存图片
   */
  cacheImage(src, data, loadTime) {
    const cacheKey = `image_${this.hashString(src)}`
    const cacheData = {
      ...data,
      cachedAt: Date.now(),
      loadTime
    }
    
    SmartCache.set(cacheKey, cacheData, 'images', this.config.cache.ttl)
  },

  /**
   * 启动预加载服务
   */
  startPreloadService() {
    // 定期处理预加载队列
    setInterval(() => {
      this.processPreloadQueue()
    }, 1000)
    
    console.log('🔥 预加载服务已启动')
  },

  /**
   * 处理预加载队列
   */
  async processPreloadQueue() {
    if (this.state.preloadQueue.length === 0) return

    const batch = this.state.preloadQueue.splice(0, this.config.preload.maxConcurrent)
    
    for (const item of batch) {
      try {
        await this.loadImage(item.url, { 
          priority: 'low',
          useCache: true 
        })
      } catch (error) {
        console.warn(`⚠️ 预加载失败: ${item.url}`, error.message)
      }
    }
  },

  /**
   * 添加到预加载队列
   */
  addToPreloadQueue(urls, priority = 'normal') {
    const items = urls.map(url => ({
      url,
      priority,
      addedAt: Date.now()
    }))

    // 按优先级排序
    this.state.preloadQueue.push(...items)
    this.state.preloadQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    console.log(`📋 添加到预加载队列: ${urls.length} 张图片`)
  },

  /**
   * 获取图片格式
   */
  getImageFormat(src) {
    const ext = src.split('.').pop()?.toLowerCase()
    return ext || 'unknown'
  },

  /**
   * 获取图片名称
   */
  getImageName(src) {
    return src.split('/').pop() || src.substring(0, 20) + '...'
  },

  /**
   * 字符串哈希
   */
  hashString(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  },

  /**
   * 数组分块
   */
  chunkArray(array, size) {
    const chunks = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  },

  /**
   * 合并配置
   */
  mergeConfig(defaultConfig, userConfig) {
    const merged = JSON.parse(JSON.stringify(defaultConfig))
    
    for (const key in userConfig) {
      if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        merged[key] = { ...merged[key], ...userConfig[key] }
      } else {
        merged[key] = userConfig[key]
      }
    }
    
    return merged
  },

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      cacheHitRate: this.stats.totalRequests > 0 
        ? (this.stats.cacheHits / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%',
      successRate: this.stats.totalRequests > 0
        ? (this.stats.loadSuccess / this.stats.totalRequests * 100).toFixed(2) + '%'
        : '0%',
      loadedCount: this.state.loadedImages.size,
      failedCount: this.state.failedImages.size,
      queueSize: this.state.preloadQueue.length
    }
  },

  /**
   * 清理资源
   */
  cleanup() {
    // 清理观察器
    for (const [selector, observer] of this.state.observers) {
      if (observer && typeof observer.disconnect === 'function') {
        observer.disconnect()
      }
    }
    this.state.observers.clear()

    // 清理队列
    this.state.loadingQueue.clear()
    this.state.preloadQueue = []
    this.state.loadedImages.clear()
    this.state.failedImages.clear()

    console.log('🧹 图片优化器资源已清理')
  }
}

module.exports = {
  ImageOptimizer
}
