{"name": "wechat-emoji-miniprogram", "version": "1.0.0", "description": "微信表情包小程序", "main": "app.js", "scripts": {"dev": "echo '请使用微信开发者工具打开此项目'", "build": "echo '请使用微信开发者工具进行构建'", "admin": "node proxy-server.js", "admin-dev": "nodemon proxy-server.js", "test-data-sync": "node playwright-data-sync-test.js", "install-playwright": "npx playwright install", "serve": "node start-local-server.js", "cors-fix": "node start-local-server.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "node-fetch": "^2.6.7", "playwright": "^1.54.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^24.1.0", "nodemon": "^3.0.1"}, "keywords": ["wechat", "miniprogram", "emoji", "admin"], "author": "", "license": "MIT"}