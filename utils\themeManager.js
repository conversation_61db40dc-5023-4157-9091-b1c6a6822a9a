// 主题管理器 - TabBar主题切换
const ThemeManager = {
  // 可用主题列表
  themes: {
    purple: {
      name: '现代紫色',
      primary: '#8B5CF6',
      description: '优雅现代，适合创意类应用'
    },
    orange: {
      name: '活力橙色', 
      primary: '#F97316',
      description: '充满活力，适合社交类应用'
    },
    green: {
      name: '清新绿色',
      primary: '#10B981', 
      description: '自然清新，适合健康类应用'
    },
    blue: {
      name: '经典蓝色',
      primary: '#3B82F6',
      description: '专业稳重，适合商务类应用'
    }
  },

  // 当前主题
  currentTheme: 'purple',

  /**
   * 初始化主题管理器
   */
  init() {
    // 从本地存储读取主题设置
    const savedTheme = wx.getStorageSync('app_theme') || 'purple'
    this.setTheme(savedTheme)
  },

  /**
   * 设置主题
   */
  setTheme(themeName) {
    if (!this.themes[themeName]) {
      console.warn('主题不存在:', themeName)
      return false
    }

    this.currentTheme = themeName
    
    // 保存到本地存储
    wx.setStorageSync('app_theme', themeName)
    
    // 应用主题到页面
    this.applyTheme(themeName)
    
    console.log('主题已切换:', this.themes[themeName].name)
    return true
  },

  /**
   * 应用主题到页面
   */
  applyTheme(themeName) {
    try {
      // 获取页面实例
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      
      if (currentPage) {
        // 为页面添加主题类名
        const themeClass = themeName === 'purple' ? '' : `theme-${themeName}`
        
        // 这里可以通过setData更新页面的主题类名
        if (currentPage.setData) {
          currentPage.setData({
            themeClass: themeClass
          })
        }
      }
    } catch (error) {
      console.error('应用主题失败:', error)
    }
  },

  /**
   * 获取当前主题信息
   */
  getCurrentTheme() {
    return {
      key: this.currentTheme,
      ...this.themes[this.currentTheme]
    }
  },

  /**
   * 获取所有主题列表
   */
  getAllThemes() {
    return Object.keys(this.themes).map(key => ({
      key,
      ...this.themes[key]
    }))
  },

  /**
   * 切换到下一个主题
   */
  switchToNext() {
    const themeKeys = Object.keys(this.themes)
    const currentIndex = themeKeys.indexOf(this.currentTheme)
    const nextIndex = (currentIndex + 1) % themeKeys.length
    const nextTheme = themeKeys[nextIndex]
    
    this.setTheme(nextTheme)
    
    // 显示切换提示
    wx.showToast({
      title: `已切换到${this.themes[nextTheme].name}`,
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 预览主题效果
   */
  previewTheme(themeName) {
    if (!this.themes[themeName]) return false
    
    // 临时应用主题（不保存）
    this.applyTheme(themeName)
    
    // 3秒后恢复原主题
    setTimeout(() => {
      this.applyTheme(this.currentTheme)
    }, 3000)
    
    wx.showToast({
      title: `预览${this.themes[themeName].name}`,
      icon: 'none',
      duration: 1000
    })
    
    return true
  }
}

module.exports = {
  ThemeManager
}