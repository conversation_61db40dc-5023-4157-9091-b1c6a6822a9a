# 管理后台部署包

## 📁 文件说明
- index.html - 主页面
- js/app.js - 应用逻辑
- test.html - 测试页面

## 🚀 部署步骤

### 方法1：微信开发者工具
1. 打开微信开发者工具
2. 选择你的小程序项目
3. 点击"云开发"
4. 进入"静态网站托管"
5. 创建 "admin" 文件夹
6. 将本文件夹中的所有文件上传到 admin 文件夹

### 方法2：云开发控制台
1. 访问 https://console.cloud.tencent.com/tcb
2. 选择环境: cloud1-5g6pvnpl88dc0142
3. 进入"静态网站托管"
4. 上传文件

## 🌐 访问地址
部署完成后访问：
https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/

## 🧪 测试
- 主页面: /admin/
- 测试页面: /admin/test.html

## ⚠️ 注意事项
1. 确保云函数 adminAPI 和 dataAPI 已部署
2. 首次访问可能显示模拟数据
3. 如有问题请检查浏览器控制台
