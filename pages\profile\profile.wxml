<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card card" wx:if="{{isLoggedIn}}">
    <view class="card-content">
      <view class="user-avatar">
        <!-- 直接使用button作为头像容器，这样整个头像区域都可以点击 -->
        <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
          <image class="avatar-image" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill" />
        </button>
      </view>
      <view class="user-info">
        <text class="user-name">{{userInfo.nickName || '微信用户'}}</text>
        <text class="user-desc">表情包收藏家</text>
      </view>
      <!-- 编辑按钮 -->
      <view class="edit-profile-btn" bindtap="onEditProfile">
        <text class="edit-icon">✏️</text>
      </view>
    </view>
    <!-- 智能展示统计数据 -->
    <view class="user-stats" wx:if="{{featureConfig.showStats}}">
      <view class="stat-item">
        <text class="stat-number">{{likedCount}}</text>
        <text class="stat-label">点赞</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{collectedCount}}</text>
        <text class="stat-label">收藏</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{downloadCount}}</text>
        <text class="stat-label">下载</text>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-prompt card" wx:else>
    <view class="login-prompt-content">
      <image class="login-icon" src="/images/login-icon.png" mode="aspectFit"></image>
      <text class="login-title">登录后享受更多功能</text>
      <button class="login-prompt-btn" bindtap="onLoginPrompt">立即登录</button>
    </view>
  </view>

  <!-- 浏览记录功能 - 第一版显示 -->
  <view class="menu-section" wx:if="{{isLoggedIn}}">
    <view class="menu-item" bindtap="onBrowseHistoryTap">
      <view class="menu-icon browse-icon">
        <text class="icon-text">👀</text>
      </view>
      <view class="menu-content">
        <text class="menu-title">浏览记录</text>
        <text class="menu-desc">查看最近浏览的表情包</text>
      </view>
      <view class="menu-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 - 智能展示 -->
  <view class="menu-section" wx:if="{{isLoggedIn && featureConfig.showMenus}}">
    <view class="menu-item" bindtap="onMyLikesTap">
      <view class="menu-icon like-icon">
        <text class="icon-text">❤️</text>
      </view>
      <view class="menu-content">
        <text class="menu-title">我的点赞</text>
        <text class="menu-desc">查看点赞的表情包</text>
      </view>
      <view class="menu-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view>

    <view class="menu-item" bindtap="onMyCollectionsTap">
      <view class="menu-icon collect-icon">
        <text class="icon-text">⭐</text>
      </view>
      <view class="menu-content">
        <text class="menu-title">我的收藏</text>
        <text class="menu-desc">查看收藏的表情包</text>
      </view>
      <view class="menu-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view>

    <view class="menu-item" bindtap="onDownloadHistoryTap">
      <view class="menu-icon download-icon">
        <text class="icon-text">⬇️</text>
      </view>
      <view class="menu-content">
        <text class="menu-title">下载记录</text>
        <text class="menu-desc">查看下载历史</text>
      </view>
      <view class="menu-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view>

    <!-- 设置菜单暂时隐藏 -->
    <!-- <view class="menu-item" bindtap="onSettingsTap">
      <view class="menu-icon settings-icon">
        <text class="icon-text">⚙️</text>
      </view>
      <view class="menu-content">
        <text class="menu-title">设置</text>
        <text class="menu-desc">个人设置与偏好</text>
      </view>
      <view class="menu-arrow">
        <text class="arrow-text">→</text>
      </view>
    </view> -->
  </view>

  <!-- 最近浏览 - 暂时隐藏，后续产品迭代再考虑 -->
  <!--
  <view class="recent-section" wx:if="{{recentEmojis.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近浏览</text>
      <text class="section-more" bindtap="onViewAllRecent">查看全部</text>
    </view>

    <scroll-view class="recent-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recent-list">
        <view
          class="recent-item"
          wx:for="{{recentEmojis}}"
          wx:key="id"
          bindtap="onRecentEmojiTap"
          data-emoji="{{item}}"
        >
          <image class="recent-image" src="{{item.imageUrl}}" mode="aspectFill" />
          <text class="recent-title">{{item.title}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
  -->



  <!-- 关于我们 -->
  <view class="about-section">
    <text class="about-text">表情包小程序 v1.0.0</text>
    <text class="about-desc">让表达更有趣</text>
  </view>

  <!-- 登录弹窗 -->
  <login-modal
    visible="{{showLoginModal}}"
    bind:close="onLoginModalClose"
    bind:success="onLoginSuccess"
  ></login-modal>


</view>