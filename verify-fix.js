// 验证startsWith修复的Node.js脚本
console.log('🔧 验证startsWith修复...');

// 模拟修复前的代码（会出错）
function testBrokenCode() {
    console.log('\n❌ 测试修复前的代码（预期会出错）:');
    try {
        const category = { name: '测试分类', icon: undefined };
        // 这行代码会出错：Cannot read properties of undefined (reading 'startsWith')
        const result = (category.icon && !category.icon.startsWith('data:')) ? category.icon : '📁';
        console.log('结果:', result);
    } catch (error) {
        console.log('✅ 预期错误:', error.message);
    }
}

// 模拟修复后的代码（不会出错）
function testFixedCode() {
    console.log('\n✅ 测试修复后的代码（应该正常工作）:');
    
    const testCases = [
        { name: '测试分类1', icon: undefined },
        { name: '测试分类2', icon: null },
        { name: '测试分类3', icon: '' },
        { name: '测试分类4', icon: '🎭' },
        { name: '测试分类5', icon: 'data:image/png;base64,test' }
    ];
    
    testCases.forEach((category, index) => {
        try {
            // 修复后的代码：添加了类型检查
            const result = (category.icon && typeof category.icon === 'string' && !category.icon.startsWith('data:')) ? category.icon : '📁';
            console.log(`测试用例${index + 1}: ${category.name}, icon: ${category.icon} => 结果: ${result}`);
        } catch (error) {
            console.log(`❌ 测试用例${index + 1}失败:`, error.message);
        }
    });
}

// 验证修复是否在文件中正确应用
function verifyFileChanges() {
    console.log('\n🔍 验证文件修改...');
    const fs = require('fs');
    const path = require('path');
    
    try {
        const filePath = path.join(__dirname, 'admin-serverless', 'main.html');
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查关键修复是否存在
        const fixes = [
            'typeof category.icon === \'string\' && !category.icon.startsWith(\'data:\')',
            'category.icon && typeof category.icon === \'string\' && category.icon.startsWith(\'data:image\')'
        ];
        
        let fixCount = 0;
        fixes.forEach((fix, index) => {
            if (content.includes(fix)) {
                console.log(`✅ 修复${index + 1}已应用: ${fix.substring(0, 50)}...`);
                fixCount++;
            } else {
                console.log(`❌ 修复${index + 1}未找到: ${fix.substring(0, 50)}...`);
            }
        });
        
        console.log(`\n📊 修复应用情况: ${fixCount}/${fixes.length} 个修复已应用`);
        
        if (fixCount === fixes.length) {
            console.log('🎉 所有修复都已正确应用到文件中！');
        } else {
            console.log('⚠️ 部分修复可能未正确应用，需要检查。');
        }
        
    } catch (error) {
        console.log('❌ 无法读取文件:', error.message);
    }
}

// 执行所有测试
console.log('🚀 开始验证startsWith修复...\n');

testBrokenCode();
testFixedCode();
verifyFileChanges();

console.log('\n✅ 验证完成！');
console.log('\n💡 下一步：请在浏览器中访问 http://localhost:9001/main.html 测试分类创建功能');
