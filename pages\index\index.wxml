<!--pages/index/index.wxml-->
<view class="container">




  <!-- 显示空状态（当没有数据时显示） -->
  <view wx:if="{{hotCategories.length === 0 && emojiList.length === 0 && bannerList.length === 0}}" style="text-align: center; padding: 20px;">
    <view style="color: #999; font-size: 14px; margin-bottom: 15px;">📭 暂无数据显示</view>
    <view style="color: #666; font-size: 12px; margin-bottom: 15px;">正在加载数据，请稍候...</view>
  </view>



  <!-- 顶部搜索入口 -->
  <view class="search-header">
    <view class="search-box">
      <view class="search-input-container">
        <text class="search-icon">🔍</text>
        <input
          class="search-input"
          placeholder="搜索表情包、标签或分类..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
          bindfocus="onSearchFocus"
          bindblur="onSearchBlur"
        />
        <!-- 清空按钮 -->
        <text
          class="clear-btn"
          wx:if="{{searchKeyword}}"
          bindtap="onClearSearch"
        >✕</text>
      </view>
      <button class="search-btn" bindtap="onSearchConfirm">
        {{isSearching ? '搜索中...' : '搜索'}}
      </button>
    </view>

    <!-- 搜索建议下拉框 -->
    <view class="search-suggestions" wx:if="{{showSuggestions}}">
      <view
        class="suggestion-item"
        wx:for="{{searchSuggestions}}"
        wx:key="*this"
        bindtap="onSuggestionTap"
        data-suggestion="{{item}}"
      >
        <text class="suggestion-icon">🔍</text>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>

    <!-- 实时同步状态显示 - 已隐藏以保持界面简洁 -->
    <!-- <view class="sync-status-bar" wx:if="{{lastSyncTimeText}}">
      <text class="sync-status-icon">📡</text>
      <text class="sync-status-text">{{lastSyncTimeText}}</text>
    </view> -->
  </view>

  <!-- 搜索结果 -->
  <view class="search-results-section" wx:if="{{searchResults.length > 0}}">
    <view class="section-header">
      <text class="section-title">搜索结果 ({{searchResults.length}})</text>
      <text class="clear-search" bindtap="onClearSearch">清空</text>
    </view>

    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{searchResults}}"
        wx:key="id"
        bindtap="onEmojiTap"
        data-emoji="{{item}}"
      >
        <!-- 表情包图片 -->
        <view class="emoji-image-container">
          <image
            class="emoji-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
        </view>

        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <text class="emoji-category">{{item.categoryName || item.category}}</text>

          <!-- 标签区域 -->
          <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <view
              class="tag-item"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >
              {{tag}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- Banner轮播图 -->
  <view class="banner-section" wx:if="{{searchResults.length === 0 && bannerList.length > 0}}">
    <swiper 
      class="banner-swiper" 
      indicator-dots="true" 
      autoplay="true" 
      interval="3000" 
      duration="500"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#ffffff"
    >
      <swiper-item 
        wx:for="{{bannerList}}" 
        wx:key="id"
        bindtap="onBannerTap"
        data-banner="{{item}}"
      >
        <view class="banner-item">
          <image class="banner-image" src="{{item.imageUrl}}" mode="aspectFill" />
          <!-- 横幅文字和按钮已隐藏，只显示图片 -->
          <!-- <view class="banner-overlay">
            <view class="banner-content">
              <text class="banner-title">{{item.title}}</text>
              <text class="banner-subtitle">{{item.subtitle}}</text>
              <view class="banner-button">
                <text class="banner-button-text">{{item.buttonText}}</text>
              </view>
            </view>
          </view> -->
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 热门分类 -->
  <view class="category-section" wx:if="{{searchResults.length === 0 && hotCategories.length > 0}}">
    <view class="section-header">
      <text class="section-title">热门分类</text>
      <text class="section-more" bindtap="onViewAllCategories">查看全部</text>
    </view>
    <view class="category-grid">
      <view 
        class="category-item"
        wx:for="{{hotCategories}}" 
        wx:key="id"
        bindtap="onCategoryTap"
        data-category="{{item}}"
      >
        <view class="category-icon" style="background: {{item.gradient}}">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="category-info">
          <text class="category-name">{{item.name}}</text>
          <text class="category-count">{{item.count}} 个表情</text>
        </view>
        <view class="category-arrow">
          <text class="arrow-icon">→</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门表情包 -->
  <view class="emoji-section" wx:if="{{searchResults.length === 0 && emojiList.length > 0}}">
    <view class="section-header">
      <text class="section-title">热门表情包</text>
    </view>
    
    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{emojiList}}"
        wx:key="_id"
        bindtap="onEmojiTap"
        data-emoji="{{item}}"
      >
        <!-- 表情包图片 -->
        <view class="emoji-image-container">
          <image
            class="emoji-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
        </view>
        
        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <text class="emoji-category">{{item.categoryName || item.category}}</text>
          
          <!-- 标签区域 -->
          <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <view
              class="tag-item"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >
              {{tag}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页加载状态 -->
    <view class="pagination-status" wx:if="{{emojiList.length > 0}}">
      <view wx:if="{{pagination.loading}}" class="loading-more">
        <text class="loading-text">正在加载更多...</text>
      </view>
      <view wx:elif="{{!pagination.hasMore}}" class="no-more">
        <text class="no-more-text">已加载全部内容</text>
      </view>
      <view wx:else class="load-more-hint">
        <text class="hint-text">上拉加载更多</text>
      </view>
    </view>
  </view>


</view>