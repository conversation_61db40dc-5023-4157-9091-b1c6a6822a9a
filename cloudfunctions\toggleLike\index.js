// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { emojiId, isLiked } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 更新表情包的点赞数
    const emojiResult = await db.collection('emojis').doc(emojiId).update({
      data: {
        likes: _.inc(isLiked ? 1 : -1)
      }
    })
    
    // 记录用户点赞状态
    if (isLiked) {
      await db.collection('user_likes').add({
        data: {
          userId: OPENID,
          emojiId: emojiId,
          createTime: new Date()
        }
      })
    } else {
      await db.collection('user_likes').where({
        userId: OPENID,
        emojiId: emojiId
      }).remove()
    }
    
    return {
      success: true,
      message: isLiked ? '点赞成功' : '取消点赞成功'
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}