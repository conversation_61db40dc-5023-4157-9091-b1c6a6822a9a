<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类筛选测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin: 5px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn:hover { opacity: 0.9; }
        .category-item {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        .error { color: #dc2626; background: #fee2e2; }
        .success { color: #059669; background: #d1fae5; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 分类筛选功能测试</h1>
        
        <div class="test-section">
            <h3>1. 模拟分类数据测试</h3>
            <p>测试不同类型的分类数据处理：</p>
            <div id="category-data-test"></div>
            <button class="btn btn-primary" onclick="testCategoryData()">测试分类数据处理</button>
        </div>

        <div class="test-section">
            <h3>2. 筛选器选项生成测试</h3>
            <p>测试分类筛选下拉框的选项生成：</p>
            <select id="test-category-filter" class="form-input" style="width: 200px;">
                <option value="">全部分类</option>
            </select>
            <button class="btn btn-success" onclick="generateFilterOptions()">生成筛选选项</button>
            <div id="filter-results"></div>
        </div>

        <div class="test-section">
            <h3>3. 问题数据修复测试</h3>
            <p>测试包含base64图片数据的分类处理：</p>
            <div id="problem-data-test"></div>
            <button class="btn btn-warning" onclick="testProblemData()">测试问题数据修复</button>
        </div>

        <div class="test-section">
            <h3>4. 实际数据结构预览</h3>
            <p>查看处理后的分类数据结构：</p>
            <pre id="data-structure"></pre>
        </div>
    </div>

    <script>
        // 模拟问题数据
        const problemCategoryData = [
            {
                _id: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                name: '测试分类',
                icon: '😊',
                status: 'show'
            },
            {
                _id: '2',
                name: '情感类',
                icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                status: 'show'
            },
            {
                _id: '3',
                name: '动物类',
                icon: '🐱',
                status: 'show'
            },
            {
                _id: '',
                name: '生活类',
                icon: '🏠',
                status: 'show'
            },
            {
                _id: '5',
                name: '',
                icon: '🎉',
                status: 'show'
            }
        ];

        // 清理分类数据的函数（不创建虚拟数据）
        function cleanCategoryData(categories) {
            return categories.map(category => {
                const cleaned = {
                    _id: category._id || category.id,
                    name: category.name,
                    icon: category.icon || '📁',
                    status: category.status || 'show',
                    sort: category.sort || 0,
                    count: category.count || 0,
                    description: category.description || ''
                };

                // 如果图标是base64图片，替换为默认emoji
                if (typeof cleaned.icon === 'string' && cleaned.icon.startsWith('data:')) {
                    cleaned.icon = '📁';
                }

                return cleaned;
            }).filter(category =>
                // 过滤掉无效的分类（包括虚拟分类）
                category._id &&
                category.name &&
                typeof category.name === 'string' &&
                category.name.trim() !== '' &&
                category.name !== '未命名分类' &&
                !category._id.toString().startsWith('temp_') &&
                !category._id.toString().startsWith('fixed_')
            );
        }

        // 生成筛选选项的函数（从主文件复制并修改）
        function updateCategoryFilter(categories, selectElement) {
            // 清空现有选项（保留"全部分类"）
            selectElement.innerHTML = '<option value="">全部分类</option>';

            if (categories && categories.length > 0) {
                const visibleCategories = categories.filter(cat => {
                    return cat && 
                           cat.name && 
                           typeof cat.name === 'string' && 
                           cat.name.trim() !== '' && 
                           cat.status !== 'hide';
                });

                visibleCategories.forEach(category => {
                    let categoryValue = '';
                    
                    // 确保使用正确的ID字段，避免使用可能包含base64数据的字段
                    if (category._id && typeof category._id === 'string' && !category._id.startsWith('data:')) {
                        categoryValue = category._id;
                    } else if (category.id && typeof category.id === 'string' && !category.id.startsWith('data:')) {
                        categoryValue = category.id;
                    } else {
                        categoryValue = category.name;
                    }

                    // 处理图标显示
                    let displayIcon = '📁';
                    if (category.icon) {
                        if (typeof category.icon === 'string' && !category.icon.startsWith('data:')) {
                            displayIcon = category.icon;
                        }
                    }

                    const option = document.createElement('option');
                    option.value = categoryValue;
                    option.textContent = `${displayIcon} ${category.name}`;
                    selectElement.appendChild(option);
                });
            }
        }

        function testCategoryData() {
            const cleanedData = cleanCategoryData(problemCategoryData);
            
            let html = '<h4>原始数据 vs 清理后数据：</h4>';
            html += '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">';
            
            html += '<div><h5>原始数据：</h5>';
            problemCategoryData.forEach((cat, index) => {
                const isError = cat._id.startsWith('data:') || cat.icon.startsWith('data:') || !cat.name;
                html += `<div class="category-item ${isError ? 'error' : ''}">
                    ID: ${cat._id.length > 20 ? cat._id.substring(0, 20) + '...' : cat._id}<br>
                    名称: ${cat.name || '(空)'}<br>
                    图标: ${cat.icon.length > 10 ? cat.icon.substring(0, 10) + '...' : cat.icon}
                </div>`;
            });
            html += '</div>';
            
            html += '<div><h5>清理后数据：</h5>';
            cleanedData.forEach(cat => {
                html += `<div class="category-item success">
                    ID: ${cat._id}<br>
                    名称: ${cat.name}<br>
                    图标: ${cat.icon}
                </div>`;
            });
            html += '</div>';
            
            html += '</div>';
            document.getElementById('category-data-test').innerHTML = html;
            
            // 更新数据结构预览
            document.getElementById('data-structure').textContent = JSON.stringify(cleanedData, null, 2);
        }

        function generateFilterOptions() {
            const cleanedData = cleanCategoryData(problemCategoryData);
            const filterSelect = document.getElementById('test-category-filter');
            
            updateCategoryFilter(cleanedData, filterSelect);
            
            let html = '<h4>生成的筛选选项：</h4>';
            html += '<ul>';
            for (let i = 0; i < filterSelect.options.length; i++) {
                const option = filterSelect.options[i];
                html += `<li><strong>值:</strong> "${option.value}" <strong>显示:</strong> "${option.textContent}"</li>`;
            }
            html += '</ul>';
            
            document.getElementById('filter-results').innerHTML = html;
        }

        function testProblemData() {
            let html = '<h4>问题数据识别和修复：</h4>';
            
            problemCategoryData.forEach((cat, index) => {
                const problems = [];
                
                if (cat._id.startsWith('data:')) {
                    problems.push('ID字段包含base64数据');
                }
                if (cat.icon.startsWith('data:')) {
                    problems.push('图标字段包含base64数据');
                }
                if (!cat.name || cat.name.trim() === '') {
                    problems.push('名称为空');
                }
                if (!cat._id || cat._id.trim() === '') {
                    problems.push('ID为空');
                }
                
                const hasProblems = problems.length > 0;
                html += `<div class="category-item ${hasProblems ? 'error' : 'success'}">
                    <strong>分类 ${index + 1}:</strong> ${cat.name || '(无名称)'}<br>
                    ${hasProblems ? '<strong>问题:</strong> ' + problems.join(', ') : '<strong>状态:</strong> 正常'}
                </div>`;
            });
            
            document.getElementById('problem-data-test').innerHTML = html;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            testCategoryData();
            generateFilterOptions();
            testProblemData();
        };
    </script>
</body>
</html>
