// 小程序端 - 云数据库实时监听器
class CloudDatabaseWatcher {
  constructor() {
    this.watchers = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.db = null;
    
    console.log('📡 CloudDatabaseWatcher初始化');
  }

  // 初始化数据库连接
  initDatabase() {
    if (this.db) return this.db;

    try {
      this.db = wx.cloud.database();
      console.log('✅ 云数据库初始化成功');
      return this.db;
    } catch (error) {
      console.error('❌ 云数据库初始化失败:', error);
      throw error;
    }
  }

  // 初始化所有监听器
  async initWatchers() {
    try {
      console.log('🔄 启动小程序端实时监听...');

      // 确保数据库已初始化
      if (!this.db) {
        this.initDatabase();
      }

      // 监听分类数据
      await this.watchCategories();
      
      // 监听表情包数据
      await this.watchEmojis();
      
      // 监听横幅数据
      await this.watchBanners();

      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      console.log('✅ 小程序端实时监听已启动');
      this.notifyConnectionStatus('connected');
      
    } catch (error) {
      console.error('❌ 初始化监听失败:', error);
      this.isConnected = false;
      this.notifyConnectionStatus('failed');
      this.scheduleReconnect();
    }
  }

  // 监听分类数据
  async watchCategories() {
    try {
      const watcher = this.db.collection('categories')
        .where({
          status: 'active'
        })
        .orderBy('sort', 'asc')
        .orderBy('createTime', 'desc')
        .watch({
          onChange: (snapshot) => {
            this.handleCategoriesChange(snapshot);
          },
          onError: (error) => {
            console.error('❌ 分类数据监听失败:', error);
            this.handleWatchError('categories', error);
          }
        });

      this.watchers.set('categories', watcher);
      console.log('✅ 分类数据监听已启动');
      
    } catch (error) {
      console.error('❌ 启动分类数据监听失败:', error);
      throw error;
    }
  }

  // 监听表情包数据
  async watchEmojis() {
    try {
      const watcher = this.db.collection('emojis')
        .where({
          status: 'published'
        })
        .orderBy('createTime', 'desc')
        .limit(100) // 限制数量避免性能问题
        .watch({
          onChange: (snapshot) => {
            this.handleEmojisChange(snapshot);
          },
          onError: (error) => {
            console.error('❌ 表情包数据监听失败:', error);
            this.handleWatchError('emojis', error);
          }
        });

      this.watchers.set('emojis', watcher);
      console.log('✅ 表情包数据监听已启动');
      
    } catch (error) {
      console.error('❌ 启动表情包数据监听失败:', error);
      throw error;
    }
  }

  // 监听横幅数据
  async watchBanners() {
    try {
      const watcher = this.db.collection('banners')
        .where({
          status: 'active'
        })
        .orderBy('sort', 'asc')
        .orderBy('createTime', 'desc')
        .watch({
          onChange: (snapshot) => {
            this.handleBannersChange(snapshot);
          },
          onError: (error) => {
            console.error('❌ 横幅数据监听失败:', error);
            this.handleWatchError('banners', error);
          }
        });

      this.watchers.set('banners', watcher);
      console.log('✅ 横幅数据监听已启动');
      
    } catch (error) {
      console.error('❌ 启动横幅数据监听失败:', error);
      throw error;
    }
  }

  // 处理分类数据变更
  handleCategoriesChange(snapshot) {
    const { docs, type, docChanges } = snapshot;
    
    console.log('📁 分类数据变更:', { type, changeCount: docChanges?.length || 0 });
    
    if (type === 'init') {
      // 初始化数据
      this.updateLocalCache('categories', docs);
      this.notifyPageUpdate('categories', 'init', docs);
      console.log('📁 分类数据初始化完成，数量:', docs.length);
    } else if (type === 'update' && docChanges) {
      // 处理数据变更
      this.processDocChanges('categories', docChanges);
    }
  }

  // 处理表情包数据变更
  handleEmojisChange(snapshot) {
    const { docs, type, docChanges } = snapshot;
    
    console.log('😀 表情包数据变更:', { type, changeCount: docChanges?.length || 0 });
    
    if (type === 'init') {
      this.updateLocalCache('emojis', docs);
      this.notifyPageUpdate('emojis', 'init', docs);
      console.log('😀 表情包数据初始化完成，数量:', docs.length);
    } else if (type === 'update' && docChanges) {
      this.processDocChanges('emojis', docChanges);
    }
  }

  // 处理横幅数据变更
  handleBannersChange(snapshot) {
    const { docs, type, docChanges } = snapshot;
    
    console.log('🎯 横幅数据变更:', { type, changeCount: docChanges?.length || 0 });
    
    if (type === 'init') {
      this.updateLocalCache('banners', docs);
      this.notifyPageUpdate('banners', 'init', docs);
      console.log('🎯 横幅数据初始化完成，数量:', docs.length);
    } else if (type === 'update' && docChanges) {
      this.processDocChanges('banners', docChanges);
    }
  }

  // 处理文档变更
  processDocChanges(collectionName, docChanges) {
    const cache = this.getLocalCache(collectionName);
    let hasChanges = false;

    docChanges.forEach(change => {
      const { queueType, doc } = change;
      
      switch (queueType) {
        case 'enqueue':
          // 新增或更新
          const index = cache.findIndex(item => item._id === doc._id);
          if (index !== -1) {
            cache[index] = doc;
            console.log(`📝 ${collectionName} 更新:`, doc.id || doc._id);
          } else {
            cache.push(doc);
            console.log(`➕ ${collectionName} 新增:`, doc.id || doc._id);
          }
          hasChanges = true;
          break;
          
        case 'dequeue':
          // 删除
          const removeIndex = cache.findIndex(item => item._id === doc._id);
          if (removeIndex !== -1) {
            cache.splice(removeIndex, 1);
            console.log(`➖ ${collectionName} 删除:`, doc.id || doc._id);
            hasChanges = true;
          }
          break;
      }
    });

    if (hasChanges) {
      // 更新本地缓存
      this.updateLocalCache(collectionName, cache);
      
      // 通知页面更新
      this.notifyPageUpdate(collectionName, 'update', cache);
    }
  }

  // 更新本地缓存
  updateLocalCache(collectionName, data) {
    try {
      wx.setStorageSync(`${collectionName}_cache`, data);
      wx.setStorageSync(`${collectionName}_cache_time`, Date.now());
      console.log(`💾 ${collectionName} 缓存已更新，数量:`, data.length);
    } catch (error) {
      console.error(`❌ 更新${collectionName}缓存失败:`, error);
    }
  }

  // 获取本地缓存
  getLocalCache(collectionName) {
    try {
      return wx.getStorageSync(`${collectionName}_cache`) || [];
    } catch (error) {
      console.error(`❌ 获取${collectionName}缓存失败:`, error);
      return [];
    }
  }

  // 通知页面数据更新
  notifyPageUpdate(collectionName, operation, data) {
    // 获取当前页面栈
    const pages = getCurrentPages();
    
    pages.forEach(page => {
      // 检查页面是否有数据更新处理方法
      if (page.onDataUpdate && typeof page.onDataUpdate === 'function') {
        try {
          page.onDataUpdate(collectionName, operation, data);
        } catch (error) {
          console.error('页面数据更新处理失败:', error);
        }
      }
      
      // 检查页面是否有特定集合的更新处理方法
      const methodName = `on${collectionName.charAt(0).toUpperCase() + collectionName.slice(1)}Update`;
      if (page[methodName] && typeof page[methodName] === 'function') {
        try {
          page[methodName](operation, data);
        } catch (error) {
          console.error(`页面${methodName}处理失败:`, error);
        }
      }
    });

    // 发送全局事件
    this.emitGlobalEvent('dataUpdate', {
      collectionName,
      operation,
      data,
      timestamp: Date.now()
    });
  }

  // 通知连接状态
  notifyConnectionStatus(status) {
    const pages = getCurrentPages();
    
    pages.forEach(page => {
      if (page.onConnectionStatusChange && typeof page.onConnectionStatusChange === 'function') {
        try {
          page.onConnectionStatusChange(status);
        } catch (error) {
          console.error('连接状态通知失败:', error);
        }
      }
    });

    // 发送全局事件
    this.emitGlobalEvent('connectionStatusChange', {
      status,
      timestamp: Date.now()
    });
  }

  // 发送全局事件
  emitGlobalEvent(eventName, data) {
    // 可以通过全局事件总线发送事件
    if (getApp().globalData && getApp().globalData.eventBus) {
      getApp().globalData.eventBus.emit(eventName, data);
    }
  }

  // 错误处理和重连
  handleWatchError(watcherName, error) {
    console.error(`❌ 监听器 ${watcherName} 出错:`, error);
    
    this.isConnected = false;
    this.notifyConnectionStatus('error');
    
    // 清理当前监听器
    const watcher = this.watchers.get(watcherName);
    if (watcher) {
      try {
        watcher.close();
      } catch (closeError) {
        console.warn('关闭监听器失败:', closeError);
      }
      this.watchers.delete(watcherName);
    }
    
    // 安排重连
    this.scheduleReconnect();
  }

  // 安排重连
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ 达到最大重连次数，停止重连');
      this.notifyConnectionStatus('failed');
      return;
    }
    
    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    this.notifyConnectionStatus('reconnecting');
    
    setTimeout(() => {
      this.initWatchers();
    }, delay);
  }

  // 手动刷新数据
  async refreshData(collectionName) {
    try {
      console.log(`🔄 手动刷新${collectionName}数据...`);
      
      let query;
      switch (collectionName) {
        case 'categories':
          query = this.db.collection('categories')
            .where({ status: 'active' })
            .orderBy('sort', 'asc');
          break;
        case 'emojis':
          query = this.db.collection('emojis')
            .where({ status: 'published' })
            .orderBy('createTime', 'desc')
            .limit(100);
          break;
        case 'banners':
          query = this.db.collection('banners')
            .where({ status: 'active' })
            .orderBy('sort', 'asc');
          break;
        default:
          throw new Error('未知的集合名称');
      }

      const result = await query.get();
      
      if (result.data) {
        this.updateLocalCache(collectionName, result.data);
        this.notifyPageUpdate(collectionName, 'refresh', result.data);
        console.log(`✅ ${collectionName}数据刷新完成，数量:`, result.data.length);
        return result.data;
      }
      
    } catch (error) {
      console.error(`❌ 刷新${collectionName}数据失败:`, error);
      throw error;
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      watchersCount: this.watchers.size,
      watchers: Array.from(this.watchers.keys())
    };
  }

  // 清理资源
  destroy() {
    console.log('🧹 清理CloudDatabaseWatcher资源...');
    
    this.watchers.forEach((watcher, name) => {
      try {
        watcher.close();
        console.log(`✅ 关闭监听器: ${name}`);
      } catch (error) {
        console.warn(`⚠️ 关闭监听器失败: ${name}`, error);
      }
    });
    
    this.watchers.clear();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    
    console.log('✅ CloudDatabaseWatcher资源清理完成');
  }
}

// 导出类
module.exports = CloudDatabaseWatcher;
