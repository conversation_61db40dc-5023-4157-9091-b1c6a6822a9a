/**
 * 实时同步管理器
 * 整合版本管理和增量同步，提供统一的实时同步接口
 */

const { VersionManager } = require('./versionManager.js')
const { IncrementalSync } = require('./incrementalSync.js')
const { EnvironmentConfig } = require('../config/environment.js')

const RealtimeSync = {
  // 同步配置
  config: {
    enableRealtime: true,
    syncInterval: 30000,      // 30秒检查一次
    maxRetries: 3,
    retryDelay: 5000,
    backgroundSync: true      // 后台同步
  },

  // 同步状态
  status: {
    initialized: false,
    running: false,
    lastSyncTime: null,
    nextSyncTime: null,
    errorCount: 0
  },

  // 定时器
  syncTimer: null,

  /**
   * 初始化实时同步
   */
  init(options = {}) {
    console.log('🚀 初始化实时同步管理器...')
    
    // 合并配置
    this.config = { ...this.config, ...options }
    
    // 在开发环境中禁用实时同步，避免频繁请求
    if (EnvironmentConfig.isDevelopment() && !options.forceEnable) {
      console.log('⚠️ 开发环境：实时同步已禁用')
      this.config.enableRealtime = false
      return
    }

    try {
      // 初始化版本管理器
      VersionManager.init()
      
      // 初始化增量同步
      IncrementalSync.init({
        batchSize: 20,
        timeout: this.config.syncInterval / 2
      })

      this.status.initialized = true
      console.log('✅ 实时同步管理器初始化完成')

      // 如果启用实时同步，开始定时同步
      if (this.config.enableRealtime) {
        this.startSync()
      }

    } catch (error) {
      console.error('❌ 实时同步初始化失败:', error.message)
      this.status.initialized = false
    }
  },

  /**
   * 开始同步
   */
  startSync() {
    if (!this.status.initialized) {
      console.error('❌ 实时同步未初始化')
      return
    }

    if (this.status.running) {
      console.log('⚠️ 实时同步已在运行')
      return
    }

    console.log('🔄 开始实时同步服务...')
    this.status.running = true

    // 立即执行一次同步
    this.performSync()

    // 设置定时同步
    this.syncTimer = setInterval(() => {
      this.performSync()
    }, this.config.syncInterval)

    console.log(`✅ 实时同步已启动，间隔: ${this.config.syncInterval}ms`)
  },

  /**
   * 停止同步
   */
  stopSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }

    this.status.running = false
    console.log('⏹️ 实时同步已停止')
  },

  /**
   * 执行同步
   */
  async performSync() {
    if (!this.config.enableRealtime) {
      return
    }

    try {
      console.log('🔄 执行定时同步检查...')
      
      // 检查并同步更新
      const result = await IncrementalSync.checkAndSync()
      
      if (result.success) {
        this.status.lastSyncTime = Date.now()
        this.status.nextSyncTime = this.status.lastSyncTime + this.config.syncInterval
        this.status.errorCount = 0

        if (result.hasUpdates) {
          console.log('✅ 同步完成，发现并应用了更新')
          
          // 触发全局同步事件
          this.triggerSyncEvent('sync_completed', {
            hasUpdates: true,
            results: result.results
          })
        } else {
          console.log('✅ 同步检查完成，数据已是最新')
        }
      } else {
        throw new Error(result.error || '同步失败')
      }

    } catch (error) {
      console.error('❌ 定时同步失败:', error.message)
      this.status.errorCount++

      // 如果错误次数过多，暂停同步
      if (this.status.errorCount >= this.config.maxRetries) {
        console.error('❌ 同步错误次数过多，暂停实时同步')
        this.stopSync()
        
        this.triggerSyncEvent('sync_error', {
          error: error.message,
          errorCount: this.status.errorCount
        })
      }
    }
  },

  /**
   * 手动同步
   */
  async manualSync() {
    console.log('🔄 执行手动同步...')
    
    try {
      const result = await IncrementalSync.syncAllData()
      
      if (result.success) {
        this.status.lastSyncTime = Date.now()
        this.status.errorCount = 0
        
        console.log('✅ 手动同步完成')
        
        this.triggerSyncEvent('manual_sync_completed', {
          results: result.results
        })
        
        return { success: true, results: result.results }
      } else {
        throw new Error(result.error || '手动同步失败')
      }

    } catch (error) {
      console.error('❌ 手动同步失败:', error.message)
      
      this.triggerSyncEvent('sync_error', {
        error: error.message,
        type: 'manual'
      })
      
      return { success: false, error: error.message }
    }
  },

  /**
   * 强制全量同步
   */
  async forceSync() {
    console.log('🔄 执行强制全量同步...')

    try {
      // 调用云函数执行强制同步
      const result = await this.callSyncAPI('forceSyncAll')

      if (result.success) {
        // 清除本地版本信息，强制重新同步
        VersionManager.resetVersions()

        // 执行全量同步
        const syncResult = await IncrementalSync.syncAllData()

        this.status.lastSyncTime = Date.now()
        this.status.errorCount = 0

        console.log('✅ 强制全量同步完成')

        this.triggerSyncEvent('force_sync_completed', {
          results: result.results,
          syncResults: syncResult.results
        })

        return {
          success: true,
          results: result.results,
          syncResults: syncResult.results
        }
      } else {
        throw new Error(result.error || '强制同步失败')
      }

    } catch (error) {
      console.error('❌ 强制同步失败:', error.message)
      return { success: false, error: error.message }
    }
  },

  /**
   * 添加数据更新监听器
   */
  addListener(type, callback) {
    IncrementalSync.addListener(type, callback)
  },

  /**
   * 移除数据更新监听器
   */
  removeListener(type, callback) {
    IncrementalSync.removeListener(type, callback)
  },

  /**
   * 触发同步事件
   */
  triggerSyncEvent(eventType, data) {
    try {
      // 可以在这里添加全局事件处理
      console.log(`📡 同步事件: ${eventType}`, data)
      
      // 如果有全局事件总线，可以在这里触发
      if (typeof getApp === 'function') {
        const app = getApp()
        if (app.globalData && app.globalData.eventBus) {
          app.globalData.eventBus.emit('sync_event', {
            type: eventType,
            data
          })
        }
      }
    } catch (error) {
      console.error('❌ 触发同步事件失败:', error.message)
    }
  },

  /**
   * 获取同步状态
   */
  getStatus() {
    return {
      ...this.status,
      config: { ...this.config },
      incrementalSyncStatus: IncrementalSync.getSyncStatus(),
      versionInfo: VersionManager.getLocalVersions()
    }
  },

  /**
   * 获取版本差异
   */
  async getVersionDiff() {
    return await VersionManager.getVersionDiff()
  },

  /**
   * 调用同步云函数
   */
  async callSyncAPI(action, data = {}) {
    try {
      console.log(`☁️ 调用同步API: ${action}`, data)

      // 检查云开发是否已初始化
      if (typeof wx === 'undefined' || !wx.cloud) {
        throw new Error('云开发未初始化')
      }

      const result = await wx.cloud.callFunction({
        name: 'syncAPI',
        data: {
          action,
          data
        }
      })

      console.log(`☁️ 云函数返回结果:`, result)

      if (result.result && result.result.success) {
        console.log(`✅ 同步API调用成功: ${action}`)
        return result.result
      } else {
        const errorMsg = result.result?.error || result.errMsg || '同步API调用失败'
        throw new Error(errorMsg)
      }

    } catch (error) {
      console.error(`❌ 同步API调用失败: ${action}`, error)

      // 如果是云函数不存在的错误，提供更明确的提示
      if (error.errMsg && error.errMsg.includes('cloud function not found')) {
        throw new Error(`云函数 syncAPI 不存在，请先部署云函数`)
      }

      throw error
    }
  },

  /**
   * 从服务器获取版本信息
   */
  async getServerVersions() {
    try {
      const result = await this.callSyncAPI('getVersions')
      return result.versions || {}
    } catch (error) {
      console.error('❌ 获取服务器版本失败:', error)
      return {}
    }
  },

  /**
   * 获取增量数据
   */
  async getIncrementalData(collection, clientVersion = 0) {
    try {
      const result = await this.callSyncAPI('getIncrementalData', {
        collection,
        clientVersion,
        limit: 50
      })

      return {
        success: true,
        hasChanges: result.hasChanges,
        changes: result.changes || [],
        serverVersion: result.serverVersion,
        clientVersion: result.clientVersion
      }

    } catch (error) {
      console.error(`❌ 获取增量数据失败: ${collection}`, error)
      return {
        success: false,
        error: error.message,
        hasChanges: false,
        changes: []
      }
    }
  },

  /**
   * 同步数据到服务器
   */
  async syncDataToServer(collection, changes) {
    try {
      const result = await this.callSyncAPI('syncData', {
        collection,
        changes
      })

      return {
        success: true,
        results: result.results || [],
        successCount: result.successCount || 0,
        totalCount: result.totalCount || 0,
        newVersion: result.newVersion
      }

    } catch (error) {
      console.error(`❌ 同步数据到服务器失败: ${collection}`, error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  /**
   * 重置同步状态
   */
  reset() {
    this.stopSync()
    
    this.status = {
      initialized: false,
      running: false,
      lastSyncTime: null,
      nextSyncTime: null,
      errorCount: 0
    }
    
    IncrementalSync.resetSyncStatus()
    VersionManager.resetVersions()
    
    console.log('🔄 实时同步状态已重置')
  },

  /**
   * 检查是否需要同步
   */
  needsSync() {
    if (!this.status.lastSyncTime) {
      return true
    }
    
    const timeSinceLastSync = Date.now() - this.status.lastSyncTime
    return timeSinceLastSync > this.config.syncInterval
  }
}

module.exports = {
  RealtimeSync
}
