// pages/test-emoji/test-emoji.js
Page({
  data: {
    testResults: [],
    currentTest: '',
    emojiList: [],
    searchResults: []
  },

  onLoad() {
    console.log('🧪 测试页面加载');
    this.addLog('测试页面初始化完成');
  },

  onReady() {
    console.log('🧪 测试页面渲染完成');
    this.addLog('页面渲染完成，可以开始测试');
  },

  // 添加日志
  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const log = {
      time: timestamp,
      message: message,
      type: type,
      id: Date.now()
    };
    
    const logs = this.data.testResults;
    logs.push(log);
    
    this.setData({
      testResults: logs
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  // 清空日志
  clearLogs() {
    this.setData({
      testResults: [],
      currentTest: ''
    });
  },

  // 测试1: 云函数调用
  async testCloudFunction() {
    this.setData({ currentTest: '测试云函数调用' });
    this.addLog('🔄 开始测试云函数调用', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: 'all',
            page: 1,
            limit: 5
          }
        }
      });

      this.addLog(`📥 云函数返回: ${JSON.stringify(result.result)}`, 'info');

      if (result.result && result.result.success) {
        this.addLog(`✅ 云函数调用成功，获取 ${result.result.data.length} 条数据`, 'success');
        return result.result.data;
      } else {
        this.addLog('❌ 云函数返回失败', 'error');
        return null;
      }
    } catch (error) {
      this.addLog(`❌ 云函数调用异常: ${error.message}`, 'error');
      return null;
    }
  },

  // 测试2: 设置表情包数据
  testSetEmojiData() {
    this.setData({ currentTest: '测试设置表情包数据' });
    this.addLog('🔄 开始测试设置表情包数据', 'info');

    const mockData = [
      {
        "_id": "test1",
        "title": "测试表情1",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2NjAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIg8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test2",
        "title": "测试表情2", 
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZkNzAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIo8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test3",
        "title": "测试表情3",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBiY2ZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmI08L3RleHQ+Cjwvc3ZnPgo="
      }
    ];

    this.setData({
      emojiList: mockData,
      searchResults: []
    });

    this.addLog(`✅ 设置了 ${mockData.length} 条测试数据`, 'success');
    this.addLog(`📊 当前状态: emojiList=${this.data.emojiList.length}, searchResults=${this.data.searchResults.length}`, 'info');
  },

  // 测试3: 使用云函数数据
  async testWithCloudData() {
    this.setData({ currentTest: '测试使用云函数数据' });
    this.addLog('🔄 开始测试使用云函数数据', 'info');

    const cloudData = await this.testCloudFunction();
    
    if (cloudData && cloudData.length > 0) {
      this.setData({
        emojiList: cloudData,
        searchResults: []
      });
      
      this.addLog(`✅ 使用云函数数据设置成功，${cloudData.length} 条数据`, 'success');
      this.addLog(`📊 当前状态: emojiList=${this.data.emojiList.length}, searchResults=${this.data.searchResults.length}`, 'info');
    } else {
      this.addLog('❌ 云函数数据获取失败，无法设置', 'error');
    }
  },

  // 测试4: 检查显示条件
  testDisplayCondition() {
    this.setData({ currentTest: '测试显示条件' });
    this.addLog('🔄 开始检查显示条件', 'info');

    const emojiLength = this.data.emojiList.length;
    const searchLength = this.data.searchResults.length;
    const shouldShow = searchLength === 0 && emojiLength > 0;

    this.addLog(`📊 数据状态:`, 'info');
    this.addLog(`- emojiList.length: ${emojiLength}`, 'info');
    this.addLog(`- searchResults.length: ${searchLength}`, 'info');
    this.addLog(`- searchResults.length === 0: ${searchLength === 0}`, 'info');
    this.addLog(`- emojiList.length > 0: ${emojiLength > 0}`, 'info');
    this.addLog(`- 最终显示条件: ${shouldShow}`, shouldShow ? 'success' : 'error');

    if (shouldShow) {
      this.addLog('✅ 显示条件满足，表情包列表应该显示', 'success');
    } else {
      this.addLog('❌ 显示条件不满足，表情包列表不会显示', 'error');
    }
  },

  // 运行所有测试
  async runAllTests() {
    this.clearLogs();
    this.addLog('🚀 开始运行所有测试', 'info');
    
    // 测试1: 云函数
    await this.testCloudFunction();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试2: 设置模拟数据
    this.testSetEmojiData();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试3: 检查显示条件
    this.testDisplayCondition();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试4: 使用云函数数据
    await this.testWithCloudData();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 最终检查
    this.testDisplayCondition();
    
    this.addLog('🎉 所有测试完成', 'success');
    this.setData({ currentTest: '测试完成' });
  },

  // 表情包点击事件
  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji;
    this.addLog(`🔄 点击表情包: ${emoji.title} (${emoji._id})`, 'info');
    
    wx.showModal({
      title: '表情包信息',
      content: `标题: ${emoji.title}\nID: ${emoji._id}`,
      showCancel: false
    });
  }
});
