// 测试数据结构修复效果
const { chromium } = require('playwright');

async function testDataStructureFix() {
    console.log('🔧 测试数据结构修复效果...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('修复数据结构') || text.includes('Web SDK获取成功') || text.includes('渲染表情包图片')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：测试修复后的数据查询');
        
        // 直接查询数据库数据
        const fixedDataResult = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('emojis');
                
                if (result.success && result.data) {
                    return {
                        success: true,
                        count: result.data.length,
                        sampleData: result.data.slice(-3).map(item => ({
                            _id: item._id,
                            title: item.title,
                            status: item.status,
                            category: item.category,
                            imageUrl: item.imageUrl ? item.imageUrl.substring(0, 50) + '...' : 'N/A',
                            createTime: item.createTime,
                            hasTitle: !!item.title,
                            hasStatus: !!item.status,
                            hasCategory: !!item.category,
                            titleType: typeof item.title,
                            statusType: typeof item.status
                        }))
                    };
                } else {
                    return {
                        success: false,
                        error: result.error || '查询失败'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 修复后数据查询结果:');
        console.log(`查询成功: ${fixedDataResult.success}`);
        
        if (fixedDataResult.success) {
            console.log(`数据条数: ${fixedDataResult.count}`);
            
            console.log('\n📋 修复后的数据样本:');
            fixedDataResult.sampleData.forEach((item, index) => {
                console.log(`\n数据 ${index + 1}:`);
                console.log(`  ID: ${item._id}`);
                console.log(`  标题: ${item.title} (类型: ${item.titleType}, 存在: ${item.hasTitle})`);
                console.log(`  状态: ${item.status} (类型: ${item.statusType}, 存在: ${item.hasStatus})`);
                console.log(`  分类: ${item.category} (存在: ${item.hasCategory})`);
                console.log(`  图片URL: ${item.imageUrl}`);
                console.log(`  创建时间: ${item.createTime}`);
                
                // 检查修复效果
                if (item.hasTitle && item.hasStatus) {
                    console.log(`  ✅ 数据结构修复成功`);
                } else {
                    console.log(`  🔴 数据结构仍有问题`);
                }
            });
            
            // 统计修复效果
            const fixedCount = fixedDataResult.sampleData.filter(item => item.hasTitle && item.hasStatus).length;
            const totalCount = fixedDataResult.sampleData.length;
            
            console.log(`\n📊 修复效果统计: ${fixedCount}/${totalCount} 条数据修复成功`);
            
        } else {
            console.log(`查询失败: ${fixedDataResult.error}`);
        }
        
        console.log('\n📍 第二步：测试表情包管理页面显示');
        
        // 进入表情包管理页面
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(5000);
        
        // 检查表格显示
        const tableDisplayResult = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-content tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const statusCell = cells[7];
                const imageCell = cells[1];
                const img = imageCell ? imageCell.querySelector('img') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent.trim() : 'N/A',
                    hasImage: !!img,
                    imageSrc: img ? img.src.substring(0, 50) + '...' : 'N/A',
                    hasValidName: nameCell && nameCell.textContent.trim() !== 'undefined' && nameCell.textContent.trim() !== '',
                    hasValidStatus: statusCell && statusCell.textContent.trim() !== 'undefined' && statusCell.textContent.trim() !== '草稿'
                };
            });
        });
        
        console.log('📊 表格显示结果:');
        console.log(`表格行数: ${tableDisplayResult.length}`);
        
        if (tableDisplayResult.length > 0) {
            console.log('\n📋 表格显示详情:');
            tableDisplayResult.forEach(row => {
                console.log(`\n行 ${row.index}:`);
                console.log(`  名称: ${row.name} (有效: ${row.hasValidName})`);
                console.log(`  状态: ${row.status} (有效: ${row.hasValidStatus})`);
                console.log(`  有图片: ${row.hasImage}`);
                console.log(`  图片源: ${row.imageSrc}`);
                
                if (row.hasValidName && row.hasValidStatus) {
                    console.log(`  ✅ 显示正常`);
                } else {
                    console.log(`  🔴 显示异常`);
                }
            });
            
            // 统计显示效果
            const validRows = tableDisplayResult.filter(row => row.hasValidName && row.hasValidStatus).length;
            const totalRows = tableDisplayResult.length;
            
            console.log(`\n📊 显示效果统计: ${validRows}/${totalRows} 行显示正常`);
            
        } else {
            console.log('❌ 表格没有数据行');
        }
        
        console.log('\n📍 第三步：创建新表情包测试');
        
        // 创建新表情包测试数据结构是否正确
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        if (await addEmojiBtn.isVisible()) {
            await addEmojiBtn.click();
            await page.waitForTimeout(3000);
            
            // 填写表单
            await page.fill('#emoji-title', '数据结构测试表情包');
            
            // 选择分类
            const categoryOptions = await page.evaluate(() => {
                const select = document.querySelector('#emoji-category');
                if (!select || select.options.length <= 1) return [];
                return Array.from(select.options).slice(1).map(option => ({
                    value: option.value,
                    text: option.textContent
                }));
            });
            
            if (categoryOptions.length > 0) {
                await page.selectOption('#emoji-category', categoryOptions[0].value);
                console.log(`✅ 已选择分类: ${categoryOptions[0].text}`);
            }
            
            await page.selectOption('#emoji-status', 'published');
            console.log('✅ 已设置状态为已发布');
            
            // 创建简单的测试图片
            const testImagePath = require('path').join(__dirname, 'structure-test.png');
            const imageBuffer = await page.evaluate(() => {
                return new Promise((resolve) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 50;
                    canvas.height = 50;
                    const ctx = canvas.getContext('2d');
                    
                    ctx.fillStyle = '#00BCD4';
                    ctx.fillRect(0, 0, 50, 50);
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('FIX', 25, 30);
                    
                    canvas.toBlob((blob) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const arrayBuffer = reader.result;
                            const uint8Array = new Uint8Array(arrayBuffer);
                            resolve(Array.from(uint8Array));
                        };
                        reader.readAsArrayBuffer(blob);
                    }, 'image/png');
                });
            });
            
            require('fs').writeFileSync(testImagePath, Buffer.from(imageBuffer));
            
            const fileInput = await page.locator('#emoji-image-file');
            await fileInput.setInputFiles(testImagePath);
            console.log('✅ 已上传测试图片');
            
            // 保存表情包
            const saveResult = await page.evaluate(() => {
                const modal = document.querySelector('[style*="position: fixed"]');
                const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
                
                if (submitBtn) {
                    submitBtn.click();
                    return { success: true };
                } else {
                    return { success: false, error: '未找到提交按钮' };
                }
            });
            
            if (saveResult.success) {
                console.log('✅ 已点击保存按钮');
                await page.waitForTimeout(8000);
                
                // 验证新数据的结构
                const newDataResult = await page.evaluate(async () => {
                    try {
                        const result = await CloudAPI.database.get('emojis');
                        if (result.success && result.data) {
                            const latest = result.data[result.data.length - 1];
                            return {
                                success: true,
                                latestData: {
                                    _id: latest._id,
                                    title: latest.title,
                                    status: latest.status,
                                    category: latest.category,
                                    hasTitle: !!latest.title,
                                    hasStatus: !!latest.status,
                                    titleType: typeof latest.title,
                                    statusType: typeof latest.status,
                                    isNewData: latest.title && latest.title.includes('数据结构测试')
                                }
                            };
                        }
                        return { success: false, error: '查询失败' };
                    } catch (error) {
                        return { success: false, error: error.message };
                    }
                });
                
                console.log('\n📊 新数据结构验证:');
                if (newDataResult.success) {
                    const data = newDataResult.latestData;
                    console.log(`  ID: ${data._id}`);
                    console.log(`  标题: ${data.title} (类型: ${data.titleType}, 存在: ${data.hasTitle})`);
                    console.log(`  状态: ${data.status} (类型: ${data.statusType}, 存在: ${data.hasStatus})`);
                    console.log(`  分类: ${data.category}`);
                    console.log(`  是新数据: ${data.isNewData}`);
                    
                    if (data.hasTitle && data.hasStatus && data.isNewData) {
                        console.log('  🎉 新数据结构完全正确！');
                    } else {
                        console.log('  🔴 新数据结构仍有问题');
                    }
                } else {
                    console.log(`  验证失败: ${newDataResult.error}`);
                }
            }
            
            // 清理测试文件
            if (require('fs').existsSync(testImagePath)) {
                require('fs').unlinkSync(testImagePath);
            }
        }
        
        console.log('\n🎯 数据结构修复测试总结:');
        
        const oldDataFixed = fixedDataResult.success && fixedDataResult.sampleData.some(item => item.hasTitle && item.hasStatus);
        const tableDisplayFixed = tableDisplayResult.length > 0 && tableDisplayResult.some(row => row.hasValidName && row.hasValidStatus);
        
        console.log(`旧数据修复: ${oldDataFixed ? '✅ 成功' : '🔴 失败'}`);
        console.log(`表格显示修复: ${tableDisplayFixed ? '✅ 成功' : '🔴 失败'}`);
        console.log(`新数据结构: ${saveResult && saveResult.success ? '✅ 正确' : '🔴 待验证'}`);
        
        // 截图
        await page.screenshot({ path: 'test-data-structure-fix.png', fullPage: true });
        console.log('\n📸 测试截图已保存: test-data-structure-fix.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开20秒供查看...');
        await page.waitForTimeout(20000);
        
        return {
            success: true,
            oldDataFixed: oldDataFixed,
            tableDisplayFixed: tableDisplayFixed
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'structure-fix-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
testDataStructureFix().then(result => {
    console.log('\n🎯 数据结构修复测试最终结果:', result);
}).catch(console.error);
