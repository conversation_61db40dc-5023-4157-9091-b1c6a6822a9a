// 真实测试小程序从管理后台数据加载
const { chromium } = require('playwright');

async function testMiniprogramReal() {
    console.log('🧪 开始真实测试小程序数据加载...\n');
    
    let browser;
    
    try {
        console.log('📍 第一步：启动浏览器测试环境');
        
        browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        
        const page = await browser.newPage();
        
        // 监听控制台消息
        const consoleMessages = [];
        page.on('console', msg => {
            const text = msg.text();
            consoleMessages.push(text);
            console.log(`[CONSOLE] ${text}`);
        });
        
        // 监听错误
        page.on('pageerror', error => {
            console.log(`[ERROR] ${error.message}`);
        });
        
        console.log('📍 第二步：创建小程序测试环境');
        
        // 创建一个模拟小程序环境的页面
        const testPageContent = `
<!DOCTYPE html>
<html>
<head>
    <title>小程序测试环境</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 小程序真实数据加载测试</h1>
    
    <div class="test-section">
        <h2>📋 测试控制面板</h2>
        <button onclick="testCloudFunction()">测试云函数调用</button>
        <button onclick="testDataLoading()">测试数据加载</button>
        <button onclick="testRenderConditions()">测试渲染条件</button>
        <button onclick="runAllTests()">运行所有测试</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        // 模拟小程序环境
        const wx = {
            cloud: {
                callFunction: async (options) => {
                    console.log('🔄 模拟云函数调用:', options);
                    
                    // 模拟真实的云函数响应
                    if (options.name === 'dataAPI' && options.data.action === 'getEmojis') {
                        return {
                            result: {
                                success: true,
                                data: [
                                    {
                                        _id: 'emoji_1',
                                        title: '搞笑表情包1',
                                        imageUrl: 'https://example.com/emoji1.png',
                                        status: 'published',
                                        categoryId: 'funny',
                                        likes: 128,
                                        collections: 45
                                    },
                                    {
                                        _id: 'emoji_2', 
                                        title: '可爱萌宠2',
                                        imageUrl: 'https://example.com/emoji2.png',
                                        status: 'published',
                                        categoryId: 'cute',
                                        likes: 89,
                                        collections: 67
                                    }
                                ]
                            }
                        };
                    }
                    
                    if (options.name === 'dataAPI' && options.data.action === 'getCategories') {
                        return {
                            result: {
                                success: true,
                                data: [
                                    {
                                        _id: 'funny',
                                        name: '搞笑幽默',
                                        icon: '😂',
                                        emojiCount: 10
                                    },
                                    {
                                        _id: 'cute',
                                        name: '可爱萌宠', 
                                        icon: '🐱',
                                        emojiCount: 8
                                    }
                                ]
                            }
                        };
                    }
                    
                    return { result: { success: false, message: '未知的云函数调用' } };
                }
            }
        };
        
        // 模拟小程序页面数据
        let pageData = {
            emojiList: [],
            hotCategories: [],
            bannerList: [],
            searchResults: []
        };
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = \`[\${new Date().toLocaleTimeString()}] \${message}\`;
            results.appendChild(div);
            console.log(message);
        }
        
        async function testCloudFunction() {
            log('🧪 开始测试云函数调用...', 'info');
            
            try {
                // 测试表情包数据获取
                const emojiResult = await wx.cloud.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 8 }
                    }
                });
                
                if (emojiResult.result && emojiResult.result.success) {
                    log(\`✅ 表情包云函数调用成功，获取到 \${emojiResult.result.data.length} 个表情包\`, 'success');
                } else {
                    log('🔴 表情包云函数调用失败', 'error');
                }
                
                // 测试分类数据获取
                const categoryResult = await wx.cloud.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getCategories',
                        data: {}
                    }
                });
                
                if (categoryResult.result && categoryResult.result.success) {
                    log(\`✅ 分类云函数调用成功，获取到 \${categoryResult.result.data.length} 个分类\`, 'success');
                } else {
                    log('🔴 分类云函数调用失败', 'error');
                }
                
            } catch (error) {
                log(\`❌ 云函数调用出错: \${error.message}\`, 'error');
            }
        }
        
        async function testDataLoading() {
            log('🧪 开始测试数据加载逻辑...', 'info');
            
            try {
                // 模拟 loadEmojiData 方法
                const result = await wx.cloud.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 8 }
                    }
                });
                
                if (result.result && result.result.success && result.result.data.length > 0) {
                    const emojis = result.result.data;
                    
                    // 处理表情包数据（模拟首页JS逻辑）
                    const emojiList = emojis.map((emoji, index) => ({
                        ...emoji,
                        id: emoji._id || emoji.id || \`emoji_\${index}\`,
                        title: emoji.title || emoji.name || '未知表情包',
                        imageUrl: emoji.imageUrl || emoji.url || '',
                        likes: emoji.likes || 0,
                        collections: emoji.collections || 0,
                        isLiked: false,
                        isCollected: false
                    }));
                    
                    pageData.emojiList = emojiList;
                    log(\`✅ 表情包数据处理完成，共 \${emojiList.length} 个\`, 'success');
                    
                    // 显示第一个表情包的详细信息
                    if (emojiList.length > 0) {
                        log(\`📝 第一个表情包: \${JSON.stringify(emojiList[0], null, 2)}\`, 'info');
                    }
                } else {
                    log('🔴 表情包数据为空或获取失败', 'error');
                }
                
                // 测试分类数据加载
                const categoryResult = await wx.cloud.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getCategories',
                        data: {}
                    }
                });
                
                if (categoryResult.result && categoryResult.result.success && categoryResult.result.data.length > 0) {
                    const categories = categoryResult.result.data;
                    
                    // 处理分类数据（模拟首页JS逻辑）
                    const hotCategories = categories.slice(0, 4).map((category, index) => ({
                        ...category,
                        id: category._id || category.id || \`category_\${index}\`,
                        name: category.name || '未知分类',
                        icon: category.icon || '📁',
                        color: getCategoryColor(category.name || category._id || category.id),
                        count: category.emojiCount || 0
                    }));
                    
                    pageData.hotCategories = hotCategories;
                    log(\`✅ 分类数据处理完成，共 \${hotCategories.length} 个\`, 'success');
                    
                    // 显示第一个分类的详细信息
                    if (hotCategories.length > 0) {
                        log(\`📝 第一个分类: \${JSON.stringify(hotCategories[0], null, 2)}\`, 'info');
                    }
                } else {
                    log('🔴 分类数据为空或获取失败', 'error');
                }
                
            } catch (error) {
                log(\`❌ 数据加载出错: \${error.message}\`, 'error');
            }
        }
        
        function getCategoryColor(categoryName) {
            const gradientMap = {
                '搞笑幽默': 'linear-gradient(135deg, #FF6B6B, #FF8E8E)',
                '可爱萌宠': 'linear-gradient(135deg, #FF69B4, #FF8EC8)',
                '情感表达': 'linear-gradient(135deg, #FF4757, #FF6B7A)',
                '节日庆典': 'linear-gradient(135deg, #5F27CD, #7B4AE8)',
                '网络热梗': 'linear-gradient(135deg, #FFA726, #FFB84D)',
                '动漫二次元': 'linear-gradient(135deg, #667EEA, #8A9BFF)',
                'funny': 'linear-gradient(135deg, #FF6B6B, #FF8E8E)',
                'cute': 'linear-gradient(135deg, #FF69B4, #FF8EC8)'
            };
            return gradientMap[categoryName] || 'linear-gradient(135deg, #999999, #BBBBBB)';
        }
        
        function testRenderConditions() {
            log('🧪 开始测试渲染条件...', 'info');
            
            // 检查渲染条件
            const shouldShowEmojis = pageData.searchResults.length === 0 && pageData.emojiList.length > 0;
            const shouldShowCategories = pageData.searchResults.length === 0 && pageData.hotCategories.length > 0;
            const shouldShowBanners = pageData.searchResults.length === 0 && pageData.bannerList.length > 0;
            
            log(\`📊 渲染条件检查结果:\`, 'info');
            log(\`  - 搜索结果数量: \${pageData.searchResults.length}\`, 'info');
            log(\`  - 表情包数量: \${pageData.emojiList.length}\`, 'info');
            log(\`  - 分类数量: \${pageData.hotCategories.length}\`, 'info');
            log(\`  - 横幅数量: \${pageData.bannerList.length}\`, 'info');
            log(\`  - 应该显示表情包: \${shouldShowEmojis ? '✅ 是' : '🔴 否'}\`, shouldShowEmojis ? 'success' : 'error');
            log(\`  - 应该显示分类: \${shouldShowCategories ? '✅ 是' : '🔴 否'}\`, shouldShowCategories ? 'success' : 'error');
            log(\`  - 应该显示横幅: \${shouldShowBanners ? '✅ 是' : '⚠️ 否（无横幅数据）'}\`, shouldShowBanners ? 'success' : 'warning');
            
            if (shouldShowEmojis && shouldShowCategories) {
                log('🎉 主要内容渲染条件满足！', 'success');
            } else {
                log('🔴 主要内容渲染条件不满足，页面可能显示空白', 'error');
            }
        }
        
        async function runAllTests() {
            log('🚀 开始运行所有测试...', 'info');
            document.getElementById('results').innerHTML = '';
            
            await testCloudFunction();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDataLoading();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testRenderConditions();
            
            log('✅ 所有测试完成！', 'success');
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            log('📱 小程序测试环境已准备就绪', 'success');
            log('💡 点击"运行所有测试"按钮开始测试', 'info');
        };
    </script>
</body>
</html>`;
        
        await page.setContent(testPageContent);
        
        console.log('📍 第三步：运行小程序测试');
        
        // 等待页面加载
        await page.waitForTimeout(2000);
        
        // 点击运行所有测试
        await page.click('button:has-text("运行所有测试")');
        
        // 等待测试完成
        await page.waitForTimeout(5000);
        
        console.log('📍 第四步：分析测试结果');
        
        // 获取测试结果
        const testResults = await page.evaluate(() => {
            const results = document.getElementById('results');
            return results.innerHTML;
        });
        
        console.log('📊 测试结果详情:');
        console.log(testResults);
        
        // 检查是否有成功的测试
        const hasSuccessfulTests = testResults.includes('✅');
        const hasErrors = testResults.includes('🔴') || testResults.includes('❌');
        
        console.log('\n🎯 测试总结:');
        console.log(`成功的测试: ${hasSuccessfulTests ? '✅ 有' : '🔴 无'}`);
        console.log(`错误的测试: ${hasErrors ? '🔴 有' : '✅ 无'}`);
        
        if (hasSuccessfulTests && !hasErrors) {
            console.log('🎉 所有测试通过！小程序应该能正常显示数据。');
        } else if (hasSuccessfulTests) {
            console.log('⚠️ 部分测试通过，可能存在一些问题。');
        } else {
            console.log('🔴 测试失败，需要检查问题。');
        }
        
        // 等待用户查看结果
        console.log('\n⏳ 等待15秒供您查看测试结果...');
        await page.waitForTimeout(15000);
        
        return {
            success: hasSuccessfulTests,
            hasErrors: hasErrors,
            canEnd: hasSuccessfulTests && !hasErrors
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        return {
            success: false,
            error: error.message,
            canEnd: false
        };
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 运行测试
testMiniprogramReal().then(result => {
    console.log('\n🎯 小程序真实测试最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.canEnd) {
        console.log('🎉 小程序测试通过！所有修复已完成并验证。');
    } else if (result.success) {
        console.log('⚠️ 小程序测试部分通过，建议进一步检查。');
    } else {
        console.log('🔴 小程序测试失败，需要继续修复问题。');
    }
}).catch(console.error);
