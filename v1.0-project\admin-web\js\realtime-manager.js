// Web管理后台 - 实时监听管理器
class RealTimeManager {
  constructor() {
    this.watchers = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.cloudApp = null;
    this.lastNotificationTime = localStorage.getItem('lastNotificationTime') || null;
    
    console.log('📡 RealTimeManager初始化');
  }

  // 初始化CloudBase
  async initCloudBase() {
    if (this.cloudApp) return this.cloudApp;

    try {
      if (typeof cloudbase === 'undefined') {
        throw new Error('CloudBase SDK未加载');
      }

      this.cloudApp = cloudbase.init({
        env: 'cloud1-5g6pvnpl88dc0142',
        clientId: 'cloud1-5g6pvnpl88dc0142'  // SDK 2.0必需参数
      });

      console.log('✅ RealTimeManager CloudBase初始化成功');
      return this.cloudApp;
    } catch (error) {
      console.error('❌ RealTimeManager CloudBase初始化失败:', error);
      throw error;
    }
  }

  // 初始化实时监听
  async initWatchers() {
    try {
      console.log('🔄 启动实时监听...');

      // 确保CloudBase已初始化
      if (!this.cloudApp) {
        await this.initCloudBase();
      }

      const db = this.cloudApp.database();
      
      // 监听同步通知
      const notificationWatcher = db.collection('sync_notifications')
        .orderBy('timestamp', 'desc')
        .limit(50)
        .watch({
          onChange: (snapshot) => {
            this.handleSyncNotification(snapshot);
          },
          onError: (error) => {
            console.error('❌ 监听同步通知失败:', error);
            this.handleWatchError('sync_notifications', error);
          }
        });

      this.watchers.set('sync_notifications', notificationWatcher);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      console.log('✅ 实时监听已启动');
      this.showConnectionStatus('已连接', 'success');
      
    } catch (error) {
      console.error('❌ 初始化监听失败:', error);
      this.isConnected = false;
      this.showConnectionStatus('连接失败', 'error');
      this.scheduleReconnect();
    }
  }

  // 处理同步通知
  handleSyncNotification(snapshot) {
    const { docs, type, docChanges } = snapshot;
    
    if (type === 'init') {
      console.log('📡 监听器初始化完成，当前通知数量:', docs.length);
      return;
    }

    // 处理文档变更
    if (docChanges && docChanges.length > 0) {
      docChanges.forEach(change => {
        const { queueType, doc } = change;
        
        if (queueType === 'enqueue') {
          // 新增的通知
          this.processNotification(doc);
        }
      });
    } else {
      // 兼容处理：检查新通知
      docs.forEach(notification => {
        if (this.isNewNotification(notification)) {
          this.processNotification(notification);
        }
      });
    }
  }

  // 处理具体通知
  processNotification(notification) {
    const { dataType, operation, dataId, timestamp, adminId, data } = notification;
    
    console.log(`🔔 收到实时通知:`, {
      dataType,
      operation,
      dataId,
      adminId,
      timestamp
    });
    
    // 更新最后通知时间
    this.updateLastNotificationTime(timestamp);
    
    // 根据数据类型刷新对应数据
    switch (dataType) {
      case 'categories':
        this.refreshCategoriesData(operation, data);
        break;
      case 'emojis':
        this.refreshEmojisData(operation, data);
        break;
      case 'banners':
        this.refreshBannersData(operation, data);
        break;
      default:
        console.warn('未知的数据类型:', dataType);
    }

    // 显示同步通知
    this.showSyncNotification(dataType, operation, adminId);
    
    // 触发全局事件
    this.triggerGlobalEvent('dataSync', {
      dataType,
      operation,
      dataId,
      data,
      timestamp
    });
  }

  // 刷新分类数据
  async refreshCategoriesData(operation, notificationData) {
    try {
      console.log('🔄 刷新分类数据...');
      
      const result = await window.apiManager.getCategories();
      if (result.success) {
        // 更新本地缓存
        localStorage.setItem('admin_categories', JSON.stringify(result.data));
        
        // 触发UI更新
        this.triggerUIUpdate('categories', result.data, operation);
        
        console.log('✅ 分类数据刷新完成');
      }
    } catch (error) {
      console.error('❌ 刷新分类数据失败:', error);
    }
  }

  // 刷新表情包数据
  async refreshEmojisData(operation, notificationData) {
    try {
      console.log('🔄 刷新表情包数据...');
      
      const result = await window.apiManager.getEmojis();
      if (result.success) {
        localStorage.setItem('admin_emojis', JSON.stringify(result.data));
        this.triggerUIUpdate('emojis', result.data, operation);
        console.log('✅ 表情包数据刷新完成');
      }
    } catch (error) {
      console.error('❌ 刷新表情包数据失败:', error);
    }
  }

  // 刷新横幅数据
  async refreshBannersData(operation, notificationData) {
    try {
      console.log('🔄 刷新横幅数据...');
      
      const result = await window.apiManager.getBanners();
      if (result.success) {
        localStorage.setItem('admin_banners', JSON.stringify(result.data));
        this.triggerUIUpdate('banners', result.data, operation);
        console.log('✅ 横幅数据刷新完成');
      }
    } catch (error) {
      console.error('❌ 刷新横幅数据失败:', error);
    }
  }

  // 触发UI更新
  triggerUIUpdate(dataType, data, operation) {
    // 发送自定义事件
    const event = new CustomEvent('dataUpdate', {
      detail: { 
        dataType, 
        data, 
        operation,
        timestamp: new Date().toISOString()
      }
    });
    window.dispatchEvent(event);
    
    console.log(`📢 触发UI更新事件: ${dataType} - ${operation}`);
  }

  // 触发全局事件
  triggerGlobalEvent(eventType, data) {
    const event = new CustomEvent(eventType, {
      detail: data
    });
    window.dispatchEvent(event);
  }

  // 显示同步通知
  showSyncNotification(dataType, operation, adminId) {
    const messages = {
      'create': '新增',
      'update': '更新', 
      'delete': '删除'
    };
    
    const typeNames = {
      'categories': '分类',
      'emojis': '表情包',
      'banners': '横幅'
    };
    
    const message = `${typeNames[dataType] || dataType}已${messages[operation] || operation}`;
    const adminInfo = adminId && adminId !== window.authManager.getCurrentAdmin().adminId 
      ? ` (by ${adminId})` 
      : '';
    
    // 显示Toast提示
    this.showToast(`🔄 ${message}${adminInfo}`, 'info', 3000);
    
    // 更新连接状态指示器
    this.updateConnectionIndicator();
  }

  // 显示连接状态
  showConnectionStatus(status, type) {
    console.log(`📡 连接状态: ${status}`);
    
    // 更新状态指示器
    const indicator = document.getElementById('connectionStatus');
    if (indicator) {
      indicator.textContent = status;
      indicator.className = `connection-status ${type}`;
    }
  }

  // 更新连接指示器
  updateConnectionIndicator() {
    const indicator = document.getElementById('connectionIndicator');
    if (indicator) {
      indicator.classList.add('pulse');
      setTimeout(() => {
        indicator.classList.remove('pulse');
      }, 1000);
    }
  }

  // 错误处理和重连
  handleWatchError(watcherName, error) {
    console.error(`❌ 监听器 ${watcherName} 出错:`, error);
    
    this.isConnected = false;
    this.showConnectionStatus('连接断开', 'error');
    
    // 清理当前监听器
    const watcher = this.watchers.get(watcherName);
    if (watcher) {
      try {
        watcher.close();
      } catch (closeError) {
        console.warn('关闭监听器失败:', closeError);
      }
      this.watchers.delete(watcherName);
    }
    
    // 安排重连
    this.scheduleReconnect();
  }

  // 安排重连
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ 达到最大重连次数，停止重连');
      this.showConnectionStatus('连接失败', 'error');
      this.showToast('实时同步连接失败，请刷新页面', 'error', 5000);
      return;
    }
    
    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    
    console.log(`🔄 ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    this.showConnectionStatus(`重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`, 'warning');
    
    setTimeout(() => {
      this.initWatchers();
    }, delay);
  }

  // 检查是否为新通知
  isNewNotification(notification) {
    if (!this.lastNotificationTime) return true;
    
    return new Date(notification.timestamp) > new Date(this.lastNotificationTime);
  }

  // 更新最后通知时间
  updateLastNotificationTime(timestamp) {
    this.lastNotificationTime = timestamp;
    localStorage.setItem('lastNotificationTime', timestamp);
  }

  // 手动刷新所有数据
  async refreshAllData() {
    try {
      console.log('🔄 手动刷新所有数据...');
      
      const [categories, emojis, banners] = await Promise.all([
        window.apiManager.getCategories(),
        window.apiManager.getEmojis(),
        window.apiManager.getBanners()
      ]);

      if (categories.success) {
        localStorage.setItem('admin_categories', JSON.stringify(categories.data));
        this.triggerUIUpdate('categories', categories.data, 'refresh');
      }

      if (emojis.success) {
        localStorage.setItem('admin_emojis', JSON.stringify(emojis.data));
        this.triggerUIUpdate('emojis', emojis.data, 'refresh');
      }

      if (banners.success) {
        localStorage.setItem('admin_banners', JSON.stringify(banners.data));
        this.triggerUIUpdate('banners', banners.data, 'refresh');
      }

      this.showToast('数据刷新完成', 'success');
      console.log('✅ 手动刷新完成');
      
    } catch (error) {
      console.error('❌ 手动刷新失败:', error);
      this.showToast('数据刷新失败', 'error');
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      watchersCount: this.watchers.size,
      lastNotificationTime: this.lastNotificationTime
    };
  }

  // 显示Toast提示
  showToast(message, type = 'info', duration = 3000) {
    console.log(`📢 Toast [${type}]: ${message}`);
    
    // 如果有全局Toast组件，使用它
    if (typeof window !== 'undefined' && window.showToast) {
      window.showToast(message, type, duration);
    } else {
      // 简单的控制台输出
      const emoji = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
      };
      console.log(`${emoji[type] || 'ℹ️'} ${message}`);
    }
  }

  // 清理资源
  destroy() {
    console.log('🧹 清理RealTimeManager资源...');
    
    this.watchers.forEach((watcher, name) => {
      try {
        watcher.close();
        console.log(`✅ 关闭监听器: ${name}`);
      } catch (error) {
        console.warn(`⚠️ 关闭监听器失败: ${name}`, error);
      }
    });
    
    this.watchers.clear();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    
    console.log('✅ RealTimeManager资源清理完成');
  }
}

// 全局实时管理器实例
window.realTimeManager = new RealTimeManager();

// 页面加载时启动监听
document.addEventListener('DOMContentLoaded', () => {
  // 延迟启动，确保其他组件已初始化
  setTimeout(() => {
    if (window.authManager.isLoggedIn()) {
      window.realTimeManager.initWatchers();
    }
  }, 2000);
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  window.realTimeManager.destroy();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RealTimeManager;
}
