// utils/configSync.js - 系统配置同步管理器
/**
 * 系统配置同步管理器
 * 负责从云端同步管理后台的配置到小程序端
 */

const ConfigSync = {
  // 缓存键
  CACHE_KEYS: {
    SYSTEM_CONFIG: 'system_config',
    CONFIG_VERSION: 'config_version',
    LAST_SYNC_TIME: 'last_config_sync'
  },

  // 配置缓存
  _configCache: null,
  _isSyncing: false,
  _syncInterval: null,

  /**
   * 初始化配置同步
   */
  init() {
    console.log('🔄 ConfigSync 初始化')
    
    // 加载本地缓存的配置
    this.loadCachedConfig()
    
    // 启动定时同步
    this.startAutoSync()
    
    // 监听登录状态变化
    const app = getApp()
    if (app && app.getAuthManager) {
      const AuthManager = app.getAuthManager()
      AuthManager.addLoginListener((isLoggedIn) => {
        if (isLoggedIn) {
          this.syncConfig()
        }
      })
    }
  },

  /**
   * 加载缓存的配置
   */
  loadCachedConfig() {
    try {
      const cachedConfig = wx.getStorageSync(this.CACHE_KEYS.SYSTEM_CONFIG)
      const cachedVersion = wx.getStorageSync(this.CACHE_KEYS.CONFIG_VERSION)
      
      if (cachedConfig) {
        this._configCache = {
          ...cachedConfig,
          version: cachedVersion || '1.0.0'
        }
        console.log('📱 已加载缓存的系统配置:', this._configCache.version)
      } else {
        this._configCache = this.getDefaultConfig()
        console.log('🆕 使用默认系统配置')
      }
    } catch (error) {
      console.error('❌ 加载缓存配置失败:', error)
      this._configCache = this.getDefaultConfig()
    }
  },

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      version: '1.0.0',
      lastUpdateTime: new Date().toISOString(),
      // 首页配置
      home: {
        enableBanner: true,
        bannerCount: 3,
        enableHotCategories: true,
        emojiPerPage: 20,
        enableSearch: true,
        enablePullRefresh: true
      },
      // 分类配置
      categories: {
        enableCustomIcons: true,
        defaultSort: 'name',
        showEmojiCount: true
      },
      // 用户功能配置
      features: {
        enableLike: true,
        enableCollect: true,
        enableDownload: true,
        enableShare: true,
        enableReport: true
      },
      // UI配置
      ui: {
        theme: 'light',
        enableAnimations: true,
        imageQuality: 'medium',
        lazyLoading: true
      },
      // 性能配置
      performance: {
        cacheTimeout: 300000, // 5分钟
        maxRetries: 3,
        requestTimeout: 10000,
        enablePrefetch: true
      }
    }
  },

  /**
   * 同步配置到云端
   */
  async syncConfig(force = false) {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化，跳过配置同步')
      return false
    }

    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化，跳过配置同步')
      return false
    }

    if (this._isSyncing) {
      console.log('⏳ 配置同步进行中，跳过重复请求')
      return false
    }

    this._isSyncing = true

    try {
      console.log('🔄 开始同步系统配置...')

      // 获取云端配置版本
      const versionResult = await this.getConfigVersion()
      if (!versionResult.success) {
        throw new Error('获取配置版本失败: ' + versionResult.error)
      }

      const cloudVersion = versionResult.data.version
      const cachedVersion = this._configCache?.version || '1.0.0'

      // 检查是否需要更新
      if (!force && cloudVersion === cachedVersion) {
        console.log('✅ 配置已是最新版本，无需同步')
        return true
      }

      // 获取最新配置
      const configResult = await this.fetchSystemConfig()
      if (!configResult.success) {
        throw new Error('获取配置失败: ' + configResult.error)
      }

      // 更新本地缓存
      const newConfig = {
        ...configResult.data.configs,
        version: cloudVersion,
        lastUpdateTime: configResult.data.lastUpdateTime
      }

      this._configCache = newConfig
      
      // 保存到本地存储
      wx.setStorageSync(this.CACHE_KEYS.SYSTEM_CONFIG, newConfig)
      wx.setStorageSync(this.CACHE_KEYS.CONFIG_VERSION, cloudVersion)
      wx.setStorageSync(this.CACHE_KEYS.LAST_SYNC_TIME, new Date().toISOString())

      console.log('✅ 配置同步成功:', {
        oldVersion: cachedVersion,
        newVersion: cloudVersion,
        configCount: Object.keys(newConfig).length - 2 // 减去version和lastUpdateTime
      })

      // 触发配置更新事件
      this.notifyConfigUpdate(newConfig)

      return true
    } catch (error) {
      console.error('❌ 配置同步失败:', error)
      return false
    } finally {
      this._isSyncing = false
    }
  },

  /**
   * 获取配置版本
   */
  async getConfigVersion() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'systemConfig',
        data: {
          action: 'getConfigVersion'
        }
      })

      return result.result || {
        success: false,
        error: '获取版本失败'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  },

  /**
   * 获取系统配置
   */
  async fetchSystemConfig() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'systemConfig',
        data: {
          action: 'getAllConfigs'
        }
      })

      return result.result || {
        success: false,
        error: '获取配置失败'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  },

  /**
   * 获取特定配置
   */
  getConfig(key, defaultValue = null) {
    if (!this._configCache) {
      this.loadCachedConfig()
    }

    const keys = key.split('.')
    let value = this._configCache

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return defaultValue
      }
    }

    return value
  },

  /**
   * 检查功能是否启用
   */
  isFeatureEnabled(feature) {
    return this.getConfig(`features.${feature}`, false)
  },

  /**
   * 启动自动同步
   */
  startAutoSync() {
    if (this._syncInterval) {
      clearInterval(this._syncInterval)
    }

    // 每30分钟检查一次配置更新
    this._syncInterval = setInterval(() => {
      this.syncConfig()
    }, 30 * 60 * 1000)

    console.log('🔄 已启动配置自动同步，间隔30分钟')
  },

  /**
   * 停止自动同步
   */
  stopAutoSync() {
    if (this._syncInterval) {
      clearInterval(this._syncInterval)
      this._syncInterval = null
      console.log('⏹️ 已停止配置自动同步')
    }
  },

  /**
   * 通知配置更新
   */
  notifyConfigUpdate(newConfig) {
    // 触发全局事件
    const app = getApp()
    if (app) {
      app.globalData.systemConfig = newConfig
    }

    // 通知所有页面
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.onConfigUpdate) {
        page.onConfigUpdate(newConfig)
      }
    })

    // 触发全局事件
    wx.$emit && wx.$emit('systemConfigUpdate', newConfig)
  },

  /**
   * 清除本地配置缓存
   */
  clearCache() {
    try {
      wx.removeStorageSync(this.CACHE_KEYS.SYSTEM_CONFIG)
      wx.removeStorageSync(this.CACHE_KEYS.CONFIG_VERSION)
      wx.removeStorageSync(this.CACHE_KEYS.LAST_SYNC_TIME)
      this._configCache = null
      console.log('🗑️ 配置缓存已清除')
    } catch (error) {
      console.error('❌ 清除缓存失败:', error)
    }
  },

  /**
   * 强制重新同步配置
   */
  async forceSync() {
    this.clearCache()
    return await this.syncConfig(true)
  },

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isSyncing: this._isSyncing,
      cachedVersion: this._configCache?.version,
      lastSyncTime: wx.getStorageSync(this.CACHE_KEYS.LAST_SYNC_TIME)
    }
  }
}

module.exports = {
  ConfigSync
}