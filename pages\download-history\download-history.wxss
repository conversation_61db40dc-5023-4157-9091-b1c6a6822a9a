/* pages/download-history/download-history.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 复用主页面的表情包列表样式 */
.emoji-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 10rpx;
}

.emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: scale(0.98);
}

.emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
}

.emoji-image {
  width: 100%;
  height: 100%;
}

.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 数据统计区域 */
.emoji-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.stat-number {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .container {
    padding: 15rpx;
  }

  .emoji-list {
    grid-template-columns: 1fr;
    gap: 15rpx;
    margin-top: 5rpx;
  }

  .emoji-image-container {
    height: 250rpx;
  }

  .emoji-info {
    padding: 20rpx;
  }

  .emoji-title {
    font-size: 26rpx;
  }

  .emoji-category {
    font-size: 20rpx;
  }

  .empty-state {
    padding: 80rpx 30rpx;
    min-height: 50vh;
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 414px) {
  .container {
    padding: 25rpx;
  }

  .emoji-list {
    gap: 25rpx;
    margin-top: 15rpx;
  }

  .emoji-image-container {
    height: 320rpx;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  min-height: 60vh;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.explore-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.explore-btn::after {
  border: none;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx;
  color: #999;
  min-height: 60vh;
}
