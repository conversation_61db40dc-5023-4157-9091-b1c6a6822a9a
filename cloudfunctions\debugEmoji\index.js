// 临时调试云函数 - 检查数据库中的图片URL格式
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 获取前10个表情包的数据
    const result = await db.collection('emojis').limit(10).get()
    
    console.log('📊 数据库中的表情包数据:')
    result.data.forEach((emoji, index) => {
      console.log(`${index + 1}. ID: ${emoji._id}`)
      console.log(`   Title: ${emoji.title}`)
      console.log(`   ImageURL: ${emoji.imageUrl}`)
      console.log(`   URL Type: ${emoji.imageUrl?.startsWith('cloud://') ? 'CLOUD' : 
                                  emoji.imageUrl?.startsWith('https://') ? 'HTTPS' :
                                  emoji.imageUrl?.startsWith('http://') ? 'HTTP' :
                                  emoji.imageUrl?.startsWith('data:') ? 'BASE64' : 'UNKNOWN'}`)
      console.log('---')
    })
    
    return {
      success: true,
      data: result.data.map(emoji => ({
        id: emoji._id,
        title: emoji.title,
        imageUrl: emoji.imageUrl,
        urlType: emoji.imageUrl?.startsWith('cloud://') ? 'CLOUD' : 
                 emoji.imageUrl?.startsWith('https://') ? 'HTTPS' :
                 emoji.imageUrl?.startsWith('http://') ? 'HTTP' :
                 emoji.imageUrl?.startsWith('data:') ? 'BASE64' : 'UNKNOWN'
      }))
    }
  } catch (error) {
    console.error('❌ 调试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
