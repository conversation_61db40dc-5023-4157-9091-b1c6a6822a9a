// 云函数入口文件 - 系统配置管理
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext()
  const { action, configKey, configValue } = event

  try {
    switch (action) {
      case 'getConfig':
        return await getSystemConfig(configKey)
      case 'getAllConfigs':
        return await getAllSystemConfigs()
      case 'updateConfig':
        return await updateSystemConfig(configKey, configValue, OPENID)
      case 'batchUpdate':
        return await batchUpdateSystemConfigs(event.configs, OPENID)
      case 'getConfigVersion':
        return await getConfigVersion()
      case 'getConfigHistory':
        return await getConfigHistory()
      default:
        return {
          success: false,
          error: '未知的操作类型'
        }
    }
  } catch (error) {
    console.error('系统配置云函数执行失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取系统配置
 */
async function getSystemConfig(configKey) {
  try {
    if (configKey) {
      // 获取特定配置
      const result = await db.collection('system_configs').doc(configKey).get()
      return {
        success: true,
        data: result.data
      }
    } else {
      // 获取所有配置
      const result = await db.collection('system_configs').get()
      const configs = {}
      result.data.forEach(item => {
        configs[item._id] = item.value
      })
      return {
        success: true,
        data: configs
      }
    }
  } catch (error) {
    console.error('获取系统配置失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取所有系统配置
 */
async function getAllSystemConfigs() {
  try {
    const result = await db.collection('system_configs').get()
    const configs = {}
    let lastUpdateTime = null

    result.data.forEach(item => {
      configs[item._id] = {
        value: item.value,
        description: item.description,
        updateTime: item.updateTime,
        updatedBy: item.updatedBy
      }
      if (!lastUpdateTime || new Date(item.updateTime) > new Date(lastUpdateTime)) {
        lastUpdateTime = item.updateTime
      }
    })

    return {
      success: true,
      data: {
        configs,
        lastUpdateTime,
        version: configs.version || '1.0.0'
      }
    }
  } catch (error) {
    console.error('获取所有系统配置失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 更新系统配置
 */
async function updateSystemConfig(configKey, configValue, updatedBy) {
  try {
    if (!configKey) {
      return {
        success: false,
        error: '配置键不能为空'
      }
    }

    // 检查配置是否存在
    const existing = await db.collection('system_configs').doc(configKey).get()
    
    if (existing.data) {
      // 更新现有配置
      await db.collection('system_configs').doc(configKey).update({
        data: {
          value: configValue,
          updateTime: new Date(),
          updatedBy
        }
      })
    } else {
      // 创建新配置
      await db.collection('system_configs').add({
        data: {
          _id: configKey,
          value: configValue,
          createTime: new Date(),
          updateTime: new Date(),
          updatedBy
        }
      })
    }

    // 记录配置变更历史
    await recordConfigChange(configKey, configValue, updatedBy)

    return {
      success: true,
      message: '配置更新成功',
      data: {
        configKey,
        configValue,
        updateTime: new Date()
      }
    }
  } catch (error) {
    console.error('更新系统配置失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 批量更新系统配置
 */
async function batchUpdateSystemConfigs(configs, updatedBy) {
  try {
    if (!configs || typeof configs !== 'object') {
      return {
        success: false,
        error: '配置数据格式错误'
      }
    }

    const batch = db.batch()
    const updateTime = new Date()

    for (const [key, value] of Object.entries(configs)) {
      // 检查配置是否存在
      const existing = await db.collection('system_configs').doc(key).get()
      
      if (existing.data) {
        // 更新现有配置
        batch.update(db.collection('system_configs').doc(key), {
          data: {
            value,
            updateTime,
            updatedBy
          }
        })
      } else {
        // 创建新配置
        batch.set(db.collection('system_configs').doc(key), {
          value,
          createTime: updateTime,
          updateTime,
          updatedBy
        })
      }

      // 记录配置变更历史
      await recordConfigChange(key, value, updatedBy)
    }

    await batch.commit()

    return {
      success: true,
      message: '批量配置更新成功',
      data: {
        updatedKeys: Object.keys(configs),
        updateTime
      }
    }
  } catch (error) {
    console.error('批量更新系统配置失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取配置版本信息
 */
async function getConfigVersion() {
  try {
    const versionConfig = await db.collection('system_configs').doc('version').get()
    const allConfigs = await db.collection('system_configs').get()
    
    let lastUpdateTime = null
    allConfigs.data.forEach(item => {
      if (!lastUpdateTime || new Date(item.updateTime) > new Date(lastUpdateTime)) {
        lastUpdateTime = item.updateTime
      }
    })

    return {
      success: true,
      data: {
        version: versionConfig.data?.value || '1.0.0',
        lastUpdateTime: lastUpdateTime || new Date().toISOString(),
        configCount: allConfigs.data.length
      }
    }
  } catch (error) {
    console.error('获取配置版本失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取配置变更历史
 */
async function getConfigHistory() {
  try {
    const history = await db.collection('config_history')
      .orderBy('updateTime', 'desc')
      .limit(50)
      .get()

    return {
      success: true,
      data: history.data
    }
  } catch (error) {
    console.error('获取配置历史失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 记录配置变更历史
 */
async function recordConfigChange(configKey, configValue, updatedBy) {
  try {
    await db.collection('config_history').add({
      data: {
        configKey,
        configValue,
        updatedBy,
        updateTime: new Date()
      }
    })
  } catch (error) {
    console.error('记录配置变更历史失败:', error)
  }
}