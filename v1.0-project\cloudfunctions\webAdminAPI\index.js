// cloudfunctions/webAdminAPI/index.js
const cloud = require('wx-server-sdk');
const jwt = require('jsonwebtoken');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// JWT密钥（与loginAPI保持一致）
const JWT_SECRET = process.env.JWT_SECRET || 'emoji-admin-v1.0-super-secret-key-2024';

exports.main = async (event, context) => {
  const { action, data, token } = event;
  const { OPENID, CLIENTIP } = cloud.getWXContext();

  console.log(`🔧 WebAdminAPI调用: ${action}`, {
    hasToken: !!token,
    ip: CLIENTIP,
    timestamp: new Date().toISOString()
  });

  try {
    // 验证JWT令牌
    const adminInfo = await verifyToken(token);
    if (!adminInfo.success) {
      return adminInfo; // 返回认证失败信息
    }

    // 记录API调用
    await logAPICall({
      action,
      adminId: adminInfo.data.adminId,
      ip: CLIENTIP,
      timestamp: new Date()
    });

    // 路由到具体的处理函数
    switch (action) {
      // 分类管理
      case 'createCategory':
        return await createCategory(data, adminInfo.data);
      case 'updateCategory':
        return await updateCategory(data.id, data.updates, adminInfo.data);
      case 'deleteCategory':
        return await deleteCategory(data.id, adminInfo.data);
      case 'getCategories':
        return await getCategories(data);

      // 表情包管理
      case 'createEmoji':
        return await createEmoji(data, adminInfo.data);
      case 'updateEmoji':
        return await updateEmoji(data.id, data.updates, adminInfo.data);
      case 'deleteEmoji':
        return await deleteEmoji(data.id, adminInfo.data);
      case 'getEmojis':
        return await getEmojis(data);

      // 横幅管理
      case 'createBanner':
        return await createBanner(data, adminInfo.data);
      case 'updateBanner':
        return await updateBanner(data.id, data.updates, adminInfo.data);
      case 'deleteBanner':
        return await deleteBanner(data.id, adminInfo.data);
      case 'getBanners':
        return await getBanners(data);

      // 系统管理
      case 'getStats':
        return await getSystemStats();
      case 'clearSyncNotifications':
        return await clearSyncNotifications(adminInfo.data);

      default:
        return {
          success: false,
          error: '未知操作',
          code: 'UNKNOWN_ACTION'
        };
    }
  } catch (error) {
    console.error('❌ WebAdminAPI错误:', error);
    
    return {
      success: false,
      error: '服务器内部错误',
      code: 'INTERNAL_ERROR',
      details: error.message
    };
  }
};

// 验证JWT令牌
async function verifyToken(token) {
  try {
    if (!token) {
      return {
        success: false,
        error: '缺少认证令牌',
        code: 'MISSING_TOKEN'
      };
    }

    // 移除Bearer前缀
    const cleanToken = token.replace('Bearer ', '');
    
    const decoded = jwt.verify(cleanToken, JWT_SECRET);
    
    return {
      success: true,
      data: {
        adminId: decoded.adminId,
        permissions: decoded.permissions || [],
        loginTime: decoded.loginTime
      }
    };
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return {
        success: false,
        error: '令牌已过期，请重新登录',
        code: 'TOKEN_EXPIRED'
      };
    } else if (error.name === 'JsonWebTokenError') {
      return {
        success: false,
        error: '无效的令牌',
        code: 'INVALID_TOKEN'
      };
    } else {
      console.error('❌ 令牌验证失败:', error);
      return {
        success: false,
        error: '令牌验证失败',
        code: 'TOKEN_VERIFICATION_FAILED'
      };
    }
  }
}

// 检查权限
function checkPermission(adminInfo, requiredPermission) {
  const permissions = adminInfo.permissions || [];
  return permissions.includes(requiredPermission) || permissions.includes('admin');
}

// 创建分类 - 事务版本
async function createCategory(categoryData, adminInfo) {
  try {
    console.log('📁 创建分类:', categoryData.name);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 数据验证
    if (!categoryData.name || !categoryData.name.trim()) {
      return {
        success: false,
        error: '分类名称不能为空',
        code: 'INVALID_DATA'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      const categoryId = generateUniqueId();
      const now = new Date();

      // 第一步：创建分类数据
      const categoryResult = await transaction.collection('categories').add({
        data: {
          id: categoryId,
          name: categoryData.name.trim(),
          icon: categoryData.icon || '',
          description: categoryData.description || '',
          sort: categoryData.sort || 0,
          status: 'active',
          emojiCount: 0,
          createTime: now,
          updateTime: now,
          createdBy: adminInfo.adminId,
          updatedBy: adminInfo.adminId
        }
      });

      // 第二步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'categories',
          operation: 'create',
          dataId: categoryId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: categoryId,
            name: categoryData.name.trim(),
            icon: categoryData.icon || '',
            status: 'active'
          }
        }
      });

      return {
        categoryId: categoryId,
        _id: categoryResult._id,
        success: true
      };
    });

    console.log('✅ 分类创建事务完成:', result.categoryId);
    
    return {
      success: true,
      data: {
        id: result.categoryId,
        _id: result._id,
        name: categoryData.name.trim()
      },
      message: '分类创建成功'
    };

  } catch (error) {
    console.error('❌ 分类创建事务失败:', error);
    
    return {
      success: false,
      error: error.message || '分类创建失败，请重试',
      code: 'CREATE_CATEGORY_FAILED'
    };
  }
}

// 更新分类 - 事务版本
async function updateCategory(categoryId, updateData, adminInfo) {
  try {
    console.log('📝 更新分类:', categoryId);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查分类是否存在
      const existingCategory = await transaction
        .collection('categories')
        .where({ id: categoryId })
        .get();

      if (existingCategory.data.length === 0) {
        throw new Error('分类不存在');
      }

      const category = existingCategory.data[0];
      const now = new Date();

      // 第二步：更新分类数据
      const updatedData = {
        ...updateData,
        updateTime: now,
        updatedBy: adminInfo.adminId
      };

      await transaction.collection('categories').doc(category._id).update({
        data: updatedData
      });

      // 第三步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'categories',
          operation: 'update',
          dataId: categoryId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          changes: Object.keys(updateData),
          data: {
            id: categoryId,
            ...updatedData
          }
        }
      });

      return { categoryId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '分类更新成功'
    };

  } catch (error) {
    console.error('❌ 分类更新事务失败:', error);
    return {
      success: false,
      error: error.message || '分类更新失败，请重试',
      code: 'UPDATE_CATEGORY_FAILED'
    };
  }
}

// 删除分类 - 事务版本（软删除）
async function deleteCategory(categoryId, adminInfo) {
  try {
    console.log('🗑️ 删除分类:', categoryId);

    // 权限检查
    if (!checkPermission(adminInfo, 'delete')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查分类是否存在
      const existingCategory = await transaction
        .collection('categories')
        .where({ id: categoryId })
        .get();

      if (existingCategory.data.length === 0) {
        throw new Error('分类不存在');
      }

      const category = existingCategory.data[0];

      // 第二步：检查是否有关联的表情包
      const relatedEmojis = await transaction
        .collection('emojis')
        .where({
          categoryId: categoryId,
          status: 'active'
        })
        .count();

      if (relatedEmojis.total > 0) {
        throw new Error(`该分类下还有${relatedEmojis.total}个表情包，无法删除`);
      }

      const now = new Date();

      // 第三步：软删除分类
      await transaction.collection('categories').doc(category._id).update({
        data: {
          status: 'deleted',
          deleteTime: now,
          deletedBy: adminInfo.adminId,
          updateTime: now
        }
      });

      // 第四步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'categories',
          operation: 'delete',
          dataId: categoryId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: categoryId,
            status: 'deleted'
          }
        }
      });

      return { categoryId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '分类删除成功'
    };

  } catch (error) {
    console.error('❌ 分类删除事务失败:', error);
    return {
      success: false,
      error: error.message || '分类删除失败，请重试',
      code: 'DELETE_CATEGORY_FAILED'
    };
  }
}

// 获取分类列表
async function getCategories(params = {}) {
  try {
    const { status = 'active', limit = 100 } = params;
    
    const result = await db.collection('categories')
      .where({ status })
      .orderBy('sort', 'asc')
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data,
      total: result.data.length
    };
  } catch (error) {
    console.error('❌ 获取分类列表失败:', error);
    return {
      success: false,
      error: '获取分类列表失败',
      code: 'GET_CATEGORIES_FAILED'
    };
  }
}

// 创建表情包 - 事务版本
async function createEmoji(emojiData, adminInfo) {
  try {
    console.log('😀 创建表情包:', emojiData.title);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 数据验证
    if (!emojiData.title || !emojiData.title.trim()) {
      return {
        success: false,
        error: '表情包标题不能为空',
        code: 'INVALID_DATA'
      };
    }

    if (!emojiData.imageUrl || !emojiData.imageUrl.trim()) {
      return {
        success: false,
        error: '表情包图片URL不能为空',
        code: 'INVALID_DATA'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      const emojiId = generateUniqueId();
      const now = new Date();

      // 第一步：创建表情包数据
      const emojiResult = await transaction.collection('emojis').add({
        data: {
          id: emojiId,
          title: emojiData.title.trim(),
          category: emojiData.category || 'default',
          categoryId: emojiData.categoryId || '',
          imageUrl: emojiData.imageUrl.trim(),
          tags: emojiData.tags || [],
          description: emojiData.description || '',
          status: 'published',
          likes: 0,
          downloads: 0,
          collections: 0,
          createTime: now,
          updateTime: now,
          createdBy: adminInfo.adminId,
          updatedBy: adminInfo.adminId
        }
      });

      // 第二步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'emojis',
          operation: 'create',
          dataId: emojiId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: emojiId,
            title: emojiData.title.trim(),
            category: emojiData.category || 'default',
            imageUrl: emojiData.imageUrl.trim(),
            status: 'published'
          }
        }
      });

      // 第三步：更新分类的表情包数量
      if (emojiData.categoryId) {
        try {
          const categoryQuery = await transaction.collection('categories')
            .where({ id: emojiData.categoryId })
            .get();

          if (categoryQuery.data.length > 0) {
            const category = categoryQuery.data[0];
            await transaction.collection('categories').doc(category._id).update({
              data: {
                emojiCount: (category.emojiCount || 0) + 1,
                updateTime: now
              }
            });
          }
        } catch (error) {
          console.warn('更新分类表情包数量失败:', error);
        }
      }

      return {
        emojiId: emojiId,
        _id: emojiResult._id,
        success: true
      };
    });

    console.log('✅ 表情包创建事务完成:', result.emojiId);

    return {
      success: true,
      data: {
        id: result.emojiId,
        _id: result._id,
        title: emojiData.title.trim()
      },
      message: '表情包创建成功'
    };

  } catch (error) {
    console.error('❌ 表情包创建事务失败:', error);

    return {
      success: false,
      error: error.message || '表情包创建失败，请重试',
      code: 'CREATE_EMOJI_FAILED'
    };
  }
}

// 更新表情包 - 事务版本
async function updateEmoji(emojiId, updateData, adminInfo) {
  try {
    console.log('📝 更新表情包:', emojiId);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查表情包是否存在
      const existingEmoji = await transaction
        .collection('emojis')
        .where({ id: emojiId })
        .get();

      if (existingEmoji.data.length === 0) {
        throw new Error('表情包不存在');
      }

      const emoji = existingEmoji.data[0];
      const now = new Date();

      // 第二步：更新表情包数据
      const updatedData = {
        ...updateData,
        updateTime: now,
        updatedBy: adminInfo.adminId
      };

      await transaction.collection('emojis').doc(emoji._id).update({
        data: updatedData
      });

      // 第三步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'emojis',
          operation: 'update',
          dataId: emojiId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          changes: Object.keys(updateData),
          data: {
            id: emojiId,
            ...updatedData
          }
        }
      });

      return { emojiId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '表情包更新成功'
    };

  } catch (error) {
    console.error('❌ 表情包更新事务失败:', error);
    return {
      success: false,
      error: error.message || '表情包更新失败，请重试',
      code: 'UPDATE_EMOJI_FAILED'
    };
  }
}

// 删除表情包 - 事务版本（软删除）
async function deleteEmoji(emojiId, adminInfo) {
  try {
    console.log('🗑️ 删除表情包:', emojiId);

    // 权限检查
    if (!checkPermission(adminInfo, 'delete')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查表情包是否存在
      const existingEmoji = await transaction
        .collection('emojis')
        .where({ id: emojiId })
        .get();

      if (existingEmoji.data.length === 0) {
        throw new Error('表情包不存在');
      }

      const emoji = existingEmoji.data[0];
      const now = new Date();

      // 第二步：软删除表情包
      await transaction.collection('emojis').doc(emoji._id).update({
        data: {
          status: 'deleted',
          deleteTime: now,
          deletedBy: adminInfo.adminId,
          updateTime: now
        }
      });

      // 第三步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'emojis',
          operation: 'delete',
          dataId: emojiId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: emojiId,
            status: 'deleted'
          }
        }
      });

      // 第四步：更新分类的表情包数量
      if (emoji.categoryId) {
        try {
          const categoryQuery = await transaction.collection('categories')
            .where({ id: emoji.categoryId })
            .get();

          if (categoryQuery.data.length > 0) {
            const category = categoryQuery.data[0];
            await transaction.collection('categories').doc(category._id).update({
              data: {
                emojiCount: Math.max((category.emojiCount || 1) - 1, 0),
                updateTime: now
              }
            });
          }
        } catch (error) {
          console.warn('更新分类表情包数量失败:', error);
        }
      }

      return { emojiId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '表情包删除成功'
    };

  } catch (error) {
    console.error('❌ 表情包删除事务失败:', error);
    return {
      success: false,
      error: error.message || '表情包删除失败，请重试',
      code: 'DELETE_EMOJI_FAILED'
    };
  }
}

async function getEmojis(params = {}) {
  try {
    const { status = 'active', limit = 100 } = params;
    
    const result = await db.collection('emojis')
      .where({ status })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get();

    return {
      success: true,
      data: result.data,
      total: result.data.length
    };
  } catch (error) {
    return {
      success: false,
      error: '获取表情包列表失败'
    };
  }
}

// 创建横幅 - 事务版本
async function createBanner(bannerData, adminInfo) {
  try {
    console.log('🎯 创建横幅:', bannerData.title);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 数据验证
    if (!bannerData.title || !bannerData.title.trim()) {
      return {
        success: false,
        error: '横幅标题不能为空',
        code: 'INVALID_DATA'
      };
    }

    if (!bannerData.imageUrl || !bannerData.imageUrl.trim()) {
      return {
        success: false,
        error: '横幅图片URL不能为空',
        code: 'INVALID_DATA'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      const bannerId = generateUniqueId();
      const now = new Date();

      // 第一步：创建横幅数据
      const bannerResult = await transaction.collection('banners').add({
        data: {
          id: bannerId,
          title: bannerData.title.trim(),
          imageUrl: bannerData.imageUrl.trim(),
          linkUrl: bannerData.linkUrl || '',
          description: bannerData.description || '',
          sort: bannerData.sort || 0,
          status: 'active',
          clickCount: 0,
          createTime: now,
          updateTime: now,
          createdBy: adminInfo.adminId,
          updatedBy: adminInfo.adminId
        }
      });

      // 第二步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'banners',
          operation: 'create',
          dataId: bannerId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: bannerId,
            title: bannerData.title.trim(),
            imageUrl: bannerData.imageUrl.trim(),
            linkUrl: bannerData.linkUrl || '',
            sort: bannerData.sort || 0,
            status: 'active'
          }
        }
      });

      return {
        bannerId: bannerId,
        _id: bannerResult._id,
        success: true
      };
    });

    console.log('✅ 横幅创建事务完成:', result.bannerId);

    return {
      success: true,
      data: {
        id: result.bannerId,
        _id: result._id,
        title: bannerData.title.trim()
      },
      message: '横幅创建成功'
    };

  } catch (error) {
    console.error('❌ 横幅创建事务失败:', error);

    return {
      success: false,
      error: error.message || '横幅创建失败，请重试',
      code: 'CREATE_BANNER_FAILED'
    };
  }
}

// 更新横幅 - 事务版本
async function updateBanner(bannerId, updateData, adminInfo) {
  try {
    console.log('📝 更新横幅:', bannerId);

    // 权限检查
    if (!checkPermission(adminInfo, 'write')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查横幅是否存在
      const existingBanner = await transaction
        .collection('banners')
        .where({ id: bannerId })
        .get();

      if (existingBanner.data.length === 0) {
        throw new Error('横幅不存在');
      }

      const banner = existingBanner.data[0];
      const now = new Date();

      // 第二步：更新横幅数据
      const updatedData = {
        ...updateData,
        updateTime: now,
        updatedBy: adminInfo.adminId
      };

      await transaction.collection('banners').doc(banner._id).update({
        data: updatedData
      });

      // 第三步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'banners',
          operation: 'update',
          dataId: bannerId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          changes: Object.keys(updateData),
          data: {
            id: bannerId,
            ...updatedData
          }
        }
      });

      return { bannerId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '横幅更新成功'
    };

  } catch (error) {
    console.error('❌ 横幅更新事务失败:', error);
    return {
      success: false,
      error: error.message || '横幅更新失败，请重试',
      code: 'UPDATE_BANNER_FAILED'
    };
  }
}

// 删除横幅 - 事务版本（软删除）
async function deleteBanner(bannerId, adminInfo) {
  try {
    console.log('🗑️ 删除横幅:', bannerId);

    // 权限检查
    if (!checkPermission(adminInfo, 'delete')) {
      return {
        success: false,
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSION'
      };
    }

    // 开始事务
    const result = await db.runTransaction(async transaction => {
      // 第一步：检查横幅是否存在
      const existingBanner = await transaction
        .collection('banners')
        .where({ id: bannerId })
        .get();

      if (existingBanner.data.length === 0) {
        throw new Error('横幅不存在');
      }

      const banner = existingBanner.data[0];
      const now = new Date();

      // 第二步：软删除横幅
      await transaction.collection('banners').doc(banner._id).update({
        data: {
          status: 'deleted',
          deleteTime: now,
          deletedBy: adminInfo.adminId,
          updateTime: now
        }
      });

      // 第三步：创建同步通知
      await transaction.collection('sync_notifications').add({
        data: {
          id: generateUniqueId(),
          dataType: 'banners',
          operation: 'delete',
          dataId: bannerId,
          timestamp: now.toISOString(),
          adminId: adminInfo.adminId,
          processed: false,
          data: {
            id: bannerId,
            status: 'deleted'
          }
        }
      });

      return { bannerId, success: true };
    });

    return {
      success: true,
      data: result,
      message: '横幅删除成功'
    };

  } catch (error) {
    console.error('❌ 横幅删除事务失败:', error);
    return {
      success: false,
      error: error.message || '横幅删除失败，请重试',
      code: 'DELETE_BANNER_FAILED'
    };
  }
}

async function getBanners(params = {}) {
  try {
    const result = await db.collection('banners')
      .where({ status: 'active' })
      .orderBy('sort', 'asc')
      .limit(50)
      .get();

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: '获取横幅列表失败'
    };
  }
}

// 获取系统统计
async function getSystemStats() {
  try {
    const [categories, emojis, banners, notifications] = await Promise.all([
      db.collection('categories').where({ status: 'active' }).count(),
      db.collection('emojis').where({ status: 'active' }).count(),
      db.collection('banners').where({ status: 'active' }).count(),
      db.collection('sync_notifications').where({ processed: false }).count()
    ]);

    return {
      success: true,
      data: {
        categories: categories.total,
        emojis: emojis.total,
        banners: banners.total,
        pendingNotifications: notifications.total,
        lastUpdate: new Date().toISOString()
      }
    };
  } catch (error) {
    return {
      success: false,
      error: '获取系统统计失败'
    };
  }
}

// 清理同步通知
async function clearSyncNotifications(adminInfo) {
  try {
    if (!checkPermission(adminInfo, 'admin')) {
      return {
        success: false,
        error: '权限不足，需要管理员权限'
      };
    }

    const result = await db.collection('sync_notifications')
      .where({ processed: true })
      .remove();

    return {
      success: true,
      data: {
        removed: result.stats.removed
      },
      message: `清理了${result.stats.removed}条已处理的通知`
    };
  } catch (error) {
    return {
      success: false,
      error: '清理同步通知失败'
    };
  }
}

// 记录API调用日志
async function logAPICall(callInfo) {
  try {
    await db.collection('api_logs').add({
      data: {
        ...callInfo,
        id: generateUniqueId()
      }
    });
  } catch (error) {
    console.warn('⚠️ 记录API调用日志失败:', error);
  }
}

// 生成唯一ID
function generateUniqueId() {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
