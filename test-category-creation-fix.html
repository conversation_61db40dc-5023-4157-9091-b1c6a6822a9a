<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试分类创建修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 测试分类创建修复</h1>
        
        <div style="text-align: center;">
            <button onclick="testStartsWithFix()">测试startsWith修复</button>
            <button onclick="testCategoryCreation()">测试分类创建功能</button>
            <button onclick="testFullWorkflow()">测试完整工作流程</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;
        let testResults = [];

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            testResults = [];
        }

        function addTestResult(testName, passed, message) {
            testResults.push({ name: testName, passed, message });
            const resultClass = passed ? 'test-pass' : 'test-fail';
            const resultIcon = passed ? '✅' : '❌';
            log(`${resultIcon} ${testName}: ${message}`, passed ? 'success' : 'error');
        }

        // 初始化CloudBase
        async function initCloudBase() {
            try {
                if (tcbApp) return tcbApp;
                
                log('🚀 初始化CloudBase SDK...');
                tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                const auth = tcbApp.auth();
                await auth.signInAnonymously();
                log('✅ CloudBase初始化成功', 'success');
                return tcbApp;
            } catch (error) {
                log('❌ CloudBase初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        // 测试startsWith修复
        async function testStartsWithFix() {
            log('🧪 开始测试startsWith修复...');
            
            // 测试用例1: undefined值
            try {
                const testCategory1 = { name: '测试分类', icon: undefined };
                const result1 = (testCategory1.icon && typeof testCategory1.icon === 'string' && !testCategory1.icon.startsWith('data:')) ? testCategory1.icon : '📁';
                addTestResult('undefined图标处理', result1 === '📁', `undefined图标正确处理为默认图标: ${result1}`);
            } catch (error) {
                addTestResult('undefined图标处理', false, `处理undefined图标时出错: ${error.message}`);
            }

            // 测试用例2: null值
            try {
                const testCategory2 = { name: '测试分类', icon: null };
                const result2 = (testCategory2.icon && typeof testCategory2.icon === 'string' && !testCategory2.icon.startsWith('data:')) ? testCategory2.icon : '📁';
                addTestResult('null图标处理', result2 === '📁', `null图标正确处理为默认图标: ${result2}`);
            } catch (error) {
                addTestResult('null图标处理', false, `处理null图标时出错: ${error.message}`);
            }

            // 测试用例3: 空字符串
            try {
                const testCategory3 = { name: '测试分类', icon: '' };
                const result3 = (testCategory3.icon && typeof testCategory3.icon === 'string' && !testCategory3.icon.startsWith('data:')) ? testCategory3.icon : '📁';
                addTestResult('空字符串图标处理', result3 === '📁', `空字符串图标正确处理为默认图标: ${result3}`);
            } catch (error) {
                addTestResult('空字符串图标处理', false, `处理空字符串图标时出错: ${error.message}`);
            }

            // 测试用例4: 正常emoji
            try {
                const testCategory4 = { name: '测试分类', icon: '🎭' };
                const result4 = (testCategory4.icon && typeof testCategory4.icon === 'string' && !testCategory4.icon.startsWith('data:')) ? testCategory4.icon : '📁';
                addTestResult('正常emoji图标处理', result4 === '🎭', `正常emoji图标正确保留: ${result4}`);
            } catch (error) {
                addTestResult('正常emoji图标处理', false, `处理正常emoji图标时出错: ${error.message}`);
            }

            // 测试用例5: base64图片
            try {
                const testCategory5 = { name: '测试分类', icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==' };
                const result5 = (testCategory5.icon && typeof testCategory5.icon === 'string' && !testCategory5.icon.startsWith('data:')) ? testCategory5.icon : '📁';
                addTestResult('base64图标处理', result5 === '📁', `base64图标正确处理为默认图标: ${result5}`);
            } catch (error) {
                addTestResult('base64图标处理', false, `处理base64图标时出错: ${error.message}`);
            }

            log('🎉 startsWith修复测试完成', 'success');
        }

        // 测试分类创建功能
        async function testCategoryCreation() {
            try {
                log('🧪 开始测试分类创建功能...');
                
                await initCloudBase();
                
                // 创建测试分类数据
                const testCategory = {
                    name: '修复测试分类_' + Date.now(),
                    icon: '🧪',
                    description: '这是一个用于测试startsWith修复的分类',
                    sort: 999,
                    status: 'show'
                };

                log('📝 准备创建测试分类...');
                log(`分类信息: ${JSON.stringify(testCategory, null, 2)}`);

                // 调用云函数创建分类
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'addCategory',
                        adminPassword: 'admin123456',
                        categoryData: testCategory
                    }
                });

                if (result && result.result && result.result.success) {
                    addTestResult('分类创建', true, `分类创建成功，ID: ${result.result.data._id}`);
                    
                    // 验证分类是否真的保存到数据库
                    log('🔍 验证分类是否保存到数据库...');
                    const db = tcbApp.database();
                    const queryResult = await db.collection('categories')
                        .where({ name: testCategory.name })
                        .get();
                    
                    if (queryResult.data && queryResult.data.length > 0) {
                        addTestResult('数据库验证', true, `分类已成功保存到数据库`);
                        log(`数据库记录: ${JSON.stringify(queryResult.data[0], null, 2)}`);
                    } else {
                        addTestResult('数据库验证', false, `分类未在数据库中找到`);
                    }
                } else {
                    const errorMsg = result?.result?.message || '未知错误';
                    addTestResult('分类创建', false, `分类创建失败: ${errorMsg}`);
                }

            } catch (error) {
                addTestResult('分类创建', false, `分类创建过程出错: ${error.message}`);
                log('错误详情: ' + JSON.stringify(error, null, 2), 'error');
            }
        }

        // 测试完整工作流程
        async function testFullWorkflow() {
            log('🚀 开始完整工作流程测试...');
            
            // 步骤1: 测试修复
            await testStartsWithFix();
            
            // 步骤2: 测试分类创建
            await testCategoryCreation();
            
            // 步骤3: 汇总结果
            log('📊 测试结果汇总:', 'info');
            const passedTests = testResults.filter(t => t.passed).length;
            const totalTests = testResults.length;
            
            if (passedTests === totalTests) {
                log(`🎉 所有测试通过! (${passedTests}/${totalTests})`, 'success');
            } else {
                log(`⚠️ 部分测试失败: ${passedTests}/${totalTests} 通过`, 'warning');
            }
            
            // 显示失败的测试
            const failedTests = testResults.filter(t => !t.passed);
            if (failedTests.length > 0) {
                log('❌ 失败的测试:', 'error');
                failedTests.forEach(test => {
                    log(`  - ${test.name}: ${test.message}`, 'error');
                });
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，准备测试startsWith修复效果');
            log('💡 这个工具将验证分类创建的startsWith错误是否已修复');
        });
    </script>
</body>
</html>
