// cloudfunctions/updateUserStats/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 更新用户统计数据的云函数
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { action, emojiId, isLiked, isCollected, downloadTime } = event
    
    // 获取用户文档
    const userDoc = await db.collection('users').doc(openid).get()
    let userData = {}
    
    if (userDoc.data) {
      userData = userDoc.data
    } else {
      // 如果用户不存在，创建新用户
      userData = {
        openid: openid,
        likedEmojis: [],
        collectedEmojis: [],
        downloadedEmojis: [],
        downloadTimes: {},
        createTime: new Date(),
        updateTime: new Date()
      }
    }
    
    // 根据不同的操作类型更新数据
    switch (action) {
      case 'updateLike':
        if (isLiked) {
          // 添加点赞
          if (!userData.likedEmojis.includes(emojiId)) {
            userData.likedEmojis.push(emojiId)
          }
        } else {
          // 取消点赞
          userData.likedEmojis = userData.likedEmojis.filter(id => id !== emojiId)
        }
        break
        
      case 'updateCollect':
        if (isCollected) {
          // 添加收藏
          if (!userData.collectedEmojis.includes(emojiId)) {
            userData.collectedEmojis.push(emojiId)
          }
        } else {
          // 取消收藏
          userData.collectedEmojis = userData.collectedEmojis.filter(id => id !== emojiId)
        }
        break
        
      case 'updateDownload':
        // 记录下载
        if (!userData.downloadedEmojis.includes(emojiId)) {
          userData.downloadedEmojis.push(emojiId)
        }
        userData.downloadTimes[emojiId] = downloadTime || new Date().toISOString()
        break
        
      case 'syncLikedData':
        // 同步点赞数据
        userData.likedEmojis = event.likedEmojis || []
        break
        
      case 'syncCollectedData':
        // 同步收藏数据
        userData.collectedEmojis = event.collectedEmojis || []
        break
        
      case 'syncDownloadData':
        // 同步下载数据
        userData.downloadedEmojis = event.downloadedEmojis || []
        userData.downloadTimes = event.downloadTimes || {}
        break
        
      default:
        // 批量更新所有数据
        if (event.likedEmojis !== undefined) {
          userData.likedEmojis = event.likedEmojis
        }
        if (event.collectedEmojis !== undefined) {
          userData.collectedEmojis = event.collectedEmojis
        }
        if (event.downloadedEmojis !== undefined) {
          userData.downloadedEmojis = event.downloadedEmojis
        }
        if (event.downloadTimes !== undefined) {
          userData.downloadTimes = event.downloadTimes
        }
    }
    
    // 更新时间戳
    userData.updateTime = new Date()
    
    // 保存到数据库
    if (userDoc.data) {
      // 更新现有用户
      await db.collection('users').doc(openid).update({
        data: userData
      })
    } else {
      // 创建新用户
      await db.collection('users').doc(openid).set({
        data: userData
      })
    }
    
    return {
      success: true,
      message: '用户数据更新成功',
      data: {
        likedCount: userData.likedEmojis.length,
        collectedCount: userData.collectedEmojis.length,
        downloadedCount: userData.downloadedEmojis.length
      }
    }
    
  } catch (error) {
    console.error('更新用户统计数据失败:', error)
    return {
      success: false,
      error: error.message || '更新失败'
    }
  }
}