@echo off
echo ========================================
echo ☁️ 启动云端管理后台 - 本地演示版
echo ========================================

cd admin-unified

echo 📦 检查依赖...
if not exist node_modules (
    echo 📥 安装依赖中...
    npm install
)

echo 🌐 启动云端管理后台代理服务器...
echo.
echo 📱 云端管理后台将在浏览器中打开
echo 🔗 访问地址: http://localhost:8002
echo 🌐 云端界面: http://localhost:8002/cloud-admin
echo.
echo ⚠️  注意: 这是本地演示版本
echo    真正的云端管理后台需要部署到微信云开发环境
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================

start http://localhost:8002
node cloud-server.js

pause
