// 云函数入口文件 - 用户行为追踪
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 内置用户验证函数
async function verifyUser(openid) {
  try {
    if (!openid) {
      return { isValid: false, message: '用户未登录' }
    }

    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length === 0) {
      return { isValid: false, message: '用户不存在' }
    }

    const user = userResult.data[0]
    const isValid = user.auth && user.auth.status === 'active'

    return {
      isValid,
      user,
      message: isValid ? '用户验证通过' : '用户状态异常'
    }
  } catch (error) {
    console.error('用户验证失败:', error)
    return { isValid: false, message: '用户验证失败' }
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, targetType, targetId, details } = event
  const wxContext = cloud.getWXContext()

  console.log('用户行为追踪:', { action, targetType, targetId, openid: wxContext.OPENID })

  try {
    // 对于敏感操作需要验证用户权限
    const sensitiveActions = ['like', 'collect', 'download', 'share']
    if (sensitiveActions.includes(action)) {
      const authResult = await verifyUser(wxContext.OPENID)
      if (!authResult.isValid) {
        return {
          success: false,
          message: '用户未登录或权限不足'
        }
      }
    }
    // 记录用户行为
    await db.collection('user_actions').add({
      data: {
        userId: wxContext.OPENID,
        action,
        targetType,
        targetId,
        details: details || {},
        timestamp: new Date(),
        // 30天后自动删除
        expireAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      }
    })
    
    // 更新相关统计
    await updateStatistics(action, targetType, targetId)
    
    return {
      success: true,
      message: '行为记录成功'
    }
  } catch (error) {
    console.error('记录用户行为失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 更新统计数据
async function updateStatistics(action, targetType, targetId) {
  try {
    if (targetType === 'emoji' && targetId) {
      const updateData = {}
      
      switch (action) {
        case 'view':
          updateData.views = _.inc(1)
          break
        case 'like':
          updateData.likes = _.inc(1)
          break
        case 'unlike':
          updateData.likes = _.inc(-1)
          break
        case 'collect':
          updateData.collections = _.inc(1)
          break
        case 'uncollect':
          updateData.collections = _.inc(-1)
          break
        case 'download':
          updateData.downloads = _.inc(1)
          break
        case 'share':
          updateData.shares = _.inc(1)
          break
      }
      
      if (Object.keys(updateData).length > 0) {
        await db.collection('emojis').doc(targetId).update({
          data: updateData
        })
      }
    }
    
    if (targetType === 'banner' && targetId && action === 'click') {
      await db.collection('banners').doc(targetId).update({
        data: {
          clickCount: _.inc(1)
        }
      })
    }
  } catch (error) {
    console.error('更新统计数据失败:', error)
  }
}