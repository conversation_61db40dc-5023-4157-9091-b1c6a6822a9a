// 调试分类数据过滤问题 - 新版本
const { chromium } = require('playwright');

async function debugCategoryFilterNew() {
    console.log('🔍 调试分类数据过滤问题 - 新版本...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[BROWSER] ${text}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 调试分类数据过滤逻辑');
        
        // 模拟云函数的分类数据处理逻辑
        const categoryDebug = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                const result = await db.collection('categories').get();
                
                const debugInfo = {
                    originalCount: result.data.length,
                    originalData: [],
                    fixedData: [],
                    filteredData: []
                };
                
                // 分析原始数据
                result.data.forEach((category, index) => {
                    debugInfo.originalData.push({
                        index: index,
                        _id: category._id,
                        hasDataField: !!category.data,
                        directName: category.name,
                        dataName: category.data?.name,
                        directStatus: category.status,
                        dataStatus: category.data?.status,
                        nameType: typeof category.name,
                        dataNameType: typeof category.data?.name,
                        statusType: typeof category.status,
                        dataStatusType: typeof category.data?.status
                    });
                });
                
                // 模拟云函数的修复逻辑
                const fixedCategories = result.data.map(category => {
                    const categoryData = category.data || category
                    
                    return {
                        _id: category._id,
                        name: categoryData.name,
                        icon: categoryData.icon || '📁',
                        status: categoryData.status || 'show',
                        sort: categoryData.sort || 0,
                        count: categoryData.count || 0,
                        description: categoryData.description || '',
                        gradient: categoryData.gradient || '',
                        createTime: categoryData.createTime || category.createTime,
                        updateTime: categoryData.updateTime || category.updateTime
                    }
                });
                
                // 分析修复后数据
                fixedCategories.forEach((category, index) => {
                    debugInfo.fixedData.push({
                        index: index,
                        _id: category._id,
                        name: category.name,
                        status: category.status,
                        nameType: typeof category.name,
                        statusType: typeof category.status,
                        hasValidName: !!category.name,
                        hasValidStatus: category.status === 'show',
                        nameValue: JSON.stringify(category.name),
                        statusValue: JSON.stringify(category.status)
                    });
                });
                
                // 应用过滤条件
                const filteredCategories = fixedCategories.filter(category => {
                    const hasValidStatus = category.status === 'show';
                    const hasValidName = !!category.name;
                    return hasValidStatus && hasValidName;
                });
                
                debugInfo.filteredData = filteredCategories;
                debugInfo.fixedCount = fixedCategories.length;
                debugInfo.filteredCount = filteredCategories.length;
                
                return {
                    success: true,
                    debug: debugInfo
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 分类数据调试结果:');
        if (categoryDebug.success) {
            const debug = categoryDebug.debug;
            console.log(`原始数据: ${debug.originalCount} 条`);
            console.log(`修复后数据: ${debug.fixedCount} 条`);
            console.log(`过滤后数据: ${debug.filteredCount} 条`);
            
            console.log('\n📋 原始数据详情:');
            debug.originalData.forEach((cat) => {
                console.log(`  ${cat.index + 1}. ID: ${cat._id}`);
                console.log(`     有data字段: ${cat.hasDataField}`);
                console.log(`     直接name: ${cat.directName} (类型: ${cat.nameType})`);
                console.log(`     data.name: ${cat.dataName} (类型: ${cat.dataNameType})`);
                console.log(`     直接status: ${cat.directStatus} (类型: ${cat.statusType})`);
                console.log(`     data.status: ${cat.dataStatus} (类型: ${cat.dataStatusType})`);
                console.log('');
            });
            
            console.log('\n📋 修复后数据详情:');
            debug.fixedData.forEach((cat) => {
                console.log(`  ${cat.index + 1}. ID: ${cat._id}`);
                console.log(`     名称: ${cat.nameValue} (类型: ${cat.nameType}, 有效: ${cat.hasValidName})`);
                console.log(`     状态: ${cat.statusValue} (类型: ${cat.statusType}, 有效: ${cat.hasValidStatus})`);
                console.log(`     通过过滤: ${cat.hasValidName && cat.hasValidStatus}`);
                console.log('');
            });
            
            if (debug.filteredCount === 0) {
                console.log('🔴 问题发现: 所有分类数据都被过滤掉了！');
                
                // 分析具体原因
                const nameIssues = debug.fixedData.filter(cat => !cat.hasValidName);
                const statusIssues = debug.fixedData.filter(cat => !cat.hasValidStatus);
                
                console.log(`名称问题: ${nameIssues.length} 条`);
                console.log(`状态问题: ${statusIssues.length} 条`);
                
                if (nameIssues.length > 0) {
                    console.log('名称问题详情:');
                    nameIssues.forEach(cat => {
                        console.log(`  - ${cat._id}: name=${cat.nameValue} (${cat.nameType})`);
                    });
                }
                
                if (statusIssues.length > 0) {
                    console.log('状态问题详情:');
                    statusIssues.forEach(cat => {
                        console.log(`  - ${cat._id}: status=${cat.statusValue} (${cat.statusType})`);
                    });
                }
            } else {
                console.log('✅ 部分分类数据通过了过滤');
            }
        } else {
            console.log(`❌ 调试失败: ${categoryDebug.error}`);
        }
        
        return categoryDebug;
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        await page.screenshot({ path: 'debug-category-filter-new.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-category-filter-new.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行调试
debugCategoryFilterNew().then(result => {
    console.log('\n🎯 分类数据过滤调试结果:', result.success ? '成功' : '失败');
}).catch(console.error);
