// utils/testData.js - 测试数据初始化工具

const TestData = {
  // 初始化测试数据
  initTestData() {
    try {
      // 模拟一些点赞的表情包
      const likedEmojis = ['1', '3', '5']
      wx.setStorageSync('likedEmojis', likedEmojis)
      
      // 模拟一些收藏的表情包
      const collectedEmojis = ['2', '4', '6']
      wx.setStorageSync('collectedEmojis', collectedEmojis)
      
      // 模拟一些下载的表情包
      const downloadedEmojis = ['1', '2', '7']
      wx.setStorageSync('downloadedEmojis', downloadedEmojis)
      
      // 模拟下载时间
      const downloadTimes = {
        '1': new Date(Date.now() - 3600000).toISOString(), // 1小时前
        '2': new Date(Date.now() - 86400000).toISOString(), // 1天前
        '7': new Date(Date.now() - 300000).toISOString()   // 5分钟前
      }
      wx.setStorageSync('downloadTimes', downloadTimes)
      
      // 模拟最近浏览
      const recentEmojis = ['1', '2', '3', '4', '5']
      wx.setStorageSync('recentEmojis', recentEmojis)
      
      // 模拟应用设置
      const appSettings = {
        autoDownload: false,
        vibration: true,
        notification: true,
        darkMode: false
      }
      wx.setStorageSync('appSettings', appSettings)
      
      console.log('测试数据初始化完成')
      return true
    } catch (error) {
      console.error('测试数据初始化失败:', error)
      return false
    }
  },

  // 清除所有测试数据
  clearTestData() {
    try {
      wx.removeStorageSync('likedEmojis')
      wx.removeStorageSync('collectedEmojis')
      wx.removeStorageSync('downloadedEmojis')
      wx.removeStorageSync('downloadTimes')
      wx.removeStorageSync('recentEmojis')
      wx.removeStorageSync('appSettings')
      
      console.log('测试数据已清除')
      return true
    } catch (error) {
      console.error('清除测试数据失败:', error)
      return false
    }
  },

  // 获取当前存储状态
  getStorageStatus() {
    try {
      const status = {
        likedEmojis: wx.getStorageSync('likedEmojis') || [],
        collectedEmojis: wx.getStorageSync('collectedEmojis') || [],
        downloadedEmojis: wx.getStorageSync('downloadedEmojis') || [],
        recentEmojis: wx.getStorageSync('recentEmojis') || [],
        downloadTimes: wx.getStorageSync('downloadTimes') || {},
        appSettings: wx.getStorageSync('appSettings') || {}
      }
      
      console.log('当前存储状态:', status)
      return status
    } catch (error) {
      console.error('获取存储状态失败:', error)
      return null
    }
  }
}

module.exports = {
  TestData
}
