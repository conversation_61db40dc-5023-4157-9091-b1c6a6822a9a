/**
 * 自动同步数据管理器
 * 让小程序能够自动从管理后台同步的数据中读取
 */

class AutoSyncDataManager {
  constructor() {
    this.baseUrl = 'http://localhost:8003'; // 小程序数据服务地址
    this.fallbackToCloud = true; // 是否降级到云函数
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 通用数据请求方法
   */
  async request(action, data = {}) {
    const cacheKey = `${action}_${JSON.stringify(data)}`;
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        console.log(`📦 使用缓存数据: ${action}`);
        return cached.data;
      }
    }

    try {
      // 首先尝试从自动同步服务获取数据
      const result = await this.requestFromSyncService(action, data);
      
      // 缓存结果
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      return result;
      
    } catch (error) {
      console.warn(`⚠️ 自动同步服务请求失败: ${error.message}`);
      
      if (this.fallbackToCloud) {
        console.log('🔄 降级到云函数调用...');
        return await this.requestFromCloud(action, data);
      } else {
        throw error;
      }
    }
  }

  /**
   * 从自动同步服务请求数据
   */
  async requestFromSyncService(action, data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}/wx-cloud-api`,
        method: 'POST',
        data: { action, data },
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            console.log(`✅ 自动同步数据获取成功: ${action}`);
            resolve(res.data);
          } else {
            reject(new Error(res.data.message || '请求失败'));
          }
        },
        fail: (error) => {
          reject(new Error(`网络请求失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 降级到云函数调用
   */
  async requestFromCloud(action, data) {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action, data },
        success: (res) => {
          if (res.result && res.result.success) {
            console.log(`☁️ 云函数数据获取成功: ${action}`);
            resolve(res.result);
          } else {
            reject(new Error(res.result?.message || '云函数调用失败'));
          }
        },
        fail: (error) => {
          reject(new Error(`云函数调用失败: ${error.errMsg}`));
        }
      });
    });
  }

  /**
   * 获取分类数据
   */
  async getCategories() {
    try {
      const result = await this.request('getCategories');
      return result.data || [];
    } catch (error) {
      console.error('获取分类数据失败:', error);
      return [];
    }
  }

  /**
   * 获取表情包数据
   */
  async getEmojis(category = 'all', page = 1, limit = 20) {
    try {
      const result = await this.request('getEmojis', { category, page, limit });
      return {
        data: result.data || [],
        total: result.total || 0,
        hasMore: result.hasMore || false
      };
    } catch (error) {
      console.error('获取表情包数据失败:', error);
      return { data: [], total: 0, hasMore: false };
    }
  }

  /**
   * 获取横幅数据
   */
  async getBanners() {
    try {
      const result = await this.request('getBanners');
      return result.data || [];
    } catch (error) {
      console.error('获取横幅数据失败:', error);
      return [];
    }
  }

  /**
   * 获取表情包详情
   */
  async getEmojiDetail(id) {
    try {
      const result = await this.request('getEmojiDetail', { id });
      return result.data;
    } catch (error) {
      console.error('获取表情包详情失败:', error);
      return null;
    }
  }

  /**
   * 搜索表情包
   */
  async searchEmojis(keyword, category = 'all') {
    try {
      const result = await this.request('searchEmojis', { keyword, category });
      return result.data || [];
    } catch (error) {
      console.error('搜索表情包失败:', error);
      return [];
    }
  }

  /**
   * 检查同步状态
   */
  async checkSyncStatus() {
    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/api/sync-status`,
          method: 'GET',
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200 && response.data.success) {
        return response.data.data;
      } else {
        throw new Error('同步状态检查失败');
      }
    } catch (error) {
      console.warn('同步状态检查失败:', error);
      return { status: 'unknown' };
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('📦 数据缓存已清除');
  }

  /**
   * 设置是否降级到云函数
   */
  setFallbackToCloud(enabled) {
    this.fallbackToCloud = enabled;
    console.log(`🔄 云函数降级${enabled ? '已启用' : '已禁用'}`);
  }

  /**
   * 获取数据统计
   */
  async getStats() {
    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/api/stats`,
          method: 'GET',
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200 && response.data.success) {
        return response.data.data;
      } else {
        throw new Error('统计数据获取失败');
      }
    } catch (error) {
      console.warn('统计数据获取失败:', error);
      return null;
    }
  }
}

// 创建全局实例
const autoSyncDataManager = new AutoSyncDataManager();

// 导出
module.exports = autoSyncDataManager;

// 如果在小程序环境中，也挂载到全局
if (typeof getApp === 'function') {
  const app = getApp();
  app.autoSyncDataManager = autoSyncDataManager;
}
