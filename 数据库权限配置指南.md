# 🔐 微信云数据库权限配置指南

## 🎯 配置目标

为Web SDK版本的管理后台配置正确的数据库权限，确保：
- ✅ 管理后台可以正常读写数据
- ✅ 小程序可以正常读取数据
- ✅ 数据安全得到保障
- ✅ 免费套餐下正常工作

## 📋 配置步骤

### 第一步：打开云开发控制台

1. **在微信开发者工具中**：
   - 点击工具栏的"云开发"按钮
   - 进入云开发控制台

2. **或者在浏览器中**：
   - 访问：https://console.cloud.tencent.com/tcb
   - 选择环境：`cloud1-5g6pvnpl88dc0142`

### 第二步：配置数据库权限

#### **进入数据库权限设置**
1. 点击左侧菜单"数据库"
2. 点击"权限设置"标签页
3. 选择要配置的集合

#### **为每个集合配置权限**

##### **1. categories 集合权限**
```json
{
  "read": true,
  "write": "auth != null"
}
```

**说明**：
- `read: true` - 允许所有人读取分类数据（小程序需要）
- `write: "auth != null"` - 只允许已认证用户写入（管理后台）

##### **2. emojis 集合权限**
```json
{
  "read": true,
  "write": "auth != null"
}
```

**说明**：
- `read: true` - 允许所有人读取表情包数据（小程序需要）
- `write: "auth != null"` - 只允许已认证用户写入（管理后台）

##### **3. users 集合权限（如果存在）**
```json
{
  "read": "doc.openid == auth.openid || auth != null",
  "write": "doc.openid == auth.openid || auth != null"
}
```

**说明**：
- 用户只能读写自己的数据，或者已认证用户可以读写所有数据

##### **4. banners 集合权限（如果存在）**
```json
{
  "read": true,
  "write": "auth != null"
}
```

### 第三步：验证权限配置

#### **使用测试页面验证**
1. 访问：`http://localhost:9000/test-websdk.html`
2. 按顺序执行所有测试：
   - Web SDK 初始化测试
   - 管理员身份验证测试
   - 数据库直接操作测试
   - 完整功能测试

#### **预期测试结果**
- ✅ SDK初始化成功
- ✅ 匿名登录成功
- ✅ 数据库读取成功
- ✅ 数据库写入成功
- ✅ 统计数据获取成功

### 第四步：测试管理后台

#### **访问Web SDK版管理后台**
1. 访问：`http://localhost:9000/index-websdk.html`
2. 检查连接状态：应显示"✅ 已连接"
3. 测试各项功能：
   - 查看数据统计
   - 初始化测试数据
   - 添加/删除表情包
   - 添加/删除分类

## 🔧 权限规则详解

### **基础语法**
- `true` - 允许所有人访问
- `false` - 禁止所有人访问
- `auth != null` - 只允许已认证用户访问
- `doc.field == auth.openid` - 只允许访问自己的数据

### **常用权限模式**

#### **1. 公开读取，认证写入**
```json
{
  "read": true,
  "write": "auth != null"
}
```
**适用场景**：分类、表情包等公共数据

#### **2. 完全公开**
```json
{
  "read": true,
  "write": true
}
```
**适用场景**：开发测试阶段（⚠️ 生产环境不推荐）

#### **3. 完全私有**
```json
{
  "read": "auth != null",
  "write": "auth != null"
}
```
**适用场景**：敏感数据、管理员专用数据

#### **4. 用户私有数据**
```json
{
  "read": "doc.userId == auth.openid",
  "write": "doc.userId == auth.openid"
}
```
**适用场景**：用户收藏、点赞等个人数据

## 🚨 常见问题和解决方案

### **问题1：权限拒绝错误**
**错误信息**：`Permission denied`
**解决方案**：
1. 检查权限规则是否正确配置
2. 确认用户已正确认证
3. 验证数据结构是否符合权限规则

### **问题2：匿名登录失败**
**错误信息**：`Anonymous sign-in is disabled`
**解决方案**：
1. 在云开发控制台 → 环境 → 登录授权
2. 开启"匿名登录"选项

### **问题3：数据库连接失败**
**错误信息**：`Database connection failed`
**解决方案**：
1. 检查环境ID是否正确
2. 确认云开发服务是否正常
3. 检查网络连接

### **问题4：写入权限不足**
**错误信息**：`Insufficient permissions`
**解决方案**：
1. 确认已正确认证
2. 检查写入权限规则
3. 验证数据格式是否正确

## 🎯 最佳实践

### **开发阶段权限配置**
```json
{
  "read": true,
  "write": true
}
```
**优点**：开发方便，无权限限制
**缺点**：安全性低
**适用**：开发测试阶段

### **生产阶段权限配置**
```json
{
  "read": true,
  "write": "auth != null && get('database.admins.${auth.uid}').role == 'admin'"
}
```
**优点**：安全性高，精确控制
**缺点**：配置复杂
**适用**：生产环境

### **推荐的渐进式配置**

#### **第一阶段：开发测试**
- 所有集合：`read: true, write: true`
- 快速开发，功能验证

#### **第二阶段：功能完善**
- 公共数据：`read: true, write: "auth != null"`
- 私有数据：`read/write: "doc.userId == auth.openid"`

#### **第三阶段：生产部署**
- 添加管理员角色验证
- 实现细粒度权限控制
- 添加操作日志记录

## 🔍 权限验证清单

### **配置前检查**
- [ ] 确认云开发环境ID正确
- [ ] 确认需要配置的集合列表
- [ ] 了解每个集合的数据结构
- [ ] 明确访问权限需求

### **配置后验证**
- [ ] Web SDK可以正常初始化
- [ ] 匿名登录功能正常
- [ ] 数据读取权限正常
- [ ] 数据写入权限正常
- [ ] 小程序端数据显示正常
- [ ] 管理后台功能正常

### **功能测试**
- [ ] 管理后台添加数据 → 小程序立即显示
- [ ] 管理后台修改数据 → 小程序立即更新
- [ ] 管理后台删除数据 → 小程序立即移除
- [ ] 权限拒绝场景正常处理
- [ ] 网络异常恢复正常

## 🎉 配置完成标志

当以下所有条件都满足时，权限配置就完成了：

1. ✅ **测试页面全部通过**：`test-websdk.html` 所有测试项都显示成功
2. ✅ **管理后台正常工作**：`index-websdk.html` 连接状态显示"已连接"
3. ✅ **数据操作正常**：可以正常增删改查数据
4. ✅ **小程序同步正常**：管理后台操作后小程序立即显示更新
5. ✅ **错误处理正常**：权限不足时有明确的错误提示

---

## 🚀 立即开始配置

**第一步**：打开云开发控制台
```
微信开发者工具 → 云开发 → 数据库 → 权限设置
```

**第二步**：按照上述规则配置每个集合的权限

**第三步**：运行测试验证
```
访问：http://localhost:9000/test-websdk.html
```

**第四步**：测试管理后台
```
访问：http://localhost:9000/index-websdk.html
```

**配置成功后，你将拥有一个完全免费、实时同步的管理后台系统！**
