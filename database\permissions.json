{"collections": {"emojis": {"read": true, "write": "auth != null"}, "categories": {"read": true, "write": "auth != null"}, "users": {"read": "doc.openid == auth.openid || auth != null", "write": "doc.openid == auth.openid || auth != null"}, "user_likes": {"read": "doc.userId == auth.openid || auth != null", "write": "doc.userId == auth.openid || auth != null"}, "user_collections": {"read": "doc.userId == auth.openid || auth != null", "write": "doc.userId == auth.openid || auth != null"}, "user_actions": {"read": "doc.userId == auth.openid || auth != null", "write": "doc.userId == auth.openid || auth != null"}, "banners": {"read": true, "write": "auth != null"}, "operation_logs": {"read": "auth != null", "write": "auth != null"}, "upload_records": {"read": "doc.uploader == auth.openid || auth != null", "write": "doc.uploader == auth.openid || auth != null"}, "daily_reports": {"read": "auth != null", "write": "auth != null"}, "system_configs": {"read": "auth != null", "write": "auth != null"}}, "description": "数据库权限配置说明 - 适用于Web SDK管理后台", "notes": ["此配置适用于使用Web SDK的管理后台", "使用 'auth != null' 允许已认证用户（包括匿名登录）进行操作", "请在云开发控制台的数据库权限设置中配置上述权限规则", "权限规则使用云开发的安全规则语法", "确保每个集合都正确配置了读写权限", "生产环境建议使用更严格的权限控制"]}