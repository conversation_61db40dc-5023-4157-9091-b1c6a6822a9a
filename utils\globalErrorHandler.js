/**
 * 全局错误处理器
 * 统一处理应用中的各种错误，提供用户友好的错误提示和错误上报
 */

const GlobalErrorHandler = {
  // 错误配置
  config: {
    enableReporting: true,      // 是否启用错误上报
    maxErrorLogs: 100,         // 最大错误日志数量
    reportingEndpoint: null,   // 错误上报端点
    userFriendlyMessages: true, // 是否显示用户友好的错误信息
    autoRetry: true,           // 是否自动重试
    maxRetries: 3              // 最大重试次数
  },

  // 错误存储
  errorLogs: [],
  errorStats: {
    totalErrors: 0,
    networkErrors: 0,
    jsErrors: 0,
    apiErrors: 0,
    userErrors: 0
  },

  // 错误类型映射
  errorTypes: {
    NETWORK_ERROR: 'network',
    API_ERROR: 'api',
    JS_ERROR: 'javascript',
    USER_ERROR: 'user',
    SYSTEM_ERROR: 'system',
    VALIDATION_ERROR: 'validation'
  },

  // 用户友好的错误消息
  friendlyMessages: {
    network: '网络连接异常，请检查网络设置',
    api: '服务暂时不可用，请稍后重试',
    javascript: '应用出现异常，正在尝试恢复',
    user: '操作失败，请重试',
    system: '系统异常，请重启应用',
    validation: '输入信息有误，请检查后重试',
    default: '操作失败，请重试'
  },

  /**
   * 初始化全局错误处理器
   */
  init(options = {}) {
    this.config = { ...this.config, ...options }
    console.log('🛡️ 全局错误处理器初始化')

    // 捕获未处理的Promise rejection
    this.setupPromiseRejectionHandler()

    // 捕获JavaScript错误
    this.setupJSErrorHandler()

    // 捕获网络错误
    this.setupNetworkErrorHandler()

    // 设置微信小程序错误监听
    this.setupWxErrorHandler()
  },

  /**
   * 设置Promise rejection处理器
   */
  setupPromiseRejectionHandler() {
    // 在微信小程序环境中，需要手动处理Promise rejection
    const originalPromise = Promise
    const self = this

    // 重写Promise构造函数
    Promise = function(executor) {
      return new originalPromise((resolve, reject) => {
        const wrappedReject = (reason) => {
          self.handleError(reason, 'promise_rejection')
          reject(reason)
        }
        
        try {
          executor(resolve, wrappedReject)
        } catch (error) {
          wrappedReject(error)
        }
      })
    }

    // 保持原有的静态方法
    Object.setPrototypeOf(Promise, originalPromise)
    Object.defineProperty(Promise, 'prototype', {
      value: originalPromise.prototype,
      writable: false
    })
  },

  /**
   * 设置JavaScript错误处理器
   */
  setupJSErrorHandler() {
    // 在测试环境中跳过console.error重写，避免递归调用
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
      return
    }

    // 重写console.error来捕获错误
    const originalConsoleError = console.error
    const self = this

    console.error = function(...args) {
      // 避免递归调用
      if (args[0] && args[0].includes && args[0].includes('🚨')) {
        originalConsoleError.apply(console, args)
        return
      }

      self.handleError(new Error(args.join(' ')), 'javascript')
      originalConsoleError.apply(console, args)
    }
  },

  /**
   * 设置网络错误处理器
   */
  setupNetworkErrorHandler() {
    if (typeof wx === 'undefined') return

    // 重写wx.request来捕获网络错误
    const originalRequest = wx.request
    const self = this

    wx.request = function(options) {
      const originalFail = options.fail
      const originalSuccess = options.success

      options.fail = function(error) {
        self.handleError(error, 'network', {
          url: options.url,
          method: options.method || 'GET'
        })
        
        if (originalFail) {
          originalFail(error)
        }
      }

      options.success = function(response) {
        // 检查HTTP状态码
        if (response.statusCode >= 400) {
          self.handleError(new Error(`HTTP ${response.statusCode}`), 'api', {
            url: options.url,
            statusCode: response.statusCode,
            response: response.data
          })
        }
        
        if (originalSuccess) {
          originalSuccess(response)
        }
      }

      return originalRequest(options)
    }
  },

  /**
   * 设置微信小程序错误处理器
   */
  setupWxErrorHandler() {
    if (typeof wx === 'undefined') return

    const self = this

    // 监听小程序错误
    wx.onError((error) => {
      self.handleError(new Error(error), 'system')
    })

    // 监听未处理的Promise rejection
    wx.onUnhandledRejection((res) => {
      self.handleError(res.reason, 'promise_rejection')
    })
  },

  /**
   * 处理错误
   * @param {Error|string} error - 错误对象或错误信息
   * @param {string} type - 错误类型
   * @param {Object} context - 错误上下文
   */
  handleError(error, type = 'unknown', context = {}) {
    const errorInfo = this.normalizeError(error, type, context)
    
    // 记录错误
    this.logError(errorInfo)
    
    // 更新统计
    this.updateErrorStats(errorInfo)
    
    // 显示用户友好的错误提示
    if (this.config.userFriendlyMessages) {
      this.showUserFriendlyMessage(errorInfo)
    }
    
    // 上报错误
    if (this.config.enableReporting) {
      this.reportError(errorInfo)
    }
    
    // 尝试自动恢复
    if (this.config.autoRetry && this.canAutoRecover(errorInfo)) {
      this.attemptAutoRecovery(errorInfo)
    }
  },

  /**
   * 标准化错误信息
   */
  normalizeError(error, type, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      type: type,
      message: '',
      stack: '',
      context: context,
      userAgent: this.getUserAgent(),
      appVersion: this.getAppVersion()
    }

    if (error instanceof Error) {
      errorInfo.message = error.message
      errorInfo.stack = error.stack || ''
    } else if (typeof error === 'string') {
      errorInfo.message = error
    } else {
      errorInfo.message = JSON.stringify(error)
    }

    return errorInfo
  },

  /**
   * 记录错误
   */
  logError(errorInfo) {
    this.errorLogs.push(errorInfo)
    
    // 限制错误日志数量
    if (this.errorLogs.length > this.config.maxErrorLogs) {
      this.errorLogs.shift()
    }
    
    console.error(`🚨 [${errorInfo.type}] ${errorInfo.message}`, errorInfo)
  },

  /**
   * 更新错误统计
   */
  updateErrorStats(errorInfo) {
    this.errorStats.totalErrors++
    
    switch (errorInfo.type) {
      case 'network':
        this.errorStats.networkErrors++
        break
      case 'api':
        this.errorStats.apiErrors++
        break
      case 'javascript':
      case 'promise_rejection':
        this.errorStats.jsErrors++
        break
      case 'user':
        this.errorStats.userErrors++
        break
    }
  },

  /**
   * 显示用户友好的错误消息
   */
  showUserFriendlyMessage(errorInfo) {
    const message = this.friendlyMessages[errorInfo.type] || this.friendlyMessages.default
    
    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })
    } else {
      console.log('用户提示:', message)
    }
  },

  /**
   * 上报错误
   */
  async reportError(errorInfo) {
    if (!this.config.reportingEndpoint) {
      return
    }

    try {
      // 这里可以实现错误上报逻辑
      console.log('📊 错误上报:', errorInfo)
      
      // 示例：发送到服务器
      // await wx.request({
      //   url: this.config.reportingEndpoint,
      //   method: 'POST',
      //   data: errorInfo
      // })
    } catch (reportError) {
      console.warn('⚠️ 错误上报失败:', reportError)
    }
  },

  /**
   * 检查是否可以自动恢复
   */
  canAutoRecover(errorInfo) {
    // 网络错误和API错误可以尝试自动重试
    return ['network', 'api'].includes(errorInfo.type) && 
           (errorInfo.retryCount || 0) < this.config.maxRetries
  },

  /**
   * 尝试自动恢复
   */
  attemptAutoRecovery(errorInfo) {
    errorInfo.retryCount = (errorInfo.retryCount || 0) + 1
    
    console.log(`🔄 尝试自动恢复 (${errorInfo.retryCount}/${this.config.maxRetries})`)
    
    // 延迟重试
    setTimeout(() => {
      if (errorInfo.context && errorInfo.context.retryCallback) {
        errorInfo.context.retryCallback()
      }
    }, Math.pow(2, errorInfo.retryCount) * 1000) // 指数退避
  },

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },

  /**
   * 获取用户代理信息
   */
  getUserAgent() {
    if (typeof wx !== 'undefined') {
      return 'WeChat MiniProgram'
    }
    return 'Unknown'
  },

  /**
   * 获取应用版本
   */
  getAppVersion() {
    try {
      const app = getApp()
      return app.globalData?.version || '1.0.0'
    } catch (error) {
      return '1.0.0'
    }
  },

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      ...this.errorStats,
      recentErrors: this.errorLogs.slice(-10),
      errorRate: this.calculateErrorRate()
    }
  },

  /**
   * 计算错误率
   */
  calculateErrorRate() {
    const recentErrors = this.errorLogs.filter(
      error => Date.now() - error.timestamp < 60000 // 最近1分钟
    )
    return recentErrors.length
  },

  /**
   * 清理错误日志
   */
  clearErrorLogs() {
    this.errorLogs = []
    this.errorStats = {
      totalErrors: 0,
      networkErrors: 0,
      jsErrors: 0,
      apiErrors: 0,
      userErrors: 0
    }
    console.log('🧹 错误日志已清理')
  }
}

module.exports = GlobalErrorHandler
