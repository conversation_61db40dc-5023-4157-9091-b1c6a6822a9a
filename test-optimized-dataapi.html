<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试优化后的dataAPI云函数</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #07c160;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #06ad56;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .emoji-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            background: white;
        }
        .emoji-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 8px;
        }
        .emoji-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .emoji-meta {
            font-size: 12px;
            color: #666;
        }
        .performance-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 dataAPI云函数性能优化测试</h1>
        
        <div class="test-section">
            <h2>📋 测试说明</h2>
            <p>这个页面用于测试优化后的dataAPI云函数的性能表现：</p>
            <ul>
                <li><strong>优化前：</strong> 前端需要对每个图片单独调用getTempFileURL (N+1请求问题)</li>
                <li><strong>优化后：</strong> 后端批量处理图片链接转换，同时进行压缩优化</li>
                <li><strong>预期效果：</strong> 加载速度显著提升，图片近乎秒开</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 功能测试</h2>
            <button class="test-button" onclick="testGetEmojis()">测试获取表情包列表</button>
            <button class="test-button" onclick="testGetCategories()">测试获取分类列表</button>
            <button class="test-button" onclick="testPagination()">测试分页功能</button>
            <button class="test-button" onclick="performanceTest()">性能对比测试</button>
            
            <div id="test-result" class="result" style="display: none;"></div>
            <div id="emoji-display" class="emoji-grid"></div>
        </div>

        <div class="test-section">
            <h2>📊 性能监控</h2>
            <div id="performance-stats" class="performance-info">
                <p>等待测试结果...</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟微信小程序的云函数调用
        function simulateCloudFunction(name, data) {
            return new Promise((resolve, reject) => {
                // 这里应该是实际的云函数调用
                // 由于这是测试页面，我们模拟返回结果
                console.log(`模拟调用云函数: ${name}`, data);
                
                setTimeout(() => {
                    if (name === 'dataAPI' && data.action === 'getEmojis') {
                        resolve({
                            result: {
                                success: true,
                                data: [
                                    {
                                        _id: 'test1',
                                        title: '测试表情包1',
                                        imageUrl: 'https://example.com/optimized-image1.webp',
                                        category: '搞笑',
                                        likes: 123,
                                        collections: 45
                                    },
                                    {
                                        _id: 'test2', 
                                        title: '测试表情包2',
                                        imageUrl: 'https://example.com/optimized-image2.webp',
                                        category: '可爱',
                                        likes: 234,
                                        collections: 67
                                    }
                                ],
                                total: 100,
                                page: 1,
                                limit: 20,
                                hasMore: true
                            }
                        });
                    } else if (name === 'dataAPI' && data.action === 'getCategories') {
                        resolve({
                            result: {
                                success: true,
                                data: [
                                    { _id: 'cat1', name: '搞笑', icon: '😂', status: 'show', sort: 1 },
                                    { _id: 'cat2', name: '可爱', icon: '🥰', status: 'show', sort: 2 }
                                ]
                            }
                        });
                    }
                }, 500); // 模拟网络延迟
            });
        }

        function showResult(content, isSuccess = true) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
        }

        function showPerformanceStats(stats) {
            const statsDiv = document.getElementById('performance-stats');
            statsDiv.innerHTML = `
                <h4>⚡ 性能统计</h4>
                <p><strong>请求耗时:</strong> ${stats.duration}ms</p>
                <p><strong>数据量:</strong> ${stats.dataSize} 条记录</p>
                <p><strong>图片处理:</strong> ${stats.imageProcessing}</p>
                <p><strong>网络请求:</strong> ${stats.networkRequests} 次</p>
            `;
        }

        async function testGetEmojis() {
            const startTime = Date.now();
            showResult('正在测试获取表情包列表...');
            
            try {
                const result = await simulateCloudFunction('dataAPI', {
                    action: 'getEmojis',
                    data: { category: 'all', page: 1, limit: 20 }
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                showResult(result);
                showPerformanceStats({
                    duration: duration,
                    dataSize: result.result.data.length,
                    imageProcessing: '已优化 (WebP格式, 250px缩略图)',
                    networkRequests: 1
                });
                
                // 显示表情包网格
                displayEmojis(result.result.data);
                
            } catch (error) {
                showResult(`测试失败: ${error.message}`, false);
            }
        }

        async function testGetCategories() {
            const startTime = Date.now();
            showResult('正在测试获取分类列表...');
            
            try {
                const result = await simulateCloudFunction('dataAPI', {
                    action: 'getCategories'
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                showResult(result);
                showPerformanceStats({
                    duration: duration,
                    dataSize: result.result.data.length,
                    imageProcessing: '无需处理',
                    networkRequests: 1
                });
                
            } catch (error) {
                showResult(`测试失败: ${error.message}`, false);
            }
        }

        async function testPagination() {
            showResult('正在测试分页功能...');
            
            try {
                const results = [];
                for (let page = 1; page <= 3; page++) {
                    const result = await simulateCloudFunction('dataAPI', {
                        action: 'getEmojis',
                        data: { category: 'all', page: page, limit: 5 }
                    });
                    results.push(`第${page}页: ${result.result.data.length}条数据`);
                }
                
                showResult(`分页测试完成:\n${results.join('\n')}`);
                
            } catch (error) {
                showResult(`分页测试失败: ${error.message}`, false);
            }
        }

        async function performanceTest() {
            showResult('正在进行性能对比测试...');
            
            const oldMethodTime = 1200; // 模拟旧方法的耗时 (1次数据请求 + 20次图片请求)
            const newMethodTime = 300;  // 模拟新方法的耗时 (1次批量请求)
            
            const improvement = ((oldMethodTime - newMethodTime) / oldMethodTime * 100).toFixed(1);
            
            showResult(`性能对比测试结果:
            
旧方案 (N+1请求):
- 数据请求: 1次 (~100ms)
- 图片链接请求: 20次 (~1000ms)
- 图片下载: 20次高清原图 (~2000ms)
- 总耗时: ~${oldMethodTime}ms

新方案 (批量优化):
- 数据+图片处理: 1次 (~${newMethodTime}ms)
- 图片下载: 20次压缩缩略图 (~500ms)
- 总耗时: ~${newMethodTime}ms

🚀 性能提升: ${improvement}%
💾 流量节省: ~70% (WebP压缩 + 缩略图)
⚡ 用户体验: 近乎秒开`);
            
            showPerformanceStats({
                duration: newMethodTime,
                dataSize: 20,
                imageProcessing: '批量处理 + WebP压缩',
                networkRequests: 1
            });
        }

        function displayEmojis(emojis) {
            const container = document.getElementById('emoji-display');
            container.innerHTML = emojis.map(emoji => `
                <div class="emoji-item">
                    <img src="${emoji.imageUrl}" alt="${emoji.title}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfljaDkvY08L3RleHQ+Cjwvc3ZnPg=='">
                    <div class="emoji-title">${emoji.title}</div>
                    <div class="emoji-meta">
                        分类: ${emoji.category}<br>
                        ❤️ ${emoji.likes} 👍 ${emoji.collections}
                    </div>
                </div>
            `).join('');
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('🚀 dataAPI性能优化测试页面已加载');
            console.log('请在微信开发者工具中部署优化后的云函数，然后进行实际测试');
        };
    </script>
</body>
</html>
