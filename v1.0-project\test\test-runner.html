<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.0 功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .test-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
        }

        .test-section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #374151;
        }

        .test-case {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #d1d5db;
            background: #f9fafb;
        }

        .test-case.running {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }

        .test-case.passed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .test-case.failed {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .test-case-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .test-case-result {
            font-size: 14px;
            color: #6b7280;
        }

        .test-case.passed .test-case-result {
            color: #059669;
        }

        .test-case.failed .test-case-result {
            color: #dc2626;
        }

        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .test-button:hover {
            background: #5a67d8;
        }

        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-pending {
            background: #d1d5db;
        }

        .status-running {
            background: #3b82f6;
            animation: pulse 1s infinite;
        }

        .status-passed {
            background: #10b981;
        }

        .status-failed {
            background: #ef4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .summary {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .summary-item {
            text-align: center;
            padding: 10px;
            border-radius: 6px;
            background: #f3f4f6;
        }

        .summary-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">V1.0 核心功能测试</h1>
        
        <div class="summary">
            <div class="summary-item">
                <div class="summary-number" id="totalTests">0</div>
                <div class="summary-label">总测试</div>
            </div>
            <div class="summary-item">
                <div class="summary-number" id="passedTests">0</div>
                <div class="summary-label">通过</div>
            </div>
            <div class="summary-item">
                <div class="summary-number" id="failedTests">0</div>
                <div class="summary-label">失败</div>
            </div>
            <div class="summary-item">
                <div class="summary-number" id="runningTests">0</div>
                <div class="summary-label">运行中</div>
            </div>
        </div>

        <div>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="runAuthTests()">测试认证系统</button>
            <button class="test-button" onclick="runAPITests()">测试API系统</button>
            <button class="test-button" onclick="runRealtimeTests()">测试实时同步</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>

        <!-- JWT认证系统测试 -->
        <div class="test-section">
            <h2 class="test-section-title">🔐 JWT认证系统测试</h2>
            
            <div class="test-case" id="test-login-success">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC001: 正确凭据登录测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-login-failure">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC002: 错误凭据登录测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-token-validation">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC003: JWT令牌验证测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-token-refresh">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC004: 令牌刷新测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-logout">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC005: 登出功能测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>
        </div>

        <!-- API系统测试 -->
        <div class="test-section">
            <h2 class="test-section-title">📡 API系统测试</h2>
            
            <div class="test-case" id="test-create-category">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC101: 创建分类事务测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-update-category">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC102: 更新分类事务测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-delete-category">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC103: 删除分类事务测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-get-categories">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC104: 获取分类列表测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>
        </div>

        <!-- 实时同步测试 -->
        <div class="test-section">
            <h2 class="test-section-title">🔄 实时同步测试</h2>
            
            <div class="test-case" id="test-realtime-connection">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC201: 实时监听连接测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-realtime-notification">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC202: 同步通知处理测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>

            <div class="test-case" id="test-realtime-reconnect">
                <div class="test-case-title">
                    <span class="status-indicator status-pending"></span>
                    TC203: 断线重连测试
                </div>
                <div class="test-case-result">等待执行...</div>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h2 class="test-section-title">📋 测试日志</h2>
            <div class="test-log" id="testLog">
等待开始测试...
            </div>
        </div>
    </div>

    <!-- CloudBase Web SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.10.10/tcb.js"></script>
    
    <!-- 管理器脚本 -->
    <script src="../admin-web/js/auth-manager.js"></script>
    <script src="../admin-web/js/api-manager.js"></script>
    <script src="../admin-web/js/realtime-manager.js"></script>

    <script>
        // 测试运行器
        class TestRunner {
            constructor() {
                this.tests = [];
                this.results = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    running: 0
                };
                this.logElement = document.getElementById('testLog');
                this.isRunning = false;
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = {
                    'info': 'ℹ️',
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️'
                }[type] || 'ℹ️';
                
                const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
                this.logElement.textContent += logMessage;
                this.logElement.scrollTop = this.logElement.scrollHeight;
                
                console.log(`[TEST] ${message}`);
            }

            updateSummary() {
                document.getElementById('totalTests').textContent = this.results.total;
                document.getElementById('passedTests').textContent = this.results.passed;
                document.getElementById('failedTests').textContent = this.results.failed;
                document.getElementById('runningTests').textContent = this.results.running;
            }

            setTestStatus(testId, status, message = '') {
                const testElement = document.getElementById(testId);
                if (!testElement) return;

                const indicator = testElement.querySelector('.status-indicator');
                const result = testElement.querySelector('.test-case-result');
                
                // 清除所有状态类
                testElement.classList.remove('running', 'passed', 'failed');
                indicator.classList.remove('status-pending', 'status-running', 'status-passed', 'status-failed');
                
                // 设置新状态
                testElement.classList.add(status);
                indicator.classList.add(`status-${status}`);
                result.textContent = message;
            }

            async runTest(testId, testName, testFunction) {
                this.log(`开始测试: ${testName}`);
                this.setTestStatus(testId, 'running', '测试中...');
                this.results.running++;
                this.updateSummary();

                try {
                    const result = await testFunction();
                    
                    if (result.success) {
                        this.setTestStatus(testId, 'passed', result.message || '测试通过');
                        this.log(`测试通过: ${testName}`, 'success');
                        this.results.passed++;
                    } else {
                        this.setTestStatus(testId, 'failed', result.error || '测试失败');
                        this.log(`测试失败: ${testName} - ${result.error}`, 'error');
                        this.results.failed++;
                    }
                } catch (error) {
                    this.setTestStatus(testId, 'failed', `异常: ${error.message}`);
                    this.log(`测试异常: ${testName} - ${error.message}`, 'error');
                    this.results.failed++;
                }

                this.results.running--;
                this.updateSummary();
            }

            // JWT认证系统测试
            async testLoginSuccess() {
                return await this.runTest('test-login-success', 'TC001: 正确凭据登录测试', async () => {
                    // 先登出确保干净状态
                    await window.authManager.logout();
                    
                    const result = await window.authManager.login('admin', 'admin123456');
                    
                    if (!result.success) {
                        return { success: false, error: result.error };
                    }
                    
                    // 验证登录状态
                    if (!window.authManager.isLoggedIn()) {
                        return { success: false, error: '登录后状态检查失败' };
                    }
                    
                    // 验证令牌存在
                    const token = localStorage.getItem('admin_token');
                    if (!token) {
                        return { success: false, error: '令牌未保存' };
                    }
                    
                    return { success: true, message: '登录成功，令牌已保存' };
                });
            }

            async testLoginFailure() {
                return await this.runTest('test-login-failure', 'TC002: 错误凭据登录测试', async () => {
                    const result = await window.authManager.login('wronguser', 'wrongpass');
                    
                    if (result.success) {
                        return { success: false, error: '错误凭据不应该登录成功' };
                    }
                    
                    if (!result.error || !result.error.includes('用户名或密码错误')) {
                        return { success: false, error: '错误信息不正确' };
                    }
                    
                    return { success: true, message: '正确拒绝了错误凭据' };
                });
            }

            async testTokenValidation() {
                return await this.runTest('test-token-validation', 'TC003: JWT令牌验证测试', async () => {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    const result = await window.authManager.validateToken();
                    
                    if (!result.success) {
                        return { success: false, error: result.error };
                    }
                    
                    if (!result.data.adminId || result.data.adminId !== 'admin') {
                        return { success: false, error: '令牌验证返回的用户信息不正确' };
                    }
                    
                    return { success: true, message: '令牌验证通过' };
                });
            }

            async testTokenRefresh() {
                return await this.runTest('test-token-refresh', 'TC004: 令牌刷新测试', async () => {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    const oldToken = localStorage.getItem('admin_token');
                    
                    const result = await window.authManager.refreshToken();
                    
                    if (!result.success) {
                        return { success: false, error: result.error };
                    }
                    
                    const newToken = localStorage.getItem('admin_token');
                    
                    if (oldToken === newToken) {
                        return { success: false, error: '令牌未更新' };
                    }
                    
                    return { success: true, message: '令牌刷新成功' };
                });
            }

            async testLogout() {
                return await this.runTest('test-logout', 'TC005: 登出功能测试', async () => {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    await window.authManager.logout();
                    
                    // 验证登出状态
                    if (window.authManager.isLoggedIn()) {
                        return { success: false, error: '登出后仍显示已登录' };
                    }
                    
                    // 验证令牌已清除
                    const token = localStorage.getItem('admin_token');
                    if (token) {
                        return { success: false, error: '令牌未清除' };
                    }
                    
                    return { success: true, message: '登出成功，状态已清除' };
                });
            }

            // API系统测试
            async testCreateCategory() {
                return await this.runTest('test-create-category', 'TC101: 创建分类事务测试', async () => {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    const testCategory = {
                        name: `测试分类_${Date.now()}`,
                        icon: 'test-icon',
                        description: '这是一个测试分类',
                        sort: 0
                    };
                    
                    const result = await window.apiManager.createCategory(testCategory);
                    
                    if (!result.success) {
                        return { success: false, error: result.error };
                    }
                    
                    if (!result.data.id) {
                        return { success: false, error: '创建结果缺少ID' };
                    }
                    
                    return { success: true, message: `分类创建成功，ID: ${result.data.id}` };
                });
            }

            // 实时同步测试
            async testRealtimeConnection() {
                return await this.runTest('test-realtime-connection', 'TC201: 实时监听连接测试', async () => {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    await window.realTimeManager.initWatchers();
                    
                    // 等待连接建立
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    const status = window.realTimeManager.getConnectionStatus();
                    
                    if (!status.isConnected) {
                        return { success: false, error: '实时监听连接失败' };
                    }
                    
                    return { success: true, message: '实时监听连接成功' };
                });
            }

            async runAuthTests() {
                if (this.isRunning) return;
                this.isRunning = true;
                
                this.log('开始JWT认证系统测试', 'info');
                this.results = { total: 5, passed: 0, failed: 0, running: 0 };
                this.updateSummary();
                
                await this.testLoginSuccess();
                await this.testLoginFailure();
                await this.testTokenValidation();
                await this.testTokenRefresh();
                await this.testLogout();
                
                this.log(`JWT认证系统测试完成 - 通过: ${this.results.passed}, 失败: ${this.results.failed}`, 'info');
                this.isRunning = false;
            }

            async runAPITests() {
                if (this.isRunning) return;
                this.isRunning = true;
                
                this.log('开始API系统测试', 'info');
                this.results = { total: 1, passed: 0, failed: 0, running: 0 };
                this.updateSummary();
                
                await this.testCreateCategory();
                
                this.log(`API系统测试完成 - 通过: ${this.results.passed}, 失败: ${this.results.failed}`, 'info');
                this.isRunning = false;
            }

            async runRealtimeTests() {
                if (this.isRunning) return;
                this.isRunning = true;
                
                this.log('开始实时同步测试', 'info');
                this.results = { total: 1, passed: 0, failed: 0, running: 0 };
                this.updateSummary();
                
                await this.testRealtimeConnection();
                
                this.log(`实时同步测试完成 - 通过: ${this.results.passed}, 失败: ${this.results.failed}`, 'info');
                this.isRunning = false;
            }

            async runAllTests() {
                if (this.isRunning) return;
                
                this.log('开始运行所有测试', 'info');
                
                await this.runAuthTests();
                await this.runAPITests();
                await this.runRealtimeTests();
                
                this.log('所有测试完成', 'success');
            }

            clearResults() {
                // 重置所有测试状态
                const testCases = document.querySelectorAll('.test-case');
                testCases.forEach(testCase => {
                    testCase.classList.remove('running', 'passed', 'failed');
                    const indicator = testCase.querySelector('.status-indicator');
                    const result = testCase.querySelector('.test-case-result');
                    
                    indicator.className = 'status-indicator status-pending';
                    result.textContent = '等待执行...';
                });
                
                // 重置统计
                this.results = { total: 0, passed: 0, failed: 0, running: 0 };
                this.updateSummary();
                
                // 清空日志
                this.logElement.textContent = '等待开始测试...\n';
                
                this.log('测试结果已清空', 'info');
            }
        }

        // 全局测试运行器
        window.testRunner = new TestRunner();

        // 全局函数
        function runAllTests() {
            window.testRunner.runAllTests();
        }

        function runAuthTests() {
            window.testRunner.runAuthTests();
        }

        function runAPITests() {
            window.testRunner.runAPITests();
        }

        function runRealtimeTests() {
            window.testRunner.runRealtimeTests();
        }

        function clearResults() {
            window.testRunner.clearResults();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.testRunner.log('测试环境初始化完成', 'success');
        });
    </script>
</body>
</html>
