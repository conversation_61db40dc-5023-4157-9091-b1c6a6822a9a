<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速数据库状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .check-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; }
        .loading { color: #007bff; }
        .count-badge { background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 快速数据库状态检查</h1>
            <p>检查管理后台是否成功保存数据到云数据库</p>
        </div>

        <div class="check-section info">
            <h3>🚀 开始检查</h3>
            <button onclick="startQuickCheck()" id="startBtn">开始快速检查</button>
            <button onclick="clearResults()" id="clearBtn">清除结果</button>
        </div>

        <div class="check-section" id="connectionStatus" style="display: none;">
            <h3>📡 云开发连接状态</h3>
            <div id="connectionResult"></div>
        </div>

        <div class="check-section" id="dataOverview" style="display: none;">
            <h3>📊 数据库概览</h3>
            <div id="overviewResult"></div>
        </div>

        <div class="check-section" id="categoriesData" style="display: none;">
            <h3>📋 分类数据详情</h3>
            <div id="categoriesResult"></div>
        </div>

        <div class="check-section" id="emojisData" style="display: none;">
            <h3>😊 表情包数据详情</h3>
            <div id="emojisResult"></div>
        </div>

        <div class="check-section" id="bannersData" style="display: none;">
            <h3>🎯 轮播图数据详情</h3>
            <div id="bannersResult"></div>
        </div>

        <div class="check-section" id="analysisResult" style="display: none;">
            <h3>🔍 问题分析</h3>
            <div id="analysisContent"></div>
        </div>
    </div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        let app = null;
        let db = null;
        let checkResults = {};

        async function startQuickCheck() {
            const startBtn = document.getElementById('startBtn');
            startBtn.disabled = true;
            startBtn.textContent = '检查中...';

            try {
                await checkCloudConnection();
                await checkDataOverview();
                await checkCategoriesData();
                await checkEmojisData();
                await checkBannersData();
                await analyzeResults();
            } catch (error) {
                console.error('检查过程出错:', error);
            } finally {
                startBtn.disabled = false;
                startBtn.textContent = '重新检查';
            }
        }

        async function checkCloudConnection() {
            const section = document.getElementById('connectionStatus');
            const result = document.getElementById('connectionResult');
            section.style.display = 'block';
            result.innerHTML = '<p class="loading">正在连接云开发...</p>';

            try {
                app = cloudbase.init({ env: 'cloud1-5g6pvnpl88dc0142' });
                await app.auth().signInAnonymously();
                db = app.database();

                // 测试连接
                await db.collection('categories').limit(1).get();

                result.innerHTML = `
                    <div class="success">
                        <h4>✅ 云开发连接成功</h4>
                        <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
                        <p>认证状态: 匿名登录成功</p>
                        <p>数据库连接: 正常</p>
                    </div>
                `;
                checkResults.connection = { success: true };
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ 云开发连接失败</h4>
                        <p>错误信息: ${error.message}</p>
                        <p>请检查网络连接和环境配置</p>
                    </div>
                `;
                checkResults.connection = { success: false, error: error.message };
                throw error;
            }
        }

        async function checkDataOverview() {
            const section = document.getElementById('dataOverview');
            const result = document.getElementById('overviewResult');
            section.style.display = 'block';
            result.innerHTML = '<p class="loading">正在获取数据概览...</p>';

            try {
                const [categoriesResult, emojisResult, bannersResult] = await Promise.all([
                    db.collection('categories').get(),
                    db.collection('emojis').get(),
                    db.collection('banners').get()
                ]);

                const categoriesCount = categoriesResult.data.length;
                const emojisCount = emojisResult.data.length;
                const bannersCount = bannersResult.data.length;

                result.innerHTML = `
                    <div class="success">
                        <h4>📊 数据库概览</h4>
                        <table class="data-table">
                            <tr>
                                <th>数据类型</th>
                                <th>数量</th>
                                <th>状态</th>
                            </tr>
                            <tr>
                                <td>分类 (categories)</td>
                                <td><span class="count-badge">${categoriesCount}</span></td>
                                <td>${categoriesCount > 0 ? '✅ 有数据' : '⚠️ 无数据'}</td>
                            </tr>
                            <tr>
                                <td>表情包 (emojis)</td>
                                <td><span class="count-badge">${emojisCount}</span></td>
                                <td>${emojisCount > 0 ? '✅ 有数据' : '⚠️ 无数据'}</td>
                            </tr>
                            <tr>
                                <td>轮播图 (banners)</td>
                                <td><span class="count-badge">${bannersCount}</span></td>
                                <td>${bannersCount > 0 ? '✅ 有数据' : '⚠️ 无数据'}</td>
                            </tr>
                        </table>
                    </div>
                `;

                checkResults.overview = {
                    categoriesCount,
                    emojisCount,
                    bannersCount,
                    categories: categoriesResult.data,
                    emojis: emojisResult.data,
                    banners: bannersResult.data
                };
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ 数据概览获取失败</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
                checkResults.overview = { success: false, error: error.message };
            }
        }

        async function checkCategoriesData() {
            const section = document.getElementById('categoriesData');
            const result = document.getElementById('categoriesResult');
            section.style.display = 'block';
            result.innerHTML = '<p class="loading">正在分析分类数据...</p>';

            try {
                const categories = checkResults.overview.categories || [];
                
                if (categories.length === 0) {
                    result.innerHTML = `
                        <div class="warning">
                            <h4>⚠️ 没有找到分类数据</h4>
                            <p>这可能是问题的根源之一</p>
                        </div>
                    `;
                    return;
                }

                // 分析分类数据结构
                const sampleCategory = categories[0];
                const hasName = 'name' in sampleCategory;
                const hasStatus = 'status' in sampleCategory;
                const hasSort = 'sort' in sampleCategory;

                let tableHtml = `
                    <div class="success">
                        <h4>✅ 分类数据分析</h4>
                        <p>总数量: <span class="count-badge">${categories.length}</span></p>
                        <p>字段完整性: ${hasName && hasStatus ? '✅ 完整' : '⚠️ 不完整'}</p>
                        
                        <h5>分类列表:</h5>
                        <table class="data-table">
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>图标</th>
                                <th>状态</th>
                                <th>排序</th>
                                <th>创建时间</th>
                            </tr>
                `;

                categories.slice(0, 10).forEach(cat => {
                    const createTime = cat.createTime ? new Date(cat.createTime).toLocaleString() : '未知';
                    tableHtml += `
                        <tr>
                            <td>${cat._id || '无'}</td>
                            <td>${cat.name || '无名称'}</td>
                            <td>${cat.icon || '无'}</td>
                            <td>${cat.status || '未知'}</td>
                            <td>${cat.sort || 0}</td>
                            <td>${createTime}</td>
                        </tr>
                    `;
                });

                tableHtml += `
                        </table>
                        ${categories.length > 10 ? `<p>... 还有 ${categories.length - 10} 条数据</p>` : ''}
                    </div>
                `;

                result.innerHTML = tableHtml;
                checkResults.categoriesAnalysis = { success: true, count: categories.length, hasValidStructure: hasName && hasStatus };
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ 分类数据分析失败</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function checkEmojisData() {
            const section = document.getElementById('emojisData');
            const result = document.getElementById('emojisResult');
            section.style.display = 'block';
            result.innerHTML = '<p class="loading">正在分析表情包数据...</p>';

            try {
                const emojis = checkResults.overview.emojis || [];
                
                if (emojis.length === 0) {
                    result.innerHTML = `
                        <div class="warning">
                            <h4>⚠️ 没有找到表情包数据</h4>
                            <p>这是主要问题！管理后台创建的表情包数据没有保存到数据库</p>
                        </div>
                    `;
                    return;
                }

                // 分析表情包数据结构
                const sampleEmoji = emojis[0];
                const hasTitle = 'title' in sampleEmoji;
                const hasCategory = 'category' in sampleEmoji;
                const hasCategoryId = 'categoryId' in sampleEmoji;
                const hasStatus = 'status' in sampleEmoji;

                // 统计字段使用情况
                const categoryFieldStats = {
                    useCategory: emojis.filter(e => e.category).length,
                    useCategoryId: emojis.filter(e => e.categoryId).length
                };

                let tableHtml = `
                    <div class="success">
                        <h4>✅ 表情包数据分析</h4>
                        <p>总数量: <span class="count-badge">${emojis.length}</span></p>
                        <p>字段完整性: ${hasTitle && (hasCategory || hasCategoryId) ? '✅ 完整' : '⚠️ 不完整'}</p>
                        
                        <h5>字段使用统计:</h5>
                        <table class="data-table">
                            <tr>
                                <th>字段类型</th>
                                <th>使用数量</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>category (分类名称)</td>
                                <td><span class="count-badge">${categoryFieldStats.useCategory}</span></td>
                                <td>${categoryFieldStats.useCategory > 0 ? '管理后台格式' : '未使用'}</td>
                            </tr>
                            <tr>
                                <td>categoryId (分类ID)</td>
                                <td><span class="count-badge">${categoryFieldStats.useCategoryId}</span></td>
                                <td>${categoryFieldStats.useCategoryId > 0 ? '云函数格式' : '未使用'}</td>
                            </tr>
                        </table>
                        
                        <h5>表情包列表 (前10条):</h5>
                        <table class="data-table">
                            <tr>
                                <th>标题</th>
                                <th>分类字段</th>
                                <th>分类ID字段</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                `;

                emojis.slice(0, 10).forEach(emoji => {
                    const createTime = emoji.createTime ? new Date(emoji.createTime).toLocaleString() : '未知';
                    tableHtml += `
                        <tr>
                            <td>${emoji.title || '无标题'}</td>
                            <td>${emoji.category || '无'}</td>
                            <td>${emoji.categoryId || '无'}</td>
                            <td>${emoji.status || '未知'}</td>
                            <td>${createTime}</td>
                        </tr>
                    `;
                });

                tableHtml += `
                        </table>
                        ${emojis.length > 10 ? `<p>... 还有 ${emojis.length - 10} 条数据</p>` : ''}
                    </div>
                `;

                result.innerHTML = tableHtml;
                checkResults.emojisAnalysis = { 
                    success: true, 
                    count: emojis.length, 
                    categoryFieldStats,
                    hasValidStructure: hasTitle && (hasCategory || hasCategoryId)
                };
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ 表情包数据分析失败</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function checkBannersData() {
            const section = document.getElementById('bannersData');
            const result = document.getElementById('bannersResult');
            section.style.display = 'block';
            result.innerHTML = '<p class="loading">正在分析轮播图数据...</p>';

            try {
                const banners = checkResults.overview.banners || [];
                
                if (banners.length === 0) {
                    result.innerHTML = `
                        <div class="warning">
                            <h4>⚠️ 没有找到轮播图数据</h4>
                            <p>管理后台创建的轮播图数据没有保存到数据库</p>
                        </div>
                    `;
                    return;
                }

                let tableHtml = `
                    <div class="success">
                        <h4>✅ 轮播图数据分析</h4>
                        <p>总数量: <span class="count-badge">${banners.length}</span></p>
                        
                        <h5>轮播图列表:</h5>
                        <table class="data-table">
                            <tr>
                                <th>标题</th>
                                <th>副标题</th>
                                <th>状态</th>
                                <th>排序</th>
                                <th>创建时间</th>
                            </tr>
                `;

                banners.slice(0, 10).forEach(banner => {
                    const createTime = banner.createTime ? new Date(banner.createTime).toLocaleString() : '未知';
                    tableHtml += `
                        <tr>
                            <td>${banner.title || '无标题'}</td>
                            <td>${banner.subtitle || '无'}</td>
                            <td>${banner.status || '未知'}</td>
                            <td>${banner.sortOrder || banner.priority || 0}</td>
                            <td>${createTime}</td>
                        </tr>
                    `;
                });

                tableHtml += `
                        </table>
                        ${banners.length > 10 ? `<p>... 还有 ${banners.length - 10} 条数据</p>` : ''}
                    </div>
                `;

                result.innerHTML = tableHtml;
                checkResults.bannersAnalysis = { success: true, count: banners.length };
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ 轮播图数据分析失败</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function analyzeResults() {
            const section = document.getElementById('analysisResult');
            const result = document.getElementById('analysisContent');
            section.style.display = 'block';

            const issues = [];
            const recommendations = [];

            // 分析问题
            if (!checkResults.overview || checkResults.overview.emojisCount === 0) {
                issues.push('❌ 数据库中没有表情包数据');
                recommendations.push('检查管理后台是否真的成功保存了数据');
                recommendations.push('检查管理后台的云开发SDK配置');
            }

            if (!checkResults.overview || checkResults.overview.categoriesCount === 0) {
                issues.push('❌ 数据库中没有分类数据');
                recommendations.push('检查管理后台的分类创建功能');
            }

            if (checkResults.emojisAnalysis && checkResults.emojisAnalysis.categoryFieldStats) {
                const stats = checkResults.emojisAnalysis.categoryFieldStats;
                if (stats.useCategory > 0 && stats.useCategoryId === 0) {
                    issues.push('⚠️ 表情包使用category字段存储分类，但云函数期望categoryId字段');
                    recommendations.push('这是数据同步问题的根本原因');
                    recommendations.push('云函数dataAPI已修复以兼容category字段');
                }
            }

            let analysisHtml = '';
            
            if (issues.length === 0) {
                analysisHtml = `
                    <div class="success">
                        <h4>✅ 数据库状态正常</h4>
                        <p>数据库中有完整的数据，问题可能在其他环节</p>
                        <p>建议检查云函数部署状态和小程序端的数据获取逻辑</p>
                    </div>
                `;
            } else {
                analysisHtml = `
                    <div class="error">
                        <h4>🔍 发现的问题</h4>
                        <ul>
                            ${issues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                        
                        <h4>💡 建议的解决方案</h4>
                        <ol>
                            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ol>
                    </div>
                `;
            }

            // 添加详细数据
            analysisHtml += `
                <div class="info">
                    <h4>📋 完整检查结果</h4>
                    <pre>${JSON.stringify(checkResults, null, 2)}</pre>
                </div>
            `;

            result.innerHTML = analysisHtml;
        }

        function clearResults() {
            const sections = ['connectionStatus', 'dataOverview', 'categoriesData', 'emojisData', 'bannersData', 'analysisResult'];
            sections.forEach(id => {
                document.getElementById(id).style.display = 'none';
            });
            checkResults = {};
        }
    </script>
</body>
</html>
