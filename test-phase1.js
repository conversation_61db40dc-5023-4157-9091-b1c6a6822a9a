// 第一阶段功能测试脚本
// 用于验证核心功能是否正常工作

console.log('🧪 开始第一阶段功能测试...')

// 测试StateManager初始化
function testStateManagerInit() {
  console.log('\n📋 测试1: StateManager初始化')
  
  try {
    const { StateManager } = require('./utils/stateManager.js')
    
    // 检查初始化状态
    const initStatus = StateManager.getInitStatus()
    console.log('✅ StateManager初始化状态:', initStatus)
    
    if (!initStatus.isInitialized) {
      console.log('⚠️ StateManager未初始化，执行初始化...')
      StateManager.init()
    }
    
    // 验证基础方法
    const likedEmojis = StateManager.getLikedEmojis()
    const collectedEmojis = StateManager.getCollectedEmojis()
    
    console.log('✅ 点赞表情包数量:', likedEmojis.length)
    console.log('✅ 收藏表情包数量:', collectedEmojis.length)
    
    return true
  } catch (error) {
    console.error('❌ StateManager测试失败:', error)
    return false
  }
}

// 测试DataManager功能
function testDataManager() {
  console.log('\n📋 测试2: DataManager功能')
  
  try {
    const { DataManager } = require('./utils/newDataManager.js')
    
    // 测试本地测试数据初始化
    const testData = DataManager.initLocalTestData()
    console.log('✅ 本地测试数据:', testData ? testData.length : 0, '个')
    
    // 测试缓存获取
    if (testData && testData.length > 0) {
      const firstEmoji = DataManager.getEmojiDataFromCache(testData[0].id)
      console.log('✅ 缓存数据获取:', firstEmoji ? '成功' : '失败')
    }
    
    return true
  } catch (error) {
    console.error('❌ DataManager测试失败:', error)
    return false
  }
}

// 测试状态操作
function testStateOperations() {
  console.log('\n📋 测试3: 状态操作')
  
  try {
    const { StateManager } = require('./utils/stateManager.js')
    
    const testEmojiId = '1'
    
    // 测试点赞操作
    console.log('🔄 测试点赞操作...')
    const initialLikeState = StateManager.getEmojiState(testEmojiId).isLiked
    const newLikeState = StateManager.toggleLike(testEmojiId)
    const finalLikeState = StateManager.getEmojiState(testEmojiId).isLiked
    
    console.log(`✅ 点赞状态变化: ${initialLikeState} → ${finalLikeState}`)
    
    // 测试收藏操作
    console.log('🔄 测试收藏操作...')
    const initialCollectState = StateManager.getEmojiState(testEmojiId).isCollected
    const newCollectState = StateManager.toggleCollect(testEmojiId)
    const finalCollectState = StateManager.getEmojiState(testEmojiId).isCollected
    
    console.log(`✅ 收藏状态变化: ${initialCollectState} → ${finalCollectState}`)
    
    // 验证数据持久化
    const likedEmojis = StateManager.getLikedEmojis()
    const collectedEmojis = StateManager.getCollectedEmojis()
    
    console.log('✅ 当前点赞列表:', likedEmojis)
    console.log('✅ 当前收藏列表:', collectedEmojis)
    
    return true
  } catch (error) {
    console.error('❌ 状态操作测试失败:', error)
    return false
  }
}

// 测试页面状态混入
function testPageStateMixin() {
  console.log('\n📋 测试4: 页面状态混入')
  
  try {
    const { withPageState, EmojiStateHelper } = require('./utils/pageStateMixin.js')
    
    // 创建测试页面配置
    const testPageConfig = {
      data: { test: true },
      onLoad() {
        console.log('✅ 测试页面onLoad调用成功')
      }
    }
    
    // 应用状态混入
    const enhancedConfig = withPageState(testPageConfig)
    console.log('✅ 页面状态混入应用成功')
    console.log('✅ EmojiStateHelper方法数量:', Object.keys(EmojiStateHelper).length)
    
    return true
  } catch (error) {
    console.error('❌ 页面状态混入测试失败:', error)
    return false
  }
}

// 执行所有测试
async function runAllTests() {
  console.log('🚀 开始执行第一阶段功能测试\n')
  
  const tests = [
    { name: 'StateManager初始化', fn: testStateManagerInit },
    { name: 'DataManager功能', fn: testDataManager },
    { name: '状态操作', fn: testStateOperations },
    { name: '页面状态混入', fn: testPageStateMixin }
  ]
  
  let passedTests = 0
  let totalTests = tests.length
  
  for (const test of tests) {
    try {
      const result = await test.fn()
      if (result) {
        passedTests++
        console.log(`✅ ${test.name} - 通过`)
      } else {
        console.log(`❌ ${test.name} - 失败`)
      }
    } catch (error) {
      console.log(`❌ ${test.name} - 异常:`, error.message)
    }
  }
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`)
  
  if (passedTests === totalTests) {
    console.log('🎉 第一阶段所有测试通过！')
    return true
  } else {
    console.log('⚠️ 部分测试失败，需要修复')
    return false
  }
}

// 如果直接运行此文件
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = {
  runAllTests,
  testStateManagerInit,
  testDataManager,
  testStateOperations,
  testPageStateMixin
}
