/**
 * 设置页面 - 增强版
 */

const UserPreferences = require('../../utils/userPreferences.js')

Page({
  data: {
    userInfo: {},
    cacheSize: '0MB',
    version: '1.0.0',

    // 设置分类
    categories: [
      {
        id: 'display',
        title: '显示设置',
        icon: '🎨',
        items: [
          { key: 'theme', label: '主题模式', type: 'picker', options: [
            { value: 'auto', label: '跟随系统' },
            { value: 'light', label: '浅色模式' },
            { value: 'dark', label: '深色模式' }
          ]},
          { key: 'gridColumns', label: '网格列数', type: 'picker', options: [
            { value: 1, label: '1列' },
            { value: 2, label: '2列' },
            { value: 3, label: '3列' }
          ]},
          { key: 'showPreview', label: '显示预览', type: 'switch' },
          { key: 'animationEnabled', label: '启用动画', type: 'switch' }
        ]
      },
      {
        id: 'download',
        title: '下载设置',
        icon: '📥',
        items: [
          { key: 'autoSave', label: '自动保存到相册', type: 'switch' },
          { key: 'saveFormat', label: '保存格式', type: 'picker', options: [
            { value: 'original', label: '原始格式' },
            { value: 'jpg', label: 'JPG格式' },
            { value: 'png', label: 'PNG格式' }
          ]},
          { key: 'watermarkEnabled', label: '添加水印', type: 'switch' }
        ]
      },
      {
        id: 'notification',
        title: '通知设置',
        icon: '🔔',
        items: [
          { key: 'newContent', label: '新内容通知', type: 'switch' },
          { key: 'updates', label: '更新通知', type: 'switch' },
          { key: 'vibration', label: '震动反馈', type: 'switch' },
          { key: 'sound', label: '声音提醒', type: 'switch' }
        ]
      },
      {
        id: 'performance',
        title: '性能设置',
        icon: '⚡',
        items: [
          { key: 'preloadImages', label: '预加载图片', type: 'switch' },
          { key: 'autoCleanup', label: '自动清理', type: 'switch' },
          { key: 'lowPowerMode', label: '低功耗模式', type: 'switch' }
        ]
      }
    ],

    // 当前设置值
    currentSettings: {},

    // 选择器相关
    pickerVisible: false,
    pickerTitle: '',
    pickerOptions: [],
    pickerValue: '',
    currentPickerPath: ''
  },

  onLoad() {
    console.log('⚙️ 设置页面加载')

    // 初始化用户偏好设置
    UserPreferences.init()

    // 加载用户信息和设置
    this.loadUserInfo()
    this.loadCurrentSettings()
    this.calculateCacheSize()

    // 添加设置变更监听器
    UserPreferences.addListener(this.onSettingChange.bind(this))
  },

  onUnload() {
    // 移除监听器
    UserPreferences.removeListener(this.onSettingChange.bind(this))
  },

  loadUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        this.setData({
          userInfo: userInfo
        })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  },

  /**
   * 加载当前设置
   */
  loadCurrentSettings() {
    const settings = UserPreferences.getAllSettings()
    this.setData({
      currentSettings: settings
    })
    console.log('📱 当前设置加载完成')
  },

  /**
   * 设置变更监听器
   */
  onSettingChange(path, newValue, oldValue) {
    console.log(`⚙️ 设置变更: ${path} = ${newValue}`)

    // 更新页面数据
    this.loadCurrentSettings()

    // 显示变更提示
    wx.showToast({
      title: '设置已保存',
      icon: 'success',
      duration: 1000
    })
  },

  calculateCacheSize() {
    try {
      // 模拟计算缓存大小
      const likedEmojis = wx.getStorageSync('likedEmojis') || []
      const collectedEmojis = wx.getStorageSync('collectedEmojis') || []
      const downloadedEmojis = wx.getStorageSync('downloadedEmojis') || []
      const recentEmojis = wx.getStorageSync('recentEmojis') || []
      
      const totalItems = likedEmojis.length + collectedEmojis.length + downloadedEmojis.length + recentEmojis.length
      const estimatedSize = Math.max(0.1, totalItems * 0.05) // 每项约50KB
      
      this.setData({
        cacheSize: estimatedSize.toFixed(1) + 'MB'
      })
    } catch (error) {
      console.error('计算缓存大小失败:', error)
    }
  },

  // 切换设置项
  onToggleSetting(e) {
    const { setting } = e.currentTarget.dataset
    const currentValue = this.data.settings[setting]
    
    this.setData({
      [`settings.${setting}`]: !currentValue
    })
    
    // 保存到本地存储
    this.saveSettings()
    
    // 特殊处理
    if (setting === 'vibration') {
      if (!currentValue) {
        wx.vibrateShort() // 开启时震动一下
      }
    }
  },

  saveSettings() {
    try {
      wx.setStorageSync('appSettings', this.data.settings)
      console.log('设置已保存:', this.data.settings)
    } catch (error) {
      console.error('保存设置失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  // 清除缓存
  onClearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？这将删除您的浏览记录，但不会影响点赞和收藏。',
      success: (res) => {
        if (res.confirm) {
          try {
            // 只清除浏览记录，保留用户数据
            wx.removeStorageSync('recentEmojis')
            wx.removeStorageSync('searchHistory')
            
            this.calculateCacheSize()
            
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于表情包小程序',
      content: `版本：${this.data.version}\n\n这是一个专注于表情包收集和分享的小程序，让表达更有趣！\n\n如有问题或建议，请联系我们。`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 用户协议
  onUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '感谢您使用表情包小程序！我们致力于为您提供优质的服务体验。使用本小程序即表示您同意我们的用户协议和隐私政策。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 隐私政策
  onPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护。本小程序仅收集必要的使用数据以改善服务质量，不会泄露您的个人信息。所有数据均存储在本地，确保您的隐私安全。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 意见反馈
  onFeedback() {
    wx.showModal({
      title: '意见反馈',
      content: '您的意见对我们很重要！如有任何建议或问题，请通过以下方式联系我们：\n\n微信：emoji_feedback\n邮箱：<EMAIL>',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 检查更新
  onCheckUpdate() {
    wx.showLoading({
      title: '检查中...'
    })
    
    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      })
    }, 1500)
  },

  // 重置所有数据
  onResetAllData() {
    wx.showModal({
      title: '重置数据',
      content: '确定要重置所有数据吗？这将清除您的所有点赞、收藏、下载记录等数据，此操作不可恢复！',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showModal({
            title: '最后确认',
            content: '请再次确认，这将删除所有数据！',
            confirmColor: '#ff4757',
            success: (res2) => {
              if (res2.confirm) {
                try {
                  // 清除所有用户数据
                  wx.removeStorageSync('likedEmojis')
                  wx.removeStorageSync('collectedEmojis')
                  wx.removeStorageSync('downloadedEmojis')
                  wx.removeStorageSync('recentEmojis')
                  wx.removeStorageSync('downloadTimes')
                  wx.removeStorageSync('searchHistory')
                  
                  this.calculateCacheSize()
                  
                  wx.showToast({
                    title: '数据已重置',
                    icon: 'success'
                  })
                } catch (error) {
                  wx.showToast({
                    title: '重置失败',
                    icon: 'error'
                  })
                }
              }
            }
          })
        }
      }
    })
  },

  /**
   * 开关切换
   */
  onSwitchChange(e) {
    const { category, key } = e.currentTarget.dataset
    const value = e.detail.value
    const path = `${category}.${key}`

    UserPreferences.set(path, value)
  },

  /**
   * 选择器点击
   */
  onPickerTap(e) {
    const { category, key, options, label } = e.currentTarget.dataset
    const currentValue = UserPreferences.get(`${category}.${key}`)

    this.setData({
      pickerVisible: true,
      pickerTitle: label,
      pickerOptions: options,
      pickerValue: currentValue,
      currentPickerPath: `${category}.${key}`
    })
  },

  /**
   * 选择器选择
   */
  onPickerSelect(e) {
    const { value } = e.currentTarget.dataset
    const path = this.data.currentPickerPath

    UserPreferences.set(path, value)

    this.setData({
      pickerVisible: false
    })
  },

  /**
   * 关闭选择器
   */
  onPickerClose() {
    this.setData({
      pickerVisible: false
    })
  },

  /**
   * 获取设置值的显示文本
   */
  getSettingDisplayValue(category, key, item) {
    const value = this.data.currentSettings[category]?.[key]

    if (item.type === 'picker' && item.options) {
      const option = item.options.find(opt => opt.value === value)
      return option ? option.label : value
    }

    return value
  },

  /**
   * 获取设置值
   */
  getSettingValue(category, key) {
    return this.data.currentSettings[category]?.[key]
  }
})
