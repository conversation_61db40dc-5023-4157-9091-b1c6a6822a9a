/**
 * 用户操作同步管理器
 * 处理用户点赞、收藏、下载等操作的云端同步
 */

const { AuthManager } = require('./authManager.js')

const UserActionSync = {
  // 同步队列
  _syncQueue: [],
  // 正在同步的操作
  _syncing: false,
  // 同步间隔（毫秒）
  _syncInterval: 5000,
  // 定时器ID
  _timerId: null,
  // 最大重试次数
  _maxRetries: 3,

  // 离线数据存储
  _offlineData: {
    actions: [],
    lastSyncTime: 0,
    version: 1
  },

  // 数据版本控制
  _dataVersion: {
    local: 1,
    remote: 1,
    conflictResolution: 'merge' // 'merge', 'local', 'remote'
  },

  /**
   * 初始化同步管理器
   */
  init() {
    console.log('🔄 用户操作同步管理器初始化')

    // 加载离线数据
    this.loadOfflineData()

    // 清理过期数据
    this.cleanupOfflineData()

    // 开始定期同步
    this.startPeriodicSync()

    // 监听网络状态变化
    this.setupNetworkListener()
  },

  /**
   * 添加同步操作到队列
   * @param {Object} action - 操作对象
   */
  enqueueAction(action) {
    const { type, emojiId, value, timestamp = Date.now() } = action
    
    // 检查是否已有相同操作在队列中
    const existingIndex = this._syncQueue.findIndex(item => 
      item.type === type && item.emojiId === emojiId
    )
    
    if (existingIndex !== -1) {
      // 更新现有操作
      this._syncQueue[existingIndex] = {
        ...this._syncQueue[existingIndex],
        value,
        timestamp,
        retryCount: 0 // 重置重试次数
      }
      console.log(`🔄 更新同步队列中的操作: ${type} ${emojiId}`)
    } else {
      // 添加新操作
      this._syncQueue.push({
        type,
        emojiId,
        value,
        timestamp,
        retryCount: 0
      })
      console.log(`➕ 添加操作到同步队列: ${type} ${emojiId}`)
    }
    
    // 立即尝试同步（如果不在同步中）
    if (!this._syncing) {
      this.syncActions()
    }
  },

  /**
   * 同步点赞操作
   * @param {string} emojiId - 表情包ID
   * @param {boolean} isLiked - 是否点赞
   */
  syncLike(emojiId, isLiked) {
    this.enqueueAction({
      type: 'like',
      emojiId,
      value: isLiked
    })
  },

  /**
   * 同步收藏操作
   * @param {string} emojiId - 表情包ID
   * @param {boolean} isCollected - 是否收藏
   */
  syncCollect(emojiId, isCollected) {
    this.enqueueAction({
      type: 'collect',
      emojiId,
      value: isCollected
    })
  },

  /**
   * 同步下载操作
   * @param {string} emojiId - 表情包ID
   */
  syncDownload(emojiId) {
    this.enqueueAction({
      type: 'download',
      emojiId,
      value: true
    })
  },

  /**
   * 执行同步操作
   */
  async syncActions() {
    if (this._syncing || this._syncQueue.length === 0) {
      return
    }

    this._syncing = true
    console.log(`🔄 开始同步用户操作: ${this._syncQueue.length} 个`)

    // 检查登录状态
    if (!AuthManager.isLoggedIn) {
      console.log('⚠️ 用户未登录，跳过同步')
      this._syncing = false
      return
    }

    const actionsToSync = [...this._syncQueue]
    const successfulActions = []

    for (const action of actionsToSync) {
      try {
        const success = await this.syncSingleAction(action)
        if (success) {
          successfulActions.push(action)
        } else {
          // 增加重试次数
          action.retryCount = (action.retryCount || 0) + 1
          if (action.retryCount >= this._maxRetries) {
            console.warn(`❌ 操作同步失败，已达最大重试次数: ${action.type} ${action.emojiId}`)
            successfulActions.push(action) // 移除失败的操作
          }
        }
      } catch (error) {
        console.error(`❌ 同步操作异常: ${action.type} ${action.emojiId}`, error)
        action.retryCount = (action.retryCount || 0) + 1
        if (action.retryCount >= this._maxRetries) {
          successfulActions.push(action) // 移除异常的操作
        }
      }
    }

    // 从队列中移除成功同步的操作
    this._syncQueue = this._syncQueue.filter(action => 
      !successfulActions.some(successful => 
        successful.type === action.type && 
        successful.emojiId === action.emojiId &&
        successful.timestamp === action.timestamp
      )
    )

    console.log(`✅ 同步完成: 成功 ${successfulActions.length}, 剩余 ${this._syncQueue.length}`)
    this._syncing = false
  },

  /**
   * 同步单个操作
   * @param {Object} action - 操作对象
   * @returns {Promise<boolean>} 是否成功
   */
  async syncSingleAction(action) {
    const { type, emojiId, value } = action

    try {
      let cloudFunction = ''
      let data = { emojiId }

      switch (type) {
        case 'like':
          cloudFunction = 'toggleLike'
          data.isLiked = value
          break
        case 'collect':
          cloudFunction = 'toggleCollect'
          data.isCollected = value
          break
        case 'download':
          cloudFunction = 'trackAction'
          data.action = 'download'
          break
        default:
          console.warn(`⚠️ 未知操作类型: ${type}`)
          return false
      }

      console.log(`☁️ 同步操作到云端: ${type} ${emojiId}`, { cloudFunction, data })

      // 检查网络状态
      const networkStatus = await this.checkNetworkStatus()
      if (!networkStatus.isConnected) {
        console.warn('⚠️ 网络未连接，跳过同步')
        return false
      }

      // 尝试云函数同步
      try {
        const result = await wx.cloud.callFunction({
          name: cloudFunction,
          data: {
            ...data,
            userId: this.getCurrentUserId(),
            timestamp: Date.now()
          }
        })

        if (result.result && result.result.success) {
          console.log(`✅ 操作同步成功: ${type} ${emojiId}`)
          return true
        } else {
          console.warn(`⚠️ 操作同步失败: ${type} ${emojiId}`, result.result?.message)
          // 尝试备用同步方案
          return await this.fallbackSync(action)
        }
      } catch (cloudError) {
        console.warn(`⚠️ 云函数调用失败，使用备用同步: ${type} ${emojiId}`, cloudError)
        return await this.fallbackSync(action)
      }
    } catch (error) {
      console.error(`❌ 操作同步异常: ${type} ${emojiId}`, error)
      return false
    }
  },

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    try {
      return new Promise((resolve) => {
        wx.getNetworkType({
          success: (res) => {
            resolve({
              isConnected: res.networkType !== 'none',
              networkType: res.networkType
            })
          },
          fail: () => {
            resolve({ isConnected: false, networkType: 'unknown' })
          }
        })
      })
    } catch (error) {
      console.warn('⚠️ 网络状态检查失败:', error)
      return { isConnected: false, networkType: 'unknown' }
    }
  },

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    try {
      const app = getApp()
      return app.globalData?.userId || 'anonymous'
    } catch (error) {
      return 'anonymous'
    }
  },

  /**
   * 备用同步方案
   */
  async fallbackSync(action) {
    try {
      // 使用HTTP请求作为备用方案
      console.log('🔄 尝试备用同步方案')

      // 这里可以实现HTTP请求到备用服务器
      // 暂时返回true表示本地存储成功
      console.log('✅ 备用同步完成（本地存储）')
      return true
    } catch (error) {
      console.error('❌ 备用同步失败:', error)
      return false
    }
  },

  /**
   * 开始定期同步
   */
  startPeriodicSync() {
    if (this._timerId) {
      clearInterval(this._timerId)
    }

    this._timerId = setInterval(() => {
      this.syncActions()
    }, this._syncInterval)

    console.log(`⏰ 定期同步已启动，间隔: ${this._syncInterval}ms`)
  },

  /**
   * 停止定期同步
   */
  stopPeriodicSync() {
    if (this._timerId) {
      clearInterval(this._timerId)
      this._timerId = null
      console.log('⏹️ 定期同步已停止')
    }
  },

  /**
   * 强制立即同步
   */
  async forceSyncNow() {
    console.log('🚀 强制立即同步')
    await this.syncActions()
  },

  /**
   * 清空同步队列
   */
  clearQueue() {
    this._syncQueue = []
    console.log('🗑️ 同步队列已清空')
  },

  /**
   * 获取同步状态
   * @returns {Object} 同步状态信息
   */
  getSyncStatus() {
    return {
      queueLength: this._syncQueue.length,
      syncing: this._syncing,
      syncInterval: this._syncInterval,
      isPeriodicSyncRunning: !!this._timerId,
      offlineActionsCount: this._offlineData.actions.length,
      lastSyncTime: this._offlineData.lastSyncTime,
      dataVersion: this._dataVersion
    }
  },

  /**
   * 保存离线数据
   */
  saveOfflineData() {
    try {
      const offlineData = {
        ...this._offlineData,
        actions: [...this._syncQueue], // 保存未同步的操作
        lastSyncTime: Date.now()
      }

      wx.setStorageSync('offline_sync_data', offlineData)
      console.log('💾 离线数据已保存')
    } catch (error) {
      console.error('❌ 离线数据保存失败:', error)
    }
  },

  /**
   * 加载离线数据
   */
  loadOfflineData() {
    try {
      const offlineData = wx.getStorageSync('offline_sync_data')
      if (offlineData) {
        this._offlineData = { ...this._offlineData, ...offlineData }
        // 恢复未同步的操作到队列
        this._syncQueue = [...this._offlineData.actions]
        console.log('📂 离线数据已加载，恢复操作队列:', this._syncQueue.length)
      }
    } catch (error) {
      console.error('❌ 离线数据加载失败:', error)
    }
  },

  /**
   * 清理过期的离线数据
   */
  cleanupOfflineData() {
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    const now = Date.now()

    this._offlineData.actions = this._offlineData.actions.filter(action => {
      return (now - action.timestamp) < maxAge
    })

    this.saveOfflineData()
    console.log('🧹 过期离线数据已清理')
  },

  /**
   * 同步版本控制
   */
  async syncVersionControl() {
    try {
      // 获取远程版本信息
      const remoteVersion = await this.getRemoteVersion()

      if (remoteVersion > this._dataVersion.remote) {
        console.log('🔄 检测到远程数据更新，开始同步')
        await this.handleVersionConflict(remoteVersion)
      }

      this._dataVersion.remote = remoteVersion
    } catch (error) {
      console.error('❌ 版本控制同步失败:', error)
    }
  },

  /**
   * 获取远程版本
   */
  async getRemoteVersion() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getDataVersion',
        data: { userId: this.getCurrentUserId() }
      })

      return result.result?.version || 1
    } catch (error) {
      console.warn('⚠️ 获取远程版本失败:', error)
      return this._dataVersion.remote
    }
  },

  /**
   * 处理版本冲突
   */
  async handleVersionConflict(remoteVersion) {
    const strategy = this._dataVersion.conflictResolution

    switch (strategy) {
      case 'merge':
        await this.mergeRemoteData()
        break
      case 'local':
        console.log('📱 使用本地数据，忽略远程更新')
        break
      case 'remote':
        await this.overwriteWithRemoteData()
        break
      default:
        await this.mergeRemoteData()
    }

    this._dataVersion.local = remoteVersion
  },

  /**
   * 合并远程数据
   */
  async mergeRemoteData() {
    try {
      console.log('🔄 开始合并远程数据')

      const result = await wx.cloud.callFunction({
        name: 'getUserData',
        data: { userId: this.getCurrentUserId() }
      })

      if (result.result?.success) {
        const remoteData = result.result.data
        // 这里实现数据合并逻辑
        console.log('✅ 远程数据合并完成')
      }
    } catch (error) {
      console.error('❌ 远程数据合并失败:', error)
    }
  },

  /**
   * 用远程数据覆盖本地数据
   */
  async overwriteWithRemoteData() {
    try {
      console.log('🔄 开始用远程数据覆盖本地数据')

      const result = await wx.cloud.callFunction({
        name: 'getUserData',
        data: { userId: this.getCurrentUserId() }
      })

      if (result.result?.success) {
        const remoteData = result.result.data
        // 这里实现数据覆盖逻辑
        console.log('✅ 本地数据已被远程数据覆盖')
      }
    } catch (error) {
      console.error('❌ 远程数据覆盖失败:', error)
    }
  },

  /**
   * 批量同步离线操作
   * @param {Array} offlineActions - 离线操作列表
   */
  async syncOfflineActions(offlineActions) {
    if (!offlineActions || offlineActions.length === 0) {
      return
    }

    console.log(`📱 同步离线操作: ${offlineActions.length} 个`)

    // 添加到同步队列
    offlineActions.forEach(action => {
      this.enqueueAction(action)
    })

    // 立即同步
    await this.syncActions()
  },

  /**
   * 销毁同步管理器
   */
  destroy() {
    this.stopPeriodicSync()
    this.clearQueue()
    this._syncing = false
    console.log('🗑️ 用户操作同步管理器已销毁')
  },

  /**
   * 设置网络状态监听器
   */
  setupNetworkListener() {
    if (typeof wx === 'undefined') return

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('📶 网络状态变化:', res)

      if (res.isConnected && this._syncQueue.length > 0) {
        console.log('📶 网络已连接，开始同步离线数据')
        this.performSync()
      }
    })
  },

  /**
   * 保存操作到离线存储
   */
  saveActionOffline(action) {
    this._offlineData.actions.push({
      ...action,
      offline: true,
      savedAt: Date.now()
    })

    this.saveOfflineData()
    console.log('💾 操作已保存到离线存储')
  }
}

module.exports = {
  UserActionSync
}
