<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .emoji-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            background: #fafafa;
        }
        .emoji-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .emoji-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }
        .emoji-id {
            font-size: 12px;
            color: #666;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .button {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 表情包显示测试工具</h1>
        
        <div class="test-section">
            <div class="test-title">📋 测试控制</div>
            <button class="button" onclick="testCloudFunction()">测试云函数调用</button>
            <button class="button" onclick="testDataDisplay()">测试数据显示</button>
            <button class="button" onclick="clearLogs()">清空日志</button>
            <div id="status"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 返回数据</div>
            <div id="dataDisplay"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🖼️ 表情包展示</div>
            <div id="emojiDisplay" class="emoji-grid"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 调试日志</div>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // 模拟微信云开发环境
        const mockWx = {
            cloud: {
                callFunction: async (options) => {
                    log(`🔄 模拟调用云函数: ${options.name}`);
                    log(`📤 请求参数: ${JSON.stringify(options.data, null, 2)}`);
                    
                    // 模拟返回数据（基于你提供的云函数日志）
                    const mockResult = {
                        result: {
                            success: true,
                            data: [
                                {
                                    "_id": "77cd31ad6889db6b006d62eb03fb77bb",
                                    "title": "123",
                                    "imageUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAAdiAAAHYgE4epnbAAAFf2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSfvu78nIGlkPSdXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQnPz4KPHg6eG1wbWV0YSB4bWxuczp4PSdhZG9iZTpuczptZXRhLyc+CjxyZGY6UkRGIHhtbG5zOnJkZj0naHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyc+CgogPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9JycKICB4bWxuczpBdHRyaWI9J2h0dHA6Ly9ucy5hdHRyaWJ1dGlvbi5jb20vYWRzLzEuMC8nPgogIDxBdHRyaWI6QWRzPgogICA8cmRmOlNlcT4KICAgIDxyZGY6bGkgcmRmOnBhcnNlVHlwZT0nUmVzb3VyY2UnPgogICAgIDxBdHRyaWI6Q3JlYXRlZD4yMDI1LTA2LTMwPC9BdHRyaWI6Q3JlYXRlZD4KICAgICA8QXR0cmliOkV4dElkPjAwMjU5Y2Q0LTliMjUtNDA1OC05ZGM3LTQ4ZGMzZTQzMGI3MzwvQXR0cmliOkV4dElkPgogICAgIDxBdHRyaWI6RmJJZD41MjUyNjU5MTQxNzk1ODA8L0F0dHJpYjpGYklkPgogICAgIDxBdHRyaWI6VG91Y2hUeXBlPjI8L0F0dHJpYjpUb3VjaFR5cGU+CiAgICA8L3JkZjpsaT4KICAgPC9yZGY6U2VxPgogIDwvQXR0cmliOkFkcz4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6Q29udGFpbnNBaUdlbmVyYXRlZENvbnRlbnQ9J2h0dHBzOi8vY2FudmEuY29tL2V4cG9ydCc+CiAgPENvbnRhaW5zQWlHZW5lcmF0ZWRDb250ZW50OkNvbnRhaW5zQWlHZW5lcmF0ZWRDb250ZW50PlllczwvQ29udGFpbnNBaUdlbmVyYXRlZENvbnRlbnQ6Q29udGFpbnNBaUdlbmVyYXRlZENvbnRlbnQ+CiA8L3JkZjpEZXNjcmlwdGlvbj4KCiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0nJwogIHhtbG5zOmRjPSdodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyc+CiAgPGRjOnRpdGxlPgogICA8cmRmOkFsdD4KICAgIDxyZGY6bGkgeG1sOmxhbmc9J3gtZGVmYXVsdCc+5YiS5rC0IC0gMTwvcmRmOmxpPgogICA8L3JkZjpBbHQ+CiAgPC9kYzp0aXRsZT4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6cGRmPSdodHRwOi8vbnMuYWRvYmUuY29tL3BkZi8xLjMvJz4KICA8cGRmOkF1dGhvcj5NSUtLTzwvcGRmOkF1dGhvcj4KIDwvcmRmOkRlc2NyaXB0aW9uPgoKIDxyZGY6RGVzY3JpcHRpb24gcmRmOmFib3V0PScnCiAgeG1sbnM6eG1wPSdodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvJz4KICA8eG1wOkNyZWF0b3JUb29sPkNhbnZhIGRvYz1EQUdyMEItNWd1YyB1c2VyPVVBR0g0bjJaNTJZIGJyYW5kPUNPIHRlbXBsYXRlPTwveG1wOkNyZWF0b3JUb29sPgogPC9yZGY6RGVzY3JpcHRpb24+CjwvcmRmOlJERj4KPC94OnhtcG1ldGE+Cjw/eHBhY2tldCBlbmQ9J3InPz6nQx2SAAAgAElEQVR4nOx9B3xb5dV+/nxAoaXQ3dJCgdLSAS0t0JZSWmi/ry2FtpC9d+LElrwtS7ZsRx4JYRRoS5llxA4rrDIzbMcrdjzkIclatlOchBmSeEi673tlS/f/nndcXZk4g8QWSe/5/d7fvZKupCv7Ss9zznnOOVOm6Kabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666aabbrrppptuuummm2666abb8Vp4z7Zr5D3Vc+X+ygJ5d9UTeHdVPd5d+Q7ZV45p9VcOkudtJfvFI/3Vv1X2Np2d6M+mm2666aabbroRG3m76jfy7mozAeun5f6qrmMG+WMmBVXPjOyuvD7Rn1s33XTTTTfdThnDeyozCcC+TID22XB/1fLDHUsAfx7ur9w94YA/PhHoGNm7/YbJ+tvopptuuumm2ylnyrv2Tx/Ke4fw+6GOH+mv+kXCgH/MCu+pSpnsv5duuummm266nRIm76ksHQ9gISow9vjw7qpVxwDSEu6v3Id3V+0CkkH1AP2VtbDIY41kBU4AEXhW1wfopptuuumm2zEaAWbfuASAAPXY43nE4DHy+H68u3IHBfTdlVsJuD9E9k14d/Wf5d3bLz/a98d7t31H3l09M9xfdTd5zdaPRwIqN5zQP4puuummm266nepGwHz+eMA6srfqpsk+H+Xd6ovIe+cRQvHhsaUDqpdM9rnqpptuuumm20ltULanBVzi0e8Z2VP9+0Sek/JWzVl4T1UWISjDRxkFCCnv1F2YyHPWTTfddNPtOAxtSPtm6KnkqwMVxhthSRsMv5CeSdF/2CfBwm9vv1J+t+Z7iT4PrSl7q79ByMm2o4wE3Jno89VNN9100+0oLLjRsDhYbqwOlBv7yDYYrDAqh1uBCsNBOJYQg53k9pvBCsPGYLnhb6FyQ1GgIsUYrEiZS17nD6Fy4zWo3HiJsinpvER/Rt1OjIHiH0SFRyAA7Yk+T91000033Q5joY2GPxHgfvtIgH+iFiEN+8jyBsoNDeR9XyXrCUIe/hqqMOSHKlJWkcdmkvXbYEXqlRBtUF5L+nSi/0a6fdRG9lbeeDgCgPsr/5Poc9RNN910020cI4B832QB/wlY75DlJOSglqwXyXqEkId1hDyYQuXG5aEK422oIvVXgacNlwefSPlaov+2E2kgzgvv3n71yJ7KP4BwEO+uSpf3VJWA+p+A7wu4v2o7NOght/ug9O9I4XooDcS7K6vI8/5FthlK/9ZLjuY8QOx3GB3AKxP9d9BNN9100+1jWGCjMeXwIX7jTg626gqWG11ke+ATQAaOLtpQYQgQgtBPzruDrEqyniX3PRDYaCgl+5nBitRFoYrUW0DbgDcYLlM2GL442f8Hpb/+fHlv5Q95K9+Z4T2VydC7nwDx36GtL825UzCv3HMUYfcTt/orPXAeyvtbP3O48yfnl3aI5w6F+yt/Mll/Q91000033Y7SILQ+DmC+ENpguPVowu7Bp9O/GtiY+gO0IeV6eA55/lKycsArJ6/zEFnPE/CtIvd1kfv2kBVKNCE4BuJwaH1DhXGNqm+oMPxurL5B2bvlC/id7d8d2b39l/KeyluhjS/tz7+n8i4CjE8Sr/h1sm0EzxwG7EwamB/Hwv1V74V3Vy883LWA3666DPoOkM9nI5/ZoLxXc/EJulR10y1xpii208SWrSn/L9HnpJtux2vE+719LOihpwy/noz3DlUknR8oX30FvJ+00TAVwvfkfHIpcSg3PgLhfXI+NRDup9qEcgNKNCE46vVMlhJ8waIEXylSQm+UKaFtdypSzd8UtONBBbc8puCOCkV2Pqdg78uK3PdGwsH9GNemybg+dNNt0kyZMuX/1dTceLpSYztdvQ+AftPM/1HsSWcoTsvnBzozPid1Wb8hOX..."
                                },
                                {
                                    "_id": "test2",
                                    "title": "测试表情2",
                                    "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZkNzAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIo8L3RleHQ+Cjwvc3ZnPgo="
                                },
                                {
                                    "_id": "test3",
                                    "title": "测试表情3",
                                    "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBiY2ZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmI08L3RleHQ+Cjwvc3ZnPgo="
                                }
                            ]
                        }
                    };
                    
                    log(`📥 返回结果: ${JSON.stringify(mockResult.result, null, 2)}`);
                    return mockResult;
                }
            }
        };

        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        // 设置状态
        function setStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logArea').textContent = '';
            document.getElementById('dataDisplay').innerHTML = '';
            document.getElementById('emojiDisplay').innerHTML = '';
            document.getElementById('status').innerHTML = '';
        }

        // 测试云函数调用
        async function testCloudFunction() {
            try {
                setStatus('🔄 正在测试云函数调用...', 'loading');
                log('🧪 开始测试云函数调用');
                
                const result = await mockWx.cloud.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getEmojis',
                        data: {
                            category: 'all',
                            page: 1,
                            limit: 20
                        }
                    }
                });

                if (result.result && result.result.success) {
                    setStatus(`✅ 云函数调用成功，获取到 ${result.result.data.length} 条数据`, 'success');
                    
                    // 显示原始数据
                    const dataDisplay = document.getElementById('dataDisplay');
                    dataDisplay.innerHTML = `<pre>${JSON.stringify(result.result, null, 2)}</pre>`;
                    
                    log(`✅ 测试成功，数据条数: ${result.result.data.length}`);
                    return result.result.data;
                } else {
                    throw new Error('云函数返回失败');
                }
            } catch (error) {
                setStatus(`❌ 云函数调用失败: ${error.message}`, 'error');
                log(`❌ 测试失败: ${error.message}`);
                return null;
            }
        }

        // 测试数据显示
        async function testDataDisplay() {
            try {
                setStatus('🔄 正在测试数据显示...', 'loading');
                log('🎨 开始测试数据显示');
                
                const data = await testCloudFunction();
                if (!data) {
                    throw new Error('没有获取到数据');
                }

                // 渲染表情包
                const emojiDisplay = document.getElementById('emojiDisplay');
                emojiDisplay.innerHTML = '';

                data.forEach((emoji, index) => {
                    const emojiItem = document.createElement('div');
                    emojiItem.className = 'emoji-item';
                    emojiItem.innerHTML = `
                        <img class="emoji-image" src="${emoji.imageUrl}" alt="${emoji.title}" 
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD4KPC9zdmc+Cg=='" />
                        <div class="emoji-title">${emoji.title}</div>
                        <div class="emoji-id">ID: ${emoji._id}</div>
                    `;
                    emojiDisplay.appendChild(emojiItem);
                    log(`🖼️ 渲染表情包 ${index + 1}: ${emoji.title}`);
                });

                setStatus(`✅ 数据显示成功，渲染了 ${data.length} 个表情包`, 'success');
                log(`✅ 数据显示测试完成`);
                
            } catch (error) {
                setStatus(`❌ 数据显示失败: ${error.message}`, 'error');
                log(`❌ 数据显示测试失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动运行测试
        window.onload = function() {
            log('🚀 页面加载完成，开始初始化测试');
            setStatus('📋 准备就绪，点击按钮开始测试', 'success');
        };
    </script>
</body>
</html>
