@echo off
title 🚀 表情包小程序一键部署工具
color 0A
echo.
echo =========================================
echo 🎯 表情包小程序一键部署工具
echo =========================================
echo.
echo 📝 当前云环境: cloud1-5g6pvnpl88dc0142
echo.

REM 创建部署日志
echo [%date% %time%] 开始部署 > deploy.log

REM 步骤1: 检查文件完整性
echo 🔍 步骤1: 检查文件完整性...
if exist "config\environment.js" (
    echo ✅ 环境配置文件已存在
) else (
    echo ❌ 环境配置文件缺失
    pause
    exit /b
)

if exist "cloudfunctions\dataAPI\index.js" (
    echo ✅ dataAPI云函数已存在
) else (
    echo ❌ dataAPI云函数缺失
    pause
    exit /b
)

if exist "cloudfunctions\adminAPI\index.js" (
    echo ✅ adminAPI云函数已存在
) else (
    echo ❌ adminAPI云函数缺失
    pause
    exit /b
)

echo.

REM 步骤2: 环境配置确认
echo ⚙️ 步骤2: 环境配置确认...
echo cloud1-5g6pvnpl88dc0142 > current_env.txt
echo ✅ 云环境ID已配置: cloud1-5g6pvnpl88dc0142

echo.

REM 步骤3: 创建部署说明
echo 📋 步骤3: 创建部署说明...
echo.
echo 🎯 部署完成！请按以下步骤操作：
echo.
echo 1️⃣ 打开微信开发者工具
echo 2️⃣ 导入本项目文件夹
echo 3️⃣ 点击左侧