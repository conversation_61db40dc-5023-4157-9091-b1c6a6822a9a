<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 云函数连接测试工具</h1>
    
    <div class="test-card">
        <h3>📋 测试说明</h3>
        <p>这个工具用于测试改造后的管理后台是否能正确调用云函数。</p>
        <div class="status info">
            <strong>测试目标：</strong>验证管理后台的数据操作是否通过云函数进行，而不是直接使用本地存储。
        </div>
    </div>
    
    <div class="test-card">
        <h3>🔧 测试操作</h3>
        <button class="btn btn-primary" onclick="testGetCategories()">测试获取分类</button>
        <button class="btn btn-primary" onclick="testGetEmojis()">测试获取表情包</button>
        <button class="btn btn-success" onclick="testAddCategory()">测试添加分类</button>
        <button class="btn btn-warning" onclick="testUpdateCategory()">测试更新分类</button>
        <button class="btn btn-warning" onclick="clearLogs()">清空日志</button>
    </div>
    
    <div class="test-card">
        <h3>📊 测试结果</h3>
        <div class="log-area" id="testLogs">等待测试开始...</div>
    </div>
    
    <div class="test-card">
        <h3>✅ 验证要点</h3>
        <ul>
            <li><strong>云函数调用：</strong>日志中应该显示 "☁️ 调用云函数" 而不是 "📦 本地存储"</li>
            <li><strong>数据同步：</strong>操作后数据应该能在小程序中看到变化</li>
            <li><strong>降级机制：</strong>如果云函数失败，应该自动降级到本地存储</li>
        </ul>
    </div>

    <script>
        function log(message, type = 'info') {
            const logArea = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️'
            }[type] || 'ℹ️';
            
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('testLogs').textContent = '日志已清空，等待新的测试...\n';
        }

        async function testGetCategories() {
            log('开始测试获取分类数据...');
            
            try {
                const response = await fetch('http://localhost:8001/api/cloud-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        functionName: 'dataAPI',
                        data: {
                            action: 'getCategories',
                            collection: 'categories'
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 云函数调用成功！获取到 ${result.result.data.length} 个分类`, 'success');
                    log(`分类数据: ${JSON.stringify(result.result.data, null, 2)}`);
                } else {
                    log(`❌ 云函数调用失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                log('请确保管理后台服务器正在运行 (http://localhost:8001)', 'warning');
            }
        }

        async function testGetEmojis() {
            log('开始测试获取表情包数据...');
            
            try {
                const response = await fetch('http://localhost:8001/api/cloud-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        functionName: 'dataAPI',
                        data: {
                            action: 'getEmojis',
                            collection: 'emojis'
                        }
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 云函数调用成功！获取到 ${result.result.data.length} 个表情包`, 'success');
                    log(`表情包数据: ${JSON.stringify(result.result.data, null, 2)}`);
                } else {
                    log(`❌ 云函数调用失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testAddCategory() {
            log('开始测试添加分类...');
            
            const testCategory = {
                name: '测试分类_' + Date.now(),
                icon: '🧪',
                description: '这是一个测试分类',
                status: 'show',
                sort: 99
            };
            
            try {
                const response = await fetch('http://localhost:8001/api/cloud-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        functionName: 'adminAPI',
                        data: {
                            action: 'createCategory',
                            collection: 'categories',
                            data: testCategory
                        }
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 分类添加成功！ID: ${result.result.id}`, 'success');
                    log(`新分类数据: ${JSON.stringify(result.result.data, null, 2)}`);
                } else {
                    log(`❌ 分类添加失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testUpdateCategory() {
            log('开始测试更新分类...');
            
            const updateData = {
                name: '更新的测试分类_' + Date.now(),
                description: '这是更新后的描述'
            };
            
            try {
                const response = await fetch('http://localhost:8001/api/cloud-function', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        functionName: 'adminAPI',
                        data: {
                            action: 'updateCategory',
                            collection: 'categories',
                            id: 'test_category_id',
                            data: updateData
                        }
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 分类更新成功！`, 'success');
                    log(`更新后数据: ${JSON.stringify(result.result.data, null, 2)}`);
                } else {
                    log(`❌ 分类更新失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 云函数连接测试工具已加载', 'success');
            log('请先启动管理后台服务器: 启动管理后台.bat', 'info');
            log('然后点击上方按钮进行测试', 'info');
        });
    </script>
</body>
</html>
