<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 最终解决方案</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background-color: #218838; }
        .solution {
            background-color: #e7f3ff;
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 最终解决方案</h1>
        
        <div class="solution">
            <h3>💡 基于项目配置的完整解决方案</h3>
            <p>使用您项目中的实际配置参数，包括超时、重试机制等</p>
        </div>
        
        <button onclick="runFinalTest()">🚀 运行最终测试</button>
        <button onclick="tryAlternativeSDK()">🔄 尝试备用SDK</button>
        <button onclick="testWithRetry()">🔁 带重试机制测试</button>
        
        <div id="results"></div>
    </div>

    <!-- 使用项目中确认的SDK版本 -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        // 使用项目实际配置
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        const REGION = 'ap-shanghai';
        const TIMEOUT = 10000;
        const RETRY_COUNT = 3;
        const RETRY_DELAY = 1000;
        
        let tcbApp = null;
        let db = null;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }

        async function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async function initWithRetry() {
            for (let attempt = 1; attempt <= RETRY_COUNT; attempt++) {
                try {
                    log(`尝试初始化 (第${attempt}次)...`, 'info');
                    
                    // 使用完整的项目配置
                    const config = {
                        env: ENV_ID,
                        region: REGION,
                        timeout: TIMEOUT,
                        traceUser: true
                    };
                    
                    log(`配置: ${JSON.stringify(config)}`, 'info');
                    
                    tcbApp = window.cloudbase.init(config);
                    
                    if (!tcbApp) {
                        throw new Error('初始化返回null');
                    }
                    
                    // 验证初始化
                    const auth = tcbApp.auth();
                    if (!auth) {
                        throw new Error('auth对象获取失败');
                    }
                    
                    db = tcbApp.database();
                    if (!db) {
                        throw new Error('database对象获取失败');
                    }
                    
                    log(`✅ 初始化成功 (第${attempt}次尝试)`, 'success');
                    return true;
                    
                } catch (error) {
                    log(`❌ 第${attempt}次初始化失败: ${error.message}`, 'error');
                    
                    if (attempt < RETRY_COUNT) {
                        log(`等待${RETRY_DELAY}ms后重试...`, 'warning');
                        await sleep(RETRY_DELAY);
                    }
                }
            }
            
            throw new Error(`初始化失败，已重试${RETRY_COUNT}次`);
        }

        async function queryWithRetry(collection, operation = 'get') {
            for (let attempt = 1; attempt <= RETRY_COUNT; attempt++) {
                try {
                    log(`执行查询 (第${attempt}次尝试)...`, 'info');
                    
                    const query = db.collection(collection).limit(1);
                    
                    // 设置查询超时
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('查询超时')), TIMEOUT);
                    });
                    
                    const queryPromise = query.get();
                    
                    const result = await Promise.race([queryPromise, timeoutPromise]);
                    
                    if (result && typeof result === 'object') {
                        log(`✅ 查询成功 (第${attempt}次尝试)`, 'success');
                        return result;
                    } else {
                        throw new Error(`查询返回无效结果: ${typeof result}`);
                    }
                    
                } catch (error) {
                    log(`❌ 第${attempt}次查询失败: ${error.message}`, 'error');
                    
                    if (attempt < RETRY_COUNT) {
                        log(`等待${RETRY_DELAY}ms后重试...`, 'warning');
                        await sleep(RETRY_DELAY);
                    }
                }
            }
            
            throw new Error(`查询失败，已重试${RETRY_COUNT}次`);
        }

        async function runFinalTest() {
            log('🎯 开始最终解决方案测试...', 'info');
            document.getElementById('results').innerHTML = '';
            
            try {
                // 1. SDK检查
                log('=== 步骤1: SDK检查 ===', 'info');
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }
                log(`✅ SDK版本: ${window.cloudbase.version || '未知'}`, 'success');
                
                // 2. 带重试的初始化
                log('=== 步骤2: 带重试的初始化 ===', 'info');
                await initWithRetry();
                
                // 3. 带重试的查询测试
                log('=== 步骤3: 带重试的查询测试 ===', 'info');
                
                const collections = ['emojis', 'categories', 'users', 'banners'];
                let successCount = 0;
                
                for (const collection of collections) {
                    try {
                        const result = await queryWithRetry(collection);
                        log(`✅ ${collection} 查询成功，记录数: ${result.data ? result.data.length : '未知'}`, 'success');
                        successCount++;
                        
                        if (result.data && result.data.length > 0) {
                            log(`示例数据: ${JSON.stringify(result.data[0], null, 2).substring(0, 200)}...`, 'info');
                        }
                    } catch (error) {
                        log(`❌ ${collection} 查询失败: ${error.message}`, 'error');
                    }
                }
                
                // 4. 结果分析
                log('=== 测试结果分析 ===', 'info');
                if (successCount > 0) {
                    log(`🎉 成功！${successCount}/${collections.length} 个集合查询成功`, 'success');
                    log('💡 数据库连接正常，可以继续使用', 'success');
                } else {
                    log('❌ 所有查询都失败了', 'error');
                    log('💡 建议检查云开发环境状态或联系技术支持', 'warning');
                }
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                log('💡 请尝试备用SDK或检查网络连接', 'warning');
            }
        }

        async function tryAlternativeSDK() {
            log('🔄 尝试加载备用SDK版本...', 'info');
            
            // 动态加载不同版本的SDK
            const sdkVersions = [
                'https://static.cloudbase.net/cloudbase-js-sdk/2.16.0/cloudbase.full.js',
                'https://static.cloudbase.net/cloudbase-js-sdk/2.15.0/cloudbase.full.js'
            ];
            
            for (const sdkUrl of sdkVersions) {
                try {
                    log(`尝试加载: ${sdkUrl}`, 'info');
                    
                    const script = document.createElement('script');
                    script.src = sdkUrl;
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                    
                    log(`✅ SDK加载成功: ${sdkUrl}`, 'success');
                    
                    // 尝试初始化
                    await runFinalTest();
                    return;
                    
                } catch (error) {
                    log(`❌ SDK加载失败: ${error.message}`, 'error');
                }
            }
            
            log('❌ 所有备用SDK都加载失败', 'error');
        }

        async function testWithRetry() {
            log('🔁 使用增强重试机制测试...', 'info');
            
            // 增加重试次数和延迟
            const originalRetryCount = RETRY_COUNT;
            const originalRetryDelay = RETRY_DELAY;
            
            // 临时修改重试参数
            window.RETRY_COUNT = 5;
            window.RETRY_DELAY = 2000;
            
            try {
                await runFinalTest();
            } finally {
                // 恢复原始参数
                window.RETRY_COUNT = originalRetryCount;
                window.RETRY_DELAY = originalRetryDelay;
            }
        }

        // 页面加载完成
        window.addEventListener('DOMContentLoaded', function() {
            log('🎯 最终解决方案已加载', 'success');
            log('💡 这个版本使用了您项目中的完整配置参数', 'info');
            log('💡 包括超时设置、重试机制等', 'info');
        });
    </script>
</body>
</html>
