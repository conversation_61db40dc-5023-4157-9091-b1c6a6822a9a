// pages/detail/detail-new.js - 重构版表情包详情页
const { StateManager } = require('../../utils/stateManager.js')

// 分类ID到名称的映射
const categoryNameMap = {
  'funny': '搞笑幽默',
  'cute': '可爱萌宠',
  'emotion': '情感表达',
  'festival': '节日庆典',
  'hot': '网络热梗',
  '2d': '动漫二次元',
  'daily': '生活日常',
  'work': '工作学习',
  'sport': '运动健身',
  'food': '美食料理'
}

Page({
  data: {
    // 基础数据
    emojiData: null,
    loading: true,
    error: null,

    // 用户状态
    isLiked: false,
    isCollected: false,

    // 相关推荐
    relatedEmojis: [],

    // UI状态
    showActions: false,
    actionLoading: {
      like: false,
      collect: false,
      download: false
    }
  },

  // 防抖定时器
  _debounceTimers: {
    like: null,
    collect: null
  },

  onLoad(options) {
    console.log('🔄 详情页加载，参数:', options);

    // 隐藏TabBar，因为详情页不是TabBar页面
    if (typeof wx.hideTabBar === 'function') {
      wx.hideTabBar({
        animation: false
      });
    }

    const emojiId = options.id;
    if (!emojiId) {
      this.showError('参数错误：缺少表情包ID');
      return;
    }

    this.loadEmojiDetail(emojiId);
    this.recordBrowseHistory(emojiId);
  },

  onUnload() {
    // 清理防抖定时器，避免内存泄漏
    if (this._debounceTimers.like) {
      clearTimeout(this._debounceTimers.like);
      this._debounceTimers.like = null;
    }
    if (this._debounceTimers.collect) {
      clearTimeout(this._debounceTimers.collect);
      this._debounceTimers.collect = null;
    }
    console.log('🧹 详情页已清理资源');
  },

  // 核心方法：加载表情包详情
  async loadEmojiDetail(id) {
    console.log('🔄 开始加载表情包详情:', id);

    this.setData({ loading: true, error: null });

    try {
      // 直接调用云函数获取详情
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojiDetail',
          data: { id }
        }
      });

      console.log('☁️ 云函数返回结果:', result);

      if (result.result && result.result.success && result.result.data) {
        const emojiData = this.processEmojiData(result.result.data);

        // 使用精确更新模式，避免传入整个emojiData对象导致的UI抖动
        this.setData({
          'emojiData.id': emojiData.id,
          'emojiData.title': emojiData.title,
          'emojiData.imageUrl': emojiData.imageUrl,
          'emojiData.description': emojiData.description,
          'emojiData.category': emojiData.category,
          'emojiData.categoryName': emojiData.categoryName,
          'emojiData.tags': emojiData.tags,
          'emojiData.downloads': emojiData.downloads,
          'emojiData.downloadsText': emojiData.downloadsText,
          'emojiData.views': emojiData.views,
          'emojiData.viewsText': emojiData.viewsText,
          loading: false,
          showActions: true
        });

        // 异步加载用户状态和相关推荐
        this.loadUserStatus(emojiData.id);
        this.loadRelatedEmojis(emojiData.category); // 启用相关推荐功能

        console.log('✅ 表情包详情加载成功:', emojiData.title);
      } else {
        throw new Error(result.result?.message || '获取表情包详情失败');
      }
    } catch (error) {
      console.error('❌ 加载表情包详情失败:', error);
      this.showError(error.message || '加载失败，请重试');
    }
  },

  // 处理表情包数据
  processEmojiData(rawData) {
    const categoryId = rawData.category || 'unknown';
    // 优先使用云函数返回的categoryName，如果没有则使用映射表
    const categoryName = rawData.categoryName || categoryNameMap[categoryId] || categoryId || '未分类';

    return {
      id: rawData._id || rawData.id,
      title: rawData.title || '未知表情包',
      imageUrl: rawData.imageUrl || '',
      description: rawData.description || '',
      category: categoryId,
      categoryName: categoryName,
      tags: rawData.tags || [],

      // 统计数据
      downloads: rawData.downloads || 0,
      views: rawData.views || 0,

      // 格式化显示
      downloadsText: this.formatNumber(rawData.downloads || 0),
      viewsText: this.formatNumber(rawData.views || 0)
    };
  },

  // 加载用户状态（点赞、收藏）- 使用StateManager获取状态
  async loadUserStatus(emojiId) {
    try {
      const userState = StateManager.getEmojiState(emojiId);

      this.setData({
        isLiked: userState.isLiked,
        isCollected: userState.isCollected
      });
    } catch (error) {
      console.warn('⚠️ 加载用户状态失败:', error);
    }
  },

  // 加载相关推荐
  async loadRelatedEmojis(category) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: { category, page: 1, limit: 6 }
        }
      });

      if (result.result && result.result.success) {
        const relatedEmojis = result.result.data
          .filter(item => item._id !== this.data.emojiData.id)
          .slice(0, 4)
          .map(item => {
            // 处理分类名称显示
            const categoryId = item.category || 'unknown'
            const categoryName = item.categoryName || categoryNameMap[categoryId] || categoryId || '未分类'

            return {
              id: item._id || item.id,
              title: item.title,
              imageUrl: item.imageUrl,
              category: categoryId,
              categoryName: categoryName,
              tags: item.tags || [],
              likes: item.likes || 0,
              collections: item.collections || 0,
              likesText: this.formatNumber(item.likes || 0),
              collectionsText: this.formatNumber(item.collections || 0)
            }
          });

        // 直接更新相关推荐数组
        this.setData({
          relatedEmojis: relatedEmojis
        });
        console.log('✅ 相关推荐加载完成:', relatedEmojis.length, '个');
      }
    } catch (error) {
      console.warn('⚠️ 加载相关推荐失败:', error);
    }
  },

  // 点赞方法 - 严格按照用户A/B测试指令的精确更新模式
  async onLike() {
    // 防止重复点击
    if (this.data.actionLoading.like) return;

    try {
      // 严格按照用户指令：测试A的精确更新模式
      this.setData({
        isLiked: !this.data.isLiked
      });

      // 触觉反馈
      wx.vibrateShort();

      console.log('✅ 点赞操作完成:', !this.data.isLiked ? '已点赞' : '已取消');

    } catch (error) {
      console.error('❌ 点赞操作失败:', error);
    }
  },





  // 收藏方法 - 严格按照用户A/B测试指令的精确更新模式
  async onCollect() {
    // 防止重复点击
    if (this.data.actionLoading.collect) return;

    try {
      // 严格按照用户指令：测试A的精确更新模式
      this.setData({
        isCollected: !this.data.isCollected
      });

      // 触觉反馈
      wx.vibrateShort();

      // 显示Toast反馈
      wx.showToast({
        title: !this.data.isCollected ? '已收藏' : '已取消收藏',
        icon: 'success',
        duration: 1500
      });

      console.log('✅ 收藏操作完成:', !this.data.isCollected ? '已收藏' : '已取消');

    } catch (error) {
      console.error('❌ 收藏操作失败:', error);
    }
  },



  // 下载操作
  async onDownload() {
    if (this.data.actionLoading.download) return;

    const emojiData = this.data.emojiData;

    // 详细的调试信息
    console.log('🔍 下载调试信息:', {
      emojiData: emojiData,
      imageUrl: emojiData?.imageUrl,
      urlType: emojiData?.imageUrl?.startsWith('https://') ? 'HTTPS' :
               emojiData?.imageUrl?.startsWith('http://') ? 'HTTP' :
               emojiData?.imageUrl?.startsWith('cloud://') ? 'CLOUD' :
               emojiData?.imageUrl?.startsWith('data:') ? 'BASE64' : 'UNKNOWN'
    });

    if (!emojiData || !emojiData.imageUrl) {
      console.error('❌ 下载失败: 图片数据无效');
      wx.showToast({ title: '图片地址无效', icon: 'error' });
      return;
    }

    // 检查URL格式 - 允许HTTP/HTTPS/云存储URL
    if (!emojiData.imageUrl.startsWith('http://') &&
        !emojiData.imageUrl.startsWith('https://') &&
        !emojiData.imageUrl.startsWith('cloud://')) {
      console.error('❌ 下载失败: 图片URL格式不正确', emojiData.imageUrl);
      wx.showToast({ title: '图片地址格式错误', icon: 'error' });
      return;
    }

    // 如果是云存储URL，先尝试转换
    if (emojiData.imageUrl.startsWith('cloud://')) {
      console.warn('⚠️ 检测到云存储URL，尝试转换:', emojiData.imageUrl);

      try {
        wx.showLoading({ title: '转换图片地址...' });

        // 调用云函数转换URL
        const result = await wx.cloud.getTempFileURL({
          fileList: [emojiData.imageUrl]
        });

        wx.hideLoading();

        if (result.fileList && result.fileList.length > 0) {
          const fileInfo = result.fileList[0];
          if (fileInfo.status === 0 && fileInfo.tempFileURL) {
            // 更新图片URL
            emojiData.imageUrl = fileInfo.tempFileURL;
            console.log('✅ URL转换成功:', emojiData.imageUrl);
          } else {
            console.error('❌ URL转换失败:', fileInfo);
            throw new Error(`URL转换失败: ${fileInfo.errMsg || '未知错误'}`);
          }
        } else {
          throw new Error('URL转换返回空结果');
        }
      } catch (error) {
        wx.hideLoading();
        console.error('❌ URL转换失败:', error);
        wx.showToast({
          title: '图片地址转换失败',
          icon: 'error',
          duration: 2000
        });
        return;
      }
    }

    this.setData({ 'actionLoading.download': true });

    try {
      // 先检查和请求相册权限
      const authResult = await this.checkAndRequestPhotoAuth();
      if (!authResult) {
        this.setData({ 'actionLoading.download': false });
        return;
      }

      wx.showLoading({ title: '保存中...' });

      console.log('📥 开始下载文件，URL:', emojiData.imageUrl);

      // 下载图片
      const downloadResult = await new Promise((resolve, reject) => {
        wx.downloadFile({
          url: emojiData.imageUrl,
          timeout: 30000, // 30秒超时
          success: (res) => {
            console.log('✅ 下载成功:', res);
            resolve(res);
          },
          fail: (error) => {
            console.error('❌ 下载文件失败详情:', {
              errMsg: error.errMsg,
              errCode: error.errCode,
              url: emojiData.imageUrl
            });
            reject(error);
          }
        });
      });

      // 检查下载结果
      if (!downloadResult.tempFilePath) {
        throw new Error('下载文件路径无效');
      }

      console.log('✅ 图片下载成功:', downloadResult.tempFilePath);

      // 保存到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: downloadResult.tempFilePath,
          success: (res) => {
            console.log('✅ 保存到相册成功:', res);
            resolve(res);
          },
          fail: (error) => {
            console.error('❌ 保存到相册失败详情:', {
              errMsg: error.errMsg,
              errCode: error.errCode,
              filePath: downloadResult.tempFilePath
            });
            reject(error);
          }
        });
      });

      // 更新下载统计
      const newDownloads = emojiData.downloads + 1;
      this.setData({
        'emojiData.downloads': newDownloads
      });

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 触觉反馈
      wx.vibrateShort();
      
      console.log('✅ 下载完成:', emojiData.title);
    } catch (error) {
      wx.hideLoading();
      console.error('❌ 下载失败:', error);

      let errorMessage = '保存失败';
      let showModal = false;

      if (error.errMsg) {
        if (error.errMsg.includes('auth deny') || error.errMsg.includes('authorize')) {
          errorMessage = '需要相册权限';
          showModal = true;
        } else if (error.errMsg.includes('network')) {
          errorMessage = '网络连接失败';
        } else if (error.errMsg.includes('timeout')) {
          errorMessage = '下载超时';
        } else if (error.errMsg.includes('downloadFile:fail')) {
          errorMessage = '图片下载失败';
        } else if (error.errMsg.includes('saveImageToPhotosAlbum:fail')) {
          errorMessage = '保存到相册失败';
        }
      }

      if (showModal) {
        wx.showModal({
          title: '需要授权',
          content: '保存图片需要访问相册权限，请在设置中开启',
          confirmText: '去设置',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      } else {
        wx.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 2000
        });
      }
    } finally {
      this.setData({ 'actionLoading.download': false });
    }
  },

  // 检查和请求相册权限
  async checkAndRequestPhotoAuth() {
    try {
      // 检查当前权限状态
      const authSetting = await new Promise((resolve, reject) => {
        wx.getSetting({
          success: resolve,
          fail: reject
        });
      });

      // 如果已经授权，直接返回true
      if (authSetting.authSetting['scope.writePhotosAlbum']) {
        return true;
      }

      // 如果未授权，请求授权
      const authResult = await new Promise((resolve, reject) => {
        wx.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => resolve(true),
          fail: () => resolve(false)
        });
      });

      if (!authResult) {
        // 授权失败，引导用户手动开启
        const modalResult = await new Promise((resolve) => {
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启权限',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => resolve(res.confirm)
          });
        });

        if (modalResult) {
          // 打开设置页面
          wx.openSetting();
        }
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ 权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
      return false;
    }
  },

  // 分享好友
  onShare() {
    // 微信小程序会自动调用 onShareAppMessage
    console.log('📤 分享表情包:', this.data.emojiData.title);
  },

  // 相关推荐点击
  onRelatedTap(e) {
    const emoji = e.currentTarget.dataset.emoji;
    if (!emoji || !emoji.id) return;

    wx.redirectTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    });
  },

  // 显示错误
  showError(message) {
    this.setData({
      loading: false,
      error: message
    });

    wx.showToast({
      title: message,
      icon: 'error',
      duration: 3000
    });
  },

  // 重试加载
  onRetry() {
    const options = getCurrentPages().pop().options;
    if (options.id) {
      this.loadEmojiDetail(options.id);
    }
  },

  // 工具方法：格式化数字
  formatNumber(num) {
    if (num < 1000) return num.toString();
    if (num < 10000) return (num / 1000).toFixed(1) + 'k';
    return (num / 10000).toFixed(1) + 'w';
  },

  // 工具方法：格式化日期
  formatDate(date) {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 30) return `${days}天前`;
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  },

  // 记录浏览历史
  recordBrowseHistory(emojiId) {
    try {
      let browseHistory = wx.getStorageSync('browseHistory') || [];

      // 移除已存在的记录（避免重复）
      browseHistory = browseHistory.filter(item => item.emojiId !== emojiId);

      // 添加新记录到开头
      browseHistory.unshift({
        emojiId: emojiId,
        browseTime: new Date().toISOString()
      });

      // 只保留最近100个记录
      if (browseHistory.length > 100) {
        browseHistory = browseHistory.slice(0, 100);
      }

      wx.setStorageSync('browseHistory', browseHistory);
      console.log(`表情包 ${emojiId} 浏览记录已保存`);
    } catch (error) {
      console.error('保存浏览记录失败:', error);
    }
  },

  // 分享配置
  onShareAppMessage() {
    const emojiData = this.data.emojiData;
    if (!emojiData) return {};

    return {
      title: emojiData.title,
      path: `/pages/detail/detail-new?id=${emojiData.id}`,
      imageUrl: emojiData.imageUrl
    };
  }
});
