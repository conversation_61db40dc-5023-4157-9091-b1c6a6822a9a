/**
 * 图片懒加载管理器
 * 实现图片懒加载、预加载和缓存机制，提升图片加载性能
 */

const LazyImageLoader = {
  // 观察器实例
  _observer: null,
  // 图片缓存
  _imageCache: new Map(),
  // 预加载队列
  _preloadQueue: [],
  // 正在加载的图片
  _loadingImages: new Set(),
  // 配置选项
  _options: {
    rootMargin: '100px', // 提前100px开始加载
    threshold: 0.1, // 10%可见时触发
    maxCacheSize: 200, // 最大缓存数量
    preloadCount: 8, // 预加载数量
    retryCount: 3, // 重试次数
    timeout: 10000, // 超时时间
    enableWebP: true, // 启用WebP格式
    enableProgressive: true, // 启用渐进式加载
    enableBlurHash: false, // 启用BlurHash占位符
    compressionQuality: 80, // 压缩质量
    enablePrefetch: true // 启用预取
  },

  // 性能统计
  _stats: {
    totalLoaded: 0,
    totalFailed: 0,
    cacheHits: 0,
    averageLoadTime: 0,
    loadTimes: []
  },

  // 图片格式支持检测
  _formatSupport: {
    webp: null,
    avif: null
  },

  /**
   * 初始化懒加载管理器
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    this._options = { ...this._options, ...options }
    console.log('🖼️ 图片懒加载管理器初始化')

    // 检测图片格式支持
    this.detectFormatSupport()

    // 创建交叉观察器
    this.createIntersectionObserver()

    // 清理过期缓存
    this.cleanExpiredCache()

    // 启动性能监控
    this.startPerformanceMonitoring()
  },

  /**
   * 创建交叉观察器
   */
  createIntersectionObserver() {
    try {
      this._observer = wx.createIntersectionObserver(null, {
        thresholds: [this._options.threshold],
        initialRatio: 0,
        observeAll: true
      })

      this._observer.relativeToViewport({
        top: -parseInt(this._options.rootMargin),
        bottom: -parseInt(this._options.rootMargin)
      })

      console.log('👁️ 交叉观察器创建成功')
    } catch (error) {
      console.error('❌ 交叉观察器创建失败:', error)
    }
  },

  /**
   * 观察图片元素
   * @param {string} selector - 选择器
   * @param {Function} callback - 回调函数
   */
  observe(selector, callback) {
    if (!this._observer) {
      console.warn('⚠️ 观察器未初始化')
      return
    }

    this._observer.observe(selector, (res) => {
      if (res.intersectionRatio > 0) {
        // 元素进入视口
        const imageUrl = res.dataset?.src
        if (imageUrl) {
          this.loadImage(imageUrl, callback)
          // 停止观察已加载的图片
          this._observer.unobserve(selector)
        }
      }
    })
  },

  /**
   * 加载图片
   * @param {string} imageUrl - 图片URL
   * @param {Function} callback - 回调函数
   * @param {number} retryCount - 重试次数
   */
  async loadImage(imageUrl, callback, retryCount = 0) {
    if (!imageUrl) {
      console.warn('⚠️ 图片URL为空')
      return
    }

    // 检查缓存
    if (this._imageCache.has(imageUrl)) {
      const cachedImage = this._imageCache.get(imageUrl)
      if (this.isCacheValid(cachedImage)) {
        console.log('📦 使用缓存图片:', imageUrl)
        this._stats.cacheHits++
        callback && callback(null, cachedImage.path)
        return
      } else {
        this._imageCache.delete(imageUrl)
      }
    }

    // 检查是否正在加载
    if (this._loadingImages.has(imageUrl)) {
      console.log('⏳ 图片正在加载中:', imageUrl)
      return
    }

    this._loadingImages.add(imageUrl)

    // 优化图片URL
    const optimizedUrl = this.optimizeImageUrl(imageUrl, {
      quality: this._options.compressionQuality
    })

    console.log('🖼️ 开始加载图片:', optimizedUrl)
    const startTime = Date.now()

    try {
      const imagePath = await this.downloadImage(optimizedUrl)

      // 缓存图片
      this.cacheImage(imageUrl, imagePath)

      // 记录性能
      const loadTime = Date.now() - startTime
      this.recordLoadPerformance(loadTime, true)

      // 执行回调
      callback && callback(null, imagePath)

      console.log('✅ 图片加载成功:', imageUrl, `(${loadTime}ms)`)
    } catch (error) {
      console.error('❌ 图片加载失败:', imageUrl, error)

      // 记录性能
      const loadTime = Date.now() - startTime
      this.recordLoadPerformance(loadTime, false)

      // 重试机制
      if (retryCount < this._options.retryCount) {
        console.log(`🔄 重试加载图片 (${retryCount + 1}/${this._options.retryCount}):`, imageUrl)
        // 使用指数退避重试
        const retryDelay = Math.pow(2, retryCount) * 1000
        setTimeout(() => {
          this.loadImage(imageUrl, callback, retryCount + 1)
        }, retryDelay)
      } else {
        callback && callback(error, null)
      }
    } finally {
      this._loadingImages.delete(imageUrl)
    }
  },

  /**
   * 下载图片
   * @param {string} imageUrl - 图片URL
   * @returns {Promise<string>} 本地图片路径
   */
  downloadImage(imageUrl) {
    return new Promise((resolve, reject) => {
      const downloadTask = wx.downloadFile({
        url: imageUrl,
        timeout: this._options.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '下载失败'))
        }
      })

      // 可以在这里添加下载进度监听
      downloadTask.onProgressUpdate && downloadTask.onProgressUpdate((res) => {
        console.log('📊 下载进度:', res.progress + '%')
      })
    })
  },

  /**
   * 缓存图片
   * @param {string} imageUrl - 图片URL
   * @param {string} imagePath - 本地图片路径
   */
  cacheImage(imageUrl, imagePath) {
    // 检查缓存大小
    if (this._imageCache.size >= this._options.maxCacheSize) {
      this.cleanOldestCache()
    }

    this._imageCache.set(imageUrl, {
      path: imagePath,
      timestamp: Date.now(),
      accessCount: 1
    })

    console.log('📦 图片已缓存:', imageUrl)
  },

  /**
   * 检查缓存是否有效
   * @param {Object} cachedImage - 缓存的图片信息
   * @returns {boolean} 是否有效
   */
  isCacheValid(cachedImage) {
    // 检查文件是否存在（简化版，实际可能需要更复杂的检查）
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    return Date.now() - cachedImage.timestamp < maxAge
  },

  /**
   * 清理最旧的缓存
   */
  cleanOldestCache() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, value] of this._imageCache.entries()) {
      if (value.timestamp < oldestTime) {
        oldestTime = value.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this._imageCache.delete(oldestKey)
      console.log('🗑️ 清理旧缓存:', oldestKey)
    }
  },

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时

    for (const [key, value] of this._imageCache.entries()) {
      if (now - value.timestamp > maxAge) {
        this._imageCache.delete(key)
        console.log('🗑️ 清理过期缓存:', key)
      }
    }
  },

  /**
   * 预加载图片列表
   * @param {Array} imageUrls - 图片URL列表
   * @param {number} count - 预加载数量
   */
  async preloadImages(imageUrls, count = this._options.preloadCount) {
    if (!imageUrls || imageUrls.length === 0) {
      return
    }

    console.log(`🚀 开始预加载图片: ${Math.min(count, imageUrls.length)} 张`)

    const preloadList = imageUrls.slice(0, count)
    const promises = preloadList.map(url => 
      this.loadImage(url, null).catch(error => {
        console.warn('⚠️ 预加载失败:', url, error)
      })
    )

    try {
      await Promise.all(promises)
      console.log('✅ 图片预加载完成')
    } catch (error) {
      console.error('❌ 图片预加载失败:', error)
    }
  },

  /**
   * 批量观察图片元素
   * @param {Array} selectors - 选择器列表
   * @param {Function} callback - 回调函数
   */
  observeMultiple(selectors, callback) {
    selectors.forEach(selector => {
      this.observe(selector, callback)
    })
  },

  /**
   * 停止观察所有元素
   */
  disconnect() {
    if (this._observer) {
      this._observer.disconnect()
      console.log('👁️ 停止观察所有元素')
    }
  },

  /**
   * 获取缓存状态
   * @returns {Object} 缓存状态信息
   */
  getCacheStatus() {
    return {
      cacheSize: this._imageCache.size,
      maxCacheSize: this._options.maxCacheSize,
      loadingCount: this._loadingImages.size,
      preloadQueueLength: this._preloadQueue.length
    }
  },

  /**
   * 清空所有缓存
   */
  clearCache() {
    this._imageCache.clear()
    this._preloadQueue = []
    this._loadingImages.clear()
    console.log('🗑️ 图片缓存已清空')
  },

  /**
   * 检测图片格式支持
   */
  detectFormatSupport() {
    // 检测WebP支持
    this.checkWebPSupport()

    // 检测AVIF支持
    this.checkAVIFSupport()
  },

  /**
   * 检测WebP支持
   */
  checkWebPSupport() {
    try {
      // 微信小程序环境，默认支持WebP
      this._formatSupport.webp = true
    } catch (error) {
      this._formatSupport.webp = false
    }

    console.log('🖼️ WebP支持:', this._formatSupport.webp)
  },

  /**
   * 检测AVIF支持
   */
  checkAVIFSupport() {
    // 微信小程序暂不支持AVIF
    this._formatSupport.avif = false
    console.log('🖼️ AVIF支持:', this._formatSupport.avif)
  },

  /**
   * 优化图片URL
   * @param {string} url - 原始URL
   * @param {Object} options - 优化选项
   * @returns {string} 优化后的URL
   */
  optimizeImageUrl(url, options = {}) {
    if (!url) return url

    const {
      width,
      height,
      quality = this._options.compressionQuality,
      format = 'auto'
    } = options

    try {
      // 如果URL包含参数，添加优化参数
      if (url.includes('?') || url.includes('&')) {
        const separator = url.includes('?') ? '&' : '?'
        let optimizedUrl = url

        // 添加尺寸参数
        if (width) optimizedUrl += `${separator}w=${width}`
        if (height) optimizedUrl += `&h=${height}`

        // 添加质量参数
        if (quality && quality < 100) {
          optimizedUrl += `&q=${quality}`
        }

        // 添加格式参数
        if (format === 'auto' && this._formatSupport.webp && this._options.enableWebP) {
          optimizedUrl += '&f=webp'
        }

        return optimizedUrl
      }

      return url
    } catch (error) {
      console.warn('⚠️ URL优化失败:', error)
      return url
    }
  },

  /**
   * 启动性能监控
   */
  startPerformanceMonitoring() {
    // 定期清理性能数据
    setInterval(() => {
      this.cleanupPerformanceData()
    }, 5 * 60 * 1000) // 5分钟清理一次
  },

  /**
   * 清理性能数据
   */
  cleanupPerformanceData() {
    // 只保留最近100次加载时间
    if (this._stats.loadTimes.length > 100) {
      this._stats.loadTimes = this._stats.loadTimes.slice(-100)

      // 重新计算平均加载时间
      const total = this._stats.loadTimes.reduce((sum, time) => sum + time, 0)
      this._stats.averageLoadTime = total / this._stats.loadTimes.length
    }
  },

  /**
   * 记录加载性能
   * @param {number} loadTime - 加载时间
   * @param {boolean} success - 是否成功
   */
  recordLoadPerformance(loadTime, success) {
    if (success) {
      this._stats.totalLoaded++
      this._stats.loadTimes.push(loadTime)

      // 更新平均加载时间
      const total = this._stats.loadTimes.reduce((sum, time) => sum + time, 0)
      this._stats.averageLoadTime = total / this._stats.loadTimes.length
    } else {
      this._stats.totalFailed++
    }
  },

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this._stats,
      cacheSize: this._imageCache.size,
      loadingCount: this._loadingImages.size,
      preloadQueueSize: this._preloadQueue.length,
      successRate: this._stats.totalLoaded / (this._stats.totalLoaded + this._stats.totalFailed) * 100,
      formatSupport: this._formatSupport
    }
  },

  /**
   * 批量预加载图片
   * @param {Array} urls - 图片URL数组
   * @param {Object} options - 预加载选项
   */
  async batchPreload(urls, options = {}) {
    const { concurrency = 3, priority = 'normal' } = options

    console.log(`🔄 批量预加载 ${urls.length} 张图片`)

    // 分批处理
    const batches = []
    for (let i = 0; i < urls.length; i += concurrency) {
      batches.push(urls.slice(i, i + concurrency))
    }

    let loadedCount = 0
    let failedCount = 0

    for (const batch of batches) {
      const promises = batch.map(url =>
        this.preloadImage(url, { priority })
          .then(() => { loadedCount++ })
          .catch(() => { failedCount++ })
      )

      await Promise.all(promises)
    }

    console.log(`✅ 批量预加载完成: 成功 ${loadedCount}, 失败 ${failedCount}`)
    return { loaded: loadedCount, failed: failedCount }
  },

  /**
   * 销毁懒加载管理器
   */
  destroy() {
    this.disconnect()
    this.clearCache()
    this._observer = null
    console.log('🗑️ 图片懒加载管理器已销毁')
  }
}

/**
 * 图片懒加载组件混入
 */
const LazyImageMixin = {
  /**
   * 初始化懒加载
   */
  initLazyImage() {
    // 在页面显示时初始化
    this.lazyImageCallback = (error, imagePath) => {
      if (error) {
        console.error('❌ 懒加载图片失败:', error)
      } else {
        // 更新页面数据
        this.updateImagePath && this.updateImagePath(imagePath)
      }
    }
  },

  /**
   * 开始观察图片
   * @param {string} selector - 选择器
   */
  observeImages(selector = '.lazy-image') {
    LazyImageLoader.observe(selector, this.lazyImageCallback)
  },

  /**
   * 预加载图片
   * @param {Array} imageUrls - 图片URL列表
   */
  preloadImages(imageUrls) {
    LazyImageLoader.preloadImages(imageUrls)
  },

  /**
   * 页面卸载时清理
   */
  destroyLazyImage() {
    LazyImageLoader.disconnect()
  }
}

module.exports = {
  LazyImageLoader,
  LazyImageMixin
}
