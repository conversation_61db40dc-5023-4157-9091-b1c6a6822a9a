/**
 * 部署管理后台相关云函数
 * 手动部署指南
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 管理后台云函数部署指南\n');

// 检查关键云函数
const keyFunctions = ['adminAPI', 'dataAPI', 'initDatabase'];

console.log('📋 需要部署的关键云函数:');

keyFunctions.forEach(funcName => {
    const funcPath = path.join(__dirname, 'cloudfunctions', funcName);
    const indexPath = path.join(funcPath, 'index.js');
    const packagePath = path.join(funcPath, 'package.json');
    
    console.log(`\n📦 ${funcName}:`);
    console.log(`   路径: ${funcPath}`);
    console.log(`   index.js: ${fs.existsSync(indexPath) ? '✅ 存在' : '❌ 缺失'}`);
    console.log(`   package.json: ${fs.existsSync(packagePath) ? '✅ 存在' : '❌ 缺失'}`);
    
    if (fs.existsSync(indexPath)) {
        const content = fs.readFileSync(indexPath, 'utf8');
        const lines = content.split('\n').length;
        console.log(`   代码行数: ${lines}`);
    }
});

console.log('\n🔧 手动部署步骤:');
console.log('1. 打开微信开发者工具');
console.log('2. 打开你的小程序项目');
console.log('3. 点击"云开发"按钮');
console.log('4. 在左侧菜单选择"云函数"');
console.log('5. 找到以下云函数，右键选择"上传并部署":');

keyFunctions.forEach(func => {
    console.log(`   - ${func}`);
});

console.log('\n⚡ 部署完成后测试:');
console.log('1. 刷新管理后台页面');
console.log('2. 如果还是卡住，点击"初始化数据"按钮');
console.log('3. 查看浏览器控制台是否有错误信息');

console.log('\n🎯 预期结果:');
console.log('- 统计数据正常显示');
console.log('- 各个标签页可以切换');
console.log('- 可以看到测试数据');

// 创建快速测试脚本
const testScript = `
// 在浏览器控制台运行此脚本测试云函数
wx.cloud.callFunction({
  name: 'adminAPI',
  data: {
    action: 'testDatabase'
  }
}).then(res => {
  console.log('数据库测试结果:', res);
}).catch(err => {
  console.error('云函数调用失败:', err);
});
`;

fs.writeFileSync(path.join(__dirname, 'test-cloud-function.js'), testScript);
console.log('\n📄 已生成测试脚本: test-cloud-function.js');
console.log('可以在浏览器控制台运行测试云函数连接');

console.log('\n🚨 如果部署后仍然卡住:');
console.log('1. 检查云函数是否部署成功');
console.log('2. 查看云函数日志是否有错误');
console.log('3. 确认环境ID是否正确');
console.log('4. 尝试重新初始化数据库');
