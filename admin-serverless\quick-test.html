<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 云开发SDK</title>
    <style>
        body {
            font-family: monospace;
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2d2d30;
            border-radius: 8px;
        }
        .log-container {
            background: #252526;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 20px;
            height: 500px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .log-line {
            margin: 2px 0;
            padding: 2px 0;
        }
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #569cd6; }
        .timestamp { color: #808080; font-size: 12px; }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        .btn:hover {
            background: #1177bb;
        }
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ 云开发SDK快速测试</h1>
            <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="runQuickTest()" id="testBtn">🚀 开始快速测试</button>
            <button class="btn" onclick="clearLog()">🗑️ 清空日志</button>
            <button class="btn" onclick="runFullDiagnostic()" id="diagnosticBtn">🔬 完整诊断</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-line info">📋 准备就绪，点击按钮开始测试...</div>
        </div>
    </div>

    <!-- 引入SDK和诊断工具 -->
    <script src="./js/cloudbase-js-sdk.min.js"></script>
    <script src="./js/diagnostic.js"></script>

    <script>
        let testRunning = false;

        // 日志函数
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = document.createElement('div');
            logLine.className = `log-line ${type}`;
            logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            container.appendChild(logLine);
            container.scrollTop = container.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = 
                '<div class="log-line info">📋 日志已清空，准备新的测试...</div>';
        }

        // 快速测试
        async function runQuickTest() {
            if (testRunning) return;
            
            testRunning = true;
            document.getElementById('testBtn').disabled = true;
            
            addLog('🚀 开始快速测试...', 'info');
            
            try {
                // 1. 检查SDK
                addLog('🔍 检查SDK状态...', 'info');
                if (typeof window.cloudbase === 'undefined' && typeof window.tcb === 'undefined') {
                    throw new Error('SDK未加载');
                }
                addLog('✅ SDK加载正常', 'success');

                // 2. 初始化
                addLog('🚀 初始化云开发...', 'info');
                const sdk = window.cloudbase || window.tcb;
                const app = sdk.init({ env: 'cloud1-5g6pvnpl88dc0142' });
                addLog('✅ 初始化成功', 'success');

                // 3. 身份验证
                addLog('🔐 执行身份验证...', 'info');
                const auth = app.auth();
                const user = await auth.signInAnonymously();
                addLog(`✅ 身份验证成功，用户ID: ${user.uid}`, 'success');

                // 4. 数据库连接
                addLog('📊 测试数据库连接...', 'info');
                const db = app.database();
                const result = await db.collection('categories').count();
                addLog(`✅ 数据库连接成功，分类数量: ${result.total}`, 'success');

                // 5. 数据查询
                addLog('📖 测试数据查询...', 'info');
                const queryResult = await db.collection('categories').get();
                addLog(`✅ 数据查询成功，返回 ${queryResult.data.length} 条记录`, 'success');

                // 6. 云函数调用
                addLog('☁️ 测试云函数调用...', 'info');
                const functionResult = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'getStats' }
                });
                addLog('✅ 云函数调用成功', 'success');

                addLog('🎉 快速测试全部通过！', 'success');
                
            } catch (error) {
                addLog(`❌ 测试失败: ${error.message}`, 'error');
            } finally {
                testRunning = false;
                document.getElementById('testBtn').disabled = false;
            }
        }

        // 完整诊断
        async function runFullDiagnostic() {
            if (testRunning) return;
            
            testRunning = true;
            document.getElementById('diagnosticBtn').disabled = true;
            
            addLog('🔬 开始完整诊断...', 'info');
            
            try {
                if (typeof CloudBaseDiagnostic === 'undefined') {
                    throw new Error('诊断工具未加载');
                }
                
                const diagnostic = new CloudBaseDiagnostic();
                
                // 重写诊断工具的日志方法，输出到页面
                const originalAddResult = diagnostic.addResult.bind(diagnostic);
                diagnostic.addResult = function(test, status, message, details) {
                    originalAddResult(test, status, message, details);
                    const type = status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning';
                    const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️';
                    addLog(`${icon} ${test}: ${message}`, type);
                    if (details) {
                        addLog(`   详情: ${JSON.stringify(details)}`, 'info');
                    }
                };
                
                await diagnostic.runAllDiagnostics();
                
                const total = diagnostic.results.length;
                const success = diagnostic.results.filter(r => r.status === 'success').length;
                const errors = diagnostic.results.filter(r => r.status === 'error').length;
                const warnings = diagnostic.results.filter(r => r.status === 'warning').length;
                
                addLog('', 'info');
                addLog('📋 诊断结果汇总:', 'info');
                addLog(`📊 总测试数: ${total}`, 'info');
                addLog(`✅ 成功: ${success}`, 'success');
                addLog(`❌ 失败: ${errors}`, errors > 0 ? 'error' : 'info');
                addLog(`⚠️ 警告: ${warnings}`, warnings > 0 ? 'warning' : 'info');
                addLog(`📈 成功率: ${Math.round(success / total * 100)}%`, success > errors ? 'success' : 'error');
                
                if (success === total) {
                    addLog('🎉 所有测试都通过了！系统运行正常', 'success');
                } else if (errors === 0) {
                    addLog('✨ 核心功能正常，有一些警告需要注意', 'warning');
                } else {
                    addLog('⚠️ 发现一些问题，请检查错误信息', 'error');
                }
                
            } catch (error) {
                addLog(`❌ 诊断失败: ${error.message}`, 'error');
            } finally {
                testRunning = false;
                document.getElementById('diagnosticBtn').disabled = false;
            }
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('🌐 页面加载完成', 'info');
            addLog('💡 提示: 点击"开始快速测试"进行基础功能测试', 'info');
            addLog('💡 提示: 点击"完整诊断"进行详细的系统检查', 'info');
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                runQuickTest();
            } else if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                runFullDiagnostic();
            } else if (e.key === 'Escape') {
                clearLog();
            }
        });

        // 显示快捷键提示
        setTimeout(() => {
            addLog('⌨️ 快捷键: Ctrl+Enter=快速测试, Ctrl+D=完整诊断, Esc=清空日志', 'info');
        }, 1000);
    </script>
</body>
</html>
