/**
 * 部署验证脚本
 * 检查实时同步功能的所有组件是否正确部署
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 开始验证实时同步功能部署...')

// 验证结果
const verificationResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
}

function addResult(type, component, message, status) {
  verificationResults.details.push({
    type,
    component,
    message,
    status,
    timestamp: new Date().toISOString()
  })
  
  if (status === 'pass') {
    verificationResults.passed++
    console.log(`✅ [${component}] ${message}`)
  } else if (status === 'fail') {
    verificationResults.failed++
    console.log(`❌ [${component}] ${message}`)
  } else if (status === 'warning') {
    verificationResults.warnings++
    console.log(`⚠️ [${component}] ${message}`)
  }
}

// 1. 验证文件结构
console.log('\n📁 验证文件结构...')

const requiredFiles = [
  'cloudfunctions/webAdminAPI/index.js',
  'utils/newDataManager.js',
  'utils/syncStatusManager.js',
  'admin/index.html',
  'admin/test-realtime-sync.html',
  'admin/test-sync-functions.html',
  'admin/init-database.html',
  'pages/index/index.js',
  'pages/index/index.wxml',
  'pages/index/index.wxss',
  'app.js'
]

requiredFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    addResult('file', 'FileSystem', `文件存在: ${filePath}`, 'pass')
  } else {
    addResult('file', 'FileSystem', `文件缺失: ${filePath}`, 'fail')
  }
})

// 2. 验证云函数代码
console.log('\n☁️ 验证云函数代码...')

try {
  const webAdminAPIContent = fs.readFileSync('cloudfunctions/webAdminAPI/index.js', 'utf8')
  
  // 检查必要的函数
  const requiredFunctions = [
    'initSyncNotifications',
    'createSyncNotification',
    'updateSyncNotificationStatus',
    'getSyncNotifications'
  ]
  
  requiredFunctions.forEach(funcName => {
    if (webAdminAPIContent.includes(funcName)) {
      addResult('function', 'webAdminAPI', `函数存在: ${funcName}`, 'pass')
    } else {
      addResult('function', 'webAdminAPI', `函数缺失: ${funcName}`, 'fail')
    }
  })
  
  // 检查同步通知相关代码
  if (webAdminAPIContent.includes('sync_notifications')) {
    addResult('database', 'webAdminAPI', '包含sync_notifications集合操作', 'pass')
  } else {
    addResult('database', 'webAdminAPI', '缺少sync_notifications集合操作', 'fail')
  }
  
} catch (error) {
  addResult('function', 'webAdminAPI', `读取云函数文件失败: ${error.message}`, 'fail')
}

// 3. 验证数据管理器
console.log('\n📊 验证数据管理器...')

try {
  const dataManagerContent = fs.readFileSync('utils/newDataManager.js', 'utf8')
  
  // 检查实时监听相关代码
  const realtimeFeatures = [
    'initRealtimeWatchers',
    'watchSyncNotifications',
    'handleSyncNotification',
    'refreshCategoriesCache',
    'refreshEmojisCache',
    'refreshBannersCache'
  ]
  
  realtimeFeatures.forEach(feature => {
    if (dataManagerContent.includes(feature)) {
      addResult('feature', 'DataManager', `实时功能存在: ${feature}`, 'pass')
    } else {
      addResult('feature', 'DataManager', `实时功能缺失: ${feature}`, 'fail')
    }
  })
  
  // 检查同步状态管理器集成
  if (dataManagerContent.includes('syncStatusManager')) {
    addResult('integration', 'DataManager', '已集成同步状态管理器', 'pass')
  } else {
    addResult('integration', 'DataManager', '未集成同步状态管理器', 'fail')
  }
  
} catch (error) {
  addResult('feature', 'DataManager', `读取数据管理器文件失败: ${error.message}`, 'fail')
}

// 4. 验证同步状态管理器
console.log('\n📡 验证同步状态管理器...')

try {
  const syncStatusContent = fs.readFileSync('utils/syncStatusManager.js', 'utf8')
  
  // 检查核心功能
  const statusFeatures = [
    'updateConnectionStatus',
    'updateSyncStatus',
    'onStatusChange',
    'getFormattedLastSyncTime'
  ]
  
  statusFeatures.forEach(feature => {
    if (syncStatusContent.includes(feature)) {
      addResult('feature', 'SyncStatusManager', `状态功能存在: ${feature}`, 'pass')
    } else {
      addResult('feature', 'SyncStatusManager', `状态功能缺失: ${feature}`, 'fail')
    }
  })
  
} catch (error) {
  addResult('feature', 'SyncStatusManager', `读取同步状态管理器失败: ${error.message}`, 'fail')
}

// 5. 验证管理后台
console.log('\n🖥️ 验证管理后台...')

try {
  const adminContent = fs.readFileSync('admin/index.html', 'utf8')
  
  // 检查实时管理器集成
  if (adminContent.includes('RealTimeManager')) {
    addResult('integration', 'AdminPanel', '已集成实时管理器', 'pass')
  } else {
    addResult('integration', 'AdminPanel', '未集成实时管理器', 'fail')
  }
  
  // 检查自动同步功能
  if (adminContent.includes('triggerAutoSync')) {
    addResult('feature', 'AdminPanel', '包含自动同步功能', 'pass')
  } else {
    addResult('feature', 'AdminPanel', '缺少自动同步功能', 'fail')
  }
  
  // 检查同步状态UI
  if (adminContent.includes('sync-status-panel')) {
    addResult('ui', 'AdminPanel', '包含同步状态面板', 'pass')
  } else {
    addResult('ui', 'AdminPanel', '缺少同步状态面板', 'fail')
  }
  
} catch (error) {
  addResult('integration', 'AdminPanel', `读取管理后台文件失败: ${error.message}`, 'fail')
}

// 6. 验证小程序页面
console.log('\n📱 验证小程序页面...')

try {
  const indexJSContent = fs.readFileSync('pages/index/index.js', 'utf8')
  const indexWXMLContent = fs.readFileSync('pages/index/index.wxml', 'utf8')
  
  // 检查实时数据更新回调
  if (indexJSContent.includes('onRealtimeDataUpdate')) {
    addResult('callback', 'MiniprogramPage', '包含实时数据更新回调', 'pass')
  } else {
    addResult('callback', 'MiniprogramPage', '缺少实时数据更新回调', 'fail')
  }
  
  // 检查同步状态监听
  if (indexJSContent.includes('registerSyncStatusListener')) {
    addResult('listener', 'MiniprogramPage', '包含同步状态监听', 'pass')
  } else {
    addResult('listener', 'MiniprogramPage', '缺少同步状态监听', 'fail')
  }
  
  // 检查同步状态UI
  if (indexWXMLContent.includes('sync-status-bar')) {
    addResult('ui', 'MiniprogramPage', '包含同步状态栏', 'pass')
  } else {
    addResult('ui', 'MiniprogramPage', '缺少同步状态栏', 'fail')
  }
  
} catch (error) {
  addResult('callback', 'MiniprogramPage', `读取小程序页面文件失败: ${error.message}`, 'fail')
}

// 7. 验证测试文件
console.log('\n🧪 验证测试文件...')

const testFiles = [
  'admin/test-realtime-sync.html',
  'admin/test-sync-functions.html',
  'admin/init-database.html'
]

testFiles.forEach(testFile => {
  if (fs.existsSync(testFile)) {
    addResult('test', 'TestFiles', `测试文件存在: ${testFile}`, 'pass')
  } else {
    addResult('test', 'TestFiles', `测试文件缺失: ${testFile}`, 'warning')
  }
})

// 8. 验证配置文件
console.log('\n⚙️ 验证配置文件...')

try {
  const appJSContent = fs.readFileSync('app.js', 'utf8')
  
  if (appJSContent.includes('syncStatusManager')) {
    addResult('config', 'AppJS', '已在app.js中初始化同步状态管理器', 'pass')
  } else {
    addResult('config', 'AppJS', '未在app.js中初始化同步状态管理器', 'fail')
  }
  
} catch (error) {
  addResult('config', 'AppJS', `读取app.js失败: ${error.message}`, 'fail')
}

// 输出验证结果
console.log('\n📊 验证结果汇总:')
console.log(`✅ 通过: ${verificationResults.passed}`)
console.log(`❌ 失败: ${verificationResults.failed}`)
console.log(`⚠️ 警告: ${verificationResults.warnings}`)

const totalChecks = verificationResults.passed + verificationResults.failed + verificationResults.warnings
const successRate = ((verificationResults.passed / totalChecks) * 100).toFixed(1)
console.log(`📈 成功率: ${successRate}%`)

// 生成验证报告
const report = {
  timestamp: new Date().toISOString(),
  summary: {
    total: totalChecks,
    passed: verificationResults.passed,
    failed: verificationResults.failed,
    warnings: verificationResults.warnings,
    successRate: successRate + '%'
  },
  details: verificationResults.details
}

try {
  fs.writeFileSync('verification-report.json', JSON.stringify(report, null, 2))
  console.log('\n📄 验证报告已保存到 verification-report.json')
} catch (error) {
  console.log(`⚠️ 保存验证报告失败: ${error.message}`)
}

// 输出部署建议
console.log('\n💡 部署建议:')

if (verificationResults.failed > 0) {
  console.log('❌ 发现关键问题，请修复后再部署:')
  verificationResults.details
    .filter(item => item.status === 'fail')
    .forEach(item => {
      console.log(`   - [${item.component}] ${item.message}`)
    })
} else {
  console.log('✅ 所有关键组件验证通过，可以进行部署')
}

if (verificationResults.warnings > 0) {
  console.log('\n⚠️ 注意事项:')
  verificationResults.details
    .filter(item => item.status === 'warning')
    .forEach(item => {
      console.log(`   - [${item.component}] ${item.message}`)
    })
}

console.log('\n🚀 验证完成！')

// 返回退出码
process.exit(verificationResults.failed > 0 ? 1 : 0)
