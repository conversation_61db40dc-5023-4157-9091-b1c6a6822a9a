// app.js
const { AuthManager } = require('./utils/authManager.js')
const { DataSync } = require('./utils/dataSync.js')
const { StateManager } = require('./utils/stateManager.js')
const { PerformanceMonitor } = require('./utils/performanceMonitor.js')
const { ErrorHandler } = require('./utils/errorHandler.js')
const { RequestOptimizer } = require('./utils/requestOptimizer.js')
const { LazyImageLoader } = require('./utils/lazyImageLoader.js')
const ResourceManager = require('./utils/resourceManager.js')
const GlobalErrorHandler = require('./utils/globalErrorHandler.js')
const NetworkOptimizer = require('./utils/networkOptimizer.js')
const UserPreferences = require('./utils/userPreferences.js')
const ShareManager = require('./utils/shareManager.js')
const { EnvironmentConfig } = require('./config/environment.js')
const { CloudConfig } = require('./config/cloud.js')
const { HealthMonitor } = require('./utils/healthMonitor.js')
const { LogManager } = require('./utils/logManager.js')
const { DatabaseInit } = require('./utils/databaseInit.js')
const { DataManager } = require('./utils/newDataManager.js')
const { ConfigSync } = require('./utils/configSync.js')
const { RealtimeSync } = require('./utils/realtimeSync.js')
const { syncStatusManager } = require('./utils/syncStatusManager.js')

App({
  onLaunch() {
    console.log('🚀 小程序启动')

    // 1. 初始化环境配置（最先初始化）
    EnvironmentConfig.init()

    // 2. 初始化日志管理器
    LogManager.init()

    // 3. 初始化错误处理器
    ErrorHandler.init()

    // 记录启动日志
    LogManager.info('小程序启动', {
      environment: EnvironmentConfig.currentEnv,
      version: this.globalData.version,
      timestamp: Date.now()
    })

    // 4. 初始化云开发（只初始化一次）
    this.initCloud()

    // 5. 延迟初始化核心服务（等待云开发完成）
    setTimeout(() => {
      this.initCoreServices()
    }, 2000)

    // 6. 初始化监控服务
    this.initMonitoringServices()

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 添加登录状态监听器
    AuthManager.addLoginListener((isLoggedIn, userInfo) => {
      this.globalData.isLoggedIn = isLoggedIn
      this.globalData.userInfo = userInfo
      LogManager.info('全局登录状态更新', { isLoggedIn, nickName: userInfo?.nickName })
    })

    // 检查登录状态
    this.checkLoginStatus()

    LogManager.info('小程序初始化完成')
  },

  /**
   * 初始化云开发
   */
  initCloud() {
    // 防止重复初始化
    if (this.globalData.cloudInitialized) {
      console.log('⚠️ 云开发已初始化，跳过重复初始化')
      return
    }

    if (!wx.cloud) {
      console.error('❌ 请使用 2.2.3 或以上的基础库以使用云能力')
      return
    }

    try {
      // 标准云开发初始化
      const cloudEnv = 'cloud1-5g6pvnpl88dc0142'

      console.log('☁️ 开始初始化云开发...')
      console.log('   环境ID:', cloudEnv)

      wx.cloud.init({
        env: cloudEnv,
        traceUser: true
      })

      this.globalData.cloudInitialized = true
      this.globalData.cloudEnv = cloudEnv

      console.log('✅ 云开发初始化成功')
      LogManager.info('云开发初始化成功', { env: cloudEnv })

      // 测试云开发连接
      setTimeout(() => {
        this.testCloudConnection()
      }, 1000)

      // 立即初始化数据库（确保数据可用）
      setTimeout(async () => {
        try {
          console.log('🗄️ 开始数据库完整初始化...')
          const dbResults = await DatabaseInit.fullInitialize()

          if (dbResults.success) {
            LogManager.info('数据库初始化完成', dbResults)
            this.globalData.databaseInitialized = true
          } else {
            LogManager.warn('数据库初始化部分失败，尝试修复...', dbResults)
            // 尝试修复数据问题
            const repairResult = await DatabaseInit.repairData()
            this.globalData.databaseInitialized = repairResult.success
            this.globalData.databaseError = dbResults.error
          }
        } catch (error) {
          LogManager.error('数据库初始化失败', error)
          this.globalData.databaseInitialized = false
          this.globalData.databaseError = error.message

          // 在开发环境中显示详细错误信息
          if (EnvironmentConfig.isDevelopment()) {
            console.error('💡 数据库初始化失败详情:', error)
            console.log('💡 建议检查云开发配置和数据库权限')
          }
        }
      }, 1000) // 缩短到1秒，更快初始化

      // 在开发环境中不显示提示，避免干扰调试
      if (!EnvironmentConfig.isDevelopment()) {
        wx.showToast({
          title: '云服务已连接',
          icon: 'success',
          duration: 2000
        })
      }
    } catch (error) {
      LogManager.error('云开发初始化失败', error)
      this.globalData.cloudInitialized = false

      // 在开发环境中不显示提示，避免干扰调试
      if (!EnvironmentConfig.isDevelopment()) {
        wx.showToast({
          title: '使用离线模式',
          icon: 'none',
          duration: 2000
        })
      }

      console.log('⚠️ 云开发初始化失败，将使用离线模式')
    }
  },

  /**
   * 初始化核心服务
   */
  initCoreServices() {
    console.log('🚀 开始初始化核心服务...')

    try {
      // 初始化请求优化器
      console.log('🔄 开始初始化RequestOptimizer...')
      RequestOptimizer.init({
        maxConcurrent: EnvironmentConfig.get('maxConcurrent', 5),
        cacheTimeout: EnvironmentConfig.getCacheTimeout(),
        retryCount: EnvironmentConfig.getMaxRetries(),
        timeout: EnvironmentConfig.getApiTimeout()
      })
      console.log('✅ RequestOptimizer初始化完成')

      // 初始化图片懒加载
      console.log('🔄 开始初始化LazyImageLoader...')
      LazyImageLoader.init({
        rootMargin: '100px',
        threshold: 0.1,
        maxCacheSize: EnvironmentConfig.get('imageCacheSize', 100)
      })
      console.log('✅ LazyImageLoader初始化完成')

      // 初始化全局错误处理器（最优先）
      console.log('🔄 开始初始化GlobalErrorHandler...')
      GlobalErrorHandler.init({
        enableReporting: EnvironmentConfig.isProduction(),
        userFriendlyMessages: true,
        autoRetry: true
      })
      console.log('✅ GlobalErrorHandler初始化完成')

      // 初始化资源管理器
      console.log('🔄 开始初始化ResourceManager...')
      ResourceManager.init()
      console.log('✅ ResourceManager初始化完成')

      // 初始化状态管理器
      console.log('🔄 开始初始化StateManager...')
      StateManager.init()
      console.log('✅ StateManager初始化完成')

      // 初始化认证管理器
      console.log('🔄 开始初始化AuthManager...')
      AuthManager.init()
      console.log('✅ AuthManager初始化完成')

      // 初始化网络优化器
      console.log('🔄 开始初始化NetworkOptimizer...')
      NetworkOptimizer.init({
        enableCache: true,
        enableDeduplication: true,
        enableRetry: true,
        concurrentLimit: 3
      })
      console.log('✅ NetworkOptimizer初始化完成')

      // 初始化用户偏好设置
      console.log('🔄 开始初始化UserPreferences...')
      UserPreferences.init()
      console.log('✅ UserPreferences初始化完成')

      // 初始化数据管理器
      console.log('🔄 开始初始化DataManager...')
      DataManager.init()
      console.log('✅ DataManager初始化完成')

      // 初始化同步状态管理器
      console.log('🔄 开始初始化SyncStatusManager...')
      syncStatusManager.init()
      console.log('✅ SyncStatusManager初始化完成')

      // 初始化数据同步管理器
      console.log('🔄 开始初始化DataSync...')
      DataSync.init()
      console.log('✅ DataSync初始化完成')

      // 初始化分享管理器
      console.log('🔄 开始初始化ShareManager...')
      ShareManager.init({
        appName: '表情包小程序',
        trackShares: true
      })
      console.log('✅ ShareManager初始化完成')

      // 初始化配置同步管理器
      console.log('🔄 开始初始化ConfigSync...')
      ConfigSync.init()
      console.log('✅ ConfigSync初始化完成')

      // 设置全局初始化完成标志
      this.globalData.coreServicesInitialized = true
      console.log('🎉 核心服务初始化完成')

      LogManager.info('核心服务初始化完成')
    } catch (error) {
      console.error('❌ 核心服务初始化失败:', error)
      LogManager.error('核心服务初始化失败', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })

      // 设置初始化失败标志，但不阻止应用启动
      this.globalData.coreServicesInitialized = false
      this.globalData.initializationError = error.message
    }
  },

  /**
   * 初始化监控服务
   */
  initMonitoringServices() {
    try {
      // 初始化性能监控
      if (EnvironmentConfig.get('enablePerformanceMonitor', true)) {
        PerformanceMonitor.init()
      }

      // 初始化健康监控
      if (EnvironmentConfig.get('enableHealthMonitor', true)) {
        HealthMonitor.init({
          checkInterval: EnvironmentConfig.get('healthCheckInterval', 60000)
        })
      }

      // 初始化实时同步（延迟启动，等待云开发初始化完成）
      setTimeout(() => {
        try {
          RealtimeSync.init({
            enableRealtime: EnvironmentConfig.isProduction(),
            syncInterval: 30000,
            maxRetries: 3,
            backgroundSync: true
          })
          this.globalData.realtimeSyncInitialized = true
          LogManager.info('实时同步服务初始化完成')
        } catch (error) {
          LogManager.error('实时同步服务初始化失败', error)
          this.globalData.realtimeSyncInitialized = false
        }
      }, 5000) // 5秒后启动，确保云开发和数据库都已初始化

      LogManager.info('监控服务初始化完成')
    } catch (error) {
      LogManager.error('监控服务初始化失败', error)
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const isLoggedIn = AuthManager.checkLoginStatus()
    this.globalData.isLoggedIn = isLoggedIn
    this.globalData.userInfo = AuthManager.userInfo

    if (isLoggedIn) {
      console.log('用户已登录:', AuthManager.userInfo?.nickName)
    } else {
      console.log('用户未登录')
    }
  },

  globalData: {
    isLoggedIn: false,
    userInfo: null,
    likedEmojis: new Set(),
    collectedEmojis: new Set(),
    cloudInitialized: false, // 云开发初始化状态
    systemConfig: null, // 系统配置
    // 数据同步队列
    _syncQueue: [],
    _isProcessing: false
  },

  // 安全的数据同步方法
  enqueueSyncOperation(operation) {
    this.globalData._syncQueue.push(operation)
    this.processSyncQueue()
  },

  processSyncQueue() {
    if (this.globalData._isProcessing || this.globalData._syncQueue.length === 0) {
      return
    }

    this.globalData._isProcessing = true

    // 批量处理同步操作，避免频繁更新
    const operations = this.globalData._syncQueue.splice(0)

    try {
      operations.forEach(op => op())
    } finally {
      this.globalData._isProcessing = false
    }
  },

  /**
   * 获取认证管理器实例
   */
  getAuthManager() {
    return AuthManager
  },

  /**
   * 检查是否需要登录
   */
  async requireLogin(showModal = true) {
    return await AuthManager.requireLogin(showModal)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return AuthManager.getCurrentUser()
  },

  /**
   * 测试云开发连接
   */
  async testCloudConnection() {
    try {
      console.log('🔍 测试云开发连接...')

      // 测试基本的云函数调用
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })

      console.log('✅ 云开发连接测试成功:', result)
      return true
    } catch (error) {
      console.error('❌ 云开发连接测试失败:', error)

      // 显示错误提示
      wx.showModal({
        title: '云开发连接失败',
        content: `错误信息: ${error.errMsg || error.message}\n\n请检查:\n1. 云开发环境是否正确\n2. 云函数是否已部署\n3. 网络连接是否正常`,
        showCancel: false,
        confirmText: '知道了'
      })

      return false
    }
  }
})