#!/usr/bin/env node

/**
 * 云开发快速修复脚本
 * 自动修复常见的云开发配置问题
 */

const fs = require('fs')
const path = require('path')

console.log('⚡ 云开发快速修复脚本')
console.log('=' .repeat(40))

class QuickCloudFixer {
  constructor() {
    this.fixes = []
  }

  async run() {
    console.log('🚀 开始快速修复...\n')

    // 1. 修复云函数package.json
    await this.fixCloudFunctionPackages()

    // 2. 修复project.config.json
    await this.fixProjectConfig()

    // 3. 创建云函数部署脚本
    await this.createDeployScript()

    // 4. 输出结果
    this.outputResults()
  }

  /**
   * 修复云函数package.json
   */
  async fixCloudFunctionPackages() {
    console.log('📦 修复云函数依赖配置...')

    const cloudFunctionDir = 'cloudfunctions'
    if (!fs.existsSync(cloudFunctionDir)) {
      console.log('   ⚠️ cloudfunctions 目录不存在，跳过')
      return
    }

    const functions = fs.readdirSync(cloudFunctionDir).filter(item => {
      const itemPath = path.join(cloudFunctionDir, item)
      return fs.statSync(itemPath).isDirectory()
    })

    for (const funcName of functions) {
      const funcDir = path.join(cloudFunctionDir, funcName)
      const packagePath = path.join(funcDir, 'package.json')

      try {
        let packageJson = {}
        let needsUpdate = false

        // 读取现有package.json或创建新的
        if (fs.existsSync(packagePath)) {
          packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
        } else {
          needsUpdate = true
        }

        // 确保基本结构
        if (!packageJson.name) {
          packageJson.name = funcName
          needsUpdate = true
        }

        if (!packageJson.version) {
          packageJson.version = '1.0.0'
          needsUpdate = true
        }

        if (!packageJson.description) {
          packageJson.description = `${funcName} 云函数`
          needsUpdate = true
        }

        if (!packageJson.main) {
          packageJson.main = 'index.js'
          needsUpdate = true
        }

        // 确保依赖
        if (!packageJson.dependencies) {
          packageJson.dependencies = {}
          needsUpdate = true
        }

        if (!packageJson.dependencies['wx-server-sdk']) {
          packageJson.dependencies['wx-server-sdk'] = '~2.6.3'
          needsUpdate = true
        }

        // 确保脚本
        if (!packageJson.scripts) {
          packageJson.scripts = {
            test: 'echo "Error: no test specified" && exit 1'
          }
          needsUpdate = true
        }

        if (needsUpdate) {
          fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2))
          this.fixes.push(`✅ 修复了云函数 ${funcName} 的package.json`)
          console.log(`   ✅ 修复了云函数 ${funcName}`)
        }

      } catch (error) {
        console.log(`   ❌ 修复云函数 ${funcName} 失败: ${error.message}`)
      }
    }
  }

  /**
   * 修复project.config.json
   */
  async fixProjectConfig() {
    console.log('⚙️ 修复项目配置...')

    const configPath = 'project.config.json'
    let config = {}
    let needsUpdate = false

    try {
      if (fs.existsSync(configPath)) {
        config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      } else {
        needsUpdate = true
      }

      // 确保云函数根目录配置
      if (!config.cloudfunctionRoot) {
        config.cloudfunctionRoot = 'cloudfunctions/'
        needsUpdate = true
      }

      // 确保基本配置
      if (!config.description) {
        config.description = '表情包小程序项目配置文件'
        needsUpdate = true
      }

      if (!config.packOptions) {
        config.packOptions = {
          ignore: []
        }
        needsUpdate = true
      }

      if (!config.setting) {
        config.setting = {
          urlCheck: false,
          es6: true,
          enhance: true,
          postcss: true,
          preloadBackgroundData: false,
          minified: true,
          newFeature: false,
          coverView: true,
          nodeModules: false,
          autoAudits: false,
          showShadowRootInWxmlPanel: true,
          scopeDataCheck: false,
          uglifyFileName: false,
          checkInvalidKey: true,
          checkSiteMap: true,
          uploadWithSourceMap: true,
          compileHotReLoad: false,
          lazyloadPlaceholderEnable: false,
          useMultiFrameRuntime: true,
          useApiHook: true,
          useApiHostProcess: true,
          babelSetting: {
            ignore: [],
            disablePlugins: [],
            outputPath: ""
          },
          enableEngineNative: false,
          useIsolateContext: true,
          userConfirmedBundleSwitch: false,
          packNpmManually: false,
          packNpmRelationList: [],
          minifyWXSS: true,
          disableUseStrict: false,
          minifyWXML: true,
          showES6CompileOption: false,
          useCompilerPlugins: false
        }
        needsUpdate = true
      }

      if (!config.compileType) {
        config.compileType = 'miniprogram'
        needsUpdate = true
      }

      if (!config.libVersion) {
        config.libVersion = '2.19.4'
        needsUpdate = true
      }

      if (!config.appid) {
        config.appid = 'touristappid'
        needsUpdate = true
      }

      if (!config.projectname) {
        config.projectname = '表情包小程序'
        needsUpdate = true
      }

      if (!config.debugOptions) {
        config.debugOptions = {
          hidedInDevtools: []
        }
        needsUpdate = true
      }

      if (!config.scripts) {
        config.scripts = {}
        needsUpdate = true
      }

      if (!config.staticServerOptions) {
        config.staticServerOptions = {
          baseURL: "",
          servePath: ""
        }
        needsUpdate = true
      }

      if (!config.isGameTourist) {
        config.isGameTourist = false
        needsUpdate = true
      }

      if (!config.condition) {
        config.condition = {
          search: {
            list: []
          },
          conversation: {
            list: []
          },
          game: {
            list: []
          },
          plugin: {
            list: []
          },
          gamePlugin: {
            list: []
          },
          miniprogram: {
            list: []
          }
        }
        needsUpdate = true
      }

      if (needsUpdate) {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2))
        this.fixes.push('✅ 修复了project.config.json配置')
        console.log('   ✅ 修复了项目配置文件')
      }

    } catch (error) {
      console.log(`   ❌ 修复项目配置失败: ${error.message}`)
    }
  }

  /**
   * 创建云函数部署脚本
   */
  async createDeployScript() {
    console.log('📜 创建部署脚本...')

    const deployScript = `#!/usr/bin/env node

/**
 * 云函数批量部署脚本
 * 使用方法：在微信开发者工具中运行此脚本
 */

console.log('🚀 开始批量部署云函数...')

// 需要部署的云函数列表
const functions = [
  'login',
  'dataAPI', 
  'getCategories',
  'getEmojiList',
  'getBanners',
  'getEmojiDetail',
  'toggleLike',
  'toggleCollect',
  'getUserStats',
  'initDatabase'
]

console.log('📋 将部署以下云函数:')
functions.forEach((func, index) => {
  console.log(\`\${index + 1}. \${func}\`)
})

console.log('\\n💡 部署步骤:')
console.log('1. 在微信开发者工具中打开项目')
console.log('2. 点击工具栏的"云开发"按钮')
console.log('3. 在云开发控制台中选择"云函数"')
console.log('4. 逐个右键点击云函数文件夹，选择"上传并部署"')
console.log('5. 或者使用命令行工具批量部署')

console.log('\\n🔧 命令行部署（需要安装@cloudbase/cli）:')
functions.forEach(func => {
  console.log(\`cloudbase functions:deploy \${func}\`)
})

console.log('\\n✅ 部署完成后，请在云开发控制台确认所有函数状态正常')
`

    try {
      fs.writeFileSync('deploy-functions.js', deployScript)
      this.fixes.push('✅ 创建了云函数部署脚本 deploy-functions.js')
      console.log('   ✅ 创建了部署脚本')
    } catch (error) {
      console.log(`   ❌ 创建部署脚本失败: ${error.message}`)
    }
  }

  /**
   * 输出结果
   */
  outputResults() {
    console.log('\\n' + '='.repeat(40))
    console.log('📊 修复完成')
    console.log('='.repeat(40))

    if (this.fixes.length > 0) {
      console.log('\\n✅ 完成的修复:')
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix}`)
      })
    }

    console.log('\\n📋 下一步操作:')
    console.log('1. 在微信开发者工具中打开项目')
    console.log('2. 确保云开发服务已开通')
    console.log('3. 运行 node deploy-functions.js 查看部署指南')
    console.log('4. 部署所有云函数')
    console.log('5. 在小程序中测试功能')

    console.log('\\n🔍 如果问题仍然存在:')
    console.log('1. 运行 node fix-cloud-issues.js 进行详细诊断')
    console.log('2. 检查云开发控制台中的错误日志')
    console.log('3. 确认环境ID配置正确')
    console.log('4. 检查网络连接')
  }
}

// 运行快速修复
const fixer = new QuickCloudFixer()
fixer.run().catch(error => {
  console.error('❌ 快速修复失败:', error)
  process.exit(1)
})
