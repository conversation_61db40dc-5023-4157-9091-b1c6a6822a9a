// 测试分类创建功能，重点检查渐变色保存
const { chromium } = require('playwright');

async function testCategoryCreationGradient() {
    console.log('🎨 测试分类创建功能 - 重点检查渐变色保存...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类') || text.includes('渐变') || text.includes('保存') || text.includes('gradient')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 点击添加分类按钮');
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        } else {
            console.log('❌ 未找到添加分类按钮');
            return { success: false, error: '未找到添加分类按钮' };
        }
        
        console.log('\n📍 检查分类创建表单');
        
        // 检查表单元素
        const formElements = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };

            const nameInput = modal.querySelector('#category-name');
            const iconInput = modal.querySelector('#category-icon');
            const descInput = modal.querySelector('#category-description');
            const gradientSelect = modal.querySelector('#category-gradient-preset');
            const saveBtn = modal.querySelector('button[type="submit"]');
            
            return {
                modalExists: true,
                nameInputExists: !!nameInput,
                nameInputReadonly: nameInput ? nameInput.readOnly : null,
                iconInputExists: !!iconInput,
                iconInputReadonly: iconInput ? iconInput.readOnly : null,
                descInputExists: !!descInput,
                gradientSelectExists: !!gradientSelect,
                saveBtnExists: !!saveBtn,
                modalHTML: modal.innerHTML.substring(0, 1000)
            };
        });
        
        console.log('📊 表单元素检查:');
        console.log('弹窗存在:', formElements.modalExists);
        console.log('名称输入框存在:', formElements.nameInputExists);
        console.log('图标输入框存在:', formElements.iconInputExists);
        console.log('图标输入框只读:', formElements.iconInputReadonly);
        console.log('描述输入框存在:', formElements.descInputExists);
        console.log('渐变选择框存在:', formElements.gradientSelectExists);
        console.log('保存按钮存在:', formElements.saveBtnExists);
        
        if (!formElements.modalExists) {
            console.log('❌ 分类创建弹窗未出现');
            return { success: false, error: '分类创建弹窗未出现' };
        }
        
        console.log('\n📍 填写分类表单');
        
        // 填写名称
        const nameInput = await page.locator('#category-name').first();
        if (await nameInput.isVisible()) {
            await nameInput.fill('测试分类-渐变功能验证');
            console.log('✅ 已填写分类名称');
        } else {
            console.log('❌ 名称输入框不可见');
        }

        // 填写描述
        const descInput = await page.locator('#category-description').first();
        if (await descInput.isVisible()) {
            await descInput.fill('这是一个用于验证渐变色保存功能的测试分类');
            console.log('✅ 已填写分类描述');
        } else {
            console.log('⚠️ 描述输入框不可见');
        }
        
        // 处理图标输入（如果是只读的，需要点击选择）
        const iconInput = await page.locator('#category-icon').first();
        if (await iconInput.isVisible()) {
            const isReadonly = await iconInput.evaluate(el => el.readOnly);
            if (isReadonly) {
                console.log('图标输入框是只读的，尝试点击选择图标');
                await iconInput.click();
                await page.waitForTimeout(1000);
                
                // 查找图标选择器
                const iconSelector = await page.locator('.icon-selector, .emoji-picker').first();
                if (await iconSelector.isVisible()) {
                    const firstIcon = await page.locator('.icon-option, .emoji-option').first();
                    if (await firstIcon.isVisible()) {
                        await firstIcon.click();
                        console.log('✅ 已选择图标');
                    }
                } else {
                    // 如果没有图标选择器，尝试直接设置值
                    await page.evaluate(() => {
                        const iconInput = document.querySelector('#category-icon');
                        if (iconInput) {
                            iconInput.value = '🎨';
                            iconInput.dispatchEvent(new Event('input', { bubbles: true }));
                        }
                    });
                    console.log('✅ 已设置图标值');
                }
            } else {
                await iconInput.fill('🎨');
                console.log('✅ 已填写图标');
            }
        }
        
        // 选择渐变色
        console.log('\n📍 选择渐变色');
        const gradientSelect = await page.locator('#category-gradient-preset').first();
        if (await gradientSelect.isVisible()) {
            // 获取渐变选项
            const gradientOptions = await page.evaluate(() => {
                const select = document.querySelector('#category-gradient-preset');
                if (!select) return [];
                return Array.from(select.options).map(option => ({
                    value: option.value,
                    text: option.textContent
                }));
            });
            
            console.log('可用的渐变选项:', gradientOptions);
            
            if (gradientOptions.length > 1) {
                // 选择一个非默认的渐变
                const selectedGradient = gradientOptions.find(opt => opt.value !== '' && opt.value !== 'none') || gradientOptions[1];
                await gradientSelect.selectOption(selectedGradient.value);
                console.log(`✅ 已选择渐变: ${selectedGradient.text} (${selectedGradient.value})`);
                await page.waitForTimeout(2000);
                
                // 检查渐变预览
                const gradientPreview = await page.evaluate(() => {
                    const preview = document.querySelector('.gradient-preview, [style*="background"]');
                    return preview ? {
                        exists: true,
                        style: preview.style.background || preview.style.backgroundImage,
                        visible: preview.offsetWidth > 0 && preview.offsetHeight > 0
                    } : { exists: false };
                });
                
                console.log('渐变预览状态:', gradientPreview);
            } else {
                console.log('❌ 没有可用的渐变选项');
            }
        } else {
            console.log('❌ 未找到渐变选择框');
        }
        
        console.log('\n📍 保存分类');

        // 等待一下确保表单完全渲染
        await page.waitForTimeout(2000);

        // 检查保存按钮
        const saveBtnCheck = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };

            const submitBtn = modal.querySelector('button[type="submit"]');
            const allButtons = Array.from(modal.querySelectorAll('button')).map(btn => ({
                type: btn.type,
                text: btn.textContent?.trim(),
                visible: btn.offsetWidth > 0 && btn.offsetHeight > 0
            }));
            const saveBtn = allButtons.find(btn => btn.text?.includes('保存'));

            return {
                modalExists: true,
                submitBtnExists: !!submitBtn,
                saveBtnExists: !!saveBtn,
                allButtons: allButtons
            };
        });

        console.log('保存按钮检查:', saveBtnCheck);

        // 点击保存按钮
        const saveBtn = await page.locator('button[type="submit"]').first();
        if (await saveBtn.isVisible()) {
            await saveBtn.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000); // 等待保存完成
        } else {
            // 尝试其他选择器
            const altSaveBtn = await page.locator('button:has-text("保存分类")').first();
            if (await altSaveBtn.isVisible()) {
                await altSaveBtn.click();
                console.log('✅ 已点击保存按钮（备用选择器）');
                await page.waitForTimeout(8000);
            } else {
                console.log('❌ 未找到保存按钮');
            }
        }
        
        console.log('\n📍 验证分类创建结果');
        
        // 检查分类列表
        const creationResult = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#category-tbody tr'));
            const newCategory = rows.find(row => {
                const nameCell = row.querySelector('td:nth-child(3)');
                return nameCell && nameCell.textContent.includes('测试分类-渐变功能验证');
            });
            
            if (newCategory) {
                const cells = Array.from(newCategory.querySelectorAll('td'));
                const gradientCell = cells[3]; // 渐变预览列
                const gradientDiv = gradientCell ? gradientCell.querySelector('div[style*="background"]') : null;
                
                return {
                    found: true,
                    name: cells[2] ? cells[2].textContent.trim() : 'N/A',
                    icon: cells[1] ? cells[1].textContent.trim() : 'N/A',
                    gradientPreview: gradientDiv ? gradientDiv.style.background : 'none',
                    hasGradient: gradientDiv && gradientDiv.style.background && !gradientDiv.style.background.includes('#f0f0f0')
                };
            } else {
                return {
                    found: false,
                    totalRows: rows.length
                };
            }
        });
        
        console.log('📊 分类创建结果:');
        if (creationResult.found) {
            console.log('✅ 找到新创建的分类');
            console.log('名称:', creationResult.name);
            console.log('图标:', creationResult.icon);
            console.log('渐变预览:', creationResult.gradientPreview);
            console.log('有渐变色:', creationResult.hasGradient);
            
            if (creationResult.hasGradient) {
                console.log('🎉 渐变色保存成功！');
            } else {
                console.log('🔴 渐变色保存失败 - 显示为默认样式');
            }
        } else {
            console.log('❌ 未找到新创建的分类');
            console.log('当前总行数:', creationResult.totalRows);
        }
        
        // 截图
        await page.screenshot({ path: 'test-category-creation-gradient.png', fullPage: true });
        console.log('\n📸 测试截图已保存: test-category-creation-gradient.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            categoryCreated: creationResult.found,
            gradientSaved: creationResult.found ? creationResult.hasGradient : false
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-creation-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
testCategoryCreationGradient().then(result => {
    console.log('\n🎯 分类创建测试结果:', result);
}).catch(console.error);
