// 验证渐变显示修复效果
const { chromium } = require('playwright');

async function testGradientFixVerification() {
    console.log('🔧 验证渐变显示修复效果...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('渐变') || text.includes('清理分类数据') || text.includes('gradient')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：进入分类管理页面');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(5000);
        
        console.log('\n📍 第二步：检查修复后的表格渐变显示');
        
        // 检查表格中的渐变显示
        const tableGradientCheck = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                found: true,
                totalRows: rows.length,
                categories: rows.map((row, index) => {
                    const nameCell = row.querySelector('td:nth-child(3)');
                    const gradientCell = row.querySelector('td:nth-child(4)');
                    
                    return {
                        index: index + 1,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        gradientHTML: gradientCell ? gradientCell.innerHTML : 'N/A',
                        hasGradientStyle: gradientCell ? gradientCell.innerHTML.includes('gradient') : false,
                        hasGradientBackground: gradientCell ? gradientCell.innerHTML.includes('background:') : false,
                        showsNoGradient: gradientCell ? gradientCell.innerHTML.includes('无渐变') : false
                    };
                })
            };
        });
        
        console.log('📊 修复后表格渐变显示检查:');
        if (tableGradientCheck.found) {
            console.log(`表格总行数: ${tableGradientCheck.totalRows}`);
            
            tableGradientCheck.categories.forEach(cat => {
                console.log(`\n分类 ${cat.index}:`);
                console.log(`  名称: ${cat.name}`);
                console.log(`  有渐变样式: ${cat.hasGradientStyle}`);
                console.log(`  有渐变背景: ${cat.hasGradientBackground}`);
                console.log(`  显示无渐变: ${cat.showsNoGradient}`);
                console.log(`  HTML预览: ${cat.gradientHTML.substring(0, 100)}...`);
                
                if (cat.hasGradientStyle && cat.hasGradientBackground) {
                    console.log(`  ✅ 渐变显示正常`);
                } else if (cat.showsNoGradient) {
                    console.log(`  🔴 仍显示无渐变`);
                } else {
                    console.log(`  ⚠️ 渐变显示状态不明`);
                }
            });
            
            // 统计修复效果
            const withGradient = tableGradientCheck.categories.filter(cat => 
                cat.hasGradientStyle && cat.hasGradientBackground).length;
            const withoutGradient = tableGradientCheck.categories.filter(cat => 
                cat.showsNoGradient).length;
            
            console.log(`\n📊 修复效果统计:`);
            console.log(`  有渐变显示: ${withGradient}/${tableGradientCheck.totalRows}`);
            console.log(`  无渐变显示: ${withoutGradient}/${tableGradientCheck.totalRows}`);
            
            if (withGradient > 0 && withoutGradient === 0) {
                console.log(`  🎉 渐变显示修复完全成功！`);
            } else if (withGradient > 0) {
                console.log(`  ✅ 渐变显示部分修复成功`);
            } else {
                console.log(`  🔴 渐变显示修复失败`);
            }
            
        } else {
            console.log(`表格检查失败: ${tableGradientCheck.error}`);
        }
        
        console.log('\n📍 第三步：对比数据库数据和表格显示');
        
        // 直接查询数据库数据进行对比
        const databaseComparison = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('categories');
                
                if (result.success && result.data) {
                    return {
                        success: true,
                        databaseCategories: result.data.map(cat => ({
                            _id: cat._id,
                            name: cat.name,
                            hasGradientInDB: !!cat.gradient,
                            gradientValue: cat.gradient || 'N/A'
                        }))
                    };
                } else {
                    return {
                        success: false,
                        error: result.error || '查询失败'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据库与表格对比:');
        if (databaseComparison.success && tableGradientCheck.found) {
            databaseComparison.databaseCategories.forEach((dbCat, index) => {
                const tableCat = tableGradientCheck.categories[index];
                
                console.log(`\n分类对比 ${index + 1}:`);
                console.log(`  数据库名称: ${dbCat.name}`);
                console.log(`  表格名称: ${tableCat ? tableCat.name : 'N/A'}`);
                console.log(`  数据库有渐变: ${dbCat.hasGradientInDB}`);
                console.log(`  表格显示渐变: ${tableCat ? tableCat.hasGradientStyle : false}`);
                console.log(`  渐变值: ${dbCat.gradientValue.substring(0, 50)}...`);
                
                if (dbCat.hasGradientInDB && tableCat && tableCat.hasGradientStyle) {
                    console.log(`  ✅ 数据一致，修复成功`);
                } else if (dbCat.hasGradientInDB && tableCat && !tableCat.hasGradientStyle) {
                    console.log(`  🔴 数据库有渐变但表格未显示，修复失败`);
                } else if (!dbCat.hasGradientInDB) {
                    console.log(`  ⚠️ 数据库本身无渐变数据`);
                }
            });
        }
        
        console.log('\n📍 第四步：测试新建分类的渐变显示');
        
        // 创建新分类测试修复效果
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            await page.waitForTimeout(3000);
            
            // 填写分类信息
            await page.fill('#category-name', '修复验证测试分类');
            await page.fill('#category-description', '验证渐变显示修复效果');
            
            // 选择预设渐变
            await page.selectOption('#category-gradient-preset', 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)');
            console.log('✅ 已选择预设渐变');
            
            // 选择状态
            await page.selectOption('#category-status', 'show');
            console.log('✅ 已设置状态');
            
            // 保存分类
            const saveResult = await page.evaluate(() => {
                const modal = document.querySelector('[style*="position: fixed"]');
                const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
                
                if (submitBtn) {
                    submitBtn.click();
                    return { success: true };
                } else {
                    return { success: false, error: '未找到提交按钮' };
                }
            });
            
            if (saveResult.success) {
                console.log('✅ 已保存新分类');
                await page.waitForTimeout(8000);
                
                // 检查新分类的渐变显示
                const newCategoryCheck = await page.evaluate(() => {
                    const categoryTable = document.querySelector('#category-content table');
                    if (!categoryTable) return { error: '未找到分类表格' };
                    
                    const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
                    const lastRow = rows[rows.length - 1];
                    
                    if (!lastRow) return { error: '未找到最后一行' };
                    
                    const nameCell = lastRow.querySelector('td:nth-child(3)');
                    const gradientCell = lastRow.querySelector('td:nth-child(4)');
                    
                    return {
                        found: true,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        gradientHTML: gradientCell ? gradientCell.innerHTML : 'N/A',
                        hasGradientStyle: gradientCell ? gradientCell.innerHTML.includes('gradient') : false,
                        hasGradientBackground: gradientCell ? gradientCell.innerHTML.includes('background:') : false,
                        isTestCategory: nameCell ? nameCell.textContent.includes('修复验证测试') : false
                    };
                });
                
                console.log('\n📊 新分类渐变显示检查:');
                if (newCategoryCheck.found && newCategoryCheck.isTestCategory) {
                    console.log(`分类名称: ${newCategoryCheck.name}`);
                    console.log(`有渐变样式: ${newCategoryCheck.hasGradientStyle}`);
                    console.log(`有渐变背景: ${newCategoryCheck.hasGradientBackground}`);
                    console.log(`HTML预览: ${newCategoryCheck.gradientHTML.substring(0, 100)}...`);
                    
                    if (newCategoryCheck.hasGradientStyle && newCategoryCheck.hasGradientBackground) {
                        console.log('🎉 新分类渐变显示完全正常！');
                    } else {
                        console.log('🔴 新分类渐变显示仍有问题');
                    }
                } else {
                    console.log(`新分类检查失败: ${newCategoryCheck.error || '未找到测试分类'}`);
                }
            }
        }
        
        // 截图
        await page.screenshot({ path: 'gradient-fix-verification.png', fullPage: true });
        console.log('\n📸 验证截图已保存: gradient-fix-verification.png');
        
        console.log('\n🎯 渐变显示修复验证总结:');
        
        const existingFixed = tableGradientCheck.found && 
            tableGradientCheck.categories.some(cat => cat.hasGradientStyle);
        const newCategoryWorking = newCategoryCheck && newCategoryCheck.found && 
            newCategoryCheck.hasGradientStyle;
        
        console.log(`现有分类修复: ${existingFixed ? '✅ 成功' : '🔴 失败'}`);
        console.log(`新分类功能: ${newCategoryWorking ? '✅ 正常' : '🔴 异常'}`);
        
        if (existingFixed && newCategoryWorking) {
            console.log('\n🎉 渐变显示问题完全修复！');
            return { success: true, allFixed: true };
        } else {
            console.log('\n⚠️ 渐变显示问题仍需进一步修复');
            return { success: true, allFixed: false, issues: {
                existingCategories: !existingFixed,
                newCategories: !newCategoryWorking
            }};
        }
        
    } catch (error) {
        console.error('❌ 验证过程中出错:', error);
        await page.screenshot({ path: 'gradient-fix-verification-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 验证完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行验证
testGradientFixVerification().then(result => {
    console.log('\n🎯 渐变显示修复验证最终结果:', result);
}).catch(console.error);
