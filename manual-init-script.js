// 手动初始化数据库脚本 - 在小程序开发者工具控制台中运行

// 1. 初始化数据库
async function initDatabaseManually() {
  console.log('🚀 开始手动初始化数据库...');

  try {
    // 显示加载提示
    wx.showLoading({ title: '正在初始化数据库...' });

    const result = await wx.cloud.callFunction({
      name: 'initDatabase',
      data: {}
    });

    wx.hideLoading();
    console.log('初始化结果:', result);

    if (result.result.success) {
      console.log('✅ 数据库初始化成功!');
      console.log('详细结果:', result.result);

      wx.showToast({
        title: '数据库初始化成功!',
        icon: 'success',
        duration: 2000
      });

      // 等待一下让数据库同步
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 测试数据是否可用
      await testDataAvailability();
    } else {
      console.error('❌ 数据库初始化失败:', result.result.message);
      wx.showToast({
        title: '初始化失败: ' + result.result.message,
        icon: 'error',
        duration: 3000
      });
    }
  } catch (error) {
    wx.hideLoading();
    console.error('❌ 调用初始化函数失败:', error);
    wx.showToast({
      title: '调用失败: ' + error.message,
      icon: 'error',
      duration: 3000
    });
  }
}

// 2. 测试数据可用性
async function testDataAvailability() {
  console.log('🧪 测试数据可用性...');
  
  try {
    // 测试分类数据
    const categoriesResult = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getCategories' }
    });
    console.log('分类数据:', categoriesResult);
    
    // 测试横幅数据
    const bannersResult = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getBanners' }
    });
    console.log('横幅数据:', bannersResult);
    
    // 测试表情包数据
    const emojisResult = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 10 } }
    });
    console.log('表情包数据:', emojisResult);
    
    // 测试热门表情包
    const hotEmojisResult = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getHotEmojis', limit: 6 }
    });
    console.log('热门表情包数据:', hotEmojisResult);
    
    console.log('✅ 数据测试完成，请查看上面的结果');
  } catch (error) {
    console.error('❌ 数据测试失败:', error);
  }
}

// 3. 直接检查数据库集合
async function checkDatabaseCollections() {
  console.log('🔍 检查数据库集合...');
  
  const collections = ['categories', 'emojis', 'banners', 'users'];
  
  for (const collection of collections) {
    try {
      const result = await wx.cloud.database().collection(collection).limit(1).get();
      console.log(`✅ ${collection}: 存在，记录数: ${result.data.length}`);
    } catch (error) {
      console.log(`❌ ${collection}: 不存在或无法访问 - ${error.message}`);
    }
  }
}

// 4. 强制重新初始化（如果第一次失败）
async function forceReinitDatabase() {
  console.log('🔄 强制重新初始化数据库...');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'forceInitDatabase' }
    });
    
    console.log('强制初始化结果:', result);
    
    if (result.result.success) {
      console.log('✅ 强制初始化成功!');
      await testDataAvailability();
    }
  } catch (error) {
    console.error('❌ 强制初始化失败:', error);
  }
}

// 使用说明
console.log(`
🚀 数据库初始化脚本使用说明：

1. 首次初始化：
   initDatabaseManually()

2. 检查集合状态：
   checkDatabaseCollections()

3. 测试数据可用性：
   testDataAvailability()

4. 强制重新初始化：
   forceReinitDatabase()

请在小程序开发者工具的控制台中运行这些函数。
`);

// 自动执行检查
checkDatabaseCollections();
