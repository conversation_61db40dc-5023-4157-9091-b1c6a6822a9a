<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阶段三：数据库事务系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .section {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #374151;
        }

        .test-case {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #d1d5db;
            background: #f9fafb;
        }

        .test-case.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .test-case.failed {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .test-case.running {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }

        .test-case-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .test-case-content {
            font-size: 14px;
            color: #6b7280;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .btn-success {
            background: #10b981;
        }

        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #f3f4f6;
            color: #374151;
        }

        .status-running {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📋 阶段三：数据库事务系统测试</h1>
        
        <div class="section">
            <h2 class="section-title">🎯 测试目标</h2>
            <p>验证数据库事务系统的完整性和可靠性，确保所有CRUD操作都具备事务保护。</p>
            <ul>
                <li>✅ 分类管理事务操作</li>
                <li>✅ 表情包管理事务操作</li>
                <li>✅ 横幅管理事务操作</li>
                <li>✅ 数据验证和错误处理</li>
            </ul>
        </div>

        <!-- 分类管理事务测试 -->
        <div class="section">
            <h2 class="section-title">📁 分类管理事务测试</h2>
            
            <div class="test-case" id="test-category-create">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-category-create">待测试</span>
                    TC101: 分类创建事务测试
                </div>
                <div class="test-case-content" id="content-category-create">验证分类创建的事务完整性</div>
            </div>

            <div class="test-case" id="test-category-update">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-category-update">待测试</span>
                    TC102: 分类更新事务测试
                </div>
                <div class="test-case-content" id="content-category-update">验证分类更新的事务完整性</div>
            </div>

            <div class="test-case" id="test-category-delete">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-category-delete">待测试</span>
                    TC103: 分类删除事务测试
                </div>
                <div class="test-case-content" id="content-category-delete">验证分类删除的事务完整性</div>
            </div>

            <div class="test-case" id="test-category-rollback">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-category-rollback">待测试</span>
                    TC104: 分类事务回滚测试
                </div>
                <div class="test-case-content" id="content-category-rollback">验证事务失败时的回滚机制</div>
            </div>

            <button class="btn" onclick="runCategoryTests()">运行分类事务测试</button>
        </div>

        <!-- 表情包管理事务测试 -->
        <div class="section">
            <h2 class="section-title">😀 表情包管理事务测试</h2>
            
            <div class="test-case" id="test-emoji-create">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-emoji-create">待测试</span>
                    TC201: 表情包创建事务测试
                </div>
                <div class="test-case-content" id="content-emoji-create">验证表情包创建的事务完整性</div>
            </div>

            <div class="test-case" id="test-emoji-update">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-emoji-update">待测试</span>
                    TC202: 表情包更新事务测试
                </div>
                <div class="test-case-content" id="content-emoji-update">验证表情包更新的事务完整性</div>
            </div>

            <div class="test-case" id="test-emoji-delete">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-emoji-delete">待测试</span>
                    TC203: 表情包删除事务测试
                </div>
                <div class="test-case-content" id="content-emoji-delete">验证表情包删除的事务完整性</div>
            </div>

            <button class="btn" onclick="runEmojiTests()">运行表情包事务测试</button>
        </div>

        <!-- 横幅管理事务测试 -->
        <div class="section">
            <h2 class="section-title">🎯 横幅管理事务测试</h2>
            
            <div class="test-case" id="test-banner-create">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-banner-create">待测试</span>
                    TC301: 横幅创建事务测试
                </div>
                <div class="test-case-content" id="content-banner-create">验证横幅创建的事务完整性</div>
            </div>

            <div class="test-case" id="test-banner-update">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-banner-update">待测试</span>
                    TC302: 横幅更新事务测试
                </div>
                <div class="test-case-content" id="content-banner-update">验证横幅更新的事务完整性</div>
            </div>

            <div class="test-case" id="test-banner-delete">
                <div class="test-case-title">
                    <span class="status status-pending" id="status-banner-delete">待测试</span>
                    TC303: 横幅删除事务测试
                </div>
                <div class="test-case-content" id="content-banner-delete">验证横幅删除的事务完整性</div>
            </div>

            <button class="btn" onclick="runBannerTests()">运行横幅事务测试</button>
        </div>

        <!-- 综合测试 -->
        <div class="section">
            <h2 class="section-title">🔄 综合测试</h2>
            
            <div>
                <button class="btn" onclick="runAllStage3Tests()">运行所有阶段三测试</button>
                <button class="btn btn-success" onclick="completeStage3()" id="completeStage3Btn" disabled>完成阶段三</button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="section">
            <h2 class="section-title">📋 测试日志</h2>
            <div class="log" id="testLog">
[等待开始] 阶段三：数据库事务系统测试准备就绪...
            </div>
        </div>
    </div>

    <!-- CloudBase Web SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.10.10/tcb.js"></script>
    
    <!-- 管理器脚本 -->
    <script src="./admin-web/js/auth-manager.js"></script>
    <script src="./admin-web/js/api-manager.js"></script>

    <script>
        // 阶段三测试管理器
        class Stage3Tester {
            constructor() {
                this.logElement = document.getElementById('testLog');
                this.testResults = {
                    category: { create: false, update: false, delete: false, rollback: false },
                    emoji: { create: false, update: false, delete: false },
                    banner: { create: false, update: false, delete: false }
                };
                this.createdItems = {
                    categories: [],
                    emojis: [],
                    banners: []
                };
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = {
                    'info': 'ℹ️',
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️'
                }[type] || 'ℹ️';
                
                const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
                this.logElement.textContent += logMessage;
                this.logElement.scrollTop = this.logElement.scrollHeight;
                
                console.log(`[STAGE3-TEST] ${message}`);
            }

            setTestStatus(testId, status, content) {
                const testElement = document.getElementById(testId);
                const statusElement = document.getElementById(`status-${testId.replace('test-', '')}`);
                const contentElement = document.getElementById(`content-${testId.replace('test-', '')}`);
                
                if (testElement && statusElement && contentElement) {
                    testElement.classList.remove('completed', 'failed', 'running');
                    statusElement.classList.remove('status-pending', 'status-running', 'status-completed', 'status-failed');
                    
                    testElement.classList.add(status);
                    statusElement.classList.add(`status-${status}`);
                    statusElement.textContent = {
                        'pending': '待测试',
                        'running': '测试中',
                        'completed': '通过',
                        'failed': '失败'
                    }[status] || status;
                    
                    contentElement.textContent = content;
                }
            }

            async ensureLoggedIn() {
                if (!window.authManager.isLoggedIn()) {
                    this.log('需要先登录，正在尝试登录...');
                    const result = await window.authManager.login('admin', 'admin123456');
                    if (!result.success) {
                        throw new Error('登录失败: ' + result.error);
                    }
                    this.log('登录成功', 'success');
                }
            }

            // 分类事务测试
            async runCategoryTests() {
                this.log('开始分类管理事务测试...', 'info');
                
                await this.ensureLoggedIn();
                
                await this.testCategoryCreate();
                await this.testCategoryUpdate();
                await this.testCategoryDelete();
                await this.testCategoryRollback();
                
                const allPassed = Object.values(this.testResults.category).every(result => result);
                if (allPassed) {
                    this.log('✅ 分类管理事务测试全部通过！', 'success');
                } else {
                    this.log('⚠️ 部分分类测试失败', 'warning');
                }
            }

            async testCategoryCreate() {
                this.setTestStatus('test-category-create', 'running', '正在测试分类创建事务...');
                
                try {
                    const testCategory = {
                        name: `测试分类_${Date.now()}`,
                        icon: 'test-icon',
                        description: '这是一个测试分类',
                        sort: 0
                    };
                    
                    const result = await window.apiManager.createCategory(testCategory);
                    
                    if (result.success && result.data.id) {
                        this.createdItems.categories.push(result.data.id);
                        this.testResults.category.create = true;
                        this.setTestStatus('test-category-create', 'completed', `分类创建成功，ID: ${result.data.id}`);
                        this.log(`TC101 通过: 分类创建事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '创建失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-category-create', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC101 失败: ${error.message}`, 'error');
                }
            }

            async testCategoryUpdate() {
                this.setTestStatus('test-category-update', 'running', '正在测试分类更新事务...');
                
                try {
                    if (this.createdItems.categories.length === 0) {
                        throw new Error('没有可更新的分类');
                    }
                    
                    const categoryId = this.createdItems.categories[0];
                    const updates = {
                        name: `更新后的分类_${Date.now()}`,
                        description: '这是更新后的描述'
                    };
                    
                    const result = await window.apiManager.updateCategory(categoryId, updates);
                    
                    if (result.success) {
                        this.testResults.category.update = true;
                        this.setTestStatus('test-category-update', 'completed', `分类更新成功`);
                        this.log(`TC102 通过: 分类更新事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '更新失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-category-update', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC102 失败: ${error.message}`, 'error');
                }
            }

            async testCategoryDelete() {
                this.setTestStatus('test-category-delete', 'running', '正在测试分类删除事务...');
                
                try {
                    if (this.createdItems.categories.length === 0) {
                        throw new Error('没有可删除的分类');
                    }
                    
                    const categoryId = this.createdItems.categories[0];
                    const result = await window.apiManager.deleteCategory(categoryId);
                    
                    if (result.success) {
                        this.testResults.category.delete = true;
                        this.setTestStatus('test-category-delete', 'completed', `分类删除成功`);
                        this.log(`TC103 通过: 分类删除事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '删除失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-category-delete', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC103 失败: ${error.message}`, 'error');
                }
            }

            async testCategoryRollback() {
                this.setTestStatus('test-category-rollback', 'running', '正在测试分类事务回滚...');
                
                try {
                    // 尝试创建一个无效的分类（空名称）
                    const invalidCategory = {
                        name: '',  // 空名称应该导致验证失败
                        icon: 'test-icon'
                    };
                    
                    const result = await window.apiManager.createCategory(invalidCategory);
                    
                    if (!result.success && result.error.includes('不能为空')) {
                        this.testResults.category.rollback = true;
                        this.setTestStatus('test-category-rollback', 'completed', `事务回滚测试通过：正确拒绝无效数据`);
                        this.log(`TC104 通过: 分类事务回滚测试`, 'success');
                    } else {
                        throw new Error('应该拒绝无效数据但没有拒绝');
                    }
                } catch (error) {
                    this.setTestStatus('test-category-rollback', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC104 失败: ${error.message}`, 'error');
                }
            }

            // 表情包事务测试
            async runEmojiTests() {
                this.log('开始表情包管理事务测试...', 'info');
                
                await this.ensureLoggedIn();
                
                await this.testEmojiCreate();
                await this.testEmojiUpdate();
                await this.testEmojiDelete();
                
                const allPassed = Object.values(this.testResults.emoji).every(result => result);
                if (allPassed) {
                    this.log('✅ 表情包管理事务测试全部通过！', 'success');
                } else {
                    this.log('⚠️ 部分表情包测试失败', 'warning');
                }
            }

            async testEmojiCreate() {
                this.setTestStatus('test-emoji-create', 'running', '正在测试表情包创建事务...');
                
                try {
                    const testEmoji = {
                        title: `测试表情包_${Date.now()}`,
                        imageUrl: 'https://example.com/test-emoji.png',
                        category: 'test',
                        tags: ['测试', '表情包'],
                        description: '这是一个测试表情包'
                    };
                    
                    const result = await window.apiManager.createEmoji(testEmoji);
                    
                    if (result.success && result.data.id) {
                        this.createdItems.emojis.push(result.data.id);
                        this.testResults.emoji.create = true;
                        this.setTestStatus('test-emoji-create', 'completed', `表情包创建成功，ID: ${result.data.id}`);
                        this.log(`TC201 通过: 表情包创建事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '创建失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-emoji-create', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC201 失败: ${error.message}`, 'error');
                }
            }

            async testEmojiUpdate() {
                this.setTestStatus('test-emoji-update', 'running', '正在测试表情包更新事务...');
                
                try {
                    if (this.createdItems.emojis.length === 0) {
                        throw new Error('没有可更新的表情包');
                    }
                    
                    const emojiId = this.createdItems.emojis[0];
                    const updates = {
                        title: `更新后的表情包_${Date.now()}`,
                        description: '这是更新后的描述'
                    };
                    
                    const result = await window.apiManager.updateEmoji(emojiId, updates);
                    
                    if (result.success) {
                        this.testResults.emoji.update = true;
                        this.setTestStatus('test-emoji-update', 'completed', `表情包更新成功`);
                        this.log(`TC202 通过: 表情包更新事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '更新失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-emoji-update', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC202 失败: ${error.message}`, 'error');
                }
            }

            async testEmojiDelete() {
                this.setTestStatus('test-emoji-delete', 'running', '正在测试表情包删除事务...');
                
                try {
                    if (this.createdItems.emojis.length === 0) {
                        throw new Error('没有可删除的表情包');
                    }
                    
                    const emojiId = this.createdItems.emojis[0];
                    const result = await window.apiManager.deleteEmoji(emojiId);
                    
                    if (result.success) {
                        this.testResults.emoji.delete = true;
                        this.setTestStatus('test-emoji-delete', 'completed', `表情包删除成功`);
                        this.log(`TC203 通过: 表情包删除事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '删除失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-emoji-delete', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC203 失败: ${error.message}`, 'error');
                }
            }

            // 横幅事务测试
            async runBannerTests() {
                this.log('开始横幅管理事务测试...', 'info');
                
                await this.ensureLoggedIn();
                
                await this.testBannerCreate();
                await this.testBannerUpdate();
                await this.testBannerDelete();
                
                const allPassed = Object.values(this.testResults.banner).every(result => result);
                if (allPassed) {
                    this.log('✅ 横幅管理事务测试全部通过！', 'success');
                } else {
                    this.log('⚠️ 部分横幅测试失败', 'warning');
                }
            }

            async testBannerCreate() {
                this.setTestStatus('test-banner-create', 'running', '正在测试横幅创建事务...');
                
                try {
                    const testBanner = {
                        title: `测试横幅_${Date.now()}`,
                        imageUrl: 'https://example.com/test-banner.png',
                        linkUrl: 'https://example.com',
                        description: '这是一个测试横幅',
                        sort: 0
                    };
                    
                    const result = await window.apiManager.createBanner(testBanner);
                    
                    if (result.success && result.data.id) {
                        this.createdItems.banners.push(result.data.id);
                        this.testResults.banner.create = true;
                        this.setTestStatus('test-banner-create', 'completed', `横幅创建成功，ID: ${result.data.id}`);
                        this.log(`TC301 通过: 横幅创建事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '创建失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-banner-create', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC301 失败: ${error.message}`, 'error');
                }
            }

            async testBannerUpdate() {
                this.setTestStatus('test-banner-update', 'running', '正在测试横幅更新事务...');
                
                try {
                    if (this.createdItems.banners.length === 0) {
                        throw new Error('没有可更新的横幅');
                    }
                    
                    const bannerId = this.createdItems.banners[0];
                    const updates = {
                        title: `更新后的横幅_${Date.now()}`,
                        description: '这是更新后的描述'
                    };
                    
                    const result = await window.apiManager.updateBanner(bannerId, updates);
                    
                    if (result.success) {
                        this.testResults.banner.update = true;
                        this.setTestStatus('test-banner-update', 'completed', `横幅更新成功`);
                        this.log(`TC302 通过: 横幅更新事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '更新失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-banner-update', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC302 失败: ${error.message}`, 'error');
                }
            }

            async testBannerDelete() {
                this.setTestStatus('test-banner-delete', 'running', '正在测试横幅删除事务...');
                
                try {
                    if (this.createdItems.banners.length === 0) {
                        throw new Error('没有可删除的横幅');
                    }
                    
                    const bannerId = this.createdItems.banners[0];
                    const result = await window.apiManager.deleteBanner(bannerId);
                    
                    if (result.success) {
                        this.testResults.banner.delete = true;
                        this.setTestStatus('test-banner-delete', 'completed', `横幅删除成功`);
                        this.log(`TC303 通过: 横幅删除事务测试`, 'success');
                    } else {
                        throw new Error(result.error || '删除失败');
                    }
                } catch (error) {
                    this.setTestStatus('test-banner-delete', 'failed', `测试失败: ${error.message}`);
                    this.log(`TC303 失败: ${error.message}`, 'error');
                }
            }

            async runAllStage3Tests() {
                this.log('🚀 开始运行所有阶段三测试...', 'info');
                
                await this.runCategoryTests();
                await this.runEmojiTests();
                await this.runBannerTests();
                
                // 检查所有测试是否通过
                const allCategoryPassed = Object.values(this.testResults.category).every(result => result);
                const allEmojiPassed = Object.values(this.testResults.emoji).every(result => result);
                const allBannerPassed = Object.values(this.testResults.banner).every(result => result);
                
                if (allCategoryPassed && allEmojiPassed && allBannerPassed) {
                    this.log('🎉 阶段三所有测试通过！', 'success');
                    document.getElementById('completeStage3Btn').disabled = false;
                } else {
                    this.log('⚠️ 部分测试失败，请检查问题', 'warning');
                }
            }

            completeStage3() {
                this.log('🎉 阶段三：数据库事务系统开发 - 完成！', 'success');
                this.log('✅ 所有事务系统功能测试通过', 'success');
                this.log('📋 下一步：开始阶段四 - 实时同步系统开发', 'info');
                
                alert('🎉 阶段三完成！\n\n✅ 数据库事务系统开发完成\n✅ 所有功能测试通过\n\n请继续阶段四的开发工作。');
            }
        }

        // 全局实例
        window.stage3Tester = new Stage3Tester();

        // 全局函数
        function runCategoryTests() {
            window.stage3Tester.runCategoryTests();
        }

        function runEmojiTests() {
            window.stage3Tester.runEmojiTests();
        }

        function runBannerTests() {
            window.stage3Tester.runBannerTests();
        }

        function runAllStage3Tests() {
            window.stage3Tester.runAllStage3Tests();
        }

        function completeStage3() {
            window.stage3Tester.completeStage3();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.stage3Tester.log('🚀 阶段三测试工具初始化完成', 'success');
            window.stage3Tester.log('📋 准备测试数据库事务系统功能', 'info');
        });
    </script>
</body>
</html>
