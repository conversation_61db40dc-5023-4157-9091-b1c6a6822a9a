// 调试DOM结构
const { chromium } = require('playwright');

async function debugDOMStructure() {
    console.log('🔍 调试DOM结构...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 分析页面DOM结构');
        
        // 获取页面的主要结构
        const domInfo = await page.evaluate(() => {
            const body = document.body;
            
            // 获取所有可见的文本内容
            const allText = Array.from(document.querySelectorAll('*'))
                .filter(el => el.offsetParent !== null) // 只获取可见元素
                .map(el => el.textContent?.trim())
                .filter(text => text && text.length > 0 && text.length < 50)
                .slice(0, 50); // 限制数量
            
            // 获取所有按钮
            const buttons = Array.from(document.querySelectorAll('button, .btn, [role="button"]'))
                .filter(btn => btn.offsetParent !== null)
                .map(btn => ({
                    text: btn.textContent?.trim(),
                    className: btn.className,
                    id: btn.id
                }));
            
            // 获取所有链接
            const links = Array.from(document.querySelectorAll('a, [role="link"]'))
                .filter(link => link.offsetParent !== null)
                .map(link => ({
                    text: link.textContent?.trim(),
                    href: link.href,
                    className: link.className
                }));
            
            // 获取导航菜单
            const navItems = Array.from(document.querySelectorAll('nav *, .nav *, .menu *, .sidebar *'))
                .filter(item => item.offsetParent !== null && item.textContent?.trim())
                .map(item => item.textContent?.trim())
                .filter(text => text && text.length > 0 && text.length < 30);
            
            return {
                title: document.title,
                url: window.location.href,
                allText: allText,
                buttons: buttons,
                links: links,
                navItems: navItems,
                hasAdminApp: typeof AdminApp !== 'undefined',
                adminAppData: typeof AdminApp !== 'undefined' ? {
                    currentPage: AdminApp.data?.currentPage,
                    categories: AdminApp.data?.categories?.length || 0,
                    emojis: AdminApp.data?.emojis?.length || 0,
                    banners: AdminApp.data?.banners?.length || 0
                } : null
            };
        });
        
        console.log('📊 DOM分析结果:');
        console.log('标题:', domInfo.title);
        console.log('URL:', domInfo.url);
        console.log('AdminApp存在:', domInfo.hasAdminApp);
        if (domInfo.adminAppData) {
            console.log('AdminApp数据:', JSON.stringify(domInfo.adminAppData, null, 2));
        }
        
        console.log('\n📋 页面中的所有文本内容:');
        domInfo.allText.forEach((text, index) => {
            console.log(`${index + 1}. "${text}"`);
        });
        
        console.log('\n🔘 页面中的按钮:');
        domInfo.buttons.forEach((btn, index) => {
            console.log(`${index + 1}. "${btn.text}" (class: ${btn.className}, id: ${btn.id})`);
        });
        
        console.log('\n🔗 页面中的链接:');
        domInfo.links.forEach((link, index) => {
            console.log(`${index + 1}. "${link.text}" (href: ${link.href})`);
        });
        
        console.log('\n🧭 导航菜单项:');
        domInfo.navItems.forEach((item, index) => {
            console.log(`${index + 1}. "${item}"`);
        });
        
        // 尝试查找特定的管理功能
        console.log('\n🔍 查找管理功能:');
        
        const managementElements = await page.evaluate(() => {
            const searchTerms = ['分类', '横幅', '表情包', '管理', '仪表板', '添加', '编辑', '删除'];
            const results = [];
            
            searchTerms.forEach(term => {
                const elements = Array.from(document.querySelectorAll('*'))
                    .filter(el => el.offsetParent !== null && el.textContent?.includes(term))
                    .map(el => ({
                        term: term,
                        text: el.textContent?.trim(),
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id
                    }));
                results.push(...elements);
            });
            
            return results.slice(0, 20); // 限制结果数量
        });
        
        managementElements.forEach((el, index) => {
            console.log(`${index + 1}. [${el.term}] ${el.tagName}: "${el.text}" (class: ${el.className})`);
        });
        
        // 截图
        await page.screenshot({ path: 'dom-debug.png', fullPage: true });
        console.log('\n📸 DOM调试截图已保存: dom-debug.png');
        
        // 保持浏览器打开
        console.log('\n⏸️ 浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugDOMStructure().catch(console.error);
