const { chromium } = require('playwright');

async function testCloudFunctionCategories() {
  console.log('🧪 测试云函数 getCategories 返回结果...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // 导航到管理后台
    console.log('📱 打开管理后台...');
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForTimeout(8000);
    
    // 在控制台中直接测试云函数
    console.log('☁️ 测试云函数 getCategories...');
    
    const result = await page.evaluate(async () => {
      try {
        // 确保SDK已加载
        if (!window.cloudbase) {
          return { error: 'SDK未加载' };
        }
        
        // 初始化云开发
        const app = window.cloudbase.init({
          env: 'cloud1-5g6pvnpl88dc0142'
        });
        
        // 匿名登录
        await app.auth().signInAnonymously();
        
        // 调用云函数
        const result = await app.callFunction({
          name: 'dataAPI',
          data: { action: 'getCategories' }
        });
        
        console.log('云函数返回结果:', result);
        return result;
        
      } catch (error) {
        console.error('云函数调用失败:', error);
        return { error: error.message };
      }
    });
    
    console.log('🔍 云函数测试结果:', JSON.stringify(result, null, 2));
    
    // 检查结果
    if (result.error) {
      console.error('❌ 云函数调用失败:', result.error);
    } else if (result.result && result.result.success && result.result.data) {
      console.log('✅ 云函数调用成功!');
      console.log('📊 分类数据:');
      result.result.data.forEach(category => {
        console.log(`  - ${category.name}: ${category.emojiCount || 0} 个表情包`);
      });
      
      // 检查数据是否正确
      const expectedCounts = { '测试': 2, '测试2': 2, '测试3': 1 };
      let allCorrect = true;
      
      result.result.data.forEach(category => {
        const expected = expectedCounts[category.name];
        const actual = category.emojiCount || 0;
        if (expected !== undefined && actual !== expected) {
          console.error(`❌ ${category.name}: 期望 ${expected} 个，实际 ${actual} 个`);
          allCorrect = false;
        } else if (expected !== undefined) {
          console.log(`✅ ${category.name}: ${actual} 个表情包 (正确)`);
        }
      });
      
      if (allCorrect) {
        console.log('🎉 所有分类的表情包数量都正确！');
      } else {
        console.log('⚠️ 部分分类的表情包数量不正确');
      }
      
    } else {
      console.log('⚠️ 云函数返回格式异常:', result);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

// 运行测试
testCloudFunctionCategories().catch(console.error);
