@echo off
echo Starting local HTTP server...
echo Make sure Python is installed

echo.
echo Choose startup method:
echo 1. Use Python (Recommended)
echo 2. Use Node.js
echo 3. Use PHP
echo.

set /p choice=Please enter choice (1-3):

if "%choice%"=="1" (
    echo Starting Python server...
    echo Access URL: http://localhost:8000
    echo Press Ctrl+C to stop server
    echo.
    python -m http.server 8000
) else if "%choice%"=="2" (
    echo Starting Node.js server...
    echo Access URL: http://localhost:8000
    echo Press Ctrl+C to stop server
    echo.
    npx http-server -p 8000
) else if "%choice%"=="3" (
    echo Starting PHP server...
    echo Access URL: http://localhost:8000
    echo Press Ctrl+C to stop server
    echo.
    php -S localhost:8000
) else (
    echo Invalid choice, using Python by default...
    python -m http.server 8000
)

pause
