<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步问题全面诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .step { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔍 数据同步问题全面诊断</h1>
    
    <div class="section info">
        <h2>诊断说明</h2>
        <p>这个工具将系统性地检查从管理后台到小程序的完整数据流程，找出真正的问题所在。</p>
        <p><strong>请按顺序执行以下步骤：</strong></p>
    </div>

    <div class="step">
        <h3>步骤1: 检查云开发环境连接</h3>
        <button onclick="checkCloudConnection()">检查云开发连接</button>
        <div id="cloudConnectionResult"></div>
    </div>

    <div class="step">
        <h3>步骤2: 检查数据库中的实际数据</h3>
        <button onclick="checkDatabaseData()">检查数据库数据</button>
        <div id="databaseDataResult"></div>
    </div>

    <div class="step">
        <h3>步骤3: 测试云函数dataAPI</h3>
        <button onclick="testDataAPIFunction()">测试dataAPI云函数</button>
        <div id="dataAPIResult"></div>
    </div>

    <div class="step">
        <h3>步骤4: 模拟管理后台数据创建</h3>
        <button onclick="simulateAdminDataCreation()">模拟创建数据</button>
        <div id="adminDataResult"></div>
    </div>

    <div class="step">
        <h3>步骤5: 验证数据读取链路</h3>
        <button onclick="verifyDataReadChain()">验证读取链路</button>
        <div id="dataReadResult"></div>
    </div>

    <div class="step">
        <h3>步骤6: 生成诊断报告</h3>
        <button onclick="generateDiagnosticReport()">生成报告</button>
        <div id="diagnosticReport"></div>
    </div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"></script>
    <script>
        // 云开发配置
        const cloudConfig = {
            env: 'cloud1-5g6pvnpl88dc0142'
        };

        let app = null;
        let db = null;
        let diagnosticResults = {};

        // 初始化云开发
        async function initCloudbase() {
            try {
                app = cloudbase.init(cloudConfig);
                await app.auth().signInAnonymously();
                db = app.database();
                return true;
            } catch (error) {
                console.error('云开发初始化失败:', error);
                return false;
            }
        }

        // 步骤1: 检查云开发环境连接
        async function checkCloudConnection() {
            const resultDiv = document.getElementById('cloudConnectionResult');
            resultDiv.innerHTML = '<p>正在检查云开发连接...</p>';

            try {
                const success = await initCloudbase();
                if (success) {
                    resultDiv.innerHTML = '<div class="success"><h4>✅ 云开发连接成功</h4><p>环境ID: ' + cloudConfig.env + '</p></div>';
                    diagnosticResults.cloudConnection = { success: true, env: cloudConfig.env };
                } else {
                    throw new Error('初始化失败');
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 云开发连接失败</h4><p>错误: ' + error.message + '</p></div>';
                diagnosticResults.cloudConnection = { success: false, error: error.message };
            }
        }

        // 步骤2: 检查数据库中的实际数据
        async function checkDatabaseData() {
            const resultDiv = document.getElementById('databaseDataResult');
            resultDiv.innerHTML = '<p>正在检查数据库数据...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先检查云开发连接</h4></div>';
                return;
            }

            try {
                // 检查分类数据
                const categoriesResult = await db.collection('categories').get();
                const categories = categoriesResult.data;

                // 检查表情包数据
                const emojisResult = await db.collection('emojis').get();
                const emojis = emojisResult.data;

                // 检查轮播图数据
                const bannersResult = await db.collection('banners').get();
                const banners = bannersResult.data;

                let html = '<div class="success"><h4>✅ 数据库数据检查完成</h4>';
                html += '<p><strong>分类数据:</strong> ' + categories.length + ' 条</p>';
                html += '<p><strong>表情包数据:</strong> ' + emojis.length + ' 条</p>';
                html += '<p><strong>轮播图数据:</strong> ' + banners.length + ' 条</p>';

                // 显示具体数据
                if (categories.length > 0) {
                    html += '<h5>分类详情:</h5><pre>' + JSON.stringify(categories.slice(0, 3), null, 2) + '</pre>';
                }
                if (emojis.length > 0) {
                    html += '<h5>表情包详情:</h5><pre>' + JSON.stringify(emojis.slice(0, 3), null, 2) + '</pre>';
                }

                html += '</div>';
                resultDiv.innerHTML = html;

                diagnosticResults.databaseData = {
                    success: true,
                    categories: categories,
                    emojis: emojis,
                    banners: banners
                };

            } catch (error) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 数据库检查失败</h4><p>错误: ' + error.message + '</p></div>';
                diagnosticResults.databaseData = { success: false, error: error.message };
            }
        }

        // 步骤3: 测试云函数dataAPI
        async function testDataAPIFunction() {
            const resultDiv = document.getElementById('dataAPIResult');
            resultDiv.innerHTML = '<p>正在测试dataAPI云函数...</p>';

            if (!app) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先检查云开发连接</h4></div>';
                return;
            }

            try {
                // 测试获取分类
                const categoriesResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });

                // 测试获取表情包
                const emojisResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 10 } }
                });

                // 测试获取轮播图
                const bannersResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });

                let html = '<div class="success"><h4>✅ dataAPI云函数测试完成</h4>';
                html += '<h5>分类API结果:</h5><pre>' + JSON.stringify(categoriesResult.result, null, 2) + '</pre>';
                html += '<h5>表情包API结果:</h5><pre>' + JSON.stringify(emojisResult.result, null, 2) + '</pre>';
                html += '<h5>轮播图API结果:</h5><pre>' + JSON.stringify(bannersResult.result, null, 2) + '</pre>';
                html += '</div>';

                resultDiv.innerHTML = html;

                diagnosticResults.dataAPI = {
                    success: true,
                    categories: categoriesResult.result,
                    emojis: emojisResult.result,
                    banners: bannersResult.result
                };

            } catch (error) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ dataAPI云函数测试失败</h4><p>错误: ' + error.message + '</p></div>';
                diagnosticResults.dataAPI = { success: false, error: error.message };
            }
        }

        // 步骤4: 模拟管理后台数据创建
        async function simulateAdminDataCreation() {
            const resultDiv = document.getElementById('adminDataResult');
            resultDiv.innerHTML = '<p>正在模拟管理后台数据创建...</p>';

            if (!db) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先检查云开发连接</h4></div>';
                return;
            }

            try {
                const timestamp = Date.now();
                
                // 创建测试分类（模拟管理后台格式）
                const testCategory = {
                    name: '诊断测试分类_' + timestamp,
                    icon: '🔧',
                    sort: 999,
                    status: 'active',
                    description: '诊断测试用分类',
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const categoryResult = await db.collection('categories').add({
                    data: testCategory
                });

                // 创建测试表情包（模拟管理后台格式）
                const testEmoji = {
                    title: '诊断测试表情包_' + timestamp,
                    category: testCategory.name, // 注意：这里使用category字段存储分类名称
                    description: '诊断测试用表情包',
                    imageUrl: 'https://via.placeholder.com/150x150?text=Test',
                    status: 'published',
                    likes: 0,
                    downloads: 0,
                    collections: 0,
                    createTime: new Date(),
                    updateTime: new Date()
                };

                const emojiResult = await db.collection('emojis').add({
                    data: testEmoji
                });

                let html = '<div class="success"><h4>✅ 模拟数据创建成功</h4>';
                html += '<p>分类ID: ' + categoryResult._id + '</p>';
                html += '<p>表情包ID: ' + emojiResult._id + '</p>';
                html += '<h5>创建的数据:</h5>';
                html += '<pre>分类: ' + JSON.stringify(testCategory, null, 2) + '</pre>';
                html += '<pre>表情包: ' + JSON.stringify(testEmoji, null, 2) + '</pre>';
                html += '</div>';

                resultDiv.innerHTML = html;

                diagnosticResults.adminDataCreation = {
                    success: true,
                    categoryId: categoryResult._id,
                    emojiId: emojiResult._id,
                    testCategory: testCategory,
                    testEmoji: testEmoji
                };

            } catch (error) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 模拟数据创建失败</h4><p>错误: ' + error.message + '</p></div>';
                diagnosticResults.adminDataCreation = { success: false, error: error.message };
            }
        }

        // 步骤5: 验证数据读取链路
        async function verifyDataReadChain() {
            const resultDiv = document.getElementById('dataReadResult');
            resultDiv.innerHTML = '<p>正在验证数据读取链路...</p>';

            if (!diagnosticResults.adminDataCreation || !diagnosticResults.adminDataCreation.success) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 请先执行步骤4创建测试数据</h4></div>';
                return;
            }

            try {
                const testCategoryName = diagnosticResults.adminDataCreation.testCategory.name;

                // 测试云函数是否能读取到刚创建的数据
                const categoriesResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });

                const emojisResult = await app.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getEmojis', data: { category: testCategoryName, page: 1, limit: 10 } }
                });

                // 检查是否能找到测试数据
                const foundCategory = categoriesResult.result.data?.find(cat => cat.name === testCategoryName);
                const foundEmojis = emojisResult.result.data || [];

                let html = '<div class="' + (foundCategory && foundEmojis.length > 0 ? 'success' : 'error') + '">';
                html += '<h4>' + (foundCategory && foundEmojis.length > 0 ? '✅' : '❌') + ' 数据读取链路验证</h4>';
                html += '<p>测试分类名称: ' + testCategoryName + '</p>';
                html += '<p>是否找到分类: ' + (foundCategory ? '是' : '否') + '</p>';
                html += '<p>找到的表情包数量: ' + foundEmojis.length + '</p>';

                if (foundCategory) {
                    html += '<h5>找到的分类:</h5><pre>' + JSON.stringify(foundCategory, null, 2) + '</pre>';
                }
                if (foundEmojis.length > 0) {
                    html += '<h5>找到的表情包:</h5><pre>' + JSON.stringify(foundEmojis, null, 2) + '</pre>';
                }

                html += '</div>';
                resultDiv.innerHTML = html;

                diagnosticResults.dataReadChain = {
                    success: foundCategory && foundEmojis.length > 0,
                    foundCategory: foundCategory,
                    foundEmojis: foundEmojis,
                    testCategoryName: testCategoryName
                };

            } catch (error) {
                resultDiv.innerHTML = '<div class="error"><h4>❌ 数据读取链路验证失败</h4><p>错误: ' + error.message + '</p></div>';
                diagnosticResults.dataReadChain = { success: false, error: error.message };
            }
        }

        // 步骤6: 生成诊断报告
        function generateDiagnosticReport() {
            const resultDiv = document.getElementById('diagnosticReport');
            
            let html = '<div class="info"><h4>📋 诊断报告</h4>';
            
            // 分析问题
            const issues = [];
            const solutions = [];

            if (!diagnosticResults.cloudConnection?.success) {
                issues.push('云开发连接失败');
                solutions.push('检查网络连接和环境ID配置');
            }

            if (!diagnosticResults.databaseData?.success) {
                issues.push('数据库访问失败');
                solutions.push('检查数据库权限配置');
            } else if (diagnosticResults.databaseData.emojis.length === 0) {
                issues.push('数据库中没有表情包数据');
                solutions.push('确认管理后台是否成功保存数据');
            }

            if (!diagnosticResults.dataAPI?.success) {
                issues.push('dataAPI云函数调用失败');
                solutions.push('检查云函数部署状态和代码');
            } else if (!diagnosticResults.dataAPI.categories?.success) {
                issues.push('dataAPI无法正确返回分类数据');
                solutions.push('检查云函数中的分类查询逻辑');
            }

            if (!diagnosticResults.dataReadChain?.success) {
                issues.push('数据读取链路不通');
                solutions.push('检查字段匹配逻辑和查询条件');
            }

            html += '<h5>发现的问题:</h5><ul>';
            issues.forEach(issue => html += '<li>' + issue + '</li>');
            html += '</ul>';

            html += '<h5>建议的解决方案:</h5><ul>';
            solutions.forEach(solution => html += '<li>' + solution + '</li>');
            html += '</ul>';

            html += '<h5>完整诊断数据:</h5>';
            html += '<pre>' + JSON.stringify(diagnosticResults, null, 2) + '</pre>';

            html += '</div>';
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
