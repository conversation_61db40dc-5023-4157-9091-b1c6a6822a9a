// utils/stateManager.js - 统一的数据状态管理器

const { AuthManager } = require('./authManager.js')
const { CloudDataService } = require('./cloudDataService.js')
const { UserActionSync } = require('./userActionSync.js')

/**
 * 全局数据状态管理器
 * 负责统一管理点赞、收藏、下载等状态数据
 * 确保所有页面使用相同的数据源
 */
const StateManager = {
  // 内存中的状态数据
  _state: {
    likedEmojis: new Set(),      // 已点赞的表情包ID集合
    collectedEmojis: new Set(),  // 已收藏的表情包ID集合
    downloadedEmojis: new Set(), // 已下载的表情包ID集合
    downloadTimes: {},           // 下载时间记录
    collectTimes: {},            // 收藏时间记录
    isInitialized: false        // 是否已初始化
  },

  // 状态变更监听器（增强版，支持页面ID关联）
  _listeners: {
    like: [],      // 点赞状态变更监听器
    collect: [],   // 收藏状态变更监听器
    download: [],  // 下载状态变更监听器
    global: []     // 全局状态变更监听器
  },

  // 页面监听器映射（用于自动清理）
  _pageListeners: new Map(), // pageId -> { type: [callback, ...], ... }

  /**
   * 初始化状态管理器
   */
  init() {
    if (this._state.isInitialized) {
      console.log('⚠️ StateManager 已经初始化，跳过重复初始化')
      return
    }

    console.log('🔄 StateManager 初始化开始')

    try {
      // 初始化云端数据服务
      CloudDataService.init()

      // 初始化用户操作同步管理器
      UserActionSync.init()

      // 从本地存储加载数据
      this.loadFromLocalStorage()

      // 如果是首次使用，初始化一些测试数据
      this.initTestDataIfNeeded()

      // 标记为已初始化
      this._state.isInitialized = true
      this._state.initTime = Date.now()

      // 监听登录状态变化
      const { AuthManager } = require('./authManager.js')
      AuthManager.addLoginListener(async (isLoggedIn) => {
        if (isLoggedIn) {
          console.log('📡 用户登录，开始同步云端数据')
          await this.syncFromCloud()
        }
      })

      console.log('✅ StateManager 初始化完成', {
        liked: this._state.likedEmojis.size,
        collected: this._state.collectedEmojis.size,
        downloaded: this._state.downloadedEmojis.size
      })
    } catch (error) {
      console.error('❌ StateManager 初始化失败:', error)
    }
  },

  /**
   * 检查是否已初始化
   */
  isInitialized() {
    return this._state.isInitialized
  },

  /**
   * 获取初始化状态信息
   */
  getInitStatus() {
    return {
      isInitialized: this._state.isInitialized,
      initTime: this._state.initTime,
      likedCount: this._state.likedEmojis.size,
      collectedCount: this._state.collectedEmojis.size,
      downloadedCount: this._state.downloadedEmojis.size
    }
  },

  /**
   * 如果需要，初始化测试数据
   */
  initTestDataIfNeeded() {
    try {
      // 检查是否是首次使用（没有任何数据）
      const hasAnyData = this._state.likedEmojis.size > 0 ||
                        this._state.collectedEmojis.size > 0 ||
                        this._state.downloadedEmojis.size > 0

      // 检查是否已经初始化过测试数据
      const hasInitTestData = wx.getStorageSync('hasInitTestData')

      if (!hasAnyData && !hasInitTestData) {
        console.log('🎯 初始化测试数据，让用户有内容可查看')

        // 添加一些测试点赞数据
        this._state.likedEmojis.add('1')
        this._state.likedEmojis.add('3')
        this._state.likedEmojis.add('5')

        // 添加一些测试收藏数据
        this._state.collectedEmojis.add('2')
        this._state.collectedEmojis.add('4')
        this._state.collectedEmojis.add('6')

        // 添加一些测试下载数据
        this._state.downloadedEmojis.add('1')
        this._state.downloadedEmojis.add('2')
        this._state.downloadedEmojis.add('7')
        this._state.downloadTimes['1'] = new Date(Date.now() - 3600000).toISOString() // 1小时前
        this._state.downloadTimes['2'] = new Date(Date.now() - 86400000).toISOString() // 1天前
        this._state.downloadTimes['7'] = new Date(Date.now() - 300000).toISOString()   // 5分钟前

        // 保存到本地存储
        this.saveToLocalStorage()

        // 标记已初始化测试数据
        wx.setStorageSync('hasInitTestData', true)

        console.log('✅ 测试数据初始化完成', {
          liked: this._state.likedEmojis.size,
          collected: this._state.collectedEmojis.size,
          downloaded: this._state.downloadedEmojis.size
        })

        // 通知监听器数据已更新
        this.notifyListeners('global', { type: 'init', source: 'test' })
      } else {
        console.log('📊 已有数据，跳过测试数据初始化', {
          liked: this._state.likedEmojis.size,
          collected: this._state.collectedEmojis.size,
          downloaded: this._state.downloadedEmojis.size
        })
      }
    } catch (error) {
      console.error('❌ 初始化测试数据失败:', error)
    }
  },

  /**
   * 从本地存储加载数据
   */
  loadFromLocalStorage() {
    try {
      // 加载点赞数据
      const likedEmojis = wx.getStorageSync('likedEmojis') || []
      this._state.likedEmojis = new Set(likedEmojis)
      
      // 加载收藏数据
      const collectedEmojis = wx.getStorageSync('collectedEmojis') || []
      this._state.collectedEmojis = new Set(collectedEmojis)
      
      // 加载下载数据
      const downloadedEmojis = wx.getStorageSync('downloadedEmojis') || []
      this._state.downloadedEmojis = new Set(downloadedEmojis)
      
      // 加载下载时间
      const downloadTimes = wx.getStorageSync('downloadTimes') || {}
      this._state.downloadTimes = downloadTimes

      // 加载收藏时间
      const collectTimes = wx.getStorageSync('collectTimes') || {}
      this._state.collectTimes = collectTimes

      console.log('📱 从本地存储加载数据完成')
    } catch (error) {
      console.error('❌ 从本地存储加载数据失败:', error)
    }
  },

  /**
   * 保存数据到本地存储
   */
  saveToLocalStorage() {
    try {
      wx.setStorageSync('likedEmojis', Array.from(this._state.likedEmojis))
      wx.setStorageSync('collectedEmojis', Array.from(this._state.collectedEmojis))
      wx.setStorageSync('downloadedEmojis', Array.from(this._state.downloadedEmojis))
      wx.setStorageSync('downloadTimes', this._state.downloadTimes)
      wx.setStorageSync('collectTimes', this._state.collectTimes)

      console.log('💾 数据已保存到本地存储')
    } catch (error) {
      console.error('❌ 保存数据到本地存储失败:', error)
    }
  },

  /**
   * 获取表情包的完整状态
   */
  getEmojiState(emojiId) {
    return {
      isLiked: this._state.likedEmojis.has(emojiId),
      isCollected: this._state.collectedEmojis.has(emojiId),
      isDownloaded: this._state.downloadedEmojis.has(emojiId),
      downloadTime: this._state.downloadTimes[emojiId] || null,
      collectTime: this._state.collectTimes[emojiId] || null
    }
  },

  /**
   * 检查表情包是否被点赞
   */
  isLiked(emojiId) {
    return this._state.likedEmojis.has(emojiId)
  },

  /**
   * 检查表情包是否被收藏
   */
  isCollected(emojiId) {
    return this._state.collectedEmojis.has(emojiId)
  },

  /**
   * 检查表情包是否被下载
   */
  isDownloaded(emojiId) {
    return this._state.downloadedEmojis.has(emojiId)
  },

  /**
   * 获取所有已点赞的表情包ID
   */
  getLikedEmojis() {
    return Array.from(this._state.likedEmojis)
  },

  /**
   * 获取所有已收藏的表情包ID
   */
  getCollectedEmojis() {
    return Array.from(this._state.collectedEmojis)
  },

  /**
   * 获取所有已下载的表情包ID
   */
  getDownloadedEmojis() {
    return Array.from(this._state.downloadedEmojis)
  },

  /**
   * 切换点赞状态
   */
  toggleLike(emojiId) {
    const wasLiked = this._state.likedEmojis.has(emojiId)
    const isLiked = !wasLiked

    if (isLiked) {
      this._state.likedEmojis.add(emojiId)
    } else {
      this._state.likedEmojis.delete(emojiId)
    }

    // 保存到本地存储
    this.saveToLocalStorage()

    // 通知监听器
    this.notifyListeners('like', { emojiId, isLiked, wasLiked })
    this.notifyListeners('global', { type: 'like', emojiId, isLiked, wasLiked })

    // 同步到云端
    UserActionSync.syncLike(emojiId, isLiked)
    
    console.log(`👍 表情包 ${emojiId} 点赞状态: ${wasLiked} → ${isLiked}`)

    return isLiked
  },

  /**
   * 切换收藏状态
   */
  toggleCollect(emojiId) {
    const wasCollected = this._state.collectedEmojis.has(emojiId)
    const isCollected = !wasCollected

    if (isCollected) {
      this._state.collectedEmojis.add(emojiId)
      // 记录收藏时间
      this._state.collectTimes[emojiId] = Date.now()
    } else {
      this._state.collectedEmojis.delete(emojiId)
      // 删除收藏时间记录
      delete this._state.collectTimes[emojiId]
    }

    // 保存到本地存储
    this.saveToLocalStorage()

    // 通知监听器
    this.notifyListeners('collect', { emojiId, isCollected, wasCollected })
    this.notifyListeners('global', { type: 'collect', emojiId, isCollected, wasCollected })

    // 同步到云端
    UserActionSync.syncCollect(emojiId, isCollected)

    console.log(`⭐ 表情包 ${emojiId} 收藏状态: ${wasCollected} → ${isCollected}`)

    return isCollected
  },

  /**
   * 记录下载
   */
  recordDownload(emojiId) {
    this._state.downloadedEmojis.add(emojiId)
    this._state.downloadTimes[emojiId] = new Date().toISOString()

    // 保存到本地存储
    this.saveToLocalStorage()

    // 通知监听器
    this.notifyListeners('download', { emojiId, downloadTime: this._state.downloadTimes[emojiId] })
    this.notifyListeners('global', { type: 'download', emojiId, downloadTime: this._state.downloadTimes[emojiId] })

    // 同步到云端
    UserActionSync.syncDownload(emojiId)
    
    console.log(`📥 表情包 ${emojiId} 下载已记录`)
  },

  /**
   * 添加状态变更监听器
   * @param {string} type - 监听器类型
   * @param {function} callback - 回调函数
   * @param {string} pageId - 页面ID（可选，用于自动清理）
   */
  addListener(type, callback, pageId = null) {
    if (this._listeners[type]) {
      this._listeners[type].push(callback)

      // 如果提供了页面ID，记录关联关系
      if (pageId) {
        if (!this._pageListeners.has(pageId)) {
          this._pageListeners.set(pageId, {})
        }
        const pageListeners = this._pageListeners.get(pageId)
        if (!pageListeners[type]) {
          pageListeners[type] = []
        }
        pageListeners[type].push(callback)
      }

      console.log(`📡 添加 ${type} 监听器${pageId ? ` (页面: ${pageId})` : ''}`)
    }
  },

  /**
   * 移除状态变更监听器
   */
  removeListener(type, callback) {
    if (this._listeners[type]) {
      const index = this._listeners[type].indexOf(callback)
      if (index > -1) {
        this._listeners[type].splice(index, 1)
        console.log(`📡 移除 ${type} 监听器`)
      }
    }
  },

  /**
   * 移除指定页面的所有监听器
   * @param {string} pageId - 页面ID
   */
  removePageListeners(pageId) {
    if (!this._pageListeners.has(pageId)) {
      return
    }

    const pageListeners = this._pageListeners.get(pageId)
    let removedCount = 0

    // 遍历该页面的所有监听器类型
    for (const [type, callbacks] of Object.entries(pageListeners)) {
      if (this._listeners[type]) {
        // 移除每个回调函数
        callbacks.forEach(callback => {
          const index = this._listeners[type].indexOf(callback)
          if (index > -1) {
            this._listeners[type].splice(index, 1)
            removedCount++
          }
        })
      }
    }

    // 清理页面监听器记录
    this._pageListeners.delete(pageId)
    console.log(`🧹 清理页面 ${pageId} 的 ${removedCount} 个监听器`)
  },

  /**
   * 获取监听器统计信息
   */
  getListenerStats() {
    const stats = {}
    for (const [type, listeners] of Object.entries(this._listeners)) {
      stats[type] = listeners.length
    }
    stats.totalPages = this._pageListeners.size
    return stats
  },

  /**
   * 通知监听器
   */
  notifyListeners(type, data) {
    if (this._listeners[type]) {
      this._listeners[type].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`❌ 监听器回调执行失败 (${type}):`, error)
        }
      })
    }
  },

  /**
   * 从云端同步数据
   */
  async syncFromCloud() {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化，跳过云端同步')
      return false
    }
    
    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化，跳过云端同步')
      return false
    }

    if (!app.globalData.isLoggedIn) {
      console.log('💾 用户未登录，跳过云端同步')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          action: 'getAllUserData'
        }
      })

      if (res.result && res.result.success) {
        const userData = res.result.data
        
        // 同步点赞数据
        if (userData.likedEmojis && userData.likedEmojis.length > 0) {
          this._state.likedEmojis = new Set(userData.likedEmojis)
        }
        
        // 同步收藏数据
        if (userData.collectedEmojis && userData.collectedEmojis.length > 0) {
          this._state.collectedEmojis = new Set(userData.collectedEmojis)
        }
        
        // 同步下载数据
        if (userData.downloadedEmojis && userData.downloadedEmojis.length > 0) {
          this._state.downloadedEmojis = new Set(userData.downloadedEmojis)
        }
        
        if (userData.downloadTimes) {
          this._state.downloadTimes = userData.downloadTimes
        }
        
        // 保存到本地存储
        this.saveToLocalStorage()
        
        console.log('✅ 云端数据同步成功')
        return true
      } else {
        console.log('❌ 云端数据同步失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 云端数据同步异常:', error)
      return false
    }
  },

  /**
   * 同步数据到云端
   */
  async syncToCloud() {
    const app = getApp()
    if (!app || !app.globalData) {
      console.log('💾 App 未初始化，跳过云端同步')
      return false
    }
    
    if (!app.globalData.cloudInitialized) {
      console.log('💾 云开发未初始化，跳过云端同步')
      return false
    }

    if (!app.globalData.isLoggedIn) {
      console.log('💾 用户未登录，跳过云端同步')
      return false
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          likedEmojis: Array.from(this._state.likedEmojis),
          collectedEmojis: Array.from(this._state.collectedEmojis),
          downloadedEmojis: Array.from(this._state.downloadedEmojis),
          downloadTimes: this._state.downloadTimes
        }
      })

      if (res.result && res.result.success) {
        console.log('✅ 本地数据同步到云端成功')
        return true
      } else {
        console.log('❌ 本地数据同步到云端失败:', res.result?.error)
        return false
      }
    } catch (error) {
      console.error('❌ 本地数据同步到云端异常:', error)
      return false
    }
  },

  /**
   * 同步点赞操作到云端
   */
  async syncLikeToCloud(emojiId, isLiked) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log(`💾 云端不可用，跳过点赞同步 ${emojiId} → ${isLiked}`)
      return
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'updateLike',
          emojiId: emojiId,
          isLiked: isLiked
        }
      })

      if (res.result && res.result.success) {
        console.log(`✅ 点赞同步成功 ${emojiId} → ${isLiked}`)
      } else {
        console.log(`❌ 点赞同步失败 ${emojiId}:`, res.result?.error)
      }
    } catch (error) {
      console.error(`❌ 点赞同步异常 ${emojiId}:`, error)
    }
  },

  /**
   * 同步收藏操作到云端
   */
  async syncCollectToCloud(emojiId, isCollected) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log(`💾 云端不可用，跳过收藏同步 ${emojiId} → ${isCollected}`)
      return
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'updateCollect',
          emojiId: emojiId,
          isCollected: isCollected
        }
      })

      if (res.result && res.result.success) {
        console.log(`✅ 收藏同步成功 ${emojiId} → ${isCollected}`)
      } else {
        console.log(`❌ 收藏同步失败 ${emojiId}:`, res.result?.error)
      }
    } catch (error) {
      console.error(`❌ 收藏同步异常 ${emojiId}:`, error)
    }
  },

  /**
   * 同步下载操作到云端
   */
  async syncDownloadToCloud(emojiId) {
    const app = getApp()
    if (!app || !app.globalData || !app.globalData.cloudInitialized || !app.globalData.isLoggedIn) {
      console.log(`💾 云端不可用，跳过下载同步 ${emojiId}`)
      return
    }

    try {
      const res = await wx.cloud.callFunction({
        name: 'updateUserStats',
        data: {
          action: 'updateDownload',
          emojiId: emojiId,
          downloadTime: this._state.downloadTimes[emojiId]
        }
      })

      if (res.result && res.result.success) {
        console.log(`✅ 下载同步成功 ${emojiId}`)
      } else {
        console.log(`❌ 下载同步失败 ${emojiId}:`, res.result?.error)
      }
    } catch (error) {
      console.error(`❌ 下载同步异常 ${emojiId}:`, error)
    }
  },

  /**
   * 清空所有状态数据
   */
  clearAll() {
    this._state.likedEmojis.clear()
    this._state.collectedEmojis.clear()
    this._state.downloadedEmojis.clear()
    this._state.downloadTimes = {}

    // 清除本地存储
    try {
      wx.removeStorageSync('likedEmojis')
      wx.removeStorageSync('collectedEmojis')
      wx.removeStorageSync('downloadedEmojis')
      wx.removeStorageSync('downloadTimes')
      wx.removeStorageSync('hasInitTestData') // 清除测试数据标记
    } catch (error) {
      console.error('❌ 清除本地存储失败:', error)
    }

    // 通知监听器
    this.notifyListeners('global', { type: 'clear' })

    console.log('🗑️ 所有状态数据已清空')
  },

  /**
   * 重置为测试数据
   */
  resetToTestData() {
    console.log('🔄 重置为测试数据')

    // 先清空所有数据
    this.clearAll()

    // 重新初始化测试数据
    this.initTestDataIfNeeded()

    wx.showToast({
      title: '数据已重置',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 重置测试数据（别名方法）
   */
  resetTestData() {
    console.log('🔄 重置测试数据')

    // 清除已初始化标记
    wx.removeStorageSync('hasInitTestData')

    // 先清空所有数据
    this.clearAll()

    // 重新初始化测试数据
    this.initTestDataIfNeeded()

    console.log('✅ 测试数据重置完成')
  }
}

module.exports = {
  StateManager
}
