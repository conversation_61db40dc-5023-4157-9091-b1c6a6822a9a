# 表情包小程序部署指南

## 🚀 部署前准备

### 1. 环境要求
- 微信开发者工具 (最新稳定版)
- Node.js 14+ 
- 已注册的小程序账号

### 2. 配置步骤

#### 步骤1: 配置小程序信息
1. 在 `project.config.json` 中填入你的 appid
2. 在微信开发者工具中导入项目

#### 步骤2: 开通云开发
1. 在微信开发者工具中点击"云开发"
2. 开通云开发服务，创建环境
3. 记录云环境ID，填入 `project.config.json` 的 cloudenv 字段

#### 步骤3: 部署云函数
1. 右键 `cloudfunctions/dataAPI` -> 上传并部署
2. 右键 `cloudfunctions/syncAPI` -> 上传并部署  
3. 右键 `cloudfunctions/login` -> 上传并部署
4. 右键 `cloudfunctions/getOpenID` -> 上传并部署

#### 步骤4: 配置数据库
1. 在云开发控制台创建以下集合:
   - emojis (表情包数据)
   - categories (分类数据)
   - banners (轮播图数据)
   - users (用户数据)
   - user_actions (用户行为数据)
   - data_versions (版本管理数据)

2. 配置数据库权限 (参考 database/permissions.json)

#### 步骤5: 初始化数据
1. 在小程序中调用数据初始化功能
2. 或通过云函数 `dataAPI` 的 `initTestData` 方法初始化

## 🧪 测试验证

### 功能测试
- [ ] 小程序正常启动
- [ ] 数据正常加载显示
- [ ] 表情包搜索功能正常
- [ ] 用户交互(点赞、收藏)正常
- [ ] 实时同步功能正常

### 性能测试  
- [ ] 首屏加载时间 < 3秒
- [ ] 图片加载流畅
- [ ] 缓存机制正常工作
- [ ] 内存使用合理

## 📱 发布上线

### 提交审核前检查
- [ ] 完成所有功能测试
- [ ] 检查用户隐私协议
- [ ] 确认服务器域名配置
- [ ] 测试真机兼容性

### 版本管理
- 建议使用语义化版本号 (如 1.0.0)
- 每次发布前打tag标记
- 保留历史版本的云函数备份

## 🔧 常见问题

### Q: 云函数调用失败
A: 检查云环境ID配置，确保云函数已正确部署

### Q: 数据加载失败  
A: 检查数据库权限配置，确保集合已创建

### Q: 实时同步不工作
A: 检查网络连接，确保syncAPI云函数正常运行

## 📞 技术支持

如遇到部署问题，请检查:
1. 微信开发者工具控制台错误信息
2. 云开发控制台日志
3. 网络请求状态

---
生成时间: 2025/7/19 11:38:13
