/**
 * 同步状态管理器
 * 用于管理小程序端的实时同步状态显示
 */

class SyncStatusManager {
  constructor() {
    this.isInitialized = false
    this.currentStatus = 'idle' // idle, connecting, connected, syncing, error
    this.lastSyncTime = null
    this.syncQueue = new Set()
    this.statusCallbacks = new Set()
    
    console.log('📡 SyncStatusManager 初始化')
  }

  /**
   * 初始化同步状态管理器
   */
  init() {
    if (this.isInitialized) return

    try {
      // 从本地存储恢复最后同步时间
      const lastSyncTime = wx.getStorageSync('lastSyncTime')
      if (lastSyncTime) {
        this.lastSyncTime = new Date(lastSyncTime)
      }

      this.isInitialized = true
      console.log('✅ SyncStatusManager 初始化完成')
    } catch (error) {
      console.error('❌ SyncStatusManager 初始化失败:', error)
    }
  }

  /**
   * 更新连接状态
   */
  updateConnectionStatus(status, info = {}) {
    this.currentStatus = status
    
    const statusInfo = {
      status,
      timestamp: new Date(),
      ...info
    }

    console.log(`📡 连接状态更新: ${status}`, statusInfo)
    
    // 通知所有监听器
    this.notifyStatusChange(statusInfo)
    
    // 显示状态提示
    this.showStatusToast(status, info)
  }

  /**
   * 更新同步状态
   */
  updateSyncStatus(type, status, details = {}) {
    const syncInfo = {
      type,
      status,
      timestamp: new Date(),
      details
    }

    console.log(`🔄 同步状态更新: ${type} - ${status}`, syncInfo)

    if (status === 'start') {
      this.syncQueue.add(type)
      this.currentStatus = 'syncing'
    } else if (status === 'complete' || status === 'error') {
      this.syncQueue.delete(type)
      if (this.syncQueue.size === 0) {
        this.currentStatus = 'connected'
      }
      
      if (status === 'complete') {
        this.lastSyncTime = new Date()
        this.saveLastSyncTime()
      }
    }

    // 通知所有监听器
    this.notifyStatusChange(syncInfo)
    
    // 显示同步提示
    this.showSyncToast(type, status, details)
  }

  /**
   * 显示状态提示
   */
  showStatusToast(status, info = {}) {
    const statusMessages = {
      'connecting': '正在连接实时服务...',
      'connected': '实时同步已连接',
      'error': '连接失败，正在重试...',
      'disconnected': '实时同步已断开'
    }

    const message = statusMessages[status]
    if (!message) return

    const toastConfig = {
      title: message,
      duration: 2000
    }

    if (status === 'connected') {
      toastConfig.icon = 'success'
    } else if (status === 'error') {
      toastConfig.icon = 'none'
    } else {
      toastConfig.icon = 'loading'
    }

    wx.showToast(toastConfig)
  }

  /**
   * 显示同步提示
   */
  showSyncToast(type, status, details = {}) {
    const typeNames = {
      'categories': '分类',
      'emojis': '表情包',
      'banners': '横幅'
    }

    const typeName = typeNames[type] || type

    if (status === 'start') {
      wx.showToast({
        title: `正在同步${typeName}...`,
        icon: 'loading',
        duration: 1500
      })
    } else if (status === 'complete') {
      wx.showToast({
        title: `${typeName}同步完成`,
        icon: 'success',
        duration: 1500
      })
    } else if (status === 'error') {
      wx.showToast({
        title: `${typeName}同步失败`,
        icon: 'none',
        duration: 2000
      })
    }
  }

  /**
   * 注册状态变化回调
   */
  onStatusChange(callback) {
    if (typeof callback === 'function') {
      this.statusCallbacks.add(callback)
    }
  }

  /**
   * 移除状态变化回调
   */
  offStatusChange(callback) {
    this.statusCallbacks.delete(callback)
  }

  /**
   * 通知状态变化
   */
  notifyStatusChange(statusInfo) {
    this.statusCallbacks.forEach(callback => {
      try {
        callback(statusInfo)
      } catch (error) {
        console.error('❌ 状态回调执行失败:', error)
      }
    })
  }

  /**
   * 获取当前状态
   */
  getCurrentStatus() {
    return {
      status: this.currentStatus,
      lastSyncTime: this.lastSyncTime,
      syncQueue: Array.from(this.syncQueue),
      isConnected: this.currentStatus === 'connected' || this.currentStatus === 'syncing'
    }
  }

  /**
   * 获取格式化的最后同步时间
   */
  getFormattedLastSyncTime() {
    if (!this.lastSyncTime) {
      return '未同步'
    }

    const now = new Date()
    const diff = now.getTime() - this.lastSyncTime.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚更新'
    } else if (diff < 3600000) { // 1小时内
      const minutes = Math.floor(diff / 60000)
      return `${minutes}分钟前更新`
    } else if (diff < 86400000) { // 24小时内
      const hours = Math.floor(diff / 3600000)
      return `${hours}小时前更新`
    } else {
      return this.lastSyncTime.toLocaleDateString() + ' 更新'
    }
  }

  /**
   * 保存最后同步时间
   */
  saveLastSyncTime() {
    try {
      if (this.lastSyncTime) {
        wx.setStorageSync('lastSyncTime', this.lastSyncTime.toISOString())
      }
    } catch (error) {
      console.warn('⚠️ 保存最后同步时间失败:', error)
    }
  }

  /**
   * 手动触发同步状态更新
   */
  triggerStatusUpdate() {
    const currentStatus = this.getCurrentStatus()
    this.notifyStatusChange({
      type: 'manual_update',
      status: 'info',
      timestamp: new Date(),
      details: currentStatus
    })
  }

  /**
   * 重置状态
   */
  reset() {
    this.currentStatus = 'idle'
    this.lastSyncTime = null
    this.syncQueue.clear()
    
    try {
      wx.removeStorageSync('lastSyncTime')
    } catch (error) {
      console.warn('⚠️ 清除同步时间存储失败:', error)
    }
    
    console.log('🔄 SyncStatusManager 状态已重置')
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.statusCallbacks.clear()
    this.syncQueue.clear()
    this.isInitialized = false
    
    console.log('🗑️ SyncStatusManager 已销毁')
  }
}

// 创建全局实例
const syncStatusManager = new SyncStatusManager()

module.exports = {
  SyncStatusManager,
  syncStatusManager
}
