<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试数据</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #666;
            margin-top: 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 创建测试数据</h1>
        
        <div class="section">
            <h2>📋 操作面板</h2>
            <button onclick="initCloudSDK()">初始化云开发SDK</button>
            <button onclick="createAllTestData()" id="createAllBtn" disabled>创建所有测试数据</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h2>📊 分步操作</h2>
            <button onclick="createTestCategories()" id="createCategoriesBtn" disabled>创建2个分类</button>
            <button onclick="createTestEmojis()" id="createEmojisBtn" disabled>创建2个表情包</button>
            <button onclick="createTestBanners()" id="createBannersBtn" disabled>创建2个横幅</button>
        </div>

        <div class="section">
            <h2>📝 操作日志</h2>
            <div id="log" class="log">等待初始化云开发SDK...</div>
        </div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;
        let isInitialized = false;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 初始化云开发SDK
        async function initCloudSDK() {
            try {
                log('🚀 开始初始化云开发SDK...');

                // 检查SDK可用性
                log('🔍 检查SDK可用性...');
                log('  - typeof cloudbase: ' + typeof cloudbase);
                log('  - window.cloudbase: ' + typeof window.cloudbase);

                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK 2.0未加载，请检查网络连接或刷新页面');
                }

                log('🎯 使用CloudBase SDK 2.17.5版本');

                // CloudBase 2.0版本需要clientId参数
                const envId = 'cloud1-5g6pvnpl88dc0142';
                const initConfig = {
                    env: envId,
                    clientId: envId  // SDK 2.0必需参数
                };

                log('🔧 使用CloudBase 2.0配置（包含clientId）');
                tcbApp = window.cloudbase.init(initConfig);

                log('✅ CloudBase SDK 2.0初始化成功');
                log('🌐 环境ID: ' + envId);

                // 匿名登录
                log('🔐 进行匿名登录...');
                const auth = tcbApp.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功', 'success');

                // 测试数据库连接
                log('🔍 测试数据库连接...');
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();
                log('✅ 数据库连接测试成功', 'success');

                isInitialized = true;
                
                // 启用按钮
                document.getElementById('createAllBtn').disabled = false;
                document.getElementById('createCategoriesBtn').disabled = false;
                document.getElementById('createEmojisBtn').disabled = false;
                document.getElementById('createBannersBtn').disabled = false;
                
                log('✅ 云开发SDK初始化完成，可以开始创建测试数据', 'success');
                
            } catch (error) {
                log('❌ 云开发SDK初始化失败: ' + error.message, 'error');
            }
        }

        // 创建测试分类
        async function createTestCategories() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('📂 开始创建测试分类...');
                const db = tcbApp.database();

                const categories = [
                    {
                        name: '搞笑表情',
                        icon: '😂',
                        description: '各种搞笑有趣的表情包',
                        sort: 1,
                        status: 'show',
                        count: 0,
                        createTime: new Date(),
                        updateTime: new Date()
                    },
                    {
                        name: '可爱萌宠',
                        icon: '🐱',
                        description: '可爱的小动物表情包',
                        sort: 2,
                        status: 'show',
                        count: 0,
                        createTime: new Date(),
                        updateTime: new Date()
                    }
                ];

                for (let i = 0; i < categories.length; i++) {
                    const category = categories[i];
                    const result = await db.collection('categories').add({
                        data: category
                    });
                    log(`✅ 创建分类成功: ${category.name} (ID: ${result._id})`, 'success');
                }

                log('✅ 所有测试分类创建完成', 'success');
                
            } catch (error) {
                log('❌ 创建测试分类失败: ' + error.message, 'error');
            }
        }

        // 创建测试表情包
        async function createTestEmojis() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('😀 开始创建测试表情包...');
                const db = tcbApp.database();

                // 先获取分类
                const categoriesResult = await db.collection('categories').get();
                if (categoriesResult.data.length === 0) {
                    log('❌ 没有找到分类，请先创建分类', 'error');
                    return;
                }

                const emojis = [
                    {
                        title: '哈哈大笑',
                        category: categoriesResult.data[0].name,
                        categoryId: categoriesResult.data[0]._id,
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8Y2lyY2xlIGN4PSIzNSIgY3k9IjQwIiByPSI1IiBmaWxsPSIjMzMzIi8+CiAgPGNpcmNsZSBjeD0iNjUiIGN5PSI0MCIgcj0iNSIgZmlsbD0iIzMzMyIvPgogIDxwYXRoIGQ9Ik0zMCA2NSBRNTAgNzUgNzAgNjUiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+Cjwvc3ZnPg==',
                        description: '开心大笑的表情',
                        tags: ['开心', '大笑', '搞笑'],
                        status: 'published',
                        likes: 0,
                        downloads: 0,
                        collections: 0,
                        views: 0,
                        createTime: new Date(),
                        updateTime: new Date()
                    },
                    {
                        title: '可爱小猫',
                        category: categoriesResult.data[1] ? categoriesResult.data[1].name : categoriesResult.data[0].name,
                        categoryId: categoriesResult.data[1] ? categoriesResult.data[1]._id : categoriesResult.data[0]._id,
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZWxsaXBzZSBjeD0iNTAiIGN5PSI2MCIgcng9IjMwIiByeT0iMjUiIGZpbGw9IiNGRkE1MDAiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIyIi8+CiAgPHBvbHlnb24gcG9pbnRzPSIzNSw0MCA0NSw1MCAyNSw1MCIgZmlsbD0iI0ZGQTUwMCIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8cG9seWdvbiBwb2ludHM9IjY1LDQwIDc1LDUwIDU1LDUwIiBmaWxsPSIjRkZBNTAwIiBzdHJva2U9IiMzMzMiIHN0cm9rZS13aWR0aD0iMiIvPgogIDxjaXJjbGUgY3g9IjQwIiBjeT0iNTUiIHI9IjMiIGZpbGw9IiMzMzMiLz4KICA8Y2lyY2xlIGN4PSI2MCIgY3k9IjU1IiByPSIzIiBmaWxsPSIjMzMzIi8+CiAgPGVsbGlwc2UgY3g9IjUwIiBjeT0iNjUiIHJ4PSI4IiByeT0iNSIgZmlsbD0iI0ZGNjk5NCIvPgo8L3N2Zz4=',
                        description: '超级可爱的小猫咪',
                        tags: ['可爱', '小猫', '萌宠'],
                        status: 'published',
                        likes: 0,
                        downloads: 0,
                        collections: 0,
                        views: 0,
                        createTime: new Date(),
                        updateTime: new Date()
                    }
                ];

                for (let i = 0; i < emojis.length; i++) {
                    const emoji = emojis[i];
                    const result = await db.collection('emojis').add({
                        data: emoji
                    });
                    log(`✅ 创建表情包成功: ${emoji.title} (ID: ${result._id})`, 'success');
                }

                log('✅ 所有测试表情包创建完成', 'success');
                
            } catch (error) {
                log('❌ 创建测试表情包失败: ' + error.message, 'error');
            }
        }

        // 创建测试横幅
        async function createTestBanners() {
            if (!isInitialized) {
                log('❌ 请先初始化云开发SDK', 'error');
                return;
            }

            try {
                log('🎨 开始创建测试横幅...');
                const db = tcbApp.database();

                const banners = [
                    {
                        title: '欢迎使用表情包小程序',
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzQyODVGNCIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mrKLov47kvb/nlKjooajmg4XljIXlsI/nqIvluo88L3RleHQ+Cjwvc3ZnPg==',
                        linkType: 'page',
                        link: '/pages/index/index',
                        priority: 1,
                        status: 'show',
                        description: '欢迎横幅',
                        createTime: new Date(),
                        updateTime: new Date()
                    },
                    {
                        title: '精选表情包推荐',
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iI0ZGNjk5NCIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7nsr7pgInooajmg4XljIXmjqjojZA8L3RleHQ+Cjwvc3ZnPg==',
                        linkType: 'page',
                        link: '/pages/category/category',
                        priority: 2,
                        status: 'show',
                        description: '推荐横幅',
                        createTime: new Date(),
                        updateTime: new Date()
                    }
                ];

                for (let i = 0; i < banners.length; i++) {
                    const banner = banners[i];
                    const result = await db.collection('banners').add({
                        data: banner
                    });
                    log(`✅ 创建横幅成功: ${banner.title} (ID: ${result._id})`, 'success');
                }

                log('✅ 所有测试横幅创建完成', 'success');
                
            } catch (error) {
                log('❌ 创建测试横幅失败: ' + error.message, 'error');
            }
        }

        // 创建所有测试数据
        async function createAllTestData() {
            log('🚀 开始创建所有测试数据...');
            
            await createTestCategories();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            
            await createTestEmojis();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            
            await createTestBanners();
            
            log('🎉 所有测试数据创建完成！', 'success');
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            log('页面加载完成，请点击"初始化云开发SDK"按钮开始');
        });
    </script>
</body>
</html>
