<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发SDK测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 云开发SDK测试页面</h1>
        <p>测试本地云开发SDK的各项功能</p>

        <!-- SDK状态检查 -->
        <div class="test-section">
            <h3>📊 SDK状态检查</h3>
            <button class="btn" onclick="checkSDK()">检查SDK状态</button>
            <div id="sdkStatus" class="result"></div>
        </div>

        <!-- 初始化测试 -->
        <div class="test-section">
            <h3>🚀 初始化测试</h3>
            <button class="btn" onclick="testInit()">初始化云开发</button>
            <div id="initResult" class="result"></div>
        </div>

        <!-- 身份验证测试 -->
        <div class="test-section">
            <h3>🔐 身份验证测试</h3>
            <button class="btn" onclick="testAuth()">匿名登录</button>
            <button class="btn" onclick="testLogout()">登出</button>
            <div id="authResult" class="result"></div>
        </div>

        <!-- 数据库测试 -->
        <div class="test-section">
            <h3>📊 数据库测试</h3>
            <button class="btn" onclick="testDatabase()">测试数据库连接</button>
            <button class="btn" onclick="testAddData()">添加测试数据</button>
            <button class="btn" onclick="testQueryData()">查询数据</button>
            <div id="dbResult" class="result"></div>
        </div>

        <!-- 云函数测试 -->
        <div class="test-section">
            <h3>☁️ 云函数测试</h3>
            <button class="btn" onclick="testCloudFunction()">调用云函数</button>
            <div id="functionResult" class="result"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🎯 综合测试</h3>
            <button class="btn" onclick="runAllTests()">运行全部测试</button>
            <button class="btn" onclick="runDiagnostic()">专业诊断</button>
            <div id="allTestsResult" class="result"></div>
        </div>

        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="testLog" class="result log"></div>
        </div>
    </div>

    <!-- 引入本地SDK和诊断工具 -->
    <script src="./js/cloudbase-js-sdk.min.js"></script>
    <script src="./js/diagnostic.js"></script>
    <script>
        let app = null;
        const envId = 'cloud1-5g6pvnpl88dc0142';

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '';
        }

        // 检查SDK状态
        function checkSDK() {
            log('🔍 检查SDK状态...');
            
            const hasCloudbase = typeof window.cloudbase !== 'undefined';
            const hasTcb = typeof window.tcb !== 'undefined';
            
            const status = `SDK状态检查:
- cloudbase: ${hasCloudbase ? '✅ 已加载' : '❌ 未加载'}
- tcb: ${hasTcb ? '✅ 已加载' : '❌ 未加载'}
- 可用SDK: ${hasCloudbase ? 'cloudbase' : hasTcb ? 'tcb' : '无'}`;

            showResult('sdkStatus', status, hasCloudbase || hasTcb ? 'success' : 'error');
            log(status);
        }

        // 初始化测试
        async function testInit() {
            try {
                log('🚀 开始初始化测试...');
                
                if (!window.cloudbase && !window.tcb) {
                    throw new Error('SDK未加载');
                }

                const sdk = window.cloudbase || window.tcb;
                app = sdk.init({ env: envId });
                
                const message = `✅ 初始化成功
环境ID: ${envId}
SDK版本: 本地版本`;
                
                showResult('initResult', message, 'success');
                log('✅ 初始化成功');
                
            } catch (error) {
                const message = `❌ 初始化失败: ${error.message}`;
                showResult('initResult', message, 'error');
                log(message);
            }
        }

        // 身份验证测试
        async function testAuth() {
            try {
                log('🔐 开始身份验证测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const auth = app.auth();
                const user = await auth.signInAnonymously();
                
                const message = `✅ 匿名登录成功
用户ID: ${user.uid}
登录类型: ${user.loginType}`;
                
                showResult('authResult', message, 'success');
                log('✅ 匿名登录成功，用户ID: ' + user.uid);
                
            } catch (error) {
                const message = `❌ 身份验证失败: ${error.message}`;
                showResult('authResult', message, 'error');
                log(message);
            }
        }

        // 登出测试
        async function testLogout() {
            try {
                log('👋 开始登出测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const auth = app.auth();
                await auth.signOut();
                
                const message = '✅ 登出成功';
                showResult('authResult', message, 'success');
                log(message);
                
            } catch (error) {
                const message = `❌ 登出失败: ${error.message}`;
                showResult('authResult', message, 'error');
                log(message);
            }
        }

        // 数据库测试
        async function testDatabase() {
            try {
                log('📊 开始数据库连接测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const db = app.database();
                const result = await db.collection('categories').count();
                
                const message = `✅ 数据库连接成功
集合: categories
文档数量: ${result.total}`;
                
                showResult('dbResult', message, 'success');
                log('✅ 数据库连接成功');
                
            } catch (error) {
                const message = `❌ 数据库连接失败: ${error.message}`;
                showResult('dbResult', message, 'error');
                log(message);
            }
        }

        // 添加数据测试
        async function testAddData() {
            try {
                log('📝 开始数据写入测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const db = app.database();
                const result = await db.collection('test').add({
                    data: {
                        name: '测试数据',
                        createTime: new Date(),
                        type: 'test'
                    }
                });
                
                const message = `✅ 数据写入成功
文档ID: ${result._id}`;
                
                showResult('dbResult', message, 'success');
                log('✅ 数据写入成功，ID: ' + result._id);
                
            } catch (error) {
                const message = `❌ 数据写入失败: ${error.message}`;
                showResult('dbResult', message, 'error');
                log(message);
            }
        }

        // 查询数据测试
        async function testQueryData() {
            try {
                log('📖 开始数据查询测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const db = app.database();
                const result = await db.collection('categories').get();
                
                const message = `✅ 数据查询成功
返回记录数: ${result.data.length}
示例数据: ${result.data.length > 0 ? JSON.stringify(result.data[0], null, 2) : '无数据'}`;
                
                showResult('dbResult', message, 'success');
                log(`✅ 数据查询成功，返回 ${result.data.length} 条记录`);
                
            } catch (error) {
                const message = `❌ 数据查询失败: ${error.message}`;
                showResult('dbResult', message, 'error');
                log(message);
            }
        }

        // 云函数测试
        async function testCloudFunction() {
            try {
                log('☁️ 开始云函数调用测试...');
                
                if (!app) {
                    throw new Error('请先初始化SDK');
                }

                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'getStats' }
                });
                
                const message = `✅ 云函数调用成功
函数名: adminAPI
返回数据: ${JSON.stringify(result.result, null, 2)}`;
                
                showResult('functionResult', message, 'success');
                log('✅ 云函数调用成功');
                
            } catch (error) {
                const message = `❌ 云函数调用失败: ${error.message}`;
                showResult('functionResult', message, 'error');
                log(message);
            }
        }

        // 运行全部测试
        async function runAllTests() {
            log('🎯 开始运行全部测试...');
            showResult('allTestsResult', '正在运行测试...', 'info');
            
            const tests = [
                { name: 'SDK检查', fn: checkSDK },
                { name: '初始化', fn: testInit },
                { name: '身份验证', fn: testAuth },
                { name: '数据库连接', fn: testDatabase },
                { name: '数据查询', fn: testQueryData },
                { name: '云函数调用', fn: testCloudFunction }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    log(`🔄 运行测试: ${test.name}`);
                    await test.fn();
                    passed++;
                    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
                } catch (error) {
                    log(`❌ 测试失败: ${test.name} - ${error.message}`);
                    failed++;
                }
            }
            
            const summary = `🎯 全部测试完成
✅ 通过: ${passed}
❌ 失败: ${failed}
📊 成功率: ${Math.round(passed / tests.length * 100)}%`;
            
            showResult('allTestsResult', summary, passed > failed ? 'success' : 'error');
            log(summary);
        }

        // 运行专业诊断
        async function runDiagnostic() {
            log('🔬 开始专业诊断...');
            showResult('allTestsResult', '正在运行专业诊断...', 'info');

            if (typeof CloudBaseDiagnostic === 'undefined') {
                const message = '❌ 诊断工具未加载';
                showResult('allTestsResult', message, 'error');
                log(message);
                return;
            }

            const diagnostic = new CloudBaseDiagnostic();
            await diagnostic.runAllDiagnostics();

            const total = diagnostic.results.length;
            const success = diagnostic.results.filter(r => r.status === 'success').length;
            const errors = diagnostic.results.filter(r => r.status === 'error').length;

            const summary = `🔬 专业诊断完成
📊 总测试数: ${total}
✅ 成功: ${success}
❌ 失败: ${errors}
📈 成功率: ${Math.round(success / total * 100)}%

详细结果请查看控制台日志`;

            showResult('allTestsResult', summary, success > errors ? 'success' : 'error');
            log('🔬 专业诊断完成，详细结果已输出到控制台');
        }

        // 页面加载完成后自动检查SDK
        window.addEventListener('load', function() {
            log('🌐 页面加载完成，开始SDK检查...');
            checkSDK();
        });
    </script>
</body>
</html>
