#!/bin/bash

# V1.0 表情包小程序一键部署脚本 (Mac/Linux)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示标题
echo ""
echo "========================================"
echo "🚀 V1.0 表情包小程序一键部署"
echo "========================================"
echo ""

# 检查Node.js
log_info "🔍 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    log_error "❌ Node.js未安装！"
    log_info "💡 请先安装Node.js: https://nodejs.org/"
    exit 1
fi
NODE_VERSION=$(node --version)
log_success "✅ Node.js已安装: $NODE_VERSION"

# 检查npm
log_info "🔍 检查npm..."
if ! command -v npm &> /dev/null; then
    log_error "❌ npm未安装！"
    exit 1
fi
NPM_VERSION=$(npm --version)
log_success "✅ npm已安装: $NPM_VERSION"

# 安装CloudBase CLI
echo ""
log_info "📦 安装CloudBase CLI工具..."
if npm install -g @cloudbase/cli; then
    log_success "✅ CloudBase CLI安装完成"
else
    log_error "❌ CloudBase CLI安装失败！"
    log_info "💡 请检查网络连接或手动安装: npm install -g @cloudbase/cli"
    exit 1
fi

# 检查登录状态
echo ""
log_info "🔍 检查腾讯云登录状态..."
if tcb env:list &> /dev/null; then
    log_success "✅ 已登录腾讯云"
else
    log_error "❌ 未登录腾讯云！"
    echo ""
    log_info "💡 请先登录腾讯云："
    log_info "   1. 运行命令: tcb login"
    log_info "   2. 在浏览器中完成登录"
    log_info "   3. 重新运行此脚本"
    echo ""
    exit 1
fi

# 运行智能部署脚本
echo ""
log_info "🚀 开始智能部署..."
echo "========================================"

if node scripts/智能部署.js; then
    echo ""
    echo "========================================"
    log_success "🎉 部署完成！"
    echo "========================================"
    echo ""
    log_info "📱 接下来的步骤："
    log_info "1. 打开管理后台测试功能"
    log_info "2. 配置微信小程序"
    log_info "3. 上传发布小程序"
    echo ""
    
    # 询问是否打开管理后台
    read -p "是否现在打开链路验证工具？(y/n): " choice
    case "$choice" in 
        y|Y ) 
            log_info "🌐 正在打开链路验证工具..."
            if command -v open &> /dev/null; then
                open 链路打通验证.html
            elif command -v xdg-open &> /dev/null; then
                xdg-open 链路打通验证.html
            else
                log_info "请手动打开: 链路打通验证.html"
            fi
            ;;
        * ) 
            log_info "您可以稍后手动打开: 链路打通验证.html"
            ;;
    esac
    
    echo ""
    log_info "📄 部署日志已保存到: deploy.log"
    log_info "💡 如有问题，请查看日志文件"
    echo ""
    
else
    echo ""
    log_error "❌ 部署失败！"
    log_info "💡 请查看上方错误信息或联系技术支持"
    log_info "📄 详细日志: deploy.log"
    exit 1
fi
