// V1.0 测试环境准备脚本
const fs = require('fs');
const path = require('path');

class TestEnvironmentSetup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.testEnvId = 'test-env-v1-0';
    this.testData = {
      categories: [],
      emojis: [],
      banners: [],
      adminUsers: []
    };
  }

  // T0.1 准备独立的测试环境
  async prepareTestEnvironment() {
    console.log('🧪 准备独立的测试环境...');
    
    try {
      // 1. 创建测试环境配置
      await this.createTestConfig();
      
      // 2. 初始化测试数据库
      await this.initTestDatabase();
      
      // 3. 部署测试云函数
      await this.deployTestFunctions();
      
      // 4. 配置测试权限
      await this.configureTestPermissions();
      
      console.log('✅ 测试环境准备完成');
      
    } catch (error) {
      console.error('❌ 测试环境准备失败:', error);
      throw error;
    }
  }

  // T0.2 准备测试数据
  async prepareTestData() {
    console.log('📊 准备测试数据...');
    
    try {
      // 1. 生成测试分类数据
      await this.generateTestCategories();
      
      // 2. 生成测试表情包数据
      await this.generateTestEmojis();
      
      // 3. 生成测试横幅数据
      await this.generateTestBanners();
      
      // 4. 生成测试用户数据
      await this.generateTestUsers();
      
      // 5. 导入测试数据
      await this.importTestData();
      
      console.log('✅ 测试数据准备完成');
      
    } catch (error) {
      console.error('❌ 测试数据准备失败:', error);
      throw error;
    }
  }

  // T0.3 配置测试工具
  async configureTestTools() {
    console.log('🔧 配置测试工具...');
    
    try {
      // 1. 配置测试框架
      await this.setupTestFramework();
      
      // 2. 配置模拟工具
      await this.setupMockTools();
      
      // 3. 配置断言库
      await this.setupAssertionLibrary();
      
      // 4. 配置测试报告
      await this.setupTestReporting();
      
      console.log('✅ 测试工具配置完成');
      
    } catch (error) {
      console.error('❌ 测试工具配置失败:', error);
      throw error;
    }
  }

  // 创建测试环境配置
  async createTestConfig() {
    const testConfig = {
      environment: 'test',
      envId: this.testEnvId,
      region: 'ap-shanghai',
      
      // 云函数配置
      cloudfunctions: {
        loginAPI: {
          timeout: 10000,
          memorySize: 256,
          environment: {
            JWT_SECRET: 'test-jwt-secret-key',
            JWT_EXPIRES_IN: '1h', // 测试环境短一些
            ADMIN_USERNAME: 'testadmin',
            ADMIN_PASSWORD: 'test123456'
          }
        },
        webAdminAPI: {
          timeout: 15000,
          memorySize: 512,
          environment: {
            JWT_SECRET: 'test-jwt-secret-key'
          }
        },
        dataAPI: {
          timeout: 10000,
          memorySize: 256,
          environment: {
            CACHE_TTL: '60000' // 测试环境缓存时间短一些
          }
        }
      },
      
      // 数据库配置
      database: {
        collections: ['categories', 'emojis', 'banners', 'sync_notifications', 'admin_logs'],
        permissions: {
          read: true,
          write: false // 只允许云函数写入
        }
      },
      
      // 测试配置
      testing: {
        timeout: 30000,
        retries: 3,
        parallel: false,
        coverage: true
      }
    };

    const configPath = path.join(this.projectRoot, 'test/test-config.json');
    fs.writeFileSync(configPath, JSON.stringify(testConfig, null, 2));
    
    console.log('✅ 测试环境配置已创建');
  }

  // 初始化测试数据库
  async initTestDatabase() {
    console.log('🗄️ 初始化测试数据库...');
    
    // 这里应该调用云开发API创建集合和索引
    // 由于在Node.js环境中，我们生成SQL脚本供手动执行
    
    const initScript = `
-- V1.0 测试数据库初始化脚本

-- 创建分类集合
-- db.createCollection('categories')

-- 创建表情包集合  
-- db.createCollection('emojis')

-- 创建横幅集合
-- db.createCollection('banners')

-- 创建同步通知集合
-- db.createCollection('sync_notifications')

-- 创建管理员日志集合
-- db.createCollection('admin_logs')

-- 创建索引
-- categories集合索引
-- db.categories.createIndex({"status": 1, "sort": 1})
-- db.categories.createIndex({"id": 1}, {"unique": true})

-- emojis集合索引
-- db.emojis.createIndex({"status": 1, "createTime": -1})
-- db.emojis.createIndex({"categoryId": 1, "status": 1})
-- db.emojis.createIndex({"tags": 1})
-- db.emojis.createIndex({"id": 1}, {"unique": true})

-- banners集合索引
-- db.banners.createIndex({"status": 1, "sort": 1})
-- db.banners.createIndex({"id": 1}, {"unique": true})

-- sync_notifications集合索引
-- db.sync_notifications.createIndex({"timestamp": -1})
-- db.sync_notifications.createIndex({"dataType": 1, "timestamp": -1})

-- admin_logs集合索引
-- db.admin_logs.createIndex({"timestamp": -1})
-- db.admin_logs.createIndex({"adminId": 1, "timestamp": -1})
`;

    const scriptPath = path.join(this.projectRoot, 'test/init-test-db.sql');
    fs.writeFileSync(scriptPath, initScript);
    
    console.log('✅ 测试数据库初始化脚本已生成');
  }

  // 部署测试云函数
  async deployTestFunctions() {
    console.log('☁️ 部署测试云函数...');
    
    // 生成测试部署脚本
    const deployScript = `#!/bin/bash
# V1.0 测试环境云函数部署脚本

echo "部署测试云函数到环境: ${this.testEnvId}"

# 部署 loginAPI
cd cloudfunctions/loginAPI
npm install
tcb fn deploy loginAPI --env ${this.testEnvId}

# 部署 webAdminAPI  
cd ../webAdminAPI
npm install
tcb fn deploy webAdminAPI --env ${this.testEnvId}

# 部署 dataAPI
cd ../dataAPI
npm install
tcb fn deploy dataAPI --env ${this.testEnvId}

echo "测试云函数部署完成"
`;

    const scriptPath = path.join(this.projectRoot, 'test/deploy-test-functions.sh');
    fs.writeFileSync(scriptPath, deployScript);
    fs.chmodSync(scriptPath, '755');
    
    console.log('✅ 测试云函数部署脚本已生成');
  }

  // 配置测试权限
  async configureTestPermissions() {
    const permissions = {
      database: {
        categories: { read: true, write: false },
        emojis: { read: true, write: false },
        banners: { read: true, write: false },
        sync_notifications: { read: false, write: false },
        admin_logs: { read: false, write: false }
      },
      
      cloudfunctions: {
        loginAPI: ['auth.login', 'auth.validate', 'auth.refresh'],
        webAdminAPI: ['admin.create', 'admin.update', 'admin.delete'],
        dataAPI: ['data.read', 'data.search']
      }
    };

    const permissionsPath = path.join(this.projectRoot, 'test/test-permissions.json');
    fs.writeFileSync(permissionsPath, JSON.stringify(permissions, null, 2));
    
    console.log('✅ 测试权限配置已生成');
  }

  // 生成测试分类数据
  async generateTestCategories() {
    this.testData.categories = [
      {
        id: 'test_cat_001',
        name: '测试分类1',
        icon: 'https://example.com/test-icon1.png',
        description: '用于测试的分类1',
        sort: 1,
        status: 'active',
        emojiCount: 0,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'test_cat_002', 
        name: '测试分类2',
        icon: 'https://example.com/test-icon2.png',
        description: '用于测试的分类2',
        sort: 2,
        status: 'active',
        emojiCount: 0,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'test_cat_003',
        name: '测试分类3',
        icon: 'https://example.com/test-icon3.png', 
        description: '用于测试的分类3',
        sort: 3,
        status: 'inactive',
        emojiCount: 0,
        createTime: new Date(),
        updateTime: new Date()
      }
    ];
    
    console.log('✅ 测试分类数据已生成');
  }

  // 生成测试表情包数据
  async generateTestEmojis() {
    this.testData.emojis = [
      {
        id: 'test_emoji_001',
        title: '测试表情包1',
        imageUrl: 'https://example.com/test-emoji1.png',
        categoryId: 'test_cat_001',
        category: '测试分类1',
        tags: ['测试', '表情包'],
        description: '用于测试的表情包1',
        status: 'published',
        likes: 10,
        downloads: 50,
        collections: 5,
        views: 100,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'test_emoji_002',
        title: '测试表情包2', 
        imageUrl: 'https://example.com/test-emoji2.png',
        categoryId: 'test_cat_001',
        category: '测试分类1',
        tags: ['测试', '开心'],
        description: '用于测试的表情包2',
        status: 'published',
        likes: 20,
        downloads: 80,
        collections: 8,
        views: 150,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'test_emoji_003',
        title: '测试表情包3',
        imageUrl: 'https://example.com/test-emoji3.png',
        categoryId: 'test_cat_002',
        category: '测试分类2', 
        tags: ['测试', '搞笑'],
        description: '用于测试的表情包3',
        status: 'draft',
        likes: 5,
        downloads: 20,
        collections: 2,
        views: 50,
        createTime: new Date(),
        updateTime: new Date()
      }
    ];
    
    console.log('✅ 测试表情包数据已生成');
  }

  // 生成测试横幅数据
  async generateTestBanners() {
    this.testData.banners = [
      {
        id: 'test_banner_001',
        title: '测试横幅1',
        imageUrl: 'https://example.com/test-banner1.png',
        linkUrl: 'https://example.com/test-link1',
        description: '用于测试的横幅1',
        sort: 1,
        status: 'active',
        clickCount: 100,
        createTime: new Date(),
        updateTime: new Date()
      },
      {
        id: 'test_banner_002',
        title: '测试横幅2',
        imageUrl: 'https://example.com/test-banner2.png', 
        linkUrl: 'https://example.com/test-link2',
        description: '用于测试的横幅2',
        sort: 2,
        status: 'active',
        clickCount: 50,
        createTime: new Date(),
        updateTime: new Date()
      }
    ];
    
    console.log('✅ 测试横幅数据已生成');
  }

  // 生成测试用户数据
  async generateTestUsers() {
    this.testData.adminUsers = [
      {
        adminId: 'testadmin',
        username: 'testadmin',
        password: 'test123456', // 实际应该加密
        permissions: ['read', 'write', 'delete'],
        status: 'active',
        createTime: new Date(),
        lastLoginTime: null
      },
      {
        adminId: 'testuser',
        username: 'testuser',
        password: 'user123456',
        permissions: ['read'],
        status: 'active', 
        createTime: new Date(),
        lastLoginTime: null
      }
    ];
    
    console.log('✅ 测试用户数据已生成');
  }

  // 导入测试数据
  async importTestData() {
    // 生成数据导入脚本
    const importScript = `#!/bin/bash
# V1.0 测试数据导入脚本

echo "导入测试数据到环境: ${this.testEnvId}"

# 导入分类数据
tcb db import --env ${this.testEnvId} --collection categories --file test/data/categories.json

# 导入表情包数据
tcb db import --env ${this.testEnvId} --collection emojis --file test/data/emojis.json

# 导入横幅数据
tcb db import --env ${this.testEnvId} --collection banners --file test/data/banners.json

echo "测试数据导入完成"
`;

    // 创建数据目录
    const dataDir = path.join(this.projectRoot, 'test/data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    // 保存测试数据文件
    fs.writeFileSync(path.join(dataDir, 'categories.json'), JSON.stringify(this.testData.categories, null, 2));
    fs.writeFileSync(path.join(dataDir, 'emojis.json'), JSON.stringify(this.testData.emojis, null, 2));
    fs.writeFileSync(path.join(dataDir, 'banners.json'), JSON.stringify(this.testData.banners, null, 2));
    fs.writeFileSync(path.join(dataDir, 'admin_users.json'), JSON.stringify(this.testData.adminUsers, null, 2));

    // 保存导入脚本
    const scriptPath = path.join(this.projectRoot, 'test/import-test-data.sh');
    fs.writeFileSync(scriptPath, importScript);
    fs.chmodSync(scriptPath, '755');
    
    console.log('✅ 测试数据文件和导入脚本已生成');
  }

  // 配置测试框架
  async setupTestFramework() {
    const packageJson = {
      name: 'v1.0-test-suite',
      version: '1.0.0',
      description: 'V1.0 测试套件',
      scripts: {
        test: 'jest',
        'test:watch': 'jest --watch',
        'test:coverage': 'jest --coverage',
        'test:unit': 'jest --testPathPattern=unit',
        'test:integration': 'jest --testPathPattern=integration',
        'test:e2e': 'jest --testPathPattern=e2e'
      },
      devDependencies: {
        jest: '^29.0.0',
        '@jest/globals': '^29.0.0',
        'jest-environment-node': '^29.0.0',
        supertest: '^6.3.0',
        nock: '^13.3.0'
      },
      jest: {
        testEnvironment: 'node',
        collectCoverageFrom: [
          'cloudfunctions/**/*.js',
          'admin-web/js/**/*.js',
          '!**/node_modules/**'
        ],
        coverageDirectory: 'test/coverage',
        testMatch: [
          '**/test/**/*.test.js'
        ],
        setupFilesAfterEnv: ['<rootDir>/test/setup.js']
      }
    };

    const packagePath = path.join(this.projectRoot, 'test/package.json');
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    
    console.log('✅ 测试框架配置已生成');
  }

  // 配置模拟工具
  async setupMockTools() {
    const mockConfig = `
// 测试模拟工具配置
const nock = require('nock');

// 模拟云开发API
function mockCloudbaseAPI() {
  // 模拟云函数调用
  nock('https://tcb-api.tencentcloudapi.com')
    .persist()
    .post('/v1/functions/invoke')
    .reply(200, {
      code: 0,
      message: 'success',
      data: {}
    });

  // 模拟数据库操作
  nock('https://tcb-api.tencentcloudapi.com')
    .persist()
    .post('/v1/database/query')
    .reply(200, {
      code: 0,
      message: 'success',
      data: []
    });
}

// 模拟JWT令牌
function mockJWTToken() {
  return 'mock.jwt.token';
}

// 模拟管理员信息
function mockAdminInfo() {
  return {
    adminId: 'testadmin',
    permissions: ['read', 'write', 'delete']
  };
}

module.exports = {
  mockCloudbaseAPI,
  mockJWTToken,
  mockAdminInfo
};
`;

    const mockPath = path.join(this.projectRoot, 'test/mocks.js');
    fs.writeFileSync(mockPath, mockConfig);
    
    console.log('✅ 模拟工具配置已生成');
  }

  // 配置断言库
  async setupAssertionLibrary() {
    const setupFile = `
// Jest测试环境设置
const { mockCloudbaseAPI } = require('./mocks');

// 全局设置
beforeAll(() => {
  // 设置测试超时时间
  jest.setTimeout(30000);
  
  // 启用API模拟
  mockCloudbaseAPI();
});

// 每个测试前的设置
beforeEach(() => {
  // 清理模拟调用记录
  jest.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 清理测试数据
});

// 全局清理
afterAll(() => {
  // 清理资源
});

// 自定义匹配器
expect.extend({
  toBeValidJWT(received) {
    const pass = typeof received === 'string' && received.split('.').length === 3;
    return {
      message: () => \`expected \${received} to be a valid JWT token\`,
      pass
    };
  },
  
  toHaveValidTimestamp(received) {
    const pass = !isNaN(Date.parse(received));
    return {
      message: () => \`expected \${received} to be a valid timestamp\`,
      pass
    };
  }
});
`;

    const setupPath = path.join(this.projectRoot, 'test/setup.js');
    fs.writeFileSync(setupPath, setupFile);
    
    console.log('✅ 断言库配置已生成');
  }

  // 配置测试报告
  async setupTestReporting() {
    const reportConfig = `
// 测试报告配置
module.exports = {
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './test/reports',
      filename: 'test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'V1.0 测试报告'
    }],
    ['jest-junit', {
      outputDirectory: './test/reports',
      outputName: 'junit.xml'
    }]
  ],
  
  coverageReporters: [
    'text',
    'lcov',
    'html',
    'json'
  ],
  
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  }
};
`;

    const reportPath = path.join(this.projectRoot, 'test/jest.config.js');
    fs.writeFileSync(reportPath, reportConfig);
    
    // 创建报告目录
    const reportsDir = path.join(this.projectRoot, 'test/reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    console.log('✅ 测试报告配置已生成');
  }

  // 执行完整的测试环境准备
  async setupAll() {
    console.log('🚀 开始V1.0测试环境完整准备...');
    
    try {
      await this.prepareTestEnvironment();
      await this.prepareTestData();
      await this.configureTestTools();
      
      console.log('🎉 V1.0测试环境准备完成！');
      console.log('📋 下一步：');
      console.log('  1. 执行 test/deploy-test-functions.sh 部署测试云函数');
      console.log('  2. 执行 test/import-test-data.sh 导入测试数据');
      console.log('  3. 运行 npm test 开始测试');
      
    } catch (error) {
      console.error('❌ 测试环境准备失败:', error);
      process.exit(1);
    }
  }
}

// 执行测试环境准备
if (require.main === module) {
  const setup = new TestEnvironmentSetup();
  setup.setupAll();
}

module.exports = TestEnvironmentSetup;
