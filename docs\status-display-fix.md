# 我的点赞和收藏页面状态显示修复

## 问题描述
在"我的点赞"和"我的收藏"页面中，列表中展示的表情包的点赞和收藏图标都显示为激活状态（❤️ 和 ⭐），但实际上有些表情包用户并没有点赞或收藏过。

## 问题原因
1. WXML模板中使用了固定的已激活图标（❤️ 和 ⭐）
2. 没有根据每个表情包的实际状态来显示不同的图标
3. 缺少相应的CSS样式来区分激活和未激活状态

## 修复方案

### 1. 修复我的点赞页面 (pages/my-likes/)

**WXML 修改：**
```xml
<!-- 修复前 -->
<text class="likes">❤️ {{item.likesText}}</text>
<text class="collections">⭐ {{item.collectionsText}}</text>

<!-- 修复后 -->
<text class="likes {{item.isLiked ? 'liked' : ''}}">{{item.isLiked ? '❤️' : '🤍'}} {{item.likesText}}</text>
<text class="collections {{item.isCollected ? 'collected' : ''}}">{{item.isCollected ? '⭐' : '☆'}} {{item.collectionsText}}</text>
```

**CSS 修改：**
```css
.likes.liked {
  color: #ff4757;
}

.collections.collected {
  color: #ffa502;
}
```

### 2. 修复我的收藏页面 (pages/my-collections/)

**WXML 修改：**
```xml
<!-- 修复前 -->
<text class="likes">❤️ {{item.likesText}}</text>
<text class="collections">⭐ {{item.collectionsText}}</text>

<!-- 修复后 -->
<text class="likes {{item.isLiked ? 'liked' : ''}}">{{item.isLiked ? '❤️' : '🤍'}} {{item.likesText}}</text>
<text class="collections {{item.isCollected ? 'collected' : ''}}">{{item.isCollected ? '⭐' : '☆'}} {{item.collectionsText}}</text>
```

**CSS 修改：**
```css
.likes.liked {
  color: #ff4757;
}

.collections.collected {
  color: #ffa502;
}
```

### 3. 优化状态监听

**JavaScript 修改：**
- 在 `onShow()` 中添加状态监听器
- 在 `onHide()` 中移除状态监听器  
- 确保状态变化时页面能及时更新

```javascript
onShow() {
  this.loadLikedEmojis()
  
  this.stateListener = (data) => {
    if (data.type === 'like') {
      this.loadLikedEmojis()
    }
  }
  StateManager.addListener('global', this.stateListener)
},

onHide() {
  if (this.stateListener) {
    StateManager.removeListener('global', this.stateListener)
  }
}
```

## 修复效果

### 修复前：
- 所有表情包都显示 ❤️ 和 ⭐ 图标
- 无法区分用户是否真的点赞或收藏了该表情包

### 修复后：
- 已点赞的表情包显示 ❤️ (红色)，未点赞的显示 🤍 (灰色)
- 已收藏的表情包显示 ⭐ (橙色)，未收藏的显示 ☆ (灰色)
- 状态显示准确反映用户的实际操作

## 测试验证
1. 在首页点赞/收藏一些表情包
2. 进入"我的点赞"页面，检查图标显示是否正确
3. 进入"我的收藏"页面，检查图标显示是否正确
4. 在详情页修改状态，返回列表页检查是否同步更新

## 技术要点
- 使用条件渲染显示不同状态的图标
- 使用CSS类名控制激活状态的样式
- 使用StateManager统一管理状态
- 使用状态监听器确保数据同步