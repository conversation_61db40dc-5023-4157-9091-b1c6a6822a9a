<!--pages/offline-test/offline-test.wxml-->
<view class="offline-test-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="title">离线模式测试</view>
    <view class="subtitle">验证小程序在云函数不可用时的降级功能</view>
  </view>

  <!-- 云开发状态 -->
  <view class="status-section">
    <view class="status-title">云开发状态</view>
    <view class="status-indicator">
      <view class="status-dot {{cloudStatus}}"></view>
      <text class="status-text">
        {{cloudStatus === 'connected' ? '已连接' : cloudStatus === 'offline' ? '离线模式' : '检查中'}}
      </text>
    </view>
  </view>

  <!-- 测试控制 -->
  <view class="test-controls">
    <button 
      class="test-btn primary"
      bindtap="runAllTests"
      disabled="{{testing}}"
    >
      {{testing ? '测试中...' : '开始测试'}}
    </button>
    
    <button 
      class="test-btn secondary"
      bindtap="clearResults"
      disabled="{{testing}}"
    >
      清除结果
    </button>
  </view>

  <!-- 测试结果 -->
  <view class="test-results" wx:if="{{testResults.length > 0}}">
    <view class="results-title">测试结果</view>
    
    <view class="result-item" wx:for="{{testResults}}" wx:key="name">
      <view class="result-header">
        <view class="result-name">{{item.name}}</view>
        <view class="result-status {{item.status}}">
          {{item.status === 'success' ? '✅' : '❌'}}
        </view>
      </view>
      
      <view class="result-details">
        <text class="result-message">{{item.message}}</text>
        <text class="result-duration" wx:if="{{item.duration > 0}}">
          {{item.duration}}ms
        </text>
      </view>
    </view>
  </view>

  <!-- 说明信息 -->
  <view class="info-section">
    <view class="info-title">测试说明</view>
    <view class="info-content">
      <text class="info-text">
        • 此页面用于测试小程序在云函数不可用时的降级功能
      </text>
      <text class="info-text">
        • 如果云开发状态显示"离线模式"，说明云函数不可用
      </text>
      <text class="info-text">
        • 测试将验证各个功能模块是否能正常使用本地数据
      </text>
      <text class="info-text">
        • 所有测试通过表示离线模式工作正常
      </text>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button class="action-btn" bindtap="goToIndex">
      返回首页
    </button>
  </view>
</view>
