<!DOCTYPE html>
<html>
<head>
    <title>调试页面</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 管理后台调试页面</h1>
    <p>当前时间: <span id="timestamp"></span></p>
    
    <div>
        <button onclick="checkEnvironment()">检查环境</button>
        <button onclick="testCloudFunction()">测试云函数</button>
        <button onclick="showMockData()">显示模拟数据</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="results"></div>
    
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            document.getElementById('results').appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function checkEnvironment() {
            addResult('🔍 检查运行环境...', 'warning');
            
            // 检查微信JS-SDK
            if (typeof wx !== 'undefined') {
                addResult('✅ 微信JS-SDK已加载', 'success');
                
                if (wx.cloud) {
                    addResult('✅ wx.cloud对象存在', 'success');
                    
                    // 尝试初始化
                    try {
                        wx.cloud.init({
                            env: 'cloud1-5g6pvnpl88dc0142'
                        });
                        addResult('✅ 云开发初始化成功', 'success');
                    } catch (error) {
                        addResult('❌ 云开发初始化失败: ' + error.message, 'error');
                    }
                } else {
                    addResult('❌ wx.cloud对象不存在', 'error');
                }
            } else {
                addResult('❌ 微信JS-SDK未加载', 'error');
                addResult('💡 这可能是因为不在微信环境中访问', 'warning');
            }
            
            // 检查当前URL
            addResult('🌐 当前URL: ' + window.location.href, 'info');
            
            // 检查User Agent
            addResult('🔧 User Agent: ' + navigator.userAgent, 'info');
        }
        
        async function testCloudFunction() {
            addResult('📡 测试云函数调用...', 'warning');
            
            if (typeof wx === 'undefined' || !wx.cloud) {
                addResult('❌ 云开发环境不可用，无法测试云函数', 'error');
                return;
            }
            
            try {
                addResult('⏳ 正在调用adminAPI云函数...', 'info');
                
                const result = await wx.cloud.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'testDatabase'
                    }
                });
                
                addResult('✅ 云函数调用成功!', 'success');
                addResult('<pre>' + JSON.stringify(result, null, 2) + '</pre>', 'success');
                
            } catch (error) {
                addResult('❌ 云函数调用失败: ' + error.message, 'error');
                addResult('💡 这说明云函数可能未部署或权限不足', 'warning');
                
                // 显示详细错误信息
                if (error.errCode) {
                    addResult('错误代码: ' + error.errCode, 'error');
                }
                if (error.errMsg) {
                    addResult('错误信息: ' + error.errMsg, 'error');
                }
            }
        }
        
        function showMockData() {
            addResult('🎭 显示模拟数据...', 'warning');
            
            const mockData = {
                stats: {
                    users: 156,
                    emojis: 89,
                    categories: 12
                },
                categories: [
                    { id: 1, name: '搞笑', icon: '😂', count: 25 },
                    { id: 2, name: '可爱', icon: '🥰', count: 18 },
                    { id: 3, name: '表情', icon: '😊', count: 32 }
                ],
                emojis: [
                    { id: 1, title: '哈哈哈', category: '搞笑', status: '已发布' },
                    { id: 2, title: '萌萌哒', category: '可爱', status: '已发布' }
                ]
            };
            
            addResult('✅ 模拟数据生成成功:', 'success');
            addResult('<pre>' + JSON.stringify(mockData, null, 2) + '</pre>', 'info');
        }
        
        // 页面加载完成后自动检查环境
        window.addEventListener('load', function() {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
