/* pages/settings/settings.wxss */
.container {
  padding: 0 20rpx 20rpx 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  margin: 20rpx 0 30rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #666;
}

/* 设置区域 */
.settings-section, .storage-section, .about-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.setting-item, .storage-item, .about-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.setting-info, .storage-info {
  flex: 1;
}

.setting-title, .storage-title, .about-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.setting-desc, .storage-desc {
  font-size: 24rpx;
  color: #666;
}

.setting-switch {
  transform: scale(0.8);
}

.storage-arrow, .about-arrow {
  font-size: 24rpx;
  color: #999;
}

.about-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.about-version {
  font-size: 24rpx;
  color: #666;
}

/* 危险操作 */
.storage-item.danger .storage-title {
  color: #ff4757;
}

.storage-item.danger .storage-desc {
  color: #ff6b7a;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx;
  margin-top: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.version-desc {
  font-size: 22rpx;
  color: #ccc;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
