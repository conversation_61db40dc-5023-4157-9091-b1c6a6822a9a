/* pages/test-emoji/test-emoji.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  background: white;
  color: #333;
  border: 2rpx solid #e0e0e0;
}

.test-btn.primary {
  background: #007aff;
  color: white;
  border-color: #007aff;
}

.test-btn.secondary {
  background: #ff3b30;
  color: white;
  border-color: #ff3b30;
}

.emoji-test-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.condition-check {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  border-left: 6rpx solid #007aff;
}

.condition-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-family: monospace;
}

.condition-result {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  font-family: monospace;
}

.emoji-section {
  margin-top: 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.emoji-item {
  background: #fafafa;
  border-radius: 15rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: scale(0.95);
  background: #f0f0f0;
}

.emoji-image-container {
  width: 100%;
  height: 160rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.emoji-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.emoji-info {
  text-align: center;
}

.emoji-title {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.emoji-id {
  display: block;
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
}

.no-data {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-data-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 15rpx;
}

.no-data-hint {
  display: block;
  font-size: 26rpx;
}

.search-interference {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.interference-text {
  display: block;
  font-size: 28rpx;
  color: #856404;
  margin-bottom: 10rpx;
}

.interference-hint {
  display: block;
  font-size: 24rpx;
  color: #856404;
  font-family: monospace;
}

.log-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.log-container {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 15rpx;
  padding: 15rpx;
  border-radius: 8rpx;
  background: white;
  border-left: 6rpx solid #e0e0e0;
}

.log-item.info {
  border-left-color: #007aff;
}

.log-item.success {
  border-left-color: #34c759;
  background: #f0fff4;
}

.log-item.error {
  border-left-color: #ff3b30;
  background: #fff5f5;
}

.log-time {
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
  margin-right: 15rpx;
}

.log-message {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

.no-logs {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 26rpx;
}
