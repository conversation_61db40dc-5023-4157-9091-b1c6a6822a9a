/**
 * 数据库初始化模块
 * 负责小程序启动时的数据库连接和初始化工作
 */

const { CloudConfig } = require('../config/cloud.js')

class DatabaseInit {
  constructor() {
    this.isInitialized = false
    this.initPromise = null
    this.db = null
    this.app = null
  }

  /**
   * 初始化数据库连接
   */
  async init() {
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._performInit()
    return this.initPromise
  }

  /**
   * 执行初始化
   */
  async _performInit() {
    try {
      console.log('🔧 开始初始化数据库连接...')

      // 获取云开发配置
      const cloudConfig = CloudConfig.getCurrentConfig()
      console.log('☁️ 使用云开发环境:', cloudConfig.name, cloudConfig.envId)

      // 初始化云开发
      if (!wx.cloud) {
        throw new Error('微信云开发SDK未加载')
      }

      wx.cloud.init({
        env: cloudConfig.envId,
        traceUser: true
      })

      // 获取数据库引用
      this.db = wx.cloud.database()
      this.app = getApp()

      // 测试数据库连接
      await this._testConnection()

      this.isInitialized = true
      console.log('✅ 数据库初始化成功')

      return {
        success: true,
        db: this.db,
        envId: cloudConfig.envId
      }

    } catch (error) {
      console.error('❌ 数据库初始化失败:', error)
      this.isInitialized = false
      
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 测试数据库连接
   */
  async _testConnection() {
    try {
      console.log('🔍 测试数据库连接...')
      
      // 尝试获取一条数据来测试连接
      const result = await this.db.collection('categories').limit(1).get()
      
      console.log('✅ 数据库连接测试成功')
      return true
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error)
      throw new Error(`数据库连接失败: ${error.message}`)
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase() {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化，请先调用init()方法')
    }
    return this.db
  }

  /**
   * 检查初始化状态
   */
  isReady() {
    return this.isInitialized
  }

  /**
   * 重新初始化
   */
  async reinit() {
    this.isInitialized = false
    this.initPromise = null
    this.db = null
    return await this.init()
  }

  /**
   * 获取集合引用
   */
  collection(name) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化')
    }
    return this.db.collection(name)
  }

  /**
   * 批量初始化集合索引（如果需要）
   */
  async initIndexes() {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化')
    }

    try {
      console.log('🔧 初始化数据库索引...')
      
      // 这里可以添加索引创建逻辑
      // 注意：小程序端通常不需要创建索引，索引在云开发控制台创建
      
      console.log('✅ 数据库索引初始化完成')
    } catch (error) {
      console.warn('⚠️ 数据库索引初始化失败:', error.message)
    }
  }

  /**
   * 完整初始化数据库（app.js中调用的方法）
   */
  async fullInitialize() {
    try {
      console.log('🗄️ 开始数据库完整初始化...')

      // 先执行基础初始化
      const initResult = await this.init()
      if (!initResult.success) {
        return initResult
      }

      // 初始化索引
      await this.initIndexes()

      // 验证数据完整性
      const validationResult = await this._validateDataIntegrity()

      return {
        success: true,
        message: '数据库完整初始化成功',
        validation: validationResult
      }

    } catch (error) {
      console.error('❌ 数据库完整初始化失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 修复数据问题
   */
  async repairData() {
    try {
      console.log('🔧 开始修复数据问题...')

      if (!this.isInitialized) {
        await this.init()
      }

      // 这里可以添加数据修复逻辑
      // 例如：检查数据完整性、修复损坏的数据等

      console.log('✅ 数据修复完成')
      return {
        success: true,
        message: '数据修复成功'
      }

    } catch (error) {
      console.error('❌ 数据修复失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 验证数据完整性
   */
  async _validateDataIntegrity() {
    try {
      console.log('🔍 验证数据完整性...')

      const collections = ['categories', 'emojis', 'banners']
      const results = {}

      for (const collectionName of collections) {
        try {
          const count = await this.db.collection(collectionName).count()
          results[collectionName] = {
            exists: true,
            count: count.total || 0
          }
        } catch (error) {
          results[collectionName] = {
            exists: false,
            error: error.message
          }
        }
      }

      console.log('✅ 数据完整性验证完成:', results)
      return results

    } catch (error) {
      console.warn('⚠️ 数据完整性验证失败:', error)
      return {}
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.isInitialized = false
    this.initPromise = null
    this.db = null
    this.app = null
    console.log('🧹 数据库初始化模块已清理')
  }
}

// 创建单例实例
const databaseInit = new DatabaseInit()

module.exports = {
  DatabaseInit: databaseInit
}
