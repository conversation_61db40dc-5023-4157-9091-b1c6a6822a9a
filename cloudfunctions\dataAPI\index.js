const cloud = require('wx-server-sdk')

// 初始化云开发环境
try {
  cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
  })
  console.log('✅ 云函数初始化成功')
} catch (error) {
  console.error('❌ 云函数初始化失败:', error)
  throw error
}

const db = cloud.database()

exports.main = async (event, context) => {
  // 修复参数类型错误 - 确保data是对象类型
  let { action, data = {} } = event

  // 如果data是数组，转换为对象
  if (Array.isArray(data)) {
    console.warn('⚠️ 参数data是数组类型，转换为对象:', data)
    data = {}
  }

  // 如果data不是对象，设为空对象
  if (typeof data !== 'object' || data === null) {
    console.warn('⚠️ 参数data不是对象类型，设为空对象:', typeof data, data)
    data = {}
  }

  console.log('dataAPI请求:', { action, data, dataType: typeof data })

  try {
    switch (action) {
      case 'getEmojis':
        return await getEmojis(data)
      case 'getCategories':
        return await getCategories(data)
      case 'getEmojiDetail':
        return await getEmojiDetail(data)
      case 'searchEmojis':
        return await searchEmojis(data)
      case 'getCategoryStats':
        return await getCategoryStats(data)
      case 'getDataVersion':
        return await getDataVersion(data)
      case 'updateDataVersion':
        return await updateDataVersion(data)
      // initTestData 已删除 - 防止创建虚拟测试数据
      case 'forceInitDatabase':
        return await forceInitDatabase()
      case 'getBanners':
        return await getBanners()
      case 'getHotEmojis':
        return await getHotEmojis(data)
      case 'ping':
        return { success: true, message: 'pong', timestamp: new Date().toISOString() }
      case 'getHomeData':
        return await getHomeData(data)
      case 'testDB':
        return await testDatabaseConnection()
      case 'getTempFileURL':
        return await getTempFileURL(data)
      default:
        return { success: false, message: '未知操作' }
    }
  } catch (error) {
    console.error('dataAPI错误:', error)
    return { success: false, message: error.message }
  }
}

// 获取表情包列表 - 【最终优化版】
async function getEmojis(params) {
  try {
    const { category = 'all', page = 1, limit = 20 } = params;
    const skip = (page - 1) * limit;

    console.log('🔄 [Optimized] 获取表情包数据，参数:', { category, page, limit });

    // 1. 构建数据库查询条件
    let query = db.collection('emojis').where({ status: 'published' });
    if (category && category !== 'all') {
      query = query.where({ category: category });
    }

    // 2. 使用 Promise.all 并发执行数据查询和总数统计，提升效率
    const [result, countResult] = await Promise.all([
      query.orderBy('createTime', 'desc').skip(skip).limit(limit).get(),
      query.count()
    ]);

    const list = result.data;
    const total = countResult.total;

    console.log(`✅ 查询成功，返回 ${list.length} 条数据，总计 ${total} 条`);

    // 如果当页结果为空，直接返回，无需进行后续处理
    if (list.length === 0) {
      return { success: true, data: [], total, page, limit, hasMore: false };
    }

    // --- 性能优化的核心逻辑开始 ---

    // 3. 从查询结果中提取所有需要处理的图片的 fileID
    const fileIDs = list.map(emoji => emoji.imageUrl)
      .filter(Boolean) // 过滤掉空的imageUrl字段
      .filter(url => url.startsWith('cloud://')); // 只处理云存储的fileID

    console.log(`📸 需要处理的图片数量: ${fileIDs.length}`);

    let urlMap = new Map();

    // 4. 只有当存在有效fileID时才进行批量转换
    if (fileIDs.length > 0) {
      try {
        // 在后端进行一次性的、批量的 fileID 到 HTTPS 链接的转换
        const tempFilesResult = await cloud.getTempFileURL({
          fileList: fileIDs.map(id => ({
            fileID: id,
            maxAge: 7200 // 2小时有效期
          }))
        });

        console.log(`📸 图片处理结果:`, tempFilesResult);

        // 5. 创建一个 fileID -> tempURL 的映射表，便于高效地将处理结果匹配回原数据
        tempFilesResult.fileList.forEach(file => {
          if (file.status === 0) {
            const originalFileID = file.fileID || file.request?.fileID; // 兼容不同版本SDK
            urlMap.set(originalFileID, file.tempFileURL);
          } else {
            console.warn(`⚠️ 图片处理失败:`, file);
          }
        });
      } catch (error) {
        console.error('❌ 批量图片处理失败:', error);
        // 图片处理失败时继续执行，只是不返回图片URL
      }
    }

    // 6. 处理base64图片，上传到云存储，并获取分类名称
    const processedEmojis = await Promise.all(list.map(async (emoji) => {
      let imageUrl = '';

      if (emoji.imageUrl) {
        if (emoji.imageUrl.startsWith('cloud://')) {
          // 云存储URL，使用转换后的临时URL
          imageUrl = urlMap.get(emoji.imageUrl) || emoji.imageUrl;
        } else if (emoji.imageUrl.startsWith('data:image/')) {
          // base64图片，上传到云存储
          console.log(`📸 检测到base64图片，长度: ${emoji.imageUrl.length} 字符，开始上传到云存储`);
          try {
            const uploadResult = await uploadBase64ToCloud(emoji.imageUrl, `emoji_${emoji._id}_${Date.now()}.png`);
            if (uploadResult.success) {
              imageUrl = uploadResult.fileID;
              console.log(`📸 base64图片已上传到云存储: ${uploadResult.fileID}`);

              // 更新数据库中的imageUrl
              await db.collection('emojis').doc(emoji._id).update({
                data: { imageUrl: uploadResult.fileID }
              });
              console.log(`📸 数据库中的imageUrl已更新: ${emoji._id}`);
            } else {
              console.error(`📸 base64图片上传失败: ${uploadResult.error}`);
              imageUrl = '/images/placeholder.png';
            }
          } catch (uploadError) {
            console.error('📸 base64图片上传异常:', uploadError);
            imageUrl = '/images/placeholder.png';
          }
        } else if (emoji.imageUrl.startsWith('http')) {
          // 普通HTTP URL，直接使用
          imageUrl = emoji.imageUrl;
        } else {
          // 其他情况，使用占位符
          imageUrl = '/images/placeholder.png';
        }
      }

      // 获取分类名称
      let categoryName = '未分类';
      if (emoji.category) {
        try {
          const categoryResult = await db.collection('categories').doc(emoji.category).get();
          if (categoryResult.data) {
            categoryName = categoryResult.data.name;
          }
        } catch (error) {
          console.warn('获取分类名称失败:', error);
        }
      }

      return {
        _id: emoji._id,
        title: emoji.title || '',
        imageUrl: imageUrl,
        category: emoji.category || '',
        categoryName: categoryName,
        likes: emoji.likes || 0,
        collections: emoji.collections || 0,
        downloads: emoji.downloads || 0,
        status: emoji.status || 'published', // ✅ 添加状态字段
        createTime: emoji.createTime,
        updateTime: emoji.updateTime,
        description: emoji.description || '',
        tags: emoji.tags || []
      };
    }));

    console.log(`✅ 处理完成，返回 ${processedEmojis.length} 条数据`);

    // --- 性能优化的核心逻辑结束 ---

    const hasMore = skip + list.length < total;

    // 7. 将包含了可直接渲染的 HTTPS 链接的列表返回给前端
    return {
      success: true,
      data: processedEmojis,
      total,
      page,
      limit,
      hasMore
    };

  } catch (error) {
    console.error('❌ 获取表情包数据失败:', error);
    return {
      success: false,
      message: error.message,
      data: []
    };
  }
}

// 获取分类列表 - 优化版本，减少数据库查询
async function getCategories(data = {}) {
  try {
    console.log('开始获取分类数据...')

    // 获取所有分类数据，不使用where条件（因为数据可能被包装在data字段中）
    const result = await db.collection('categories').get()

    console.log('分类数据查询结果:', result)

    // 修复数据结构 - 直接使用category数据，不需要处理data字段
    const fixedCategories = result.data.map(category => {
      return {
        _id: category._id,
        name: (category.name || '').substring(0, 15), // 进一步限制名称长度
        icon: (category.icon || '📁').substring(0, 5), // 限制图标长度
        status: category.status || 'show',
        sort: category.sort || 0
        // 严格移除所有不必要的字段，包括描述、创建时间等
      }
    }).filter(category => {
      // 只返回状态为show的分类，并且名称不为空
      return category.status === 'show' && category.name && category.name.trim()
    }).sort((a, b) => {
      // 按sort字段排序
      return (a.sort || 0) - (b.sort || 0)
    }).slice(0, 20) // 最多返回20个分类，防止数据过多

    console.log('修复后的分类数据:', fixedCategories.length, '条')

    // 优化：使用兼容性查询统计每个分类的表情包数量，兼容多种字段格式
    for (let category of fixedCategories) {
      try {
        // 兼容管理后台数据格式的统计，使用与getCategoryStats相同的逻辑
        let count = 0
        try {
          const allEmojis = await db.collection('emojis').where({
            $or: [
              { categoryId: category._id, status: 'published' },
              { category: category._id, status: 'published' },
              { category: category.name, status: 'published' }
            ]
          }).get()

          // 使用Set去重
          const uniqueEmojiIds = new Set(allEmojis.data.map(emoji => emoji._id))
          count = uniqueEmojiIds.size

        } catch (error) {
          console.warn(`统计分类 ${category.name} 失败:`, error.message)
          // 降级方案：分别统计后求和
          try {
            const counts = await Promise.all([
              db.collection('emojis').where({ categoryId: category._id, status: 'published' }).count(),
              db.collection('emojis').where({ category: category._id, status: 'published' }).count(),
              db.collection('emojis').where({ category: category.name, status: 'published' }).count()
            ])
            count = Math.max(...counts.map(c => c.total || 0))
          } catch (fallbackError) {
            console.error(`分类 ${category.name} 降级统计也失败:`, fallbackError.message)
            count = 0
          }
        }

        category.emojiCount = count
        console.log(`分类 ${category.name} 统计结果: ${category.emojiCount} 个表情包`)

      } catch (error) {
        console.error(`统计分类 ${category.name} 表情包数量失败:`, error)
        category.emojiCount = 0
      }
    }

    console.log('分类数据处理完成:', fixedCategories.map(cat => ({ name: cat.name, count: cat.emojiCount })))
    return { success: true, data: fixedCategories }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return { success: false, message: error.message }
  }
}

// 获取表情包详情
async function getEmojiDetail(params) {
  const { id } = params

  if (!id) {
    return { success: false, message: '表情包ID不能为空' }
  }

  try {
    const result = await db.collection('emojis').doc(id).get()

    if (!result.data) {
      return { success: false, message: '表情包不存在' }
    }

    const emoji = result.data

    // 处理图片URL - 如果是云存储URL，转换为临时URL
    let imageUrl = emoji.imageUrl || '';
    if (imageUrl && imageUrl.startsWith('cloud://')) {
      try {
        console.log(`📸 转换云存储URL: ${imageUrl}`);
        const tempFilesResult = await cloud.getTempFileURL({
          fileList: [{
            fileID: imageUrl,
            maxAge: 7200 // 2小时有效期
          }]
        });

        if (tempFilesResult.fileList && tempFilesResult.fileList.length > 0) {
          const fileInfo = tempFilesResult.fileList[0];
          if (fileInfo.status === 0) {
            imageUrl = fileInfo.tempFileURL;
            console.log(`✅ 云存储URL转换成功: ${imageUrl}`);
          } else {
            console.error(`❌ 云存储URL转换失败: ${fileInfo.errMsg}`);
          }
        }
      } catch (error) {
        console.error('❌ 转换云存储URL异常:', error);
      }
    }

    // 更新emoji对象的imageUrl
    emoji.imageUrl = imageUrl;

    // 获取分类名称
    if (emoji.category) {
      try {
        const categoryResult = await db.collection('categories').doc(emoji.category).get()
        if (categoryResult.data) {
          emoji.categoryName = categoryResult.data.name
        } else {
          emoji.categoryName = '未分类'
        }
      } catch (error) {
        console.warn('获取分类名称失败:', error)
        emoji.categoryName = '未分类'
      }
    } else {
      emoji.categoryName = '未分类'
    }

    console.log(`✅ 表情包详情获取成功: ${emoji.title}, imageUrl: ${emoji.imageUrl}`);
    return { success: true, data: emoji }
  } catch (error) {
    console.error('获取表情包详情失败:', error)
    return { success: false, message: error.message }
  }
}

// 搜索表情包
async function searchEmojis(params) {
  const { keyword, page = 1, limit = 20, sortBy = 'relevance' } = params
  const skip = (page - 1) * limit

  if (!keyword || !keyword.trim()) {
    return { success: false, message: '搜索关键词不能为空' }
  }

  try {
    const searchKeyword = keyword.trim()
    console.log(`🔍 搜索表情包: "${searchKeyword}", page=${page}, limit=${limit}`)

    // 构建搜索条件 - 支持多字段搜索
    const searchConditions = []

    // 标题搜索
    searchConditions.push({
      title: db.RegExp({
        regexp: searchKeyword,
        options: 'i'
      })
    })

    // 标签搜索
    searchConditions.push({
      tags: db.RegExp({
        regexp: searchKeyword,
        options: 'i'
      })
    })

    // 分类名称搜索（需要先获取分类信息）- 兼容管理后台格式
    const categories = await db.collection('categories')
      .where({
        name: db.RegExp({
          regexp: searchKeyword,
          options: 'i'
        })
      })
      .get()

    if (categories.data.length > 0) {
      const categoryIds = categories.data.map(cat => cat._id)
      const categoryNames = categories.data.map(cat => cat.name)

      // 兼容多种分类字段格式
      searchConditions.push({
        categoryId: db.command.in(categoryIds)
      })
      searchConditions.push({
        category: db.command.in(categoryIds)
      })
      searchConditions.push({
        category: db.command.in(categoryNames)
      })
    }

    // 执行搜索
    let query = db.collection('emojis')
      .where({
        status: 'published',
        $or: searchConditions
      })

    // 排序逻辑
    switch (sortBy) {
      case 'likes':
        query = query.orderBy('likes', 'desc')
        break
      case 'downloads':
        query = query.orderBy('downloads', 'desc')
        break
      case 'newest':
        query = query.orderBy('createTime', 'desc')
        break
      case 'relevance':
      default:
        // 相关性排序：优先显示标题匹配的结果
        query = query.orderBy('createTime', 'desc')
        break
    }

    const result = await query
      .skip(skip)
      .limit(limit)
      .get()

    // 获取总数（用于分页）
    const totalResult = await db.collection('emojis')
      .where({
        status: 'published',
        $or: searchConditions
      })
      .count()

    // 增强搜索结果数据 - 添加分类名称
    const enhancedData = await Promise.all(result.data.map(async (item) => {
      // 获取分类名称
      let categoryName = '未分类';
      if (item.category) {
        try {
          const categoryResult = await db.collection('categories').doc(item.category).get();
          if (categoryResult.data) {
            categoryName = categoryResult.data.name;
          }
        } catch (error) {
          console.warn('获取分类名称失败:', error);
        }
      }

      return {
        ...item,
        categoryName: categoryName, // 添加分类名称
        // 高亮搜索关键词
        highlightedTitle: highlightKeyword(item.title, searchKeyword),
        // 匹配度评分（简单实现）
        relevanceScore: calculateRelevanceScore(item, searchKeyword)
      };
    }))

    // 按相关性重新排序（如果选择了相关性排序）
    if (sortBy === 'relevance') {
      enhancedData.sort((a, b) => b.relevanceScore - a.relevanceScore)
    }

    return {
      success: true,
      data: enhancedData,
      total: totalResult.total,
      page,
      limit,
      keyword: searchKeyword,
      hasMore: skip + result.data.length < totalResult.total
    }
  } catch (error) {
    console.error('搜索表情包失败:', error)
    return { success: false, message: error.message }
  }
}

// 高亮搜索关键词
function highlightKeyword(text, keyword) {
  if (!text || !keyword) return text

  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 计算相关性评分
function calculateRelevanceScore(item, keyword) {
  let score = 0
  const lowerKeyword = keyword.toLowerCase()

  // 标题完全匹配
  if (item.title && item.title.toLowerCase() === lowerKeyword) {
    score += 100
  }
  // 标题包含关键词
  else if (item.title && item.title.toLowerCase().includes(lowerKeyword)) {
    score += 50
  }

  // 标签匹配
  if (item.tags && Array.isArray(item.tags)) {
    const tagMatches = item.tags.filter(tag =>
      tag.toLowerCase().includes(lowerKeyword)
    ).length
    score += tagMatches * 20
  }

  // 热度加分
  score += (item.likes || 0) * 0.1
  score += (item.downloads || 0) * 0.05

  return score
}

// 获取分类统计信息
async function getCategoryStats(data = {}) {
  try {
    const categories = await db.collection('categories')
      .where({ status: 'show' })
      .orderBy('sort', 'asc')
      .get()

    // 预定义的渐变色
    const gradients = {
      'funny': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'cute': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'emotion': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'festival': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      'hot': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      '2d': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
    }

    const statsPromises = categories.data.map(async (category) => {
      // 兼容管理后台数据格式的统计
      let count = 0
      try {
        const allEmojis = await db.collection('emojis').where({
          $or: [
            { categoryId: category._id, status: 'published' },
            { category: category._id, status: 'published' },
            { category: category.name, status: 'published' }
          ]
        }).get()

        // 使用Set去重
        const uniqueEmojiIds = new Set(allEmojis.data.map(emoji => emoji._id))
        count = uniqueEmojiIds.size

      } catch (error) {
        console.warn(`统计分类 ${category.name} 失败:`, error.message)
        // 降级方案：分别统计后求和
        try {
          const counts = await Promise.all([
            db.collection('emojis').where({ categoryId: category._id, status: 'published' }).count(),
            db.collection('emojis').where({ category: category._id, status: 'published' }).count(),
            db.collection('emojis').where({ category: category.name, status: 'published' }).count()
          ])
          count = Math.max(...counts.map(c => c.total || 0))
        } catch (fallbackError) {
          console.error(`分类 ${category.name} 降级统计也失败:`, fallbackError.message)
          count = 0
        }
      }

      return {
        id: category._id,
        name: category.name,
        icon: category.icon,
        count: count,
        categoryId: category._id,
        gradient: gradients[category._id] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }
    })

    const stats = await Promise.all(statsPromises)

    // 按表情包数量排序
    stats.sort((a, b) => b.count - a.count)

    return { success: true, data: stats }
  } catch (error) {
    console.error('获取分类统计失败:', error)
    return { success: false, message: error.message }
  }
}

// 初始化分类数据
async function initializeCategories() {
  try {
    console.log('开始初始化分类数据...')

    // 创建基础分类
    const categories = [
      { _id: 'funny', name: '搞笑幽默', icon: '😂', sort: 1 },
      { _id: 'cute', name: '可爱萌宠', icon: '🐱', sort: 2 },
      { _id: 'emotion', name: '情感表达', icon: '❤️', sort: 3 },
      { _id: 'festival', name: '节日庆典', icon: '🎉', sort: 4 },
      { _id: 'hot', name: '网络热梗', icon: '🔥', sort: 5 },
      { _id: '2d', name: '动漫二次元', icon: '🎭', sort: 6 }
    ]

    // 插入分类数据
    for (const category of categories) {
      try {
        await db.collection('categories').add({
          data: {
            ...category,
            description: `${category.name}相关的表情包`,
            status: 'show',
            createTime: new Date(),
            updateTime: new Date()
          }
        })
        console.log(`分类创建成功: ${category.name}`)
      } catch (error) {
        if (error.message.includes('duplicate')) {
          console.log(`分类已存在: ${category.name}`)
        } else {
          console.error(`分类创建失败: ${category.name}`, error)
        }
      }
    }

    console.log('分类数据初始化完成')
    return true
  } catch (error) {
    console.error('初始化分类数据失败:', error)
    return false
  }
}

// 初始化表情包数据
// initializeEmojis 函数已删除 - 防止创建测试数据
// 所有表情包数据请通过管理后台手动添加

// 强制初始化数据库（删除现有数据并重新创建）
async function forceInitDatabase() {
  try {
    console.log('开始强制初始化数据库...')

    const results = {
      categories: false,
      emojis: false,
      banners: false,
      errors: []
    }

    // 先尝试创建集合（如果不存在）
    await ensureCollectionsExist()

    // 1. 清空并重新创建分类数据
    try {
      console.log('清空并重新创建分类数据...')

      // 尝试删除现有数据（如果集合存在）
      try {
        const existingCategories = await db.collection('categories').get()
        for (const category of existingCategories.data) {
          await db.collection('categories').doc(category._id).remove()
        }
        console.log('已清空现有分类数据')
      } catch (error) {
        console.log('分类集合不存在或为空，跳过清空步骤')
      }

      // 创建新的分类数据
      const categories = [
        { name: '搞笑幽默', icon: '😂', sort: 1, description: '搞笑幽默类表情包' },
        { name: '可爱萌宠', icon: '🐱', sort: 2, description: '可爱萌宠类表情包' },
        { name: '情感表达', icon: '❤️', sort: 3, description: '情感表达类表情包' },
        { name: '节日庆典', icon: '🎉', sort: 4, description: '节日庆典类表情包' },
        { name: '网络热梗', icon: '🔥', sort: 5, description: '网络热梗类表情包' },
        { name: '动漫二次元', icon: '🎭', sort: 6, description: '动漫二次元类表情包' }
      ]

      for (const category of categories) {
        try {
          const addResult = await db.collection('categories').add({
            data: {
              ...category,
              status: 'show',
              createTime: new Date(),
              updateTime: new Date()
            }
          })
          console.log(`分类创建成功: ${category.name}, ID: ${addResult._id}`)
        } catch (addError) {
          console.error(`分类创建失败: ${category.name}`, addError)
          throw new Error(`分类创建失败 ${category.name}: ${addError.message}`)
        }
      }

      results.categories = true
    } catch (error) {
      console.error('创建分类失败:', error)
      results.errors.push(`分类创建失败: ${error.message}`)
    }

    // 2. 清空表情包数据（不创建测试数据）
    try {
      console.log('清空表情包数据...')

      // 尝试删除现有数据
      try {
        const existingEmojis = await db.collection('emojis').get()
        for (const emoji of existingEmojis.data) {
          await db.collection('emojis').doc(emoji._id).remove()
        }
        console.log('已清空现有表情包数据')
      } catch (error) {
        console.log('表情包集合不存在或为空，跳过清空步骤')
      }

      // 不创建测试数据 - 所有表情包请通过管理后台手动添加
      console.log('表情包数据清空完成，请通过管理后台手动添加表情包')
      results.emojis = true
    } catch (error) {
      console.error('清空表情包失败:', error)
      results.errors.push(`清空表情包失败: ${error.message}`)
    }

    // 3. 清空横幅数据（不创建测试数据）
    try {
      console.log('清空横幅数据...')

      // 尝试删除现有数据
      try {
        const existingBanners = await db.collection('banners').get()
        for (const banner of existingBanners.data) {
          await db.collection('banners').doc(banner._id).remove()
        }
        console.log('已清空现有横幅数据')
      } catch (error) {
        console.log('横幅集合不存在或为空，跳过清空步骤')
      }

      // 不创建测试数据 - 所有横幅请通过管理后台手动添加
      console.log('横幅数据清空完成，请通过管理后台手动添加横幅')
      results.banners = true
    } catch (error) {
      console.error('清空横幅失败:', error)
      results.errors.push(`清空横幅失败: ${error.message}`)
    }

    const successCount = [results.categories, results.emojis, results.banners].filter(Boolean).length

    let message = `数据库强制初始化完成！成功创建 ${successCount}/3 个数据类型`
    if (results.errors.length > 0) {
      message += `\n错误信息: ${results.errors.join('; ')}`
    }

    return {
      success: successCount > 0,
      message,
      results,
      errors: results.errors,
      timestamp: new Date().toISOString()
    }

  } catch (error) {
    console.error('强制初始化数据库失败:', error)
    return {
      success: false,
      message: '强制初始化失败: ' + error.message,
      error: error.message
    }
  }
}

// 确保数据库集合存在
async function ensureCollectionsExist() {
  const collections = ['categories', 'emojis', 'banners']

  for (const collectionName of collections) {
    try {
      // 尝试向集合添加一个临时文档来创建集合
      const tempDoc = await db.collection(collectionName).add({
        data: {
          _temp: true,
          createTime: new Date()
        }
      })

      // 立即删除临时文档
      await db.collection(collectionName).doc(tempDoc._id).remove()

      console.log(`集合 ${collectionName} 已确保存在`)
    } catch (error) {
      console.log(`集合 ${collectionName} 可能已存在或创建失败:`, error.message)
    }
  }
}

// 获取横幅数据
async function getBanners(data = {}) {
  try {
    console.log('开始获取横幅数据...')

    // 获取所有横幅数据，不使用where条件（因为数据可能被包装在data字段中）
    const result = await db.collection('banners').get()

    console.log('横幅原始查询结果:', result)

    // 修复数据结构 - 处理被包装在data字段中的数据
    const fixedBanners = result.data.map(banner => {
      // 如果数据被包装在data字段中，提取出来
      const bannerData = banner.data || banner

      return {
        _id: banner._id,
        title: (bannerData.title || banner.title || '').substring(0, 30), // 限制标题长度
        imageUrl: bannerData.imageUrl || banner.imageUrl,
        status: bannerData.status || banner.status,
        priority: bannerData.priority || banner.priority || 0
        // 移除不必要的字段以减少数据量
      }
    }).filter(banner => {
      // 只返回状态为show的横幅
      return banner.status === 'show' && banner.title
    }).sort((a, b) => {
      // 按priority字段降序排列
      return (b.priority || 0) - (a.priority || 0)
    }).slice(0, 3) // 最多返回3个横幅，避免数据过大

    console.log('修复后的横幅数据:', fixedBanners.length, '条')

    return {
      success: true,
      data: fixedBanners,
      message: '获取横幅数据成功'
    }
  } catch (error) {
    console.error('获取横幅数据失败:', error)
    return {
      success: false,
      message: '获取横幅数据失败: ' + error.message,
      data: []
    }
  }
}

// initTestData 函数已删除 - 防止创建虚拟测试数据
// 所有数据请通过管理后台手动添加



// ========== 版本管理功能 ==========

/**
 * 获取数据版本
 * @param {Object} params - 参数
 * @returns {Object} 版本信息
 */
async function getDataVersion(params) {
  const { dataType = 'all' } = params

  try {
    console.log('获取数据版本:', dataType)

    // 从系统配置表获取版本信息
    const versionKey = `dataVersion_${dataType}`
    const result = await db.collection('system_config')
      .where({ key: versionKey })
      .get()

    let version = '1.0.0'
    if (result.data.length > 0) {
      version = result.data[0].value || '1.0.0'
    } else {
      // 如果没有版本记录，创建一个
      await db.collection('system_config').add({
        data: {
          key: versionKey,
          value: version,
          description: `${dataType} 数据版本`,
          updateTime: new Date()
        }
      })
    }

    return {
      success: true,
      version,
      dataType,
      timestamp: new Date()
    }
  } catch (error) {
    console.error('获取数据版本失败:', error)
    return {
      success: false,
      message: '获取版本失败: ' + error.message
    }
  }
}

/**
 * 更新数据版本
 * @param {Object} params - 参数
 * @returns {Object} 更新结果
 */
async function updateDataVersion(params) {
  const { dataType = 'all', version } = params

  try {
    console.log('更新数据版本:', { dataType, version })

    const versionKey = `dataVersion_${dataType}`

    // 生成新版本号（如果没有提供）
    const newVersion = version || generateNewVersion()

    // 更新或创建版本记录
    const existingResult = await db.collection('system_config')
      .where({ key: versionKey })
      .get()

    if (existingResult.data.length > 0) {
      // 更新现有记录
      await db.collection('system_config')
        .doc(existingResult.data[0]._id)
        .update({
          data: {
            value: newVersion,
            updateTime: new Date()
          }
        })
    } else {
      // 创建新记录
      await db.collection('system_config').add({
        data: {
          key: versionKey,
          value: newVersion,
          description: `${dataType} 数据版本`,
          updateTime: new Date()
        }
      })
    }

    console.log(`数据版本已更新: ${dataType} -> ${newVersion}`)

    return {
      success: true,
      version: newVersion,
      dataType,
      message: '版本更新成功'
    }
  } catch (error) {
    console.error('更新数据版本失败:', error)
    return {
      success: false,
      message: '更新版本失败: ' + error.message
    }
  }
}

/**
 * 生成新版本号
 * @returns {string} 版本号
 */
// 获取热门表情包
async function getHotEmojis(params) {
  const { limit = 6 } = params

  try {
    console.log('获取热门表情包，限制数量:', limit)

    const result = await db.collection('emojis')
      .where({
        status: 'published'
      })
      .orderBy('downloads', 'desc')
      .limit(limit)
      .get()

    console.log('热门表情包查询结果:', result)

    return {
      success: true,
      data: result.data || [],
      message: '获取热门表情包成功'
    }
  } catch (error) {
    console.error('获取热门表情包失败:', error)
    return {
      success: false,
      data: [],
      message: '获取热门表情包失败: ' + error.message
    }
  }
}

function generateNewVersion() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hour = String(now.getHours()).padStart(2, '0')
  const minute = String(now.getMinutes()).padStart(2, '0')

  return `${year}.${month}${day}.${hour}${minute}`
}

// 测试数据库连接
async function testDatabaseConnection() {
  try {
    console.log('🔍 测试数据库连接...')

    // 测试基本连接
    const testResult = await db.collection('emojis').limit(1).get()
    console.log('✅ 数据库连接正常')

    // 检查集合是否存在数据
    const emojiCount = await db.collection('emojis').count()
    const categoryCount = await db.collection('categories').count()

    console.log(`📊 数据统计: 表情包 ${emojiCount.total} 个，分类 ${categoryCount.total} 个`)

    return {
      success: true,
      message: '数据库连接正常',
      data: {
        emojis: emojiCount.total,
        categories: categoryCount.total,
        sampleEmoji: testResult.data[0] || null
      }
    }
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error)
    return {
      success: false,
      message: '数据库连接失败: ' + error.message,
      error: error
    }
  }
}

// 将base64图片上传到云存储
async function uploadBase64ToCloud(base64Data, cloudPath) {
  try {
    // 解析base64数据
    const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/)
    if (!matches) {
      throw new Error('无效的base64图片格式')
    }

    const imageType = matches[1]
    const base64Content = matches[2]

    // 将base64转换为Buffer
    const buffer = Buffer.from(base64Content, 'base64')

    console.log(`📸 上传图片: ${cloudPath}, 大小: ${buffer.length} bytes, 类型: ${imageType}`)

    // 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: buffer
    })

    console.log(`✅ 图片上传成功: ${uploadResult.fileID}`)

    return {
      success: true,
      fileID: uploadResult.fileID,
      size: buffer.length
    }

  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 获取云存储文件的临时访问URL
async function getTempFileURL(data) {
  try {
    const { fileID } = data

    if (!fileID) {
      return {
        success: false,
        message: '缺少fileID参数'
      }
    }

    console.log(`🔄 获取临时URL: ${fileID}`)

    // 调用云开发API获取临时访问URL
    const result = await cloud.getTempFileURL({
      fileList: [{
        fileID: fileID,
        maxAge: 7200 // 2小时有效期
      }]
    })

    if (result.fileList && result.fileList.length > 0) {
      const fileInfo = result.fileList[0]
      if (fileInfo.status === 0) {
        console.log(`✅ 获取临时URL成功: ${fileInfo.tempFileURL}`)
        return {
          success: true,
          tempFileURL: fileInfo.tempFileURL,
          fileID: fileID
        }
      } else {
        console.error(`❌ 获取临时URL失败: ${fileInfo.errMsg}`)
        return {
          success: false,
          message: fileInfo.errMsg || '获取临时URL失败'
        }
      }
    } else {
      return {
        success: false,
        message: '未返回文件信息'
      }
    }

  } catch (error) {
    console.error('❌ 获取临时URL异常:', error)
    return {
      success: false,
      message: error.message
    }
  }
}
