# 测试用例文档

## 1. 测试概述

### 1.1 测试目标
- 验证所有功能模块正常运行
- 确保数据操作的准确性和一致性
- 验证系统性能满足要求
- 确保系统安全性和稳定性

### 1.2 测试环境
- **测试环境**: Node.js 18+, MongoDB 6.0+, Redis 7.0+
- **测试工具**: Jest, Supertest, MongoDB Memory Server
- **测试数据**: 使用模拟数据和测试数据库

## 2. 功能测试用例

### 2.1 表情包管理模块

#### 2.1.1 创建表情包

**测试用例ID**: TC_EMOJI_001  
**测试标题**: 正常创建表情包  
**优先级**: 高  

**前置条件**:
- 管理员已登录
- 至少存在一个分类

**测试步骤**:
1. 发送POST请求到 `/api/v1/emojis`
2. 请求体包含有效的表情包数据
3. 验证响应状态码和数据

**测试数据**:
```json
{
  "title": "测试表情包",
  "description": "这是一个测试表情包",
  "categoryId": "507f1f77bcf86cd799439012",
  "tags": ["测试", "搞笑"],
  "imageUrl": "https://cdn.example.com/test.jpg",
  "status": "published"
}
```

**预期结果**:
- 响应状态码: 200
- 返回创建的表情包完整信息
- 数据库中成功插入记录
- 操作日志记录创建操作

**实际结果**: [待填写]  
**测试状态**: [通过/失败/阻塞]

---

**测试用例ID**: TC_EMOJI_002  
**测试标题**: 创建表情包 - 缺少必填字段  
**优先级**: 高  

**测试数据**:
```json
{
  "description": "缺少标题的表情包",
  "categoryId": "507f1f77bcf86cd799439012"
}
```

**预期结果**:
- 响应状态码: 400
- 返回参数验证错误信息
- 数据库中不插入记录

---

**测试用例ID**: TC_EMOJI_003  
**测试标题**: 创建表情包 - 无效分类ID  
**优先级**: 中  

**测试数据**:
```json
{
  "title": "测试表情包",
  "categoryId": "invalid_category_id",
  "imageUrl": "https://cdn.example.com/test.jpg"
}
```

**预期结果**:
- 响应状态码: 400
- 返回"分类不存在"错误信息

#### 2.1.2 获取表情包列表

**测试用例ID**: TC_EMOJI_004  
**测试标题**: 获取表情包列表 - 默认参数  
**优先级**: 高  

**测试步骤**:
1. 发送GET请求到 `/api/v1/emojis`
2. 不传递任何查询参数

**预期结果**:
- 响应状态码: 200
- 返回分页数据，默认第1页，每页20条
- 数据按创建时间倒序排列
- 包含分页信息

---

**测试用例ID**: TC_EMOJI_005  
**测试标题**: 获取表情包列表 - 分类筛选  
**优先级**: 高  

**测试步骤**:
1. 发送GET请求到 `/api/v1/emojis?category=funny`

**预期结果**:
- 返回指定分类的表情包
- 所有返回的表情包分类都是"funny"

---

**测试用例ID**: TC_EMOJI_006  
**测试标题**: 获取表情包列表 - 关键词搜索  
**优先级**: 高  

**测试步骤**:
1. 发送GET请求到 `/api/v1/emojis?keyword=搞笑`

**预期结果**:
- 返回标题或标签包含"搞笑"的表情包
- 搜索结果相关性排序

#### 2.1.3 更新表情包

**测试用例ID**: TC_EMOJI_007  
**测试标题**: 更新表情包信息  
**优先级**: 高  

**前置条件**:
- 表情包已存在
- 管理员已登录

**测试步骤**:
1. 发送PUT请求到 `/api/v1/emojis/:id`
2. 请求体包含要更新的字段

**测试数据**:
```json
{
  "title": "更新后的标题",
  "tags": ["更新", "测试"]
}
```

**预期结果**:
- 响应状态码: 200
- 返回更新后的表情包信息
- 数据库中记录已更新
- 更新时间字段已修改

#### 2.1.4 删除表情包

**测试用例ID**: TC_EMOJI_008  
**测试标题**: 删除表情包  
**优先级**: 高  

**测试步骤**:
1. 发送DELETE请求到 `/api/v1/emojis/:id`

**预期结果**:
- 响应状态码: 200
- 数据库中记录状态变为"deleted"
- 相关统计数据更新

#### 2.1.5 批量操作

**测试用例ID**: TC_EMOJI_009  
**测试标题**: 批量删除表情包  
**优先级**: 中  

**测试数据**:
```json
{
  "action": "delete",
  "ids": ["id1", "id2", "id3"]
}
```

**预期结果**:
- 所有指定的表情包状态变为"deleted"
- 返回操作结果统计

### 2.2 分类管理模块

#### 2.2.1 创建分类

**测试用例ID**: TC_CATEGORY_001  
**测试标题**: 创建顶级分类  
**优先级**: 高  

**测试数据**:
```json
{
  "name": "测试分类",
  "slug": "test-category",
  "icon": "🧪",
  "color": "#FF6B6B",
  "description": "这是一个测试分类"
}
```

**预期结果**:
- 响应状态码: 200
- 分类创建成功
- slug字段唯一性验证

---

**测试用例ID**: TC_CATEGORY_002  
**测试标题**: 创建子分类  
**优先级**: 中  

**测试数据**:
```json
{
  "name": "子分类",
  "parentId": "507f1f77bcf86cd799439012",
  "slug": "sub-category"
}
```

**预期结果**:
- 子分类创建成功
- 层级关系正确建立

#### 2.2.2 分类排序

**测试用例ID**: TC_CATEGORY_003  
**测试标题**: 更新分类排序  
**优先级**: 中  

**测试数据**:
```json
{
  "categories": [
    {"id": "cat1", "sortOrder": 1},
    {"id": "cat2", "sortOrder": 2}
  ]
}
```

**预期结果**:
- 所有分类的排序权重更新成功
- 前端显示顺序改变

### 2.3 用户管理模块

#### 2.3.1 用户登录

**测试用例ID**: TC_USER_001  
**测试标题**: 管理员正常登录  
**优先级**: 高  

**测试数据**:
```json
{
  "username": "admin",
  "password": "admin123",
  "captcha": "1234",
  "captchaId": "captcha_xxx"
}
```

**预期结果**:
- 响应状态码: 200
- 返回JWT token
- 登录日志记录

---

**测试用例ID**: TC_USER_002  
**测试标题**: 用户登录 - 错误密码  
**优先级**: 高  

**测试数据**:
```json
{
  "username": "admin",
  "password": "wrong_password"
}
```

**预期结果**:
- 响应状态码: 401
- 返回"用户名或密码错误"

#### 2.3.2 权限验证

**测试用例ID**: TC_USER_003  
**测试标题**: 普通用户访问管理接口  
**优先级**: 高  

**测试步骤**:
1. 使用普通用户token访问 `/api/v1/emojis` POST接口

**预期结果**:
- 响应状态码: 403
- 返回权限不足错误

### 2.4 文件上传模块

#### 2.4.1 图片上传

**测试用例ID**: TC_UPLOAD_001  
**测试标题**: 上传有效图片文件  
**优先级**: 高  

**测试步骤**:
1. 发送POST请求到 `/api/v1/emojis/upload`
2. 上传JPG格式图片文件

**预期结果**:
- 响应状态码: 200
- 返回图片URL和缩略图URL
- 文件成功保存到存储服务

---

**测试用例ID**: TC_UPLOAD_002  
**测试标题**: 上传超大文件  
**优先级**: 中  

**测试步骤**:
1. 上传15MB的图片文件

**预期结果**:
- 响应状态码: 400
- 返回"文件大小超过限制"错误

---

**测试用例ID**: TC_UPLOAD_003  
**测试标题**: 上传非图片文件  
**优先级**: 中  

**测试步骤**:
1. 上传TXT文件

**预期结果**:
- 响应状态码: 400
- 返回"文件格式不支持"错误

## 3. 性能测试用例

### 3.1 接口性能测试

**测试用例ID**: TC_PERF_001  
**测试标题**: 表情包列表接口性能  
**优先级**: 高  

**测试条件**:
- 数据库中有10万条表情包记录
- 并发用户数: 100

**测试步骤**:
1. 使用压力测试工具发送并发请求
2. 监控响应时间和成功率

**性能要求**:
- 平均响应时间 < 500ms
- 95%请求响应时间 < 1s
- 成功率 > 99%

---

**测试用例ID**: TC_PERF_002  
**测试标题**: 文件上传性能  
**优先级**: 中  

**测试条件**:
- 上传5MB图片文件
- 并发上传数: 10

**性能要求**:
- 上传完成时间 < 10s
- 成功率 > 95%

### 3.2 数据库性能测试

**测试用例ID**: TC_PERF_003  
**测试标题**: 复杂查询性能  
**优先级**: 中  

**测试场景**:
- 多条件筛选查询
- 全文搜索查询
- 统计聚合查询

**性能要求**:
- 查询响应时间 < 1s
- 索引命中率 > 90%

## 4. 安全测试用例

### 4.1 认证安全测试

**测试用例ID**: TC_SEC_001  
**测试标题**: JWT Token安全性  
**优先级**: 高  

**测试步骤**:
1. 使用过期token访问接口
2. 使用伪造token访问接口
3. 使用其他用户token访问受限资源

**预期结果**:
- 过期token返回401错误
- 伪造token返回401错误
- 权限验证正确执行

### 4.2 输入验证测试

**测试用例ID**: TC_SEC_002  
**测试标题**: SQL注入防护  
**优先级**: 高  

**测试步骤**:
1. 在搜索参数中注入恶意代码
2. 在表单字段中注入SQL语句

**预期结果**:
- 恶意输入被正确过滤
- 数据库操作安全执行

### 4.3 文件上传安全测试

**测试用例ID**: TC_SEC_003  
**测试标题**: 恶意文件上传防护  
**优先级**: 高  

**测试步骤**:
1. 上传包含恶意代码的图片
2. 上传可执行文件
3. 上传超大文件

**预期结果**:
- 恶意文件被拒绝
- 文件类型验证有效
- 文件大小限制生效

## 5. 兼容性测试用例

### 5.1 浏览器兼容性

**测试用例ID**: TC_COMPAT_001  
**测试标题**: 主流浏览器兼容性  
**优先级**: 中  

**测试环境**:
- Chrome 最新版
- Firefox 最新版
- Safari 最新版
- Edge 最新版

**测试内容**:
- 页面正常显示
- 功能正常使用
- 样式显示正确

### 5.2 移动端兼容性

**测试用例ID**: TC_COMPAT_002  
**测试标题**: 移动端响应式设计  
**优先级**: 中  

**测试设备**:
- iPhone (iOS)
- Android 手机
- iPad (iPadOS)

**测试内容**:
- 页面自适应显示
- 触摸操作正常
- 性能表现良好

## 6. 边界条件测试用例

### 6.1 数据边界测试

**测试用例ID**: TC_BOUNDARY_001  
**测试标题**: 字符串长度边界  
**优先级**: 中  

**测试场景**:
- 表情包标题: 1字符、50字符、51字符
- 描述: 0字符、200字符、201字符
- 标签: 0个、10个、11个

**预期结果**:
- 有效范围内的数据正常处理
- 超出范围的数据返回验证错误

### 6.2 数值边界测试

**测试用例ID**: TC_BOUNDARY_002  
**测试标题**: 分页参数边界  
**优先级**: 中  

**测试场景**:
- page: 0, 1, 999999
- limit: 0, 1, 100, 101

**预期结果**:
- 无效参数使用默认值
- 超出限制的参数被调整

## 7. 异常场景测试用例

### 7.1 网络异常测试

**测试用例ID**: TC_EXCEPTION_001  
**测试标题**: 数据库连接异常  
**优先级**: 高  

**测试步骤**:
1. 模拟数据库连接中断
2. 发送API请求

**预期结果**:
- 返回500错误
- 错误信息记录到日志
- 系统自动重连

### 7.2 存储异常测试

**测试用例ID**: TC_EXCEPTION_002  
**测试标题**: 文件存储服务异常  
**优先级**: 中  

**测试步骤**:
1. 模拟存储服务不可用
2. 尝试上传文件

**预期结果**:
- 返回上传失败错误
- 提供重试机制

### 7.3 并发异常测试

**测试用例ID**: TC_EXCEPTION_003  
**测试标题**: 高并发数据冲突  
**优先级**: 中  

**测试步骤**:
1. 多个用户同时编辑同一表情包
2. 多个用户同时创建相同slug的分类

**预期结果**:
- 数据一致性保持
- 冲突处理机制生效

## 8. 回归测试用例

### 8.1 核心功能回归

**测试用例ID**: TC_REGRESSION_001  
**测试标题**: 核心功能回归测试  
**优先级**: 高  

**测试范围**:
- 表情包CRUD操作
- 用户登录认证
- 文件上传下载
- 数据统计查询

**执行频率**: 每次发布前

### 8.2 接口回归测试

**测试用例ID**: TC_REGRESSION_002  
**测试标题**: API接口回归测试  
**优先级**: 高  

**测试方法**:
- 自动化测试脚本执行
- 接口响应格式验证
- 数据一致性检查

## 9. 测试数据准备

### 9.1 基础测试数据

```javascript
// 测试用户
const testUsers = [
  {
    username: "admin",
    password: "admin123",
    role: "admin",
    nickname: "测试管理员"
  },
  {
    username: "user1",
    password: "user123",
    role: "user",
    nickname: "测试用户1"
  }
]

// 测试分类
const testCategories = [
  {
    name: "搞笑幽默",
    slug: "funny",
    icon: "😂",
    color: "#FF6B6B"
  },
  {
    name: "可爱萌宠",
    slug: "cute",
    icon: "😍",
    color: "#FF69B4"
  }
]

// 测试表情包
const testEmojis = [
  {
    title: "测试表情包1",
    description: "这是第一个测试表情包",
    categoryId: "funny",
    tags: ["测试", "搞笑"],
    imageUrl: "https://cdn.example.com/test1.jpg",
    status: "published"
  }
]
```

### 9.2 性能测试数据

```javascript
// 生成大量测试数据的脚本
function generateTestData(count) {
  const emojis = []
  for (let i = 0; i < count; i++) {
    emojis.push({
      title: `测试表情包${i}`,
      description: `这是第${i}个测试表情包`,
      categoryId: getRandomCategory(),
      tags: getRandomTags(),
      imageUrl: `https://cdn.example.com/test${i}.jpg`,
      status: "published",
      stats: {
        views: Math.floor(Math.random() * 10000),
        likes: Math.floor(Math.random() * 1000),
        downloads: Math.floor(Math.random() * 500)
      }
    })
  }
  return emojis
}
```

## 10. 测试执行计划

### 10.1 测试阶段

| 阶段 | 测试类型 | 执行时间 | 负责人 |
|------|----------|----------|--------|
| 单元测试 | 功能测试 | 开发期间 | 开发工程师 |
| 集成测试 | 接口测试 | 功能完成后 | 测试工程师 |
| 系统测试 | 全功能测试 | 集成完成后 | 测试团队 |
| 验收测试 | 业务验证 | 系统测试后 | 产品经理 |

### 10.2 测试环境

| 环境 | 用途 | 数据 | 访问权限 |
|------|------|------|----------|
| 开发环境 | 开发调试 | 模拟数据 | 开发团队 |
| 测试环境 | 功能测试 | 测试数据 | 测试团队 |
| 预发环境 | 验收测试 | 生产数据副本 | 核心团队 |
| 生产环境 | 正式运行 | 真实数据 | 运维团队 |

### 10.3 缺陷管理

**缺陷等级定义**:
- **P0 - 阻塞**: 系统无法使用，核心功能失效
- **P1 - 严重**: 主要功能异常，影响正常使用
- **P2 - 一般**: 次要功能问题，不影响主流程
- **P3 - 轻微**: 界面问题，体验优化建议

**缺陷处理流程**:
1. 发现缺陷 → 记录缺陷
2. 分析缺陷 → 分配处理
3. 修复缺陷 → 验证修复
4. 关闭缺陷 → 总结分析

---

**文档版本**: v1.0  
**创建时间**: 2024-01-15  
**负责人**: 测试团队