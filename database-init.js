// 数据库结构初始化脚本
// 仅创建必要的数据库集合，不创建测试数据
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 初始化数据库集合结构
async function initDatabase() {
  console.log('🚀 开始初始化数据库结构...');

  try {
    // 1. 创建必要的数据库集合
    await createCollections();

    // 2. 创建数据库索引
    await createIndexes();

    console.log('✅ 数据库结构初始化完成！');
    console.log('💡 请通过Web管理后台添加实际数据');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  }
}

// 创建数据库集合
async function createCollections() {
  console.log('📁 创建数据库集合...');

  const collections = [
    'categories',    // 分类数据
    'emojis',       // 表情包数据
    'banners',      // 横幅数据
    'users',        // 用户数据
    'user_actions', // 用户行为记录
    'sync_logs',    // 同步日志
    'sync_notifications' // 同步通知
  ];

  for (const collectionName of collections) {
    try {
      // 尝试创建集合（通过添加一个临时文档）
      const tempDoc = await db.collection(collectionName).add({
        data: { _temp: true, createTime: new Date() }
      });

      // 立即删除临时文档
      await db.collection(collectionName).doc(tempDoc._id).remove();

      console.log(`✅ 创建集合: ${collectionName}`);
    } catch (error) {
      console.log(`⚠️ 集合已存在: ${collectionName}`);
    }
  }
}

// 创建数据库索引
async function createIndexes() {
  console.log('📊 创建数据库索引...');

  const indexConfigs = [
    {
      collection: 'categories',
      indexes: [
        { keys: { sort: 1 }, name: 'sort_index' },
        { keys: { status: 1 }, name: 'status_index' }
      ]
    },
    {
      collection: 'emojis',
      indexes: [
        { keys: { category: 1 }, name: 'category_index' },
        { keys: { status: 1 }, name: 'status_index' },
        { keys: { createTime: -1 }, name: 'createTime_index' },
        { keys: { tags: 1 }, name: 'tags_index' }
      ]
    },
    {
      collection: 'banners',
      indexes: [
        { keys: { sort: 1 }, name: 'sort_index' },
        { keys: { status: 1 }, name: 'status_index' }
      ]
    },
    {
      collection: 'users',
      indexes: [
        { keys: { openid: 1 }, name: 'openid_index', unique: true },
        { keys: { createTime: -1 }, name: 'createTime_index' }
      ]
    },
    {
      collection: 'user_actions',
      indexes: [
        { keys: { userId: 1 }, name: 'userId_index' },
        { keys: { actionType: 1 }, name: 'actionType_index' },
        { keys: { createTime: -1 }, name: 'createTime_index' }
      ]
    },
    {
      collection: 'sync_logs',
      indexes: [
        { keys: { timestamp: -1 }, name: 'timestamp_index' },
        { keys: { eventType: 1 }, name: 'eventType_index' }
      ]
    },
    {
      collection: 'sync_notifications',
      indexes: [
        { keys: { timestamp: -1 }, name: 'timestamp_index' },
        { keys: { type: 1, status: 1 }, name: 'type_status_index' }
      ]
    }
  ];

  for (const config of indexConfigs) {
    for (const index of config.indexes) {
      try {
        console.log(`📊 为 ${config.collection} 创建索引: ${index.name}`);
        // 注意：在云开发中，索引通常需要在控制台手动创建
        // 这里只是记录需要创建的索引
      } catch (error) {
        console.log(`⚠️ 索引创建失败: ${config.collection}.${index.name}`);
      }
    }
  }

  console.log('💡 请在云开发控制台手动创建以上索引以提升查询性能');
}

// 导出初始化函数
module.exports = { initDatabase };

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}