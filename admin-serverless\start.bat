@echo off
echo.
echo ========================================
echo Admin Panel Server Starting...
echo ========================================
echo.

echo Starting proxy server...
echo.

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js installed
echo.

REM Check proxy-server.js
if not exist "proxy-server.js" (
    echo ERROR: proxy-server.js not found
    echo Please run this script in admin-serverless directory
    echo.
    pause
    exit /b 1
)

echo Proxy server file found
echo.

echo Starting server...
echo.
echo Local URL: http://localhost:9001
echo.
echo Available pages:
echo    Admin Panel: http://localhost:9001/index.html
echo    Test Page: http://localhost:9001/test.html
echo    Sync Verification: http://localhost:9001/sync-verification.html
echo.
echo Press Ctrl+C to stop server
echo.
echo ========================================
echo.

REM Start server
start http://localhost:9001/index.html
node proxy-server.js

echo.
echo Server stopped
pause
