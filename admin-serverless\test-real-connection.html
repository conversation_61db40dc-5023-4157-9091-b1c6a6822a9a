<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实连接测试</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
            border: 1px solid #00ff00;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .log-container {
            background: #000;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .btn {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        .btn:hover {
            background: #00ff00;
            color: #000;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        .timestamp { color: #888; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 真实云开发连接测试</h1>
            <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
            <p style="color: #ffff00;">⚠️ 这将连接你的真实数据库，不是模拟数据</p>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>
            <button class="btn" onclick="testRealConnection()">1. 测试真实连接</button>
            <button class="btn" onclick="testRealDatabase()">2. 测试真实数据库</button>
            <button class="btn" onclick="testRealCloudFunction()">3. 测试真实云函数</button>
            <button class="btn" onclick="testDataSync()">4. 测试数据同步</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="info">📋 准备测试真实云开发连接...</div>
            <div class="warning">⚠️ 注意：这将操作你的真实数据库</div>
        </div>
    </div>

    <!-- 引入云开发SDK - 多重备用方案 -->
    <script>
        // 尝试多个CDN地址
        const cdnUrls = [
            'https://static.cloudbase.net/cloudbase-js-sdk/1.7.0/cloudbase.full.js',
            'https://imgcache.qq.com/qcloud/cloudbase-js-sdk/1.7.0/cloudbase.full.js',
            'https://unpkg.com/@cloudbase/js-sdk@1.7.0/dist/index.umd.js',
            './js/cloudbase-js-sdk.min.js' // 本地备用
        ];

        let currentCdnIndex = 0;

        function loadNextCDN() {
            if (currentCdnIndex >= cdnUrls.length) {
                console.error('❌ 所有CDN都加载失败');
                document.getElementById('logContainer').innerHTML +=
                    '<div class="error">❌ 所有CDN都加载失败，请检查网络连接</div>';
                return;
            }

            const script = document.createElement('script');
            script.src = cdnUrls[currentCdnIndex];

            script.onload = function() {
                console.log(`✅ CDN加载成功: ${cdnUrls[currentCdnIndex]}`);
                document.getElementById('logContainer').innerHTML +=
                    `<div class="success">✅ SDK加载成功: ${cdnUrls[currentCdnIndex]}</div>`;
            };

            script.onerror = function() {
                console.warn(`⚠️ CDN加载失败: ${cdnUrls[currentCdnIndex]}`);
                currentCdnIndex++;
                loadNextCDN();
            };

            document.head.appendChild(script);
        }

        // 开始加载
        loadNextCDN();
    </script>

    <script>
        const envId = 'cloud1-5g6pvnpl88dc0142';
        let app = null;

        // 日志函数
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = document.createElement('div');
            logLine.className = type;
            logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            container.appendChild(logLine);
            container.scrollTop = container.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = 
                '<div class="info">📋 日志已清空</div>';
        }

        // 1. 测试真实连接
        async function testRealConnection() {
            addLog('🔗 开始测试真实云开发连接...', 'info');
            
            try {
                // 检查SDK
                if (typeof cloudbase === 'undefined') {
                    throw new Error('云开发SDK未加载');
                }
                addLog('✅ 云开发SDK加载成功', 'success');

                // 初始化
                app = cloudbase.init({ env: envId });
                addLog('✅ 云开发应用初始化成功', 'success');

                // 身份验证
                const auth = app.auth();
                const user = await auth.signInAnonymously();
                addLog(`✅ 身份验证成功，用户ID: ${user.uid}`, 'success');

                addLog('🎉 真实云开发连接测试通过！', 'success');
                
            } catch (error) {
                addLog(`❌ 连接测试失败: ${error.message}`, 'error');
                console.error('连接测试失败:', error);
            }
        }

        // 2. 测试真实数据库
        async function testRealDatabase() {
            addLog('📊 开始测试真实数据库连接...', 'info');
            
            try {
                if (!app) {
                    throw new Error('请先运行连接测试');
                }

                const db = app.database();
                
                // 查询真实的categories集合
                addLog('📖 查询真实的categories集合...', 'info');
                const categoriesResult = await db.collection('categories').get();
                addLog(`✅ categories查询成功，返回 ${categoriesResult.data.length} 条真实数据`, 'success');
                
                if (categoriesResult.data.length > 0) {
                    addLog(`📋 示例数据: ${JSON.stringify(categoriesResult.data[0], null, 2)}`, 'info');
                }

                // 查询真实的emojis集合
                addLog('📖 查询真实的emojis集合...', 'info');
                const emojisResult = await db.collection('emojis').get();
                addLog(`✅ emojis查询成功，返回 ${emojisResult.data.length} 条真实数据`, 'success');

                addLog('🎉 真实数据库测试通过！', 'success');
                
            } catch (error) {
                addLog(`❌ 数据库测试失败: ${error.message}`, 'error');
                console.error('数据库测试失败:', error);
            }
        }

        // 3. 测试真实云函数
        async function testRealCloudFunction() {
            addLog('☁️ 开始测试真实云函数调用...', 'info');
            
            try {
                if (!app) {
                    throw new Error('请先运行连接测试');
                }

                // 调用真实的adminAPI云函数
                addLog('📡 调用真实的adminAPI云函数...', 'info');
                const result = await app.callFunction({
                    name: 'adminAPI',
                    data: { action: 'getStats' }
                });

                addLog('✅ 真实云函数调用成功！', 'success');
                addLog(`📊 返回的真实统计数据: ${JSON.stringify(result.result, null, 2)}`, 'info');

                if (result.result && result.result.success) {
                    addLog('🎉 云函数返回真实数据，与小程序完全同步！', 'success');
                } else {
                    addLog('⚠️ 云函数调用成功，但返回格式需要检查', 'warning');
                }
                
            } catch (error) {
                addLog(`❌ 云函数测试失败: ${error.message}`, 'error');
                console.error('云函数测试失败:', error);
            }
        }

        // 4. 测试数据同步
        async function testDataSync() {
            addLog('🔄 开始测试数据同步功能...', 'info');
            
            try {
                if (!app) {
                    throw new Error('请先运行连接测试');
                }

                // 添加一条测试数据
                const testData = {
                    name: '同步测试分类',
                    icon: '🧪',
                    status: 'show',
                    sort: 999,
                    description: '这是一条测试数据，用于验证管理后台与小程序的数据同步',
                    createTime: new Date(),
                    testFlag: true
                };

                addLog('📝 向真实数据库添加测试数据...', 'info');
                const db = app.database();
                const addResult = await db.collection('categories').add({
                    data: testData
                });

                addLog(`✅ 测试数据添加成功，ID: ${addResult.id}`, 'success');
                addLog('🎉 数据已写入真实数据库，小程序可以立即看到！', 'success');

                // 验证数据是否真的写入了
                addLog('🔍 验证数据是否真实写入...', 'info');
                const verifyResult = await db.collection('categories')
                    .where({ testFlag: true })
                    .get();

                if (verifyResult.data.length > 0) {
                    addLog(`✅ 验证成功！找到 ${verifyResult.data.length} 条测试数据`, 'success');
                    addLog('🎯 数据同步测试完全通过！管理后台与小程序数据完全同步！', 'success');
                } else {
                    addLog('⚠️ 验证失败，数据可能没有正确写入', 'warning');
                }
                
            } catch (error) {
                addLog(`❌ 数据同步测试失败: ${error.message}`, 'error');
                console.error('数据同步测试失败:', error);
            }
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('🌐 页面加载完成', 'info');
            addLog('💡 请按顺序点击测试按钮', 'info');
            addLog('⚠️ 注意：这将操作你的真实云开发数据库', 'warning');
        });
    </script>
</body>
</html>
