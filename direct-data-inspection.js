// 直接检查数据库中的表情包数据
const { chromium } = require('playwright');

async function directDataInspection() {
    console.log('🔍 直接检查数据库中的表情包数据...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 直接查询数据库中的表情包数据');
        
        // 直接在浏览器中执行数据库查询
        const databaseData = await page.evaluate(async () => {
            try {
                // 直接调用CloudAPI查询数据
                const result = await CloudAPI.database.get('emojis');
                
                if (result.success && result.data) {
                    return {
                        success: true,
                        count: result.data.length,
                        data: result.data.map(emoji => ({
                            _id: emoji._id,
                            title: emoji.title,
                            status: emoji.status,
                            imageUrl: emoji.imageUrl,
                            imageUrlType: typeof emoji.imageUrl,
                            imageUrlLength: emoji.imageUrl ? emoji.imageUrl.length : 0,
                            category: emoji.category,
                            createTime: emoji.createTime,
                            hasImageUrl: !!emoji.imageUrl,
                            imageUrlPreview: emoji.imageUrl ? emoji.imageUrl.substring(0, 100) : 'N/A'
                        }))
                    };
                } else {
                    return {
                        success: false,
                        error: result.error || '查询失败'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据库查询结果:');
        console.log(`查询成功: ${databaseData.success}`);
        
        if (databaseData.success) {
            console.log(`表情包总数: ${databaseData.count}`);
            
            console.log('\n📋 表情包数据详情:');
            databaseData.data.forEach((emoji, index) => {
                console.log(`\n表情包 ${index + 1}:`);
                console.log(`  ID: ${emoji._id}`);
                console.log(`  标题: ${emoji.title}`);
                console.log(`  状态: ${emoji.status} (类型: ${typeof emoji.status})`);
                console.log(`  分类: ${emoji.category}`);
                console.log(`  创建时间: ${emoji.createTime}`);
                console.log(`  有图片URL: ${emoji.hasImageUrl}`);
                console.log(`  图片URL类型: ${emoji.imageUrlType}`);
                console.log(`  图片URL长度: ${emoji.imageUrlLength}`);
                console.log(`  图片URL预览: ${emoji.imageUrlPreview}`);
                
                // 分析问题
                if (emoji.status !== 'published') {
                    console.log(`  🔴 状态问题: 状态不是'published'，而是'${emoji.status}'`);
                }
                
                if (!emoji.hasImageUrl) {
                    console.log(`  🔴 图片问题: imageUrl字段为空`);
                } else if (emoji.imageUrl === '/images/placeholder.png') {
                    console.log(`  🔴 图片问题: 使用占位符图片`);
                } else if (emoji.imageUrl.startsWith('cloud://')) {
                    console.log(`  ✅ 图片正常: 使用云存储`);
                } else if (emoji.imageUrl.startsWith('data:image/')) {
                    console.log(`  ⚠️ 图片降级: 使用base64存储`);
                } else {
                    console.log(`  ❓ 图片未知: 未知的URL格式`);
                }
            });
            
            // 统计分析
            const statusStats = {};
            const imageStats = { hasImage: 0, noImage: 0, placeholder: 0, cloud: 0, base64: 0 };
            
            databaseData.data.forEach(emoji => {
                // 状态统计
                statusStats[emoji.status] = (statusStats[emoji.status] || 0) + 1;
                
                // 图片统计
                if (!emoji.hasImageUrl) {
                    imageStats.noImage++;
                } else if (emoji.imageUrl === '/images/placeholder.png') {
                    imageStats.placeholder++;
                } else if (emoji.imageUrl.startsWith('cloud://')) {
                    imageStats.cloud++;
                } else if (emoji.imageUrl.startsWith('data:image/')) {
                    imageStats.base64++;
                } else {
                    imageStats.hasImage++;
                }
            });
            
            console.log('\n📊 统计分析:');
            console.log('状态分布:', statusStats);
            console.log('图片分布:', imageStats);
            
            // 问题总结
            console.log('\n🎯 问题总结:');
            const publishedCount = statusStats['published'] || 0;
            const draftCount = statusStats['draft'] || 0;
            const otherStatusCount = databaseData.count - publishedCount - draftCount;
            
            if (otherStatusCount > 0) {
                console.log(`🔴 状态问题: 有${otherStatusCount}个表情包的状态不是'published'或'draft'`);
            }
            
            if (imageStats.noImage > 0) {
                console.log(`🔴 图片问题: 有${imageStats.noImage}个表情包没有图片URL`);
            }
            
            if (imageStats.placeholder > 0) {
                console.log(`🔴 图片问题: 有${imageStats.placeholder}个表情包使用占位符图片`);
            }
            
            if (publishedCount > 0 && imageStats.cloud > 0) {
                console.log(`✅ 正常数据: 有${publishedCount}个已发布表情包，${imageStats.cloud}个使用云存储图片`);
            }
            
        } else {
            console.log(`查询失败: ${databaseData.error}`);
        }
        
        console.log('\n📍 检查表格渲染');
        
        // 进入表情包管理页面
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(5000);
        
        // 检查表格渲染结果
        const renderResult = await page.evaluate(() => {
            const container = document.querySelector('#emoji-content');
            const table = document.querySelector('#emoji-content table');
            const rows = document.querySelectorAll('#emoji-content tbody tr');
            
            return {
                containerExists: !!container,
                tableExists: !!table,
                rowCount: rows.length,
                containerContent: container ? container.innerHTML.substring(0, 500) : 'N/A',
                isEmptyState: container ? container.innerHTML.includes('暂无表情包数据') : false
            };
        });
        
        console.log('📊 表格渲染结果:');
        console.log(`容器存在: ${renderResult.containerExists}`);
        console.log(`表格存在: ${renderResult.tableExists}`);
        console.log(`行数: ${renderResult.rowCount}`);
        console.log(`是空状态: ${renderResult.isEmptyState}`);
        console.log(`容器内容预览: ${renderResult.containerContent}`);
        
        // 最终结论
        console.log('\n🎯 最终结论:');
        if (databaseData.success && databaseData.count > 0) {
            if (renderResult.rowCount === 0) {
                console.log('🔴 核心问题: 数据库有数据，但表格渲染失败');
                console.log('   可能原因: renderEmojiTable函数有bug，或者数据格式问题');
            } else if (renderResult.rowCount !== databaseData.count) {
                console.log(`🔴 部分问题: 数据库有${databaseData.count}条数据，但只渲染了${renderResult.rowCount}行`);
                console.log('   可能原因: 部分数据格式异常，被过滤掉了');
            } else {
                console.log('✅ 渲染正常: 数据库和表格数据一致');
            }
        }
        
        // 截图
        await page.screenshot({ path: 'direct-data-inspection.png', fullPage: true });
        console.log('\n📸 检查截图已保存: direct-data-inspection.png');
        
        console.log('\n⏸️ 检查完成，浏览器将保持打开30秒供查看...');
        await page.waitForTimeout(30000);
        
        return databaseData;
        
    } catch (error) {
        console.error('❌ 检查过程中出错:', error);
        await page.screenshot({ path: 'data-inspection-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行检查
directDataInspection().then(result => {
    console.log('\n🎯 数据检查最终结果:', result.success ? '成功' : '失败');
}).catch(console.error);
