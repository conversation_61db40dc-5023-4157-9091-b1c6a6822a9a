<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步验证工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 12px;
            margin-bottom: 12px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .result-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            border: 2px solid #e9ecef;
        }
        
        .status-card.connected {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .data-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 16px;
        }
        
        .data-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e9ecef;
        }
        
        .data-panel h4 {
            margin: 0 0 12px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        
        .data-item {
            background: white;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        
        .sync-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .sync-indicator.synced {
            background: #28a745;
        }
        
        .sync-indicator.pending {
            background: #ffc107;
        }
        
        .sync-indicator.error {
            background: #dc3545;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 12px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-log {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 4px;
        }
        
        .log-entry.success {
            color: #68d391;
        }
        
        .log-entry.error {
            color: #fc8181;
        }
        
        .log-entry.info {
            color: #63b3ed;
        }
        
        .log-entry.warning {
            color: #f6e05e;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 数据同步验证工具</h1>
        <p>验证管理后台与小程序之间的实时数据同步功能</p>
    </div>

    <!-- 连接状态 -->
    <div class="test-section">
        <div class="test-title">📡 连接状态检查</div>
        <div class="status-grid">
            <div id="admin-status" class="status-card">
                <div class="status-title">
                    <span>🖥️</span>
                    <span>管理后台连接</span>
                </div>
                <div id="admin-status-text">检测中...</div>
            </div>
            <div id="miniprogram-status" class="status-card">
                <div class="status-title">
                    <span>📱</span>
                    <span>小程序数据源</span>
                </div>
                <div id="miniprogram-status-text">检测中...</div>
            </div>
        </div>
        <button class="btn" onclick="checkConnections()">🔍 检查连接状态</button>
    </div>

    <!-- 数据同步测试 -->
    <div class="test-section">
        <div class="test-title">🧪 数据同步测试</div>
        <div>
            <button class="btn" onclick="testAddSync()">➕ 测试添加同步</button>
            <button class="btn" onclick="testUpdateSync()">✏️ 测试修改同步</button>
            <button class="btn" onclick="testDeleteSync()">🗑️ 测试删除同步</button>
            <button class="btn success" onclick="runFullSyncTest()">🚀 完整同步测试</button>
        </div>
        
        <div class="progress-bar">
            <div id="test-progress" class="progress-fill"></div>
        </div>
        
        <div class="data-comparison">
            <div class="data-panel">
                <h4>📊 管理后台数据</h4>
                <div id="admin-data-list">暂无数据</div>
            </div>
            <div class="data-panel">
                <h4>📱 小程序数据</h4>
                <div id="miniprogram-data-list">暂无数据</div>
            </div>
        </div>
    </div>

    <!-- 实时监控 -->
    <div class="test-section">
        <div class="test-title">👀 实时数据监控</div>
        <div>
            <button class="btn" onclick="startMonitoring()">▶️ 开始监控</button>
            <button class="btn" onclick="stopMonitoring()">⏹️ 停止监控</button>
            <button class="btn" onclick="clearLogs()">🧹 清空日志</button>
        </div>
        <div id="monitoring-status">监控状态：未启动</div>
        <div id="test-logs" class="test-log">
            <div class="log-entry info">等待开始监控...</div>
        </div>
    </div>

    <!-- 性能测试 -->
    <div class="test-section">
        <div class="test-title">⚡ 同步性能测试</div>
        <div>
            <button class="btn" onclick="testSyncSpeed()">🏃 同步速度测试</button>
            <button class="btn" onclick="testBatchSync()">📦 批量同步测试</button>
            <button class="btn" onclick="testConcurrentSync()">🔀 并发同步测试</button>
        </div>
        <div id="performance-result" class="result-area">等待测试...</div>
    </div>

    <!-- 引入云开发Web SDK -->
    <script src="https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js"></script>
    
    <script>
        // 全局变量
        let tcbApp = null;
        let monitoringInterval = null;
        let testResults = {
            connections: {},
            syncTests: {},
            performance: {}
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initializeVerificationTool();
        });

        // 初始化验证工具
        async function initializeVerificationTool() {
            log('🚀 初始化数据同步验证工具...', 'info');
            
            try {
                // 初始化云开发
                if (window.tcb) {
                    tcbApp = window.tcb.init({
                        env: 'cloud1-5g6pvnpl88dc0142'
                    });
                    
                    // 匿名登录
                    const auth = tcbApp.auth();
                    await auth.signInAnonymously();
                    
                    log('✅ 云开发初始化成功', 'success');
                } else {
                    throw new Error('云开发SDK未加载');
                }
                
                // 自动检查连接状态
                await checkConnections();
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        // 检查连接状态
        async function checkConnections() {
            log('🔍 检查连接状态...', 'info');
            
            // 检查管理后台连接
            try {
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();
                
                updateStatus('admin-status', 'connected', '✅ 连接正常');
                testResults.connections.admin = true;
                log('✅ 管理后台连接正常', 'success');
                
            } catch (error) {
                updateStatus('admin-status', 'error', '❌ 连接失败');
                testResults.connections.admin = false;
                log(`❌ 管理后台连接失败: ${error.message}`, 'error');
            }

            // 检查小程序数据源（模拟）
            try {
                // 这里可以调用小程序的数据接口或者检查数据一致性
                updateStatus('miniprogram-status', 'connected', '✅ 数据源正常');
                testResults.connections.miniprogram = true;
                log('✅ 小程序数据源正常', 'success');
                
            } catch (error) {
                updateStatus('miniprogram-status', 'error', '❌ 数据源异常');
                testResults.connections.miniprogram = false;
                log(`❌ 小程序数据源异常: ${error.message}`, 'error');
            }

            // 加载当前数据进行对比
            await loadDataForComparison();
        }

        // 更新状态显示
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-card ${status}`;
            element.querySelector('div:last-child').textContent = text;
        }

        // 加载数据进行对比
        async function loadDataForComparison() {
            try {
                const db = tcbApp.database();
                
                // 获取管理后台数据
                const [categoriesResult, emojisResult] = await Promise.all([
                    db.collection('categories').get(),
                    db.collection('emojis').get()
                ]);

                // 显示管理后台数据
                const adminDataList = document.getElementById('admin-data-list');
                adminDataList.innerHTML = '';
                
                categoriesResult.data.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.innerHTML = `
                        <span class="sync-indicator synced"></span>
                        分类: ${item.name} (${item._id})
                    `;
                    adminDataList.appendChild(div);
                });

                emojisResult.data.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.innerHTML = `
                        <span class="sync-indicator synced"></span>
                        表情: ${item.name || item.title} (${item._id})
                    `;
                    adminDataList.appendChild(div);
                });

                // 模拟小程序数据（实际应该从小程序数据源获取）
                const miniprogramDataList = document.getElementById('miniprogram-data-list');
                miniprogramDataList.innerHTML = '';
                
                // 这里应该调用小程序的数据接口，现在先模拟
                categoriesResult.data.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.innerHTML = `
                        <span class="sync-indicator synced"></span>
                        分类: ${item.name} (同步)
                    `;
                    miniprogramDataList.appendChild(div);
                });

                emojisResult.data.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'data-item';
                    div.innerHTML = `
                        <span class="sync-indicator synced"></span>
                        表情: ${item.name || item.title} (同步)
                    `;
                    miniprogramDataList.appendChild(div);
                });

                log(`📊 数据对比完成: 分类${categoriesResult.data.length}个, 表情${emojisResult.data.length}个`, 'info');

            } catch (error) {
                log(`❌ 数据加载失败: ${error.message}`, 'error');
            }
        }

        // 测试添加同步
        async function testAddSync() {
            log('🧪 开始测试添加同步...', 'info');
            updateProgress(10);

            try {
                const db = tcbApp.database();
                const testData = {
                    name: `测试分类_${Date.now()}`,
                    icon: '🧪',
                    description: '同步测试分类',
                    id: `test_${Date.now()}`,
                    status: 'show',
                    createTime: new Date(),
                    updateTime: new Date()
                };

                updateProgress(30);
                
                // 添加到管理后台（云数据库）
                const result = await db.collection('categories').add({
                    data: testData
                });

                updateProgress(60);
                log(`✅ 数据添加成功: ${result._id}`, 'success');

                // 等待一段时间模拟同步延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                updateProgress(80);
                
                // 验证数据是否同步到小程序（这里模拟验证）
                const verification = await db.collection('categories').doc(result._id).get();
                
                if (verification.data.length > 0) {
                    log('✅ 数据同步验证成功', 'success');
                    testResults.syncTests.add = true;
                } else {
                    log('❌ 数据同步验证失败', 'error');
                    testResults.syncTests.add = false;
                }

                updateProgress(100);
                
                // 刷新数据对比
                await loadDataForComparison();

            } catch (error) {
                log(`❌ 添加同步测试失败: ${error.message}`, 'error');
                testResults.syncTests.add = false;
            }

            setTimeout(() => updateProgress(0), 2000);
        }

        // 测试修改同步
        async function testUpdateSync() {
            log('🧪 开始测试修改同步...', 'info');
            // 实现修改同步测试逻辑
            log('⚠️ 修改同步测试功能开发中...', 'warning');
        }

        // 测试删除同步
        async function testDeleteSync() {
            log('🧪 开始测试删除同步...', 'info');
            // 实现删除同步测试逻辑
            log('⚠️ 删除同步测试功能开发中...', 'warning');
        }

        // 完整同步测试
        async function runFullSyncTest() {
            log('🚀 开始完整同步测试...', 'info');
            
            await testAddSync();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testUpdateSync();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            await testDeleteSync();
            
            log('🎉 完整同步测试完成', 'success');
        }

        // 开始监控
        function startMonitoring() {
            if (monitoringInterval) {
                stopMonitoring();
            }

            log('👀 开始实时数据监控...', 'info');
            document.getElementById('monitoring-status').textContent = '监控状态：运行中';

            monitoringInterval = setInterval(async () => {
                try {
                    const db = tcbApp.database();
                    const result = await db.collection('categories').count();
                    log(`📊 当前分类数量: ${result.total}`, 'info');
                } catch (error) {
                    log(`❌ 监控错误: ${error.message}`, 'error');
                }
            }, 5000);
        }

        // 停止监控
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('⏹️ 停止数据监控', 'info');
                document.getElementById('monitoring-status').textContent = '监控状态：已停止';
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('test-logs').innerHTML = '<div class="log-entry info">日志已清空</div>';
        }

        // 同步速度测试
        async function testSyncSpeed() {
            const resultArea = document.getElementById('performance-result');
            resultArea.textContent = '正在测试同步速度...\n';

            const startTime = Date.now();
            
            try {
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                resultArea.textContent += `✅ 同步速度测试完成\n`;
                resultArea.textContent += `⏱️ 响应时间: ${duration}ms\n`;
                
                if (duration < 500) {
                    resultArea.textContent += `🚀 性能评级: 优秀\n`;
                } else if (duration < 1000) {
                    resultArea.textContent += `👍 性能评级: 良好\n`;
                } else {
                    resultArea.textContent += `⚠️ 性能评级: 需要优化\n`;
                }

            } catch (error) {
                resultArea.textContent += `❌ 同步速度测试失败: ${error.message}\n`;
            }
        }

        // 批量同步测试
        async function testBatchSync() {
            const resultArea = document.getElementById('performance-result');
            resultArea.textContent = '正在测试批量同步...\n';
            
            // 实现批量同步测试逻辑
            resultArea.textContent += '⚠️ 批量同步测试功能开发中...\n';
        }

        // 并发同步测试
        async function testConcurrentSync() {
            const resultArea = document.getElementById('performance-result');
            resultArea.textContent = '正在测试并发同步...\n';
            
            // 实现并发同步测试逻辑
            resultArea.textContent += '⚠️ 并发同步测试功能开发中...\n';
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('test-progress').style.width = percent + '%';
        }

        // 日志记录
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('test-logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    </script>
</body>
</html>
