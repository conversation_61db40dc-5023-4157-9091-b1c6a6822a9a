const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 9001;

// 云开发环境配置 - 使用正确的云函数名称
const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
const ADMIN_API_URL = `https://${ENV_ID}.service.tcloudbase.com/adminAPI`;
const DATA_API_URL = `https://${ENV_ID}.service.tcloudbase.com/dataAPI`;
const WEB_ADMIN_API_URL = `https://${ENV_ID}.service.tcloudbase.com/webAdminAPI`;  // 正确的云函数名称

// 中间件
app.use(cors()); // 允许跨域
app.use(express.json()); // 解析JSON
app.use(express.static(__dirname)); // 静态文件服务

console.log('🚀 启动云开发代理服务器...');
console.log(`📡 AdminAPI URL: ${ADMIN_API_URL}`);
console.log(`📡 DataAPI URL: ${DATA_API_URL}`);
console.log(`📡 WebAdminAPI URL: ${WEB_ADMIN_API_URL}`);

// 代理adminAPI云函数
app.post('/api/adminAPI', async (req, res) => {
    console.log('📡 代理adminAPI请求:', req.body);
    
    try {
        const response = await fetch(ADMIN_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        console.log('✅ adminAPI响应:', data);
        
        res.json(data);
    } catch (error) {
        console.error('❌ adminAPI代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 代理webAdminAPI云函数 - 专门用于Web端管理后台
app.post('/api/webAdminAPI', async (req, res) => {
    console.log('📡 代理webAdminAPI请求:', req.body);

    try {
        const response = await fetch(WEB_ADMIN_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        console.log('✅ webAdminAPI响应:', data);

        res.json(data);
    } catch (error) {
        console.error('❌ webAdminAPI代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 通用云函数代理路由 - 管理后台使用的路径
app.post('/api/cloud-function', async (req, res) => {
    console.log('📡 代理云函数请求:', req.body);

    const { functionName, data } = req.body;

    try {
        let targetUrl;

        // 根据函数名选择对应的URL
        switch (functionName) {
            case 'webAdminAPI':
                targetUrl = WEB_ADMIN_API_URL;
                break;
            case 'adminAPI':
                targetUrl = ADMIN_API_URL;
                break;
            case 'dataAPI':
                targetUrl = DATA_API_URL;
                break;
            default:
                throw new Error(`不支持的云函数: ${functionName}`);
        }

        const response = await fetch(targetUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        console.log(`✅ ${functionName}响应:`, result);

        res.json(result);
    } catch (error) {
        console.error(`❌ ${functionName}代理失败:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 代理webAdminAPI云函数 - 专门用于数据同步
app.post('/api/syncData', async (req, res) => {
    console.log('📡 代理webAdminAPI同步请求:', req.body);

    try {
        // 将请求转换为webAdminAPI格式
        const webAdminAPIRequest = {
            action: 'syncData',
            data: req.body,
            adminPassword: 'admin123456'
        };

        const response = await fetch(WEB_ADMIN_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(webAdminAPIRequest)
        });

        const data = await response.json();
        console.log('✅ webAdminAPI同步响应:', data);

        res.json(data);
    } catch (error) {
        console.error('❌ webAdminAPI同步代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 代理dataAPI云函数
app.post('/api/dataAPI', async (req, res) => {
    console.log('📡 代理dataAPI请求:', req.body);
    
    try {
        const response = await fetch(DATA_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(req.body)
        });

        const data = await response.json();
        console.log('✅ dataAPI响应:', data);
        
        res.json(data);
    } catch (error) {
        console.error('❌ dataAPI代理失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        envId: ENV_ID
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🌐 代理服务器启动成功!`);
    console.log(`📍 本地地址: http://localhost:${PORT}`);
    console.log(`🔗 管理后台: http://localhost:${PORT}/index.html`);
    console.log(`🧪 测试页面: http://localhost:${PORT}/simple-test.html`);
    console.log('');
    console.log('💡 现在可以通过本地代理访问云开发API了!');
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
});
