<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单SDK测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单SDK测试</h1>
        
        <div style="text-align: center;">
            <button onclick="testSDKInit()">测试SDK初始化</button>
            <button onclick="testCloudFunction()">测试云函数调用</button>
            <button onclick="testDatabase()">测试数据库连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 测试SDK初始化
        async function testSDKInit() {
            try {
                log('🚀 开始测试SDK初始化...');
                
                // 检查SDK可用性
                log('🔍 检查SDK可用性...');
                log('  - typeof cloudbase: ' + typeof cloudbase);
                log('  - typeof window.cloudbase: ' + typeof window.cloudbase);
                log('  - cloudbase对象: ' + (window.cloudbase ? '存在' : '不存在'));
                
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK 2.0未加载，请检查网络连接');
                }

                log('🎯 使用CloudBase SDK 2.17.5版本');

                // 尝试不同的初始化方式
                const envId = 'cloud1-5g6pvnpl88dc0142';
                
                log('🔧 尝试方式1: 包含clientId参数');
                try {
                    tcbApp = window.cloudbase.init({
                        env: envId,
                        clientId: envId
                    });
                    log('✅ 方式1成功: CloudBase SDK初始化成功（包含clientId）', 'success');
                } catch (error1) {
                    log('❌ 方式1失败: ' + error1.message, 'error');
                    
                    log('🔧 尝试方式2: 不包含clientId参数');
                    try {
                        tcbApp = window.cloudbase.init({
                            env: envId
                        });
                        log('✅ 方式2成功: CloudBase SDK初始化成功（不包含clientId）', 'success');
                    } catch (error2) {
                        log('❌ 方式2失败: ' + error2.message, 'error');
                        throw new Error('所有初始化方式都失败');
                    }
                }

                // 检查SDK实例
                log('🔍 检查SDK实例...');
                log('  - tcbApp对象: ' + (tcbApp ? '存在' : '不存在'));
                if (tcbApp) {
                    log('  - 可用方法: ' + Object.keys(tcbApp).join(', '));
                    log('  - auth方法: ' + (tcbApp.auth ? '存在' : '不存在'));
                    log('  - database方法: ' + (tcbApp.database ? '存在' : '不存在'));
                    log('  - callFunction方法: ' + (tcbApp.callFunction ? '存在' : '不存在'));
                }

                log('🎉 SDK初始化测试完成', 'success');
                
            } catch (error) {
                log('❌ SDK初始化测试失败: ' + error.message, 'error');
                log('📋 错误详情: ' + error.stack, 'error');
            }
        }

        // 测试云函数调用
        async function testCloudFunction() {
            if (!tcbApp) {
                log('❌ 请先初始化SDK', 'error');
                return;
            }

            try {
                log('☁️ 开始测试云函数调用...');

                // 测试多种匿名登录方式
                log('🔐 尝试匿名登录...');
                const auth = tcbApp.auth();

                // 检查auth对象的方法
                log('🔍 检查auth对象方法:');
                log('  - signInAnonymously: ' + typeof auth.signInAnonymously);
                log('  - anonymousAuthProvider: ' + typeof auth.anonymousAuthProvider);
                log('  - getLoginState: ' + typeof auth.getLoginState);

                let loginSuccess = false;

                // 方式1: 检查现有登录状态
                try {
                    if (typeof auth.getLoginState === 'function') {
                        const loginState = await auth.getLoginState();
                        if (loginState) {
                            log('✅ 发现现有登录状态', 'success');
                            loginSuccess = true;
                        }
                    }
                } catch (e) {
                    log('⚠️ 检查登录状态失败: ' + e.message, 'warning');
                }

                // 方式2: 尝试简单匿名登录
                if (!loginSuccess && typeof auth.signInAnonymously === 'function') {
                    try {
                        log('🔐 尝试简单匿名登录...');
                        await auth.signInAnonymously();
                        log('✅ 简单匿名登录成功', 'success');
                        loginSuccess = true;
                    } catch (e) {
                        log('⚠️ 简单匿名登录失败: ' + e.message, 'warning');
                        if (e.message && e.message.includes('ACCESS_TOKEN_DISABLED')) {
                            log('💡 提示：需要在云开发控制台启用匿名登录', 'info');
                        }
                    }
                }

                // 方式3: 尝试复杂匿名登录
                if (!loginSuccess && typeof auth.anonymousAuthProvider === 'function') {
                    try {
                        log('🔐 尝试复杂匿名登录...');
                        await auth.anonymousAuthProvider().signIn();
                        log('✅ 复杂匿名登录成功', 'success');
                        loginSuccess = true;
                    } catch (e) {
                        log('⚠️ 复杂匿名登录失败: ' + e.message, 'warning');
                    }
                }

                if (!loginSuccess) {
                    log('⚠️ 所有登录方式都失败，尝试无认证调用', 'warning');
                }

                // 测试云函数调用
                log('📞 调用dataAPI云函数...');
                const result = await tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });

                log('📊 云函数调用结果:');
                log('  - 调用成功: ' + (result ? '是' : '否'));
                if (result) {
                    log('  - result对象: ' + JSON.stringify(result, null, 2));
                }

                log('🎉 云函数调用测试完成', 'success');

            } catch (error) {
                log('❌ 云函数调用测试失败: ' + (error.message || error), 'error');
                log('📋 错误类型: ' + typeof error, 'error');
                log('📋 错误对象: ' + JSON.stringify(error, null, 2), 'error');
                if (error.stack) {
                    log('📋 错误堆栈: ' + error.stack, 'error');
                }
            }
        }

        // 测试数据库连接
        async function testDatabase() {
            if (!tcbApp) {
                log('❌ 请先初始化SDK', 'error');
                return;
            }

            try {
                log('🗄️ 开始测试数据库连接...');

                const db = tcbApp.database();
                log('✅ 数据库对象创建成功', 'success');

                // 检查数据库对象的方法
                log('🔍 检查数据库对象方法:');
                log('  - collection: ' + typeof db.collection);
                log('  - 可用方法: ' + Object.getOwnPropertyNames(db).join(', '));

                // 测试查询
                log('🔍 测试查询categories集合...');

                try {
                    const result = await db.collection('categories').limit(1).get();

                    log('📊 数据库查询结果:');
                    log('  - 查询成功: ' + (result ? '是' : '否'));
                    log('  - result类型: ' + typeof result);

                    if (result) {
                        log('  - 数据条数: ' + (result.data ? result.data.length : 0));
                        log('  - 结果详情: ' + JSON.stringify(result, null, 2));

                        if (result.data && result.data.length > 0) {
                            log('✅ 找到数据，categories集合存在且有数据', 'success');
                        } else {
                            log('⚠️ categories集合存在但为空', 'warning');
                        }
                    }
                } catch (queryError) {
                    log('❌ 数据库查询失败: ' + (queryError.message || queryError), 'error');
                    log('📋 查询错误类型: ' + typeof queryError, 'error');
                    log('📋 查询错误对象: ' + JSON.stringify(queryError, null, 2), 'error');

                    // 检查是否是权限问题
                    if (queryError.message && queryError.message.includes('permission')) {
                        log('💡 可能是权限问题，请检查数据库权限配置', 'info');
                    }

                    // 检查是否是集合不存在
                    if (queryError.message && queryError.message.includes('not exist')) {
                        log('💡 categories集合不存在，需要先创建数据', 'info');
                    }
                }

                log('🎉 数据库连接测试完成', 'success');

            } catch (error) {
                log('❌ 数据库连接测试失败: ' + (error.message || error), 'error');
                log('📋 错误类型: ' + typeof error, 'error');
                log('📋 错误对象: ' + JSON.stringify(error, null, 2), 'error');
                if (error.stack) {
                    log('📋 错误堆栈: ' + error.stack, 'error');
                }
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，请点击"测试SDK初始化"按钮开始测试');
        });
    </script>
</body>
</html>
