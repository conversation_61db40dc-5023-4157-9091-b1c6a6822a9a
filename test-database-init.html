<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        .warning {
            background: #ffc107;
            color: #000;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            display: none;
            color: #007aff;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007aff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 数据库初始化工具</h1>
        <p>这个工具用于初始化微信云开发数据库，创建必要的集合和数据。</p>
        
        <div class="actions">
            <button class="button" onclick="checkCollections()">📊 检查集合状态</button>
            <button class="button success" onclick="initDatabase()">🔧 初始化数据库</button>
            <button class="button warning" onclick="testDataAPI()">🧪 测试数据API</button>
            <button class="button danger" onclick="clearResults()">🗑️ 清空结果</button>
        </div>
        
        <div class="loading" id="loading">⏳ 正在处理...</div>
        
        <div id="results"></div>
    </div>

    <!-- 引入云开发 SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script src="https://unpkg.com/@cloudbase/js-sdk@1.7.1/dist/index.umd.js"></script>
    
    <script>
        // 全局变量
        let app = null;
        let isInitialized = false;

        // 初始化云开发
        async function initCloudbase() {
            try {
                console.log('🔧 开始初始化云开发...');

                // 检查SDK是否加载
                if (typeof cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK 未加载');
                }

                app = cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'  // SDK 2.0必需参数
                });

                console.log('🔐 开始匿名登录...');
                await app.auth().signInAnonymously();

                isInitialized = true;
                console.log('✅ 云开发初始化成功');
                addResult('✅ 云开发初始化成功', 'success');

                return true;
            } catch (error) {
                console.error('❌ 云开发初始化失败:', error);
                addResult('❌ 云开发初始化失败: ' + error.message, 'error');
                return false;
            }
        }

        // 确保初始化完成后再执行操作
        async function ensureInitialized() {
            if (!isInitialized) {
                addResult('⏳ 正在初始化云开发...', 'success');
                const success = await initCloudbase();
                if (!success) {
                    throw new Error('云开发初始化失败');
                }
            }
            return app;
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 添加结果显示
        function addResult(message, type = 'success') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 检查集合状态
        async function checkCollections() {
            showLoading();
            addResult('🔍 开始检查数据库集合状态...');

            try {
                const cloudApp = await ensureInitialized();
                const collections = ['categories', 'emojis', 'banners', 'users'];

                for (const collection of collections) {
                    try {
                        const db = cloudApp.database();
                        const result = await db.collection(collection).limit(1).get();
                        addResult(`✅ ${collection}: 存在 (${result.data.length} 条记录)`);
                    } catch (error) {
                        addResult(`❌ ${collection}: 不存在或无法访问 - ${error.message}`, 'error');
                    }
                }
            } catch (error) {
                addResult(`❌ 初始化失败: ${error.message}`, 'error');
            }

            hideLoading();
        }

        // 初始化数据库
        async function initDatabase() {
            showLoading();
            addResult('🚀 开始初始化数据库...');

            try {
                const cloudApp = await ensureInitialized();

                const result = await cloudApp.callFunction({
                    name: 'initDatabase',
                    data: {}
                });

                if (result.result.success) {
                    addResult('✅ 数据库初始化成功!');
                    addResult(`📊 结果详情: ${result.result.message}`);

                    // 显示详细结果
                    if (result.result.results) {
                        const results = result.result.results;
                        addResult(`分类: ${results.categories ? '✅' : '❌'}`);
                        addResult(`表情包: ${results.emojis ? '✅' : '❌'}`);
                        addResult(`横幅: ${results.banners ? '✅' : '❌'}`);

                        if (results.errors && results.errors.length > 0) {
                            addResult('⚠️ 部分错误:', 'error');
                            results.errors.forEach(error => {
                                addResult(`  - ${error}`, 'error');
                            });
                        }
                    }
                } else {
                    addResult(`❌ 数据库初始化失败: ${result.result.message}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 调用初始化函数失败: ${error.message}`, 'error');
            }

            hideLoading();
        }

        // 测试数据API
        async function testDataAPI() {
            showLoading();
            addResult('🧪 开始测试数据API...');

            try {
                const cloudApp = await ensureInitialized();

                // 测试获取分类
                addResult('📁 测试获取分类数据...');
                const categoriesResult = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getCategories'
                    }
                });

                if (categoriesResult.result.success) {
                    addResult(`✅ 分类数据获取成功: ${categoriesResult.result.data.length} 个分类`);
                } else {
                    addResult(`❌ 分类数据获取失败: ${categoriesResult.result.error}`, 'error');
                }

                // 测试获取横幅
                addResult('🎨 测试获取横幅数据...');
                const bannersResult = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getBanners'
                    }
                });

                if (bannersResult.result.success) {
                    addResult(`✅ 横幅数据获取成功: ${bannersResult.result.data.length} 个横幅`);
                } else {
                    addResult(`❌ 横幅数据获取失败: ${bannersResult.result.error}`, 'error');
                }

                // 测试获取热门表情包
                addResult('🔥 测试获取热门表情包...');
                const hotEmojisResult = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getHotEmojis',
                        limit: 6
                    }
                });

                if (hotEmojisResult.result.success) {
                    addResult(`✅ 热门表情包获取成功: ${hotEmojisResult.result.data.length} 个表情包`);
                } else {
                    addResult(`❌ 热门表情包获取失败: ${hotEmojisResult.result.error}`, 'error');
                }

            } catch (error) {
                addResult(`❌ 测试API失败: ${error.message}`, 'error');
            }

            hideLoading();
        }

        // 页面加载完成后自动初始化
        window.onload = function() {
            setTimeout(async () => {
                addResult('🔧 页面加载完成，开始初始化...');
                try {
                    await initCloudbase();
                    // 初始化成功后自动检查集合状态
                    setTimeout(() => {
                        checkCollections();
                    }, 1000);
                } catch (error) {
                    addResult(`❌ 自动初始化失败: ${error.message}`, 'error');
                }
            }, 1000);
        };
    </script>
</body>
</html>
