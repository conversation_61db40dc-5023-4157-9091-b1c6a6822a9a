<!--pages/collection/collection.wxml-->
<view class="collection-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">
      <text class="title">我的收藏</text>
      <text class="count">{{stats.total}}个表情包</text>
    </view>
    
    <!-- 工具栏 -->
    <view class="toolbar">
      <!-- 搜索框 -->
      <view class="search-box">
        <input 
          class="search-input"
          placeholder="搜索收藏的表情包"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
        />
        <view class="search-icon" wx:if="{{!searchKeyword}}">🔍</view>
        <view class="clear-icon" wx:if="{{searchKeyword}}" bindtap="onClearSearch">×</view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn" bindtap="onToggleSelectionMode">
          <text class="btn-icon">{{selectionMode ? '✓' : '☐'}}</text>
          <text class="btn-text">{{selectionMode ? '完成' : '选择'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选和排序栏 -->
  <view class="filter-bar" wx:if="{{!selectionMode}}">
    <!-- 显示模式切换 -->
    <view class="view-mode">
      <view 
        class="mode-btn {{viewMode === 'grid' ? 'active' : ''}}"
        data-mode="grid"
        bindtap="onViewModeChange"
      >
        <text class="mode-icon">⊞</text>
      </view>
      <view 
        class="mode-btn {{viewMode === 'list' ? 'active' : ''}}"
        data-mode="list"
        bindtap="onViewModeChange"
      >
        <text class="mode-icon">☰</text>
      </view>
    </view>
    
    <!-- 网格列数选择 -->
    <view class="grid-columns" wx:if="{{viewMode === 'grid'}}">
      <view 
        class="column-btn {{gridColumns === 1 ? 'active' : ''}}"
        data-columns="1"
        bindtap="onGridColumnsChange"
      >1</view>
      <view 
        class="column-btn {{gridColumns === 2 ? 'active' : ''}}"
        data-columns="2"
        bindtap="onGridColumnsChange"
      >2</view>
      <view 
        class="column-btn {{gridColumns === 3 ? 'active' : ''}}"
        data-columns="3"
        bindtap="onGridColumnsChange"
      >3</view>
    </view>
    
    <!-- 排序选择 -->
    <view class="sort-options">
      <view 
        class="sort-btn {{sortBy === 'collectTime' ? 'active' : ''}}"
        data-sort-by="collectTime"
        data-sort-order="desc"
        bindtap="onSortChange"
      >最新收藏</view>
      <view 
        class="sort-btn {{sortBy === 'likes' ? 'active' : ''}}"
        data-sort-by="likes"
        data-sort-order="desc"
        bindtap="onSortChange"
      >最多点赞</view>
      <view 
        class="sort-btn {{sortBy === 'title' ? 'active' : ''}}"
        data-sort-by="title"
        data-sort-order="asc"
        bindtap="onSortChange"
      >按名称</view>
    </view>
  </view>

  <!-- 批量操作栏 -->
  <view class="batch-actions" wx:if="{{selectionMode}}">
    <view class="batch-info">
      <text class="selected-count">已选择 {{selectedEmojis.length}} 个</text>
      <view class="select-all-btn" bindtap="onSelectAll">
        {{selectedEmojis.length === (isSearching ? searchResults.length : collectedEmojis.length) ? '取消全选' : '全选'}}
      </view>
    </view>
    
    <view class="batch-buttons">
      <button class="batch-btn uncollect-btn" bindtap="onBatchUncollect">
        取消收藏
      </button>
      <button class="batch-btn share-btn" bindtap="onBatchShare">
        分享
      </button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && stats.total === 0}}">
    <view class="empty-icon">💖</view>
    <view class="empty-title">暂无收藏</view>
    <view class="empty-desc">快去收藏喜欢的表情包吧</view>
    <button class="empty-action" bindtap="onGoToIndex">
      去首页看看
    </button>
  </view>

  <!-- 搜索无结果 -->
  <view class="empty-state" wx:if="{{!loading && isSearching && searchResults.length === 0}}">
    <view class="empty-icon">🔍</view>
    <view class="empty-title">未找到相关表情包</view>
    <view class="empty-desc">试试其他关键词</view>
  </view>

  <!-- 表情包列表 -->
  <view class="emoji-list" wx:if="{{!loading && (isSearching ? searchResults.length > 0 : collectedEmojis.length > 0)}}">
    <!-- 网格模式 -->
    <view 
      class="emoji-grid columns-{{gridColumns}}" 
      wx:if="{{viewMode === 'grid'}}"
    >
      <view 
        class="emoji-item {{selectionMode ? 'selection-mode' : ''}} {{selectedEmojis.includes(item.id) ? 'selected' : ''}}"
        wx:for="{{isSearching ? searchResults : collectedEmojis}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onEmojiTap"
      >
        <!-- 选择框 -->
        <view class="selection-checkbox" wx:if="{{selectionMode}}">
          <view class="checkbox {{selectedEmojis.includes(item.id) ? 'checked' : ''}}">
            <text class="check-icon" wx:if="{{selectedEmojis.includes(item.id)}}">✓</text>
          </view>
        </view>
        
        <!-- 表情包图片 -->
        <image 
          class="emoji-image"
          src="{{item.imageUrl || item.url}}"
          mode="aspectFill"
          lazy-load="{{true}}"
        />
        
        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <view class="emoji-meta">
            <text class="emoji-category">{{item.category}}</text>
            <text class="collect-time">{{item.collectTime | formatTime}}</text>
          </view>
        </view>
        
        <!-- 状态标识 -->
        <view class="status-badges">
          <view class="badge liked" wx:if="{{item.isLiked}}">❤️</view>
          <view class="badge downloaded" wx:if="{{item.downloadTime}}">📥</view>
        </view>
      </view>
    </view>

    <!-- 列表模式 -->
    <view class="emoji-list-view" wx:if="{{viewMode === 'list'}}">
      <view 
        class="emoji-list-item {{selectionMode ? 'selection-mode' : ''}} {{selectedEmojis.includes(item.id) ? 'selected' : ''}}"
        wx:for="{{isSearching ? searchResults : collectedEmojis}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onEmojiTap"
      >
        <!-- 选择框 -->
        <view class="selection-checkbox" wx:if="{{selectionMode}}">
          <view class="checkbox {{selectedEmojis.includes(item.id) ? 'checked' : ''}}">
            <text class="check-icon" wx:if="{{selectedEmojis.includes(item.id)}}">✓</text>
          </view>
        </view>
        
        <!-- 表情包缩略图 -->
        <image 
          class="emoji-thumbnail"
          src="{{item.imageUrl || item.url}}"
          mode="aspectFill"
          lazy-load="{{true}}"
        />
        
        <!-- 表情包详细信息 -->
        <view class="emoji-details">
          <view class="emoji-title">{{item.title}}</view>
          <view class="emoji-meta">
            <text class="emoji-category">{{item.category}}</text>
            <text class="emoji-stats">❤️ {{item.likes || 0}} 📥 {{item.downloads || 0}}</text>
          </view>
          <view class="collect-info">
            <text class="collect-time">收藏于 {{item.collectTime | formatTime}}</text>
          </view>
        </view>
        
        <!-- 状态标识 -->
        <view class="status-badges">
          <view class="badge liked" wx:if="{{item.isLiked}}">❤️</view>
          <view class="badge downloaded" wx:if="{{item.downloadTime}}">📥</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-panel" wx:if="{{!loading && stats.total > 0 && !selectionMode}}">
    <view class="stats-title">收藏统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{stats.total}}</text>
        <text class="stat-label">总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{stats.recentCount}}</text>
        <text class="stat-label">本周新增</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{Object.keys(stats.categories).length}}</text>
        <text class="stat-label">分类数</text>
      </view>
    </view>
  </view>
</view>

<!-- 自定义过滤器 -->
<wxs module="formatTime">
  function formatTime(timestamp) {
    if (!timestamp) return ''

    var now = getDate()
    var date = getDate(timestamp)
    var diff = now.getTime() - date.getTime()

    var minute = 60 * 1000
    var hour = 60 * minute
    var day = 24 * hour
    var week = 7 * day

    if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < week) {
      return Math.floor(diff / day) + '天前'
    } else {
      return (date.getMonth() + 1) + '月' + date.getDate() + '日'
    }
  }

  module.exports = {
    formatTime: formatTime
  }
</wxs>
