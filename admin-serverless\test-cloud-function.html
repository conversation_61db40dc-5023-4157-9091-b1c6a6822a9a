<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 云函数测试页面</h1>
    
    <div class="test-section">
        <h2>📊 测试统计数据获取</h2>
        <button onclick="testGetStats()">测试 getStats</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>🔐 测试密码验证</h2>
        <button onclick="testWithPassword()">使用正确密码</button>
        <button onclick="testWithWrongPassword()">使用错误密码</button>
        <button onclick="testWithoutPassword()">不使用密码</button>
        <div id="authResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>📝 测试数据库初始化</h2>
        <button onclick="testInitDatabase()">初始化数据库</button>
        <div id="initResult" class="result"></div>
    </div>

    <!-- 云开发SDK 2.0版本 -->
    <script src="https://unpkg.com/@cloudbase/js-sdk@2.0.1/dist/index.umd.js"></script>
    
    <script>
        let tcbApp = null;
        
        // 初始化云开发 - 使用2.0 SDK
        async function initCloud() {
            try {
                const envId = 'cloud1-5g6pvnpl88dc0142';

                // 检查SDK是否加载
                if (typeof cloudbase !== 'undefined') {
                    console.log('🚀 使用CloudBase 2.0 SDK初始化...');
                    tcbApp = cloudbase.init({
                        env: envId,
                        region: 'ap-shanghai'
                    });
                } else if (typeof tcb !== 'undefined') {
                    console.log('🚀 使用TCB SDK初始化...');
                    tcbApp = tcb.init({
                        env: envId,
                        region: 'ap-shanghai'
                    });
                } else {
                    throw new Error('CloudBase SDK 未加载');
                }

                // 匿名登录
                const auth = tcbApp.auth();
                try {
                    await auth.anonymousAuthProvider().signIn();
                    console.log('✅ 匿名登录成功');
                } catch (authError) {
                    console.warn('⚠️ 匿名登录失败，继续尝试:', authError);
                }

                console.log('✅ 云开发初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 云开发初始化失败:', error);
                return false;
            }
        }
        
        // 调用云函数
        async function callFunction(name, data) {
            if (!tcbApp) {
                const initialized = await initCloud();
                if (!initialized) {
                    throw new Error('云开发初始化失败');
                }
            }
            
            const response = await tcbApp.callFunction({
                name: name,
                data: data
            });
            
            return response.result;
        }
        
        // 测试获取统计数据
        async function testGetStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callFunction('adminAPI', {
                    action: 'getStats',
                    adminPassword: 'admin123456'
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }
        
        // 测试正确密码
        async function testWithPassword() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callFunction('adminAPI', {
                    action: 'getStats',
                    adminPassword: 'admin123456'
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 正确密码测试:\n' + JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 正确密码测试失败: ' + error.message;
            }
        }
        
        // 测试错误密码
        async function testWithWrongPassword() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callFunction('adminAPI', {
                    action: 'getStats',
                    adminPassword: 'wrongpassword'
                });
                
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 错误密码应该失败但成功了:\n' + JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 错误密码正确被拒绝: ' + error.message;
            }
        }
        
        // 测试不使用密码
        async function testWithoutPassword() {
            const resultDiv = document.getElementById('authResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callFunction('adminAPI', {
                    action: 'getStats'
                });
                
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 无密码应该失败但成功了:\n' + JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 无密码正确被拒绝: ' + error.message;
            }
        }
        
        // 测试数据库初始化
        async function testInitDatabase() {
            const resultDiv = document.getElementById('initResult');
            resultDiv.textContent = '正在初始化数据库...';
            
            try {
                const result = await callFunction('adminAPI', {
                    action: 'initDatabase',
                    adminPassword: 'admin123456'
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '错误: ' + error.message;
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', async () => {
            console.log('🚀 开始初始化云开发...');
            await initCloud();
        });
    </script>
</body>
</html>
