@echo off
chcp 65001 >nul
echo ========================================
echo 启动真实数据同步系统
echo ========================================
echo.

echo 检查环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或不在PATH中
    echo 请安装 Node.js 14.0.0 或更高版本
    pause
    exit /b 1
)

echo ✅ Node.js 环境正常

echo.
echo 检查配置文件...
if not exist "admin-unified\config\cloud-config.js" (
    echo ⚠️ 云开发配置文件不存在
    echo 正在复制配置模板...
    copy "admin-unified\config\cloud-config.template.js" "admin-unified\config\cloud-config.js"
    echo.
    echo ❌ 请先编辑 admin-unified\config\cloud-config.js 文件
    echo 填写正确的云开发环境ID和访问密钥
    echo.
    echo 配置完成后重新运行此脚本
    pause
    exit /b 1
)

echo ✅ 配置文件存在

echo.
echo 检查依赖包...
cd admin-unified
if not exist "node_modules" (
    echo 📦 安装依赖包...
    npm install wx-server-sdk express cors
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖包正常

echo.
echo 停止现有服务...
taskkill /f /im node.exe 2>nul

echo.
echo 创建日志目录...
if not exist "logs" mkdir "logs"

echo.
echo 运行环境验证...
node ..\scripts\verify-setup.js
if %errorlevel% neq 0 (
    echo ❌ 环境验证失败
    echo 请检查配置文件和网络连接
    pause
    exit /b 1
)

echo ✅ 环境验证通过

echo.
echo 🚀 启动管理后台服务器...
echo 服务地址: http://localhost:8001
echo 管理界面: http://localhost:8001/index-fixed.html
echo.
echo 按 Ctrl+C 停止服务
echo ========================================

node real-cloud-server.js

echo.
echo 服务已停止
pause
