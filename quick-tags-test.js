// 快速标签显示测试脚本 - 直接在小程序控制台中运行

console.log('🚀 开始快速标签显示测试...');

// 1. 检查当前页面
function quickTest() {
    const pages = getCurrentPages();
    if (pages.length === 0) {
        console.log('❌ 没有找到当前页面');
        return;
    }
    
    const currentPage = pages[pages.length - 1];
    console.log('📄 当前页面:', currentPage.route);
    
    // 2. 检查页面数据
    const data = currentPage.data;
    console.log('📊 页面数据键:', Object.keys(data));
    
    // 3. 检查表情包数据
    if (data.emojiList && data.emojiList.length > 0) {
        const firstEmoji = data.emojiList[0];
        console.log('📦 第一个表情包数据:');
        console.log('  - 标题:', firstEmoji.title);
        console.log('  - 标签存在:', 'tags' in firstEmoji);
        console.log('  - 标签值:', firstEmoji.tags);
        console.log('  - 标签类型:', typeof firstEmoji.tags);
        console.log('  - 是数组:', Array.isArray(firstEmoji.tags));
        console.log('  - 标签长度:', firstEmoji.tags ? firstEmoji.tags.length : 'N/A');
        
        const condition = firstEmoji.tags && firstEmoji.tags.length > 0;
        console.log('  - wx:if条件满足:', condition);
        
        if (!condition) {
            console.log('⚠️ 条件不满足，标签不会显示');
            
            // 添加测试标签
            console.log('🔧 添加测试标签...');
            const updatedEmojiList = data.emojiList.map(emoji => ({
                ...emoji,
                tags: emoji.tags || ['测试', '标签', '显示']
            }));
            
            currentPage.setData({
                emojiList: updatedEmojiList
            });
            
            console.log('✅ 已添加测试标签，请检查页面显示');
        }
    } else {
        console.log('❌ 没有找到表情包数据');
        
        // 创建测试数据
        console.log('🔧 创建测试数据...');
        const testData = [
            {
                _id: 'test_001',
                title: '测试表情包1',
                imageUrl: 'https://via.placeholder.com/200x200/FF6B6B/FFFFFF?text=😂',
                category: '测试分类',
                tags: ['搞笑', '测试', '标签'],
                likes: 100,
                collections: 50
            },
            {
                _id: 'test_002',
                title: '测试表情包2',
                imageUrl: 'https://via.placeholder.com/200x200/4ECDC4/FFFFFF?text=😍',
                category: '测试分类',
                tags: ['可爱', '萌宠', '治愈'],
                likes: 80,
                collections: 30
            }
        ];
        
        currentPage.setData({
            emojiList: testData,
            searchResults: []
        });
        
        console.log('✅ 已创建测试数据，请检查页面显示');
    }
}

// 4. 检查DOM元素
function checkDOM() {
    console.log('🔍 检查DOM元素...');
    
    // 注意：在小程序中无法直接访问DOM，这里只是示例
    console.log('⚠️ 小程序中无法直接检查DOM元素');
    console.log('请在开发者工具的调试器中查看页面结构');
    console.log('查找 .emoji-tags 和 .tag-item 元素');
}

// 5. 强制刷新云函数数据
async function refreshCloudData() {
    console.log('☁️ 刷新云函数数据...');
    
    try {
        const result = await wx.cloud.callFunction({
            name: 'dataAPI',
            data: {
                action: 'getEmojis',
                data: {
                    category: 'all',
                    page: 1,
                    limit: 5
                }
            }
        });
        
        console.log('☁️ 云函数调用成功');
        console.log('返回数据:', result.result);
        
        if (result.result && result.result.success && result.result.data) {
            const emojis = result.result.data;
            console.log(`📦 获取到 ${emojis.length} 个表情包`);
            
            // 检查第一个表情包的标签
            if (emojis.length > 0) {
                const firstEmoji = emojis[0];
                console.log('第一个表情包标签:', firstEmoji.tags);
                
                // 如果没有标签，添加测试标签
                if (!firstEmoji.tags || firstEmoji.tags.length === 0) {
                    console.log('⚠️ 云函数返回的数据没有标签，添加测试标签...');
                    emojis.forEach(emoji => {
                        emoji.tags = ['云函数', '测试', '标签'];
                    });
                }
            }
            
            // 更新页面数据
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            currentPage.setData({
                emojiList: emojis,
                searchResults: []
            });
            
            console.log('✅ 页面数据已更新');
        }
    } catch (error) {
        console.error('❌ 云函数调用失败:', error);
    }
}

// 主测试函数
async function runQuickTest() {
    console.log('='.repeat(50));
    console.log('🏷️ 快速标签显示测试');
    console.log('='.repeat(50));
    
    // 1. 基础检查
    quickTest();
    
    // 2. DOM检查提示
    checkDOM();
    
    // 3. 刷新云函数数据
    console.log('\n⏳ 3秒后刷新云函数数据...');
    setTimeout(async () => {
        await refreshCloudData();
        
        // 4. 再次检查
        console.log('\n🔄 再次检查数据...');
        quickTest();
        
        console.log('\n✅ 测试完成！');
        console.log('📋 如果仍然看不到标签，请：');
        console.log('1. 点击开发者工具的"编译"按钮');
        console.log('2. 清除缓存后重新编译');
        console.log('3. 检查WXML和WXSS文件是否正确修改');
    }, 3000);
}

// 运行测试
runQuickTest();

// 导出函数供手动调用
window.quickTagsTest = {
    runQuickTest,
    quickTest,
    checkDOM,
    refreshCloudData
};

console.log('💡 可以手动调用以下函数：');
console.log('- quickTagsTest.runQuickTest() // 运行完整测试');
console.log('- quickTagsTest.quickTest() // 快速检查');
console.log('- quickTagsTest.refreshCloudData() // 刷新云函数数据');
