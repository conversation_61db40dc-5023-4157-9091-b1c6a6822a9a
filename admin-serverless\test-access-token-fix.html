<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACCESS_TOKEN_DISABLED 修复测试</title>
    <!-- 智能SDK加载器 - 与main.html保持一致 -->
    <script>
        // 🎯 标准配置：强制使用SDK 2.0版本（根据技术文档）
        const sdkSources = [
            'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js',  // 首选
            'https://unpkg.com/@cloudbase/js-sdk@2.17.5/dist/index.umd.js',            // 备用1
            'https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js'   // 备用2（官方CDN）
        ];

        // 动态加载脚本函数
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`✅ SDK加载成功: ${src}`);
                    resolve();
                };
                script.onerror = () => {
                    console.log(`❌ SDK加载失败: ${src}`);
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }

        // 智能SDK加载器
        async function loadSDK() {
            console.log('🚀 开始智能SDK加载...');

            for (let i = 0; i < sdkSources.length; i++) {
                const url = sdkSources[i];
                try {
                    console.log(`🔄 尝试CDN ${i + 1}/${sdkSources.length}: ${url}`);
                    await loadScript(url);

                    // 检查SDK是否成功加载
                    if (window.cloudbase || window.tcb) {
                        console.log('✅ SDK加载成功，可用对象:', {
                            cloudbase: !!window.cloudbase,
                            tcb: !!window.tcb
                        });
                        return true;
                    }
                } catch (error) {
                    console.log(`❌ CDN ${i + 1} 失败: ${url}`);
                    if (i === sdkSources.length - 1) {
                        throw new Error('所有CDN都无法加载SDK');
                    }
                    continue;
                }
            }
        }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 ACCESS_TOKEN_DISABLED 修复测试</h1>
    
    <div class="test-section">
        <h2>📋 测试步骤（基于技术文档的正确方案）</h2>
        <ol>
            <li>检查SDK版本和加载状态</li>
            <li>初始化云开发SDK（强制CloudBase 2.0版本）</li>
            <li><strong>测试身份认证 + webAdminAPI（完整正确方案）</strong></li>
            <li>测试云函数调用（验证webAdminAPI功能）</li>
            <li>测试数据库连接（通过云函数代理）</li>
        </ol>
        <div style="background: #e7f3ff; padding: 10px; border-radius: 4px; margin-top: 10px;">
            <strong>💡 重要提示：</strong>根据技术文档，Web端需要先进行匿名登录获取身份认证，然后调用webAdminAPI云函数。
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 SDK状态检查</h2>
        <div id="sdk-status"></div>
        <button onclick="checkSDKStatus()">检查SDK状态</button>
    </div>

    <div class="test-section">
        <h2>🚀 云开发初始化</h2>
        <div id="init-status"></div>
        <button onclick="initCloudBase()">初始化云开发</button>
    </div>

    <div class="test-section">
        <h2>🔐 身份认证 + webAdminAPI测试（完整方案）</h2>
        <div id="auth-status"></div>
        <button onclick="testAnonymousLogin()">测试身份认证和webAdminAPI</button>
        <p style="color: #666; font-size: 12px; margin-top: 10px;">
            💡 根据技术文档，需要先匿名登录获取身份认证，然后调用webAdminAPI云函数
        </p>
    </div>

    <div class="test-section">
        <h2>☁️ 云函数测试</h2>
        <div id="function-status"></div>
        <button onclick="testCloudFunction()">测试云函数</button>
    </div>

    <div class="test-section">
        <h2>🗄️ 数据库测试</h2>
        <div id="database-status"></div>
        <button onclick="testDatabase()">测试数据库</button>
    </div>

    <div class="test-section">
        <h2>📊 测试日志</h2>
        <pre id="test-logs"></pre>
        <button onclick="clearLogs()">清空日志</button>
    </div>

    <script>
        // 全局变量
        let app = null;
        const envId = 'cloud1-5g6pvnpl88dc0142';
        
        // 日志记录
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-logs');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = '';
        }

        // 1. 检查SDK状态
        function checkSDKStatus() {
            log('🔍 开始检查SDK状态...');
            
            const hasCloudbase = typeof window.cloudbase !== 'undefined';
            const hasTcb = typeof window.tcb !== 'undefined';
            
            let statusMessage = '';
            let statusType = 'info';
            
            if (hasCloudbase) {
                statusMessage += '✅ CloudBase SDK 2.17.5 已加载<br>';
                log('✅ CloudBase SDK 2.17.5 已加载');
                statusType = 'success';
            } else {
                statusMessage += '❌ CloudBase SDK 未加载<br>';
                log('❌ CloudBase SDK 未加载');
                statusType = 'error';
            }
            
            if (hasTcb) {
                statusMessage += '✅ TCB SDK 已加载<br>';
                log('✅ TCB SDK 已加载');
            } else {
                statusMessage += '❌ TCB SDK 未加载<br>';
                log('❌ TCB SDK 未加载');
            }
            
            statusMessage += `<br>环境ID: ${envId}`;
            
            showStatus('sdk-status', statusMessage, statusType);
        }

        // 2. 初始化云开发 - 智能版本检测
        async function initCloudBase() {
            log('🚀 开始初始化云开发...');

            try {
                // 强制使用CloudBase 2.0版本（根据技术文档）
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK 2.0未加载，请检查网络连接或刷新页面');
                }

                log('🎯 使用CloudBase SDK 2.17.5版本（强制2.0版本）');

                // CloudBase 2.0版本必须提供clientId参数
                const initConfig = {
                    env: envId,
                    clientId: envId  // SDK 2.0必需参数
                };

                log('🔧 使用CloudBase 2.0配置（包含clientId）');
                app = window.cloudbase.init(initConfig);

                log('✅ 云开发初始化成功');
                log(`📱 SDK版本: CloudBase 2.17.5`);
                log(`🔧 初始化配置: ${JSON.stringify(initConfig)}`);
                log(`📱 SDK实例: ${!!app}`);
                log(`🔍 可用方法: ${Object.keys(app).join(', ')}`);

                showStatus('init-status', `✅ 云开发初始化成功 (CloudBase 2.17.5)`, 'success');

            } catch (error) {
                log(`❌ 云开发初始化失败: ${error.message}`);
                showStatus('init-status', `❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        // 3. 测试身份认证和webAdminAPI（正确方案）
        async function testAnonymousLogin() {
            log('🔐 开始测试身份认证和webAdminAPI云函数...');

            if (!app) {
                showStatus('auth-status', '❌ 请先初始化云开发', 'error');
                return;
            }

            try {
                log('💡 根据技术文档，需要先进行匿名登录，然后调用webAdminAPI');

                // 第一步：匿名登录
                log('🔄 步骤1：执行匿名登录...');
                const auth = app.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功');

                // 第二步：调用webAdminAPI云函数
                log('🔄 步骤2：调用webAdminAPI云函数...');
                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getCategoryList',
                        adminPassword: 'admin123456'
                    }
                });

                log('✅ webAdminAPI云函数调用成功');
                log(`📊 返回结果: ${JSON.stringify(result, null, 2)}`);

                if (result.result && result.result.success) {
                    showStatus('auth-status',
                        '✅ 身份认证和webAdminAPI调用成功<br>' +
                        '💡 这是Web端的正确完整方案', 'success');
                } else {
                    showStatus('auth-status',
                        `⚠️ 云函数调用成功但业务逻辑失败<br>` +
                        `错误: ${result.result?.error || '未知错误'}`, 'warning');
                }

            } catch (error) {
                log(`❌ 操作失败: ${error.message}`);

                if (error.message.includes('ACCESS_TOKEN_DISABLED')) {
                    showStatus('auth-status',
                        '❌ ACCESS_TOKEN_DISABLED错误<br>' +
                        '💡 需要在云开发控制台开启匿名登录<br>' +
                        '📍 路径：云开发控制台 → 环境 → 登录授权 → 匿名登录', 'error');
                } else if (error.message.includes('you can\'t request without auth')) {
                    showStatus('auth-status',
                        '❌ 身份认证失败<br>' +
                        '💡 请确保已开启匿名登录功能', 'error');
                } else {
                    showStatus('auth-status',
                        `❌ 操作失败: ${error.message}<br>` +
                        `💡 请检查网络连接和云函数部署状态`, 'error');
                }
            }
        }

        // 4. 测试云函数
        async function testCloudFunction() {
            log('☁️ 开始测试云函数...');
            
            if (!app) {
                showStatus('function-status', '❌ 请先初始化云开发', 'error');
                return;
            }
            
            try {
                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getCategoryList',
                        adminPassword: 'admin123456'
                    }
                });
                
                log('✅ 云函数调用成功');
                log(`📊 返回结果: ${JSON.stringify(result, null, 2)}`);
                showStatus('function-status', '✅ 云函数调用成功', 'success');
                
            } catch (error) {
                log(`❌ 云函数调用失败: ${error.message}`);
                showStatus('function-status', `❌ 云函数调用失败: ${error.message}`, 'error');
            }
        }

        // 5. 测试数据库
        async function testDatabase() {
            log('🗄️ 开始测试数据库...');
            
            if (!app) {
                showStatus('database-status', '❌ 请先初始化云开发', 'error');
                return;
            }
            
            try {
                const db = app.database();
                log('✅ 数据库对象创建成功');
                
                // 测试查询
                const result = await db.collection('categories').limit(1).get();
                log('✅ 数据库查询成功');
                log(`📊 查询结果: ${JSON.stringify(result, null, 2)}`);
                showStatus('database-status', '✅ 数据库连接和查询成功', 'success');
                
            } catch (error) {
                log(`❌ 数据库操作失败: ${error.message}`);
                showStatus('database-status', `❌ 数据库操作失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后智能初始化
        window.addEventListener('load', async function() {
            log('🚀 页面加载完成，开始智能初始化...');

            try {
                // 先加载SDK
                await loadSDK();
                log('✅ SDK加载完成');

                // 自动检查SDK状态
                setTimeout(checkSDKStatus, 500);

            } catch (error) {
                log(`❌ SDK加载失败: ${error.message}`);
                showStatus('sdk-status', `❌ SDK加载失败: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
