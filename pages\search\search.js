// pages/search/search.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')
const { withPageState, EmojiStateHelper, PaginationMixin } = require('../../utils/pageStateMixin.js')

// 分类ID到名称的映射
const categoryNameMap = {
  'funny': '搞笑幽默',
  'cute': '可爱萌宠',
  'emotion': '情感表达',
  'festival': '节日庆典',
  'hot': '网络热梗',
  '2d': '动漫二次元',
  'daily': '生活日常',
  'work': '工作学习',
  'sport': '运动健身',
  'food': '美食料理'
}

const pageConfig = {
  data: {
    searchKeyword: '',
    searchFocus: false,
    searchResults: [],
    hasSearched: false,
    hotTags: ['搞笑', '可爱', '萌宠', '爱情', '新年', '表白', '猫咪', '快乐'],
    searchHistory: [],
    // 搜索配置
    searchConfig: {
      sortBy: 'relevance', // relevance, likes, downloads, newest
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      loading: false
    },
    // 搜索统计
    searchStats: {
      total: 0,
      keyword: '',
      searchTime: 0
    },
    // 排序选项
    sortOptions: [
      { value: 'relevance', label: '相关性', icon: '🎯' },
      { value: 'likes', label: '最多点赞', icon: '❤️' },
      { value: 'downloads', label: '最多下载', icon: '📥' },
      { value: 'newest', label: '最新发布', icon: '🆕' }
    ],
    showSortMenu: false,
    currentSortLabel: '相关性' // 默认排序标签
  },

  onLoad(options) {
    this.loadSearchHistory()

    // 检查是否有从首页传递过来的搜索关键词
    if (options.keyword) {
      const keyword = decodeURIComponent(options.keyword)
      this.setData({
        searchKeyword: keyword,
        searchFocus: false
      })
      // 自动执行搜索
      this.performSearch(keyword)
      this.saveSearchHistory(keyword)
    } else {
      this.setData({
        searchFocus: true
      })
    }

    // 预加载搜索建议
    this.preloadSearchSuggestions()
  },

  onShow() {
    // 同步自定义TabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
    
    // 页面显示时可以刷新搜索历史
    this.loadSearchHistory()
  },

  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || []
    this.setData({
      searchHistory: history.slice(0, 10) // 最多显示10条历史记录
    })
  },

  saveSearchHistory(keyword) {
    if (!keyword.trim()) return
    
    let history = wx.getStorageSync('searchHistory') || []
    
    // 移除重复项
    history = history.filter(item => item !== keyword)
    
    // 添加到开头
    history.unshift(keyword)
    
    // 限制历史记录数量
    history = history.slice(0, 20)
    
    wx.setStorageSync('searchHistory', history)
    this.setData({
      searchHistory: history.slice(0, 10)
    })
  },

  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    // 添加智能搜索建议
    if (keyword.trim()) {
      const suggestions = this.getSmartSuggestions(keyword)
      this.setData({
        searchSuggestions: suggestions,
        showSuggestions: suggestions.length > 0
      })
    } else {
      this.setData({
        showSuggestions: false
      })
    }
  },

  onSearchConfirm() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    this.performSearch(keyword)
    this.saveSearchHistory(keyword)
  },

  async performSearch(keyword, isLoadMore = false) {
    console.log('🔍 开始搜索:', keyword, isLoadMore ? '(加载更多)' : '')

    if (!isLoadMore) {
      // 重置搜索状态
      this.setData({
        'searchConfig.currentPage': 1,
        'searchConfig.hasMore': true,
        'searchConfig.loading': true,
        searchResults: []
      })

      // 显示加载状态
      wx.showLoading({ title: '搜索中...' })
    } else {
      this.setData({
        'searchConfig.loading': true
      })
    }

    try {
      const startTime = Date.now()
      const { currentPage, pageSize, sortBy } = this.data.searchConfig

      // 使用云端搜索功能
      const result = await DataManager.searchEmojis(
        keyword,
        isLoadMore ? currentPage + 1 : 1,
        pageSize,
        { sortBy }
      )

      const searchTime = Date.now() - startTime

      // 处理搜索结果
      const searchResults = result.data || result || []

      // 添加格式化的统计数据和状态
      const formattedResults = searchResults.map(item => {
        const emojiId = item._id || item.id
        const state = StateManager.getEmojiState(emojiId)

        // 处理分类名称显示
        const categoryId = item.category || 'unknown'
        const categoryName = item.categoryName || categoryNameMap[categoryId] || categoryId || '未分类'

        return {
          ...item,
          id: emojiId, // 确保有id字段
          category: categoryId,
          categoryName: categoryName, // 添加分类名称
          isLiked: state.isLiked,
          isCollected: state.isCollected,
          downloadTime: state.downloadTime,
          likesText: DataManager.formatNumber(item.likes || 0),
          collectionsText: DataManager.formatNumber(item.collections || 0),
          // 高亮标题（如果有的话）
          displayTitle: item.highlightedTitle || item.title
        }
      })

      // 更新数据
      const updateData = {
        hasSearched: true,
        'searchConfig.loading': false,
        'searchStats.keyword': keyword,
        'searchStats.searchTime': searchTime
      }

      if (isLoadMore) {
        // 加载更多：追加结果
        updateData.searchResults = this.data.searchResults.concat(formattedResults)
        updateData['searchConfig.currentPage'] = currentPage + 1
      } else {
        // 新搜索：替换结果
        updateData.searchResults = formattedResults
        updateData['searchConfig.currentPage'] = 1
      }

      // 更新分页状态
      if (result.hasMore !== undefined) {
        updateData['searchConfig.hasMore'] = result.hasMore
      } else {
        updateData['searchConfig.hasMore'] = formattedResults.length >= pageSize
      }

      // 更新总数
      if (result.total !== undefined) {
        updateData['searchStats.total'] = result.total
      } else {
        updateData['searchStats.total'] = formattedResults.length
      }

      this.setData(updateData)

      wx.hideLoading()

      // 显示搜索结果提示
      if (!isLoadMore) {
        if (formattedResults.length > 0) {
          const total = result.total || formattedResults.length
          wx.showToast({
            title: `找到${total}个结果`,
            icon: 'success',
            duration: 1500
          })
          console.log('✅ 搜索完成:', total, '个结果，耗时:', searchTime, 'ms')
        } else {
          wx.showToast({
            title: '未找到相关内容',
            icon: 'none'
          })
          console.log('⚠️ 搜索无结果')
        }
      } else {
        console.log('✅ 加载更多完成:', formattedResults.length, '个结果')
      }
    } catch (error) {
      console.error('❌ 搜索失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      })

      // 设置空结果
      this.setData({
        searchResults: [],
        hasSearched: true
      })
    }
  },

  onTagTap(e) {
    const tag = e.currentTarget.dataset.tag
    this.setData({
      searchKeyword: tag
    })
    this.performSearch(tag)
    this.saveSearchHistory(tag)
  },

  onHistoryTap(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword
    })
    this.performSearch(keyword)
  },

  onClearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory')
          this.setData({
            searchHistory: []
          })
          wx.showToast({
            title: '已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    })
  },

  // 点赞功能
  onToggleLike(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    const searchResults = this.data.searchResults.map(item => {
      if (item.id === id) {
        const newLiked = !item.isLiked
        const newLikes = newLiked ? item.likes + 1 : item.likes - 1
        return {
          ...item,
          isLiked: newLiked,
          likes: newLikes,
          likesText: this.formatNumber(newLikes)
        }
      }
      return item
    })

    this.setData({ searchResults })

    wx.showToast({
      title: searchResults.find(item => item.id === id).isLiked ? '点赞成功' : '取消点赞',
      icon: 'success'
    })
  },

  // 收藏功能
  onToggleCollect(e) {
    e.stopPropagation()
    const id = e.currentTarget.dataset.id
    const searchResults = this.data.searchResults.map(item => {
      if (item.id === id) {
        const newCollected = !item.isCollected
        const newCollections = newCollected ? item.collections + 1 : item.collections - 1
        return {
          ...item,
          isCollected: newCollected,
          collections: newCollections,
          collectionsText: this.formatNumber(newCollections)
        }
      }
      return item
    })

    this.setData({ searchResults })

    wx.showToast({
      title: searchResults.find(item => item.id === id).isCollected ? '收藏成功' : '取消收藏',
      icon: 'success'
    })
  },


  // 格式化数字显示
  formatNumber(num) {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },

  onShareAppMessage() {
    return {
      title: '超好玩的表情包搜索',
      path: '/pages/search/search'
    }
  },

  /**
   * 预加载搜索建议，提升搜索体验
   */
  preloadSearchSuggestions() {
    // 基于热门标签生成搜索建议
    const allEmojis = DataManager.getAllEmojiData()
    const titleWords = []

    allEmojis.forEach(emoji => {
      const words = emoji.title.split(/[\s，。！？、]/).filter(word => word.length > 0)
      titleWords.push(...words)
    })

    // 统计词频并生成建议
    const wordCount = {}
    titleWords.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1
    })

    // 按频率排序，取前20个作为搜索建议
    const suggestions = Object.entries(wordCount)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word]) => word)

    // 缓存搜索建议
    this.searchSuggestionCache = suggestions
    console.log('✅ 搜索建议预加载完成:', suggestions.slice(0, 10))
  },

  /**
   * 获取智能搜索建议
   */
  getSmartSuggestions(keyword) {
    if (!this.searchSuggestionCache) {
      this.preloadSearchSuggestions()
    }

    if (!keyword) return this.searchSuggestionCache?.slice(0, 8) || []

    // 基于输入关键词过滤建议
    return this.searchSuggestionCache?.filter(suggestion =>
      suggestion.includes(keyword) || keyword.includes(suggestion)
    ).slice(0, 8) || []
  },

  // ========== 状态管理相关方法 ==========

  /**
   * 表情包状态变更回调
   * @param {Object} data - 状态变更数据
   */
  onEmojiStateChange(data) {
    console.log('📄 搜索页接收到状态变更:', data)

    // 搜索页特定的状态变更处理逻辑
    if (data.type === 'like' || data.type === 'collect') {
      // 可以在这里添加搜索结果的实时更新逻辑
    }
  },

  // 添加状态操作辅助方法
  ...EmojiStateHelper,

  // ========== 分页加载相关方法 ==========

  /**
   * 上拉加载更多搜索结果
   */
  async onReachBottom() {
    if (!this.data.hasSearched ||
        !this.data.searchKeyword ||
        !this.data.searchConfig.hasMore ||
        this.data.searchConfig.loading) {
      return
    }

    console.log('📄 加载更多搜索结果')

    // 使用增强的搜索逻辑加载更多
    await this.performSearch(this.data.searchKeyword, true)
  },

  /**
   * 切换排序方式
   */
  onSortChange(e) {
    const sortBy = e.currentTarget.dataset.sort

    if (sortBy === this.data.searchConfig.sortBy) {
      return
    }

    // 找到对应的排序选项标签
    const sortOption = this.data.sortOptions.find(option => option.value === sortBy)
    const currentSortLabel = sortOption ? sortOption.label : '相关性'

    this.setData({
      'searchConfig.sortBy': sortBy,
      currentSortLabel: currentSortLabel,
      showSortMenu: false
    })

    // 重新搜索
    if (this.data.hasSearched && this.data.searchKeyword) {
      this.performSearch(this.data.searchKeyword)
    }
  },

  /**
   * 显示/隐藏排序菜单
   */
  toggleSortMenu() {
    this.setData({
      showSortMenu: !this.data.showSortMenu
    })
  },

  /**
   * 隐藏排序菜单
   */
  hideSortMenu() {
    this.setData({
      showSortMenu: false
    })
  },

  /**
   * 搜索建议点击
   */
  onSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion
    this.setData({
      searchKeyword: suggestion,
      searchFocus: false
    })
    this.performSearch(suggestion)
    this.saveSearchHistory(suggestion)
  },

  /**
   * 清空搜索结果
   */
  clearSearchResults() {
    this.setData({
      searchResults: [],
      hasSearched: false,
      'searchConfig.currentPage': 1,
      'searchConfig.hasMore': true,
      'searchStats.total': 0,
      'searchStats.keyword': '',
      'searchStats.searchTime': 0
    })
  },

  /**
   * 搜索输入实时建议
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    // 实时搜索建议（防抖）
    if (this.searchSuggestionTimer) {
      clearTimeout(this.searchSuggestionTimer)
    }

    this.searchSuggestionTimer = setTimeout(() => {
      if (keyword && keyword.length >= 2) {
        this.loadSearchSuggestions(keyword)
      }
    }, 300)
  },

  /**
   * 加载搜索建议
   */
  async loadSearchSuggestions(keyword) {
    try {
      // 这里可以调用云函数获取搜索建议
      // 暂时使用本地逻辑
      const suggestions = this.getSmartSuggestions(keyword)

      this.setData({
        searchSuggestions: suggestions
      })
    } catch (error) {
      console.error('❌ 加载搜索建议失败:', error)
    }
  },

  /**
   * 初始化搜索分页
   * @param {string} keyword - 搜索关键词
   */
  initSearchPagination(keyword) {
    if (this.paginationInstance) {
      this.paginationInstance.destroy()
    }

    this.initPagination({
      pageSize: 20,
      dataSource: 'emojis',
      searchKeyword: keyword,
      onDataLoad: (result) => {
        console.log('📄 搜索分页数据加载:', result.type, result.data.length, '个')

        // 更新搜索结果
        this.setData({
          searchResults: result.data,
          hasSearched: true
        })

        // 更新表情包状态
        if (this.updateEmojiListState) {
          this.updateEmojiListState()
        }
      },
      onError: (error) => {
        console.error('❌ 搜索分页失败:', error)
        wx.showToast({
          title: '搜索失败',
          icon: 'none'
        })
      }
    })
  },

  // 添加分页混入方法
  ...PaginationMixin
}

// 使用页面状态混入增强页面配置
Page(withPageState(pageConfig))