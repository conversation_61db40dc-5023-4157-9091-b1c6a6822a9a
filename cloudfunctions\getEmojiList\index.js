// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { category = 'all', page = 1, limit = 20 } = event
  
  try {
    let query = db.collection('emojis').where({
      status: 'published'
    })

    // 如果指定了分类，添加分类过滤
    if (category !== 'all') {
      query = query.where({
        category: category,
        status: 'published'
      })
    }

    // 分页查询
    const result = await query
      .orderBy('createTime', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get()

    // 为每个表情包获取分类名称
    const emojisWithCategoryName = await Promise.all(
      result.data.map(async (emoji) => {
        if (emoji.category) {
          try {
            const categoryResult = await db.collection('categories').doc(emoji.category).get()
            if (categoryResult.data) {
              emoji.categoryName = categoryResult.data.name
            } else {
              emoji.categoryName = '未分类'
            }
          } catch (error) {
            console.warn('获取分类名称失败:', error)
            emoji.categoryName = '未分类'
          }
        } else {
          emoji.categoryName = '未分类'
        }
        return emoji
      })
    )

    return {
      success: true,
      data: emojisWithCategoryName,
      total: emojisWithCategoryName.length
    }
  } catch (error) {
    console.error('获取表情包列表失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}