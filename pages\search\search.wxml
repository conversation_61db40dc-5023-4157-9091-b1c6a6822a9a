<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-box">
      <view class="search-input-container">
        <text class="search-icon">🔍</text>
        <input
          class="search-input"
          placeholder="搜索表情包、标签或分类..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
          focus="{{searchFocus}}"
        />
      </view>
      <button
        class="search-btn"
        bindtap="onSearchConfirm"
      >
        搜索
      </button>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <!-- 热门标签 -->
  <view class="hot-tags-section" wx:if="{{!searchKeyword}}">
    <text class="section-title">热门标签</text>
    <view class="tags-container">
      <view 
        class="tag-item"
        wx:for="{{hotTags}}" 
        wx:key="*this"
        bindtap="onTagTap"
        data-tag="{{item}}"
      >
        <text class="tag-text">#{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{searchKeyword}}">
    <!-- 搜索统计和排序 - 已隐藏 -->
    <view class="results-header" wx:if="{{false && hasSearched}}">
      <view class="results-info">
        <text class="results-title">搜索结果</text>
        <text class="results-count">
          共找到 {{searchStats.total || searchResults.length}} 个表情包
          <text class="search-time" wx:if="{{searchStats.searchTime}}">({{searchStats.searchTime}}ms)</text>
        </text>
      </view>

      <!-- 排序选择器 -->
      <view class="sort-container">
        <view class="sort-trigger" bindtap="toggleSortMenu">
          <text class="sort-text">
            {{currentSortLabel}}
          </text>
          <text class="sort-icon">{{showSortMenu ? '▲' : '▼'}}</text>
        </view>

        <!-- 排序菜单 -->
        <view class="sort-menu {{showSortMenu ? 'show' : ''}}" wx:if="{{showSortMenu}}">
          <view
            class="sort-option {{item.value === searchConfig.sortBy ? 'active' : ''}}"
            wx:for="{{sortOptions}}"
            wx:key="value"
            bindtap="onSortChange"
            data-sort="{{item.value}}"
          >
            <text class="sort-icon">{{item.icon}}</text>
            <text class="sort-label">{{item.label}}</text>
            <text class="sort-check" wx:if="{{item.value === searchConfig.sortBy}}">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 遮罩层 -->
    <view class="sort-mask {{showSortMenu ? 'show' : ''}}" bindtap="hideSortMenu"></view>

    <view class="emoji-list" wx:if="{{searchResults.length > 0}}">
      <view
        class="emoji-item"
        wx:for="{{searchResults}}"
        wx:key="id"
        bindtap="onEmojiTap"
        data-emoji="{{item}}"
      >
        <!-- 表情包图片 -->
        <view class="emoji-image-container">
          <image
            class="emoji-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
        </view>

        <!-- 表情包信息 -->
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <text class="emoji-category">{{item.categoryName || item.category}}</text>

          <!-- 标签区域 -->
          <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <view
              class="tag-item"
              wx:for="{{item.tags}}"
              wx:for-item="tag"
              wx:key="*this"
            >
              {{tag}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{searchResults.length === 0 && hasSearched}}">
      <text class="empty-icon">😅</text>
      <text class="empty-title">没有找到相关表情包</text>
      <text class="empty-desc">试试其他关键词吧</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!searchKeyword && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="section-title">搜索历史</text>
      <text class="clear-btn" bindtap="onClearHistory">清空</text>
    </view>
    <view class="history-list">
      <view 
        class="history-item"
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="onHistoryTap"
        data-keyword="{{item}}"
      >
        <text class="history-icon">🕐</text>
        <text class="history-text">{{item}}</text>
      </view>
    </view>
  </view>
  </view>
</view>