
// 在微信开发者工具控制台中运行此脚本来测试云函数

// 测试 dataAPI 云函数
wx.cloud.callFunction({
  name: 'dataAPI',
  data: {
    action: 'ping'
  }
}).then(res => {
  console.log('dataAPI 测试结果:', res)
}).catch(err => {
  console.error('dataAPI 测试失败:', err)
})

// 测试 syncAPI 云函数
wx.cloud.callFunction({
  name: 'syncAPI',
  data: {
    action: 'getVersions'
  }
}).then(res => {
  console.log('syncAPI 测试结果:', res)
}).catch(err => {
  console.error('syncAPI 测试失败:', err)
})

// 测试 login 云函数
wx.cloud.callFunction({
  name: 'login'
}).then(res => {
  console.log('login 测试结果:', res)
}).catch(err => {
  console.error('login 测试失败:', err)
})
