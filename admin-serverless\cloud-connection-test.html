<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云数据库连接诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 云数据库连接诊断</h1>
        
        <div class="test-item">
            <h3>1. SDK加载检查</h3>
            <span id="sdk-status" class="status info">检查中...</span>
            <div id="sdk-details"></div>
        </div>

        <div class="test-item">
            <h3>2. 云开发初始化</h3>
            <span id="init-status" class="status info">等待测试</span>
            <button onclick="testCloudInit()">测试初始化</button>
            <div id="init-details"></div>
        </div>

        <div class="test-item">
            <h3>3. 数据库连接测试</h3>
            <span id="db-status" class="status info">等待测试</span>
            <button onclick="testDatabaseConnection()">测试连接</button>
            <div id="db-details"></div>
        </div>

        <div class="test-item">
            <h3>4. 数据读取测试</h3>
            <span id="data-status" class="status info">等待测试</span>
            <button onclick="testDataRead()">测试读取</button>
            <div id="data-details"></div>
        </div>

        <div class="test-item">
            <h3>5. 实时监听测试</h3>
            <span id="watch-status" class="status info">等待测试</span>
            <button onclick="testRealTimeWatch()">测试监听</button>
            <div id="watch-details"></div>
        </div>

        <div class="test-item">
            <h3>📋 诊断日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js"></script>
    
    <script>
        // 云开发配置
        const CloudConfig = {
            env: 'cloud1-5g6pvnpl88dc0142',
            initialized: false
        };

        let tcbApp = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, className) {
            const element = document.getElementById(elementId);
            element.textContent = status;
            element.className = `status ${className}`;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 1. 检查SDK加载
        function checkSDK() {
            log('🔍 检查云开发SDK加载状态...');
            
            if (typeof tcb !== 'undefined') {
                updateStatus('sdk-status', '✅ SDK已加载', 'success');
                document.getElementById('sdk-details').innerHTML = `
                    <p>SDK版本: ${tcb.version || '未知'}</p>
                    <p>加载时间: ${new Date().toLocaleString()}</p>
                `;
                log('✅ 云开发SDK加载成功');
                return true;
            } else {
                updateStatus('sdk-status', '❌ SDK未加载', 'error');
                document.getElementById('sdk-details').innerHTML = `
                    <p style="color: red;">请检查网络连接或SDK地址</p>
                `;
                log('❌ 云开发SDK加载失败');
                return false;
            }
        }

        // 2. 测试云开发初始化
        async function testCloudInit() {
            log('🚀 开始测试云开发初始化...');
            updateStatus('init-status', '🔄 初始化中...', 'info');

            try {
                if (!checkSDK()) {
                    throw new Error('SDK未加载');
                }

                log(`🔧 初始化环境: ${CloudConfig.env}`);
                tcbApp = tcb.init({
                    env: CloudConfig.env
                });

                log('🔐 开始匿名登录...');
                const auth = tcbApp.auth();
                const loginResult = await auth.signInAnonymously();
                
                CloudConfig.initialized = true;
                updateStatus('init-status', '✅ 初始化成功', 'success');
                document.getElementById('init-details').innerHTML = `
                    <p>环境ID: ${CloudConfig.env}</p>
                    <p>登录状态: ${loginResult ? '已登录' : '未登录'}</p>
                    <p>初始化时间: ${new Date().toLocaleString()}</p>
                `;
                log('✅ 云开发初始化成功');
                return true;

            } catch (error) {
                updateStatus('init-status', '❌ 初始化失败', 'error');
                document.getElementById('init-details').innerHTML = `
                    <p style="color: red;">错误: ${error.message}</p>
                `;
                log(`❌ 云开发初始化失败: ${error.message}`);
                return false;
            }
        }

        // 3. 测试数据库连接
        async function testDatabaseConnection() {
            log('🔍 开始测试数据库连接...');
            updateStatus('db-status', '🔄 连接中...', 'info');

            try {
                if (!CloudConfig.initialized) {
                    const initSuccess = await testCloudInit();
                    if (!initSuccess) {
                        throw new Error('云开发未初始化');
                    }
                }

                const db = tcbApp.database();
                log('📊 测试数据库基本操作...');
                
                // 测试获取集合信息
                const testResult = await db.collection('categories').limit(1).get();
                
                updateStatus('db-status', '✅ 连接成功', 'success');
                document.getElementById('db-details').innerHTML = `
                    <p>数据库状态: 正常</p>
                    <p>测试集合: categories</p>
                    <p>测试结果: ${testResult.data ? '有数据' : '无数据'}</p>
                    <p>连接时间: ${new Date().toLocaleString()}</p>
                `;
                log('✅ 数据库连接测试成功');
                return true;

            } catch (error) {
                updateStatus('db-status', '❌ 连接失败', 'error');
                document.getElementById('db-details').innerHTML = `
                    <p style="color: red;">错误: ${error.message}</p>
                `;
                log(`❌ 数据库连接失败: ${error.message}`);
                return false;
            }
        }

        // 4. 测试数据读取
        async function testDataRead() {
            log('📖 开始测试数据读取...');
            updateStatus('data-status', '🔄 读取中...', 'info');

            try {
                if (!CloudConfig.initialized) {
                    const initSuccess = await testCloudInit();
                    if (!initSuccess) {
                        throw new Error('云开发未初始化');
                    }
                }

                const db = tcbApp.database();
                
                // 测试读取各个集合
                const [categories, emojis, banners] = await Promise.all([
                    db.collection('categories').get().catch(e => ({ data: [], error: e.message })),
                    db.collection('emojis').get().catch(e => ({ data: [], error: e.message })),
                    db.collection('banners').get().catch(e => ({ data: [], error: e.message }))
                ]);

                updateStatus('data-status', '✅ 读取成功', 'success');
                document.getElementById('data-details').innerHTML = `
                    <p>分类数据: ${categories.data ? categories.data.length : 0} 条</p>
                    <p>表情包数据: ${emojis.data ? emojis.data.length : 0} 条</p>
                    <p>横幅数据: ${banners.data ? banners.data.length : 0} 条</p>
                    <p>读取时间: ${new Date().toLocaleString()}</p>
                `;
                
                log(`✅ 数据读取成功 - 分类:${categories.data?.length || 0}, 表情包:${emojis.data?.length || 0}, 横幅:${banners.data?.length || 0}`);
                return true;

            } catch (error) {
                updateStatus('data-status', '❌ 读取失败', 'error');
                document.getElementById('data-details').innerHTML = `
                    <p style="color: red;">错误: ${error.message}</p>
                `;
                log(`❌ 数据读取失败: ${error.message}`);
                return false;
            }
        }

        // 5. 测试实时监听
        async function testRealTimeWatch() {
            log('📡 开始测试实时监听...');
            updateStatus('watch-status', '🔄 测试中...', 'info');

            try {
                if (!CloudConfig.initialized) {
                    const initSuccess = await testCloudInit();
                    if (!initSuccess) {
                        throw new Error('云开发未初始化');
                    }
                }

                const db = tcbApp.database();
                
                // 测试监听sync_notifications集合
                const watcher = db.collection('sync_notifications')
                    .orderBy('timestamp', 'desc')
                    .limit(10)
                    .watch({
                        onChange: (snapshot) => {
                            log(`📨 收到实时数据变更: ${snapshot.docs.length} 条记录`);
                            updateStatus('watch-status', '✅ 监听正常', 'success');
                        },
                        onError: (error) => {
                            log(`❌ 实时监听错误: ${error.message}`);
                            updateStatus('watch-status', '❌ 监听失败', 'error');
                        }
                    });

                // 5秒后关闭监听
                setTimeout(() => {
                    watcher.close();
                    log('⏹️ 实时监听测试结束');
                }, 5000);

                updateStatus('watch-status', '✅ 监听启动', 'success');
                document.getElementById('watch-details').innerHTML = `
                    <p>监听集合: sync_notifications</p>
                    <p>监听状态: 已启动</p>
                    <p>测试时长: 5秒</p>
                    <p>启动时间: ${new Date().toLocaleString()}</p>
                `;
                
                log('✅ 实时监听启动成功，将在5秒后自动关闭');
                return true;

            } catch (error) {
                updateStatus('watch-status', '❌ 监听失败', 'error');
                document.getElementById('watch-details').innerHTML = `
                    <p style="color: red;">错误: ${error.message}</p>
                `;
                log(`❌ 实时监听失败: ${error.message}`);
                return false;
            }
        }

        // 页面加载时自动检查SDK
        window.addEventListener('load', () => {
            setTimeout(checkSDK, 1000);
        });
    </script>
</body>
</html>
