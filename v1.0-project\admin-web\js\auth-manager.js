// Web管理后台 - JWT认证管理器
class AuthManager {
  constructor() {
    this.token = localStorage.getItem('admin_token');
    this.adminInfo = JSON.parse(localStorage.getItem('admin_info') || '{}');
    this.refreshTimer = null;
    this.cloudApp = null;
    
    console.log('🔐 AuthManager初始化', {
      hasToken: !!this.token,
      adminId: this.adminInfo.adminId
    });
  }

  // 初始化CloudBase
  async initCloudBase() {
    if (this.cloudApp) return this.cloudApp;

    try {
      // 确保CloudBase SDK已加载
      if (typeof cloudbase === 'undefined') {
        throw new Error('CloudBase SDK未加载');
      }

      this.cloudApp = cloudbase.init({
        env: 'cloud1-5g6pvnpl88dc0142'
      });

      console.log('✅ CloudBase初始化成功');
      return this.cloudApp;
    } catch (error) {
      console.error('❌ CloudBase初始化失败:', error);
      throw error;
    }
  }

  // 登录
  async login(username, password) {
    try {
      console.log(`🔐 尝试登录: ${username}`);

      // 输入验证
      if (!username || !password) {
        return {
          success: false,
          error: '用户名和密码不能为空'
        };
      }

      // 调用登录API
      const result = await this.callLoginAPI('login', {
        username: username.trim(),
        password: password
      });

      if (result.success) {
        // 保存认证信息
        this.token = result.data.token;
        this.adminInfo = result.data.adminInfo;
        
        // 持久化存储
        localStorage.setItem('admin_token', this.token);
        localStorage.setItem('admin_info', JSON.stringify(this.adminInfo));
        localStorage.setItem('login_time', new Date().toISOString());
        
        // 启动自动刷新
        this.startTokenRefresh();
        
        console.log('✅ 登录成功', {
          adminId: this.adminInfo.adminId,
          permissions: this.adminInfo.permissions
        });

        return { 
          success: true, 
          message: '登录成功',
          adminInfo: this.adminInfo
        };
      } else {
        console.warn('❌ 登录失败:', result.error);
        return { 
          success: false, 
          error: result.error || '登录失败'
        };
      }
    } catch (error) {
      console.error('❌ 登录异常:', error);
      return { 
        success: false, 
        error: '网络错误，请检查连接后重试'
      };
    }
  }

  // 获取认证头
  getAuthHeaders() {
    if (!this.token) {
      throw new Error('未登录，请先登录');
    }

    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json',
      'X-Admin-ID': this.adminInfo.adminId || 'unknown'
    };
  }

  // 检查登录状态
  isLoggedIn() {
    if (!this.token || !this.adminInfo.adminId) {
      return false;
    }

    // 检查令牌是否即将过期（提前1小时检查）
    const loginTime = localStorage.getItem('login_time');
    if (loginTime) {
      const elapsed = Date.now() - new Date(loginTime).getTime();
      const twentyThreeHours = 23 * 60 * 60 * 1000;
      
      if (elapsed > twentyThreeHours) {
        console.warn('⚠️ 令牌即将过期，需要刷新');
        this.refreshToken().catch(error => {
          console.error('自动刷新令牌失败:', error);
          this.logout();
        });
      }
    }

    return true;
  }

  // 验证令牌
  async validateToken() {
    if (!this.token) {
      return { success: false, error: '无令牌' };
    }

    try {
      const result = await this.callLoginAPI('validateToken', {
        token: this.token
      });

      if (!result.success) {
        // 令牌无效，清理本地状态
        this.clearAuthData();
      }

      return result;
    } catch (error) {
      console.error('❌ 令牌验证失败:', error);
      this.clearAuthData();
      return { success: false, error: '令牌验证失败' };
    }
  }

  // 启动令牌自动刷新
  startTokenRefresh() {
    // 清理现有定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // 在令牌过期前1小时刷新
    const refreshInterval = 23 * 60 * 60 * 1000; // 23小时
    
    this.refreshTimer = setInterval(async () => {
      try {
        console.log('🔄 自动刷新令牌...');
        await this.refreshToken();
      } catch (error) {
        console.error('❌ 自动刷新令牌失败:', error);
        this.logout();
      }
    }, refreshInterval);

    console.log('⏰ 令牌自动刷新已启动');
  }

  // 刷新令牌
  async refreshToken() {
    try {
      if (!this.token) {
        throw new Error('无令牌可刷新');
      }

      const result = await this.callLoginAPI('refreshToken', {
        token: this.token
      });

      if (result.success) {
        this.token = result.data.token;
        localStorage.setItem('admin_token', this.token);
        localStorage.setItem('login_time', new Date().toISOString());
        
        console.log('✅ 令牌刷新成功');
        return result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('❌ 令牌刷新失败:', error);
      throw error;
    }
  }

  // 登出
  async logout() {
    try {
      console.log('🚪 执行登出...');

      // 调用登出API
      if (this.token) {
        try {
          await this.callLoginAPI('logout', { token: this.token });
        } catch (error) {
          console.warn('登出API调用失败:', error);
        }
      }
    } catch (error) {
      console.warn('登出请求失败:', error);
    } finally {
      // 清理本地数据
      this.clearAuthData();
      
      // 清理定时器
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
      
      console.log('✅ 登出完成');
      
      // 跳转到登录页
      if (window.location.pathname !== '/login.html') {
        window.location.href = './login.html';
      }
    }
  }

  // 清理认证数据
  clearAuthData() {
    this.token = null;
    this.adminInfo = {};
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_info');
    localStorage.removeItem('login_time');
  }

  // 检查权限
  hasPermission(permission) {
    if (!this.adminInfo.permissions) {
      return false;
    }
    
    return this.adminInfo.permissions.includes(permission) || 
           this.adminInfo.permissions.includes('admin');
  }

  // 获取当前管理员信息
  getCurrentAdmin() {
    return {
      adminId: this.adminInfo.adminId,
      permissions: this.adminInfo.permissions || [],
      loginTime: this.adminInfo.loginTime,
      isLoggedIn: this.isLoggedIn()
    };
  }

  // 调用登录API
  async callLoginAPI(action, data = {}) {
    try {
      // 确保CloudBase已初始化
      if (!this.cloudApp) {
        await this.initCloudBase();
      }

      console.log(`📡 调用LoginAPI: ${action}`);

      const result = await this.cloudApp.callFunction({
        name: 'loginAPI',
        data: {
          action,
          ...data
        }
      });

      console.log(`📡 LoginAPI响应: ${action}`, result.result);
      return result.result;

    } catch (error) {
      console.error(`❌ LoginAPI调用失败: ${action}`, error);
      
      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || error.errCode === -1) {
        throw new Error('网络连接失败，请检查网络后重试');
      }
      
      // 处理云函数错误
      if (error.errCode) {
        throw new Error(`服务器错误: ${error.errMsg || error.message}`);
      }
      
      throw error;
    }
  }

  // 显示错误提示
  showError(message) {
    // 可以集成具体的UI提示组件
    console.error('🚨 认证错误:', message);
    
    // 简单的alert提示（可以替换为更好的UI组件）
    if (typeof window !== 'undefined') {
      alert(`认证错误: ${message}`);
    }
  }

  // 显示成功提示
  showSuccess(message) {
    console.log('✅ 认证成功:', message);
    
    // 简单的提示（可以替换为更好的UI组件）
    if (typeof window !== 'undefined' && window.showToast) {
      window.showToast(message, 'success');
    }
  }
}

// 全局认证管理器实例
window.authManager = new AuthManager();

// 页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', () => {
  // 如果不是登录页面，检查登录状态
  if (!window.location.pathname.includes('login.html')) {
    if (!window.authManager.isLoggedIn()) {
      console.log('🔒 未登录，跳转到登录页');
      window.location.href = './login.html';
    } else {
      console.log('✅ 已登录，管理员:', window.authManager.getCurrentAdmin());
    }
  }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AuthManager;
}
