/**
 * 环境配置管理
 * 支持开发、测试、生产环境的配置切换
 */

const EnvironmentConfig = {
  // 当前环境
  currentEnv: 'development', // development, testing, production

  // 环境配置
  environments: {
    development: {
      name: '开发环境',
      cloudEnv: 'cloud1-5g6pvnpl88dc0142',
      apiTimeout: 10000,
      cacheTimeout: 5 * 60 * 1000, // 5分钟
      enableDebug: true,
      enablePerformanceMonitor: true,
      enableErrorReport: false,
      maxRetries: 3,
      logLevel: 'debug',
      features: {
        lazyLoading: true,
        requestOptimization: true,
        imagePreload: true,
        offlineSupport: false
      }
    },

    testing: {
      name: '测试环境',
      cloudEnv: 'cloud1-5g6pvnpl88dc0142',
      apiTimeout: 8000,
      cacheTimeout: 10 * 60 * 1000, // 10分钟
      enableDebug: true,
      enablePerformanceMonitor: true,
      enableErrorReport: true,
      maxRetries: 2,
      logLevel: 'info',
      features: {
        lazyLoading: true,
        requestOptimization: true,
        imagePreload: true,
        offlineSupport: true
      }
    },

    production: {
      name: '生产环境',
      cloudEnv: 'cloud1-5g6pvnpl88dc0142',
      apiTimeout: 5000,
      cacheTimeout: 30 * 60 * 1000, // 30分钟
      enableDebug: false,
      enablePerformanceMonitor: true,
      enableErrorReport: true,
      maxRetries: 3,
      logLevel: 'error',
      features: {
        lazyLoading: true,
        requestOptimization: true,
        imagePreload: true,
        offlineSupport: true
      }
    }
  },

  /**
   * 初始化环境配置
   */
  init() {
    // 根据编译环境自动设置
    if (typeof __wxConfig !== 'undefined' && __wxConfig.envVersion) {
      switch (__wxConfig.envVersion) {
        case 'develop':
          this.currentEnv = 'development'
          break
        case 'trial':
          this.currentEnv = 'testing'
          break
        case 'release':
          this.currentEnv = 'production'
          break
      }
    }

    console.log(`🌍 环境配置初始化: ${this.getCurrentConfig().name}`)
  },

  /**
   * 获取当前环境配置
   * @returns {Object} 当前环境配置
   */
  getCurrentConfig() {
    return this.environments[this.currentEnv] || this.environments.development
  },

  /**
   * 获取配置项
   * @param {string} key - 配置键
   * @param {*} defaultValue - 默认值
   * @returns {*} 配置值
   */
  get(key, defaultValue = null) {
    const config = this.getCurrentConfig()
    const keys = key.split('.')
    let value = config

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return defaultValue
      }
    }

    return value
  },

  /**
   * 检查功能是否启用
   * @param {string} feature - 功能名称
   * @returns {boolean} 是否启用
   */
  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`, false)
  },

  /**
   * 获取云环境ID
   * @returns {string} 云环境ID
   */
  getCloudEnv() {
    // 优先使用配置中的云环境ID
    let cloudEnv = this.get('cloudEnv')

    // 如果配置为空，尝试从微信开发者工具获取
    if (!cloudEnv) {
      try {
        // 尝试从全局配置获取
        if (typeof __wxConfig !== 'undefined' && __wxConfig.envId) {
          cloudEnv = __wxConfig.envId
        }
      } catch (error) {
        console.warn('无法获取云环境ID:', error)
      }
    }

    // 如果仍然为空，使用默认值（但会在初始化时提示用户配置）
    if (!cloudEnv) {
      console.warn('⚠️ 云环境ID未配置，请在微信开发者工具中配置云开发环境')
      // 返回空字符串，让wx.cloud.init使用默认环境
      return ''
    }

    return cloudEnv
  },

  /**
   * 是否为生产环境
   * @returns {boolean} 是否为生产环境
   */
  isProduction() {
    return this.currentEnv === 'production'
  },

  /**
   * 是否为开发环境
   * @returns {boolean} 是否为开发环境
   */
  isDevelopment() {
    return this.currentEnv === 'development'
  },

  /**
   * 是否启用调试
   * @returns {boolean} 是否启用调试
   */
  isDebugEnabled() {
    return this.get('enableDebug', false)
  },

  /**
   * 获取日志级别
   * @returns {string} 日志级别
   */
  getLogLevel() {
    return this.get('logLevel', 'info')
  },

  /**
   * 获取API超时时间
   * @returns {number} 超时时间（毫秒）
   */
  getApiTimeout() {
    return this.get('apiTimeout', 10000)
  },

  /**
   * 获取缓存超时时间
   * @returns {number} 缓存超时时间（毫秒）
   */
  getCacheTimeout() {
    return this.get('cacheTimeout', 5 * 60 * 1000)
  },

  /**
   * 获取最大重试次数
   * @returns {number} 最大重试次数
   */
  getMaxRetries() {
    return this.get('maxRetries', 3)
  },

  /**
   * 设置环境（仅用于测试）
   * @param {string} env - 环境名称
   */
  setEnvironment(env) {
    if (this.environments[env]) {
      this.currentEnv = env
      console.log(`🔄 环境切换到: ${this.getCurrentConfig().name}`)
    } else {
      console.warn(`⚠️ 未知环境: ${env}`)
    }
  },

  /**
   * 获取环境信息
   * @returns {Object} 环境信息
   */
  getEnvironmentInfo() {
    const config = this.getCurrentConfig()
    return {
      environment: this.currentEnv,
      name: config.name,
      cloudEnv: config.cloudEnv,
      debugEnabled: config.enableDebug,
      logLevel: config.logLevel,
      features: config.features
    }
  },

  /**
   * 验证配置完整性
   * @returns {Object} 验证结果
   */
  validateConfig() {
    const config = this.getCurrentConfig()
    const requiredKeys = [
      'name', 'cloudEnv', 'apiTimeout', 'cacheTimeout',
      'enableDebug', 'maxRetries', 'logLevel', 'features'
    ]

    const missing = requiredKeys.filter(key => !(key in config))
    const isValid = missing.length === 0

    return {
      isValid,
      missing,
      config: config
    }
  }
}

module.exports = {
  EnvironmentConfig
}
