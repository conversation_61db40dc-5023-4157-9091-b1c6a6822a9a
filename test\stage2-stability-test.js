/**
 * 第二阶段稳定性测试
 * 测试内存管理、错误处理、数据同步等稳定性功能
 */

// 设置测试环境
process.env.NODE_ENV = 'test'

// 模拟微信小程序环境
global.wx = {
  request: (options) => {
    setTimeout(() => {
      if (Math.random() > 0.1) { // 90%成功率
        options.success && options.success({
          statusCode: 200,
          data: { success: true, data: 'test data' }
        })
      } else {
        options.fail && options.fail({ errMsg: 'network error' })
      }
    }, Math.random() * 100)
  },
  getNetworkType: (options) => {
    setTimeout(() => {
      options.success && options.success({ networkType: 'wifi' })
    }, 10)
  },
  onNetworkStatusChange: () => {},
  onAppHide: () => {},
  showToast: () => {},
  vibrateShort: () => {},
  getSystemInfo: (options) => {
    setTimeout(() => {
      options.success && options.success({
        platform: 'devtools',
        system: 'Windows 10'
      })
    }, 10)
  },
  setStorageSync: () => {},
  getStorageSync: () => null,
  onError: () => {},
  onUnhandledRejection: () => {}
}

// 模拟getApp
global.getApp = () => ({
  globalData: {
    userId: 'test_user_123',
    version: '2.0.0'
  }
})

// 模拟Page函数
global.Page = (config) => {
  return config
}

// 导入测试模块
const ResourceManager = require('../utils/resourceManager.js')
const GlobalErrorHandler = require('../utils/globalErrorHandler.js')
const NetworkOptimizer = require('../utils/networkOptimizer.js')
const { PerformanceMonitor } = require('../utils/performanceMonitor.js')
const { DataManager } = require('../utils/newDataManager.js')
const { UserActionSync } = require('../utils/userActionSync.js')

/**
 * 第二阶段稳定性测试套件
 */
class Stage2StabilityTest {
  constructor() {
    this.testResults = {
      memoryManagement: { passed: 0, failed: 0, tests: [] },
      errorHandling: { passed: 0, failed: 0, tests: [] },
      dataSync: { passed: 0, failed: 0, tests: [] },
      networkOptimization: { passed: 0, failed: 0, tests: [] },
      performance: { passed: 0, failed: 0, tests: [] }
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始第二阶段稳定性测试...\n')

    try {
      // 初始化所有模块
      await this.initializeModules()

      // 内存管理测试
      await this.testMemoryManagement()

      // 错误处理测试
      await this.testErrorHandling()

      // 数据同步测试
      await this.testDataSync()

      // 网络优化测试
      await this.testNetworkOptimization()

      // 性能监控测试
      await this.testPerformanceMonitoring()

      // 输出测试结果
      this.printTestResults()

    } catch (error) {
      console.error('❌ 测试运行失败:', error)
    }
  }

  /**
   * 初始化所有模块
   */
  async initializeModules() {
    console.log('🔄 初始化测试模块...')

    ResourceManager.init()
    GlobalErrorHandler.init()
    NetworkOptimizer.init()
    PerformanceMonitor.init()
    await DataManager.init()
    UserActionSync.init()

    console.log('✅ 模块初始化完成\n')
  }

  /**
   * 内存管理测试
   */
  async testMemoryManagement() {
    console.log('🧠 内存管理测试开始...')

    // 测试1: 资源管理器基本功能
    await this.runTest('memoryManagement', '资源管理器基本功能', async () => {
      const pageId = 'test_page_1'
      
      // 创建定时器
      const timerId = ResourceManager.createTimer(pageId, () => {}, 1000)
      
      // 创建观察器
      const observer = { disconnect: () => {} }
      const observerId = ResourceManager.createObserver(pageId, 'test', observer)
      
      // 检查统计信息
      const stats = ResourceManager.getResourceStats()
      
      if (stats.totalPages === 0 || stats.pages[pageId].timers === 0) {
        throw new Error('资源管理器统计信息不正确')
      }
      
      // 清理资源
      ResourceManager.cleanupPageResources(pageId)
      
      const statsAfter = ResourceManager.getResourceStats()
      if (statsAfter.pages[pageId]) {
        throw new Error('资源清理失败')
      }
      
      return true
    })

    // 测试2: 内存泄漏检测
    await this.runTest('memoryManagement', '内存泄漏检测', async () => {
      // 模拟内存泄漏场景
      for (let i = 0; i < 10; i++) {
        const pageId = `leak_page_${i}`
        ResourceManager.createTimer(pageId, () => {}, 1000)
        ResourceManager.createTimer(pageId, () => {}, 2000)
      }
      
      // 触发清理
      ResourceManager.cleanupNonEssentialResources()
      
      const stats = ResourceManager.getResourceStats()
      console.log('📊 资源统计:', stats)
      
      return true
    })

    // 测试3: 性能监控内存检测
    await this.runTest('memoryManagement', '性能监控内存检测', async () => {
      // 收集资源统计
      PerformanceMonitor.collectResourceStats()
      
      // 获取内存泄漏报告
      const report = PerformanceMonitor.getMemoryLeakReport()
      
      if (typeof report.totalLeaks !== 'number') {
        throw new Error('内存泄漏报告格式不正确')
      }
      
      return true
    })

    console.log('✅ 内存管理测试完成\n')
  }

  /**
   * 错误处理测试
   */
  async testErrorHandling() {
    console.log('🛡️ 错误处理测试开始...')

    // 测试1: 全局错误处理器
    await this.runTest('errorHandling', '全局错误处理器', async () => {
      // 模拟JavaScript错误
      GlobalErrorHandler.handleError(new Error('测试错误'), 'javascript')
      
      // 模拟网络错误
      GlobalErrorHandler.handleError({ errMsg: 'network timeout' }, 'network')
      
      // 检查错误统计
      const stats = GlobalErrorHandler.getErrorStats()
      
      if (stats.totalErrors < 2) {
        throw new Error('错误统计不正确')
      }
      
      return true
    })

    // 测试2: 错误恢复机制
    await this.runTest('errorHandling', '错误恢复机制', async () => {
      // 模拟可恢复的错误
      const errorInfo = {
        type: 'network',
        retryCount: 0,
        context: {
          retryCallback: () => console.log('重试回调执行')
        }
      }
      
      const canRecover = GlobalErrorHandler.canAutoRecover(errorInfo)
      
      if (!canRecover) {
        throw new Error('错误恢复检测失败')
      }
      
      return true
    })

    // 测试3: 错误提示管理
    await this.runTest('errorHandling', '错误提示管理', async () => {
      const ErrorToastManager = require('../utils/errorToastManager.js')
      
      // 显示不同类型的错误
      ErrorToastManager.showNetworkError('网络测试错误')
      ErrorToastManager.showApiError('API测试错误')
      ErrorToastManager.showUserError('用户测试错误')
      
      const status = ErrorToastManager.getStatus()
      
      if (!status.isShowing) {
        throw new Error('错误提示显示失败')
      }
      
      return true
    })

    console.log('✅ 错误处理测试完成\n')
  }

  /**
   * 数据同步测试
   */
  async testDataSync() {
    console.log('🔄 数据同步测试开始...')

    // 测试1: 用户操作同步
    await this.runTest('dataSync', '用户操作同步', async () => {
      // 添加同步操作
      UserActionSync.enqueueAction({
        type: 'like',
        emojiId: 'test_emoji_1',
        value: true,
        timestamp: Date.now()
      })
      
      UserActionSync.enqueueAction({
        type: 'collect',
        emojiId: 'test_emoji_2',
        value: true,
        timestamp: Date.now()
      })
      
      // 检查同步状态
      const status = UserActionSync.getSyncStatus()
      
      if (status.queueLength < 2) {
        throw new Error('同步队列长度不正确')
      }
      
      return true
    })

    // 测试2: 离线数据管理
    await this.runTest('dataSync', '离线数据管理', async () => {
      // 保存离线数据
      UserActionSync.saveOfflineData()
      
      // 加载离线数据
      UserActionSync.loadOfflineData()
      
      // 清理过期数据
      UserActionSync.cleanupOfflineData()
      
      return true
    })

    // 测试3: 数据版本控制
    await this.runTest('dataSync', '数据版本控制', async () => {
      // 模拟版本同步
      await UserActionSync.syncVersionControl()
      
      const status = UserActionSync.getSyncStatus()
      
      if (!status.dataVersion) {
        throw new Error('数据版本信息缺失')
      }
      
      return true
    })

    console.log('✅ 数据同步测试完成\n')
  }

  /**
   * 网络优化测试
   */
  async testNetworkOptimization() {
    console.log('🌐 网络优化测试开始...')

    // 测试1: 请求缓存
    await this.runTest('networkOptimization', '请求缓存', async () => {
      const options = {
        url: 'https://test.com/api/data',
        method: 'GET'
      }
      
      // 第一次请求
      await NetworkOptimizer.request(options)
      
      // 第二次请求应该使用缓存
      await NetworkOptimizer.request(options)
      
      const stats = NetworkOptimizer.getStats()
      
      if (stats.cacheSize === 0) {
        throw new Error('请求缓存未生效')
      }
      
      return true
    })

    // 测试2: 请求去重
    await this.runTest('networkOptimization', '请求去重', async () => {
      const options = {
        url: 'https://test.com/api/duplicate',
        method: 'GET'
      }
      
      // 同时发起多个相同请求
      const promises = [
        NetworkOptimizer.request(options),
        NetworkOptimizer.request(options),
        NetworkOptimizer.request(options)
      ]
      
      await Promise.all(promises)
      
      return true
    })

    // 测试3: 并发控制
    await this.runTest('networkOptimization', '并发控制', async () => {
      const requests = []
      
      // 发起大量请求
      for (let i = 0; i < 10; i++) {
        requests.push(NetworkOptimizer.request({
          url: `https://test.com/api/concurrent/${i}`,
          method: 'GET'
        }))
      }
      
      await Promise.all(requests)
      
      return true
    })

    console.log('✅ 网络优化测试完成\n')
  }

  /**
   * 性能监控测试
   */
  async testPerformanceMonitoring() {
    console.log('📊 性能监控测试开始...')

    // 测试1: 性能数据收集
    await this.runTest('performance', '性能数据收集', async () => {
      // 记录页面加载时间
      PerformanceMonitor.recordPageLoad('test_page', 1500)
      
      // 记录API调用时间
      PerformanceMonitor.recordApiCall('/api/test', 800, true)
      
      // 记录用户操作
      PerformanceMonitor.recordUserAction('click', 'button_test')
      
      const data = PerformanceMonitor.performanceData
      
      if (Object.keys(data.pageLoadTimes).length === 0) {
        throw new Error('性能数据收集失败')
      }
      
      return true
    })

    // 测试2: 性能报告生成
    await this.runTest('performance', '性能报告生成', async () => {
      const report = PerformanceMonitor.getPerformanceReport()
      
      if (!report.summary || !report.details || !report.recommendations) {
        throw new Error('性能报告格式不完整')
      }
      
      console.log('📋 性能报告摘要:', report.summary)
      
      return true
    })

    // 测试3: 内存泄漏检测
    await this.runTest('performance', '内存泄漏检测', async () => {
      // 启动内存泄漏检测
      PerformanceMonitor.startMemoryLeakDetection()
      
      // 模拟一些资源使用
      for (let i = 0; i < 5; i++) {
        PerformanceMonitor.collectResourceStats()
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      const leakReport = PerformanceMonitor.getMemoryLeakReport()
      
      if (typeof leakReport.totalLeaks !== 'number') {
        throw new Error('内存泄漏检测报告格式错误')
      }
      
      return true
    })

    console.log('✅ 性能监控测试完成\n')
  }

  /**
   * 运行单个测试
   */
  async runTest(category, testName, testFunction) {
    try {
      console.log(`  🧪 ${testName}...`)
      const result = await testFunction()
      
      if (result) {
        this.testResults[category].passed++
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' })
        console.log(`  ✅ ${testName} - 通过`)
      } else {
        throw new Error('测试返回false')
      }
    } catch (error) {
      this.testResults[category].failed++
      this.testResults[category].tests.push({ 
        name: testName, 
        status: 'FAILED', 
        error: error.message 
      })
      console.log(`  ❌ ${testName} - 失败: ${error.message}`)
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60))
    console.log('📊 第二阶段稳定性测试结果')
    console.log('='.repeat(60))

    let totalPassed = 0
    let totalFailed = 0

    for (const [category, results] of Object.entries(this.testResults)) {
      const categoryName = {
        memoryManagement: '内存管理',
        errorHandling: '错误处理',
        dataSync: '数据同步',
        networkOptimization: '网络优化',
        performance: '性能监控'
      }[category]

      console.log(`\n${categoryName}:`)
      console.log(`  通过: ${results.passed}`)
      console.log(`  失败: ${results.failed}`)
      
      totalPassed += results.passed
      totalFailed += results.failed

      if (results.failed > 0) {
        console.log('  失败的测试:')
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`    - ${test.name}: ${test.error}`)
          })
      }
    }

    console.log('\n' + '-'.repeat(60))
    console.log(`总计: ${totalPassed + totalFailed} 个测试`)
    console.log(`通过: ${totalPassed} 个`)
    console.log(`失败: ${totalFailed} 个`)
    console.log(`成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`)

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！第二阶段稳定性验证成功！')
    } else {
      console.log('\n⚠️ 部分测试失败，需要修复后重新测试')
    }

    console.log('='.repeat(60))
  }
}

// 运行测试
if (require.main === module) {
  const test = new Stage2StabilityTest()
  test.runAllTests().catch(console.error)
}

module.exports = Stage2StabilityTest
