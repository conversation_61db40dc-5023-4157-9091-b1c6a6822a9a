# 云开发部署指南

## 1. 云开发环境配置

### 1.1 创建云开发环境
1. 打开微信开发者工具
2. 在项目中点击"云开发"按钮
3. 创建新的云开发环境，记录环境ID
4. 将环境ID更新到 `app.js` 中的 `wx.cloud.init()` 配置

### 1.2 数据库设置
在云开发控制台中创建以下数据库集合：
- `users`: 用户数据集合
- `emojis`: 表情包数据集合（可选）
- `categories`: 分类数据集合（可选）

### 1.3 权限设置
在云开发控制台中，为 `users` 集合设置权限：
- 读权限：仅创建者可读
- 写权限：仅创建者可写

## 2. 云函数部署

### 2.1 必需的云函数
以下云函数需要部署：
- `getUserStats`: 获取用户统计数据
- `updateUserStats`: 更新用户统计数据
- `login`: 用户登录处理

### 2.2 部署步骤
1. 在微信开发者工具中右键点击 `cloudfunctions` 文件夹
2. 选择"当前环境"为您的云开发环境
3. 分别右键点击每个云函数文件夹，选择"上传并部署：云端安装依赖"

### 2.3 云函数依赖
确保每个云函数的 `package.json` 包含：
```json
{
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  }
}
```

## 3. 代码配置

### 3.1 云环境ID配置
在 `app.js` 中确保云环境ID正确：
```javascript
wx.cloud.init({
  env: 'your-cloud-env-id', // 替换为您的真实环境ID
  traceUser: true
})
```

### 3.2 功能特性
当前实现的功能：
- ✅ 用户登录状态管理
- ✅ 点赞/收藏数据云端同步
- ✅ 下载历史云端存储
- ✅ 本地缓存 + 云端备份
- ✅ 离线模式支持

## 4. 测试验证

### 4.1 功能测试
1. 编译并运行小程序
2. 测试用户登录功能
3. 测试点赞/收藏功能
4. 检查云开发控制台中的数据变化

### 4.2 错误排查
如果遇到云函数调用失败：
1. 检查云环境ID是否正确
2. 确认云函数已正确部署
3. 检查网络连接
4. 查看云函数日志

## 5. 生产环境注意事项

### 5.1 安全配置
- 设置合适的数据库权限
- 配置域名白名单
- 启用云函数访问控制

### 5.2 性能优化
- 使用数据库索引
- 实现请求缓存
- 优化云函数代码

### 5.3 监控告警
- 设置云函数调用量监控
- 配置数据库存储量告警
- 监控错误率和响应时间

## 6. 常见问题

### Q: 云函数调用失败，返回 -501000 错误
A: 检查云环境ID是否正确，确保云函数已部署成功

### Q: 数据同步不及时
A: 检查网络连接，确认云函数执行正常

### Q: 本地测试时无法连接云开发
A: 确保在微信开发者工具中正确配置了云开发环境

---

完成以上配置后，您的微信小程序就可以正常使用云开发功能了。