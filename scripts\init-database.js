/**
 * 数据库初始化脚本
 * 用于创建和配置 sync_notifications 集合
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 开始数据库初始化...')

// 检查是否在正确的项目目录
const projectConfigPath = path.join(process.cwd(), 'project.config.json')
if (!fs.existsSync(projectConfigPath)) {
  console.error('❌ 请在微信小程序项目根目录下运行此脚本')
  process.exit(1)
}

console.log('✅ 项目目录验证通过')

// 读取项目配置
let projectConfig
try {
  const configContent = fs.readFileSync(projectConfigPath, 'utf8')
  projectConfig = JSON.parse(configContent)
  console.log(`📋 项目名称: ${projectConfig.projectname || '未知'}`)
  console.log(`🆔 项目ID: ${projectConfig.appid || '未知'}`)
} catch (error) {
  console.error('❌ 读取项目配置失败:', error.message)
  process.exit(1)
}

// 检查云函数目录
const cloudFunctionsPath = path.join(process.cwd(), 'cloudfunctions')
if (!fs.existsSync(cloudFunctionsPath)) {
  console.error('❌ 未找到 cloudfunctions 目录')
  process.exit(1)
}

console.log('✅ 云函数目录验证通过')

// 创建临时云函数用于数据库初始化
const tempFunctionName = 'temp-db-init'
const tempFunctionPath = path.join(cloudFunctionsPath, tempFunctionName)

try {
  // 创建临时云函数目录
  if (!fs.existsSync(tempFunctionPath)) {
    fs.mkdirSync(tempFunctionPath, { recursive: true })
  }

  // 复制初始化代码
  const initScriptPath = path.join(process.cwd(), 'database', 'init-sync-notifications.js')
  const targetPath = path.join(tempFunctionPath, 'index.js')
  
  if (fs.existsSync(initScriptPath)) {
    fs.copyFileSync(initScriptPath, targetPath)
    console.log('✅ 初始化代码复制成功')
  } else {
    console.error('❌ 未找到初始化脚本:', initScriptPath)
    process.exit(1)
  }

  // 创建 package.json
  const packageJson = {
    "name": tempFunctionName,
    "version": "1.0.0",
    "description": "临时数据库初始化云函数",
    "main": "index.js",
    "dependencies": {
      "wx-server-sdk": "~2.6.3"
    }
  }

  fs.writeFileSync(
    path.join(tempFunctionPath, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  )

  console.log('✅ 临时云函数创建成功')
  console.log('')
  console.log('📋 接下来请手动执行以下步骤：')
  console.log('')
  console.log('1. 在微信开发者工具中：')
  console.log('   - 右键点击 cloudfunctions/' + tempFunctionName + ' 文件夹')
  console.log('   - 选择 "上传并部署：云端安装依赖"')
  console.log('')
  console.log('2. 部署完成后：')
  console.log('   - 右键点击 cloudfunctions/' + tempFunctionName + ' 文件夹')
  console.log('   - 选择 "云函数测试"')
  console.log('   - 点击 "调用" 按钮执行初始化')
  console.log('')
  console.log('3. 初始化完成后：')
  console.log('   - 可以删除 cloudfunctions/' + tempFunctionName + ' 文件夹')
  console.log('')
  console.log('🔍 或者，您也可以在云开发控制台中手动创建 sync_notifications 集合')

} catch (error) {
  console.error('❌ 创建临时云函数失败:', error.message)
  process.exit(1)
}

console.log('')
console.log('🎯 数据库初始化准备完成！')
