<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #007aff;
        }
        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #ff3b30;
        }
        .btn.danger:hover {
            background: #d70015;
        }
        .btn.success {
            background: #34c759;
        }
        .btn.success:hover {
            background: #248a3d;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库初始化测试工具</h1>
            <p>用于测试和初始化小程序数据库</p>
        </div>

        <div class="section">
            <h3>📋 快速操作</h3>
            <button class="btn success" onclick="initDatabase()">🚀 初始化数据库</button>
            <button class="btn" onclick="testConnection()">🔍 测试连接</button>
            <button class="btn" onclick="checkData()">📊 检查数据</button>
            <button class="btn danger" onclick="clearLogs()">🗑️ 清空日志</button>
        </div>

        <div class="section">
            <h3>📊 数据统计</h3>
            <div id="dataStats">
                <div class="status info">点击"检查数据"按钮获取统计信息</div>
            </div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="logContainer" class="log">等待操作...</div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let dataStats = document.getElementById('dataStats');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logContainer.textContent += logMessage;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(message);
        }

        function clearLogs() {
            logContainer.textContent = '日志已清空...\n';
        }

        function updateStats(stats) {
            dataStats.innerHTML = `
                <div class="status success">
                    <strong>📊 数据统计</strong><br>
                    分类数量: ${stats.categories || 0}<br>
                    表情包数量: ${stats.emojis || 0}<br>
                    横幅数量: ${stats.banners || 0}<br>
                    最后更新: ${new Date().toLocaleString()}
                </div>
            `;
        }

        async function testConnection() {
            log('🔍 开始测试连接...');
            
            try {
                // 这里应该调用小程序的云函数
                log('⚠️ 注意：这是网页版测试工具');
                log('📱 请在小程序开发者工具中运行以下代码：');
                log('wx.cloud.callFunction({ name: "dataAPI", data: { action: "ping" } })');
                log('✅ 或者使用小程序内置的测试功能');
                
            } catch (error) {
                log(`❌ 连接测试失败: ${error.message}`, 'error');
            }
        }

        async function initDatabase() {
            log('🚀 开始初始化数据库...');
            
            try {
                log('📋 准备创建以下数据：');
                log('  - 6个分类（搞笑幽默、可爱萌宠、情感表达、节日庆典、网络热梗、动漫二次元）');
                log('  - 示例表情包数据');
                log('  - 横幅轮播图数据');
                log('');
                log('📱 请在小程序开发者工具中运行以下代码：');
                log('wx.cloud.callFunction({');
                log('  name: "dataAPI",');
                log('  data: { action: "forceInitDatabase" }');
                log('}).then(res => console.log("初始化结果:", res))');
                log('');
                log('✅ 或者在小程序首页点击"强制初始化数据库"按钮');
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
            }
        }

        async function checkData() {
            log('📊 开始检查数据...');
            
            try {
                log('📱 请在小程序开发者工具中运行以下代码检查数据：');
                log('');
                log('// 检查分类数据');
                log('wx.cloud.callFunction({');
                log('  name: "dataAPI",');
                log('  data: { action: "getCategories" }');
                log('}).then(res => console.log("分类数据:", res))');
                log('');
                log('// 检查表情包数据');
                log('wx.cloud.callFunction({');
                log('  name: "dataAPI",');
                log('  data: { action: "getEmojis", data: { category: "all", page: 1, limit: 10 } }');
                log('}).then(res => console.log("表情包数据:", res))');
                log('');
                log('// 检查横幅数据');
                log('wx.cloud.callFunction({');
                log('  name: "dataAPI",');
                log('  data: { action: "getBanners" }');
                log('}).then(res => console.log("横幅数据:", res))');
                
                // 模拟统计数据
                updateStats({
                    categories: '请在小程序中检查',
                    emojis: '请在小程序中检查',
                    banners: '请在小程序中检查'
                });
                
            } catch (error) {
                log(`❌ 检查数据失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🌐 数据库测试工具已加载');
            log('💡 提示：这是网页版工具，实际操作需要在小程序开发者工具中进行');
            log('📱 建议直接在小程序中使用内置的测试和初始化功能');
        };
    </script>
</body>
</html>
