{"name": "emoji-admin-proxy", "version": "1.0.0", "description": "表情包管理后台代理服务器", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["emoji", "admin", "proxy", "cloudbase"], "author": "AI Assistant", "license": "MIT"}