<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单数据库权限测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🔍 数据库权限测试</h1>
    
    <div class="test-panel">
        <h2>📊 基础信息</h2>
        <div class="status info">
            <strong>环境ID:</strong> cloud1-5g6pvnpl88dc0142<br>
            <strong>测试目的:</strong> 检查数据库访问权限和安全规则
        </div>
    </div>

    <div class="test-panel">
        <h2>🚀 快速测试</h2>
        <button onclick="runFullTest()">运行完整测试</button>
        <div id="testResults"></div>
    </div>

    <div class="test-panel">
        <h2>📝 详细日志</h2>
        <div id="detailLog"></div>
    </div>

    <!-- 尝试多个CDN源 -->
    <script>
        // 动态加载SDK的函数
        function loadSDK() {
            return new Promise((resolve, reject) => {
                const cdnSources = [
                    'https://unpkg.com/@cloudbase/js-sdk@1.6.0/dist/index.umd.js',
                    'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@1.6.0/dist/index.umd.js',
                    'https://static.cloudbase.net/cloudbase-js-sdk/1.6.0/cloudbase.full.js'
                ];

                let currentIndex = 0;

                function tryNextCDN() {
                    if (currentIndex >= cdnSources.length) {
                        reject(new Error('所有CDN源都失败了'));
                        return;
                    }

                    const script = document.createElement('script');
                    script.src = cdnSources[currentIndex];

                    script.onload = function() {
                        console.log(`✅ SDK加载成功: ${cdnSources[currentIndex]}`);
                        // 等待一下确保SDK完全初始化
                        setTimeout(() => {
                            if (typeof window.cloudbase !== 'undefined') {
                                resolve(window.cloudbase);
                            } else {
                                console.log('SDK对象未找到，尝试下一个CDN...');
                                currentIndex++;
                                tryNextCDN();
                            }
                        }, 500);
                    };

                    script.onerror = function() {
                        console.log(`❌ CDN失败: ${cdnSources[currentIndex]}`);
                        currentIndex++;
                        tryNextCDN();
                    };

                    document.head.appendChild(script);
                }

                tryNextCDN();
            });
        }

        // 页面加载完成后加载SDK
        window.addEventListener('DOMContentLoaded', async function() {
            try {
                await loadSDK();
                console.log('SDK准备就绪');
            } catch (error) {
                console.error('SDK加载失败:', error);
            }
        });
    </script>
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        let tcbApp = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('detailLog');
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function runFullTest() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">正在运行测试...</div>';

            try {
                log('=== 开始完整测试 ===', 'info');

                // 等待SDK加载完成
                if (typeof window.cloudbase === 'undefined') {
                    log('等待SDK加载...', 'info');
                    await new Promise((resolve, reject) => {
                        let attempts = 0;
                        const maxAttempts = 20; // 最多等待10秒

                        const checkSDK = () => {
                            attempts++;
                            if (typeof window.cloudbase !== 'undefined') {
                                resolve();
                            } else if (attempts >= maxAttempts) {
                                reject(new Error('SDK加载超时'));
                            } else {
                                setTimeout(checkSDK, 500);
                            }
                        };

                        checkSDK();
                    });
                }
                
                // 步骤1: 初始化SDK
                log('步骤1: 初始化云开发SDK', 'info');

                // 详细检查SDK加载状态
                log(`window.cloudbase类型: ${typeof window.cloudbase}`, 'info');
                log(`window对象中的cloudbase相关属性: ${Object.keys(window).filter(k => k.includes('cloud')).join(', ')}`, 'info');

                if (typeof window.cloudbase === 'undefined') {
                    // 等待一下，可能SDK还在加载
                    log('SDK未加载，等待3秒后重试...', 'warning');
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    if (typeof window.cloudbase === 'undefined') {
                        throw new Error('CloudBase SDK未加载，请检查网络连接或CDN可用性');
                    }
                }
                
                tcbApp = window.cloudbase.init({
                    env: ENV_ID
                });

                // 检查SDK版本和可用方法
                log(`SDK版本信息: ${JSON.stringify(window.cloudbase.version || 'unknown')}`, 'info');

                db = tcbApp.database();
                log('✅ SDK初始化成功', 'success');
                
                // 步骤2: 身份认证
                log('步骤2: 执行匿名登录', 'info');
                const auth = tcbApp.auth();

                // 检查auth对象和可用方法
                log(`Auth对象类型: ${typeof auth}`, 'info');
                log(`Auth对象可用方法: ${Object.getOwnPropertyNames(auth).join(', ')}`, 'info');
                log(`Auth原型方法: ${Object.getOwnPropertyNames(Object.getPrototypeOf(auth)).join(', ')}`, 'info');
                log(`anonymousAuthProvider方法存在: ${typeof auth.anonymousAuthProvider}`, 'info');

                // 修复：先检查方法是否存在，然后正确调用
                if (typeof auth.anonymousAuthProvider === 'function') {
                    try {
                        log('正在执行匿名登录...', 'info');
                        const loginResult = await auth.anonymousAuthProvider().signIn();
                        log('✅ 匿名登录成功', 'success');
                        log(`登录结果: ${JSON.stringify(loginResult)}`, 'info');
                    } catch (authError) {
                        log(`⚠️ 匿名登录失败: ${authError.message}`, 'warning');
                        log('尝试继续测试...', 'info');
                    }
                } else {
                    log('❌ anonymousAuthProvider方法不存在，跳过登录', 'error');
                }

                // 步骤3: 数据库连接测试 (确保在初始化后)
                log('步骤3: 测试数据库连接', 'info');

                // 确保db已经初始化
                if (!db) {
                    db = tcbApp.database();
                    log('重新初始化数据库连接', 'info');
                }
                
                // 测试不同的集合
                const collections = ['emojis', 'categories', 'users', 'banners'];
                let successCount = 0;
                let totalTests = collections.length;
                
                for (const collectionName of collections) {
                    try {
                        log(`测试集合: ${collectionName}`, 'info');
                        const result = await db.collection(collectionName).limit(1).get();
                        
                        if (result && result.data) {
                            log(`✅ ${collectionName}: 查询成功，${result.data.length} 条记录`, 'success');
                            successCount++;
                        } else {
                            log(`⚠️ ${collectionName}: 查询成功但无数据`, 'warning');
                        }
                    } catch (error) {
                        log(`❌ ${collectionName}: ${error.message}`, 'error');
                        
                        // 详细错误分析
                        if (error.message.includes('PERMISSION_DENIED')) {
                            log(`   权限被拒绝 - 可能需要调整安全规则`, 'warning');
                        } else if (error.message.includes('not found')) {
                            log(`   集合不存在 - 需要先创建数据`, 'warning');
                        }
                    }
                }
                
                // 步骤4: 尝试写入测试（如果有权限）
                log('步骤4: 测试写入权限', 'info');
                try {
                    const testDoc = {
                        test: true,
                        timestamp: new Date(),
                        message: '权限测试'
                    };
                    
                    await db.collection('test').add(testDoc);
                    log('✅ 写入权限测试成功', 'success');
                    
                    // 清理测试数据
                    try {
                        await db.collection('test').where({ test: true }).remove();
                        log('✅ 测试数据清理成功', 'success');
                    } catch (cleanError) {
                        log(`⚠️ 测试数据清理失败: ${cleanError.message}`, 'warning');
                    }
                    
                } catch (writeError) {
                    log(`❌ 写入权限测试失败: ${writeError.message}`, 'error');
                }
                
                // 总结结果
                log('=== 测试完成 ===', 'info');
                const successRate = (successCount / totalTests * 100).toFixed(1);
                
                if (successCount === totalTests) {
                    resultsDiv.innerHTML = `
                        <div class="status success">
                            ✅ 测试完成！所有集合都可以访问 (${successCount}/${totalTests})
                        </div>
                    `;
                } else if (successCount > 0) {
                    resultsDiv.innerHTML = `
                        <div class="status warning">
                            ⚠️ 部分成功！${successCount}/${totalTests} 个集合可以访问 (${successRate}%)
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="status error">
                            ❌ 测试失败！无法访问任何集合，请检查数据库安全规则
                        </div>
                    `;
                }
                
            } catch (error) {
                log(`❌ 测试过程中出现严重错误: ${error.message}`, 'error');
                resultsDiv.innerHTML = `
                    <div class="status error">
                        ❌ 测试失败: ${error.message}
                    </div>
                `;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备测试', 'info');
            
            // 检查SDK是否加载
            if (typeof window.cloudbase !== 'undefined') {
                log('✅ CloudBase SDK已加载', 'success');
            } else {
                log('❌ CloudBase SDK未加载', 'error');
            }
        });
    </script>
</body>
</html>
