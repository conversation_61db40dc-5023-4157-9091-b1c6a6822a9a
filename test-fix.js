// 测试修复效果的脚本
// 在微信开发者工具控制台中运行

console.log('🧪 开始测试修复效果...')

// 检查应用状态
const app = getApp()
console.log('📊 应用状态检查:')
console.log('   云开发初始化:', app.globalData.cloudInitialized)
console.log('   数据库初始化:', app.globalData.databaseInitialized)
console.log('   核心服务初始化:', app.globalData.coreServicesInitialized)

// 等待5秒后测试数据访问
setTimeout(() => {
  console.log('\n🔍 测试数据访问...')
  
  // 测试云函数调用
  wx.cloud.callFunction({
    name: 'dataAPI',
    data: { action: 'ping' }
  }).then(res => {
    console.log('✅ 云函数调用成功:', res)
    
    // 测试数据库访问
    return wx.cloud.database().collection('categories').limit(1).get()
  }).then(res => {
    console.log('✅ 数据库访问成功:', res)
    console.log('🎉 修复验证通过！')
  }).catch(err => {
    console.error('❌ 测试失败:', err)
    
    if (err.errCode === -502005) {
      console.log('💡 数据库集合不存在，正在创建...')
      
      // 尝试创建基础数据
      wx.cloud.database().collection('categories').add({
        data: {
          id: 'test',
          name: '测试分类',
          status: 'active',
          createdAt: new Date()
        }
      }).then(() => {
        console.log('✅ 测试数据创建成功')
      }).catch(createErr => {
        console.error('❌ 创建测试数据失败:', createErr)
      })
    }
  })
}, 5000)

console.log('⏳ 等待5秒后开始测试...')
