/**
 * 收藏页面
 * 显示用户收藏的表情包，支持分类、搜索、批量操作等功能
 */

const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')
const { withPageState, EmojiStateHelper } = require('../../utils/pageStateMixin.js')
const ShareManager = require('../../utils/shareManager.js')

const pageConfig = {
  data: {
    // 收藏的表情包
    collectedEmojis: [],
    
    // 显示模式
    viewMode: 'grid', // grid, list
    gridColumns: 2,
    
    // 排序方式
    sortBy: 'collectTime', // collectTime, title, category, likes
    sortOrder: 'desc', // asc, desc
    
    // 筛选条件
    filterCategory: 'all',
    categories: [],
    
    // 搜索
    searchKeyword: '',
    searchResults: [],
    isSearching: false,
    
    // 选择模式
    selectionMode: false,
    selectedEmojis: [],
    
    // 加载状态
    loading: true,
    refreshing: false,
    
    // 统计信息
    stats: {
      total: 0,
      categories: {},
      recentCount: 0
    }
  },

  onLoad() {
    console.log('💖 收藏页面加载')
    this.loadCollectedEmojis()
    this.loadCategories()
  },

  onShow() {
    // 每次显示时刷新收藏状态
    this.refreshCollectionStatus()
  },

  onPullDownRefresh() {
    this.refreshCollectedEmojis()
  },

  /**
   * 加载收藏的表情包
   */
  async loadCollectedEmojis() {
    try {
      this.setData({ loading: true })
      
      // 获取所有表情包数据
      const allEmojis = await DataManager.getEmojis('all', 1, 1000)
      
      // 筛选出收藏的表情包
      const collectedEmojis = allEmojis.filter(emoji => {
        const state = StateManager.getEmojiState(emoji.id)
        return state.isCollected
      })
      
      // 添加收藏时间信息
      const enhancedEmojis = collectedEmojis.map(emoji => {
        const state = StateManager.getEmojiState(emoji.id)
        return {
          ...emoji,
          collectTime: state.collectTime || Date.now(),
          isLiked: state.isLiked,
          downloadTime: state.downloadTime
        }
      })
      
      // 排序
      this.sortEmojis(enhancedEmojis)
      
      // 计算统计信息
      const stats = this.calculateStats(enhancedEmojis)
      
      this.setData({
        collectedEmojis: enhancedEmojis,
        stats,
        loading: false
      })
      
      console.log('✅ 收藏表情包加载完成:', enhancedEmojis.length, '个')
    } catch (error) {
      console.error('❌ 加载收藏失败:', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  /**
   * 刷新收藏状态
   */
  refreshCollectionStatus() {
    const updatedEmojis = this.data.collectedEmojis.map(emoji => {
      const state = StateManager.getEmojiState(emoji.id)
      return {
        ...emoji,
        isLiked: state.isLiked,
        isCollected: state.isCollected
      }
    }).filter(emoji => emoji.isCollected) // 移除已取消收藏的
    
    if (updatedEmojis.length !== this.data.collectedEmojis.length) {
      const stats = this.calculateStats(updatedEmojis)
      this.setData({
        collectedEmojis: updatedEmojis,
        stats
      })
    }
  },

  /**
   * 刷新收藏列表
   */
  async refreshCollectedEmojis() {
    this.setData({ refreshing: true })
    await this.loadCollectedEmojis()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const categories = await DataManager.getCategories()
      this.setData({ categories })
    } catch (error) {
      console.error('❌ 加载分类失败:', error)
    }
  },

  /**
   * 排序表情包
   */
  sortEmojis(emojis) {
    const { sortBy, sortOrder } = this.data
    
    emojis.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'collectTime':
          comparison = (a.collectTime || 0) - (b.collectTime || 0)
          break
        case 'title':
          comparison = (a.title || '').localeCompare(b.title || '')
          break
        case 'category':
          comparison = (a.category || '').localeCompare(b.category || '')
          break
        case 'likes':
          comparison = (a.likes || 0) - (b.likes || 0)
          break
        default:
          comparison = 0
      }
      
      return sortOrder === 'desc' ? -comparison : comparison
    })
  },

  /**
   * 计算统计信息
   */
  calculateStats(emojis) {
    const stats = {
      total: emojis.length,
      categories: {},
      recentCount: 0
    }
    
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000
    
    emojis.forEach(emoji => {
      // 分类统计
      const category = emoji.category || '其他'
      stats.categories[category] = (stats.categories[category] || 0) + 1
      
      // 最近收藏统计
      if (emoji.collectTime && emoji.collectTime > oneWeekAgo) {
        stats.recentCount++
      }
    })
    
    return stats
  },

  /**
   * 切换显示模式
   */
  onViewModeChange(e) {
    const viewMode = e.currentTarget.dataset.mode
    this.setData({ viewMode })
  },

  /**
   * 切换网格列数
   */
  onGridColumnsChange(e) {
    const columns = parseInt(e.currentTarget.dataset.columns)
    this.setData({ gridColumns: columns })
  },

  /**
   * 排序方式改变
   */
  onSortChange(e) {
    const { sortBy, sortOrder } = e.currentTarget.dataset
    
    this.setData({ sortBy, sortOrder })
    
    // 重新排序
    const sortedEmojis = [...this.data.collectedEmojis]
    this.sortEmojis(sortedEmojis)
    this.setData({ collectedEmojis: sortedEmojis })
  },

  /**
   * 分类筛选
   */
  onCategoryFilter(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ filterCategory: category })
    this.applyFilter()
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    // 这里可以实现筛选逻辑
    // 暂时不实现，因为数据量不大
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    
    if (keyword.trim()) {
      this.performSearch(keyword)
    } else {
      this.setData({ 
        searchResults: [],
        isSearching: false 
      })
    }
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    const results = this.data.collectedEmojis.filter(emoji => {
      const title = emoji.title || ''
      const category = emoji.category || ''
      const tags = Array.isArray(emoji.tags) ? emoji.tags.join(' ') : ''
      
      const searchText = `${title} ${category} ${tags}`.toLowerCase()
      return searchText.includes(keyword.toLowerCase())
    })
    
    this.setData({
      searchResults: results,
      isSearching: true
    })
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      isSearching: false
    })
  },

  /**
   * 切换选择模式
   */
  onToggleSelectionMode() {
    const selectionMode = !this.data.selectionMode
    this.setData({
      selectionMode,
      selectedEmojis: selectionMode ? [] : this.data.selectedEmojis
    })
  },

  /**
   * 选择表情包
   */
  onSelectEmoji(e) {
    if (!this.data.selectionMode) return
    
    const emojiId = e.currentTarget.dataset.id
    const selectedEmojis = [...this.data.selectedEmojis]
    const index = selectedEmojis.indexOf(emojiId)
    
    if (index > -1) {
      selectedEmojis.splice(index, 1)
    } else {
      selectedEmojis.push(emojiId)
    }
    
    this.setData({ selectedEmojis })
  },

  /**
   * 全选/取消全选
   */
  onSelectAll() {
    const emojis = this.data.isSearching ? this.data.searchResults : this.data.collectedEmojis
    const allSelected = this.data.selectedEmojis.length === emojis.length
    
    this.setData({
      selectedEmojis: allSelected ? [] : emojis.map(e => e.id)
    })
  },

  /**
   * 批量取消收藏
   */
  onBatchUncollect() {
    if (this.data.selectedEmojis.length === 0) {
      wx.showToast({
        title: '请选择要取消收藏的表情包',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '确认取消收藏',
      content: `确定要取消收藏选中的 ${this.data.selectedEmojis.length} 个表情包吗？`,
      success: (res) => {
        if (res.confirm) {
          this.data.selectedEmojis.forEach(emojiId => {
            StateManager.toggleCollect(emojiId)
          })
          
          this.setData({
            selectionMode: false,
            selectedEmojis: []
          })
          
          this.refreshCollectionStatus()
          
          wx.showToast({
            title: '取消收藏成功',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 批量分享
   */
  async onBatchShare() {
    if (this.data.selectedEmojis.length === 0) {
      wx.showToast({
        title: '请选择要分享的表情包',
        icon: 'none'
      })
      return
    }
    
    try {
      const selectedEmojis = this.data.collectedEmojis.filter(emoji => 
        this.data.selectedEmojis.includes(emoji.id)
      )
      
      await ShareManager.shareCollection(selectedEmojis, {
        title: `我的${selectedEmojis.length}个精选表情包`
      })
      
      this.setData({
        selectionMode: false,
        selectedEmojis: []
      })
    } catch (error) {
      console.error('❌ 批量分享失败:', error)
      wx.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  },

  /**
   * 跳转到表情包详情
   */
  onEmojiTap(e) {
    if (this.data.selectionMode) {
      this.onSelectEmoji(e)
      return
    }

    const emojiId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emojiId}&from=collection`
    })
  },

  /**
   * 跳转到首页
   */
  onGoToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
}

// 使用页面状态混入增强页面配置
Page(withPageState(pageConfig))
