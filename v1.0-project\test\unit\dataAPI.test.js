// dataAPI 单元测试
const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');

// 模拟云开发SDK
jest.mock('wx-server-sdk', () => ({
  init: jest.fn(),
  database: jest.fn(() => ({
    collection: jest.fn(() => ({
      where: jest.fn(() => ({
        get: jest.fn(),
        count: jest.fn(),
        orderBy: jest.fn(() => ({
          get: jest.fn(),
          limit: jest.fn(() => ({
            get: jest.fn()
          })),
          skip: jest.fn(() => ({
            limit: jest.fn(() => ({
              get: jest.fn()
            }))
          }))
        }))
      })),
      orderBy: jest.fn(() => ({
        limit: jest.fn(() => ({
          get: jest.fn()
        }))
      })),
      limit: jest.fn(() => ({
        get: jest.fn()
      }))
    }))
  }))
}));

describe('dataAPI 单元测试', () => {
  let dataAPI;
  let mockDb;
  
  beforeEach(() => {
    jest.resetModules();
    dataAPI = require('../../cloudfunctions/dataAPI/index');
    
    const cloud = require('wx-server-sdk');
    mockDb = {
      collection: jest.fn(() => ({
        where: jest.fn(() => ({
          get: jest.fn(() => ({ data: [] })),
          count: jest.fn(() => ({ total: 0 })),
          orderBy: jest.fn(() => ({
            get: jest.fn(() => ({ data: [] })),
            limit: jest.fn(() => ({
              get: jest.fn(() => ({ data: [] }))
            })),
            skip: jest.fn(() => ({
              limit: jest.fn(() => ({
                get: jest.fn(() => ({ data: [] }))
              }))
            }))
          }))
        })),
        orderBy: jest.fn(() => ({
          limit: jest.fn(() => ({
            get: jest.fn(() => ({ data: [] }))
          }))
        }))
      }))
    };
    cloud.database.mockReturnValue(mockDb);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('T3.1 分类数据查询测试', () => {
    test('T3.1.1 获取分类列表应该成功', async () => {
      const mockCategories = [
        {
          id: 'cat_001',
          name: '搞笑',
          icon: 'https://example.com/icon1.png',
          description: '搞笑表情包',
          emojiCount: 50
        },
        {
          id: 'cat_002',
          name: '可爱',
          icon: 'https://example.com/icon2.png',
          description: '可爱表情包',
          emojiCount: 30
        }
      ];

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            orderBy: jest.fn(() => ({
              get: jest.fn(() => ({ data: mockCategories }))
            }))
          }))
        }))
      });

      const event = {
        action: 'getCategories'
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toHaveProperty('id');
      expect(result.data[0]).toHaveProperty('name');
      expect(result.data[0]).toHaveProperty('emojiCount');
    });

    test('T3.1.2 获取分类详情应该成功', async () => {
      const mockCategory = {
        id: 'cat_001',
        name: '搞笑',
        icon: 'https://example.com/icon.png',
        description: '搞笑表情包',
        emojiCount: 50
      };

      const mockEmojis = [
        {
          id: 'emoji_001',
          title: '哈哈',
          imageUrl: 'https://example.com/emoji1.png',
          tags: ['搞笑', '开心']
        }
      ];

      // 模拟分类查询
      const mockCategoryQuery = {
        where: jest.fn(() => ({
          get: jest.fn(() => ({ data: [mockCategory] }))
        }))
      };

      // 模拟表情包查询
      const mockEmojiQuery = {
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            limit: jest.fn(() => ({
              get: jest.fn(() => ({ data: mockEmojis }))
            }))
          }))
        }))
      };

      mockDb.collection
        .mockReturnValueOnce(mockCategoryQuery) // 第一次调用返回分类查询
        .mockReturnValueOnce(mockEmojiQuery);   // 第二次调用返回表情包查询

      const event = {
        action: 'getCategoryDetail',
        params: {
          categoryId: 'cat_001'
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('category');
      expect(result.data).toHaveProperty('emojis');
      expect(result.data.category.id).toBe('cat_001');
      expect(result.data.emojis).toHaveLength(1);
    });

    test('T3.1.3 获取不存在的分类详情应该失败', async () => {
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn(() => ({ data: [] })) // 空数组表示不存在
        }))
      });

      const event = {
        action: 'getCategoryDetail',
        params: {
          categoryId: 'nonexistent_cat'
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('分类不存在');
    });
  });

  describe('T3.2 表情包数据查询测试', () => {
    test('T3.2.1 获取表情包列表应该成功', async () => {
      const mockEmojis = [
        {
          id: 'emoji_001',
          title: '开心',
          imageUrl: 'https://example.com/emoji1.png',
          categoryId: 'cat_001',
          tags: ['开心', '笑脸'],
          likes: 100,
          downloads: 500
        }
      ];

      const mockQuery = {
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            skip: jest.fn(() => ({
              limit: jest.fn(() => ({
                get: jest.fn(() => ({ data: mockEmojis }))
              }))
            }))
          }))
        }))
      };

      const mockCountQuery = {
        where: jest.fn(() => ({
          count: jest.fn(() => ({ total: 1 }))
        }))
      };

      mockDb.collection
        .mockReturnValueOnce(mockQuery)     // 第一次调用返回数据查询
        .mockReturnValueOnce(mockCountQuery); // 第二次调用返回计数查询

      const event = {
        action: 'getEmojis',
        params: {
          page: 1,
          pageSize: 20
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('list');
      expect(result.data).toHaveProperty('pagination');
      expect(result.data.list).toHaveLength(1);
      expect(result.data.pagination.total).toBe(1);
    });

    test('T3.2.2 分页参数应该正确处理', async () => {
      const mockEmojis = [];
      let skipValue, limitValue;

      const mockQuery = {
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            skip: jest.fn((skip) => {
              skipValue = skip;
              return {
                limit: jest.fn((limit) => {
                  limitValue = limit;
                  return {
                    get: jest.fn(() => ({ data: mockEmojis }))
                  };
                })
              };
            })
          }))
        }))
      };

      mockDb.collection.mockReturnValue(mockQuery);

      const event = {
        action: 'getEmojis',
        params: {
          page: 3,
          pageSize: 10
        }
      };

      await dataAPI.main(event, {});

      expect(skipValue).toBe(20); // (3-1) * 10
      expect(limitValue).toBe(10);
    });

    test('T3.2.3 搜索表情包应该成功', async () => {
      const mockEmojis = [
        {
          id: 'emoji_001',
          title: '开心笑脸',
          tags: ['开心', '笑脸']
        }
      ];

      const mockQuery = {
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            skip: jest.fn(() => ({
              limit: jest.fn(() => ({
                get: jest.fn(() => ({ data: mockEmojis }))
              }))
            }))
          }))
        }))
      };

      const mockCountQuery = {
        where: jest.fn(() => ({
          count: jest.fn(() => ({ total: 1 }))
        }))
      };

      mockDb.collection
        .mockReturnValueOnce(mockQuery)
        .mockReturnValueOnce(mockCountQuery);

      const event = {
        action: 'searchEmojis',
        params: {
          keyword: '开心',
          page: 1,
          pageSize: 20
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('keyword');
      expect(result.data.keyword).toBe('开心');
      expect(result.data).toHaveProperty('list');
      expect(result.data).toHaveProperty('pagination');
    });

    test('T3.2.4 空关键词搜索应该失败', async () => {
      const event = {
        action: 'searchEmojis',
        params: {
          keyword: '',
          page: 1,
          pageSize: 20
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('搜索关键词不能为空');
    });

    test('T3.2.5 获取表情包详情应该成功', async () => {
      const mockEmoji = {
        _id: 'mock_id',
        id: 'emoji_001',
        title: '开心',
        imageUrl: 'https://example.com/emoji.png',
        likes: 100,
        downloads: 500,
        views: 1000
      };

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          get: jest.fn(() => ({ data: [mockEmoji] }))
        })),
        doc: jest.fn(() => ({
          update: jest.fn() // 模拟更新查看次数
        }))
      });

      const event = {
        action: 'getEmojiDetail',
        params: {
          emojiId: 'emoji_001'
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data.id).toBe('emoji_001');
      expect(result.data.views).toBe(1001); // 查看次数应该增加1
    });
  });

  describe('T3.3 横幅数据查询测试', () => {
    test('T3.3.1 获取横幅列表应该成功', async () => {
      const mockBanners = [
        {
          id: 'banner_001',
          title: '活动横幅',
          imageUrl: 'https://example.com/banner.png',
          linkUrl: 'https://example.com/activity',
          sort: 0
        }
      ];

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            orderBy: jest.fn(() => ({
              get: jest.fn(() => ({ data: mockBanners }))
            }))
          }))
        }))
      });

      const event = {
        action: 'getBanners'
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toHaveProperty('id');
      expect(result.data[0]).toHaveProperty('title');
      expect(result.data[0]).toHaveProperty('imageUrl');
    });
  });

  describe('T3.4 统计数据查询测试', () => {
    test('T3.4.1 获取统计数据应该成功', async () => {
      // 模拟各种统计查询
      const mockCountQueries = [
        { total: 10 }, // categories count
        { total: 500 }, // emojis count  
        { total: 3 },   // banners count
      ];

      const mockDataQueries = [
        { data: [{ likes: 100 }, { likes: 200 }] }, // total likes
        { data: [{ downloads: 500 }, { downloads: 800 }] } // total downloads
      ];

      let queryIndex = 0;
      mockDb.collection.mockImplementation(() => ({
        where: jest.fn(() => ({
          count: jest.fn(() => mockCountQueries[queryIndex++]),
          field: jest.fn(() => ({
            get: jest.fn(() => mockDataQueries[queryIndex++ - 3])
          }))
        }))
      }));

      const event = {
        action: 'getStats'
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('categories');
      expect(result.data).toHaveProperty('emojis');
      expect(result.data).toHaveProperty('banners');
      expect(result.data).toHaveProperty('totalLikes');
      expect(result.data).toHaveProperty('totalDownloads');
      expect(result.data.categories).toBe(10);
      expect(result.data.emojis).toBe(500);
      expect(result.data.totalLikes).toBe(300); // 100 + 200
      expect(result.data.totalDownloads).toBe(1300); // 500 + 800
    });
  });

  describe('T3.5 缓存功能测试', () => {
    test('T3.5.1 缓存命中应该返回缓存数据', async () => {
      // 第一次调用，数据库返回数据
      const mockCategories = [{ id: 'cat_001', name: '测试分类' }];
      
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            orderBy: jest.fn(() => ({
              get: jest.fn(() => ({ data: mockCategories }))
            }))
          }))
        }))
      });

      const event = {
        action: 'getCategories'
      };

      // 第一次调用
      const result1 = await dataAPI.main(event, {});
      expect(result1.success).toBe(true);
      expect(result1.fromCache).toBe(false);

      // 第二次调用应该从缓存返回
      const result2 = await dataAPI.main(event, {});
      expect(result2.success).toBe(true);
      expect(result2.fromCache).toBe(true);
      expect(result2.data).toEqual(result1.data);
    });

    test('T3.5.2 清理缓存应该成功', async () => {
      const event = {
        action: 'clearCache',
        params: {
          cacheKeys: ['categories_cache']
        }
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(true);
      expect(result.message).toBe('缓存清理完成');
    });
  });

  describe('T3.6 错误处理测试', () => {
    test('T3.6.1 未知操作应该返回错误', async () => {
      const event = {
        action: 'unknownAction'
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.error).toContain('未知的操作类型');
      expect(result.code).toBe('UNKNOWN_ACTION');
    });

    test('T3.6.2 数据库查询失败应该返回错误', async () => {
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            orderBy: jest.fn(() => ({
              get: jest.fn(() => {
                throw new Error('Database error');
              })
            }))
          }))
        }))
      });

      const event = {
        action: 'getCategories'
      };

      const result = await dataAPI.main(event, {});

      expect(result.success).toBe(false);
      expect(result.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('T3.7 性能测试', () => {
    test('T3.7.1 查询操作应该在合理时间内完成', async () => {
      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            orderBy: jest.fn(() => ({
              get: jest.fn(() => ({ data: [] }))
            }))
          }))
        }))
      });

      const startTime = Date.now();

      const event = {
        action: 'getCategories'
      };

      await dataAPI.main(event, {});

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    test('T3.7.2 分页查询应该限制返回数量', async () => {
      let limitValue;

      mockDb.collection.mockReturnValue({
        where: jest.fn(() => ({
          orderBy: jest.fn(() => ({
            skip: jest.fn(() => ({
              limit: jest.fn((limit) => {
                limitValue = limit;
                return {
                  get: jest.fn(() => ({ data: [] }))
                };
              })
            }))
          }))
        }))
      });

      const event = {
        action: 'getEmojis',
        params: {
          page: 1,
          pageSize: 100 // 超过最大限制
        }
      };

      await dataAPI.main(event, {});

      expect(limitValue).toBeLessThanOrEqual(50); // 应该被限制在50以内
    });
  });
});
