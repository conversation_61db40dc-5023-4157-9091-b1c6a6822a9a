<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS跨域问题解决指南</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 20px; background: #f8f9fa; border-left: 4px solid #007bff; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .highlight { background: #ffeaa7; padding: 2px 4px; border-radius: 3px; }
        img { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CORS跨域问题解决指南</h1>
        
        <div class="error">
            <h3>❌ 当前错误</h3>
            <p><strong>错误信息:</strong> cors permission denied, please check if in your client cloud1-5g6pvnpl88dc0142 domains</p>
            <p><strong>错误原因:</strong> 云开发环境没有配置当前域名的安全域名</p>
        </div>

        <div class="info">
            <h3>📋 问题分析</h3>
            <p>云开发为了安全考虑，只允许配置的安全域名访问云开发服务。当前您在本地文件（file://）或未配置的域名下访问，导致CORS跨域错误。</p>
        </div>

        <div class="step">
            <h3>🎯 解决方案1: 配置安全域名（推荐）</h3>
            
            <h4>步骤1: 登录腾讯云控制台</h4>
            <ol>
                <li>打开 <a href="https://console.cloud.tencent.com/tcb" target="_blank">腾讯云云开发控制台</a></li>
                <li>选择您的环境：<span class="highlight">cloud1-5g6pvnpl88dc0142</span></li>
            </ol>

            <h4>步骤2: 配置安全域名</h4>
            <ol>
                <li>在左侧菜单中找到 <strong>"环境设置"</strong> → <strong>"安全配置"</strong></li>
                <li>找到 <strong>"WEB安全域名"</strong> 配置项</li>
                <li>添加以下域名：</li>
            </ol>

            <div class="code-block">
localhost<br>
127.0.0.1<br>
file://<br>
*.localhost<br>
*.127.0.0.1
            </div>

            <div class="warning">
                <p><strong>注意:</strong> 如果您使用的是本地服务器，请添加对应的端口，例如：</p>
                <ul>
                    <li>localhost:3000</li>
                    <li>localhost:8080</li>
                    <li>127.0.0.1:3000</li>
                </ul>
            </div>

            <h4>步骤3: 保存配置</h4>
            <p>点击保存后，等待1-2分钟配置生效。</p>
        </div>

        <div class="step">
            <h3>🚀 解决方案2: 使用本地服务器</h3>
            
            <p>如果无法配置安全域名，可以启动一个本地HTTP服务器：</p>

            <h4>方法1: 使用Python（推荐）</h4>
            <div class="code-block">
# Python 3<br>
python -m http.server 8080<br><br>
# Python 2<br>
python -m SimpleHTTPServer 8080
            </div>
            <p>然后访问：<code>http://localhost:8080</code></p>

            <h4>方法2: 使用Node.js</h4>
            <div class="code-block">
# 安装http-server<br>
npm install -g http-server<br><br>
# 启动服务器<br>
http-server -p 8080
            </div>

            <h4>方法3: 使用Live Server（VS Code插件）</h4>
            <ol>
                <li>在VS Code中安装 "Live Server" 插件</li>
                <li>右键HTML文件，选择 "Open with Live Server"</li>
            </ol>
        </div>

        <div class="step">
            <h3>🧪 解决方案3: 临时测试方案</h3>
            
            <p>创建一个简化的测试页面，绕过CORS限制：</p>
            
            <button onclick="createTestPage()">创建测试页面</button>
            <div id="testPageResult"></div>
        </div>

        <div class="step">
            <h3>✅ 验证修复结果</h3>
            
            <p>配置完成后，请测试以下功能：</p>
            
            <button onclick="testCORSFix()">测试CORS修复</button>
            <div id="corsTestResult"></div>
        </div>

        <div class="step">
            <h3>📞 如果问题仍然存在</h3>
            
            <div class="warning">
                <h4>可能的其他原因：</h4>
                <ul>
                    <li><strong>环境ID错误:</strong> 请确认环境ID是否正确</li>
                    <li><strong>环境状态异常:</strong> 检查云开发环境是否正常运行</li>
                    <li><strong>网络问题:</strong> 检查网络连接是否正常</li>
                    <li><strong>浏览器缓存:</strong> 清除浏览器缓存后重试</li>
                </ul>
            </div>

            <h4>调试步骤：</h4>
            <ol>
                <li>打开浏览器开发者工具（F12）</li>
                <li>查看Console和Network标签页的错误信息</li>
                <li>检查是否有其他网络请求失败</li>
            </ol>
        </div>
    </div>

    <script>
        function createTestPage() {
            const resultDiv = document.getElementById('testPageResult');
            
            const testPageContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>云开发CORS测试</title>
</head>
<body>
    <h1>云开发CORS测试页面</h1>
    <button onclick="testCloudbase()">测试云开发连接</button>
    <div id="result"></div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.19.1/cloudbase.full.js"><\/script>
    <script>
        async function testCloudbase() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const app = cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142'
                });
                
                await app.auth().signInAnonymously();
                const db = app.database();
                const result = await db.collection('categories').limit(1).get();
                
                resultDiv.innerHTML = '<div style="color: green;">✅ 连接成功！数据条数: ' + result.data.length + '</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div style="color: red;">❌ 连接失败: ' + error.message + '</div>';
                console.error('详细错误:', error);
            }
        }
    <\/script>
</body>
</html>`;

            // 创建下载链接
            const blob = new Blob([testPageContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'cors-test.html';
            a.click();
            URL.revokeObjectURL(url);

            resultDiv.innerHTML = `
                <div class="success">
                    <h4>✅ 测试页面已创建</h4>
                    <p>文件名: cors-test.html</p>
                    <p>请将此文件放在HTTP服务器上运行，或配置安全域名后使用。</p>
                </div>
            `;
        }

        async function testCORSFix() {
            const resultDiv = document.getElementById('corsTestResult');
            resultDiv.innerHTML = '<p>正在测试CORS修复...</p>';

            try {
                // 检查当前协议
                const protocol = window.location.protocol;
                const hostname = window.location.hostname || 'localhost';
                const port = window.location.port;
                
                let currentDomain = hostname;
                if (port) {
                    currentDomain += ':' + port;
                }

                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>🔍 当前访问信息</h4>
                        <p><strong>协议:</strong> ${protocol}</p>
                        <p><strong>域名:</strong> ${currentDomain}</p>
                        <p><strong>完整地址:</strong> ${window.location.href}</p>
                        
                        <h4>📋 需要在云开发控制台配置的安全域名:</h4>
                        <div class="code-block">
                            ${protocol === 'file:' ? 'file://' : currentDomain}
                        </div>
                        
                        <h4>💡 建议:</h4>
                        ${protocol === 'file:' ? 
                            '<p style="color: orange;">⚠️ 当前使用file://协议，建议使用HTTP服务器访问</p>' : 
                            '<p style="color: green;">✅ 当前使用HTTP协议，请在安全域名中添加: ' + currentDomain + '</p>'
                        }
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 页面加载时自动显示当前访问信息
        window.addEventListener('load', function() {
            setTimeout(testCORSFix, 1000);
        });
    </script>
</body>
</html>
