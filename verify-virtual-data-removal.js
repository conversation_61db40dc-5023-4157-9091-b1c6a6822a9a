// 验证虚拟数据删除的Node.js脚本
const fs = require('fs');
const path = require('path');

console.log('🧹 验证虚拟数据删除...\n');

function checkFileContent() {
    const filePath = path.join(__dirname, 'admin-serverless', 'main.html');
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ 文件不存在:', filePath);
        return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查项目列表
    const checks = [
        {
            name: '模拟系统日志',
            pattern: /const mockLogs = \[/,
            shouldExist: false,
            description: '检查是否还有模拟日志数组'
        },
        {
            name: '真实日志查询',
            pattern: /CloudAPI\.database\.get\('admin_logs'/,
            shouldExist: true,
            description: '检查是否添加了真实日志查询'
        },
        {
            name: '模拟统计数据',
            pattern: /Math\.floor\(Math\.random\(\) \* 1000\) \+ 100/,
            shouldExist: false,
            description: '检查是否还有随机统计数据生成'
        },
        {
            name: '真实统计查询',
            pattern: /viewCount: banner\.viewCount \|\| 0/,
            shouldExist: true,
            description: '检查是否使用真实的统计数据'
        },
        {
            name: '模拟下载链接',
            pattern: /https:\/\/mock-cdn\.com\//,
            shouldExist: false,
            description: '检查是否还有模拟CDN链接'
        },
        {
            name: '真实下载API',
            pattern: /storage\.getDownloadURL\(fileID\)/,
            shouldExist: true,
            description: '检查是否使用真实的下载API'
        },
        {
            name: '随机图表数据',
            pattern: /Math\.floor\(Math\.random\(\) \* 20\) \+ 5/,
            shouldExist: false,
            description: '检查是否还有随机图表数据'
        },
        {
            name: '真实趋势数据',
            pattern: /generateTrendData: async function/,
            shouldExist: true,
            description: '检查趋势数据函数是否改为异步真实查询'
        }
    ];
    
    console.log('📋 检查结果:\n');
    
    let passedChecks = 0;
    let totalChecks = checks.length;
    
    checks.forEach((check, index) => {
        const found = check.pattern.test(content);
        const passed = found === check.shouldExist;
        
        const status = passed ? '✅' : '❌';
        const expectedText = check.shouldExist ? '应该存在' : '不应该存在';
        const actualText = found ? '存在' : '不存在';
        
        console.log(`${status} ${index + 1}. ${check.name}`);
        console.log(`   ${check.description}`);
        console.log(`   期望: ${expectedText}, 实际: ${actualText}`);
        
        if (passed) {
            passedChecks++;
        } else {
            console.log(`   ⚠️  需要修复: ${check.shouldExist ? '缺少' : '仍然存在'}`);
        }
        console.log('');
    });
    
    console.log(`📊 检查结果: ${passedChecks}/${totalChecks} 项通过\n`);
    
    if (passedChecks === totalChecks) {
        console.log('🎉 所有虚拟数据已成功删除并替换为真实数据源!');
        return true;
    } else {
        console.log('⚠️ 仍有部分虚拟数据需要处理');
        return false;
    }
}

// 检查特定的代码模式
function checkSpecificPatterns() {
    console.log('\n🔍 详细模式检查:\n');
    
    const filePath = path.join(__dirname, 'admin-serverless', 'main.html');
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否还有其他随机数生成
    const randomPatterns = content.match(/Math\.random\(\)/g);
    if (randomPatterns) {
        console.log(`⚠️  发现 ${randomPatterns.length} 处 Math.random() 调用`);
        console.log('   请检查这些是否为必要的随机数生成（如ID生成等）');
    } else {
        console.log('✅ 未发现 Math.random() 调用');
    }
    
    // 检查是否还有"模拟"相关的注释
    const mockComments = content.match(/\/\/.*模拟.*/g);
    if (mockComments && mockComments.length > 0) {
        console.log(`\n⚠️  发现 ${mockComments.length} 处"模拟"相关注释:`);
        mockComments.slice(0, 5).forEach((comment, index) => {
            console.log(`   ${index + 1}. ${comment.trim()}`);
        });
        if (mockComments.length > 5) {
            console.log(`   ... 还有 ${mockComments.length - 5} 处`);
        }
    } else {
        console.log('✅ 未发现"模拟"相关注释');
    }
    
    // 检查是否有真实的数据库查询
    const dbQueries = content.match(/CloudAPI\.database\.(get|add|update|delete)/g);
    if (dbQueries) {
        console.log(`\n✅ 发现 ${dbQueries.length} 处真实数据库操作`);
    } else {
        console.log('\n❌ 未发现真实数据库操作');
    }
}

// 生成修复报告
function generateReport() {
    console.log('\n📋 虚拟数据删除报告:\n');
    
    console.log('✅ 已完成的修复:');
    console.log('   1. 删除模拟系统日志，替换为真实数据库查询');
    console.log('   2. 删除模拟统计数据，使用真实的数据库统计');
    console.log('   3. 删除模拟文件下载链接，使用真实的云存储API');
    console.log('   4. 修改图表数据生成为异步真实查询');
    console.log('   5. 添加真实的日志记录功能');
    
    console.log('\n🎯 下一步测试建议:');
    console.log('   1. 在浏览器中访问 http://localhost:9001/main.html');
    console.log('   2. 测试系统日志功能是否显示真实数据');
    console.log('   3. 测试统计图表是否显示真实数据');
    console.log('   4. 测试文件下载功能是否正常工作');
    console.log('   5. 创建一些测试数据验证统计功能');
    
    console.log('\n💡 注意事项:');
    console.log('   - 如果数据库中没有历史数据，统计图表可能显示为空');
    console.log('   - 系统日志需要有操作记录才会显示内容');
    console.log('   - 文件下载需要云存储中有实际文件');
}

// 执行所有检查
console.log('🚀 开始验证虚拟数据删除...\n');

const success = checkFileContent();
checkSpecificPatterns();
generateReport();

if (success) {
    console.log('\n🎉 验证完成：虚拟数据删除成功！');
    process.exit(0);
} else {
    console.log('\n⚠️ 验证完成：仍需进一步修复');
    process.exit(1);
}
