// 诊断管理后台问题的详细脚本
const { chromium } = require('playwright');

async function diagnoseAdminIssues() {
    console.log('🔍 开始诊断管理后台问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        console.log(`🖥️ [${type.toUpperCase()}] ${text}`);
    });
    
    // 监听网络请求
    page.on('request', request => {
        if (request.url().includes('cloudbase') || request.url().includes('tcb')) {
            console.log(`🌐 [REQUEST] ${request.method()} ${request.url()}`);
        }
    });
    
    page.on('response', response => {
        if (response.url().includes('cloudbase') || response.url().includes('tcb')) {
            console.log(`🌐 [RESPONSE] ${response.status()} ${response.url()}`);
        }
    });
    
    page.on('requestfailed', request => {
        console.log(`❌ [FAILED] ${request.url()} - ${request.failure().errorText}`);
    });
    
    try {
        console.log('📍 步骤1: 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 等待页面加载
        await page.waitForTimeout(5000);
        
        console.log('\n📍 步骤2: 检查页面基本状态');
        
        // 检查页面标题和内容
        const title = await page.title();
        console.log('📄 页面标题:', title);
        
        // 检查是否有错误提示
        const errorElements = await page.locator('.error, .alert-danger, [class*="error"]').count();
        console.log('⚠️ 错误元素数量:', errorElements);
        
        console.log('\n📍 步骤3: 检查JavaScript环境');
        
        // 检查关键对象是否存在
        const jsStatus = await page.evaluate(() => {
            return {
                hasWindow: typeof window !== 'undefined',
                hasCloudbase: typeof window.cloudbase !== 'undefined',
                hasTcb: typeof window.tcb !== 'undefined',
                hasTcbApp: typeof window.tcbApp !== 'undefined',
                hasAdminApp: typeof AdminApp !== 'undefined',
                hasCloudAPI: typeof CloudAPI !== 'undefined',
                cloudConfigEnv: typeof CloudConfig !== 'undefined' ? CloudConfig.env : 'undefined',
                cloudConfigInitialized: typeof CloudConfig !== 'undefined' ? CloudConfig.initialized : 'undefined'
            };
        });
        
        console.log('🔧 JavaScript环境状态:', JSON.stringify(jsStatus, null, 2));
        
        console.log('\n📍 步骤4: 检查CloudBase初始化');
        
        // 等待CloudBase初始化
        await page.waitForTimeout(3000);
        
        const cloudbaseStatus = await page.evaluate(async () => {
            try {
                // 检查CloudAPI初始化状态
                if (typeof CloudAPI !== 'undefined' && CloudAPI.init) {
                    console.log('尝试初始化CloudAPI...');
                    await CloudAPI.init();
                    return {
                        initSuccess: true,
                        hasDatabase: typeof CloudAPI.database !== 'undefined',
                        initialized: CloudConfig.initialized
                    };
                } else {
                    return {
                        initSuccess: false,
                        error: 'CloudAPI未定义或没有init方法'
                    };
                }
            } catch (error) {
                return {
                    initSuccess: false,
                    error: error.message
                };
            }
        });
        
        console.log('☁️ CloudBase状态:', JSON.stringify(cloudbaseStatus, null, 2));
        
        console.log('\n📍 步骤5: 测试数据库连接');
        
        if (cloudbaseStatus.initSuccess) {
            const dbTestResult = await page.evaluate(async () => {
                try {
                    console.log('测试数据库连接...');
                    const result = await CloudAPI.database.get('categories');
                    return {
                        success: true,
                        dataLength: result.data ? result.data.length : 0,
                        result: result
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            });
            
            console.log('🗄️ 数据库测试结果:', JSON.stringify(dbTestResult, null, 2));
        }
        
        console.log('\n📍 步骤6: 检查AdminApp数据状态');
        
        const adminAppData = await page.evaluate(() => {
            if (typeof AdminApp !== 'undefined') {
                return {
                    hasData: typeof AdminApp.data !== 'undefined',
                    currentPage: AdminApp.data ? AdminApp.data.currentPage : 'undefined',
                    categoriesLength: AdminApp.data && AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                    emojisLength: AdminApp.data && AdminApp.data.emojis ? AdminApp.data.emojis.length : 0,
                    bannersLength: AdminApp.data && AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                    sampleCategory: AdminApp.data && AdminApp.data.categories && AdminApp.data.categories[0] ? AdminApp.data.categories[0] : null
                };
            } else {
                return { error: 'AdminApp未定义' };
            }
        });
        
        console.log('📊 AdminApp数据状态:', JSON.stringify(adminAppData, null, 2));
        
        console.log('\n📍 步骤7: 检查页面导航');
        
        // 查找所有可能的导航元素
        const navElements = await page.evaluate(() => {
            const elements = [];
            
            // 查找包含"分类"的元素
            const categoryElements = document.querySelectorAll('*');
            for (let el of categoryElements) {
                if (el.textContent && el.textContent.includes('分类')) {
                    elements.push({
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id,
                        textContent: el.textContent.trim().substring(0, 50)
                    });
                }
            }
            
            return elements.slice(0, 10); // 只返回前10个
        });
        
        console.log('🧭 导航元素:', JSON.stringify(navElements, null, 2));
        
        console.log('\n📍 步骤8: 尝试手动加载分类数据');
        
        const manualLoadResult = await page.evaluate(async () => {
            try {
                if (typeof loadCategories === 'function') {
                    console.log('调用loadCategories函数...');
                    await loadCategories();
                    return {
                        success: true,
                        categoriesAfterLoad: AdminApp.data.categories ? AdminApp.data.categories.length : 0
                    };
                } else {
                    return {
                        success: false,
                        error: 'loadCategories函数未定义'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📂 手动加载结果:', JSON.stringify(manualLoadResult, null, 2));
        
        // 最终截图
        await page.screenshot({ path: 'diagnosis-screenshot.png', fullPage: true });
        console.log('\n📸 诊断截图已保存: diagnosis-screenshot.png');
        
    } catch (error) {
        console.error('❌ 诊断过程中出错:', error);
        await page.screenshot({ path: 'diagnosis-error.png' });
    } finally {
        await browser.close();
    }
}

// 运行诊断
diagnoseAdminIssues().catch(console.error);
