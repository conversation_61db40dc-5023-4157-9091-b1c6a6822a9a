/* pages/debug/debug.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  color: white;
}

.btn.primary {
  background: #007bff;
}

.btn.secondary {
  background: #6c757d;
}

.btn.warning {
  background: #ffc107;
  color: #212529;
}

.btn.info {
  background: #17a2b8;
}

.log-container {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.log-header {
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #dee2e6;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.log-content {
  height: 800rpx;
  padding: 20rpx;
}

.log-item {
  padding: 10rpx 0;
  font-size: 24rpx;
  font-family: monospace;
  line-height: 1.4;
  border-bottom: 1rpx solid #f0f0f0;
  word-break: break-all;
}

.log-empty {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 100rpx 0;
}
