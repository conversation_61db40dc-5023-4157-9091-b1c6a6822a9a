// utils/cloudDiagnostic.js - 云开发环境诊断工具
// 用于诊断云开发配置和连接问题

const CloudDiagnostic = {
  /**
   * 检查云开发基础环境
   */
  async checkCloudEnvironment() {
    console.log('=== 开始检查云开发环境 ===')
    
    const results = {
      cloudSupport: false,
      cloudInit: false,
      envId: null,
      error: null
    }
    
    try {
      // 1. 检查云开发支持
      if (typeof wx.cloud === 'undefined') {
        results.error = '当前基础库不支持云开发，请升级到2.2.3或以上版本'
        console.error('❌', results.error)
        return results
      }
      
      results.cloudSupport = true
      console.log('✅ 云开发API支持正常')
      
      // 2. 检查云开发初始化
      try {
        // 尝试获取云环境信息
        const envInfo = wx.cloud.getCloudEnvInfo()
        results.cloudInit = true
        results.envId = envInfo?.env || 'unknown'
        console.log('✅ 云开发已初始化，环境ID:', results.envId)
      } catch (error) {
        results.error = '云开发未正确初始化: ' + error.message
        console.error('❌', results.error)
      }
      
    } catch (error) {
      results.error = '检查云开发环境失败: ' + error.message
      console.error('❌', results.error)
    }
    
    return results
  },
  
  /**
   * 测试云函数连接
   */
  async testCloudFunction(functionName = 'login') {
    console.log(`=== 测试云函数 ${functionName} ===`)
    
    const results = {
      exists: false,
      callable: false,
      response: null,
      error: null,
      responseTime: 0
    }
    
    try {
      const startTime = Date.now()
      
      // 尝试调用云函数
      const res = await wx.cloud.callFunction({
        name: functionName,
        data: {
          test: true,
          timestamp: Date.now()
        }
      })
      
      results.responseTime = Date.now() - startTime
      results.exists = true
      results.callable = true
      results.response = res.result
      
      console.log(`✅ 云函数 ${functionName} 调用成功`)
      console.log('   响应时间:', results.responseTime + 'ms')
      console.log('   响应内容:', results.response)
      
    } catch (error) {
      results.error = error.message || error.errMsg || '未知错误'
      
      // 分析错误类型
      if (results.error.includes('cloud function not found')) {
        console.error(`❌ 云函数 ${functionName} 不存在或未部署`)
      } else if (results.error.includes('timeout')) {
        console.error(`❌ 云函数 ${functionName} 调用超时`)
      } else if (results.error.includes('permission')) {
        console.error(`❌ 云函数 ${functionName} 权限不足`)
      } else {
        console.error(`❌ 云函数 ${functionName} 调用失败:`, results.error)
      }
    }
    
    return results
  },
  
  /**
   * 测试数据库连接
   */
  async testDatabase() {
    console.log('=== 测试数据库连接 ===')
    
    const results = {
      accessible: false,
      collections: [],
      error: null
    }
    
    try {
      const db = wx.cloud.database()
      
      // 尝试获取数据库信息（通过查询一个可能不存在的集合）
      const testResult = await db.collection('test_connection').limit(1).get()
      
      results.accessible = true
      console.log('✅ 数据库连接正常')
      
      // 尝试检查必要的集合是否存在
      const requiredCollections = ['users', 'user_likes', 'user_collections', 'user_downloads']
      
      for (const collectionName of requiredCollections) {
        try {
          await db.collection(collectionName).limit(1).get()
          results.collections.push({ name: collectionName, exists: true })
          console.log(`✅ 集合 ${collectionName} 存在`)
        } catch (error) {
          results.collections.push({ name: collectionName, exists: false, error: error.message })
          console.log(`⚠️  集合 ${collectionName} 不存在或无权限访问`)
        }
      }
      
    } catch (error) {
      results.error = error.message || error.errMsg || '未知错误'
      console.error('❌ 数据库连接失败:', results.error)
    }
    
    return results
  },
  
  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    console.log('=== 测试网络连接 ===')
    
    const results = {
      networkType: 'unknown',
      isConnected: false,
      error: null
    }
    
    try {
      const networkInfo = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        })
      })
      
      results.networkType = networkInfo.networkType
      results.isConnected = networkInfo.networkType !== 'none'
      
      if (results.isConnected) {
        console.log('✅ 网络连接正常，类型:', results.networkType)
      } else {
        console.log('❌ 网络未连接')
      }
      
    } catch (error) {
      results.error = error.message || error.errMsg || '未知错误'
      console.error('❌ 获取网络状态失败:', results.error)
    }
    
    return results
  },
  
  /**
   * 运行完整诊断
   */
  async runFullDiagnostic() {
    console.log('🔍 开始运行云开发环境完整诊断')
    console.log('=====================================')
    
    const diagnosticResults = {
      timestamp: new Date().toISOString(),
      environment: {},
      cloudFunction: {},
      database: {},
      network: {},
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      }
    }
    
    try {
      // 1. 检查云开发环境
      diagnosticResults.environment = await this.checkCloudEnvironment()
      
      // 2. 测试网络连接
      diagnosticResults.network = await this.testNetworkConnection()
      
      // 3. 测试云函数
      diagnosticResults.cloudFunction = await this.testCloudFunction('login')
      
      // 4. 测试数据库
      diagnosticResults.database = await this.testDatabase()
      
      // 5. 生成诊断摘要
      this.generateSummary(diagnosticResults)
      
      console.log('=====================================')
      console.log('🎉 诊断完成，结果摘要:')
      console.log('   通过项目:', diagnosticResults.summary.passed)
      console.log('   失败项目:', diagnosticResults.summary.failed)
      console.log('   警告项目:', diagnosticResults.summary.warnings)
      
      return diagnosticResults
      
    } catch (error) {
      console.error('❌ 诊断过程异常:', error)
      diagnosticResults.error = error.message
      return diagnosticResults
    }
  },
  
  /**
   * 生成诊断摘要
   */
  generateSummary(results) {
    let passed = 0, failed = 0, warnings = 0
    
    // 检查云开发环境
    if (results.environment.cloudSupport && results.environment.cloudInit) {
      passed++
    } else {
      failed++
    }
    
    // 检查网络连接
    if (results.network.isConnected) {
      passed++
    } else {
      failed++
    }
    
    // 检查云函数
    if (results.cloudFunction.callable) {
      passed++
    } else {
      failed++
    }
    
    // 检查数据库
    if (results.database.accessible) {
      passed++
      
      // 检查集合
      const existingCollections = results.database.collections.filter(c => c.exists).length
      const totalCollections = results.database.collections.length
      
      if (existingCollections === totalCollections) {
        passed++
      } else if (existingCollections > 0) {
        warnings++
      } else {
        failed++
      }
    } else {
      failed++
    }
    
    results.summary = { passed, failed, warnings }
  },
  
  /**
   * 获取修复建议
   */
  getFixSuggestions(diagnosticResults) {
    const suggestions = []
    
    // 云开发环境问题
    if (!diagnosticResults.environment.cloudSupport) {
      suggestions.push({
        type: 'error',
        title: '升级基础库版本',
        description: '请在微信开发者工具中升级基础库到2.2.3或以上版本'
      })
    }
    
    if (!diagnosticResults.environment.cloudInit) {
      suggestions.push({
        type: 'error',
        title: '初始化云开发',
        description: '请检查app.js中的wx.cloud.init()配置，确保云环境ID正确'
      })
    }
    
    // 网络连接问题
    if (!diagnosticResults.network.isConnected) {
      suggestions.push({
        type: 'error',
        title: '检查网络连接',
        description: '请确保设备已连接到互联网'
      })
    }
    
    // 云函数问题
    if (!diagnosticResults.cloudFunction.callable) {
      suggestions.push({
        type: 'error',
        title: '部署云函数',
        description: '请在微信开发者工具中右键点击cloudfunctions/login文件夹，选择"上传并部署：云端安装依赖"'
      })
    }
    
    // 数据库问题
    if (!diagnosticResults.database.accessible) {
      suggestions.push({
        type: 'error',
        title: '检查数据库权限',
        description: '请在云开发控制台检查数据库权限配置'
      })
    }
    
    // 数据库集合问题
    const missingCollections = diagnosticResults.database.collections?.filter(c => !c.exists) || []
    if (missingCollections.length > 0) {
      suggestions.push({
        type: 'warning',
        title: '创建数据库集合',
        description: `请在云开发控制台创建以下集合: ${missingCollections.map(c => c.name).join(', ')}`
      })
    }
    
    return suggestions
  }
}

module.exports = {
  CloudDiagnostic
}
