// 简单的分类保存测试
const { chromium } = require('playwright');

async function simpleCategorySaveTest() {
    console.log('💾 简单的分类保存测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 2000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('保存分类数据') || text.includes('分类添加成功') || text.includes('ERROR')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 点击添加分类按钮');
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(5000); // 等待更长时间确保弹窗完全加载
        }
        
        console.log('\n📍 检查弹窗和表单');
        
        // 详细检查弹窗结构
        const modalCheck = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };
            
            const form = modal.querySelector('#add-category-form');
            const nameInput = modal.querySelector('#category-name');
            const iconInput = modal.querySelector('#category-icon');
            const gradientInput = modal.querySelector('#category-gradient');
            const submitBtn = modal.querySelector('button[type="submit"]');
            
            return {
                modalExists: true,
                formExists: !!form,
                formId: form ? form.id : null,
                nameInputExists: !!nameInput,
                iconInputExists: !!iconInput,
                gradientInputExists: !!gradientInput,
                submitBtnExists: !!submitBtn,
                submitBtnText: submitBtn ? submitBtn.textContent : null,
                modalHTML: modal.innerHTML.substring(0, 500)
            };
        });
        
        console.log('📊 弹窗检查结果:');
        console.log('弹窗存在:', modalCheck.modalExists);
        console.log('表单存在:', modalCheck.formExists);
        console.log('表单ID:', modalCheck.formId);
        console.log('名称输入框存在:', modalCheck.nameInputExists);
        console.log('图标输入框存在:', modalCheck.iconInputExists);
        console.log('渐变输入框存在:', modalCheck.gradientInputExists);
        console.log('提交按钮存在:', modalCheck.submitBtnExists);
        console.log('提交按钮文本:', modalCheck.submitBtnText);
        
        if (!modalCheck.formExists) {
            console.log('❌ 表单不存在，无法继续测试');
            console.log('弹窗HTML预览:', modalCheck.modalHTML);
            return { success: false, error: '表单不存在' };
        }
        
        console.log('\n📍 填写表单');
        
        // 填写表单
        await page.fill('#category-name', '渐变保存测试');
        console.log('✅ 已填写分类名称');

        // 图标输入框是只读的，需要用JavaScript设置
        await page.evaluate(() => {
            const iconInput = document.querySelector('#category-icon');
            if (iconInput) {
                iconInput.value = '🎯';
                iconInput.dispatchEvent(new Event('input', { bubbles: true }));
                iconInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
        console.log('✅ 已设置分类图标');
        
        await page.fill('#category-description', '测试渐变色保存功能');
        console.log('✅ 已填写分类描述');

        // 填写排序值（必填字段）
        await page.fill('#category-sort', '10');
        console.log('✅ 已填写排序值');

        // 选择渐变
        await page.selectOption('#category-gradient-preset', 'linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%)'); // 红粉渐变
        console.log('✅ 已选择红粉渐变');
        
        await page.waitForTimeout(2000);
        
        // 检查渐变输入框的值
        const gradientValue = await page.inputValue('#category-gradient');
        console.log('渐变输入框值:', gradientValue);
        
        console.log('\n📍 提交表单');

        // 检查提交按钮状态
        const buttonCheck = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            return {
                modalExists: !!modal,
                exists: !!submitBtn,
                visible: submitBtn ? submitBtn.offsetWidth > 0 && submitBtn.offsetHeight > 0 : false,
                text: submitBtn ? submitBtn.textContent?.trim() : null,
                disabled: submitBtn ? submitBtn.disabled : null,
                style: submitBtn ? submitBtn.style.cssText : null
            };
        });

        console.log('提交按钮状态:', buttonCheck);

        // 点击提交按钮（确保选择弹窗内的按钮）
        const submitBtn = await page.locator('[style*="position: fixed"] button[type="submit"]').first();
        if (await submitBtn.isVisible()) {
            await submitBtn.click();
            console.log('✅ 已点击提交按钮');
            
            // 等待保存完成
            await page.waitForTimeout(10000);
            
            // 检查是否有成功通知
            const notification = await page.evaluate(() => {
                const notifications = Array.from(document.querySelectorAll('.notification, .alert, .toast'));
                return notifications.map(n => n.textContent?.trim()).join(', ');
            });
            
            console.log('通知消息:', notification);
            
        } else {
            console.log('❌ 提交按钮不可见');
            return { success: false, error: '提交按钮不可见' };
        }
        
        console.log('\n📍 检查保存结果');
        
        // 检查分类列表
        const categories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#category-tbody tr'));
            return rows.map(row => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const gradientCell = cells[3];
                const gradientDiv = gradientCell ? gradientCell.querySelector('div') : null;
                
                return {
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    gradientStyle: gradientDiv ? gradientDiv.style.background : 'none',
                    gradientText: gradientDiv ? gradientDiv.textContent.trim() : 'N/A',
                    isNewCategory: nameCell ? nameCell.textContent.includes('渐变保存测试') : false
                };
            });
        });
        
        console.log('📊 分类列表:');
        let saveSuccess = false;
        
        categories.forEach((category, index) => {
            console.log(`\n分类 ${index + 1}: ${category.name}`);
            console.log(`  渐变样式: ${category.gradientStyle}`);
            console.log(`  渐变文本: ${category.gradientText}`);
            console.log(`  是新分类: ${category.isNewCategory}`);
            
            if (category.isNewCategory) {
                if (category.gradientStyle.includes('linear-gradient') &&
                    (category.gradientStyle.includes('255, 138, 128') || category.gradientStyle.includes('234, 76, 137'))) {
                    console.log('  🎉 渐变保存成功！');
                    saveSuccess = true;
                } else if (category.gradientText === '无渐变') {
                    console.log('  🔴 渐变保存失败 - 显示无渐变');
                } else if (category.gradientStyle.includes('linear-gradient')) {
                    console.log('  🎉 渐变保存成功！（其他渐变色）');
                    saveSuccess = true;
                } else {
                    console.log('  ⚠️ 渐变状态不明确');
                }
            }
        });
        
        console.log('\n📊 测试结果:');
        if (saveSuccess) {
            console.log('🎉 分类保存和渐变显示完全成功！');
        } else {
            console.log('🔴 分类保存或渐变显示失败');
        }
        
        // 截图
        await page.screenshot({ path: 'simple-category-save-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: simple-category-save-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            saveSuccess: saveSuccess
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'simple-save-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
simpleCategorySaveTest().then(result => {
    console.log('\n🎯 简单保存测试结果:', result);
}).catch(console.error);
