// 调试云数据库权限和数据结构问题
const { chromium } = require('playwright');

async function debugDatabasePermissions() {
    console.log('🔍 调试云数据库权限和数据结构问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        console.log(`[CONSOLE] ${msg.text()}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：检查云数据库原始查询结果');
        
        // 直接调用云数据库API，获取原始数据
        const rawDatabaseResult = await page.evaluate(async () => {
            try {
                // 直接使用云数据库SDK查询
                const db = window.tcbApp.database();
                const result = await db.collection('emojis').get();
                
                console.log('🔧 原始查询结果:', result);
                
                return {
                    success: true,
                    data: result.data,
                    count: result.data.length,
                    rawResult: {
                        requestId: result.requestId,
                        total: result.total,
                        limit: result.limit,
                        offset: result.offset
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    stack: error.stack
                };
            }
        });
        
        console.log('📊 原始数据库查询结果:');
        console.log(`查询成功: ${rawDatabaseResult.success}`);
        
        if (rawDatabaseResult.success) {
            console.log(`数据条数: ${rawDatabaseResult.count}`);
            console.log(`查询信息:`, rawDatabaseResult.rawResult);
            
            // 检查最新的几条数据
            const latestData = rawDatabaseResult.data.slice(-3);
            console.log('\n📋 最新3条数据的原始结构:');
            
            latestData.forEach((item, index) => {
                console.log(`\n数据 ${index + 1}:`);
                console.log(`  _id: ${item._id}`);
                console.log(`  _openid: ${item._openid || 'N/A'}`);
                console.log(`  title: ${item.title} (类型: ${typeof item.title})`);
                console.log(`  status: ${item.status} (类型: ${typeof item.status})`);
                console.log(`  category: ${item.category} (类型: ${typeof item.category})`);
                console.log(`  imageUrl: ${item.imageUrl ? item.imageUrl.substring(0, 50) + '...' : 'N/A'} (类型: ${typeof item.imageUrl})`);
                console.log(`  createTime: ${item.createTime} (类型: ${typeof item.createTime})`);
                console.log(`  所有字段:`, Object.keys(item));
            });
            
        } else {
            console.log(`查询失败: ${rawDatabaseResult.error}`);
            console.log(`错误堆栈: ${rawDatabaseResult.stack}`);
        }
        
        console.log('\n📍 第二步：对比CloudAPI.database.get的结果');
        
        // 对比CloudAPI封装的查询结果
        const cloudApiResult = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('emojis');
                return {
                    success: result.success,
                    data: result.data,
                    count: result.data ? result.data.length : 0,
                    error: result.error
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 CloudAPI查询结果:');
        console.log(`查询成功: ${cloudApiResult.success}`);
        
        if (cloudApiResult.success) {
            console.log(`数据条数: ${cloudApiResult.count}`);
            
            // 检查最新的几条数据
            const latestCloudApiData = cloudApiResult.data.slice(-3);
            console.log('\n📋 CloudAPI最新3条数据:');
            
            latestCloudApiData.forEach((item, index) => {
                console.log(`\n数据 ${index + 1}:`);
                console.log(`  _id: ${item._id}`);
                console.log(`  title: ${item.title} (类型: ${typeof item.title})`);
                console.log(`  status: ${item.status} (类型: ${typeof item.status})`);
                console.log(`  category: ${item.category} (类型: ${typeof item.category})`);
                console.log(`  imageUrl: ${item.imageUrl ? item.imageUrl.substring(0, 50) + '...' : 'N/A'} (类型: ${typeof item.imageUrl})`);
                console.log(`  createTime: ${item.createTime} (类型: ${typeof item.createTime})`);
            });
            
            // 对比数据差异
            if (rawDatabaseResult.success && rawDatabaseResult.count === cloudApiResult.count) {
                console.log('\n🔍 数据对比分析:');
                
                const rawLatest = rawDatabaseResult.data[rawDatabaseResult.data.length - 1];
                const cloudApiLatest = cloudApiResult.data[cloudApiResult.data.length - 1];
                
                console.log('原始数据最新记录字段数:', Object.keys(rawLatest).length);
                console.log('CloudAPI数据最新记录字段数:', Object.keys(cloudApiLatest).length);
                
                // 检查字段差异
                const rawFields = Object.keys(rawLatest);
                const cloudApiFields = Object.keys(cloudApiLatest);
                
                const missingInCloudApi = rawFields.filter(field => !cloudApiFields.includes(field));
                const extraInCloudApi = cloudApiFields.filter(field => !rawFields.includes(field));
                
                if (missingInCloudApi.length > 0) {
                    console.log('🔴 CloudAPI中缺失的字段:', missingInCloudApi);
                }
                
                if (extraInCloudApi.length > 0) {
                    console.log('🔴 CloudAPI中多出的字段:', extraInCloudApi);
                }
                
                // 检查字段值差异
                rawFields.forEach(field => {
                    if (rawLatest[field] !== cloudApiLatest[field]) {
                        console.log(`🔴 字段值差异 [${field}]:`);
                        console.log(`  原始: ${rawLatest[field]} (${typeof rawLatest[field]})`);
                        console.log(`  CloudAPI: ${cloudApiLatest[field]} (${typeof cloudApiLatest[field]})`);
                    }
                });
            }
            
        } else {
            console.log(`CloudAPI查询失败: ${cloudApiResult.error}`);
        }
        
        console.log('\n📍 第三步：检查数据库集合权限配置');
        
        // 尝试获取数据库集合信息
        const collectionInfo = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                
                // 尝试获取集合统计信息
                const countResult = await db.collection('emojis').count();
                
                return {
                    success: true,
                    count: countResult.total,
                    requestId: countResult.requestId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 集合权限检查:');
        console.log(`统计查询成功: ${collectionInfo.success}`);
        if (collectionInfo.success) {
            console.log(`集合总数: ${collectionInfo.count}`);
        } else {
            console.log(`权限错误: ${collectionInfo.error}`);
        }
        
        console.log('\n📍 第四步：测试单条记录查询');
        
        if (rawDatabaseResult.success && rawDatabaseResult.data.length > 0) {
            const testId = rawDatabaseResult.data[rawDatabaseResult.data.length - 1]._id;
            
            const singleRecordResult = await page.evaluate(async (id) => {
                try {
                    const db = window.tcbApp.database();
                    const result = await db.collection('emojis').doc(id).get();
                    
                    return {
                        success: true,
                        data: result.data,
                        hasData: !!result.data
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            }, testId);
            
            console.log('📊 单条记录查询结果:');
            console.log(`查询成功: ${singleRecordResult.success}`);
            
            if (singleRecordResult.success && singleRecordResult.hasData) {
                const record = singleRecordResult.data;
                console.log('单条记录详情:');
                console.log(`  _id: ${record._id}`);
                console.log(`  title: ${record.title} (类型: ${typeof record.title})`);
                console.log(`  status: ${record.status} (类型: ${typeof record.status})`);
                console.log(`  category: ${record.category} (类型: ${typeof record.category})`);
                console.log(`  imageUrl存在: ${!!record.imageUrl}`);
                console.log(`  createTime: ${record.createTime} (类型: ${typeof record.createTime})`);
            } else {
                console.log(`单条记录查询失败: ${singleRecordResult.error}`);
            }
        }
        
        console.log('\n🎯 问题诊断结论:');
        
        if (rawDatabaseResult.success && cloudApiResult.success) {
            if (rawDatabaseResult.data[0].title && !cloudApiResult.data[0].title) {
                console.log('🔴 问题确认: CloudAPI封装层过滤了数据字段');
                console.log('🔧 修复方向: 检查CloudAPI.database.get方法的数据处理逻辑');
            } else if (!rawDatabaseResult.data[0].title) {
                console.log('🔴 问题确认: 数据库中的数据本身就是损坏的');
                console.log('🔧 修复方向: 检查数据保存逻辑，数据可能没有正确保存');
            } else {
                console.log('✅ 数据查询正常，问题可能在其他地方');
            }
        }
        
        // 截图
        await page.screenshot({ path: 'debug-database-permissions.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-database-permissions.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开30秒供查看...');
        await page.waitForTimeout(30000);
        
        return {
            success: true,
            rawDatabaseResult,
            cloudApiResult,
            collectionInfo
        };
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        await page.screenshot({ path: 'debug-permissions-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行调试
debugDatabasePermissions().then(result => {
    console.log('\n🎯 数据库权限调试结果:', result.success ? '成功' : '失败');
}).catch(console.error);
