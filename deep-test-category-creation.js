// 深度测试分类管理功能 - 真实创建分类并检查渐变色保存问题
const { chromium } = require('playwright');

async function deepTestCategoryCreation() {
    console.log('📂 开始深度测试分类管理功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500,
        args: ['--start-maximized']
    });
    
    const context = await browser.newContext({
        viewport: { width: 1920, height: 1080 }
    });
    
    const page = await context.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[CONSOLE] ${text}`);
    });
    
    // 监听网络错误
    page.on('response', response => {
        if (response.status() >= 400) {
            console.log(`🔴 [HTTP ERROR] ${response.status()} - ${response.url()}`);
        }
    });
    
    try {
        console.log('📍 步骤1: 访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        console.log('📍 步骤2: 登录管理后台');
        await page.waitForTimeout(3000);
        
        // 检查登录表单
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        console.log('登录表单检查:');
        console.log('  用户名输入框:', await usernameInput.isVisible());
        console.log('  密码输入框:', await passwordInput.isVisible());
        console.log('  登录按钮:', await loginButton.isVisible());
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录信息已提交');
            await page.waitForTimeout(10000); // 等待登录完成和初始化
        } else {
            console.log('ℹ️ 可能已经登录');
        }
        
        console.log('\n📍 步骤3: 进入分类管理页面');
        
        // 查找分类管理链接
        const categoryLink = await page.locator('text=📂 分类管理').first();
        const categoryLinkVisible = await categoryLink.isVisible();
        console.log('分类管理链接可见:', categoryLinkVisible);
        
        if (categoryLinkVisible) {
            await categoryLink.click();
            console.log('✅ 已点击分类管理链接');
            await page.waitForTimeout(5000);
        } else {
            console.log('❌ 未找到分类管理链接');
            // 尝试其他可能的选择器
            const altCategoryLink = await page.locator('a:has-text("分类管理")').first();
            if (await altCategoryLink.isVisible()) {
                await altCategoryLink.click();
                console.log('✅ 使用备用选择器点击分类管理');
                await page.waitForTimeout(5000);
            } else {
                console.log('❌ 完全找不到分类管理链接');
                return;
            }
        }
        
        console.log('\n📍 步骤4: 检查现有分类数据');
        
        // 检查分类表格
        const existingCategories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                return {
                    index: index + 1,
                    data: cells.map(cell => cell.textContent?.trim()).slice(0, 5),
                    hasGradient: !!row.querySelector('.gradient-preview'),
                    gradientStyle: row.querySelector('.gradient-preview')?.style?.background || 'none'
                };
            });
        });
        
        console.log('📋 现有分类数据:');
        existingCategories.forEach(category => {
            console.log(`  分类 ${category.index}: [${category.data.join(', ')}]`);
            console.log(`    有渐变预览: ${category.hasGradient}`);
            console.log(`    渐变样式: ${category.gradientStyle}`);
        });
        
        console.log('\n📍 步骤5: 创建新分类');
        
        // 点击添加分类按钮
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        const addBtnVisible = await addCategoryBtn.isVisible();
        console.log('添加分类按钮可见:', addBtnVisible);
        
        if (addBtnVisible) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        } else {
            console.log('❌ 未找到添加分类按钮');
            // 尝试其他可能的选择器
            const altAddBtn = await page.locator('button:has-text("添加分类")').first();
            if (await altAddBtn.isVisible()) {
                await altAddBtn.click();
                console.log('✅ 使用备用选择器点击添加分类');
                await page.waitForTimeout(3000);
            } else {
                console.log('❌ 完全找不到添加分类按钮');
                return;
            }
        }
        
        console.log('\n📍 步骤6: 填写分类表单');
        
        // 检查表单元素
        const nameInput = await page.locator('input[name="name"], input[placeholder*="名称"]').first();
        const descInput = await page.locator('textarea[name="description"], textarea[placeholder*="描述"]').first();
        const gradientSelect = await page.locator('select[name="gradientType"], .gradient-type-select').first();
        const iconInput = await page.locator('input[name="icon"], input[placeholder*="图标"]').first();
        
        console.log('表单元素检查:');
        console.log('  名称输入框:', await nameInput.isVisible());
        console.log('  描述输入框:', await descInput.isVisible());
        console.log('  渐变选择框:', await gradientSelect.isVisible());
        console.log('  图标输入框:', await iconInput.isVisible());
        
        // 填写表单
        if (await nameInput.isVisible()) {
            await nameInput.fill('测试分类-渐变修复版');
            console.log('✅ 已填写分类名称: 测试分类-渐变修复版');
        }
        
        if (await descInput.isVisible()) {
            await descInput.fill('这是一个测试分类，用于验证渐变背景色保存功能');
            console.log('✅ 已填写分类描述');
        }
        
        if (await iconInput.isVisible()) {
            await iconInput.fill('🎨');
            console.log('✅ 已填写分类图标: 🎨');
        }
        
        // 选择渐变类型
        if (await gradientSelect.isVisible()) {
            // 获取可用的渐变选项
            const gradientOptions = await page.evaluate(() => {
                const select = document.querySelector('select[name="gradientType"], .gradient-type-select');
                if (!select) return [];
                return Array.from(select.options).map(option => ({
                    value: option.value,
                    text: option.textContent
                }));
            });
            
            console.log('可用的渐变选项:', gradientOptions);
            
            if (gradientOptions.length > 1) {
                // 选择第二个选项（跳过默认选项）
                const selectedGradient = gradientOptions[1];
                await gradientSelect.selectOption(selectedGradient.value);
                console.log(`✅ 已选择渐变类型: ${selectedGradient.text} (${selectedGradient.value})`);
                await page.waitForTimeout(2000);
                
                // 检查渐变预览是否显示
                const gradientPreview = await page.locator('.gradient-preview').first();
                const hasPreview = await gradientPreview.isVisible();
                console.log(`渐变预览显示: ${hasPreview}`);
                
                if (hasPreview) {
                    const previewStyle = await gradientPreview.evaluate(el => el.style.background);
                    console.log(`渐变预览样式: ${previewStyle}`);
                }
            } else {
                console.log('❌ 没有可用的渐变选项');
            }
        } else {
            console.log('❌ 未找到渐变选择框');
        }
        
        console.log('\n📍 步骤7: 保存分类');
        
        // 查找保存按钮
        const saveBtn = await page.locator('button:has-text("保存"), button:has-text("确定"), .save-btn').first();
        const saveBtnVisible = await saveBtn.isVisible();
        console.log('保存按钮可见:', saveBtnVisible);
        
        if (saveBtnVisible) {
            await saveBtn.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(8000); // 等待保存完成
        } else {
            console.log('❌ 未找到保存按钮');
        }
        
        console.log('\n📍 步骤8: 验证分类创建结果');
        
        // 等待页面更新
        await page.waitForTimeout(3000);
        
        // 重新检查分类列表
        const updatedCategories = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('table tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2]; // 名称列
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent?.trim() : 'N/A',
                    data: cells.map(cell => cell.textContent?.trim()).slice(0, 5),
                    hasGradient: !!row.querySelector('.gradient-preview'),
                    gradientStyle: row.querySelector('.gradient-preview')?.style?.background || 'none',
                    isNewCategory: nameCell ? nameCell.textContent?.includes('测试分类-渐变修复版') : false
                };
            });
        });
        
        console.log('📊 更新后的分类列表:');
        let foundNewCategory = false;
        let gradientSaved = false;
        
        updatedCategories.forEach(category => {
            console.log(`\n分类 ${category.index}:`);
            console.log(`  名称: "${category.name}"`);
            console.log(`  数据: [${category.data.join(', ')}]`);
            console.log(`  有渐变预览: ${category.hasGradient}`);
            console.log(`  渐变样式: ${category.gradientStyle}`);
            console.log(`  是新分类: ${category.isNewCategory}`);
            
            if (category.isNewCategory) {
                foundNewCategory = true;
                console.log('  ✅ 找到新创建的分类！');
                
                if (category.hasGradient && category.gradientStyle !== 'none') {
                    gradientSaved = true;
                    console.log('  ✅ 渐变背景色保存成功！');
                } else {
                    console.log('  🔴 渐变背景色保存失败！');
                }
            }
        });
        
        console.log('\n📊 测试结果总结:');
        if (foundNewCategory) {
            console.log('✅ 分类创建成功');
            if (gradientSaved) {
                console.log('✅ 渐变背景色保存成功');
            } else {
                console.log('🔴 渐变背景色保存失败 - 需要修复');
            }
        } else {
            console.log('🔴 分类创建失败');
        }
        
        // 截图
        await page.screenshot({ path: 'deep-test-category-creation.png', fullPage: true });
        console.log('\n📸 测试截图已保存: deep-test-category-creation.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            categoryCreated: foundNewCategory,
            gradientSaved: gradientSaved
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-test-error.png' });
        return {
            categoryCreated: false,
            gradientSaved: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
deepTestCategoryCreation().then(result => {
    console.log('\n🎯 最终测试结果:', result);
}).catch(console.error);
