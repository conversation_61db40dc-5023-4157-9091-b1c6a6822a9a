/**
 * 实时同步云函数
 * 提供版本管理和增量同步服务
 */

const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 版本管理集合
const VERSIONS_COLLECTION = 'data_versions'

// 支持的数据集合
const SUPPORTED_COLLECTIONS = {
  emojis: 'emojis',
  categories: 'categories', 
  banners: 'banners',
  app_config: 'app_config',
  users: 'users',
  user_actions: 'user_actions'
}

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  const { action, data = {} } = event
  
  console.log('🔄 同步API调用:', action, data)
  
  try {
    switch (action) {
      case 'getVersions':
        return await getVersions(data)
      
      case 'updateVersion':
        return await updateVersion(data)
      
      case 'getIncrementalData':
        return await getIncrementalData(data)
      
      case 'syncData':
        return await syncData(data)
      
      case 'forceSyncAll':
        return await forceSyncAll(data)
      
      case 'getDataChanges':
        return await getDataChanges(data)
      
      default:
        throw new Error(`不支持的操作: ${action}`)
    }
  } catch (error) {
    console.error('❌ 同步API错误:', error)
    return {
      success: false,
      error: error.message,
      code: error.code || 'SYNC_ERROR'
    }
  }
}

/**
 * 获取所有数据集合的版本信息
 */
async function getVersions(data) {
  try {
    console.log('📋 获取版本信息')
    
    const versions = {}
    
    // 获取版本记录
    const versionDoc = await db.collection(VERSIONS_COLLECTION)
      .doc('current_versions')
      .get()
    
    if (versionDoc.data) {
      Object.assign(versions, versionDoc.data.versions || {})
    }
    
    // 如果没有版本记录，初始化版本信息
    if (Object.keys(versions).length === 0) {
      console.log('🔄 初始化版本信息')
      await initializeVersions()
      
      // 重新获取
      const newVersionDoc = await db.collection(VERSIONS_COLLECTION)
        .doc('current_versions')
        .get()
      
      if (newVersionDoc.data) {
        Object.assign(versions, newVersionDoc.data.versions || {})
      }
    }
    
    console.log('✅ 版本信息获取成功:', versions)
    
    return {
      success: true,
      versions,
      timestamp: Date.now()
    }
    
  } catch (error) {
    console.error('❌ 获取版本信息失败:', error)
    throw error
  }
}

/**
 * 更新指定集合的版本号
 */
async function updateVersion(data) {
  const { collection, version } = data
  
  if (!collection || version === undefined) {
    throw new Error('缺少必要参数: collection, version')
  }
  
  if (!SUPPORTED_COLLECTIONS[collection]) {
    throw new Error(`不支持的集合: ${collection}`)
  }
  
  try {
    console.log(`📋 更新版本: ${collection} -> ${version}`)
    
    await db.collection(VERSIONS_COLLECTION)
      .doc('current_versions')
      .update({
        data: {
          [`versions.${collection}`]: version,
          [`lastUpdate.${collection}`]: Date.now(),
          updatedAt: Date.now()
        }
      })
    
    console.log(`✅ 版本更新成功: ${collection} -> ${version}`)
    
    return {
      success: true,
      collection,
      version,
      timestamp: Date.now()
    }
    
  } catch (error) {
    console.error(`❌ 版本更新失败: ${collection}`, error)
    throw error
  }
}

/**
 * 获取增量数据
 */
async function getIncrementalData(data) {
  const { collection, clientVersion = 0, limit = 50 } = data
  
  if (!collection) {
    throw new Error('缺少必要参数: collection')
  }
  
  if (!SUPPORTED_COLLECTIONS[collection]) {
    throw new Error(`不支持的集合: ${collection}`)
  }
  
  try {
    console.log(`📋 获取增量数据: ${collection}, 客户端版本: ${clientVersion}`)
    
    const collectionName = SUPPORTED_COLLECTIONS[collection]
    const changes = []
    
    // 获取服务器版本
    const versionInfo = await getVersions({})
    const serverVersion = versionInfo.versions[collection] || 0
    
    if (clientVersion >= serverVersion) {
      console.log('✅ 客户端数据已是最新')
      return {
        success: true,
        hasChanges: false,
        changes: [],
        serverVersion,
        clientVersion
      }
    }
    
    // 获取变更数据
    // 这里简化处理，实际项目中可能需要更复杂的增量逻辑
    const result = await db.collection(collectionName)
      .where({
        updatedAt: _.gt(clientVersion)
      })
      .limit(limit)
      .orderBy('updatedAt', 'asc')
      .get()
    
    for (const doc of result.data) {
      changes.push({
        operation: 'upsert', // 插入或更新
        id: doc._id,
        data: doc
      })
    }
    
    console.log(`✅ 增量数据获取成功: ${changes.length} 条变更`)
    
    return {
      success: true,
      hasChanges: changes.length > 0,
      changes,
      serverVersion,
      clientVersion,
      collection
    }
    
  } catch (error) {
    console.error(`❌ 获取增量数据失败: ${collection}`, error)
    throw error
  }
}

/**
 * 同步数据到服务器
 */
async function syncData(data) {
  const { collection, changes = [] } = data
  
  if (!collection || !Array.isArray(changes)) {
    throw new Error('缺少必要参数: collection, changes')
  }
  
  if (!SUPPORTED_COLLECTIONS[collection]) {
    throw new Error(`不支持的集合: ${collection}`)
  }
  
  try {
    console.log(`📋 同步数据到服务器: ${collection}, ${changes.length} 条变更`)
    
    const collectionName = SUPPORTED_COLLECTIONS[collection]
    const results = []
    
    for (const change of changes) {
      const { operation, id, data: changeData } = change
      
      try {
        let result
        
        switch (operation) {
          case 'insert':
            result = await db.collection(collectionName).add({
              data: {
                ...changeData,
                createdAt: Date.now(),
                updatedAt: Date.now()
              }
            })
            break
            
          case 'update':
            result = await db.collection(collectionName)
              .doc(id)
              .update({
                data: {
                  ...changeData,
                  updatedAt: Date.now()
                }
              })
            break
            
          case 'delete':
            result = await db.collection(collectionName)
              .doc(id)
              .remove()
            break
            
          default:
            throw new Error(`不支持的操作: ${operation}`)
        }
        
        results.push({
          operation,
          id,
          success: true,
          result
        })
        
      } catch (error) {
        console.error(`❌ 同步操作失败: ${operation} ${id}`, error)
        results.push({
          operation,
          id,
          success: false,
          error: error.message
        })
      }
    }
    
    // 更新版本号
    const newVersion = Date.now()
    await updateVersion({ collection, version: newVersion })
    
    const successCount = results.filter(r => r.success).length
    console.log(`✅ 数据同步完成: ${successCount}/${changes.length} 成功`)
    
    return {
      success: true,
      results,
      successCount,
      totalCount: changes.length,
      newVersion
    }
    
  } catch (error) {
    console.error(`❌ 数据同步失败: ${collection}`, error)
    throw error
  }
}

/**
 * 强制全量同步
 */
async function forceSyncAll(data) {
  try {
    console.log('📋 执行强制全量同步')
    
    const results = {}
    
    // 重置所有版本为0，强制客户端重新同步
    const resetVersions = {}
    for (const collection of Object.keys(SUPPORTED_COLLECTIONS)) {
      resetVersions[collection] = 0
    }
    
    await db.collection(VERSIONS_COLLECTION)
      .doc('current_versions')
      .update({
        data: {
          versions: resetVersions,
          lastUpdate: {},
          updatedAt: Date.now(),
          forceSyncAt: Date.now()
        }
      })
    
    // 获取所有集合的数据统计
    for (const [key, collectionName] of Object.entries(SUPPORTED_COLLECTIONS)) {
      try {
        const count = await db.collection(collectionName).count()
        results[key] = {
          collection: collectionName,
          count: count.total,
          version: 0
        }
      } catch (error) {
        console.warn(`⚠️ 获取集合统计失败: ${collectionName}`, error)
        results[key] = {
          collection: collectionName,
          count: 0,
          version: 0,
          error: error.message
        }
      }
    }
    
    console.log('✅ 强制全量同步完成')
    
    return {
      success: true,
      results,
      timestamp: Date.now(),
      message: '所有版本已重置，客户端将重新同步所有数据'
    }
    
  } catch (error) {
    console.error('❌ 强制全量同步失败:', error)
    throw error
  }
}

/**
 * 获取数据变更记录
 */
async function getDataChanges(data) {
  const { collection, startTime, endTime, limit = 100 } = data
  
  try {
    console.log(`📋 获取数据变更记录: ${collection}`)
    
    let query = db.collection('data_changes')
    
    if (collection) {
      query = query.where({ collection })
    }
    
    if (startTime) {
      query = query.where({ timestamp: _.gte(startTime) })
    }
    
    if (endTime) {
      query = query.where({ timestamp: _.lte(endTime) })
    }
    
    const result = await query
      .limit(limit)
      .orderBy('timestamp', 'desc')
      .get()
    
    console.log(`✅ 变更记录获取成功: ${result.data.length} 条`)
    
    return {
      success: true,
      changes: result.data,
      count: result.data.length
    }
    
  } catch (error) {
    console.error('❌ 获取变更记录失败:', error)
    throw error
  }
}

/**
 * 初始化版本信息
 */
async function initializeVersions() {
  try {
    console.log('🔄 初始化版本信息')
    
    const initialVersions = {}
    const lastUpdate = {}
    
    // 为每个支持的集合初始化版本
    for (const collection of Object.keys(SUPPORTED_COLLECTIONS)) {
      initialVersions[collection] = 0
      lastUpdate[collection] = Date.now()
    }
    
    await db.collection(VERSIONS_COLLECTION)
      .doc('current_versions')
      .set({
        data: {
          versions: initialVersions,
          lastUpdate,
          createdAt: Date.now(),
          updatedAt: Date.now()
        }
      })
    
    console.log('✅ 版本信息初始化完成')
    
  } catch (error) {
    console.error('❌ 版本信息初始化失败:', error)
    throw error
  }
}
