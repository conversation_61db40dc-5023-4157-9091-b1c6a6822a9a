# 微信小程序后端部署指南

## 1. 环境准备

### 1.1 开发工具
- 微信开发者工具 (最新版本)
- Node.js 16+ 
- 微信小程序账号和AppID

### 1.2 云开发环境
1. 在微信开发者工具中打开小程序项目
2. 点击工具栏中的"云开发"按钮
3. 开通云开发服务
4. 创建云环境（建议创建开发和生产两个环境）

## 2. 云函数部署

### 2.1 云函数列表
```
cloudfunctions/
├── admin/              # 后台管理系统API
├── login/              # 用户登录
├── getEmojiList/       # 获取表情包列表
├── getEmojiDetail/     # 获取表情包详情
├── toggleLike/         # 点赞/取消点赞
├── toggleCollect/      # 收藏/取消收藏
├── searchEmojis/       # 搜索表情包
├── getCategories/      # 获取分类列表
├── getBanners/         # 获取轮播图
├── getUserStats/       # 获取用户统计
├── uploadFile/         # 文件上传处理
├── trackAction/        # 用户行为追踪
└── dataSync/           # 数据同步和维护
```

### 2.2 部署步骤
1. 右键点击每个云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成
4. 在云开发控制台验证部署状态

### 2.3 环境变量配置
在 `app.js` 中配置云环境ID：
```javascript
wx.cloud.init({
  env: 'your-env-id', // 替换为你的云环境ID
  traceUser: true,
})
```

## 3. 数据库配置

### 3.1 创建集合
在云开发控制台的数据库中创建以下集合：

| 集合名称 | 描述 | 权限设置 |
|---------|------|----------|
| emojis | 表情包数据 | 所有用户可读，仅管理员可写 |
| categories | 分类数据 | 所有用户可读，仅管理员可写 |
| users | 用户信息 | 仅创建者可读写 |
| user_likes | 点赞记录 | 仅创建者可读写 |
| user_collections | 收藏记录 | 仅创建者可读写 |
| user_actions | 用户行为 | 仅创建者可读写 |
| banners | 轮播图 | 所有用户可读，仅管理员可写 |
| operation_logs | 操作日志 | 仅管理员可读写 |
| upload_records | 上传记录 | 仅创建者可读写 |
| daily_reports | 日报数据 | 仅管理员可读写 |
| system_configs | 系统配置 | 仅管理员可读写 |

### 3.2 权限配置
参考 `database/permissions.json` 文件中的权限规则，在云开发控制台中配置每个集合的权限。

### 3.3 初始化数据
1. 参考 `database/init-data.js` 文件
2. 在云开发控制台的数据库中手动添加初始数据
3. 注意修改管理员用户的openid为实际值

## 4. 云存储配置

### 4.1 存储桶设置
1. 在云开发控制台的存储中创建文件夹：
   - `emojis/` - 存储表情包图片
   - `banners/` - 存储轮播图
   - `avatars/` - 存储用户头像

### 4.2 安全规则
配置云存储的安全规则，确保文件访问权限正确。

## 5. 定时任务配置

### 5.1 创建定时触发器
在云开发控制台为 `dataSync` 云函数创建定时触发器：

```javascript
// 每日凌晨2点执行数据清理
{
  "triggers": [
    {
      "name": "dailyCleanup",
      "type": "timer",
      "config": "0 2 * * *"
    }
  ]
}
```

### 5.2 定时任务列表
- 每日数据清理（凌晨2点）
- 统计数据更新（每小时）
- 日报生成（凌晨3点）

## 6. 监控和日志

### 6.1 云函数监控
1. 在云开发控制台查看云函数调用统计
2. 监控错误率和响应时间
3. 设置告警规则

### 6.2 日志查看
1. 在云开发控制台查看云函数日志
2. 使用日志检索功能定位问题
3. 定期清理过期日志

## 7. 性能优化

### 7.1 数据库优化
- 为常用查询字段创建索引
- 使用聚合查询减少数据传输
- 实现数据分页加载

### 7.2 云函数优化
- 减少冷启动时间
- 优化代码逻辑
- 使用缓存减少数据库查询

### 7.3 存储优化
- 图片压缩和格式优化
- CDN加速配置
- 缓存策略设置

## 8. 安全配置

### 8.1 访问控制
- 配置数据库安全规则
- 实现用户权限验证
- 防止恶意请求

### 8.2 数据保护
- 敏感数据加密存储
- 定期数据备份
- 访问日志记录

## 9. 测试验证

### 9.1 功能测试
- 测试所有云函数接口
- 验证数据库操作
- 检查权限控制

### 9.2 性能测试
- 并发请求测试
- 响应时间测试
- 资源使用监控

## 10. 上线发布

### 10.1 生产环境配置
1. 创建生产环境
2. 部署所有云函数
3. 配置生产数据库
4. 导入生产数据

### 10.2 域名配置
1. 在小程序管理后台配置服务器域名
2. 添加云开发域名到白名单
3. 配置业务域名

### 10.3 版本管理
1. 提交代码审核
2. 发布小程序版本
3. 监控线上运行状态

## 11. 运维管理

### 11.1 日常维护
- 监控系统运行状态
- 定期备份重要数据
- 更新安全补丁

### 11.2 故障处理
- 建立故障响应流程
- 准备回滚方案
- 记录故障处理过程

### 11.3 容量规划
- 监控资源使用情况
- 预估容量需求
- 制定扩容计划

## 12. 常见问题

### 12.1 部署问题
**Q: 云函数部署失败**
A: 检查网络连接，确保依赖包正确安装

**Q: 数据库连接失败**
A: 检查环境ID配置，确保权限设置正确

### 12.2 权限问题
**Q: 用户无法访问数据**
A: 检查数据库权限规则，确保用户认证正常

**Q: 管理员权限验证失败**
A: 检查用户角色设置，确保管理员用户正确配置

### 12.3 性能问题
**Q: 接口响应慢**
A: 检查数据库索引，优化查询语句

**Q: 图片加载慢**
A: 配置CDN加速，优化图片格式和大小

## 13. 联系支持

如遇到技术问题，可通过以下方式获取支持：
- 微信开发者社区
- 云开发官方文档
- 技术支持邮箱

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15  
**维护人员**: 开发团队