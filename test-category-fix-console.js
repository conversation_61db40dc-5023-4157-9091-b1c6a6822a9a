// 测试分类数据修复效果
// 在微信开发者工具控制台中运行

console.log('🧪 开始测试分类数据修复效果...')

// 模拟小程序端调用云函数
async function testGetCategories() {
  console.log('\n📂 测试获取分类数据（修复后）...')
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getCategories' }
    })
    
    console.log('☁️ 云函数返回结果:', result)
    
    if (result.result && result.result.success) {
      const categories = result.result.data
      console.log(`✅ 获取到 ${categories.length} 个分类:`)
      
      categories.forEach(category => {
        console.log(`  - ${category.name}: ${category.emojiCount || 0} 个表情包`)
      })
      
      // 检查特定分类
      const testCategories = ['测试', '测试2', '测试3']
      console.log('\n🔍 检查特定分类:')
      testCategories.forEach(name => {
        const category = categories.find(c => c.name === name)
        if (category) {
          console.log(`✅ 找到分类 "${name}": ${category.emojiCount || 0} 个表情包`)
        } else {
          console.log(`❌ 未找到分类 "${name}"`)
        }
      })
      
      return categories
    } else {
      console.log('❌ 分类数据获取失败:', result.result?.message)
      return []
    }
  } catch (error) {
    console.error('❌ 云函数调用失败:', error)
    return []
  }
}

// 测试获取分类统计（管理后台使用的函数）
async function testGetCategoryStats() {
  console.log('\n📊 测试获取分类统计（管理后台）...')
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getCategoryStats' }
    })
    
    console.log('☁️ 云函数返回结果:', result)
    
    if (result.result && result.result.success) {
      const categories = result.result.data
      console.log(`✅ 获取到 ${categories.length} 个分类统计:`)
      
      categories.forEach(category => {
        console.log(`  - ${category.name}: ${category.count || 0} 个表情包`)
      })
      
      return categories
    } else {
      console.log('❌ 分类统计获取失败:', result.result?.message)
      return []
    }
  } catch (error) {
    console.error('❌ 云函数调用失败:', error)
    return []
  }
}

// 对比两个函数的结果
async function compareResults() {
  console.log('\n🔄 对比两个函数的结果...')
  
  const [categories, stats] = await Promise.all([
    testGetCategories(),
    testGetCategoryStats()
  ])
  
  console.log('\n📊 结果对比:')
  console.log('分类名称 | getCategories | getCategoryStats | 是否一致')
  console.log('-------|---------------|------------------|--------')
  
  const testNames = ['测试', '测试2', '测试3']
  testNames.forEach(name => {
    const category = categories.find(c => c.name === name)
    const stat = stats.find(s => s.name === name)
    
    const categoryCount = category ? (category.emojiCount || 0) : 0
    const statCount = stat ? (stat.count || 0) : 0
    const isMatch = categoryCount === statCount
    
    console.log(`${name} | ${categoryCount} | ${statCount} | ${isMatch ? '✅' : '❌'}`)
  })
}

// 主测试函数
async function runTest() {
  console.log('🚀 开始分类数据修复测试...\n')
  
  // 测试修复后的效果
  const categories = await testGetCategories()
  
  // 对比管理后台结果
  await compareResults()
  
  console.log('\n📝 测试总结:')
  console.log('1. 检查 getCategories 函数是否返回正确的 emojiCount')
  console.log('2. 对比 getCategories 和 getCategoryStats 的结果是否一致')
  console.log('3. 验证首页热门分类是否显示正确数量')
}

// 运行测试
runTest().catch(console.error)
