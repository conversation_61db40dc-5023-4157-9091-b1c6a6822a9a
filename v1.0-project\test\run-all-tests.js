#!/usr/bin/env node

// V1.0 完整测试套件运行脚本
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TestRunner {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.testResults = {
      unit: { passed: 0, failed: 0, total: 0 },
      integration: { passed: 0, failed: 0, total: 0 },
      e2e: { passed: 0, failed: 0, total: 0 },
      performance: { passed: 0, failed: 0, total: 0 }
    };
    this.startTime = Date.now();
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始运行V1.0完整测试套件...\n');

    try {
      // 检查测试环境
      await this.checkTestEnvironment();

      // 运行单元测试
      await this.runUnitTests();

      // 运行集成测试
      await this.runIntegrationTests();

      // 运行端到端测试
      await this.runE2ETests();

      // 运行性能测试
      await this.runPerformanceTests();

      // 生成测试报告
      await this.generateTestReport();

      console.log('\n🎉 所有测试完成！');

    } catch (error) {
      console.error('\n❌ 测试运行失败:', error.message);
      process.exit(1);
    }
  }

  // 检查测试环境
  async checkTestEnvironment() {
    console.log('🔍 检查测试环境...');

    // 检查Node.js版本
    const nodeVersion = process.version;
    console.log(`✅ Node.js版本: ${nodeVersion}`);

    // 检查Jest是否安装
    try {
      execSync('npx jest --version', { stdio: 'pipe' });
      console.log('✅ Jest测试框架已安装');
    } catch (error) {
      throw new Error('Jest测试框架未安装，请运行: npm install jest');
    }

    // 检查测试文件是否存在
    const testFiles = [
      'test/unit/loginAPI.test.js',
      'test/unit/webAdminAPI.test.js',
      'test/unit/dataAPI.test.js',
      'test/integration/api-integration.test.js',
      'test/integration/realtime-sync.test.js',
      'test/e2e/end-to-end.test.js',
      'test/performance/performance.test.js'
    ];

    for (const testFile of testFiles) {
      const filePath = path.join(this.projectRoot, testFile);
      if (!fs.existsSync(filePath)) {
        throw new Error(`测试文件不存在: ${testFile}`);
      }
    }

    console.log('✅ 所有测试文件检查通过\n');
  }

  // 运行单元测试
  async runUnitTests() {
    console.log('🧪 运行单元测试...');

    try {
      const testPattern = 'test/unit/*.test.js';
      const result = this.runJestTests(testPattern, '单元测试');
      
      this.testResults.unit = this.parseJestResults(result);
      
      console.log(`✅ 单元测试完成: ${this.testResults.unit.passed}/${this.testResults.unit.total} 通过\n`);

    } catch (error) {
      console.error('❌ 单元测试失败:', error.message);
      throw error;
    }
  }

  // 运行集成测试
  async runIntegrationTests() {
    console.log('🔗 运行集成测试...');

    try {
      const testPattern = 'test/integration/*.test.js';
      const result = this.runJestTests(testPattern, '集成测试');
      
      this.testResults.integration = this.parseJestResults(result);
      
      console.log(`✅ 集成测试完成: ${this.testResults.integration.passed}/${this.testResults.integration.total} 通过\n`);

    } catch (error) {
      console.error('❌ 集成测试失败:', error.message);
      throw error;
    }
  }

  // 运行端到端测试
  async runE2ETests() {
    console.log('🎯 运行端到端测试...');

    try {
      const testPattern = 'test/e2e/*.test.js';
      const result = this.runJestTests(testPattern, '端到端测试');
      
      this.testResults.e2e = this.parseJestResults(result);
      
      console.log(`✅ 端到端测试完成: ${this.testResults.e2e.passed}/${this.testResults.e2e.total} 通过\n`);

    } catch (error) {
      console.error('❌ 端到端测试失败:', error.message);
      throw error;
    }
  }

  // 运行性能测试
  async runPerformanceTests() {
    console.log('⚡ 运行性能测试...');

    try {
      const testPattern = 'test/performance/*.test.js';
      const result = this.runJestTests(testPattern, '性能测试', ['--testTimeout=60000']);
      
      this.testResults.performance = this.parseJestResults(result);
      
      console.log(`✅ 性能测试完成: ${this.testResults.performance.passed}/${this.testResults.performance.total} 通过\n`);

    } catch (error) {
      console.error('❌ 性能测试失败:', error.message);
      throw error;
    }
  }

  // 运行Jest测试
  runJestTests(pattern, testType, extraArgs = []) {
    console.log(`  正在运行${testType}...`);

    const jestArgs = [
      'jest',
      pattern,
      '--verbose',
      '--json',
      '--outputFile=test-results.json',
      ...extraArgs
    ];

    try {
      const output = execSync(`npx ${jestArgs.join(' ')}`, {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return output;

    } catch (error) {
      // Jest在有测试失败时会返回非零退出码，但我们仍需要解析结果
      if (fs.existsSync(path.join(this.projectRoot, 'test-results.json'))) {
        return fs.readFileSync(path.join(this.projectRoot, 'test-results.json'), 'utf8');
      }
      throw error;
    }
  }

  // 解析Jest测试结果
  parseJestResults(output) {
    try {
      const results = JSON.parse(output);
      
      return {
        passed: results.numPassedTests || 0,
        failed: results.numFailedTests || 0,
        total: results.numTotalTests || 0,
        success: results.success || false,
        testResults: results.testResults || []
      };

    } catch (error) {
      console.warn('无法解析测试结果JSON，使用默认值');
      return { passed: 0, failed: 0, total: 0, success: false };
    }
  }

  // 生成测试报告
  async generateTestReport() {
    console.log('📊 生成测试报告...');

    const totalDuration = Date.now() - this.startTime;
    
    const report = {
      summary: {
        timestamp: new Date().toISOString(),
        duration: totalDuration,
        totalTests: Object.values(this.testResults).reduce((sum, result) => sum + result.total, 0),
        totalPassed: Object.values(this.testResults).reduce((sum, result) => sum + result.passed, 0),
        totalFailed: Object.values(this.testResults).reduce((sum, result) => sum + result.failed, 0)
      },
      results: this.testResults,
      coverage: await this.getCoverageInfo(),
      recommendations: this.generateRecommendations()
    };

    // 计算总体成功率
    report.summary.successRate = report.summary.totalTests > 0 
      ? (report.summary.totalPassed / report.summary.totalTests * 100).toFixed(2) + '%'
      : '0%';

    // 保存报告
    const reportPath = path.join(this.projectRoot, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // 输出报告摘要
    this.printReportSummary(report);

    console.log(`📄 详细报告已保存: ${reportPath}`);
  }

  // 获取代码覆盖率信息
  async getCoverageInfo() {
    try {
      const coveragePath = path.join(this.projectRoot, 'coverage/coverage-summary.json');
      if (fs.existsSync(coveragePath)) {
        return JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
      }
    } catch (error) {
      console.warn('无法读取代码覆盖率信息');
    }
    return null;
  }

  // 生成建议
  generateRecommendations() {
    const recommendations = [];

    // 检查测试通过率
    Object.entries(this.testResults).forEach(([testType, result]) => {
      if (result.total > 0) {
        const passRate = (result.passed / result.total) * 100;
        
        if (passRate < 90) {
          recommendations.push({
            type: 'warning',
            category: testType,
            message: `${testType}测试通过率较低: ${passRate.toFixed(1)}%`,
            suggestion: '建议检查失败的测试用例并修复相关问题'
          });
        }

        if (result.failed > 0) {
          recommendations.push({
            type: 'action',
            category: testType,
            message: `${testType}测试有${result.failed}个失败用例`,
            suggestion: '请查看详细日志并修复失败的测试'
          });
        }
      }
    });

    // 检查总体测试覆盖率
    const totalTests = Object.values(this.testResults).reduce((sum, result) => sum + result.total, 0);
    if (totalTests < 50) {
      recommendations.push({
        type: 'improvement',
        category: 'coverage',
        message: `总测试用例数量较少: ${totalTests}`,
        suggestion: '建议增加更多测试用例以提高代码覆盖率'
      });
    }

    return recommendations;
  }

  // 打印报告摘要
  printReportSummary(report) {
    console.log('\n📊 测试报告摘要');
    console.log('='.repeat(50));
    console.log(`总测试用例: ${report.summary.totalTests}`);
    console.log(`通过: ${report.summary.totalPassed}`);
    console.log(`失败: ${report.summary.totalFailed}`);
    console.log(`成功率: ${report.summary.successRate}`);
    console.log(`总耗时: ${(report.summary.duration / 1000).toFixed(2)}秒`);
    
    console.log('\n📋 分类结果:');
    Object.entries(this.testResults).forEach(([testType, result]) => {
      const passRate = result.total > 0 ? (result.passed / result.total * 100).toFixed(1) : '0';
      console.log(`  ${testType}: ${result.passed}/${result.total} (${passRate}%)`);
    });

    if (report.recommendations.length > 0) {
      console.log('\n💡 建议:');
      report.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. [${rec.type.toUpperCase()}] ${rec.message}`);
        console.log(`     ${rec.suggestion}`);
      });
    }

    console.log('='.repeat(50));
  }

  // 清理临时文件
  cleanup() {
    const tempFiles = [
      'test-results.json',
      'coverage'
    ];

    tempFiles.forEach(file => {
      const filePath = path.join(this.projectRoot, file);
      if (fs.existsSync(filePath)) {
        try {
          if (fs.statSync(filePath).isDirectory()) {
            fs.rmSync(filePath, { recursive: true, force: true });
          } else {
            fs.unlinkSync(filePath);
          }
        } catch (error) {
          console.warn(`清理文件失败: ${file}`);
        }
      }
    });
  }
}

// 主函数
async function main() {
  const runner = new TestRunner();
  
  try {
    await runner.runAllTests();
    process.exit(0);
  } catch (error) {
    console.error('测试运行失败:', error);
    process.exit(1);
  } finally {
    // 清理临时文件
    runner.cleanup();
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = TestRunner;
