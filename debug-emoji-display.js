// 表情包显示问题诊断测试用例
// 在小程序控制台中运行这些测试

const EmojiDisplayTest = {
  
  // 测试1: 检查页面数据状态
  testPageData() {
    console.log('🧪 测试1: 检查页面数据状态');
    console.log('='.repeat(50));
    
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (!currentPage) {
      console.error('❌ 无法获取当前页面实例');
      return false;
    }
    
    const data = currentPage.data;
    
    console.log('📊 页面数据状态:');
    console.log('- emojiList:', data.emojiList);
    console.log('- emojiList.length:', data.emojiList?.length || 0);
    console.log('- searchResults:', data.searchResults);
    console.log('- searchResults.length:', data.searchResults?.length || 0);
    console.log('- hotCategories.length:', data.hotCategories?.length || 0);
    console.log('- bannerList.length:', data.bannerList?.length || 0);
    
    // 检查显示条件
    const showCondition = data.searchResults?.length === 0 && data.emojiList?.length > 0;
    console.log('🔍 显示条件检查:');
    console.log('- searchResults.length === 0:', data.searchResults?.length === 0);
    console.log('- emojiList.length > 0:', data.emojiList?.length > 0);
    console.log('- 最终显示条件:', showCondition);
    
    return {
      hasEmojiData: data.emojiList?.length > 0,
      shouldShow: showCondition,
      pageData: data
    };
  },
  
  // 测试2: 手动调用云函数
  async testCloudFunction() {
    console.log('🧪 测试2: 手动调用云函数');
    console.log('='.repeat(50));
    
    try {
      console.log('🔄 调用 dataAPI 云函数...');
      
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: 'all',
            page: 1,
            limit: 10
          }
        }
      });
      
      console.log('📥 云函数返回结果:', result);
      
      if (result.result && result.result.success) {
        console.log('✅ 云函数调用成功');
        console.log('- 数据条数:', result.result.data?.length || 0);
        console.log('- 数据内容:', result.result.data);
        return result.result.data;
      } else {
        console.error('❌ 云函数返回失败:', result.result);
        return null;
      }
    } catch (error) {
      console.error('❌ 云函数调用异常:', error);
      return null;
    }
  },
  
  // 测试3: 手动设置页面数据
  testSetPageData(testData = null) {
    console.log('🧪 测试3: 手动设置页面数据');
    console.log('='.repeat(50));
    
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (!currentPage) {
      console.error('❌ 无法获取当前页面实例');
      return false;
    }
    
    // 使用测试数据或默认数据
    const mockData = testData || [
      {
        "_id": "test1",
        "title": "测试表情1",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2NjAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIg8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test2", 
        "title": "测试表情2",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZkNzAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIo8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test3",
        "title": "测试表情3", 
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBiY2ZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmI08L3RleHQ+Cjwvc3ZnPgo="
      }
    ];
    
    console.log('📝 设置测试数据:', mockData);
    
    try {
      currentPage.setData({
        emojiList: mockData,
        searchResults: [] // 确保搜索结果为空
      });
      
      console.log('✅ 数据设置成功');
      
      // 验证设置结果
      setTimeout(() => {
        const newData = currentPage.data;
        console.log('🔍 验证设置结果:');
        console.log('- emojiList.length:', newData.emojiList?.length || 0);
        console.log('- searchResults.length:', newData.searchResults?.length || 0);
        console.log('- 显示条件:', newData.searchResults?.length === 0 && newData.emojiList?.length > 0);
      }, 100);
      
      return true;
    } catch (error) {
      console.error('❌ 设置数据失败:', error);
      return false;
    }
  },
  
  // 测试4: 检查DOM元素
  testDOMElements() {
    console.log('🧪 测试4: 检查DOM元素');
    console.log('='.repeat(50));
    
    try {
      // 使用小程序的选择器API
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (!currentPage) {
        console.error('❌ 无法获取当前页面实例');
        return false;
      }
      
      // 检查关键元素
      const query = wx.createSelectorQuery().in(currentPage);
      
      query.select('.emoji-section').boundingClientRect();
      query.select('.emoji-list').boundingClientRect();
      query.selectAll('.emoji-item').boundingClientRect();
      
      query.exec((res) => {
        console.log('🔍 DOM元素检查结果:');
        console.log('- .emoji-section:', res[0]);
        console.log('- .emoji-list:', res[1]);
        console.log('- .emoji-item 数量:', res[2]?.length || 0);
        console.log('- .emoji-item 详情:', res[2]);
      });
      
      return true;
    } catch (error) {
      console.error('❌ DOM检查失败:', error);
      return false;
    }
  },
  
  // 测试5: 完整流程测试
  async runFullTest() {
    console.log('🧪 开始完整测试流程');
    console.log('='.repeat(60));
    
    // 步骤1: 检查当前状态
    console.log('\n📋 步骤1: 检查当前页面状态');
    const pageTest = this.testPageData();
    
    // 步骤2: 测试云函数
    console.log('\n📋 步骤2: 测试云函数调用');
    const cloudData = await this.testCloudFunction();
    
    // 步骤3: 手动设置数据
    console.log('\n📋 步骤3: 手动设置测试数据');
    const setDataResult = this.testSetPageData(cloudData);
    
    // 步骤4: 检查DOM
    console.log('\n📋 步骤4: 检查DOM元素');
    setTimeout(() => {
      this.testDOMElements();
    }, 500);
    
    // 总结
    console.log('\n📊 测试总结:');
    console.log('='.repeat(50));
    console.log('- 页面数据状态:', pageTest.hasEmojiData ? '✅ 有数据' : '❌ 无数据');
    console.log('- 显示条件:', pageTest.shouldShow ? '✅ 满足' : '❌ 不满足');
    console.log('- 云函数调用:', cloudData ? '✅ 成功' : '❌ 失败');
    console.log('- 手动设置数据:', setDataResult ? '✅ 成功' : '❌ 失败');
    
    return {
      pageData: pageTest,
      cloudData: cloudData,
      setDataResult: setDataResult
    };
  }
};

// 导出测试对象到全局，方便在控制台中使用
if (typeof global !== 'undefined') {
  global.EmojiDisplayTest = EmojiDisplayTest;
} else if (typeof window !== 'undefined') {
  window.EmojiDisplayTest = EmojiDisplayTest;
}

console.log('🧪 表情包显示测试用例已加载');
console.log('📋 使用方法:');
console.log('- EmojiDisplayTest.testPageData() - 检查页面数据');
console.log('- EmojiDisplayTest.testCloudFunction() - 测试云函数');
console.log('- EmojiDisplayTest.testSetPageData() - 手动设置数据');
console.log('- EmojiDisplayTest.testDOMElements() - 检查DOM元素');
console.log('- EmojiDisplayTest.runFullTest() - 运行完整测试');

// 自动运行测试（可选）
// EmojiDisplayTest.runFullTest();
