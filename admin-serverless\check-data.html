<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查数据库数据</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>检查数据库数据</h1>
    <button onclick="checkData()">检查数据库</button>
    <button onclick="testCloudFunction()">测试云函数</button>
    <button onclick="testDirectWrite()">测试直接写入</button>
    <div id="results"></div>

    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    <script>
        const ENV_ID = 'cloud1-5g6pvnpl88dc0142';
        
        function log(message, type = 'info') {
            document.getElementById('results').innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        async function checkData() {
            document.getElementById('results').innerHTML = '';

            try {
                // 初始化
                const app = window.cloudbase.init({
                    env: ENV_ID,
                    region: 'ap-shanghai'
                });

                // 先登录
                const auth = app.auth();
                log('正在进行匿名登录...', 'info');

                try {
                    await auth.signInAnonymously();
                    log('✅ 匿名登录成功', 'success');
                } catch (loginError) {
                    log(`登录失败: ${loginError.message}`, 'error');
                    return;
                }

                const db = app.database();
                log('✅ 数据库连接成功', 'success');

                // 测试云函数调用
                log('=== 测试云函数调用 ===', 'info');
                try {
                    const funcResult = await app.callFunction({
                        name: 'adminAPI',
                        data: { action: 'getStats' }
                    });
                    log(`✅ 云函数调用成功: ${JSON.stringify(funcResult.result).substring(0, 100)}...`, 'success');
                } catch (funcError) {
                    log(`❌ 云函数调用失败: ${funcError.message}`, 'error');
                    log('💡 这解释了为什么主应用无法加载数据', 'warning');
                }
                
                // 检查各个集合
                const collections = ['emojis', 'categories', 'users', 'banners'];
                
                for (const collection of collections) {
                    try {
                        const result = await db.collection(collection).get();
                        log(`${collection}: ${result.data ? result.data.length : 0} 条记录`, 'info');
                        
                        if (result.data && result.data.length > 0) {
                            log(`${collection} 示例数据: ${JSON.stringify(result.data[0]).substring(0, 100)}...`, 'info');
                        }
                    } catch (error) {
                        log(`${collection} 查询失败: ${error.message}`, 'error');
                    }
                }
                
            } catch (error) {
                log(`连接失败: ${error.message}`, 'error');
            }
        }

        async function testCloudFunction() {
            document.getElementById('results').innerHTML = '';
            log('=== 测试云函数调用 ===', 'info');

            try {
                const app = window.cloudbase.init({
                    env: ENV_ID,
                    region: 'ap-shanghai'
                });

                const auth = app.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功', 'success');

                // 测试各种云函数调用
                const tests = [
                    { action: 'getStats', desc: '获取统计数据' },
                    { action: 'getCategoryList', desc: '获取分类列表' },
                    { action: 'getEmojiList', desc: '获取表情包列表' },
                    { action: 'getBannerList', desc: '获取横幅列表' },
                    { action: 'getUserList', desc: '获取用户列表' }
                ];

                for (const test of tests) {
                    try {
                        log(`测试: ${test.desc}...`, 'info');
                        const result = await app.callFunction({
                            name: 'adminAPI',
                            data: { action: test.action }
                        });

                        if (result.result && result.result.success) {
                            log(`✅ ${test.desc} 成功: ${result.result.data ? result.result.data.length || '有数据' : '无数据'}`, 'success');
                        } else {
                            log(`❌ ${test.desc} 失败: ${result.result ? result.result.error : '未知错误'}`, 'error');
                        }
                    } catch (error) {
                        log(`❌ ${test.desc} 异常: ${error.message}`, 'error');
                    }
                }

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
            }
        }

        async function testDirectWrite() {
            document.getElementById('results').innerHTML = '';
            log('=== 测试直接写入操作 ===', 'info');

            try {
                const app = window.cloudbase.init({
                    env: ENV_ID,
                    region: 'ap-shanghai'
                });

                const auth = app.auth();
                await auth.signInAnonymously();
                log('✅ 匿名登录成功', 'success');

                // 测试创建分类
                log('测试: 创建分类...', 'info');
                try {
                    const result = await app.callFunction({
                        name: 'adminAPI',
                        data: {
                            action: 'createCategory',
                            data: {
                                name: '测试分类_' + Date.now(),
                                description: '这是一个测试分类',
                                sort: 999
                            }
                        }
                    });

                    if (result.result && result.result.success) {
                        log(`✅ 创建分类成功: ${JSON.stringify(result.result)}`, 'success');
                    } else {
                        log(`❌ 创建分类失败: ${result.result ? result.result.error : '未知错误'}`, 'error');
                    }
                } catch (error) {
                    log(`❌ 创建分类异常: ${error.message}`, 'error');
                }

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
