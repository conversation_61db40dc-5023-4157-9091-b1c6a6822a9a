/* pages/index/index.wxss */
.container {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 160rpx; /* 增加底部安全距离 */
  padding-bottom: calc(160rpx + env(safe-area-inset-bottom)); /* 适配刘海屏 */
}

/* 测试按钮 (开发阶段) */
.test-button-container {
  padding: 20rpx;
  background: #fff3cd;
  border-bottom: 2rpx solid #ffeaa7;
}

.test-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  height: 80rpx;
  line-height: 80rpx;
}

/* 搜索头部 */
.search-header {
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 2rpx solid #e8e8e8;
  border-radius: 60rpx;
  padding: 20rpx 30rpx;
  margin-right: 24rpx;
  height: 80rpx;
  box-sizing: border-box;
}

.search-input-container:active {
  border-color: #d0d0d0;
}

.search-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  color: #c0c0c0;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  line-height: 1;
}

.search-input::placeholder {
  color: #c0c0c0;
}

.clear-btn {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx;
  margin-left: 8rpx;
}

.clear-btn:active {
  color: #666;
}

.search-btn {
  background: #8B5CF6 !important;
  color: white !important;
  border-radius: 50rpx !important;
  border: none !important;
  padding: 0 !important;
  font-size: 28rpx !important;
  font-weight: 500 !important;
  height: 80rpx !important;
  width: 120rpx !important;
  line-height: 80rpx !important;
  text-align: center !important;
  box-sizing: border-box !important;
  margin: 0 !important;
}

.search-btn::after {
  border: none !important;
}

/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 32rpx;
  right: 32rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400rpx;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background-color: #f8f9fa;
}

.suggestion-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.suggestion-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 实时同步状态栏 */
.sync-status-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 24rpx;
  margin-top: 2rpx;
}

.sync-status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.sync-status-text {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 搜索结果区域 */
.search-results-section {
  padding: 40rpx 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.search-results-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.clear-search {
  font-size: 24rpx;
  color: #8B5CF6;
  padding: 8rpx 16rpx;
  border: 1rpx solid #8B5CF6;
  border-radius: 20rpx;
}

/* Banner轮播图 */
.banner-section {
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.banner-swiper {
  height: 360rpx;
  border-radius: 20rpx;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.2) 100%);
  display: flex;
  align-items: flex-end;
  padding: 40rpx;
}

.banner-content {
  color: white;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  display: block;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 24rpx;
  display: block;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

.banner-button {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 16rpx 32rpx;
  backdrop-filter: blur(10rpx);
  display: inline-block;
}

.banner-button-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 分类部分 */
.category-section {
  margin: 40rpx 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #8B5CF6;
  font-weight: 500;
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.category-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  min-height: 120rpx;
}

.category-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 36rpx;
}

.category-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.category-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.category-count {
  font-size: 22rpx;
  color: #666;
  display: block;
}

.category-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}

/* 表情包部分 */
.emoji-section {
  margin: 40rpx 20rpx 0;
}

.emoji-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: scale(0.98);
}

.emoji-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
}

.emoji-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

/* 数据统计区域 */
.emoji-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx;
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.stat-number {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .category-grid {
    grid-template-columns: 1fr;
  }
  
  .emoji-list {
    grid-template-columns: 1fr;
  }
}

/* 数据同步状态样式 */
.sync-status {
  background: #f8f9fa;
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  border-left: 8rpx solid #007aff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

/* 优化测试按钮样式 */
.test-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
  background: #fff3cd;
  margin: 20rpx;
  border-radius: 16rpx;
  flex-wrap: wrap;
  gap: 10rpx;
  border-bottom: 2rpx solid #ffeaa7;
}

.test-button {
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  color: white;
  border: none;
  min-width: 120rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.test-button:active {
  transform: scale(0.95);
}

/* 调试按钮样式 */
.debug-buttons {
  position: fixed;
  bottom: 200rpx;
  right: 40rpx;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.debug-btn {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  color: white;
  border: none;
  min-width: 160rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
  transition: all 0.3s ease;
}

.debug-btn:active {
  transform: scale(0.95);
}

/* 分页状态样式 */
.pagination-status {
  padding: 40rpx 20rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8B5CF6;
  font-weight: 500;
}

.no-more {
  padding: 20rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

.load-more-hint {
  padding: 20rpx;
}

.hint-text {
  font-size: 26rpx;
  color: #ccc;
}