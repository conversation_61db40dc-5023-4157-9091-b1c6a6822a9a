// 自定义TabBar组件
Component({
  data: {
    selected: 0,
    color: "#8A8A8A",
    selectedColor: "#FF8C00",
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "/images/home.png",
        selectedIconPath: "/images/home.png",
        text: "首页"
      },
      {
        pagePath: "/pages/search/search",
        iconPath: "/images/search.png",
        selectedIconPath: "/images/search.png",
        text: "搜索"
      },
      {
        pagePath: "/pages/category/category",
        iconPath: "/images/category.png",
        selectedIconPath: "/images/category.png",
        text: "分类"
      },
      {
        pagePath: "/pages/profile/profile",
        iconPath: "/images/profile.png",
        selectedIconPath: "/images/profile.png",
        text: "我的"
      }
    ]
  },
  
  attached() {
    // 延迟执行，确保页面完全初始化
    setTimeout(() => {
      this.setSelected()
    }, 100)
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      const index = data.index
      
      // 触发点击动画
      this.triggerAnimation(index)
      
      // 切换页面
      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: index
          })
        }
      })
    },

    // 触发点击动画
    triggerAnimation(index) {
      // 添加点击动画类
      this.setData({
        [`list[${index}].isAnimating`]: true
      })
      
      // 500ms后移除动画类
      setTimeout(() => {
        this.setData({
          [`list[${index}].isAnimating`]: false
        })
      }, 600)
    },

    // 设置当前选中的tab
    setSelected() {
      try {
        // 检查是否在小程序环境中
        if (typeof getCurrentPages !== 'function') {
          console.warn('TabBar: getCurrentPages 方法不可用')
          return
        }

        const pages = getCurrentPages()
        if (!pages || pages.length === 0) {
          console.warn('TabBar: 无法获取当前页面信息，页面栈为空')
          return
        }

        const currentPage = pages[pages.length - 1]
        if (!currentPage) {
          console.warn('TabBar: 当前页面对象为空')
          return
        }

        // 检查 route 属性是否存在
        if (!currentPage.route || typeof currentPage.route !== 'string') {
          console.warn('TabBar: 当前页面路由信息不完整', {
            hasRoute: !!currentPage.route,
            routeType: typeof currentPage.route,
            pageKeys: Object.keys(currentPage)
          })
          return
        }

        const currentPath = '/' + currentPage.route
        console.log('TabBar: 当前页面路径', currentPath)

        const selected = this.data.list.findIndex(item => item.pagePath === currentPath)
        if (selected !== -1) {
          this.setData({
            selected: selected
          })
          console.log('TabBar: 设置选中项', selected)
        } else {
          console.log('TabBar: 未找到匹配的tab项', currentPath, '可用路径:', this.data.list.map(item => item.pagePath))
        }
      } catch (error) {
        console.error('TabBar: setSelected 方法出错', error)
        // 设置默认选中项，避免界面异常
        this.setData({
          selected: 0
        })
      }
    }
  }
})