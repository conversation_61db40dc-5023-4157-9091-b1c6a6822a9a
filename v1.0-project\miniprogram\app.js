// 小程序应用入口 - 集成V1.0实时同步系统
const CloudDatabaseWatcher = require('./utils/cloud-database-watcher');

App({
  globalData: {
    dataWatcher: null,
    eventBus: null,
    userInfo: null,
    systemInfo: null
  },

  onLaunch() {
    console.log('🚀 小程序启动 - V1.0版本');
    
    // 初始化云开发
    this.initCloud();
    
    // 初始化系统信息
    this.initSystemInfo();
    
    // 初始化事件总线
    this.initEventBus();
    
    // 初始化数据监听器
    this.initDataWatcher();
    
    // 监听应用生命周期
    this.setupLifecycleListeners();
  },

  onShow() {
    console.log('📱 小程序显示');
    
    // 检查数据监听器状态
    this.checkDataWatcherStatus();
  },

  onHide() {
    console.log('📱 小程序隐藏');
    
    // 可以在这里暂停一些不必要的监听
    // 但保持核心数据监听器运行
  },

  onError(error) {
    console.error('🚨 小程序错误:', error);
    
    // 错误上报
    this.reportError(error);
  },

  // 初始化云开发
  initCloud() {
    try {
      wx.cloud.init({
        env: 'cloud1-5g6pvnpl88dc0142', // 云开发环境ID
        traceUser: true
      });
      
      console.log('✅ 云开发初始化成功');
      
    } catch (error) {
      console.error('❌ 云开发初始化失败:', error);
    }
  },

  // 初始化系统信息
  initSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      
      console.log('📱 系统信息:', {
        platform: systemInfo.platform,
        version: systemInfo.version,
        SDKVersion: systemInfo.SDKVersion
      });
      
    } catch (error) {
      console.error('❌ 获取系统信息失败:', error);
    }
  },

  // 初始化事件总线
  initEventBus() {
    // 简单的事件总线实现
    this.globalData.eventBus = {
      events: {},
      
      on(event, callback) {
        if (!this.events[event]) {
          this.events[event] = [];
        }
        this.events[event].push(callback);
      },
      
      off(event, callback) {
        if (this.events[event]) {
          const index = this.events[event].indexOf(callback);
          if (index > -1) {
            this.events[event].splice(index, 1);
          }
        }
      },
      
      emit(event, data) {
        if (this.events[event]) {
          this.events[event].forEach(callback => {
            try {
              callback(data);
            } catch (error) {
              console.error('事件回调执行失败:', error);
            }
          });
        }
      }
    };
    
    console.log('✅ 事件总线初始化完成');
  },

  // 初始化数据监听器
  initDataWatcher() {
    try {
      // 创建全局数据监听器实例
      this.globalData.dataWatcher = new CloudDatabaseWatcher();
      
      // 监听数据更新事件
      this.globalData.eventBus.on('dataUpdate', (data) => {
        console.log('📢 全局数据更新事件:', data);
        
        // 可以在这里处理全局数据更新逻辑
        this.handleGlobalDataUpdate(data);
      });
      
      // 监听连接状态变更事件
      this.globalData.eventBus.on('connectionStatusChange', (data) => {
        console.log('📡 全局连接状态变更:', data);
        
        // 可以在这里处理全局连接状态变更逻辑
        this.handleGlobalConnectionStatusChange(data);
      });
      
      console.log('✅ 数据监听器初始化完成');
      
    } catch (error) {
      console.error('❌ 数据监听器初始化失败:', error);
    }
  },

  // 设置生命周期监听
  setupLifecycleListeners() {
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('🌐 网络状态变化:', res);
      
      if (res.isConnected && this.globalData.dataWatcher) {
        // 网络恢复时重新初始化监听器
        console.log('🔄 网络恢复，重新初始化数据监听器');
        this.globalData.dataWatcher.initWatchers();
      }
    });
    
    // 监听内存警告
    wx.onMemoryWarning((res) => {
      console.warn('⚠️ 内存警告:', res);
      
      // 可以在这里清理一些缓存
      this.cleanupMemory();
    });
  },

  // 检查数据监听器状态
  checkDataWatcherStatus() {
    if (this.globalData.dataWatcher) {
      const status = this.globalData.dataWatcher.getConnectionStatus();
      
      console.log('📊 数据监听器状态:', status);
      
      // 如果监听器未连接，尝试重新连接
      if (!status.isConnected && status.reconnectAttempts === 0) {
        console.log('🔄 重新启动数据监听器');
        this.globalData.dataWatcher.initWatchers();
      }
    }
  },

  // 处理全局数据更新
  handleGlobalDataUpdate(data) {
    const { collectionName, operation, timestamp } = data;
    
    // 记录数据更新日志
    console.log(`📊 全局数据更新: ${collectionName} - ${operation}`);
    
    // 可以在这里添加全局数据更新处理逻辑
    // 比如更新全局状态、发送统计数据等
    
    // 更新最后同步时间
    wx.setStorageSync('lastSyncTime', timestamp);
  },

  // 处理全局连接状态变更
  handleGlobalConnectionStatusChange(data) {
    const { status, timestamp } = data;
    
    console.log(`📡 全局连接状态: ${status}`);
    
    // 记录连接状态历史
    const statusHistory = wx.getStorageSync('connectionStatusHistory') || [];
    statusHistory.push({ status, timestamp });
    
    // 只保留最近10条记录
    if (statusHistory.length > 10) {
      statusHistory.splice(0, statusHistory.length - 10);
    }
    
    wx.setStorageSync('connectionStatusHistory', statusHistory);
  },

  // 获取用户信息
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo);
        return;
      }
      
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  },

  // 获取数据监听器
  getDataWatcher() {
    return this.globalData.dataWatcher;
  },

  // 获取事件总线
  getEventBus() {
    return this.globalData.eventBus;
  },

  // 清理内存
  cleanupMemory() {
    console.log('🧹 清理内存...');
    
    try {
      // 清理过期的缓存数据
      const cacheKeys = ['categories_cache', 'emojis_cache', 'banners_cache'];
      
      cacheKeys.forEach(key => {
        const cacheTime = wx.getStorageSync(`${key}_time`);
        if (cacheTime) {
          const now = Date.now();
          const oneHour = 60 * 60 * 1000;
          
          if (now - cacheTime > oneHour) {
            wx.removeStorageSync(key);
            wx.removeStorageSync(`${key}_time`);
            console.log(`🗑️ 清理过期缓存: ${key}`);
          }
        }
      });
      
      // 清理连接状态历史（只保留最近5条）
      const statusHistory = wx.getStorageSync('connectionStatusHistory') || [];
      if (statusHistory.length > 5) {
        const recentHistory = statusHistory.slice(-5);
        wx.setStorageSync('connectionStatusHistory', recentHistory);
      }
      
      console.log('✅ 内存清理完成');
      
    } catch (error) {
      console.error('❌ 内存清理失败:', error);
    }
  },

  // 错误上报
  reportError(error) {
    try {
      // 可以在这里集成错误上报服务
      console.log('📊 错误上报:', error);
      
      // 记录错误到本地存储
      const errorLogs = wx.getStorageSync('errorLogs') || [];
      errorLogs.push({
        error: error.toString(),
        timestamp: Date.now(),
        page: getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].route : 'unknown'
      });
      
      // 只保留最近20条错误日志
      if (errorLogs.length > 20) {
        errorLogs.splice(0, errorLogs.length - 20);
      }
      
      wx.setStorageSync('errorLogs', errorLogs);
      
    } catch (reportError) {
      console.error('❌ 错误上报失败:', reportError);
    }
  },

  // 获取应用统计信息
  getAppStats() {
    return {
      lastSyncTime: wx.getStorageSync('lastSyncTime'),
      connectionStatusHistory: wx.getStorageSync('connectionStatusHistory') || [],
      errorLogs: wx.getStorageSync('errorLogs') || [],
      dataWatcherStatus: this.globalData.dataWatcher ? this.globalData.dataWatcher.getConnectionStatus() : null
    };
  }
});
