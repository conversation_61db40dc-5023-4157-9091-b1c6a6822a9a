// 测试页面 - 直接在微信开发者工具控制台运行这段代码

console.log('🚀 开始测试数据加载...')

// 测试云函数调用
async function testCloudFunction() {
  try {
    console.log('📡 测试 dataAPI 云函数...')
    
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getCategories' }
    })
    
    console.log('✅ 云函数调用成功:', result)
    return true
  } catch (error) {
    console.log('❌ 云函数调用失败:', error.message)
    console.log('💡 错误代码:', error.errCode)
    
    if (error.errCode === -501000) {
      console.log('🔧 解决方案: 云函数未部署，请在微信开发者工具中部署 dataAPI 云函数')
    }
    
    return false
  }
}

// 测试本地数据
function testLocalData() {
  console.log('📋 测试本地数据...')
  
  const categories = [
    { _id: 'funny', name: '搞笑幽默', icon: '😂', emojiCount: 25 },
    { _id: 'cute', name: '可爱萌宠', icon: '🐱', emojiCount: 18 },
    { _id: 'emotion', name: '情感表达', icon: '❤️', emojiCount: 22 }
  ]
  
  const emojis = [
    { id: '1', title: '哈哈哈笑死我了', likes: 1200, collections: 850 },
    { id: '2', title: '可爱小猫咪', likes: 987, collections: 654 },
    { id: '3', title: '爱你么么哒', likes: 756, collections: 543 }
  ]
  
  console.log('✅ 本地分类数据:', categories)
  console.log('✅ 本地表情包数据:', emojis)
  
  return { categories, emojis }
}

// 运行测试
async function runTest() {
  console.log('=' .repeat(50))
  console.log('🧪 表情包小程序数据测试')
  console.log('=' .repeat(50))
  
  // 1. 测试云开发环境
  console.log('\n1️⃣ 测试云开发环境...')
  if (typeof wx.cloud === 'undefined') {
    console.log('❌ 云开发不可用，请升级基础库版本')
    return
  }
  console.log('✅ 云开发环境正常')
  
  // 2. 测试云函数
  console.log('\n2️⃣ 测试云函数调用...')
  const cloudSuccess = await testCloudFunction()
  
  // 3. 测试本地数据
  console.log('\n3️⃣ 测试本地数据...')
  const localData = testLocalData()
  
  // 4. 总结
  console.log('\n' + '=' .repeat(50))
  console.log('📊 测试结果总结')
  console.log('=' .repeat(50))
  
  if (cloudSuccess) {
    console.log('🎉 云函数正常，数据将从云端加载')
  } else {
    console.log('⚠️ 云函数异常，将使用本地数据')
    console.log('🔧 修复方法：')
    console.log('   1. 在微信开发者工具中点击"云开发"')
    console.log('   2. 右键点击 dataAPI 文件夹')
    console.log('   3. 选择"上传并部署：云端安装依赖"')
  }
  
  console.log('\n💡 无论如何，小程序都能正常显示内容！')
}

// 执行测试
runTest()

// 如果你想手动测试，可以在控制台运行：
// testCloudFunction()  // 测试云函数
// testLocalData()      // 测试本地数据
