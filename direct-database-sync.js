// 直接数据库同步方案 - 绕过云函数
// 在微信开发者工具控制台运行

console.log('🔄 开始直接数据库同步...')

const directSync = {
  // 初始化云开发
  async init() {
    try {
      wx.cloud.init({
        env: 'cloud1-5g6pvnpl88dc0142',
        traceUser: true
      })
      console.log('✅ 云开发初始化成功')
      return true
    } catch (error) {
      console.error('❌ 云开发初始化失败:', error)
      return false
    }
  },

  // 直接从数据库获取数据
  async getDataFromDatabase() {
    const db = wx.cloud.database()
    const results = {}

    try {
      // 获取分类数据
      console.log('📋 获取分类数据...')
      const categories = await db.collection('categories').get()
      results.categories = categories.data
      console.log(`✅ 获取到 ${categories.data.length} 个分类`)

      // 获取表情包数据
      console.log('😀 获取表情包数据...')
      const emojis = await db.collection('emojis').limit(20).get()
      results.emojis = emojis.data
      console.log(`✅ 获取到 ${emojis.data.length} 个表情包`)

      // 获取轮播图数据
      console.log('🖼️ 获取轮播图数据...')
      try {
        const banners = await db.collection('banners').get()
        results.banners = banners.data
        console.log(`✅ 获取到 ${banners.data.length} 个轮播图`)
      } catch (error) {
        console.log('⚠️ 轮播图集合不存在，跳过')
        results.banners = []
      }

      return results
    } catch (error) {
      console.error('❌ 数据库查询失败:', error)
      return null
    }
  },

  // 创建测试数据
  async createTestData() {
    const db = wx.cloud.database()

    try {
      // 创建分类数据
      console.log('📋 创建分类数据...')
      await db.collection('categories').add({
        data: [
          {
            id: 'all',
            name: '全部',
            icon: '🎭',
            order: 0,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'funny',
            name: '搞笑幽默',
            icon: '😂',
            order: 1,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'cute',
            name: '可爱萌宠',
            icon: '🐱',
            order: 2,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'love',
            name: '恋爱情感',
            icon: '💕',
            order: 3,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
      })
      console.log('✅ 分类数据创建成功')

      // 创建表情包数据
      console.log('😀 创建表情包数据...')
      await db.collection('emojis').add({
        data: [
          {
            title: '开心笑脸',
            imageUrl: 'https://picsum.photos/200/200?random=1',
            tags: ['开心', '笑脸', '表情'],
            category: 'funny',
            categoryName: '搞笑幽默',
            likes: 128,
            collections: 45,
            views: 1250,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '可爱小猫',
            imageUrl: 'https://picsum.photos/200/200?random=2',
            tags: ['可爱', '小猫', '萌宠'],
            category: 'cute',
            categoryName: '可爱萌宠',
            likes: 89,
            collections: 32,
            views: 890,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '爱心表情',
            imageUrl: 'https://picsum.photos/200/200?random=3',
            tags: ['爱心', '恋爱', '表情'],
            category: 'love',
            categoryName: '恋爱情感',
            likes: 156,
            collections: 67,
            views: 1560,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '搞笑表情',
            imageUrl: 'https://picsum.photos/200/200?random=4',
            tags: ['搞笑', '幽默', '表情'],
            category: 'funny',
            categoryName: '搞笑幽默',
            likes: 203,
            collections: 89,
            views: 2030,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            title: '萌宠表情',
            imageUrl: 'https://picsum.photos/200/200?random=5',
            tags: ['萌宠', '可爱', '动物'],
            category: 'cute',
            categoryName: '可爱萌宠',
            likes: 345,
            collections: 123,
            views: 3450,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
      })
      console.log('✅ 表情包数据创建成功')

      // 创建版本数据
      console.log('📊 创建版本数据...')
      await db.collection('data_versions').add({
        data: [
          {
            collection: 'emojis',
            version: 1,
            lastUpdated: new Date(),
            createdAt: new Date()
          },
          {
            collection: 'categories',
            version: 1,
            lastUpdated: new Date(),
            createdAt: new Date()
          }
        ]
      })
      console.log('✅ 版本数据创建成功')

      return true
    } catch (error) {
      console.error('❌ 创建测试数据失败:', error)
      if (error.errMsg && error.errMsg.includes('duplicate')) {
        console.log('💡 数据可能已存在，这是正常的')
        return true
      }
      return false
    }
  },

  // 执行完整同步
  async runFullSync() {
    console.log('🚀 开始完整数据同步...')

    // 1. 初始化
    if (!await this.init()) {
      console.error('❌ 初始化失败，无法继续')
      return false
    }

    // 2. 尝试获取现有数据
    let data = await this.getDataFromDatabase()
    
    if (!data || data.categories.length === 0) {
      console.log('📝 没有找到数据，创建测试数据...')
      
      // 3. 创建测试数据
      if (!await this.createTestData()) {
        console.error('❌ 创建测试数据失败')
        return false
      }
      
      // 4. 重新获取数据
      data = await this.getDataFromDatabase()
    }

    if (data) {
      console.log('🎉 数据同步成功！')
      console.log('📊 同步结果:')
      console.log(`   分类: ${data.categories.length} 个`)
      console.log(`   表情包: ${data.emojis.length} 个`)
      console.log(`   轮播图: ${data.banners.length} 个`)
      
      // 5. 更新应用状态
      const app = getApp()
      if (app) {
        app.globalData.cloudInitialized = true
        app.globalData.databaseInitialized = true
        console.log('✅ 应用状态已更新')
      }
      
      console.log('💡 现在重新编译小程序，应该能看到数据了！')
      return true
    } else {
      console.error('❌ 数据同步失败')
      return false
    }
  }
}

// 执行同步
directSync.runFullSync()
