<!DOCTYPE html>
<html>
<head>
    <title>图标压缩工具 - 解决40KB限制</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #fafafa;
        }
        .upload-area.dragover {
            border-color: #FF8C00;
            background: #fff5e6;
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            border: 1px solid #eee;
            margin: 5px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .file-size {
            font-size: 12px;
            color: #666;
        }
        .size-warning {
            color: #ff4757;
            font-weight: bold;
        }
        .size-ok {
            color: #2ed573;
            font-weight: bold;
        }
        .compress-btn {
            background: #FF8C00;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .download-btn {
            background: #2ed573;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .preview {
            max-width: 50px;
            max-height: 50px;
            border-radius: 4px;
        }
        h1 { color: #333; text-align: center; }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗜️ TabBar图标压缩工具</h1>
        
        <div class="warning">
            <strong>⚠️ 问题说明：</strong><br>
            微信小程序TabBar图标文件大小不能超过40KB，你的3D图标文件太大了（500-600KB）。<br>
            这个工具可以帮你压缩图标到40KB以下，同时保持视觉质量。
        </div>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽你的图标文件到这里，或点击选择文件</p>
            <input type="file" id="fileInput" multiple accept="image/png,image/jpg,image/jpeg" style="display: none;">
            <button onclick="document.getElementById('fileInput').click()" class="compress-btn">
                选择图标文件
            </button>
        </div>
        
        <div id="fileList"></div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="compressAll" class="compress-btn" style="font-size: 16px; padding: 12px 24px;" onclick="compressAllFiles()">
                🗜️ 压缩所有图标到40KB以下
            </button>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        
        // 文件上传处理
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            selectedFiles = Array.from(files);
            displayFiles();
        }
        
        function displayFiles() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const sizeKB = (file.size / 1024).toFixed(1);
                const sizeClass = file.size > 40960 ? 'size-warning' : 'size-ok';
                const sizeText = file.size > 40960 ? `${sizeKB}KB (超出限制!)` : `${sizeKB}KB (符合要求)`;
                
                fileItem.innerHTML = `
                    <div class="file-info">
                        <img class="preview" src="${URL.createObjectURL(file)}" alt="预览">
                        <div>
                            <div>${file.name}</div>
                            <div class="file-size ${sizeClass}">${sizeText}</div>
                        </div>
                    </div>
                    <button class="compress-btn" onclick="compressFile(${index})">压缩</button>
                `;
                
                fileList.appendChild(fileItem);
            });
        }
        
        function compressFile(index) {
            const file = selectedFiles[index];
            compressImage(file, (compressedBlob) => {
                const compressedSize = (compressedBlob.size / 1024).toFixed(1);
                
                // 下载压缩后的文件
                const link = document.createElement('a');
                link.href = URL.createObjectURL(compressedBlob);
                link.download = file.name.replace(/\.(png|jpg|jpeg)$/i, '_compressed.png');
                link.click();
                
                alert(`✅ 压缩完成！\n原始大小: ${(file.size/1024).toFixed(1)}KB\n压缩后: ${compressedSize}KB`);
            });
        }
        
        function compressAllFiles() {
            if (selectedFiles.length === 0) {
                alert('请先选择图标文件！');
                return;
            }
            
            let completed = 0;
            selectedFiles.forEach((file, index) => {
                compressImage(file, (compressedBlob) => {
                    // 下载压缩后的文件
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(compressedBlob);
                    link.download = file.name.replace(/\.(png|jpg|jpeg)$/i, '_compressed.png');
                    link.click();
                    
                    completed++;
                    if (completed === selectedFiles.length) {
                        alert(`🎉 所有图标压缩完成！\n请将压缩后的文件重命名为原文件名，替换images目录下的图标文件。`);
                    }
                });
            });
        }
        
        function compressImage(file, callback) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                // 设置目标尺寸 (TabBar图标推荐81x81)
                const targetSize = 81;
                canvas.width = targetSize;
                canvas.height = targetSize;
                
                // 绘制压缩后的图像
                ctx.drawImage(img, 0, 0, targetSize, targetSize);
                
                // 尝试不同的质量设置，直到文件小于40KB
                let quality = 0.8;
                
                function tryCompress() {
                    canvas.toBlob((blob) => {
                        if (blob.size <= 40960 || quality <= 0.1) {
                            // 文件大小符合要求或质量已经很低了
                            callback(blob);
                        } else {
                            // 继续降低质量
                            quality -= 0.1;
                            tryCompress();
                        }
                    }, 'image/png', quality);
                }
                
                tryCompress();
            };
            
            img.src = URL.createObjectURL(file);
        }
    </script>
</body>
</html>