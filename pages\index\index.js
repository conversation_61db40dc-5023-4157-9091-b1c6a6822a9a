// pages/index/index.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')
const { withPageState, EmojiStateHelper, PaginationMixin } = require('../../utils/pageStateMixin.js')
const { DatabaseDiagnostic } = require('../../utils/databaseDiagnostic.js')
const { UserExperience } = require('../../utils/userExperience.js')
const { ErrorHandler } = require('../../utils/errorHandler.js')
const ShareManager = require('../../utils/shareManager.js')
const { syncStatusManager } = require('../../utils/syncStatusManager.js')

const pageConfig = {
  data: {
    // 搜索相关 - 确保初始状态清空，避免阻挡主内容显示
    searchKeyword: '',
    searchSuggestions: [],
    showSuggestions: false,
    isSearching: false,
    searchResults: [], // 确保初始为空数组

    // 页面状态
    loading: true,
    showEmptyState: false,

    // 数据同步状态
    lastSyncTime: 0,
    lastSyncTimeText: '',
    syncInProgress: false,

    // Banner轮播图数据 - 初始为空，从后端实时加载
    bannerList: [],

    // 热门分类数据 - 初始为空，从后端实时加载
    hotCategories: [],

    // 表情包列表数据 - 初始为空，从后端实时加载
    emojiList: [],

    // 标准分页状态
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    loadingMore: false

  },

  onLoad() {
    console.log('🚀 首页 onLoad 开始...')
    // 在 onLoad 中只做基础初始化，不加载数据
    this.pageReady = false

    // 先检查并初始化数据库
    this.checkAndInitializeData()
  },

  async onReady() {
    console.log('🚀 首页 onReady 开始，页面渲染完成，开始加载数据...')
    this.pageReady = true

    // 使用用户体验增强的加载提示
    try {
      UserExperience.showLoading('正在加载精彩内容...', {
        key: 'page_init',
        timeout: 15000
      })
    } catch (error) {
      console.log('UserExperience 加载提示失败，使用系统提示')
      wx.showLoading({ title: '加载中...' })
    }

    try {
      // 页面渲染完成后再加载数据，确保 setData 能正常工作
      console.log('📋 步骤1: 加载分类数据')
      UserExperience.showProgress('加载分类数据...', 0.2)
      await this.loadCategoryData()

      console.log('📋 步骤2: 加载横幅数据')
      UserExperience.showProgress('加载轮播图...', 0.4)
      await this.loadBannerData()

      console.log('📋 步骤3: 加载表情包数据')
      UserExperience.showProgress('加载表情包...', 0.8)

      // 直接加载表情包数据，不使用分页管理器
      await this.loadFirstPageData()

      console.log('✅ 首页数据加载完成')
      UserExperience.hideLoading('page_init')
      UserExperience.showSuccess('加载完成')

    } catch (error) {
      console.error('❌ 页面数据加载失败:', error)
      UserExperience.hideLoading('page_init')

      // 使用错误处理器处理错误
      ErrorHandler.handleError({
        type: ErrorHandler.ERROR_TYPES.DATA,
        level: ErrorHandler.ERROR_LEVELS.ERROR,
        message: error.message || '页面数据加载失败',
        source: 'index.onReady',
        showToUser: true,
        allowRetry: true,
        retryAction: () => this.onReady()
      })
    }

    // 注册同步状态监听
    this.registerSyncStatusListener()
  },

  onShow() {
    // 同步自定义TabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }

    // 确保搜索状态清空，避免阻挡主内容
    this.clearSearchState()

    // 页面显示时检查数据同步
    this.checkAndSyncData()

    // 页面显示时预加载关键页面
    this.preloadPages()

    // 延迟更新，确保数据同步
    setTimeout(() => {
      if (this.data.emojiList && this.data.emojiList.length > 0) {
        this.updateEmojiStatus()
      }
    }, 200)
  },

  onHide() {
    // 页面隐藏时清理定时器等资源
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
      this.syncTimer = null
    }
  },

  onUnload() {
    // 页面卸载时清理所有资源
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
      this.syncTimer = null
    }

    // 清理数据同步状态
    this.setData({
      syncInProgress: false
    })

    // 移除同步状态监听
    this.unregisterSyncStatusListener()
  },

  // 实时数据更新回调方法
  onRealtimeDataUpdate(updateInfo) {
    const { type, operation, details, timestamp } = updateInfo
    console.log(`🔔 收到实时数据更新: ${type} - ${operation}`, updateInfo)

    // 根据数据类型执行相应的更新操作
    switch (type) {
      case 'categories':
        this.handleCategoriesUpdate(operation, details)
        break
      case 'emojis':
        this.handleEmojisUpdate(operation, details)
        break
      case 'banners':
        this.handleBannersUpdate(operation, details)
        break
      default:
        console.warn(`未知的数据类型: ${type}`)
    }

    // 更新最后同步时间
    this.updateLastSyncTime(timestamp)

    // 显示更新提示
    this.showUpdateNotification(type, operation)
  },

  // 处理分类数据更新
  async handleCategoriesUpdate(operation, details) {
    try {
      console.log('🔄 处理分类数据更新...')

      // 重新加载分类数据
      await this.loadCategoryData()

      console.log('✅ 分类数据更新完成')
    } catch (error) {
      console.error('❌ 处理分类数据更新失败:', error)
    }
  },

  // 处理表情包数据更新
  async handleEmojisUpdate(operation, details) {
    try {
      console.log('🔄 处理表情包数据更新...')

      // 如果当前在搜索状态，清空搜索结果
      if (this.data.searchKeyword) {
        this.clearSearchState()
      }

      // 重新加载表情包数据
      await this.loadEmojiData()

      console.log('✅ 表情包数据更新完成')
    } catch (error) {
      console.error('❌ 处理表情包数据更新失败:', error)
    }
  },

  // 处理横幅数据更新
  async handleBannersUpdate(operation, details) {
    try {
      console.log('🔄 处理横幅数据更新...')

      // 重新加载横幅数据
      await this.loadBannerData()

      console.log('✅ 横幅数据更新完成')
    } catch (error) {
      console.error('❌ 处理横幅数据更新失败:', error)
    }
  },

  // 实时连接状态变化回调
  onRealtimeConnectionChange(connectionInfo) {
    const { status, isConnected, reconnectAttempts } = connectionInfo
    console.log(`📡 实时连接状态变化: ${status}`, connectionInfo)

    // 可以在这里更新UI显示连接状态
    // 例如在页面顶部显示连接状态指示器
  },

  // 显示更新通知
  showUpdateNotification(type, operation) {
    const typeNames = {
      'categories': '分类',
      'emojis': '表情包',
      'banners': '横幅'
    }

    const operationNames = {
      'sync': '同步',
      'create': '新增',
      'update': '更新',
      'delete': '删除'
    }

    const message = `${typeNames[type] || type}${operationNames[operation] || operation}完成`

    // 显示轻量级提示
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  },

  // 更新最后同步时间
  updateLastSyncTime(timestamp) {
    const syncTime = timestamp ? new Date(timestamp) : new Date()
    const syncTimeText = this.formatSyncTime(syncTime)

    this.setData({
      lastSyncTime: syncTime.getTime(),
      lastSyncTimeText: syncTimeText
    })
  },

  // 格式化同步时间
  formatSyncTime(date) {
    const now = new Date()
    const diff = now.getTime() - date.getTime()

    if (diff < 60000) { // 1分钟内
      return '刚刚更新'
    } else if (diff < 3600000) { // 1小时内
      const minutes = Math.floor(diff / 60000)
      return `${minutes}分钟前更新`
    } else if (diff < 86400000) { // 24小时内
      const hours = Math.floor(diff / 3600000)
      return `${hours}小时前更新`
    } else {
      return date.toLocaleDateString() + ' 更新'
    }
  },

  // 清空搜索状态，确保主内容正常显示
  clearSearchState() {
    if (this.data.searchResults.length > 0 || this.data.searchKeyword || this.data.showSuggestions) {
      console.log('🧹 清空搜索状态，确保主内容显示')
      this.setData({
        searchResults: [],
        searchKeyword: '',
        searchSuggestions: [],
        showSuggestions: false,
        isSearching: false
      })
    }
  },

  // 检查并同步数据
  async checkAndSyncData() {
    if (this.data.syncInProgress) {
      return
    }

    const now = Date.now()
    const timeSinceLastSync = now - this.data.lastSyncTime
    const SYNC_INTERVAL = 5000 // 5秒同步一次，更及时

    // 如果距离上次同步超过30秒，或者数据为空，则进行同步
    const needSync = timeSinceLastSync > SYNC_INTERVAL ||
                     this.data.hotCategories.length === 0 ||
                     this.data.emojiList.length === 0

    if (needSync) {
      console.log('🔄 触发数据同步检查...')
      await this.syncDataFromBackend()
    }
  },

  // 从后端同步数据
  async syncDataFromBackend() {
    if (this.data.syncInProgress) {
      console.log('⏳ 数据同步进行中，跳过...')
      return
    }

    try {
      this.setData({ syncInProgress: true })
      console.log('🚀 开始后端数据同步...')

      // 并行加载所有数据
      const [categoryResult, emojiResult, bannerResult] = await Promise.allSettled([
        this.loadCategoryData(),
        this.loadEmojiData(),
        this.loadBannerData()
      ])

      // 检查结果
      const successCount = [categoryResult, emojiResult, bannerResult]
        .filter(result => result.status === 'fulfilled').length

      console.log(`✅ 数据同步完成: ${successCount}/3 成功`)

      // 更新同步时间
      const now = Date.now()
      this.setData({
        lastSyncTime: now,
        lastSyncTimeText: new Date(now).toLocaleTimeString(),
        loading: false
      })

      // 如果有失败的，记录错误但不阻断
      [categoryResult, emojiResult, bannerResult].forEach((result, index) => {
        if (result.status === 'rejected') {
          const dataTypes = ['分类', '表情包', '轮播图']
          console.warn(`⚠️ ${dataTypes[index]}数据同步失败:`, result.reason)
        }
      })

      // 如果启用自动同步，设置下次同步定时器
      this.scheduleNextSync()

    } catch (error) {
      console.error('❌ 数据同步失败:', error)
    } finally {
      this.setData({ syncInProgress: false })
    }
  },

  // 安排下次自动同步
  scheduleNextSync() {
    // 清除现有定时器
    if (this.syncTimer) {
      clearTimeout(this.syncTimer)
    }

    // 设置下次同步（5秒后）
    this.syncTimer = setTimeout(() => {
      console.log('⏰ 定时自动同步触发')
      this.checkAndSyncData()
    }, 5000) // 5秒，更及时的同步

    console.log('⏰ 已安排下次自动同步（5秒后）')
  },

  // 验证数据完整性
  validateDataIntegrity() {
    const issues = []

    // 检查分类数据
    if (!this.data.hotCategories || this.data.hotCategories.length === 0) {
      issues.push('分类数据为空')
    } else {
      this.data.hotCategories.forEach((category, index) => {
        if (!category.id || !category.name) {
          issues.push(`分类${index + 1}缺少必要字段`)
        }
      })
    }

    // 检查表情包数据
    if (!this.data.emojiList || this.data.emojiList.length === 0) {
      issues.push('表情包数据为空')
    } else {
      this.data.emojiList.forEach((emoji, index) => {
        if (!emoji.id || !emoji.title || !emoji.imageUrl) {
          issues.push(`表情包${index + 1}缺少必要字段`)
        }
      })
    }

    // 检查轮播图数据
    if (!this.data.bannerList || this.data.bannerList.length === 0) {
      issues.push('轮播图数据为空')
    } else {
      this.data.bannerList.forEach((banner, index) => {
        if (!banner.id || !banner.title || !banner.imageUrl) {
          issues.push(`轮播图${index + 1}缺少必要字段`)
        }
      })
    }

    if (issues.length > 0) {
      console.warn('⚠️ 数据完整性检查发现问题:', issues)
      return { valid: false, issues }
    }

    console.log('✅ 数据完整性检查通过')
    return { valid: true, issues: [] }
  },

  // 获取数据同步状态信息
  getDataSyncStatus() {
    const now = Date.now()
    const timeSinceLastSync = this.data.lastSyncTime ? now - this.data.lastSyncTime : 0

    return {
      syncInProgress: this.data.syncInProgress,
      lastSyncTime: this.data.lastSyncTime,
      timeSinceLastSync,
      dataIntegrity: this.validateDataIntegrity(),
      dataCounts: {
        categories: this.data.hotCategories.length,
        emojis: this.data.emojiList.length,
        banners: this.data.bannerList.length
      }
    }
  },

  async loadCategoryData() {
    try {
      console.log('🔄 加载分类数据...')

      // 首先尝试云函数
      let categories = []
      let dataSource = 'unknown'

      try {
        console.log('📋 开始调用云函数获取分类数据...')
        const result = await wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'getCategories' }
        })

        console.log('云函数分类数据结果:', result)

        // 检查返回结果的结构
        if (!result) {
          console.error('❌ 云函数返回结果为空')
          throw new Error('云函数返回结果为空')
        }

        if (!result.result) {
          console.error('❌ 云函数返回结果中没有result字段:', result)
          throw new Error('云函数返回格式错误')
        }

        if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
          categories = result.result.data
          dataSource = 'cloud'
          console.log('✅ 从云函数获取到分类数据:', categories.length, '个')
        }
      } catch (cloudError) {
        console.warn('⚠️ 云函数调用失败，尝试本地数据:', cloudError.message)

        // 检查是否是云函数不存在的错误
        if (cloudError.errCode === -501000 || cloudError.message?.includes('FunctionName parameter could not be found')) {
          console.log('💡 提示：请确保云函数已正确部署')
          // 在开发环境显示提示
          if (typeof getApp === 'function') {
            const app = getApp()
            if (app.globalData?.isDevelopment !== false) {
              wx.showToast({
                title: '云函数未部署，使用本地数据',
                icon: 'none',
                duration: 3000
              })
            }
          }
        }
      }

      // 如果没有后端数据，不显示分类
      if (categories.length === 0) {
        console.log('⚠️ 后端未配置分类数据，不显示分类模块')
        dataSource = 'none'
      }

      if (categories.length > 0) {
        // 显示前4个分类
        const hotCategories = categories.slice(0, 4).map((category, index) => ({
          ...category,
          id: category._id || category.id || `category_${index}`,
          name: category.name || '未知分类',
          icon: category.icon || '📁',
          gradient: category.gradient || this.getCategoryGradient(category.name || category._id || category.id, index),
          count: category.emojiCount || 0,
          emojiCount: category.emojiCount || 0,
          dataSource // 标记数据来源
        }))

        console.log(`处理后的热门分类 (${dataSource}):`, hotCategories)

        // 确保页面已经准备好再执行 setData
        if (this.pageReady !== false) {
          this.setData({
            hotCategories: hotCategories
          })
          console.log('✅ 分类数据设置完成，当前页面数据:', this.data.hotCategories)
        } else {
          console.warn('⚠️ 页面未准备好，跳过 setData')
        }
      } else {
        console.warn('⚠️ 无法获取分类数据')
        if (this.pageReady !== false) {
          this.setData({
            hotCategories: []
          })
        }
      }
    } catch (error) {
      console.error('❌ 加载分类数据失败:', error)
      if (this.pageReady !== false) {
        this.setData({
          hotCategories: []
        })
      }
    }
  },

  getCategoryGradient(categoryName, index = 0) {
    // 使用与分类页面一致的渐变色格式
    const gradientMap = {
      '搞笑幽默': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      '可爱萌宠': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      '情感表达': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      '节日庆典': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      '网络热梗': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      '动漫二次元': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      'funny': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'cute': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'emotion': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'festival': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      'hot': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      '2d': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
    }

    // 如果没有匹配的分类名称，使用索引来分配渐变色
    if (gradientMap[categoryName]) {
      return gradientMap[categoryName]
    }

    // 备用渐变色数组，与分类页面保持一致
    const fallbackGradients = [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
      'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
      'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)',
      'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    ]

    return fallbackGradients[index % fallbackGradients.length]
  },

  async loadBannerData() {
    try {
      console.log('🔄 加载横幅数据...')

      // 首先尝试云函数
      let banners = []
      let dataSource = 'unknown'

      try {
        const result = await wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'getBanners' }
        })

        console.log('云函数横幅数据结果:', result)

        if (result.result && result.result.success && result.result.data && result.result.data.length > 0) {
          banners = result.result.data
          dataSource = 'cloud'
          console.log('✅ 从云函数获取到横幅数据:', banners.length, '个')
        }
      } catch (cloudError) {
        console.warn('⚠️ 云函数调用失败，使用本地数据:', cloudError.message)

        // 检查是否是云函数不存在的错误
        if (cloudError.errCode === -501000 || cloudError.message?.includes('FunctionName parameter could not be found')) {
          console.log('💡 提示：请确保云函数已正确部署')
        }
      }

      // 如果没有后端数据，不显示横幅
      if (banners.length === 0) {
        console.log('⚠️ 后端未配置横幅数据，不显示横幅模块')
        dataSource = 'none'
      }

      if (banners.length > 0) {
        // 确保横幅数据格式正确
        const bannerList = banners.map((banner, index) => ({
          ...banner,
          id: banner._id || banner.id || `banner_${index}`,
          title: banner.title || '横幅标题',
          subtitle: banner.subtitle || banner.description || '横幅描述',
          buttonText: banner.buttonText || '查看详情',
          imageUrl: banner.imageUrl || '',
          backgroundColor: banner.backgroundColor || '#999999',
          dataSource // 标记数据来源
        }))

        console.log(`处理后的横幅数据 (${dataSource}):`, bannerList)

        // 确保页面已经准备好再执行 setData
        if (this.pageReady !== false) {
          this.setData({
            bannerList: bannerList
          })
          console.log('✅ 横幅数据设置完成，当前页面数据:', this.data.bannerList)
        } else {
          console.warn('⚠️ 页面未准备好，跳过 setData')
        }
      } else {
        console.warn('⚠️ 无法获取横幅数据')
        if (this.pageReady !== false) {
          this.setData({
            bannerList: []
          })
        }
      }
    } catch (error) {
      console.error('❌ 加载横幅数据失败:', error)
      if (this.pageReady !== false) {
        this.setData({
          bannerList: []
        })
      }
    }
  },

  initializeData() {
    // 使用全局数据管理器获取表情包数据
    const allEmojis = DataManager.getAllEmojiData()

    // 只显示前8个表情包作为首页推荐
    const featuredEmojis = allEmojis.slice(0, 8)

    const emojiList = featuredEmojis.map(emoji => {
      // 确保ID字段正确（数据库使用_id，前端使用id）
      const emojiId = emoji._id || emoji.id
      const emojiState = StateManager.getEmojiState(emojiId)
      return {
        ...emoji,
        id: emojiId, // 统一使用id字段
        likesText: DataManager.formatNumber(emoji.likes),
        collectionsText: DataManager.formatNumber(emoji.collections),
        isLiked: emojiState.isLiked,
        isCollected: emojiState.isCollected
      }
    })

    this.setData({ emojiList })
    console.log('首页表情包数据:', emojiList)
  },

  // 加载第一页数据的简化方法
  async loadFirstPageData() {
    try {
      console.log('🔄 加载第一页表情包数据...')

      // 直接调用云函数获取数据
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: 'all',
            page: 1,
            limit: 20
          }
        }
      })

      console.log('☁️ 首页云函数调用结果:', result.result)

      if (result.result && result.result.success && result.result.data) {
        // 直接设置数据到页面
        this.setData({
          emojiList: result.result.data
        })

        console.log(`✅ 首页数据加载成功，获取 ${result.result.data.length} 条数据`)
        console.log('📋 表情包列表:', result.result.data)

        // 调试：检查页面数据状态
        console.log('🔍 页面数据状态检查:')
        console.log('- emojiList.length:', this.data.emojiList.length)
        console.log('- searchResults.length:', this.data.searchResults.length)
        console.log('- 显示条件:', this.data.searchResults.length === 0 && this.data.emojiList.length > 0)
      } else {
        console.error('❌ 首页数据加载失败:', result.result)
        throw new Error(result.result?.message || '获取数据失败')
      }
    } catch (error) {
      console.error('❌ 首页数据加载异常:', error)
      throw error
    }
  },

  async loadEmojiData() {
    try {
      console.log('🔄 初始加载表情包数据...')

      // 重置分页状态
      this.setData({
        currentPage: 1,
        hasMore: true,
        loadingMore: false
      })

      // 调用云函数获取第一页数据
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: 'all',
            page: 1,
            limit: this.data.pageSize
          }
        }
      })

      console.log('☁️ 云函数调用结果:', result.result)

      if (result.result && result.result.success && result.result.data) {
        // 设置数据和分页状态
        this.setData({
          emojiList: result.result.data,
          hasMore: result.result.hasMore,
          currentPage: 1
        })

        console.log(`✅ 初始数据加载成功，获取 ${result.result.data.length} 条数据`)
        return
      }

    } catch (error) {
      console.error('❌ 初始数据加载失败:', error)

      // 设置空状态
      this.setData({
        emojiList: [],
        hasMore: false
      })

      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    }
  },

  // 简单的数字格式化函数
  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + 'w'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k'
    }
    return num.toString()
  },

  // 初始化数据（如果需要）
  async initDataIfNeeded() {
    try {
      console.log('🔍 检查是否需要初始化数据...')

      // 检查是否有数据（使用更安全的方式）
      let categories = []
      let emojis = []

      try {
        categories = await DataManager.getCategoriesWithStats({ forceRefresh: true })
        console.log('📊 分类数据检查结果:', categories.length, '个')
      } catch (error) {
        console.warn('分类数据检查失败:', error)
      }

      try {
        emojis = await DataManager.getAllEmojiData('all', 1, 1, { forceRefresh: true })
        console.log('📦 表情包数据检查结果:', emojis.length, '个')
      } catch (error) {
        console.warn('表情包数据检查失败:', error)
      }

      // 自动初始化已禁用 - 防止创建虚拟测试数据
      if (categories.length === 0 || emojis.length === 0) {
        console.log('⚠️ 检测到无数据，但自动初始化已禁用')
        console.log('📝 请使用管理后台手动添加分类和表情包数据')
        wx.showToast({
          title: '请使用管理后台添加数据',
          icon: 'none',
          duration: 3000
        })
      } else {
        console.log('✅ 数据检查完成，已有数据')
      }
    } catch (error) {
      console.error('❌ 数据初始化检查失败:', error)
      // 不阻断后续流程
    }
  },

  loadBanners() {
    wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getBanners' }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          bannerList: res.result.data
        })
      }
    }).catch(err => {
      console.error('获取轮播图失败:', err)
    })
  },

  loadEmojis() {
    wx.showLoading({ title: '加载中...' })
    wx.cloud.callFunction({
      name: 'dataAPI',
      data: {
        action: 'getEmojis',
        data: {
          category: 'all',
          page: 1,
          limit: 3
        }
      }
    }).then(res => {
      wx.hideLoading()
      console.log('表情包数据加载结果:', res)
      if (res.result && res.result.success) {
        this.setData({
          emojiList: res.result.data || []
        })
        console.log('✅ 表情包列表更新成功:', res.result.data?.length || 0, '条')
        this.updateEmojiStatus()
      } else {
        console.error('❌ 表情包数据加载失败:', res.result?.message || '未知错误')
        wx.showToast({
          title: res.result?.message || '加载失败',
          icon: 'error'
        })
      }
    }).catch(err => {
      wx.hideLoading()
      console.error('获取表情包列表失败:', err)
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      })
    })
  },

  updateEmojiStatus() {
    try {
      let hasChanges = false

      const emojiList = this.data.emojiList.map(emoji => {
        // 使用StateManager获取最新状态
        const emojiState = StateManager.getEmojiState(emoji._id || emoji.id)

        // 只有状态真正改变时才标记需要更新
        if (emoji.isLiked !== emojiState.isLiked || emoji.isCollected !== emojiState.isCollected) {
          hasChanges = true
        }

        return {
          ...emoji,
          isLiked: emojiState.isLiked,
          isCollected: emojiState.isCollected,
          likesText: this.formatNumber(emoji.likes),
          collectionsText: this.formatNumber(emoji.collections)
        }
      })

      // 只在有实际变化时才调用setData
      if (hasChanges) {
        this.setData({ emojiList })
        console.log('✅ 首页状态已从StateManager更新')
      }
    } catch (error) {
      console.error('首页读取本地存储失败:', error)
    }
  },

  // 格式化数字显示
  formatNumber(num) {
    if (num > 10000) {
      return (num / 10000).toFixed(1) + 'w'
    }
    return num.toString()
  },

  // 搜索输入事件
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })

    // 实时搜索建议
    if (keyword.trim()) {
      this.showSearchSuggestions(keyword)
    } else {
      this.hideSearchSuggestions()
    }
  },

  // 搜索确认事件
  onSearchConfirm() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }

    // 隐藏搜索建议
    this.hideSearchSuggestions()

    // 执行搜索
    this.performSearch(keyword)
  },

  // 搜索框获得焦点事件
  onSearchFocus() {
    const keyword = this.data.searchKeyword.trim()
    if (keyword) {
      this.showSearchSuggestions(keyword)
    }
  },

  // 搜索框失去焦点事件
  onSearchBlur() {
    // 延迟隐藏建议，避免点击建议时立即隐藏
    setTimeout(() => {
      this.hideSearchSuggestions()
    }, 200)
  },

  // 显示搜索建议
  showSearchSuggestions(keyword) {
    // 基于实际数据的搜索建议
    const allSuggestions = [
      '哈哈哈笑死我了', '搞笑', '搞笑幽默', '笑死',
      '可爱小猫咪', '可爱', '可爱萌宠', '猫咪', '萌宠',
      '爱你么么哒', '爱情', '情感表达', '表白', '么么哒',
      '新年快乐', '新年', '节日庆典', '快乐', '庆祝'
    ]

    const suggestions = allSuggestions.filter(item =>
      item.includes(keyword)
    ).slice(0, 5)

    this.setData({
      searchSuggestions: suggestions,
      showSuggestions: suggestions.length > 0
    })
  },

  // 隐藏搜索建议
  hideSearchSuggestions() {
    this.setData({
      showSuggestions: false,
      searchSuggestions: []
    })
  },

  // 点击搜索建议
  onSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion
    this.setData({
      searchKeyword: suggestion
    })
    this.performSearch(suggestion)
  },

  // 执行搜索
  async performSearch(keyword) {
    console.log('首页搜索:', keyword)

    if (!keyword.trim()) {
      UserExperience.showWarning('请输入搜索关键词')
      return
    }

    this.setData({
      isSearching: true
    })

    // 使用用户体验增强的搜索流程
    UserExperience.showLoading('正在搜索...', {
      key: 'search',
      timeout: 8000
    })

    try {
      // 模拟搜索延迟（实际项目中这里会调用真实的搜索API）
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟搜索结果
      const mockResults = this.data.emojiList.filter(item =>
        item.title.includes(keyword) ||
        item.category.includes(keyword) ||
        item.tags.some(tag => tag.includes(keyword))
      )

      this.setData({
        searchResults: mockResults,
        isSearching: false
      })

      UserExperience.hideLoading('search')

      // 保存搜索历史
      this.saveSearchHistory(keyword)

      // 显示搜索结果提示
      if (mockResults.length > 0) {
        UserExperience.showSuccess(`找到 ${mockResults.length} 个相关表情包`)
      } else {
        UserExperience.showInfo('未找到相关内容，试试其他关键词吧')

        // 提供搜索建议
        this.showSearchSuggestions()
      }

    } catch (error) {
      console.error('❌ 搜索失败:', error)
      UserExperience.hideLoading('search')

      this.setData({
        isSearching: false
      })

      // 使用错误处理器
      ErrorHandler.handleError({
        type: ErrorHandler.ERROR_TYPES.DATA,
        level: ErrorHandler.ERROR_LEVELS.ERROR,
        message: '搜索失败，请重试',
        source: 'index.performSearch',
        showToUser: true,
        allowRetry: true,
        retryAction: () => this.performSearch(keyword)
      })
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return

    let history = wx.getStorageSync('searchHistory') || []
    history = history.filter(item => item !== keyword)
    history.unshift(keyword)
    history = history.slice(0, 10)

    wx.setStorageSync('searchHistory', history)
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showSuggestions: false,
      searchSuggestions: []
    })
  },

  // 搜索框点击事件（保留兼容性）
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // Banner点击事件
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner
    if (banner.linkUrl) {
      wx.navigateTo({
        url: banner.linkUrl
      })
    }
  },

  // 分类点击事件
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    console.log('点击分类:', category)

    if (category && (category._id || category.id)) {
      // 使用正确的分类ID（_id 是数据库中的真实ID）
      const categoryId = category._id || category.id
      wx.navigateTo({
        url: `/pages/category-detail/category-detail?id=${encodeURIComponent(categoryId)}&name=${encodeURIComponent(category.name)}`
      })
    } else {
      console.error('分类数据不完整:', category)
      wx.showToast({
        title: '分类数据错误',
        icon: 'error'
      })
    }
  },

  // 查看全部分类
  onViewAllCategories() {
    console.log('点击查看全部分类')
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 表情包点击事件
  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    console.log('🔄 点击表情包:', emoji)

    // 获取正确的ID字段（数据库使用_id，前端使用id）
    const emojiId = emoji._id || emoji.id

    if (!emoji || !emojiId) {
      console.error('❌ 表情包数据无效:', emoji)
      UserExperience.showError('表情包数据错误，请重试')
      return
    }

    console.log('🔄 跳转到详情页:', emojiId)

    // 使用用户体验增强的页面跳转
    UserExperience.wrapWithUX(
      async () => {
        await new Promise((resolve, reject) => {
          wx.navigateTo({
            url: `/pages/detail/detail-new?id=${emojiId}`,
            success: resolve,
            fail: reject
          })
        })
      },
      {
        loadingTitle: '正在打开...',
        successMessage: '',
        showSuccess: false,
        showError: true
      }
    )().catch(error => {
      console.error('❌ 页面跳转失败:', error)
      UserExperience.showError('页面打开失败，请重试')
    })
  },



  // 点赞功能
  onToggleLike(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }

    const emojiId = e.currentTarget.dataset.id
    const emojiIndex = this.data.emojiList.findIndex(item => item.id === emojiId)

    if (emojiIndex === -1) {
      UserExperience.showWarning('表情包不存在')
      return
    }

    try {
      // 使用StateManager切换点赞状态
      const newIsLiked = StateManager.toggleLike(emojiId)

      const emoji = this.data.emojiList[emojiIndex]
      const newLikes = newIsLiked ? emoji.likes + 1 : Math.max(0, emoji.likes - 1)

      // 更新全局数据
      DataManager.updateLikes(emojiId, newLikes)

      // 更新页面数据
      this.setData({
        [`emojiList[${emojiIndex}].isLiked`]: newIsLiked,
        [`emojiList[${emojiIndex}].likes`]: newLikes
      })

      // 使用用户体验增强的提示
      if (newIsLiked) {
        UserExperience.showSuccess('👍 点赞成功')
      } else {
        UserExperience.showInfo('已取消点赞')
      }

      console.log(`👍 主页点赞操作: ${emojiId} → ${newIsLiked}`)

    } catch (error) {
      console.error('❌ 主页点赞操作失败:', error)

      // 使用错误处理器
      ErrorHandler.handleError({
        type: ErrorHandler.ERROR_TYPES.DATA,
        level: ErrorHandler.ERROR_LEVELS.WARNING,
        message: '点赞操作失败，请重试',
        source: 'index.onToggleLike',
        showToUser: true,
        allowRetry: true,
        retryAction: () => this.onToggleLike(e)
      })
    }
  },

  // 收藏功能
  onToggleCollect(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }

    const emojiId = e.currentTarget.dataset.id
    const emojiIndex = this.data.emojiList.findIndex(item => item.id === emojiId)

    if (emojiIndex === -1) {
      UserExperience.showWarning('表情包不存在')
      return
    }

    try {
      // 使用StateManager切换收藏状态
      const newIsCollected = StateManager.toggleCollect(emojiId)

      const emoji = this.data.emojiList[emojiIndex]
      const newCollections = newIsCollected ? emoji.collections + 1 : Math.max(0, emoji.collections - 1)

      // 更新全局数据
      DataManager.updateCollections(emojiId, newCollections)

      // 更新页面数据
      this.setData({
        [`emojiList[${emojiIndex}].isCollected`]: newIsCollected,
        [`emojiList[${emojiIndex}].collections`]: newCollections
      })

      // 使用用户体验增强的提示
      if (newIsCollected) {
        UserExperience.showSuccess('⭐ 收藏成功')
      } else {
        UserExperience.showInfo('已取消收藏')
      }

      console.log(`⭐ 主页收藏操作: ${emojiId} → ${newIsCollected}`)

    } catch (error) {
      console.error('❌ 主页收藏操作失败:', error)

      // 使用错误处理器
      ErrorHandler.handleError({
        type: ErrorHandler.ERROR_TYPES.DATA,
        level: ErrorHandler.ERROR_LEVELS.WARNING,
        message: '收藏操作失败，请重试',
        source: 'index.onToggleCollect',
        showToUser: true,
        allowRetry: true,
        retryAction: () => this.onToggleCollect(e)
      })
    }
  },

  onShareAppMessage() {
    return {
      title: '超好玩的表情包小程序',
      path: '/pages/index/index'
    }
  },

  /**
   * 预加载关键页面，提升性能
   */
  preloadPages() {
    // 检查是否支持 wx.preloadPage API
    if (!wx.preloadPage) {
      console.log('⚠️ 当前环境不支持 wx.preloadPage，跳过预加载')
      return
    }

    try {
      // 预加载常用页面，提高打开速度
      wx.preloadPage({
        url: '/pages/search/search',
        complete: () => {
          console.log('✅ 预加载搜索页面完成')
        }
      })

      wx.preloadPage({
        url: '/pages/category/category',
        complete: () => {
          console.log('✅ 预加载分类页面完成')
        }
      })

      // 预加载热门分类详情页
      if (this.data.hotCategories && this.data.hotCategories.length > 0) {
        const hotCategory = this.data.hotCategories[0]
        wx.preloadPage({
          url: `/pages/category-detail/category-detail?id=${hotCategory.id}`,
          complete: () => {
            console.log(`✅ 预加载热门分类页面完成: ${hotCategory.name}`)
          }
        })
      }
    } catch (error) {
      console.error('❌ 预加载页面失败:', error)
    }
  },

  // 跳转到测试页面
  goToTest() {
    wx.navigateTo({
      url: '/pages/test/test'
    })
  },

  // 直接初始化数据库
  async debugCloudFunction() {
    wx.showLoading({ title: '正在初始化数据库...' })

    try {
      console.log('🚀 开始强制初始化数据库...')

      // 调用 dataAPI 的强制初始化功能
      console.log('📋 调用强制初始化数据库...')
      const initResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'forceInitDatabase'
        }
      })

      console.log('初始化结果:', initResult)

      if (initResult.result && initResult.result.success) {
        console.log('✅ 数据库初始化成功')
        wx.showToast({
          title: '数据库初始化成功！',
          icon: 'success',
          duration: 2000
        })

        // 等待一下，然后重新加载数据
        setTimeout(async () => {
          await this.loadAllData()
        }, 1000)

      } else {
        console.error('❌ 数据库初始化失败:', initResult.result?.message)
        console.error('❌ 详细错误信息:', initResult.result?.errors)

        // 显示详细错误信息
        const errorMsg = initResult.result?.message || '未知错误'
        wx.showModal({
          title: '初始化失败',
          content: errorMsg,
          showCancel: false,
          confirmText: '确定'
        })
      }

    } catch (error) {
      console.error('❌ 初始化失败:', error)
      wx.showToast({
        title: '初始化失败: ' + error.message,
        icon: 'error',
        duration: 3000
      })
    }

    wx.hideLoading()
  },

  // 检查并自动初始化数据
  async checkAndInitializeData() {
    try {
      console.log('🔍 开始检查数据库连接...')

      // 首先测试云函数连接
      console.log('🔍 测试云函数连接...')
      const pingResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })

      console.log('云函数连接测试结果:', pingResult)

      if (!pingResult.result || !pingResult.result.success) {
        console.error('❌ 云函数连接失败')
        return
      }

      console.log('✅ 云函数连接正常，开始检查数据...')

      // 检查分类数据
      console.log('🔍 检查分类数据...')
      const categoryResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })

      console.log('分类检查结果:', categoryResult)

      const hasData = categoryResult.result?.success &&
                     categoryResult.result?.data &&
                     categoryResult.result.data.length > 0

      console.log('数据检查结果:', {
        hasData,
        categoryCount: categoryResult.result?.data?.length || 0
      })

      // 如果没有分类数据，说明需要初始化
      if (!hasData) {
        console.log('🚀 检测到无数据，开始自动初始化数据库...')

        // 显示初始化提示
        wx.showLoading({ title: '首次使用，正在初始化数据...' })

        const initResult = await wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'forceInitDatabase' }
        })

        console.log('自动初始化结果:', initResult)

        if (initResult.result?.success) {
          console.log('✅ 数据库自动初始化成功')
          wx.showToast({
            title: '数据初始化完成！',
            icon: 'success',
            duration: 2000
          })

          // 初始化成功后，清除所有缓存并等待一下再继续
          const DataManager = require('../../utils/newDataManager.js').DataManager
          DataManager.clearCache('all')
          await new Promise(resolve => setTimeout(resolve, 1000))

        } else {
          console.error('❌ 数据库自动初始化失败:', initResult.result?.message)
          console.error('❌ 初始化错误详情:', initResult.result?.errors)

          wx.showModal({
            title: '初始化失败',
            content: '数据初始化失败，请点击"一键初始化数据库"按钮手动初始化',
            showCancel: false,
            confirmText: '确定'
          })
        }

        wx.hideLoading()
      } else {
        console.log('✅ 数据检查完成，已有数据无需初始化')
      }

    } catch (error) {
      console.error('❌ 数据检查失败:', error)
      console.log('❌ 将跳过自动初始化，用户可手动初始化')
    }
  },

  // 重新加载所有数据
  async loadAllData() {
    wx.showLoading({ title: '加载数据中...' })

    try {
      // 清除缓存
      const DataManager = require('../../utils/newDataManager.js').DataManager
      DataManager.clearCache()

      // 重新加载数据
      await this.loadCategoryData()
      await this.loadBannerData()
      await this.loadEmojiData()

      console.log('✅ 所有数据重新加载完成')
      wx.showToast({
        title: '数据加载完成！',
        icon: 'success'
      })

    } catch (error) {
      console.error('❌ 数据加载失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    }

    wx.hideLoading()
  },

  // 检查数据状态（调试用）
  async checkDataStatus() {
    wx.showLoading({ title: '检查中...' })

    try {
      console.log('🔍 手动检查数据状态...')

      // 检查分类数据
      const categoryResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })

      // 检查表情包数据
      const emojiResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 5 } }
      })

      // 检查横幅数据
      const bannerResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      })

      const categoryCount = categoryResult.result?.data?.length || 0
      const emojiCount = emojiResult.result?.data?.length || 0
      const bannerCount = bannerResult.result?.data?.length || 0

      console.log('📊 数据状态检查结果:')
      console.log('- 分类数据:', categoryCount, '个')
      console.log('- 表情包数据:', emojiCount, '个')
      console.log('- 横幅数据:', bannerCount, '个')

      const statusText = `数据状态检查结果:\n分类: ${categoryCount} 个\n表情包: ${emojiCount} 个\n横幅: ${bannerCount} 个`

      wx.showModal({
        title: '数据状态',
        content: statusText,
        showCancel: false,
        confirmText: '确定'
      })

    } catch (error) {
      console.error('❌ 检查数据状态失败:', error)
      wx.showModal({
        title: '检查失败',
        content: '检查数据状态失败: ' + error.message,
        showCancel: false,
        confirmText: '确定'
      })
    }

    wx.hideLoading()
  },

  // 测试云函数数据（调试用）
  async testCloudFunctionData() {
    wx.showLoading({ title: '测试数据...' })

    try {
      console.log('🧪 开始测试云函数数据...')

      // 测试分类数据
      console.log('📋 测试分类数据...')
      const categoriesResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      })
      console.log('分类数据结果:', categoriesResult)

      // 测试表情包数据
      console.log('📋 测试表情包数据...')
      const emojisResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: { category: 'all', page: 1, limit: 10 }
        }
      })
      console.log('表情包数据结果:', emojisResult)

      // 测试横幅数据
      console.log('📋 测试横幅数据...')
      const bannersResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      })
      console.log('横幅数据结果:', bannersResult)

      wx.showModal({
        title: '测试完成',
        content: '请查看控制台日志，数据已打印',
        showCancel: false
      })

    } catch (error) {
      console.error('❌ 测试云函数数据失败:', error)
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      })
    }

    wx.hideLoading()
  },

  // 数据库诊断（调试用）
  async runDatabaseDiagnostic() {
    wx.showLoading({ title: '诊断中...' })

    try {
      console.log('🔍 开始数据库诊断...')
      const results = await DatabaseDiagnostic.runFullDiagnostic()
      DatabaseDiagnostic.showDiagnosticResult(results)
    } catch (error) {
      console.error('❌ 数据库诊断失败:', error)
      wx.showModal({
        title: '诊断失败',
        content: '数据库诊断失败: ' + error.message,
        showCancel: false,
        confirmText: '确定'
      })
    }

    wx.hideLoading()
  },



  // ========== 状态管理相关方法 ==========

  /**
   * 表情包状态变更回调
   * @param {Object} data - 状态变更数据
   */
  onEmojiStateChange(data) {
    console.log('📄 首页接收到状态变更:', data)

    // 可以在这里添加页面特定的状态变更处理逻辑
    switch (data.type) {
      case 'like':
        // 点赞状态变更时的特殊处理
        break
      case 'collect':
        // 收藏状态变更时的特殊处理
        break
      case 'download':
        // 下载状态变更时的特殊处理
        break
    }
  },

  // 添加状态操作辅助方法
  ...EmojiStateHelper,

  // ========== 分页加载相关方法 ==========

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    console.log('🔄 下拉刷新')
    if (this.paginationInstance) {
      await this.paginationInstance.refresh()
    }
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多 - 标准实现
   */
  async onReachBottom() {
    console.log('📄 上拉加载更多')

    // 防止重复加载
    if (this.data.loadingMore || !this.data.hasMore) {
      console.log('⚠️ 无法加载更多:', { loadingMore: this.data.loadingMore, hasMore: this.data.hasMore })
      return
    }

    this.setData({ loadingMore: true })

    try {
      const nextPage = this.data.currentPage + 1
      console.log(`📄 加载第 ${nextPage} 页数据`)

      // 直接调用云函数
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: {
            category: 'all',
            page: nextPage,
            limit: this.data.pageSize
          }
        }
      })

      console.log('☁️ 云函数调用结果:', result.result)

      if (result.result && result.result.success && result.result.data.length > 0) {
        // 拼接新数据
        const newEmojiList = [...this.data.emojiList, ...result.result.data]

        this.setData({
          emojiList: newEmojiList,
          currentPage: nextPage,
          hasMore: result.result.hasMore,
          loadingMore: false
        })

        console.log(`✅ 加载成功，新增 ${result.result.data.length} 条数据，总计 ${newEmojiList.length} 条`)
      } else {
        // 没有更多数据
        this.setData({
          hasMore: false,
          loadingMore: false
        })
        wx.showToast({
          title: '已加载全部数据',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('❌ 加载更多失败:', error)
      this.setData({ loadingMore: false })
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'error'
      })
    }
  },

  /**
   * 分页数据加载回调（重写以适配首页）
   * @param {Object} result - 加载结果
   */
  onPaginationDataLoad(result) {
    const { type, data, pagination } = result

    console.log(`📄 首页分页数据更新: ${type}, 数据量: ${data.length}`)

    // 更新页面数据
    this.setData({
      emojiList: data,
      pagination: {
        hasMore: pagination.hasMore,
        loading: pagination.loading,
        refreshing: pagination.refreshing
      }
    })

    // 更新表情包状态
    if (this.updateEmojiListState) {
      this.updateEmojiListState()
    }
  },



  // 调试：查看当前数据状态
  debugCurrentData() {
    console.log('🔍 当前页面数据状态:')
    console.log('- 表情包列表:', this.data.emojiList.length, '个')
    console.log('- 热门分类:', this.data.hotCategories.length, '个')
    console.log('- 横幅列表:', this.data.bannerList.length, '个')

    if (this.data.emojiList.length > 0) {
      console.log('- 第一个表情包:', this.data.emojiList[0])
    }

    // 检查DataManager缓存
    console.log('📦 DataManager缓存状态:')
    const cacheInfo = DataManager.getCacheInfo()
    console.log('- 缓存信息:', cacheInfo)

    wx.showModal({
      title: '数据状态',
      content: `表情包: ${this.data.emojiList.length}个\n分类: ${this.data.hotCategories.length}个\n横幅: ${this.data.bannerList.length}个`,
      showCancel: false
    })
  },

  // 调试：查看当前数据状态
  debugCurrentData() {
    console.log('🔍 当前页面数据状态:')
    console.log('- 表情包列表:', this.data.emojiList.length, '个')
    console.log('- 热门分类:', this.data.hotCategories.length, '个')
    console.log('- 横幅列表:', this.data.bannerList.length, '个')

    if (this.data.emojiList.length > 0) {
      console.log('- 第一个表情包:', this.data.emojiList[0])
    }

    wx.showModal({
      title: '数据状态',
      content: `表情包: ${this.data.emojiList.length}个\n分类: ${this.data.hotCategories.length}个\n横幅: ${this.data.bannerList.length}个`,
      showCancel: false
    })
  },







  // 测试云函数连接
  async testCloudFunction() {
    console.log('🔍 开始测试云函数连接...')

    try {
      wx.showLoading({ title: '测试连接中...' })

      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      })

      console.log('云函数测试结果:', result)

      if (result.result && result.result.success) {
        wx.showToast({ title: '云函数连接正常', icon: 'success' })
      } else {
        wx.showToast({ title: '云函数连接异常', icon: 'error' })
      }

    } catch (error) {
      console.error('云函数测试失败:', error)
      wx.showToast({ title: '云函数测试失败', icon: 'error' })
    } finally {
      wx.hideLoading()
    }
  },

  // 强制初始化数据库
  async initDatabaseForce() {
    console.log('🚀 开始强制初始化数据库...')

    const confirmResult = await new Promise((resolve) => {
      wx.showModal({
        title: '确认初始化',
        content: '这将清空现有数据并重新创建，确定继续吗？',
        success: (res) => resolve(res.confirm)
      })
    })

    if (!confirmResult) {
      console.log('用户取消初始化')
      return
    }

    try {
      wx.showLoading({ title: '正在初始化数据库...' })

      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'forceInitDatabase' }
      })

      console.log('数据库初始化结果:', result)

      if (result.result && result.result.success) {
        wx.showToast({ title: '数据库初始化成功', icon: 'success' })

        // 重新加载数据
        setTimeout(() => {
          this.initData()
        }, 1000)
      } else {
        wx.showToast({ title: '数据库初始化失败', icon: 'error' })
      }

    } catch (error) {
      console.error('数据库初始化失败:', error)
      wx.showToast({ title: '数据库初始化失败', icon: 'error' })
    } finally {
      wx.hideLoading()
    }
  },

  // 检查数据状态
  async checkDataStatus() {
    console.log('📊 开始检查数据状态...')

    try {
      wx.showLoading({ title: '检查数据中...' })

      // 并行检查三种数据
      const [categoryResult, emojiResult, bannerResult] = await Promise.all([
        wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'getCategories' }
        }),
        wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'getEmojis', data: { category: 'all', page: 1, limit: 5 } }
        }),
        wx.cloud.callFunction({
          name: 'dataAPI',
          data: { action: 'getBanners' }
        })
      ])

      const categoryCount = categoryResult.result?.data?.length || 0
      const emojiCount = emojiResult.result?.data?.length || 0
      const bannerCount = bannerResult.result?.data?.length || 0

      console.log('数据统计:', { categoryCount, emojiCount, bannerCount })

      wx.showModal({
        title: '数据状态检查',
        content: `分类数量: ${categoryCount}\n表情包数量: ${emojiCount}\n横幅数量: ${bannerCount}`,
        showCancel: false
      })

    } catch (error) {
      console.error('检查数据状态失败:', error)
      wx.showToast({ title: '检查数据失败', icon: 'error' })
    } finally {
      wx.hideLoading()
    }
  },

  // 添加分页混入方法
  ...PaginationMixin,

  /**
   * 页面分享配置
   */
  onShareAppMessage() {
    return {
      title: '表情包小程序 - 海量表情包',
      desc: '发现有趣的表情包，让聊天更生动',
      path: '/pages/index/index?from=share'
    }
  },

  /**
   * 分享收藏的表情包
   */
  async shareCollection() {
    try {
      // 获取用户收藏的表情包
      const collectedEmojis = this.data.emojis.filter(emoji =>
        StateManager.getEmojiState(emoji.id).isCollected
      )

      if (collectedEmojis.length === 0) {
        wx.showToast({
          title: '暂无收藏的表情包',
          icon: 'none'
        })
        return
      }

      await ShareManager.shareCollection(collectedEmojis.slice(0, 9), {
        title: `我的${collectedEmojis.length}个精选表情包`
      })
    } catch (error) {
      console.error('❌ 分享收藏失败:', error)
      wx.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  },

  // 同步状态监听相关方法

  /**
   * 注册同步状态监听
   */
  registerSyncStatusListener() {
    if (this.syncStatusCallback) return // 避免重复注册

    this.syncStatusCallback = (statusInfo) => {
      this.handleSyncStatusChange(statusInfo)
    }

    syncStatusManager.onStatusChange(this.syncStatusCallback)

    // 立即获取当前状态并更新UI
    const currentStatus = syncStatusManager.getCurrentStatus()
    this.updateSyncStatusUI(currentStatus)

    console.log('✅ 同步状态监听已注册')
  },

  /**
   * 移除同步状态监听
   */
  unregisterSyncStatusListener() {
    if (this.syncStatusCallback) {
      syncStatusManager.offStatusChange(this.syncStatusCallback)
      this.syncStatusCallback = null
      console.log('🗑️ 同步状态监听已移除')
    }
  },

  /**
   * 处理同步状态变化
   */
  handleSyncStatusChange(statusInfo) {
    const { type, status, timestamp, details } = statusInfo

    console.log('📡 收到同步状态变化:', statusInfo)

    // 更新UI显示
    this.updateSyncStatusUI(statusInfo)

    // 根据状态类型执行特定操作
    if (type === 'manual_update') {
      // 手动更新，刷新显示
      this.refreshSyncTimeDisplay()
    }
  },

  /**
   * 更新同步状态UI
   */
  updateSyncStatusUI(statusInfo) {
    try {
      const formattedTime = syncStatusManager.getFormattedLastSyncTime()

      this.setData({
        lastSyncTimeText: formattedTime,
        syncInProgress: statusInfo.status === 'syncing'
      })

      console.log(`📱 UI状态已更新: ${formattedTime}`)
    } catch (error) {
      console.error('❌ 更新同步状态UI失败:', error)
    }
  },

  /**
   * 刷新同步时间显示
   */
  refreshSyncTimeDisplay() {
    const formattedTime = syncStatusManager.getFormattedLastSyncTime()
    this.setData({
      lastSyncTimeText: formattedTime
    })
  },

  /**
   * 手动触发同步状态更新
   */
  triggerSyncStatusUpdate() {
    syncStatusManager.triggerStatusUpdate()
  }


}

// 使用页面状态混入增强页面配置
Page(withPageState(pageConfig))