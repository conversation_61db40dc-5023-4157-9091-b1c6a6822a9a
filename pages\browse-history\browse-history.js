// pages/browse-history/browse-history.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')

Page({
  data: {
    historyList: [],
    loading: false,
    isEmpty: false
  },

  onLoad() {
    this.loadBrowseHistory()
  },

  onShow() {
    // 每次显示时刷新数据，确保与其他页面同步
    this.loadBrowseHistory()
  },

  async loadBrowseHistory() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 从本地存储获取浏览记录
      const browseHistory = wx.getStorageSync('browseHistory') || []
      
      if (browseHistory.length === 0) {
        this.setData({
          historyList: [],
          isEmpty: true,
          loading: false
        })
        return
      }

      const historyList = []

      // 按时间倒序处理浏览记录
      const sortedHistory = browseHistory.sort((a, b) => new Date(b.browseTime) - new Date(a.browseTime))

      for (const historyItem of sortedHistory.slice(0, 50)) { // 最多显示50条记录
        let emojiData = DataManager.getEmojiDataFromCache(historyItem.emojiId)

        // 如果缓存中没有，尝试从数据库获取
        if (!emojiData) {
          try {
            emojiData = await DataManager.getEmojiDataFromDatabase(historyItem.emojiId)
          } catch (error) {
            console.error(`获取表情包 ${historyItem.emojiId} 失败:`, error)
            continue
          }
        }

        if (emojiData) {
          const processedEmoji = {
            ...emojiData,
            browseTime: this.formatTime(historyItem.browseTime)
          }

          historyList.push(processedEmoji)
        }
      }

      this.setData({
        historyList: historyList,
        isEmpty: historyList.length === 0,
        loading: false
      })

      console.log('浏览记录加载完成:', historyList.length)
    } catch (error) {
      console.error('加载浏览记录失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({
        historyList: [],
        isEmpty: true,
        loading: false
      })
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 604800000) { // 1周内
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      return time.toLocaleDateString()
    }
  },

  // 格式化数字
  formatNumber(num) {
    if (num < 1000) return num.toString()
    if (num < 10000) return (num / 1000).toFixed(1) + 'k'
    return (num / 10000).toFixed(1) + 'w'
  },

  // 点击表情包
  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },



  // 清空浏览记录
  onClearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有浏览记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('browseHistory')
            this.setData({
              historyList: [],
              isEmpty: true
            })
            wx.showToast({
              title: '已清空',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空浏览记录失败:', error)
            wx.showToast({
              title: '清空失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  // 去探索
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  onPullDownRefresh() {
    this.loadBrowseHistory()
    wx.stopPullDownRefresh()
  }
})
