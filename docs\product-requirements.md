# 表情包后台管理系统 - 产品开发需求文档

## 1. 项目概述

### 1.1 产品定位
表情包后台管理系统，用于管理表情包内容、分类、用户和数据分析的Web管理平台。

### 1.2 核心功能模块
- 数据概览仪表板
- 表情包内容管理
- 分类管理
- 轮播图管理
- 用户管理
- 数据分析
- 系统设置

## 2. 详细功能需求

### 2.1 数据概览仪表板

#### 2.1.1 统计卡片
**功能描述**: 显示核心业务数据统计
**数据字段**:
```json
{
  "totalEmojis": 26,        // 总表情包数
  "totalCategories": 6,     // 总分类数
  "totalUsers": 1250,       // 总用户数
  "totalDownloads": 209388  // 总下载数
}
```

**验收标准**:
- 数据实时更新，延迟不超过5分钟
- 支持点击跳转到对应管理页面
- 数字格式化显示（如：209,388）

#### 2.1.2 快速操作
**功能描述**: 提供常用操作的快捷入口
**操作项**:
- 添加表情包
- 添加分类
- 批量获取
- 刷新数据

#### 2.1.3 最近活动
**功能描述**: 显示系统最近的操作记录
**数据结构**:
```json
{
  "id": "activity_001",
  "type": "emoji_upload",
  "title": "上传了新表情包",
  "description": "哈哈哈笑死我了",
  "operator": "管理员",
  "timestamp": "2024-01-15T10:30:00Z",
  "status": "success"
}
```

### 2.2 表情包管理

#### 2.2.1 表情包列表
**功能描述**: 展示所有表情包，支持搜索、筛选、排序

**数据结构**:
```json
{
  "id": "emoji_001",
  "title": "哈哈哈笑死我了",
  "thumbnail": "https://cdn.example.com/emoji_001_thumb.jpg",
  "imageUrl": "https://cdn.example.com/emoji_001.jpg",
  "category": {
    "id": "funny",
    "name": "搞笑幽默",
    "color": "#FF6B6B"
  },
  "tags": ["搞笑", "哈哈", "笑死"],
  "stats": {
    "likes": 12340,
    "downloads": 3702,
    "views": 25680
  },
  "status": "published",
  "createTime": "2024-01-15T00:00:00Z",
  "updateTime": "2024-01-15T00:00:00Z"
}
```

**功能要求**:
- 支持按标题、分类、标签搜索
- 支持按时间、热度、下载量排序
- 支持批量操作（删除、修改分类、修改状态）
- 支持预览大图
- 支持快速编辑（标题、标签、分类）

**验收标准**:
- 列表加载时间 < 2秒
- 支持分页，每页20条记录
- 搜索响应时间 < 1秒
- 图片预览支持缩放

#### 2.2.2 添加/编辑表情包
**功能描述**: 新增或编辑表情包信息

**表单字段**:
```json
{
  "title": "表情包标题",           // 必填，1-50字符
  "description": "表情包描述",     // 可选，最多200字符
  "category": "分类ID",          // 必填，下拉选择
  "tags": ["标签1", "标签2"],     // 可选，最多10个标签
  "image": "图片文件",           // 必填，支持jpg/png/gif
  "status": "published",        // 发布状态：draft/published/archived
  "sortOrder": 0               // 排序权重
}
```

**验收标准**:
- 图片上传支持拖拽
- 图片自动压缩和生成缩略图
- 标签支持自动补全
- 表单验证实时反馈
- 保存成功后跳转到列表页

#### 2.2.3 批量导入
**功能描述**: 支持批量上传表情包

**导入格式**:
- 支持ZIP文件上传
- 支持Excel模板导入
- 支持从URL批量抓取

**验收标准**:
- 单次最多导入100个文件
- 支持进度显示
- 导入失败的文件提供错误报告

### 2.3 分类管理

#### 2.3.1 分类展示
**功能描述**: 卡片式展示所有分类，显示分类统计信息

**数据结构**:
```json
{
  "id": "funny",
  "name": "搞笑幽默",
  "icon": "😂",
  "color": "#FF6B6B",
  "description": "搞笑幽默类表情包",
  "emojiCount": 5,
  "subCategories": [
    {
      "id": "funny_animal",
      "name": "沙雕萌宠",
      "count": 2
    }
  ],
  "sortOrder": 1,
  "status": "active",
  "createTime": "2024-01-01T00:00:00Z"
}
```

**功能要求**:
- 支持拖拽排序
- 支持快速编辑分类名称
- 显示每个分类的表情包数量
- 支持分类启用/禁用

#### 2.3.2 分类管理操作
**功能要求**:
- 添加新分类
- 编辑分类信息
- 删除分类（需确认，且分类下无表情包）
- 合并分类
- 批量操作

### 2.4 轮播图管理

#### 2.4.1 轮播图列表
**数据结构**:
```json
{
  "id": "banner_001",
  "title": "新年表情包",
  "subtitle": "龙年大吉，表情包拜年",
  "image": "https://cdn.example.com/banner_001.jpg",
  "linkType": "category", // category/emoji/external
  "linkValue": "/category/节日庆典",
  "buttonText": "点击查看详情",
  "sortOrder": 1,
  "status": "active",
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-02-01T00:00:00Z",
  "clickCount": 1250,
  "createTime": "2024-01-01T00:00:00Z"
}
```

**功能要求**:
- 支持拖拽排序
- 支持定时发布
- 支持点击统计
- 轮播图预览功能

**验收标准**:
- 图片尺寸要求：1920x600像素
- 支持自动裁剪和压缩
- 预览效果与小程序端一致

### 2.5 用户管理

#### 2.5.1 用户列表
**数据结构**:
```json
{
  "id": "user_001",
  "openid": "oxxxxxxxxxxxxxxxxxxxxxx",
  "unionid": "uxxxxxxxxxxxxxxxxxxxxxx",
  "nickname": "张三",
  "avatar": "https://cdn.example.com/avatar_001.jpg",
  "email": "<EMAIL>",
  "role": "admin", // admin/user
  "status": "active", // active/inactive/banned
  "stats": {
    "likeCount": 156,
    "collectCount": 89,
    "downloadCount": 234
  },
  "registerTime": "2024-01-01T00:00:00Z",
  "lastLoginTime": "2024-01-15T10:30:00Z",
  "loginCount": 45
}
```

**功能要求**:
- 用户搜索（昵称、邮箱、ID）
- 用户状态管理（激活/禁用/封禁）
- 用户角色管理
- 用户行为统计
- 批量操作

#### 2.5.2 用户统计图表
**统计维度**:
- 用户角色分布（饼图）
- 用户状态分布（饼图）
- 注册趋势（折线图）
- 活跃用户趋势（折线图）

### 2.6 数据分析

#### 2.6.1 核心指标
**指标定义**:
```json
{
  "dailyActiveUsers": 1250,      // 日活用户
  "weeklyActiveUsers": 5600,     // 周活用户
  "monthlyActiveUsers": 18900,   // 月活用户
  "totalDownloads": 209388,      // 总下载量
  "dailyDownloads": 1250,        // 日下载量
  "averageSessionTime": 180,     // 平均使用时长（秒）
  "retentionRate": {
    "day1": 0.75,               // 次日留存率
    "day7": 0.45,               // 7日留存率
    "day30": 0.25               // 30日留存率
  }
}
```

#### 2.6.2 热门内容分析
**分析维度**:
- 热门表情包排行（按下载量、点赞数、收藏数）
- 热门分类排行
- 热门搜索关键词
- 用户行为路径分析

#### 2.6.3 趋势分析
**图表类型**:
- 用户增长趋势（折线图）
- 内容增长趋势（折线图）
- 下载量趋势（柱状图）
- 分类热度分布（雷达图）

## 3. 技术要求

### 3.1 后端技术栈
- **框架**: Node.js + Express.js
- **数据库**: MongoDB (主库) + Redis (缓存)
- **文件存储**: 阿里云OSS / 腾讯云COS
- **认证**: JWT + RBAC权限控制
- **API文档**: Swagger/OpenAPI 3.0

### 3.2 前端技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Zustand
- **路由**: React Router 6
- **图表**: ECharts / Chart.js
- **构建工具**: Vite

### 3.3 部署环境
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

### 3.4 性能要求
- **页面加载时间**: < 2秒
- **API响应时间**: < 500ms
- **图片上传**: 支持10MB以内文件
- **并发用户**: 支持100+管理员同时在线
- **数据库查询**: 复杂查询 < 1秒

### 3.5 安全要求
- **身份认证**: 多因素认证（MFA）
- **权限控制**: 基于角色的访问控制（RBAC）
- **数据加密**: HTTPS + 数据库字段加密
- **操作审计**: 所有操作记录日志
- **文件安全**: 文件类型检查 + 病毒扫描

## 4. API接口规范

### 4.1 通用规范
**Base URL**: `https://api.emoji-admin.com/v1`

**请求头**:
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_xxxxxxxxxx"
}
```

### 4.2 核心接口

#### 4.2.1 表情包管理
```
GET    /emojis              # 获取表情包列表
POST   /emojis              # 创建表情包
GET    /emojis/:id          # 获取表情包详情
PUT    /emojis/:id          # 更新表情包
DELETE /emojis/:id          # 删除表情包
POST   /emojis/batch        # 批量操作
POST   /emojis/upload       # 上传图片
```

#### 4.2.2 分类管理
```
GET    /categories          # 获取分类列表
POST   /categories          # 创建分类
PUT    /categories/:id      # 更新分类
DELETE /categories/:id      # 删除分类
PUT    /categories/sort     # 排序分类
```

#### 4.2.3 用户管理
```
GET    /users               # 获取用户列表
GET    /users/:id           # 获取用户详情
PUT    /users/:id/status    # 更新用户状态
PUT    /users/:id/role      # 更新用户角色
```

#### 4.2.4 数据统计
```
GET    /stats/overview      # 概览统计
GET    /stats/trends        # 趋势数据
GET    /stats/popular       # 热门内容
GET    /stats/users         # 用户统计
```

## 5. 数据库设计

### 5.1 表情包表 (emojis)
```javascript
{
  _id: ObjectId,
  title: String,              // 标题
  description: String,        // 描述
  imageUrl: String,          // 图片URL
  thumbnailUrl: String,      // 缩略图URL
  category: ObjectId,        // 分类ID
  tags: [String],           // 标签数组
  fileSize: Number,         // 文件大小（字节）
  dimensions: {             // 图片尺寸
    width: Number,
    height: Number
  },
  stats: {
    views: Number,          // 浏览次数
    likes: Number,          // 点赞次数
    downloads: Number,      // 下载次数
    collections: Number     // 收藏次数
  },
  status: String,           // published/draft/archived
  sortOrder: Number,        // 排序权重
  createdBy: ObjectId,      // 创建者ID
  createdAt: Date,
  updatedAt: Date
}
```

### 5.2 分类表 (categories)
```javascript
{
  _id: ObjectId,
  name: String,             // 分类名称
  slug: String,             // URL友好名称
  icon: String,             // 图标
  color: String,            // 主题色
  description: String,      // 描述
  parentId: ObjectId,       // 父分类ID
  sortOrder: Number,        // 排序权重
  status: String,           // active/inactive
  emojiCount: Number,       // 表情包数量
  createdAt: Date,
  updatedAt: Date
}
```

### 5.3 用户表 (users)
```javascript
{
  _id: ObjectId,
  openid: String,           // 微信openid
  unionid: String,          // 微信unionid
  nickname: String,         // 昵称
  avatar: String,           // 头像URL
  email: String,            // 邮箱
  phone: String,            // 手机号
  role: String,             // admin/user
  status: String,           // active/inactive/banned
  permissions: [String],    // 权限列表
  stats: {
    likeCount: Number,
    collectCount: Number,
    downloadCount: Number,
    loginCount: Number
  },
  lastLoginAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

## 6. 测试用例

### 6.1 表情包管理测试

#### 6.1.1 正常流程测试
- **测试用例**: 创建表情包
- **前置条件**: 管理员已登录
- **测试步骤**:
  1. 点击"添加表情包"按钮
  2. 填写标题"测试表情包"
  3. 选择分类"搞笑幽默"
  4. 上传图片文件
  5. 添加标签"测试,搞笑"
  6. 点击保存
- **预期结果**: 
  - 表情包创建成功
  - 跳转到列表页
  - 列表中显示新创建的表情包

#### 6.1.2 边界条件测试
- **测试用例**: 上传超大图片
- **测试数据**: 15MB的图片文件
- **预期结果**: 显示"文件大小超过限制"错误

#### 6.1.3 异常场景测试
- **测试用例**: 网络中断时上传图片
- **预期结果**: 
  - 显示上传失败提示
  - 支持重新上传
  - 不会产生脏数据

### 6.2 用户管理测试

#### 6.2.1 权限控制测试
- **测试用例**: 普通用户访问管理功能
- **预期结果**: 返回403权限不足错误

#### 6.2.2 批量操作测试
- **测试用例**: 批量禁用用户
- **测试数据**: 选择10个用户
- **预期结果**: 
  - 所有选中用户状态变为"禁用"
  - 操作记录写入日志

### 6.3 数据统计测试

#### 6.3.1 数据准确性测试
- **测试用例**: 验证下载量统计
- **测试方法**: 
  1. 记录当前下载总量
  2. 模拟10次下载操作
  3. 检查统计数据是否增加10
- **预期结果**: 统计数据准确无误

#### 6.3.2 性能测试
- **测试用例**: 大数据量统计查询
- **测试数据**: 100万条记录
- **预期结果**: 查询时间 < 2秒

## 7. 开发计划

### 7.1 第一阶段（2周）
- 项目架构搭建
- 用户认证系统
- 基础CRUD接口
- 数据库设计实现

### 7.2 第二阶段（2周）
- 表情包管理功能
- 分类管理功能
- 文件上传功能
- 基础前端页面

### 7.3 第三阶段（2周）
- 用户管理功能
- 轮播图管理
- 数据统计功能
- 权限控制系统

### 7.4 第四阶段（1周）
- 系统测试
- 性能优化
- 部署上线
- 文档完善

## 8. 验收标准

### 8.1 功能验收
- 所有功能模块正常运行
- 用户界面友好易用
- 数据操作准确无误
- 权限控制有效

### 8.2 性能验收
- 页面加载时间 < 2秒
- API响应时间 < 500ms
- 支持100+并发用户
- 系统稳定性 > 99.9%

### 8.3 安全验收
- 通过安全扫描测试
- 权限控制测试通过
- 数据加密验证
- 操作日志完整

### 8.4 兼容性验收
- 支持主流浏览器
- 响应式设计适配
- 移动端访问正常

---

**文档版本**: v1.0  
**创建时间**: 2024-01-15  
**更新时间**: 2024-01-15  
**负责人**: 产品团队