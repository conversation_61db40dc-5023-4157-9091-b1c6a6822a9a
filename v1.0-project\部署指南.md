# 🚀 V1.0 系统部署指南

## 📋 部署概述

本指南将详细说明如何将V1.0表情包小程序系统部署到腾讯云开发环境。整个部署过程分为环境准备、代码部署、配置设置、测试验证四个阶段。

**预计部署时间**: 30-60分钟  
**技术要求**: 基础的命令行操作能力  
**前置条件**: 腾讯云账号、微信开发者账号

## 🛠️ 环境准备

### 1. 账号准备

#### 1.1 腾讯云账号
- 注册腾讯云账号: https://cloud.tencent.com/
- 开通云开发服务
- 确保账户有足够的免费额度或余额

#### 1.2 微信开发者账号
- 注册微信小程序账号: https://mp.weixin.qq.com/
- 获取小程序AppID
- 配置服务器域名白名单

### 2. 开发工具安装

#### 2.1 Node.js环境
```bash
# 检查Node.js版本 (需要14.0+)
node --version

# 如果没有安装，请访问 https://nodejs.org/ 下载安装
```

#### 2.2 微信开发者工具
- 下载地址: https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
- 安装并登录微信开发者工具

#### 2.3 CloudBase CLI工具
```bash
# 安装CloudBase CLI
npm install -g @cloudbase/cli

# 验证安装
tcb --version
```

### 3. 项目文件准备

#### 3.1 下载项目代码
```bash
# 如果您有项目压缩包，解压到本地目录
# 或者从代码仓库克隆项目
```

#### 3.2 安装项目依赖
```bash
cd v1.0-project

# 安装云函数依赖
cd cloudfunctions/loginAPI && npm install && cd ../..
cd cloudfunctions/webAdminAPI && npm install && cd ../..
cd cloudfunctions/dataAPI && npm install && cd ../..

# 安装测试依赖
npm install
```

## ☁️ 云开发环境配置

### 1. 创建云开发环境

#### 1.1 通过腾讯云控制台创建
1. 登录腾讯云控制台
2. 搜索"云开发"服务
3. 点击"新建环境"
4. 选择"小程序云开发"
5. 填写环境信息：
   - 环境名称: `emoji-miniprogram-v1`
   - 环境ID: 系统自动生成 (记录下来，后续需要用到)
   - 计费方式: 选择"按量付费"或"包年包月"

#### 1.2 通过CLI创建 (可选)
```bash
# 登录CloudBase
tcb login

# 创建环境
tcb env:create emoji-miniprogram-v1 --alias "表情包小程序V1.0"
```

### 2. 配置环境变量

#### 2.1 更新项目配置
编辑 `config/production.js` 文件：
```javascript
module.exports = {
  // 替换为您的环境ID
  ENV_ID: 'your-env-id-here',
  
  // JWT密钥 (生产环境请使用强密钥)
  JWT_SECRET: 'your-super-secret-jwt-key-2025',
  
  // 管理员账号配置
  ADMIN_CONFIG: {
    username: 'admin',
    password: 'your-admin-password-here' // 请修改默认密码
  }
};
```

#### 2.2 更新小程序配置
编辑 `miniprogram/app.js` 中的环境ID：
```javascript
// 第26行附近，替换环境ID
env: 'your-env-id-here'
```

#### 2.3 更新管理后台配置
编辑 `admin-web/js/auth-manager.js` 中的环境ID：
```javascript
// 第26行附近，替换环境ID
env: 'your-env-id-here'
```

## 📦 代码部署

### 1. 使用自动化部署脚本

#### 1.1 配置部署脚本
编辑 `scripts/deploy.js` 文件：
```javascript
// 第10行附近，更新部署配置
const DEPLOY_CONFIG = {
  env: 'your-env-id-here', // 替换为您的环境ID
  region: 'ap-shanghai',   // 根据需要选择地域
  // ... 其他配置保持不变
};
```

#### 1.2 执行自动化部署
```bash
# 进入项目目录
cd v1.0-project

# 执行部署脚本
node scripts/deploy.js

# 部署过程会自动完成以下步骤：
# 1. 环境检查
# 2. 云函数部署
# 3. 数据库初始化
# 4. 静态网站部署
# 5. 部署验证
```

### 2. 手动部署 (如果自动化部署失败)

#### 2.1 部署云函数
```bash
# 登录CloudBase
tcb login

# 部署loginAPI云函数
cd cloudfunctions/loginAPI
tcb functions:deploy loginAPI --env your-env-id-here

# 部署webAdminAPI云函数
cd ../webAdminAPI
tcb functions:deploy webAdminAPI --env your-env-id-here

# 部署dataAPI云函数
cd ../dataAPI
tcb functions:deploy dataAPI --env your-env-id-here

cd ../..
```

#### 2.2 初始化数据库
```bash
# 创建数据库集合
tcb db:createCollection categories --env your-env-id-here
tcb db:createCollection emojis --env your-env-id-here
tcb db:createCollection banners --env your-env-id-here
tcb db:createCollection sync_notifications --env your-env-id-here
tcb db:createCollection admin_logs --env your-env-id-here

# 设置数据库权限
tcb db:updateCollection categories --rule config/database-rules.json --env your-env-id-here
```

#### 2.3 部署管理后台
```bash
# 部署静态网站
tcb hosting:deploy admin-web --env your-env-id-here

# 获取访问地址
tcb hosting:detail --env your-env-id-here
```

## 🔧 配置设置

### 1. 数据库安全规则

#### 1.1 应用安全规则
```bash
# 应用数据库安全规则
tcb db:updateCollection categories --rule config/database-rules.json --env your-env-id-here
tcb db:updateCollection emojis --rule config/database-rules.json --env your-env-id-here
tcb db:updateCollection banners --rule config/database-rules.json --env your-env-id-here
tcb db:updateCollection sync_notifications --rule config/database-rules.json --env your-env-id-here
tcb db:updateCollection admin_logs --rule config/database-rules.json --env your-env-id-here
```

### 2. 小程序配置

#### 2.1 配置服务器域名
在微信小程序管理后台配置以下域名：
- request合法域名: `https://your-env-id.service.tcloudbase.com`
- socket合法域名: `wss://your-env-id.service.tcloudbase.com`

#### 2.2 上传小程序代码
1. 打开微信开发者工具
2. 导入项目，选择 `miniprogram` 目录
3. 填入您的小程序AppID
4. 点击"上传"按钮上传代码
5. 在微信小程序管理后台提交审核

### 3. 管理后台配置

#### 3.1 获取管理后台地址
```bash
# 查看静态网站访问地址
tcb hosting:detail --env your-env-id-here
```

#### 3.2 配置HTTPS证书 (可选)
如果需要自定义域名，可以在腾讯云控制台配置：
1. 进入云开发控制台
2. 选择"静态网站托管"
3. 点击"设置"
4. 添加自定义域名并配置SSL证书

## ✅ 测试验证

### 1. 运行链路验证

#### 1.1 打开链路验证工具
在浏览器中打开：`v1.0-project/链路打通验证.html`

#### 1.2 执行验证步骤
1. 点击"开始验证"按钮
2. 等待所有检查项完成
3. 查看验证结果和成功率
4. 如果成功率低于80%，检查错误日志

### 2. 功能测试

#### 2.1 管理后台测试
1. 访问管理后台地址
2. 使用配置的管理员账号登录
3. 测试分类管理功能
4. 测试表情包管理功能
5. 测试横幅管理功能
6. 验证实时同步功能

#### 2.2 小程序测试
1. 在微信开发者工具中预览小程序
2. 测试数据加载功能
3. 验证实时更新功能
4. 测试搜索和分页功能

### 3. 性能测试

#### 3.1 运行自动化测试
```bash
# 运行完整测试套件
cd v1.0-project
node test/run-all-tests.js

# 查看测试报告
cat test-report.json
```

#### 3.2 监控系统性能
1. 在腾讯云控制台查看云函数调用统计
2. 监控数据库读写次数
3. 检查错误日志和异常情况

## 🚨 常见问题解决

### 1. 部署失败问题

#### 1.1 权限不足
```bash
# 重新登录CloudBase
tcb logout
tcb login

# 检查权限
tcb env:list
```

#### 1.2 环境ID错误
- 检查所有配置文件中的环境ID是否正确
- 确保环境ID与实际创建的环境一致

#### 1.3 网络连接问题
- 检查网络连接是否正常
- 尝试使用VPN或更换网络环境

### 2. 功能异常问题

#### 2.1 登录失败
- 检查管理员账号密码配置
- 查看云函数日志排查错误

#### 2.2 数据同步异常
- 检查数据库权限配置
- 验证CloudBase Watch功能是否正常

#### 2.3 小程序无法访问
- 检查服务器域名配置
- 确认小程序AppID配置正确

### 3. 性能问题

#### 3.1 响应速度慢
- 检查云函数冷启动情况
- 优化数据库查询语句
- 启用缓存机制

#### 3.2 并发能力不足
- 调整云函数并发配置
- 优化数据库连接池设置

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. **查看日志**: 检查 `deploy.log` 文件中的详细错误信息
2. **运行诊断**: 使用 `链路打通验证.html` 工具诊断问题
3. **查看文档**: 参考 `docs/` 目录下的详细文档
4. **联系支持**: 发送邮件到技术支持邮箱

---

**部署指南版本**: 1.0  
**最后更新时间**: 2025年7月25日  
**适用系统版本**: V1.0
