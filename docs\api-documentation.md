# 云函数API文档

## 1. 概述

本文档描述了微信小程序表情包后端的所有云函数接口。所有接口都基于微信云开发，使用标准的云函数调用方式。

### 1.1 调用方式
```javascript
wx.cloud.callFunction({
  name: 'functionName',
  data: {
    action: 'actionName',
    data: {}
  }
}).then(res => {
  console.log(res.result)
}).catch(err => {
  console.error(err)
})
```

### 1.2 响应格式
```javascript
{
  success: true,        // 操作是否成功
  data: {},            // 返回数据
  message: "操作成功", // 提示信息
  code: 200,           // 状态码
  error: null          // 错误信息
}
```

## 2. 用户认证相关

### 2.1 用户登录
**云函数**: `login`

**描述**: 用户登录并创建/更新用户信息

**参数**: 无

**返回数据**:
```javascript
{
  success: true,
  openid: "用户openid",
  unionid: "用户unionid",
  message: "登录成功"
}
```

**调用示例**:
```javascript
wx.cloud.callFunction({
  name: 'login'
}).then(res => {
  if (res.result.success) {
    console.log('登录成功', res.result.openid)
  }
})
```

## 3. 表情包相关

### 3.1 获取表情包列表
**云函数**: `getEmojiList`

**描述**: 获取表情包列表，支持分页和筛选

**参数**:
```javascript
{
  category: 'all',      // 分类筛选，'all'表示全部
  page: 1,              // 页码，默认1
  limit: 20             // 每页数量，默认20
}
```

**返回数据**:
```javascript
{
  success: true,
  data: [
    {
      id: "表情包ID",
      title: "表情包标题",
      imageUrl: "图片URL",
      category: "分类",
      likes: 1200,
      collections: 850,
      tags: ["标签1", "标签2"],
      date: "2024-01-15"
    }
  ],
  total: 100
}
```

### 3.2 获取表情包详情
**云函数**: `getEmojiDetail`

**描述**: 获取单个表情包的详细信息

**参数**:
```javascript
{
  emojiId: "表情包ID"
}
```

**返回数据**:
```javascript
{
  success: true,
  data: {
    id: "表情包ID",
    title: "表情包标题",
    imageUrl: "图片URL",
    category: "分类",
    likes: 1200,
    collections: 850,
    views: 5600,
    tags: ["标签1", "标签2"],
    isLiked: false,      // 当前用户是否已点赞
    isCollected: false   // 当前用户是否已收藏
  }
}
```

### 3.3 点赞/取消点赞
**云函数**: `toggleLike`

**描述**: 切换表情包的点赞状态

**参数**:
```javascript
{
  emojiId: "表情包ID",
  isLiked: true         // true表示点赞，false表示取消点赞
}
```

**返回数据**:
```javascript
{
  success: true,
  message: "点赞成功"
}
```

### 3.4 收藏/取消收藏
**云函数**: `toggleCollect`

**描述**: 切换表情包的收藏状态

**参数**:
```javascript
{
  emojiId: "表情包ID",
  isCollected: true     // true表示收藏，false表示取消收藏
}
```

**返回数据**:
```javascript
{
  success: true,
  message: "收藏成功"
}
```

### 3.5 搜索表情包
**云函数**: `searchEmojis`

**描述**: 根据关键词搜索表情包

**参数**:
```javascript
{
  keyword: "搜索关键词",
  page: 1,
  limit: 20
}
```

**返回数据**:
```javascript
{
  success: true,
  data: [
    // 表情包列表，格式同getEmojiList
  ],
  total: 50,
  keyword: "搜索关键词"
}
```

## 4. 分类相关

### 4.1 获取分类列表
**云函数**: `getCategories`

**描述**: 获取所有分类信息

**参数**: 无

**返回数据**:
```javascript
{
  success: true,
  data: [
    {
      id: "分类ID",
      name: "分类名称",
      icon: "😂",
      color: "#FF6B6B",
      count: 5
    }
  ]
}
```

## 5. 轮播图相关

### 5.1 获取轮播图
**云函数**: `getBanners`

**描述**: 获取首页轮播图列表

**参数**: 无

**返回数据**:
```javascript
{
  success: true,
  data: [
    {
      id: "轮播图ID",
      title: "标题",
      subtitle: "副标题",
      imageUrl: "图片URL",
      linkType: "category",
      linkValue: "分类ID",
      buttonText: "按钮文字"
    }
  ]
}
```

## 6. 用户统计相关

### 6.1 获取用户统计
**云函数**: `getUserStats`

**描述**: 获取当前用户的统计信息

**参数**: 无

**返回数据**:
```javascript
{
  success: true,
  data: {
    likeCount: 156,      // 点赞数
    collectCount: 89     // 收藏数
  }
}
```

## 7. 文件上传相关

### 7.1 获取上传URL
**云函数**: `uploadFile`

**描述**: 获取文件上传的临时URL

**参数**:
```javascript
{
  action: 'getUploadUrl',
  data: {
    fileType: 'image/jpeg',
    fileName: 'emoji.jpg',
    fileSize: 245760
  }
}
```

**返回数据**:
```javascript
{
  success: true,
  data: {
    uploadUrl: "上传URL",
    cloudPath: "云存储路径",
    fileId: "文件ID"
  }
}
```

### 7.2 确认上传
**云函数**: `uploadFile`

**描述**: 确认文件上传完成

**参数**:
```javascript
{
  action: 'confirmUpload',
  data: {
    cloudPath: "云存储路径",
    fileId: "文件ID"
  }
}
```

**返回数据**:
```javascript
{
  success: true,
  data: {
    fileId: "文件ID",
    url: "访问URL",
    thumbnailUrl: "缩略图URL"
  }
}
```

## 8. 用户行为追踪

### 8.1 记录用户行为
**云函数**: `trackAction`

**描述**: 记录用户的各种行为（浏览、下载、分享等）

**参数**:
```javascript
{
  action: 'view',           // 行为类型：view/download/share
  targetType: 'emoji',      // 目标类型：emoji/category
  targetId: '目标ID',
  details: {                // 额外信息
    source: 'homepage',
    position: 3
  }
}
```

**返回数据**:
```javascript
{
  success: true,
  message: "行为记录成功"
}
```

## 9. 后台管理相关

### 9.1 获取概览数据
**云函数**: `admin`

**描述**: 获取后台管理系统的概览统计数据

**参数**:
```javascript
{
  action: 'getOverview'
}
```

**返回数据**:
```javascript
{
  success: true,
  data: {
    totalEmojis: 26,
    totalCategories: 6,
    totalUsers: 1250,
    totalDownloads: 209388,
    todayUsers: 45,
    todayDownloads: 1250,
    todayUploads: 5
  }
}
```

### 9.2 获取表情包管理列表
**云函数**: `admin`

**参数**:
```javascript
{
  action: 'getEmojiList',
  data: {
    page: 1,
    limit: 20,
    keyword: '搜索关键词',
    category: '分类ID',
    status: 'published',
    sortBy: 'createTime',
    sortOrder: 'desc'
  }
}
```

### 9.3 创建表情包
**云函数**: `admin`

**参数**:
```javascript
{
  action: 'createEmoji',
  data: {
    title: '表情包标题',
    description: '描述',
    imageUrl: '图片URL',
    category: '分类ID',
    tags: ['标签1', '标签2'],
    status: 'published'
  }
}
```

### 9.4 更新表情包
**云函数**: `admin`

**参数**:
```javascript
{
  action: 'updateEmoji',
  data: {
    id: '表情包ID',
    title: '新标题',
    // 其他要更新的字段
  }
}
```

### 9.5 删除表情包
**云函数**: `admin`

**参数**:
```javascript
{
  action: 'deleteEmoji',
  data: {
    id: '表情包ID'
  }
}
```

### 9.6 批量操作
**云函数**: `admin`

**参数**:
```javascript
{
  action: 'batchOperation',
  data: {
    action: 'delete',        // delete/updateStatus/updateCategory
    ids: ['id1', 'id2'],
    params: {
      status: 'archived',
      category: 'funny'
    }
  }
}
```

### 9.7 分类管理
**云函数**: `admin`

**获取分类列表**:
```javascript
{
  action: 'getCategoryList'
}
```

**创建分类**:
```javascript
{
  action: 'createCategory',
  data: {
    name: '分类名称',
    icon: '😂',
    color: '#FF6B6B',
    description: '分类描述'
  }
}
```

**更新分类**:
```javascript
{
  action: 'updateCategory',
  data: {
    id: '分类ID',
    name: '新名称'
  }
}
```

**删除分类**:
```javascript
{
  action: 'deleteCategory',
  data: {
    id: '分类ID'
  }
}
```

### 9.8 轮播图管理
**云函数**: `admin`

**获取轮播图列表**:
```javascript
{
  action: 'getBannerList'
}
```

**创建轮播图**:
```javascript
{
  action: 'createBanner',
  data: {
    title: '标题',
    subtitle: '副标题',
    imageUrl: '图片URL',
    linkType: 'category',
    linkValue: '分类ID',
    buttonText: '按钮文字'
  }
}
```

### 9.9 用户管理
**云函数**: `admin`

**获取用户列表**:
```javascript
{
  action: 'getUserList',
  data: {
    page: 1,
    limit: 20,
    keyword: '搜索关键词',
    role: 'user',
    status: 'active'
  }
}
```

**更新用户状态**:
```javascript
{
  action: 'updateUserStatus',
  data: {
    userId: '用户ID',
    status: 'inactive',
    reason: '违规操作'
  }
}
```

### 9.10 统计数据
**云函数**: `admin`

**获取统计数据**:
```javascript
{
  action: 'getStatistics',
  data: {
    type: 'trends',      // trends/popular/users
    period: '7d'         // 1d/7d/30d/90d
  }
}
```

## 10. 数据同步维护

### 10.1 数据同步
**云函数**: `dataSync`

**更新分类统计**:
```javascript
{
  action: 'updateCategoryStats'
}
```

**清理过期数据**:
```javascript
{
  action: 'cleanExpiredData'
}
```

**更新用户统计**:
```javascript
{
  action: 'updateUserStats'
}
```

**生成日报**:
```javascript
{
  action: 'generateDailyReport'
}
```

## 11. 错误处理

### 11.1 常见错误码
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 11.2 错误响应格式
```javascript
{
  success: false,
  error: "错误描述",
  code: 400
}
```

## 12. 调用示例

### 12.1 完整的表情包列表获取示例
```javascript
// 获取搞笑分类的表情包
wx.cloud.callFunction({
  name: 'getEmojiList',
  data: {
    category: 'funny',
    page: 1,
    limit: 10
  }
}).then(res => {
  if (res.result.success) {
    const emojis = res.result.data
    console.log('获取到表情包:', emojis.length, '个')
    
    // 更新页面数据
    this.setData({
      emojiList: emojis,
      total: res.result.total
    })
  } else {
    wx.showToast({
      title: res.result.error,
      icon: 'error'
    })
  }
}).catch(err => {
  console.error('调用失败:', err)
  wx.showToast({
    title: '网络错误',
    icon: 'error'
  })
})
```

### 12.2 用户行为追踪示例
```javascript
// 记录用户浏览表情包
wx.cloud.callFunction({
  name: 'trackAction',
  data: {
    action: 'view',
    targetType: 'emoji',
    targetId: emojiId,
    details: {
      source: 'category_page',
      position: index
    }
  }
})

// 记录用户下载表情包
wx.cloud.callFunction({
  name: 'trackAction',
  data: {
    action: 'download',
    targetType: 'emoji',
    targetId: emojiId
  }
})
```

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15  
**维护人员**: 后端开发团队