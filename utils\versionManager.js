/**
 * 数据版本管理器
 * 用于管理数据版本，实现增量同步
 */

const { CloudConfig } = require('../config/cloud.js')

const VersionManager = {
  // 本地版本信息
  localVersions: {
    emojis: 0,
    categories: 0,
    banners: 0,
    app_config: 0,
    users: 0,
    user_actions: 0
  },

  // 版本存储键
  STORAGE_KEY: 'data_versions',

  /**
   * 初始化版本管理器
   */
  init() {
    try {
      // 从本地存储加载版本信息
      const stored = wx.getStorageSync(this.STORAGE_KEY)
      if (stored) {
        this.localVersions = { ...this.localVersions, ...stored }
        console.log('📋 已加载本地版本信息:', this.localVersions)
      } else {
        console.log('📋 首次运行，使用默认版本信息')
      }
    } catch (error) {
      console.warn('⚠️ 加载版本信息失败:', error.message)
    }
  },

  /**
   * 保存版本信息到本地存储
   */
  saveVersions() {
    try {
      wx.setStorageSync(this.STORAGE_KEY, this.localVersions)
      console.log('💾 版本信息已保存')
    } catch (error) {
      console.error('❌ 保存版本信息失败:', error.message)
    }
  },

  /**
   * 获取服务器版本信息
   */
  async getServerVersions() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getVersions'
        }
      })

      if (result.result && result.result.success) {
        return result.result.versions
      } else {
        throw new Error(result.result?.message || '获取服务器版本失败')
      }
    } catch (error) {
      console.error('❌ 获取服务器版本失败:', error.message)
      // 返回默认版本，避免阻塞
      return this.localVersions
    }
  },

  /**
   * 检查版本更新
   */
  async checkUpdates() {
    console.log('🔍 检查数据版本更新...')
    
    try {
      const serverVersions = await this.getServerVersions()
      const updates = []

      for (const [type, localVersion] of Object.entries(this.localVersions)) {
        const serverVersion = serverVersions[type] || 0
        
        if (serverVersion > localVersion) {
          updates.push({
            type,
            localVersion,
            serverVersion,
            needUpdate: true
          })
          console.log(`📦 发现更新: ${type} ${localVersion} -> ${serverVersion}`)
        } else {
          console.log(`✅ ${type} 版本最新: ${localVersion}`)
        }
      }

      console.log(`🔍 检查完成，发现 ${updates.length} 个更新`)
      return {
        hasUpdates: updates.length > 0,
        updates,
        serverVersions
      }

    } catch (error) {
      console.error('❌ 检查版本更新失败:', error.message)
      return {
        hasUpdates: false,
        updates: [],
        error: error.message
      }
    }
  },

  /**
   * 更新本地版本号
   */
  updateLocalVersion(type, version) {
    if (this.localVersions.hasOwnProperty(type)) {
      this.localVersions[type] = version
      this.saveVersions()
      console.log(`📋 已更新 ${type} 版本: ${version}`)
    } else {
      console.warn(`⚠️ 未知的数据类型: ${type}`)
    }
  },

  /**
   * 批量更新版本号
   */
  updateVersions(versions) {
    let updated = false
    
    for (const [type, version] of Object.entries(versions)) {
      if (this.localVersions.hasOwnProperty(type)) {
        if (this.localVersions[type] !== version) {
          this.localVersions[type] = version
          updated = true
        }
      }
    }

    if (updated) {
      this.saveVersions()
      console.log('📋 批量更新版本完成:', this.localVersions)
    }
  },

  /**
   * 获取本地版本信息
   */
  getLocalVersions() {
    return { ...this.localVersions }
  },

  /**
   * 重置版本信息
   */
  resetVersions() {
    this.localVersions = {
      emojis: 0,
      categories: 0,
      banners: 0,
      app_config: 0,
      users: 0,
      user_actions: 0
    }
    this.saveVersions()
    console.log('🔄 版本信息已重置')
  },

  /**
   * 获取版本差异报告
   */
  async getVersionDiff() {
    try {
      const serverVersions = await this.getServerVersions()
      const diff = {}

      for (const [type, localVersion] of Object.entries(this.localVersions)) {
        const serverVersion = serverVersions[type] || 0
        diff[type] = {
          local: localVersion,
          server: serverVersion,
          status: serverVersion > localVersion ? 'outdated' : 
                  serverVersion < localVersion ? 'ahead' : 'synced'
        }
      }

      return diff
    } catch (error) {
      console.error('❌ 获取版本差异失败:', error.message)
      return null
    }
  },

  /**
   * 强制同步所有数据
   */
  async forceSyncAll() {
    console.log('🔄 强制同步所有数据...')
    
    try {
      // 重置本地版本
      this.resetVersions()
      
      // 检查更新
      const updateResult = await this.checkUpdates()
      
      return {
        success: true,
        updates: updateResult.updates || []
      }
    } catch (error) {
      console.error('❌ 强制同步失败:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = {
  VersionManager
}
