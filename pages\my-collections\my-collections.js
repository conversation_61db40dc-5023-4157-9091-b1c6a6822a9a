// pages/my-collections/my-collections.js
const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')

// 分类ID到名称的映射
const categoryNameMap = {
  'funny': '搞笑幽默',
  'cute': '可爱萌宠',
  'emotion': '情感表达',
  'festival': '节日庆典',
  'hot': '网络热梗',
  '2d': '动漫二次元',
  'daily': '生活日常',
  'work': '工作学习',
  'sport': '运动健身',
  'food': '美食料理'
}

Page({
  data: {
    collectedEmojis: [],
    loading: false,
    isEmpty: false
  },

  onLoad() {
    this.loadCollectedEmojis()
  },

  onShow() {
    // 每次显示时刷新数据，确保与详情页同步
    this.loadCollectedEmojis()
    
    // 监听状态变化
    this.stateListener = (data) => {
      if (data.type === 'collect') {
        this.loadCollectedEmojis()
      }
    }
    StateManager.addListener('global', this.stateListener)
  },

  onHide() {
    // 移除状态监听器
    if (this.stateListener) {
      StateManager.removeListener('global', this.stateListener)
      this.stateListener = null
    }
  },

  async loadCollectedEmojis() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 使用StateManager获取收藏的表情包ID列表
      const collectedEmojiIds = StateManager.getCollectedEmojis()

      if (collectedEmojiIds.length === 0) {
        this.setData({
          collectedEmojis: [],
          isEmpty: true,
          loading: false
        })
        return
      }

      // 从全局数据管理器获取完整的表情包信息
      const collectedEmojis = []

      for (const emojiId of collectedEmojiIds) {
        let emojiData = DataManager.getEmojiDataFromCache(emojiId)

        // 如果缓存中没有，尝试从数据库获取
        if (!emojiData) {
          try {
            emojiData = await DataManager.getEmojiDataFromDatabase(emojiId)
          } catch (error) {
            console.error(`获取表情包 ${emojiId} 失败:`, error)
            continue
          }
        }

        if (emojiData) {
          const emojiState = StateManager.getEmojiState(emojiId)

          // 处理分类名称显示
          const categoryId = emojiData.category || 'unknown'
          const categoryName = emojiData.categoryName || categoryNameMap[categoryId] || categoryId || '未分类'

          collectedEmojis.push({
            ...emojiData,
            // 确保 likes 和 collections 有默认值
            likes: emojiData.likes || 0,
            collections: emojiData.collections || 0,
            tags: emojiData.tags || [], // 确保包含标签数据
            category: categoryId,
            categoryName: categoryName, // 添加分类名称
            likesText: DataManager.formatNumber(emojiData.likes || 0),
            collectionsText: DataManager.formatNumber(emojiData.collections || 0),
            isLiked: emojiState.isLiked,
            isCollected: emojiState.isCollected
          })
        } else {
          console.warn(`表情包 ${emojiId} 可能已被删除，从收藏列表中移除`)
          // 从收藏列表中移除不存在的表情包
          if (StateManager.isCollected(emojiId)) {
            StateManager.toggleCollect(emojiId)
          }
        }
      }

      this.setData({
        collectedEmojis: collectedEmojis,
        isEmpty: collectedEmojis.length === 0,
        loading: false
      })

      console.log('我的收藏列表:', collectedEmojis)
    } catch (error) {
      console.error('加载收藏列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({
        collectedEmojis: [],
        isEmpty: true,
        loading: false
      })
    }
  },

  onEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail-new?id=${emoji.id}`
    })
  },

  // 取消收藏操作
  onUncollectEmoji(e) {
    const emojiId = e.currentTarget.dataset.id

    try {
      // 使用StateManager切换收藏状态
      const newIsCollected = StateManager.toggleCollect(emojiId)

      if (!newIsCollected) {
        // 更新全局数据中的收藏数
        const emojiData = DataManager.getEmojiData(emojiId)
        if (emojiData) {
          const newCollections = Math.max(0, emojiData.collections - 1)
          DataManager.updateCollections(emojiId, newCollections)
        }

        // 刷新列表
        this.loadCollectedEmojis()

        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('❌ 取消收藏失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },

  onPullDownRefresh() {
    this.loadCollectedEmojis()
    wx.stopPullDownRefresh()
  },

  // 去探索更多表情包
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})