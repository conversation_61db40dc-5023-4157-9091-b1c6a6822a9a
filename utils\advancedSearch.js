/**
 * 高级搜索模块
 * 提供智能搜索、搜索建议、搜索历史和个性化推荐功能
 */

const { SmartCache } = require('./smartCache.js')
const { DataAnalytics } = require('./dataAnalytics.js')

const AdvancedSearch = {
  // 搜索配置
  config: {
    enabled: true,
    maxSuggestions: 10,       // 最大建议数量
    maxHistory: 20,           // 最大历史记录数
    fuzzyThreshold: 0.6,      // 模糊匹配阈值
    enablePersonalization: true, // 个性化推荐
    cacheTimeout: 5 * 60 * 1000  // 缓存超时时间(5分钟)
  },

  // 搜索状态
  state: {
    initialized: false,
    searchHistory: [],
    popularKeywords: [],
    userPreferences: {}
  },

  // 搜索类型
  searchTypes: {
    KEYWORD: 'keyword',       // 关键词搜索
    CATEGORY: 'category',     // 分类搜索
    TAG: 'tag',              // 标签搜索
    FUZZY: 'fuzzy',          // 模糊搜索
    SEMANTIC: 'semantic'      // 语义搜索
  },

  /**
   * 初始化高级搜索
   */
  init(options = {}) {
    console.log('🔍 初始化高级搜索模块...')
    
    // 合并配置
    this.config = { ...this.config, ...options }
    
    if (!this.config.enabled) {
      console.log('⚠️ 高级搜索已禁用')
      return
    }

    // 初始化缓存策略
    SmartCache.strategies.search = {
      level: 'temp',
      ttl: this.config.cacheTimeout,
      preload: false
    }

    // 加载搜索历史
    this.loadSearchHistory()
    
    // 加载热门关键词
    this.loadPopularKeywords()
    
    // 加载用户偏好
    this.loadUserPreferences()
    
    this.state.initialized = true
    console.log('✅ 高级搜索模块初始化完成')
  },

  /**
   * 执行高级搜索
   */
  async search(query, options = {}) {
    if (!this.config.enabled || !this.state.initialized) {
      return this.fallbackSearch(query, options)
    }

    const {
      type = this.searchTypes.KEYWORD,
      category = null,
      limit = 20,
      offset = 0,
      sortBy = 'relevance',
      filters = {},
      enableCache = true
    } = options

    try {
      console.log(`🔍 执行高级搜索: "${query}" (${type})`)
      
      // 记录搜索行为
      DataAnalytics.trackSearch(query, 0, category)
      
      // 检查缓存
      if (enableCache) {
        const cached = this.getCachedResults(query, options)
        if (cached) {
          console.log('📦 搜索结果缓存命中')
          return cached
        }
      }

      // 预处理查询
      const processedQuery = this.preprocessQuery(query)
      
      // 执行搜索
      let results = []
      switch (type) {
        case this.searchTypes.KEYWORD:
          results = await this.keywordSearch(processedQuery, options)
          break
        case this.searchTypes.FUZZY:
          results = await this.fuzzySearch(processedQuery, options)
          break
        case this.searchTypes.SEMANTIC:
          results = await this.semanticSearch(processedQuery, options)
          break
        case this.searchTypes.CATEGORY:
          results = await this.categorySearch(processedQuery, options)
          break
        case this.searchTypes.TAG:
          results = await this.tagSearch(processedQuery, options)
          break
        default:
          results = await this.keywordSearch(processedQuery, options)
      }

      // 应用过滤器
      if (Object.keys(filters).length > 0) {
        results = this.applyFilters(results, filters)
      }

      // 排序结果
      results = this.sortResults(results, sortBy, query)

      // 分页
      const paginatedResults = results.slice(offset, offset + limit)

      const searchResult = {
        query,
        type,
        results: paginatedResults,
        total: results.length,
        hasMore: offset + limit < results.length,
        suggestions: await this.generateSuggestions(query),
        relatedKeywords: this.getRelatedKeywords(query),
        searchTime: Date.now(),
        cached: false
      }

      // 缓存结果
      if (enableCache) {
        this.cacheResults(query, options, searchResult)
      }

      // 更新搜索历史
      this.addToHistory(query, searchResult.total)

      // 更新用户偏好
      this.updateUserPreferences(query, category, searchResult.total)

      console.log(`✅ 搜索完成: ${searchResult.total} 个结果`)
      return searchResult

    } catch (error) {
      console.error('❌ 高级搜索失败:', error)
      return this.fallbackSearch(query, options)
    }
  },

  /**
   * 关键词搜索
   */
  async keywordSearch(query, options) {
    // 模拟关键词搜索逻辑
    const mockResults = this.generateMockResults(query, 'keyword')
    
    // 根据关键词匹配度排序
    return mockResults.map(result => ({
      ...result,
      relevance: this.calculateKeywordRelevance(query, result),
      matchType: 'keyword'
    }))
  },

  /**
   * 模糊搜索
   */
  async fuzzySearch(query, options) {
    // 模拟模糊搜索逻辑
    const mockResults = this.generateMockResults(query, 'fuzzy')
    
    return mockResults.map(result => ({
      ...result,
      relevance: this.calculateFuzzyRelevance(query, result),
      matchType: 'fuzzy'
    }))
  },

  /**
   * 语义搜索
   */
  async semanticSearch(query, options) {
    // 模拟语义搜索逻辑
    const mockResults = this.generateMockResults(query, 'semantic')
    
    return mockResults.map(result => ({
      ...result,
      relevance: this.calculateSemanticRelevance(query, result),
      matchType: 'semantic'
    }))
  },

  /**
   * 分类搜索
   */
  async categorySearch(query, options) {
    const mockResults = this.generateMockResults(query, 'category')
    
    return mockResults.filter(result => 
      result.category && result.category.includes(query)
    )
  },

  /**
   * 标签搜索
   */
  async tagSearch(query, options) {
    const mockResults = this.generateMockResults(query, 'tag')
    
    return mockResults.filter(result => 
      result.tags && result.tags.some(tag => tag.includes(query))
    )
  },

  /**
   * 获取搜索建议
   */
  async getSuggestions(query, limit = 10) {
    try {
      console.log(`🔍 获取搜索建议: "${query}"`)
      
      const suggestions = []
      
      // 历史搜索建议
      const historySuggestions = this.getHistorySuggestions(query)
      suggestions.push(...historySuggestions)
      
      // 热门关键词建议
      const popularSuggestions = this.getPopularSuggestions(query)
      suggestions.push(...popularSuggestions)
      
      // 个性化建议
      if (this.config.enablePersonalization) {
        const personalSuggestions = this.getPersonalizedSuggestions(query)
        suggestions.push(...personalSuggestions)
      }
      
      // 去重并排序
      const uniqueSuggestions = [...new Set(suggestions)]
        .slice(0, limit)
        .map(suggestion => ({
          text: suggestion,
          type: this.getSuggestionType(suggestion),
          score: this.calculateSuggestionScore(suggestion, query)
        }))
        .sort((a, b) => b.score - a.score)
      
      console.log(`✅ 生成搜索建议: ${uniqueSuggestions.length} 个`)
      return uniqueSuggestions
      
    } catch (error) {
      console.error('❌ 获取搜索建议失败:', error)
      return []
    }
  },

  /**
   * 获取搜索历史
   */
  getSearchHistory(limit = 20) {
    return this.state.searchHistory
      .slice(0, limit)
      .map(item => ({
        ...item,
        timeAgo: this.getTimeAgo(item.timestamp)
      }))
  },

  /**
   * 清除搜索历史
   */
  clearSearchHistory() {
    this.state.searchHistory = []
    this.saveSearchHistory()
    console.log('🗑️ 搜索历史已清除')
  },

  /**
   * 获取热门搜索
   */
  getPopularSearches(limit = 10) {
    return this.state.popularKeywords.slice(0, limit)
  },

  /**
   * 获取个性化推荐
   */
  getPersonalizedRecommendations(limit = 10) {
    if (!this.config.enablePersonalization) {
      return []
    }

    const preferences = this.state.userPreferences
    const recommendations = []

    // 基于搜索历史推荐
    if (preferences.favoriteCategories) {
      recommendations.push(...preferences.favoriteCategories.slice(0, 3))
    }

    // 基于搜索频率推荐
    if (preferences.frequentKeywords) {
      recommendations.push(...preferences.frequentKeywords.slice(0, 5))
    }

    return recommendations.slice(0, limit)
  },

  // 辅助方法

  /**
   * 预处理查询
   */
  preprocessQuery(query) {
    return query
      .trim()
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文、英文、数字
  },

  /**
   * 生成模拟搜索结果（已禁用，不再提供虚拟数据）
   */
  generateMockResults(query, type) {
    console.warn('⚠️ generateMockResults被调用，但不再提供虚拟数据');
    // 不再提供模拟数据，返回空数组，确保只显示真实数据
    return [];
  },

  /**
   * 计算关键词相关度
   */
  calculateKeywordRelevance(query, result) {
    let score = 0
    
    // 标题匹配
    if (result.title.includes(query)) {
      score += 10
    }
    
    // 分类匹配
    if (result.category && result.category.includes(query)) {
      score += 5
    }
    
    // 标签匹配
    if (result.tags) {
      const matchingTags = result.tags.filter(tag => tag.includes(query))
      score += matchingTags.length * 3
    }
    
    return score
  },

  /**
   * 计算模糊相关度
   */
  calculateFuzzyRelevance(query, result) {
    // 简化的模糊匹配算法
    const similarity = this.calculateStringSimilarity(query, result.title)
    return similarity * 10
  },

  /**
   * 计算语义相关度
   */
  calculateSemanticRelevance(query, result) {
    // 简化的语义匹配（实际项目中可能使用NLP技术）
    const semanticKeywords = {
      '开心': ['笑', '快乐', '高兴', '愉快'],
      '难过': ['哭', '伤心', '悲伤', '沮丧'],
      '可爱': ['萌', '甜美', '温柔', '小巧']
    }
    
    let score = 0
    for (const [key, synonyms] of Object.entries(semanticKeywords)) {
      if (query.includes(key)) {
        synonyms.forEach(synonym => {
          if (result.title.includes(synonym) || result.tags?.includes(synonym)) {
            score += 5
          }
        })
      }
    }
    
    return score
  },

  /**
   * 字符串相似度计算
   */
  calculateStringSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.calculateEditDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  },

  /**
   * 计算编辑距离
   */
  calculateEditDistance(str1, str2) {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  },

  /**
   * 应用过滤器
   */
  applyFilters(results, filters) {
    return results.filter(result => {
      for (const [key, value] of Object.entries(filters)) {
        if (result[key] !== value) {
          return false
        }
      }
      return true
    })
  },

  /**
   * 排序结果
   */
  sortResults(results, sortBy, query) {
    switch (sortBy) {
      case 'relevance':
        return results.sort((a, b) => (b.relevance || 0) - (a.relevance || 0))
      case 'popularity':
        return results.sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
      case 'date':
        return results.sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0))
      default:
        return results
    }
  },

  /**
   * 生成搜索建议
   */
  async generateSuggestions(query) {
    const suggestions = await this.getSuggestions(query, 5)
    return suggestions.map(s => s.text)
  },

  /**
   * 获取相关关键词
   */
  getRelatedKeywords(query) {
    // 简化的相关关键词生成
    const related = {
      '笑': ['开心', '快乐', '搞笑', '幽默'],
      '哭': ['难过', '伤心', '眼泪', '悲伤'],
      '可爱': ['萌', '甜美', '小巧', '温柔']
    }
    
    return related[query] || []
  },

  /**
   * 缓存相关方法
   */
  getCachedResults(query, options) {
    const cacheKey = `search_${this.hashQuery(query, options)}`
    const cached = SmartCache.get(cacheKey, 'search')
    if (cached) {
      cached.cached = true
    }
    return cached
  },

  cacheResults(query, options, results) {
    const cacheKey = `search_${this.hashQuery(query, options)}`
    SmartCache.set(cacheKey, results, 'search', this.config.cacheTimeout)
  },

  hashQuery(query, options) {
    const str = JSON.stringify({ query, ...options })
    return str.split('').reduce((hash, char) => {
      return ((hash << 5) - hash) + char.charCodeAt(0)
    }, 0).toString(36)
  },

  /**
   * 历史记录相关方法
   */
  loadSearchHistory() {
    try {
      if (typeof wx !== 'undefined') {
        const history = wx.getStorageSync('search_history') || []
        this.state.searchHistory = history
      }
    } catch (error) {
      console.warn('⚠️ 加载搜索历史失败:', error)
      this.state.searchHistory = []
    }
  },

  saveSearchHistory() {
    try {
      if (typeof wx !== 'undefined') {
        wx.setStorageSync('search_history', this.state.searchHistory)
      }
    } catch (error) {
      console.warn('⚠️ 保存搜索历史失败:', error)
    }
  },

  addToHistory(query, resultCount) {
    const historyItem = {
      query,
      resultCount,
      timestamp: Date.now()
    }
    
    // 移除重复项
    this.state.searchHistory = this.state.searchHistory.filter(item => item.query !== query)
    
    // 添加到开头
    this.state.searchHistory.unshift(historyItem)
    
    // 限制数量
    if (this.state.searchHistory.length > this.config.maxHistory) {
      this.state.searchHistory = this.state.searchHistory.slice(0, this.config.maxHistory)
    }
    
    this.saveSearchHistory()
  },

  /**
   * 其他辅助方法
   */
  loadPopularKeywords() {
    this.state.popularKeywords = ['笑', '可爱', '开心', '加油', '生气']
  },

  loadUserPreferences() {
    try {
      if (typeof wx !== 'undefined') {
        const preferences = wx.getStorageSync('user_search_preferences') || {}
        this.state.userPreferences = preferences
      }
    } catch (error) {
      console.warn('⚠️ 加载用户偏好失败:', error)
      this.state.userPreferences = {}
    }
  },

  updateUserPreferences(query, category, resultCount) {
    // 更新用户搜索偏好
    if (!this.state.userPreferences.frequentKeywords) {
      this.state.userPreferences.frequentKeywords = []
    }
    
    if (!this.state.userPreferences.favoriteCategories) {
      this.state.userPreferences.favoriteCategories = []
    }
    
    // 更新频繁关键词
    const keywordIndex = this.state.userPreferences.frequentKeywords.findIndex(k => k.keyword === query)
    if (keywordIndex >= 0) {
      this.state.userPreferences.frequentKeywords[keywordIndex].count++
    } else {
      this.state.userPreferences.frequentKeywords.push({ keyword: query, count: 1 })
    }
    
    // 更新喜爱分类
    if (category && resultCount > 0) {
      const categoryIndex = this.state.userPreferences.favoriteCategories.findIndex(c => c.category === category)
      if (categoryIndex >= 0) {
        this.state.userPreferences.favoriteCategories[categoryIndex].count++
      } else {
        this.state.userPreferences.favoriteCategories.push({ category, count: 1 })
      }
    }
    
    // 保存偏好
    try {
      if (typeof wx !== 'undefined') {
        wx.setStorageSync('user_search_preferences', this.state.userPreferences)
      }
    } catch (error) {
      console.warn('⚠️ 保存用户偏好失败:', error)
    }
  },

  getHistorySuggestions(query) {
    return this.state.searchHistory
      .filter(item => item.query.includes(query))
      .map(item => item.query)
      .slice(0, 5)
  },

  getPopularSuggestions(query) {
    return this.state.popularKeywords
      .filter(keyword => keyword.includes(query))
      .slice(0, 3)
  },

  getPersonalizedSuggestions(query) {
    const suggestions = []
    
    if (this.state.userPreferences.frequentKeywords) {
      const frequent = this.state.userPreferences.frequentKeywords
        .filter(item => item.keyword.includes(query))
        .sort((a, b) => b.count - a.count)
        .map(item => item.keyword)
        .slice(0, 3)
      
      suggestions.push(...frequent)
    }
    
    return suggestions
  },

  getSuggestionType(suggestion) {
    if (this.state.searchHistory.some(item => item.query === suggestion)) {
      return 'history'
    }
    if (this.state.popularKeywords.includes(suggestion)) {
      return 'popular'
    }
    return 'personalized'
  },

  calculateSuggestionScore(suggestion, query) {
    let score = 0
    
    // 完全匹配得分更高
    if (suggestion === query) {
      score += 10
    }
    
    // 包含查询得分
    if (suggestion.includes(query)) {
      score += 5
    }
    
    // 历史搜索得分
    const historyItem = this.state.searchHistory.find(item => item.query === suggestion)
    if (historyItem) {
      score += 3
    }
    
    return score
  },

  getTimeAgo(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
    return `${Math.floor(diff / 86400000)}天前`
  },

  fallbackSearch(query, options) {
    // 降级搜索逻辑
    console.log('🔍 使用降级搜索')
    const results = this.generateMockResults(query, 'fallback')
    
    return {
      query,
      results,
      total: results.length,
      hasMore: false,
      suggestions: [],
      relatedKeywords: [],
      searchTime: Date.now(),
      fallback: true
    }
  },

  /**
   * 获取搜索统计
   */
  getSearchStats() {
    return {
      initialized: this.state.initialized,
      historyCount: this.state.searchHistory.length,
      popularKeywordsCount: this.state.popularKeywords.length,
      userPreferences: Object.keys(this.state.userPreferences).length,
      config: this.config
    }
  },

  /**
   * 清理资源
   */
  cleanup() {
    this.state.searchHistory = []
    this.state.popularKeywords = []
    this.state.userPreferences = {}
    console.log('🧹 高级搜索模块资源已清理')
  }
}

module.exports = {
  AdvancedSearch
}
