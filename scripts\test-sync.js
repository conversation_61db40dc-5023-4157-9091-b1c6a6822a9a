/**
 * 数据同步测试脚本
 * 全面测试管理后台和小程序的数据同步功能
 */

const http = require('http')
const https = require('https')

class SyncTester {
  constructor() {
    this.baseUrl = 'http://localhost:8001'
    this.testResults = []
    this.errors = []
    this.warnings = []
  }

  /**
   * HTTP请求封装
   */
  async request(url, options = {}) {
    return new Promise((resolve, reject) => {
      const { method = 'GET', data, headers = {} } = options
      
      const requestOptions = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      }
      
      const req = http.request(url, requestOptions, (res) => {
        let body = ''
        res.on('data', chunk => body += chunk)
        res.on('end', () => {
          try {
            const result = JSON.parse(body)
            resolve({ status: res.statusCode, data: result })
          } catch (error) {
            resolve({ status: res.statusCode, data: body })
          }
        })
      })
      
      req.on('error', reject)
      
      if (data) {
        req.write(JSON.stringify(data))
      }
      
      req.end()
    })
  }

  /**
   * 记录测试结果
   */
  logResult(test, success, message, data = null) {
    const result = {
      test,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    }
    
    this.testResults.push(result)
    
    const icon = success ? '✅' : '❌'
    console.log(`${icon} ${test}: ${message}`)
    
    if (data && process.env.VERBOSE) {
      console.log('   数据:', JSON.stringify(data, null, 2))
    }
  }

  /**
   * 测试服务器健康状态
   */
  async testServerHealth() {
    console.log('\n🔍 测试服务器健康状态...')
    
    try {
      const response = await this.request(`${this.baseUrl}/health`)
      
      if (response.status === 200 && response.data.status === 'ok') {
        this.logResult('服务器健康检查', true, `服务器正常运行 (${response.data.mode}模式)`, response.data)
        return true
      } else {
        this.logResult('服务器健康检查', false, `服务器状态异常: ${response.status}`, response.data)
        return false
      }
    } catch (error) {
      this.logResult('服务器健康检查', false, `连接失败: ${error.message}`)
      return false
    }
  }

  /**
   * 测试分类CRUD操作
   */
  async testCategoryCRUD() {
    console.log('\n🔍 测试分类CRUD操作...')
    
    let categoryId = null
    
    // 测试创建分类
    try {
      const createData = {
        functionName: 'adminAPI',
        data: {
          action: 'createCategory',
          data: {
            name: `测试分类_${Date.now()}`,
            icon: '🧪',
            status: 'show',
            sort: 99
          }
        }
      }
      
      const createResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
        method: 'POST',
        data: createData
      })
      
      if (createResponse.status === 200 && createResponse.data.success && createResponse.data.result.success) {
        categoryId = createResponse.data.result._id
        this.logResult('创建分类', true, `分类创建成功: ${categoryId}`, createResponse.data.result)
      } else {
        this.logResult('创建分类', false, '分类创建失败', createResponse.data)
        return false
      }
    } catch (error) {
      this.logResult('创建分类', false, `创建分类异常: ${error.message}`)
      return false
    }
    
    // 测试获取分类列表
    try {
      const listResponse = await this.request(`${this.baseUrl}/api/categories`)
      
      if (listResponse.status === 200 && listResponse.data.success) {
        const categories = listResponse.data.data
        const foundCategory = categories.find(cat => cat._id === categoryId)
        
        if (foundCategory) {
          this.logResult('获取分类列表', true, `找到新创建的分类，总数: ${categories.length}`, { count: categories.length })
        } else {
          this.logResult('获取分类列表', false, '新创建的分类未在列表中找到')
        }
      } else {
        this.logResult('获取分类列表', false, '获取分类列表失败', listResponse.data)
      }
    } catch (error) {
      this.logResult('获取分类列表', false, `获取分类列表异常: ${error.message}`)
    }
    
    // 测试更新分类
    if (categoryId) {
      try {
        const updateData = {
          functionName: 'adminAPI',
          data: {
            action: 'updateCategory',
            id: categoryId,
            data: {
              name: `更新测试分类_${Date.now()}`,
              icon: '🔄',
              status: 'show',
              sort: 88
            }
          }
        }
        
        const updateResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
          method: 'POST',
          data: updateData
        })
        
        if (updateResponse.status === 200 && updateResponse.data.success && updateResponse.data.result.success) {
          this.logResult('更新分类', true, '分类更新成功', updateResponse.data.result)
        } else {
          this.logResult('更新分类', false, '分类更新失败', updateResponse.data)
        }
      } catch (error) {
        this.logResult('更新分类', false, `更新分类异常: ${error.message}`)
      }
    }
    
    // 测试删除分类
    if (categoryId) {
      try {
        const deleteData = {
          functionName: 'adminAPI',
          data: {
            action: 'deleteCategory',
            id: categoryId
          }
        }
        
        const deleteResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
          method: 'POST',
          data: deleteData
        })
        
        if (deleteResponse.status === 200 && deleteResponse.data.success && deleteResponse.data.result.success) {
          this.logResult('删除分类', true, '分类删除成功', deleteResponse.data.result)
        } else {
          this.logResult('删除分类', false, '分类删除失败', deleteResponse.data)
        }
      } catch (error) {
        this.logResult('删除分类', false, `删除分类异常: ${error.message}`)
      }
    }
    
    return true
  }

  /**
   * 测试表情包CRUD操作
   */
  async testEmojiCRUD() {
    console.log('\n🔍 测试表情包CRUD操作...')
    
    let emojiId = null
    
    // 测试创建表情包
    try {
      const createData = {
        functionName: 'adminAPI',
        data: {
          action: 'addEmoji',
          data: {
            title: `测试表情包_${Date.now()}`,
            category: '情感表达',
            status: 'published',
            likes: 0,
            downloads: 0,
            tags: ['测试', '自动化']
          }
        }
      }
      
      const createResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
        method: 'POST',
        data: createData
      })
      
      if (createResponse.status === 200 && createResponse.data.success && createResponse.data.result.success) {
        emojiId = createResponse.data.result._id
        this.logResult('创建表情包', true, `表情包创建成功: ${emojiId}`, createResponse.data.result)
      } else {
        this.logResult('创建表情包', false, '表情包创建失败', createResponse.data)
        return false
      }
    } catch (error) {
      this.logResult('创建表情包', false, `创建表情包异常: ${error.message}`)
      return false
    }
    
    // 测试获取表情包列表
    try {
      const listResponse = await this.request(`${this.baseUrl}/api/emojis`)
      
      if (listResponse.status === 200 && listResponse.data.success) {
        const emojis = listResponse.data.data
        const foundEmoji = emojis.find(emoji => emoji._id === emojiId)
        
        if (foundEmoji) {
          this.logResult('获取表情包列表', true, `找到新创建的表情包，总数: ${emojis.length}`, { count: emojis.length })
        } else {
          this.logResult('获取表情包列表', false, '新创建的表情包未在列表中找到')
        }
      } else {
        this.logResult('获取表情包列表', false, '获取表情包列表失败', listResponse.data)
      }
    } catch (error) {
      this.logResult('获取表情包列表', false, `获取表情包列表异常: ${error.message}`)
    }
    
    // 测试获取表情包详情
    if (emojiId) {
      try {
        const detailData = {
          functionName: 'dataAPI',
          data: {
            action: 'getEmojiDetail',
            data: { id: emojiId }
          }
        }
        
        const detailResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
          method: 'POST',
          data: detailData
        })
        
        if (detailResponse.status === 200 && detailResponse.data.success && detailResponse.data.result.success) {
          const emoji = detailResponse.data.result.data
          if (emoji && emoji._id === emojiId) {
            this.logResult('获取表情包详情', true, '表情包详情获取成功', { title: emoji.title })
          } else {
            this.logResult('获取表情包详情', false, '表情包详情不匹配')
          }
        } else {
          this.logResult('获取表情包详情', false, '获取表情包详情失败', detailResponse.data)
        }
      } catch (error) {
        this.logResult('获取表情包详情', false, `获取表情包详情异常: ${error.message}`)
      }
    }
    
    // 清理：删除测试表情包
    if (emojiId) {
      try {
        const deleteData = {
          functionName: 'adminAPI',
          data: {
            action: 'deleteEmoji',
            id: emojiId
          }
        }
        
        const deleteResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
          method: 'POST',
          data: deleteData
        })
        
        if (deleteResponse.status === 200 && deleteResponse.data.success && deleteResponse.data.result.success) {
          this.logResult('删除表情包', true, '表情包删除成功', deleteResponse.data.result)
        } else {
          this.logResult('删除表情包', false, '表情包删除失败', deleteResponse.data)
        }
      } catch (error) {
        this.logResult('删除表情包', false, `删除表情包异常: ${error.message}`)
      }
    }
    
    return true
  }

  /**
   * 测试数据API兼容性
   */
  async testDataAPICompatibility() {
    console.log('\n🔍 测试数据API兼容性...')
    
    // 测试ping
    try {
      const pingData = {
        functionName: 'dataAPI',
        data: { action: 'ping' }
      }
      
      const pingResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
        method: 'POST',
        data: pingData
      })
      
      if (pingResponse.status === 200 && pingResponse.data.success && pingResponse.data.result.success) {
        this.logResult('DataAPI Ping', true, 'Ping测试成功', pingResponse.data.result)
      } else {
        this.logResult('DataAPI Ping', false, 'Ping测试失败', pingResponse.data)
      }
    } catch (error) {
      this.logResult('DataAPI Ping', false, `Ping测试异常: ${error.message}`)
    }
    
    // 测试获取分类（小程序格式）
    try {
      const categoriesData = {
        functionName: 'dataAPI',
        data: { action: 'getCategories' }
      }
      
      const categoriesResponse = await this.request(`${this.baseUrl}/api/cloud-function`, {
        method: 'POST',
        data: categoriesData
      })
      
      if (categoriesResponse.status === 200 && categoriesResponse.data.success && categoriesResponse.data.result.success) {
        const categories = categoriesResponse.data.result.data
        this.logResult('DataAPI获取分类', true, `获取到${categories.length}个分类`, { count: categories.length })
      } else {
        this.logResult('DataAPI获取分类', false, '获取分类失败', categoriesResponse.data)
      }
    } catch (error) {
      this.logResult('DataAPI获取分类', false, `获取分类异常: ${error.message}`)
    }
    
    return true
  }

  /**
   * 测试统计信息
   */
  async testStatistics() {
    console.log('\n🔍 测试统计信息...')
    
    try {
      const statsResponse = await this.request(`${this.baseUrl}/api/stats`)
      
      if (statsResponse.status === 200 && statsResponse.data.success) {
        const stats = statsResponse.data.data
        this.logResult('获取统计信息', true, '统计信息获取成功', stats)
        
        // 验证统计数据结构
        const requiredFields = ['categories', 'emojis', 'banners', 'users', 'totalLikes', 'totalDownloads', 'totalCollections']
        const missingFields = requiredFields.filter(field => stats[field] === undefined)
        
        if (missingFields.length === 0) {
          this.logResult('统计数据结构', true, '统计数据结构完整')
        } else {
          this.logResult('统计数据结构', false, `缺少字段: ${missingFields.join(', ')}`)
        }
      } else {
        this.logResult('获取统计信息', false, '获取统计信息失败', statsResponse.data)
      }
    } catch (error) {
      this.logResult('获取统计信息', false, `获取统计信息异常: ${error.message}`)
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('========================================')
    console.log('🧪 开始数据同步测试...')
    console.log('========================================')
    
    const startTime = Date.now()
    
    // 运行各项测试
    const serverHealthy = await this.testServerHealth()
    
    if (serverHealthy) {
      await this.testCategoryCRUD()
      await this.testEmojiCRUD()
      await this.testDataAPICompatibility()
      await this.testStatistics()
    } else {
      console.log('❌ 服务器不健康，跳过其他测试')
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 生成测试报告
    this.generateReport(duration)
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    console.log('\n========================================')
    console.log('📊 测试报告')
    console.log('========================================')
    
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.success).length
    const failedTests = totalTests - passedTests
    const passRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0
    
    console.log(`📈 测试统计:`)
    console.log(`   总测试数: ${totalTests}`)
    console.log(`   通过: ${passedTests}`)
    console.log(`   失败: ${failedTests}`)
    console.log(`   通过率: ${passRate}%`)
    console.log(`   耗时: ${duration}ms`)
    
    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`))
    }
    
    console.log('\n========================================')
    
    if (passRate >= 80) {
      console.log('🎉 测试通过！系统运行正常')
      process.exit(0)
    } else {
      console.log('❌ 测试失败！请检查系统配置')
      process.exit(1)
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new SyncTester()
  tester.runAllTests().catch(error => {
    console.error('测试过程出错:', error)
    process.exit(1)
  })
}

module.exports = SyncTester
