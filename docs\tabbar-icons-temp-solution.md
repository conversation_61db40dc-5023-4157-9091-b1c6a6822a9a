# TabBar 图标临时解决方案

由于无法直接创建图标文件，提供以下临时解决方案：

## 方案一：使用 Emoji 字符（推荐）

修改 app.json 中的 text 字段：

```json
"list": [
  {
    "pagePath": "pages/index/index",
    "text": "🏠 首页"
  },
  {
    "pagePath": "pages/search/search", 
    "text": "🔍 搜索"
  },
  {
    "pagePath": "pages/category/category",
    "text": "📂 分类"
  },
  {
    "pagePath": "pages/profile/profile",
    "text": "👤 我的"
  }
]
```

## 方案二：创建简单图标文件

### 1. 使用在线工具制作图标
- 访问 https://www.iconfont.cn/
- 搜索并下载以下图标：
  - home (首页)
  - search (搜索)
  - category/grid (分类)
  - user/profile (我的)

### 2. 图标参数设置
- 尺寸: 81px × 81px
- 格式: PNG
- 颜色: 
  - 未选中: #999999
  - 选中: #8B5CF6

### 3. 文件命名和路径
```
images/tabbar/
├── home.png
├── home-active.png
├── search.png
├── search-active.png
├── category.png
├── category-active.png
├── profile.png
└── profile-active.png
```

## 方案三：使用纯色方块图标（开发测试用）

如果需要快速测试，可以创建纯色方块图标：
- 创建 81x81px 的纯色方块
- 未选中状态：浅灰色
- 选中状态：紫色

## 当前配置状态

app.json 文件已经配置完毕，包含：
- ✅ 图标路径配置
- ✅ 选中/未选中状态
- ✅ 正确的页面路径
- ✅ 颜色主题配置

现在只需要准备对应的图标文件即可完成配置。