/* pages/fix-sync/fix-sync.wxss */
.container {
  padding: 20rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2d3748;
}

.alert {
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.alert-danger {
  background: #f8d7da;
  border: 2rpx solid #f5c6cb;
}

.alert-title {
  display: block;
  font-weight: bold;
  color: #721c24;
  margin-bottom: 20rpx;
  font-size: 32rpx;
}

.alert-text {
  display: block;
  color: #721c24;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.step {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.step-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 20rpx;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin: 20rpx 0;
  width: 100%;
}

.btn-success {
  background: #28a745;
}

.btn-danger {
  background: #dc3545;
}

.btn-primary {
  background: #667eea;
  font-size: 36rpx;
  padding: 30rpx;
}

.result {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #495057;
}

.log-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.log-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 20rpx;
}

.log-area {
  background: #2d3748;
  color: #e2e8f0;
  padding: 30rpx;
  border-radius: 12rpx;
  height: 600rpx;
  font-family: 'Monaco', 'Consolas', monospace;
}

.log-text {
  font-size: 24rpx;
  line-height: 1.5;
  white-space: pre-wrap;
}
