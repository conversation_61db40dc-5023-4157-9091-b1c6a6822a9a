// 测试 dataAPI 修复后的功能
// 在微信开发者工具控制台中运行

console.log('🧪 开始测试 dataAPI 修复后的功能');

// 测试1: 检查云函数是否部署成功
async function testCloudFunctionDeployment() {
  console.log('\n📋 测试1: 检查云函数部署状态');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: {
        action: 'ping'
      }
    });
    
    console.log('✅ dataAPI 云函数部署正常:', result.result);
    return true;
  } catch (error) {
    console.error('❌ dataAPI 云函数部署异常:', error);
    return false;
  }
}

// 测试2: 检查数据库连接
async function testDatabaseConnection() {
  console.log('\n📋 测试2: 检查数据库连接');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: {
        action: 'testDB'
      }
    });
    
    console.log('✅ 数据库连接测试结果:', result.result);
    return result.result.success;
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error);
    return false;
  }
}

// 测试3: 检查是否有base64图片需要处理
async function checkBase64Images() {
  console.log('\n📋 测试3: 检查base64图片');
  
  try {
    // 直接查询数据库检查base64图片
    const db = wx.cloud.database();
    const result = await db.collection('emojis')
      .where({
        imageUrl: db.RegExp({
          regexp: '^data:image/',
          options: 'i'
        })
      })
      .limit(5)
      .get();
    
    console.log(`📊 发现 ${result.data.length} 个base64图片需要处理`);
    
    if (result.data.length > 0) {
      console.log('📸 base64图片示例:');
      result.data.forEach((item, index) => {
        console.log(`  ${index + 1}. ID: ${item._id}, 图片长度: ${item.imageUrl.length} 字符`);
      });
    }
    
    return result.data.length;
  } catch (error) {
    console.error('❌ 检查base64图片失败:', error);
    return -1;
  }
}

// 测试4: 测试getEmojis函数（小批量）
async function testGetEmojis() {
  console.log('\n📋 测试4: 测试getEmojis函数');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: {
        action: 'getEmojis',
        data: {
          category: 'all',
          page: 1,
          limit: 2  // 只测试2条数据，避免超时
        }
      }
    });
    
    console.log('✅ getEmojis 调用成功:', result.result);
    
    if (result.result.success && result.result.data) {
      console.log(`📦 返回数据数量: ${result.result.data.length}`);
      
      result.result.data.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.title}`);
        console.log(`     图片URL: ${item.imageUrl.substring(0, 100)}${item.imageUrl.length > 100 ? '...' : ''}`);
        console.log(`     URL类型: ${item.imageUrl.startsWith('cloud://') ? '云存储' : 
                                   item.imageUrl.startsWith('data:image/') ? 'base64' : 
                                   item.imageUrl.startsWith('http') ? 'HTTP' : '其他'}`);
      });
    }
    
    return result.result.success;
  } catch (error) {
    console.error('❌ getEmojis 测试失败:', error);
    console.error('错误详情:', error.message);
    return false;
  }
}

// 测试5: 检查页面数据设置
function testPageDataSetting() {
  console.log('\n📋 测试5: 检查页面数据设置');
  
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  
  if (!currentPage) {
    console.error('❌ 无法获取当前页面实例');
    return false;
  }
  
  console.log('✅ 当前页面:', currentPage.route);
  console.log('📊 页面数据状态:');
  console.log(`  - emojiList: ${currentPage.data.emojiList?.length || 0} 条`);
  console.log(`  - searchResults: ${currentPage.data.searchResults?.length || 0} 条`);
  console.log(`  - 显示条件: ${currentPage.data.searchResults?.length === 0 && currentPage.data.emojiList?.length > 0}`);
  
  return true;
}

// 运行完整测试
async function runCompleteTest() {
  console.log('🚀 开始完整测试流程');
  console.log('='.repeat(60));
  
  const results = {
    deployment: false,
    database: false,
    base64Check: -1,
    getEmojis: false,
    pageData: false
  };
  
  // 测试1: 云函数部署
  results.deployment = await testCloudFunctionDeployment();
  
  if (!results.deployment) {
    console.log('\n❌ 云函数部署失败，请先重新部署 dataAPI 云函数');
    return results;
  }
  
  // 测试2: 数据库连接
  results.database = await testDatabaseConnection();
  
  if (!results.database) {
    console.log('\n❌ 数据库连接失败，请检查云开发环境');
    return results;
  }
  
  // 测试3: base64图片检查
  results.base64Check = await checkBase64Images();
  
  // 测试4: getEmojis函数
  results.getEmojis = await testGetEmojis();
  
  // 测试5: 页面数据
  results.pageData = testPageDataSetting();
  
  // 输出测试总结
  console.log('\n📊 测试总结');
  console.log('='.repeat(60));
  console.log(`✅ 云函数部署: ${results.deployment ? '正常' : '失败'}`);
  console.log(`✅ 数据库连接: ${results.database ? '正常' : '失败'}`);
  console.log(`📸 base64图片: ${results.base64Check >= 0 ? `${results.base64Check} 个` : '检查失败'}`);
  console.log(`✅ getEmojis函数: ${results.getEmojis ? '正常' : '失败'}`);
  console.log(`✅ 页面数据: ${results.pageData ? '正常' : '异常'}`);
  
  if (results.deployment && results.database && results.getEmojis) {
    console.log('\n🎉 基本功能测试通过！');
    
    if (results.base64Check > 0) {
      console.log(`💡 发现 ${results.base64Check} 个base64图片，将在查询时自动转换为云存储URL`);
    }
  } else {
    console.log('\n❌ 测试未完全通过，请检查上述失败项目');
  }
  
  return results;
}

// 导出测试函数
window.testDataAPI = {
  runCompleteTest,
  testCloudFunctionDeployment,
  testDatabaseConnection,
  checkBase64Images,
  testGetEmojis,
  testPageDataSetting
};

console.log('📋 测试脚本已加载');
console.log('💡 使用方法:');
console.log('  - testDataAPI.runCompleteTest() - 运行完整测试');
console.log('  - testDataAPI.testGetEmojis() - 单独测试getEmojis');
console.log('  - testDataAPI.checkBase64Images() - 检查base64图片');

// 自动运行完整测试
runCompleteTest();
