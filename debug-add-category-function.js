// 调试addCategoryAdvanced函数
const { chromium } = require('playwright');

async function debugAddCategoryFunction() {
    console.log('🔍 调试addCategoryAdvanced函数...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        console.log(`[CONSOLE] ${msg.text()}`);
    });
    
    // 监听JavaScript错误
    page.on('pageerror', error => {
        console.log(`[PAGE ERROR] ${error.message}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查addCategoryAdvanced函数是否存在');
        
        // 检查函数是否存在
        const functionCheck = await page.evaluate(() => {
            return {
                addCategoryAdvancedExists: typeof addCategoryAdvanced === 'function',
                addCategoryExists: typeof addCategory === 'function',
                saveCategoryUnifiedExists: typeof saveCategoryUnified === 'function'
            };
        });
        
        console.log('📊 函数存在性检查:');
        console.log('addCategoryAdvanced存在:', functionCheck.addCategoryAdvancedExists);
        console.log('addCategory存在:', functionCheck.addCategoryExists);
        console.log('saveCategoryUnified存在:', functionCheck.saveCategoryUnifiedExists);
        
        if (!functionCheck.addCategoryAdvancedExists) {
            console.log('❌ addCategoryAdvanced函数不存在');
            return { success: false, error: 'addCategoryAdvanced函数不存在' };
        }
        
        console.log('\n📍 手动调用addCategoryAdvanced函数');
        
        // 手动调用函数
        const manualCall = await page.evaluate(() => {
            try {
                console.log('🔧 手动调用addCategoryAdvanced...');
                addCategoryAdvanced();
                
                // 检查弹窗是否创建
                const modal = document.querySelector('[style*="position: fixed"]');
                if (modal) {
                    return {
                        success: true,
                        modalExists: true,
                        modalHTML: modal.innerHTML.substring(0, 1000)
                    };
                } else {
                    return {
                        success: false,
                        error: '弹窗未创建'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    stack: error.stack
                };
            }
        });
        
        console.log('📊 手动调用结果:');
        console.log('成功:', manualCall.success);
        if (manualCall.success) {
            console.log('弹窗存在:', manualCall.modalExists);
            console.log('弹窗HTML预览:', manualCall.modalHTML);
        } else {
            console.log('错误:', manualCall.error);
            if (manualCall.stack) {
                console.log('错误堆栈:', manualCall.stack);
            }
        }
        
        if (manualCall.success) {
            console.log('\n📍 检查表单元素');
            
            // 检查表单元素
            const formCheck = await page.evaluate(() => {
                const modal = document.querySelector('[style*="position: fixed"]');
                if (!modal) return { modalExists: false };
                
                const form = modal.querySelector('#add-category-form');
                const nameInput = modal.querySelector('#category-name');
                const iconInput = modal.querySelector('#category-icon');
                const gradientInput = modal.querySelector('#category-gradient');
                const gradientPreset = modal.querySelector('#category-gradient-preset');
                const submitBtn = modal.querySelector('button[type="submit"]');
                
                return {
                    modalExists: true,
                    formExists: !!form,
                    nameInputExists: !!nameInput,
                    iconInputExists: !!iconInput,
                    gradientInputExists: !!gradientInput,
                    gradientPresetExists: !!gradientPreset,
                    submitBtnExists: !!submitBtn,
                    allElements: Array.from(modal.querySelectorAll('*')).length
                };
            });
            
            console.log('📊 表单元素检查:');
            console.log('表单存在:', formCheck.formExists);
            console.log('名称输入框存在:', formCheck.nameInputExists);
            console.log('图标输入框存在:', formCheck.iconInputExists);
            console.log('渐变输入框存在:', formCheck.gradientInputExists);
            console.log('渐变预设存在:', formCheck.gradientPresetExists);
            console.log('提交按钮存在:', formCheck.submitBtnExists);
            console.log('总元素数量:', formCheck.allElements);
            
            if (formCheck.formExists && formCheck.nameInputExists && formCheck.submitBtnExists) {
                console.log('✅ 表单结构完整');
            } else {
                console.log('🔴 表单结构不完整');
            }
        }
        
        console.log('\n📍 点击页面上的添加分类按钮');
        
        // 关闭手动创建的弹窗
        await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (modal) {
                document.body.removeChild(modal);
            }
        });
        
        // 点击页面上的按钮
        const addBtn = await page.locator('text=➕ 添加分类').first();
        if (await addBtn.isVisible()) {
            await addBtn.click();
            console.log('✅ 已点击页面上的添加分类按钮');
            await page.waitForTimeout(3000);
            
            // 再次检查弹窗
            const clickResult = await page.evaluate(() => {
                const modal = document.querySelector('[style*="position: fixed"]');
                return {
                    modalExists: !!modal,
                    modalHTML: modal ? modal.innerHTML.substring(0, 500) : null
                };
            });
            
            console.log('📊 点击按钮后的结果:');
            console.log('弹窗存在:', clickResult.modalExists);
            console.log('弹窗HTML预览:', clickResult.modalHTML);
        } else {
            console.log('❌ 添加分类按钮不可见');
        }
        
        // 截图
        await page.screenshot({ path: 'debug-add-category-function.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-add-category-function.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            functionExists: functionCheck.addCategoryAdvancedExists,
            manualCallSuccess: manualCall.success
        };
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        await page.screenshot({ path: 'debug-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行调试
debugAddCategoryFunction().then(result => {
    console.log('\n🎯 调试结果:', result);
}).catch(console.error);
