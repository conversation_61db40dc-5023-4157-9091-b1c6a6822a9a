<!--pages/detail/detail-new.wxml - 重构版表情包详情页-->

<view class="container">
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-skeleton">
      <view class="skeleton-image"></view>
      <view class="skeleton-info">
        <view class="skeleton-title"></view>
        <view class="skeleton-meta"></view>
        <view class="skeleton-tags">
          <view class="skeleton-tag"></view>
          <view class="skeleton-tag"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error && !loading}}">
    <view class="error-content">
      <text class="error-icon">😵</text>
      <text class="error-message">{{error}}</text>
      <button class="retry-btn" bindtap="onRetry">重试</button>
    </view>
  </view>

  <!-- 详情内容 -->
  <view class="detail-content" wx:if="{{emojiData && !loading && !error}}">
    
    <!-- 表情包图片 -->
    <view class="emoji-image-container">
      <image 
        class="emoji-image" 
        src="{{emojiData.imageUrl}}" 
        mode="aspectFit"
        lazy-load="{{true}}"
        show-menu-by-longpress="{{true}}"
      />
    </view>

    <!-- 表情包信息 -->
    <view class="emoji-info-card">
      <!-- 标题和基本信息 -->
      <view class="emoji-header">
        <text class="emoji-title">{{emojiData.title}}</text>
        <view class="emoji-meta" wx:if="{{emojiData.description}}">
          <text class="emoji-description">{{emojiData.description}}</text>
        </view>
      </view>

      <!-- 分类和标签信息 - 按截图样式重新设计 -->
      <view class="category-tags-section">
        <!-- 第一行：分类 - 紫色背景 -->
        <view class="category-row" wx:if="{{emojiData.categoryName}}">
          <text class="category-tag">{{emojiData.categoryName}}</text>
        </view>

        <!-- 第二行：标签 - 灰色边框 -->
        <view class="hashtags-row" wx:if="{{emojiData.tags && emojiData.tags.length > 0}}">
          <text class="hashtag-item" wx:for="{{emojiData.tags}}" wx:key="*this">#{{item}}</text>
        </view>
      </view>

      <!-- 统计数据 - 已隐藏 -->
      <view class="stats-container" wx:if="{{false}}">
        <view class="stat-item">
          <text class="stat-icon">👁</text>
          <text class="stat-number">{{emojiData.viewsText || emojiData.views || 0}}</text>
          <text class="stat-label">浏览</text>
        </view>
        <view class="stat-item">
          <text class="stat-icon">⬇️</text>
          <text class="stat-number">{{emojiData.downloadsText || emojiData.downloads || 0}}</text>
          <text class="stat-label">下载</text>
        </view>

      </view>
    </view>

    <!-- 操作按钮 - 暂时隐藏点赞收藏功能 -->
    <view class="action-buttons" wx:if="{{false}}">
      <view class="action-row">
        <!-- 点赞按钮 -->
        <button
          class="action-btn like-btn {{isLiked ? 'liked' : ''}}"
          bindtap="onLike"
          disabled="{{actionLoading.like}}"
          hover-class="btn-hover"
          hover-stay-time="100"
        >
          <text class="btn-icon">{{isLiked ? '❤️' : '🤍'}}</text>
          <text class="btn-text">{{isLiked ? '已点赞' : '点赞'}}</text>
        </button>

        <!-- 收藏按钮 -->
        <button
          class="action-btn collect-btn {{isCollected ? 'collected' : ''}}"
          bindtap="onCollect"
          disabled="{{actionLoading.collect}}"
          hover-class="btn-hover"
          hover-stay-time="100"
        >
          <text class="btn-icon">{{isCollected ? '⭐' : '☆'}}</text>
          <text class="btn-text">{{isCollected ? '已收藏' : '收藏'}}</text>
        </button>
      </view>

      <view class="action-row">
        <!-- 下载按钮 -->
        <button 
          class="action-btn download-btn"
          bindtap="onDownload"
          disabled="{{actionLoading.download}}"
        >
          <text class="btn-icon">⬇️</text>
          <text class="btn-text">下载保存</text>
        </button>

        <!-- 分享按钮 -->
        <button 
          class="action-btn share-btn"
          open-type="share"
        >
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享好友</text>
        </button>
      </view>
    </view>

    <!-- 新的操作按钮 - 下载和分享 - 已隐藏 -->
    <view class="download-actions" wx:if="{{false}}">
      <button
        class="download-btn primary-btn"
        bindtap="onDownload"
        hover-class="btn-hover"
        hover-stay-time="100"
      >
        <text class="btn-icon">⬇️</text>
        <text class="btn-text">保存到相册</text>
      </button>

      <button
        class="share-btn secondary-btn"
        open-type="share"
        hover-class="btn-hover"
        hover-stay-time="100"
      >
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享好友</text>
      </button>
    </view>

    <!-- 相关推荐 - 与首页样式保持一致 -->
    <view wx:if="{{relatedEmojis.length > 0}}">
      <!-- 标题区域 -->
      <view class="related-header">
        <text class="related-title">相关推荐</text>
      </view>

      <!-- 表情包列表 - 与首页完全一致的样式 -->
      <view class="related-emoji-list">
        <view
          class="related-emoji-item"
          wx:for="{{relatedEmojis}}"
          wx:key="id"
          bindtap="onRelatedTap"
          data-emoji="{{item}}"
        >
          <!-- 表情包图片 -->
          <view class="related-emoji-image-container">
            <image
              class="related-emoji-image"
              src="{{item.imageUrl}}"
              mode="aspectFill"
              lazy-load="{{true}}"
            />
          </view>

          <!-- 表情包信息 -->
          <view class="related-emoji-info">
            <text class="related-emoji-title">{{item.title}}</text>
            <text class="related-emoji-category">{{item.categoryName || item.category}}</text>

            <!-- 标签区域 -->
            <view class="related-emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <view
                class="related-tag-item"
                wx:for="{{item.tags}}"
                wx:for-item="tag"
                wx:key="*this"
              >
                {{tag}}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>
</view>
