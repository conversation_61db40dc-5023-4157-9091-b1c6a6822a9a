// components/login-modal/login-modal.js
const { AuthManager } = require('../../utils/authManager.js')

Component({
  properties: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      value: false
    }
  },

  data: {
    isLoading: false,
    errorMessage: ''
  },

  methods: {
    /**
     * 阻止触摸穿透
     */
    preventTouchMove() {
      return false
    },

    /**
     * 点击遮罩层关闭弹窗
     */
    onMaskTap() {
      this.onClose()
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      this.setData({
        errorMessage: ''
      })
      this.triggerEvent('close')
    },

    /**
     * 登录按钮点击
     */
    onLogin() {
      if (this.data.isLoading) {
        return
      }

      console.log('=== 登录弹窗开始登录流程 ===')

      this.setData({
        isLoading: true,
        errorMessage: ''
      })

      // 重要：必须在用户点击事件的同步执行中调用 getUserProfile
      console.log('开始获取用户信息...')
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (userProfileRes) => {
          console.log('✅ 获取用户信息成功:', userProfileRes.userInfo.nickName)

          // 获取用户信息成功后，继续登录流程
          this.continueLogin(userProfileRes)
        },
        fail: (error) => {
          console.error('❌ 获取用户信息失败:', error)

          let errorMessage = '需要您的授权才能使用完整功能'
          if (error.errMsg && error.errMsg.includes('auth deny')) {
            errorMessage = '您拒绝了授权，无法完成登录'
          }

          this.setData({
            isLoading: false,
            errorMessage: errorMessage
          })
        }
      })
    },

    /**
     * 继续登录流程（在获取用户信息成功后）
     */
    async continueLogin(userProfile) {
      try {
        // 获取微信登录code
        const loginRes = await this.wxLogin()
        console.log('✅ wx.login 成功:', loginRes.code)

        // 直接使用本地登录，避免云函数调用阻塞
        console.log('使用本地登录模式...')
        const cloudLoginRes = {
          success: true,
          openid: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          message: '本地登录成功'
        }
        console.log('✅ 本地登录成功:', cloudLoginRes)

        if (cloudLoginRes.success) {
          console.log('开始保存登录信息...')
          // 保存登录信息
          AuthManager.saveLoginData(cloudLoginRes.openid, userProfile.userInfo)
          console.log('✅ 登录信息保存成功')

          // 登录成功
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })

          console.log('触发登录成功事件...')
          // 触发登录成功事件
          this.triggerEvent('success', {
            userInfo: userProfile.userInfo,
            openid: cloudLoginRes.openid
          })

          // 关闭弹窗
          console.log('✅ 登录弹窗流程完成，关闭弹窗')
          this.onClose()
        } else {
          console.error('❌ 云端登录返回失败:', cloudLoginRes)
          throw new Error(cloudLoginRes.error || '云端登录失败')
        }

      } catch (error) {
        console.error('❌ 继续登录流程异常:', error)

        let errorMessage = '登录失败，请重试'

        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '网络连接超时，请重试'
          } else {
            errorMessage = error.errMsg
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        console.log('显示错误信息:', errorMessage)
        this.setData({
          isLoading: false,
          errorMessage: errorMessage
        })
      }
    },

    /**
     * 隐私政策点击
     */
    onPrivacyTap() {
      wx.showModal({
        title: '隐私政策',
        content: '我们重视您的隐私保护，仅会收集必要的用户信息用于提供更好的服务体验。',
        showCancel: false,
        confirmText: '知道了'
      })
    },

    /**
     * 用户协议点击
     */
    onTermsTap() {
      wx.showModal({
        title: '用户协议',
        content: '请遵守相关法律法规，文明使用本应用，不得发布违法违规内容。',
        showCancel: false,
        confirmText: '知道了'
      })
    },

    /**
     * 微信登录
     */
    wxLogin() {
      return new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })
    },



    /**
     * 云函数登录
     */
    async cloudLogin(code, userInfo) {
      const res = await wx.cloud.callFunction({
        name: 'login',
        data: {
          code: code,
          userInfo: userInfo
        }
      })

      return res.result
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('登录弹窗组件已加载')
    },

    detached() {
      console.log('登录弹窗组件已卸载')
    }
  }
})
