// utils/performanceMonitor.js - 性能监控工具

/**
 * 性能监控工具
 * 用于监控小程序的性能指标和用户体验
 */
const PerformanceMonitor = {
  
  // 性能数据存储
  performanceData: {
    pageLoadTimes: {},
    apiCallTimes: {},
    userActions: [],
    memoryUsage: [],
    errorLogs: [],
    resourceStats: [],
    memoryLeaks: []
  },

  // 内存监控配置
  memoryConfig: {
    checkInterval: 30000,    // 30秒检查一次
    warningThreshold: 50,    // 50MB警告阈值
    criticalThreshold: 80,   // 80MB严重阈值
    maxSamples: 100,         // 最大样本数
    leakDetectionWindow: 10  // 内存泄漏检测窗口
  },

  // 内存监控定时器
  memoryTimer: null,

  /**
   * 初始化性能监控
   */
  init() {
    console.log('🚀 性能监控初始化')
    
    // 监听页面性能
    this.monitorPagePerformance()
    
    // 监听内存使用
    this.monitorMemoryUsage()
    
    // 监听网络状态
    this.monitorNetworkStatus()
    
    // 监听错误
    this.monitorErrors()

    // 开始资源监控
    this.startResourceMonitoring()

    // 开始内存泄漏检测
    this.startMemoryLeakDetection()
  },

  /**
   * 监控页面性能
   */
  monitorPagePerformance() {
    // 在测试环境中跳过Page监控
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
      console.log('📊 测试环境：跳过页面性能监控')
      return
    }

    const originalPage = Page
    const self = this
    
    Page = function(options) {
      const originalOnLoad = options.onLoad || function() {}
      const originalOnShow = options.onShow || function() {}
      
      // 监控页面加载时间
      options.onLoad = function(...args) {
        const startTime = Date.now()
        let pagePath = 'unknown'

        try {
          const pages = getCurrentPages()
          const currentPage = pages && pages.length > 0 ? pages[pages.length - 1] : null
          pagePath = currentPage && currentPage.route ? currentPage.route : 'unknown'
        } catch (error) {
          console.warn('获取页面路径失败:', error)
        }

        originalOnLoad.apply(this, args)

        const loadTime = Date.now() - startTime
        self.recordPageLoadTime(pagePath, loadTime)
      }
      
      // 监控页面显示时间
      options.onShow = function(...args) {
        const startTime = Date.now()
        let pagePath = 'unknown'

        try {
          const pages = getCurrentPages()
          const currentPage = pages && pages.length > 0 ? pages[pages.length - 1] : null
          pagePath = currentPage && currentPage.route ? currentPage.route : 'unknown'
        } catch (error) {
          console.warn('获取页面路径失败:', error)
        }

        originalOnShow.apply(this, args)

        const showTime = Date.now() - startTime
        self.recordPageShowTime(pagePath, showTime)
      }
      
      return originalPage(options)
    }
  },

  /**
   * 记录页面加载时间
   */
  recordPageLoadTime(pagePath, loadTime) {
    if (!this.performanceData.pageLoadTimes[pagePath]) {
      this.performanceData.pageLoadTimes[pagePath] = []
    }
    
    this.performanceData.pageLoadTimes[pagePath].push({
      loadTime,
      timestamp: Date.now()
    })
    
    console.log(`📊 页面加载时间 [${pagePath}]: ${loadTime}ms`)
    
    // 如果加载时间过长，记录警告
    if (loadTime > 1000) {
      console.warn(`⚠️ 页面加载较慢 [${pagePath}]: ${loadTime}ms`)
    }
  },

  /**
   * 记录页面显示时间
   */
  recordPageShowTime(pagePath, showTime) {
    console.log(`📊 页面显示时间 [${pagePath}]: ${showTime}ms`)
  },

  /**
   * 监控API调用性能
   */
  monitorApiCall(apiName, startTime, endTime, success = true) {
    const callTime = endTime - startTime
    
    if (!this.performanceData.apiCallTimes[apiName]) {
      this.performanceData.apiCallTimes[apiName] = []
    }
    
    this.performanceData.apiCallTimes[apiName].push({
      callTime,
      success,
      timestamp: Date.now()
    })
    
    console.log(`🔗 API调用 [${apiName}]: ${callTime}ms ${success ? '✅' : '❌'}`)
    
    // 如果API调用时间过长，记录警告
    if (callTime > 2000) {
      console.warn(`⚠️ API调用较慢 [${apiName}]: ${callTime}ms`)
    }
  },

  /**
   * 监控内存使用
   */
  monitorMemoryUsage() {
    setInterval(() => {
      // 使用新的 API 替代已废弃的 wx.getSystemInfo
      try {
        const systemInfo = wx.getSystemInfoSync()
        const memoryInfo = {
          timestamp: Date.now(),
          platform: systemInfo.platform,
          version: systemInfo.version,
          SDKVersion: systemInfo.SDKVersion
        }

        this.performanceData.memoryUsage.push(memoryInfo)

        // 只保留最近100条记录
        if (this.performanceData.memoryUsage.length > 100) {
          this.performanceData.memoryUsage = this.performanceData.memoryUsage.slice(-100)
        }
      } catch (error) {
        console.warn('⚠️ 获取系统信息失败:', error.message)
      }
    }, 30000) // 每30秒检查一次
  },

  /**
   * 监控网络状态
   */
  monitorNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      console.log(`📶 网络状态变化: ${res.networkType} ${res.isConnected ? '已连接' : '已断开'}`)
      
      this.recordUserAction('network_change', {
        networkType: res.networkType,
        isConnected: res.isConnected
      })
    })
  },

  /**
   * 监控错误
   */
  monitorErrors() {
    const originalConsoleError = console.error
    const self = this
    
    console.error = function(...args) {
      // 记录错误日志
      self.performanceData.errorLogs.push({
        timestamp: Date.now(),
        error: args.join(' '),
        stack: new Error().stack
      })
      
      // 只保留最近50条错误记录
      if (self.performanceData.errorLogs.length > 50) {
        self.performanceData.errorLogs = self.performanceData.errorLogs.slice(-50)
      }
      
      originalConsoleError.apply(console, args)
    }
  },

  /**
   * 记录用户行为
   */
  recordUserAction(action, data = {}) {
    let pagePath = 'unknown'

    try {
      const pages = getCurrentPages()
      const currentPage = pages && pages.length > 0 ? pages[pages.length - 1] : null
      pagePath = currentPage && currentPage.route ? currentPage.route : 'unknown'
    } catch (error) {
      console.warn('获取页面路径失败:', error)
    }

    this.performanceData.userActions.push({
      action,
      data,
      timestamp: Date.now(),
      page: pagePath
    })

    // 只保留最近200条用户行为记录
    if (this.performanceData.userActions.length > 200) {
      this.performanceData.userActions = this.performanceData.userActions.slice(-200)
    }
  },

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const report = {
      summary: this.generateSummary(),
      pageLoadTimes: this.performanceData.pageLoadTimes,
      apiCallTimes: this.performanceData.apiCallTimes,
      userActions: this.performanceData.userActions.slice(-20), // 最近20条
      memoryUsage: this.performanceData.memoryUsage.slice(-10), // 最近10条
      errorLogs: this.performanceData.errorLogs.slice(-10), // 最近10条错误
      timestamp: new Date().toISOString()
    }
    
    return report
  },

  /**
   * 生成性能摘要
   */
  generateSummary() {
    const summary = {
      totalPages: Object.keys(this.performanceData.pageLoadTimes).length,
      totalApiCalls: Object.values(this.performanceData.apiCallTimes).reduce((sum, calls) => sum + calls.length, 0),
      totalUserActions: this.performanceData.userActions.length,
      totalErrors: this.performanceData.errorLogs.length,
      averagePageLoadTime: 0,
      slowestPage: null,
      mostUsedPage: null
    }
    
    // 计算平均页面加载时间
    let totalLoadTime = 0
    let totalLoadCount = 0
    let slowestTime = 0
    let slowestPage = null
    let pageUsageCount = {}
    
    Object.entries(this.performanceData.pageLoadTimes).forEach(([page, times]) => {
      pageUsageCount[page] = times.length
      
      times.forEach(({ loadTime }) => {
        totalLoadTime += loadTime
        totalLoadCount++
        
        if (loadTime > slowestTime) {
          slowestTime = loadTime
          slowestPage = page
        }
      })
    })
    
    if (totalLoadCount > 0) {
      summary.averagePageLoadTime = Math.round(totalLoadTime / totalLoadCount)
    }
    
    summary.slowestPage = slowestPage ? { page: slowestPage, time: slowestTime } : null
    
    // 找出最常用的页面
    const mostUsedPageEntry = Object.entries(pageUsageCount).sort((a, b) => b[1] - a[1])[0]
    summary.mostUsedPage = mostUsedPageEntry ? { page: mostUsedPageEntry[0], count: mostUsedPageEntry[1] } : null
    
    return summary
  },

  /**
   * 打印性能报告
   */
  printPerformanceReport() {
    const report = this.getPerformanceReport()
    
    console.log('=====================================')
    console.log('📊 性能监控报告')
    console.log('=====================================')
    console.log('📱 总页面数:', report.summary.totalPages)
    console.log('🔗 总API调用:', report.summary.totalApiCalls)
    console.log('👆 总用户操作:', report.summary.totalUserActions)
    console.log('❌ 总错误数:', report.summary.totalErrors)
    console.log('⏱️ 平均页面加载时间:', report.summary.averagePageLoadTime + 'ms')
    
    if (report.summary.slowestPage) {
      console.log('🐌 最慢页面:', report.summary.slowestPage.page, report.summary.slowestPage.time + 'ms')
    }
    
    if (report.summary.mostUsedPage) {
      console.log('🔥 最常用页面:', report.summary.mostUsedPage.page, '(' + report.summary.mostUsedPage.count + '次)')
    }
    
    console.log('=====================================')
    
    return report
  },

  /**
   * 清除性能数据
   */
  clearPerformanceData() {
    this.performanceData = {
      pageLoadTimes: {},
      apiCallTimes: {},
      userActions: [],
      memoryUsage: [],
      errorLogs: []
    }
    
    console.log('🧹 性能数据已清除')
  },

  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000

    // 计算最近一小时的性能指标
    const recentPageLoads = Object.values(this.performanceData.pageLoadTimes)
      .filter(time => time.timestamp > oneHourAgo)

    const recentApiCalls = Object.values(this.performanceData.apiCallTimes)
      .filter(call => call.timestamp > oneHourAgo)

    const recentErrors = this.performanceData.errorLogs
      .filter(error => error.timestamp > oneHourAgo)

    return {
      summary: {
        totalPageLoads: recentPageLoads.length,
        averagePageLoadTime: this.calculateAverage(recentPageLoads.map(p => p.loadTime)),
        totalApiCalls: recentApiCalls.length,
        averageApiResponseTime: this.calculateAverage(recentApiCalls.map(a => a.responseTime)),
        totalErrors: recentErrors.length,
        errorRate: (recentErrors.length / Math.max(recentPageLoads.length, 1)) * 100
      },
      details: {
        slowestPages: recentPageLoads
          .sort((a, b) => b.loadTime - a.loadTime)
          .slice(0, 5),
        slowestApis: recentApiCalls
          .sort((a, b) => b.responseTime - a.responseTime)
          .slice(0, 5),
        recentErrors: recentErrors.slice(-10),
        memoryLeakReport: this.getMemoryLeakReport()
      },
      recommendations: this.generatePerformanceRecommendations()
    }
  },

  /**
   * 计算平均值
   */
  calculateAverage(values) {
    if (values.length === 0) return 0
    return values.reduce((sum, val) => sum + val, 0) / values.length
  },

  /**
   * 生成性能优化建议
   */
  generatePerformanceRecommendations() {
    const recommendations = []
    const report = this.getPerformanceReport()

    if (report.summary.averagePageLoadTime > 3000) {
      recommendations.push('页面加载时间较长，建议优化资源加载和代码分割')
    }

    if (report.summary.averageApiResponseTime > 2000) {
      recommendations.push('API响应时间较长，建议优化网络请求和缓存策略')
    }

    if (report.summary.errorRate > 5) {
      recommendations.push('错误率较高，建议加强错误处理和用户体验优化')
    }

    const memoryReport = this.getMemoryLeakReport()
    if (memoryReport.criticalLeaks > 0) {
      recommendations.push('检测到严重内存泄漏，建议检查资源清理机制')
    }

    return recommendations
  },

  /**
   * 开始资源监控
   */
  startResourceMonitoring() {
    // 定期收集资源统计信息
    setInterval(() => {
      this.collectResourceStats()
    }, this.memoryConfig.checkInterval)
  },

  /**
   * 收集资源统计信息
   */
  collectResourceStats() {
    try {
      const ResourceManager = require('./resourceManager.js')
      const { StateManager } = require('./stateManager.js')

      // 安全地获取资源统计
      const resourceStats = (ResourceManager && typeof ResourceManager.getResourceStats === 'function')
        ? ResourceManager.getResourceStats()
        : { error: 'ResourceManager.getResourceStats not available' }

      // 安全地获取状态统计
      const stateStats = (StateManager && typeof StateManager.getListenerStats === 'function')
        ? StateManager.getListenerStats()
        : { error: 'StateManager.getListenerStats not available' }

      const stats = {
        timestamp: Date.now(),
        resources: resourceStats,
        listeners: stateStats,
        memoryUsage: this.getCurrentMemoryUsage()
      }

      this.performanceData.resourceStats.push(stats)

      // 只保留最近的100个样本
      if (this.performanceData.resourceStats.length > this.memoryConfig.maxSamples) {
        this.performanceData.resourceStats.shift()
      }

      // 检查资源泄漏
      this.checkResourceLeaks(stats)

    } catch (error) {
      console.error('❌ 资源统计收集失败:', error)
    }
  },

  /**
   * 获取当前内存使用情况
   */
  getCurrentMemoryUsage() {
    try {
      // 微信小程序环境中获取内存信息
      if (typeof wx !== 'undefined') {
        try {
          const systemInfo = wx.getSystemInfoSync()
          return Promise.resolve({
            platform: systemInfo.platform,
            system: systemInfo.system,
            // 估算内存使用（实际值需要通过其他方式获取）
            estimated: this.estimateMemoryUsage()
          })
        } catch (error) {
          console.warn('⚠️ 获取系统信息失败:', error.message)
          return Promise.resolve({ estimated: this.estimateMemoryUsage() })
        }
      }

      return Promise.resolve({ estimated: this.estimateMemoryUsage() })
    } catch (error) {
      console.error('❌ 内存信息获取失败:', error)
      return Promise.resolve({ estimated: 0 })
    }
  },

  /**
   * 估算内存使用量
   */
  estimateMemoryUsage() {
    let estimatedMemory = 0

    try {
      // 估算各种数据结构的内存使用
      const dataSize = JSON.stringify(this.performanceData).length
      const stateSize = this.performanceData.resourceStats.length * 1000 // 估算

      estimatedMemory = (dataSize + stateSize) / 1024 / 1024 // 转换为MB
    } catch (error) {
      console.warn('⚠️ 内存估算失败:', error)
    }

    return Math.round(estimatedMemory * 100) / 100 // 保留两位小数
  },

  /**
   * 检查资源泄漏
   */
  checkResourceLeaks(currentStats) {
    const stats = this.performanceData.resourceStats
    if (stats.length < this.memoryConfig.leakDetectionWindow) {
      return
    }

    // 获取最近的样本
    const recentStats = stats.slice(-this.memoryConfig.leakDetectionWindow)

    // 检查资源数量是否持续增长
    const resourceTrend = this.analyzeResourceTrend(recentStats)
    const memoryTrend = this.analyzeMemoryTrend(recentStats)

    if (resourceTrend.isIncreasing || memoryTrend.isIncreasing) {
      const leak = {
        timestamp: Date.now(),
        type: resourceTrend.isIncreasing ? 'resource' : 'memory',
        severity: this.calculateLeakSeverity(resourceTrend, memoryTrend),
        details: {
          resourceTrend,
          memoryTrend,
          currentStats
        }
      }

      this.performanceData.memoryLeaks.push(leak)
      this.handleMemoryLeak(leak)
    }
  },

  /**
   * 分析资源趋势
   */
  analyzeResourceTrend(stats) {
    const totalResources = stats.map(s =>
      s.resources.totalTimers + s.resources.totalObservers + s.resources.totalListeners
    )

    const trend = this.calculateTrend(totalResources)
    return {
      isIncreasing: trend > 0.1, // 10%以上的增长趋势
      slope: trend,
      values: totalResources
    }
  },

  /**
   * 分析内存趋势
   */
  analyzeMemoryTrend(stats) {
    const memoryValues = stats.map(s => s.memoryUsage.estimated || 0)
    const trend = this.calculateTrend(memoryValues)

    return {
      isIncreasing: trend > 0.05, // 5%以上的增长趋势
      slope: trend,
      values: memoryValues
    }
  },

  /**
   * 计算趋势斜率
   */
  calculateTrend(values) {
    if (values.length < 2) return 0

    const n = values.length
    const sumX = (n * (n - 1)) / 2
    const sumY = values.reduce((a, b) => a + b, 0)
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0)
    const sumX2 = values.reduce((sum, _, x) => sum + x * x, 0)

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
    return slope || 0
  },

  /**
   * 计算泄漏严重程度
   */
  calculateLeakSeverity(resourceTrend, memoryTrend) {
    const resourceScore = Math.abs(resourceTrend.slope) * 10
    const memoryScore = Math.abs(memoryTrend.slope) * 20

    const totalScore = resourceScore + memoryScore

    if (totalScore > 50) return 'critical'
    if (totalScore > 20) return 'warning'
    return 'info'
  },

  /**
   * 处理内存泄漏
   */
  handleMemoryLeak(leak) {
    console.warn(`⚠️ 检测到${leak.type}泄漏 (${leak.severity}):`, leak.details)

    if (leak.severity === 'critical') {
      // 严重泄漏，尝试清理
      this.performEmergencyCleanup()
    }

    // 记录到错误日志
    this.performanceData.errorLogs.push({
      timestamp: Date.now(),
      type: 'memory_leak',
      level: leak.severity,
      message: `${leak.type}泄漏检测`,
      data: leak
    })
  },

  /**
   * 紧急清理
   */
  performEmergencyCleanup() {
    console.log('🚨 执行紧急内存清理')

    try {
      const ResourceManager = require('./resourceManager.js')

      // 清理非必要资源
      ResourceManager.cleanupNonEssentialResources()

      // 清理性能数据
      this.cleanupPerformanceData()

      console.log('✅ 紧急清理完成')
    } catch (error) {
      console.error('❌ 紧急清理失败:', error)
    }
  },

  /**
   * 清理性能数据
   */
  cleanupPerformanceData() {
    // 只保留最近的数据
    const keepCount = Math.floor(this.memoryConfig.maxSamples / 2)

    if (this.performanceData.resourceStats.length > keepCount) {
      this.performanceData.resourceStats = this.performanceData.resourceStats.slice(-keepCount)
    }

    if (this.performanceData.memoryUsage.length > keepCount) {
      this.performanceData.memoryUsage = this.performanceData.memoryUsage.slice(-keepCount)
    }

    if (this.performanceData.errorLogs.length > keepCount) {
      this.performanceData.errorLogs = this.performanceData.errorLogs.slice(-keepCount)
    }
  },

  /**
   * 开始内存泄漏检测
   */
  startMemoryLeakDetection() {
    console.log('🔍 启动内存泄漏检测')

    // 定期检测
    this.memoryTimer = setInterval(() => {
      this.collectResourceStats()
    }, this.memoryConfig.checkInterval)
  },

  /**
   * 停止内存泄漏检测
   */
  stopMemoryLeakDetection() {
    if (this.memoryTimer) {
      clearInterval(this.memoryTimer)
      this.memoryTimer = null
      console.log('⏹️ 内存泄漏检测已停止')
    }
  },

  /**
   * 获取内存泄漏报告
   */
  getMemoryLeakReport() {
    return {
      totalLeaks: this.performanceData.memoryLeaks.length,
      criticalLeaks: this.performanceData.memoryLeaks.filter(l => l.severity === 'critical').length,
      warningLeaks: this.performanceData.memoryLeaks.filter(l => l.severity === 'warning').length,
      recentLeaks: this.performanceData.memoryLeaks.slice(-10),
      currentResourceStats: this.performanceData.resourceStats.slice(-1)[0],
      recommendations: this.generateRecommendations()
    }
  },

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = []
    const recentStats = this.performanceData.resourceStats.slice(-5)

    if (recentStats.length === 0) return recommendations

    const avgResources = recentStats.reduce((sum, s) =>
      sum + s.resources.totalTimers + s.resources.totalObservers + s.resources.totalListeners, 0
    ) / recentStats.length

    if (avgResources > 50) {
      recommendations.push('考虑减少同时活跃的定时器和监听器数量')
    }

    const avgMemory = recentStats.reduce((sum, s) => sum + (s.memoryUsage.estimated || 0), 0) / recentStats.length

    if (avgMemory > this.memoryConfig.warningThreshold) {
      recommendations.push('内存使用量较高，建议清理不必要的数据缓存')
    }

    if (this.performanceData.memoryLeaks.length > 0) {
      recommendations.push('检测到内存泄漏，请检查页面卸载时的资源清理')
    }

    return recommendations
  }
}

module.exports = {
  PerformanceMonitor
}
