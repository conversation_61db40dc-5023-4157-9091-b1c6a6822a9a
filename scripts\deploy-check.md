# 云函数部署检查脚本

## 📋 部署前检查清单

### 1. 环境准备
- [ ] 微信开发者工具已安装最新版本
- [ ] 小程序项目已导入到开发者工具
- [ ] 小程序AppID已正确配置
- [ ] 云开发服务已开通

### 2. 云环境配置
- [ ] 云环境已创建
- [ ] 环境ID已记录
- [ ] app.js中的环境ID已更新

### 3. 云函数部署步骤

#### 部署login云函数
1. 在微信开发者工具中找到 `cloudfunctions/login` 文件夹
2. 右键点击该文件夹
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成（通常需要1-3分钟）
5. 查看部署日志确认成功

#### 部署dataSync云函数
1. 在微信开发者工具中找到 `cloudfunctions/dataSync` 文件夹
2. 右键点击该文件夹
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成
5. 查看部署日志确认成功

### 4. 数据库配置

#### 创建必要的集合
在云开发控制台的数据库页面创建以下集合：

1. **users** - 用户信息表
   ```json
   {
     "openid": "string",
     "profile": {
       "nickname": "string",
       "avatar": "string"
     },
     "createTime": "date",
     "lastLoginTime": "date"
   }
   ```

2. **user_likes** - 用户点赞记录
   ```json
   {
     "userId": "string",
     "emojiId": "string",
     "createTime": "date"
   }
   ```

3. **user_collections** - 用户收藏记录
   ```json
   {
     "userId": "string",
     "emojiId": "string",
     "createTime": "date"
   }
   ```

4. **user_downloads** - 用户下载记录
   ```json
   {
     "userId": "string",
     "emojiId": "string",
     "downloadTime": "date",
     "createTime": "date"
   }
   ```

#### 配置数据库权限
在数据库权限设置中配置以下规则：

```javascript
// users集合权限
{
  "read": "doc.openid == auth.openid",
  "write": "doc.openid == auth.openid"
}

// user_likes集合权限
{
  "read": "doc.userId == auth.openid",
  "write": "doc.userId == auth.openid"
}

// user_collections集合权限
{
  "read": "doc.userId == auth.openid",
  "write": "doc.userId == auth.openid"
}

// user_downloads集合权限
{
  "read": "doc.userId == auth.openid",
  "write": "doc.userId == auth.openid"
}
```

### 5. 部署验证

#### 在微信开发者工具中验证
1. 打开个人中心页面
2. 点击"环境诊断"按钮
3. 确认所有检查项目通过
4. 点击"测试云函数"按钮
5. 确认云函数调用成功

#### 在云开发控制台验证
1. 访问云开发控制台
2. 在云函数页面确认login和dataSync函数显示为"已部署"
3. 在数据库页面确认所有集合已创建
4. 查看云函数调用日志确认无错误

### 6. 功能测试

#### 基础登录测试
1. 点击"测试登录"按钮
2. 完成微信授权流程
3. 确认登录成功并显示用户信息

#### 数据同步测试
1. 登录后进行点赞操作
2. 在云开发控制台查看user_likes集合
3. 确认数据已正确同步

### 7. 常见部署问题

#### 云函数部署失败
- **原因**: 网络连接问题或依赖安装失败
- **解决**: 重试部署，确保网络连接稳定

#### 数据库权限错误
- **原因**: 权限规则配置错误
- **解决**: 检查权限规则语法，确保auth.openid正确使用

#### 环境ID错误
- **原因**: app.js中的环境ID与实际环境不匹配
- **解决**: 在云开发控制台查看正确的环境ID并更新

### 8. 部署完成检查

完成部署后，请确认以下项目：

- [ ] 云函数部署状态为"已部署"
- [ ] 数据库集合已创建
- [ ] 数据库权限已配置
- [ ] 环境诊断全部通过
- [ ] 登录功能测试成功
- [ ] 数据同步功能正常

## 🚀 快速部署命令

如果你熟悉命令行操作，可以使用以下步骤快速部署：

1. **打开微信开发者工具**
2. **批量部署云函数**：
   - 选中所有云函数文件夹
   - 右键选择"上传并部署：云端安装依赖"
3. **等待部署完成**
4. **运行环境诊断验证**

## 📞 获取帮助

如果部署过程中遇到问题：

1. 查看 `docs/login-troubleshooting.md` 故障排除指南
2. 检查微信开发者工具控制台的错误信息
3. 查看云开发控制台的部署日志
4. 参考微信云开发官方文档

---

*部署完成后，登录系统即可正常使用。建议在生产环境部署前先在开发环境充分测试。*
