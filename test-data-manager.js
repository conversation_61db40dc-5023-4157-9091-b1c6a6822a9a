/**
 * 数据管理器测试脚本
 * 用于验证数据获取、缓存、版本管理等功能
 */

// 模拟微信小程序环境
global.wx = {
  cloud: {
    callFunction: async ({ name, data }) => {
      console.log(`模拟云函数调用: ${name}`, data)
      
      // 模拟不同的云函数响应
      switch (name) {
        case 'dataAPI':
          return mockDataAPIResponse(data)
        case 'login':
          return mockLoginResponse(data)
        default:
          return { result: { success: false, message: '未知云函数' } }
      }
    }
  },
  getStorageSync: (key) => {
    console.log(`模拟获取存储: ${key}`)
    return null // 模拟空存储
  },
  setStorageSync: (key, value) => {
    console.log(`模拟设置存储: ${key} =`, value)
  },
  removeStorageSync: (key) => {
    console.log(`模拟删除存储: ${key}`)
  }
}

// 模拟 dataAPI 云函数响应
function mockDataAPIResponse(data) {
  const { action } = data
  
  switch (action) {
    case 'getEmojis':
      return {
        result: {
          success: true,
          data: [
            {
              _id: 'test1',
              title: '测试表情包1',
              imageUrl: 'https://example.com/emoji1.jpg',
              category: 'funny',
              likes: 100,
              collections: 50,
              tags: ['搞笑', '测试']
            },
            {
              _id: 'test2',
              title: '测试表情包2',
              imageUrl: 'https://example.com/emoji2.jpg',
              category: 'cute',
              likes: 200,
              collections: 80,
              tags: ['可爱', '测试']
            }
          ]
        }
      }
      
    case 'getCategoryStats':
      return {
        result: {
          success: true,
          data: [
            {
              id: 'funny',
              name: '搞笑幽默',
              icon: '😂',
              count: 25,
              gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            },
            {
              id: 'cute',
              name: '可爱萌宠',
              icon: '🐱',
              count: 30,
              gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
            }
          ]
        }
      }
      
    case 'searchEmojis':
      return {
        result: {
          success: true,
          data: [
            {
              _id: 'search1',
              title: '搜索结果1',
              imageUrl: 'https://example.com/search1.jpg',
              category: 'funny',
              likes: 150,
              collections: 60,
              tags: ['搜索', '测试']
            }
          ]
        }
      }
      
    case 'getDataVersion':
      return {
        result: {
          success: true,
          version: '2024.0717.1200',
          dataType: data.dataType,
          timestamp: new Date()
        }
      }
      
    default:
      return {
        result: {
          success: false,
          message: `未知操作: ${action}`
        }
      }
  }
}

// 模拟登录云函数响应
function mockLoginResponse(data) {
  if (data.action === 'validatePermission') {
    return {
      result: {
        success: true,
        isValid: true,
        user: {
          _id: 'testuser',
          openid: 'test_openid',
          auth: { role: 'user', status: 'active' }
        },
        role: 'user',
        message: '权限验证通过'
      }
    }
  }
  
  return {
    result: {
      success: false,
      message: '未知登录操作'
    }
  }
}

// 测试数据管理器
async function testDataManager() {
  console.log('🧪 开始测试数据管理器...\n')
  
  try {
    // 导入数据管理器
    const { DataManager } = require('./utils/newDataManager.js')
    
    // 测试1: 获取表情包数据
    console.log('📋 测试1: 获取表情包数据')
    const emojis = await DataManager.getAllEmojiData('all', 1, 10)
    console.log('✅ 获取表情包数据成功:', emojis.length, '个')
    console.log('📦 表情包数据:', emojis.map(e => e.title).join(', '))
    console.log('')
    
    // 测试2: 获取分类数据
    console.log('📋 测试2: 获取分类数据')
    const categories = await DataManager.getCategoriesWithStats()
    console.log('✅ 获取分类数据成功:', categories.length, '个')
    console.log('📦 分类数据:', categories.map(c => c.name).join(', '))
    console.log('')
    
    // 测试3: 搜索功能
    console.log('📋 测试3: 搜索功能')
    const searchResults = await DataManager.searchEmojis('测试')
    console.log('✅ 搜索功能成功:', searchResults.length, '个结果')
    console.log('📦 搜索结果:', searchResults.map(e => e.title).join(', '))
    console.log('')
    
    // 测试4: 缓存功能
    console.log('📋 测试4: 缓存功能')
    const cacheStatus = DataManager.getCacheStatus()
    console.log('✅ 缓存状态:', cacheStatus)
    console.log('')
    
    // 测试5: 版本管理
    console.log('📋 测试5: 版本管理')
    const versionCheck = await DataManager.checkDataVersion('emojis')
    console.log('✅ 版本检查结果:', versionCheck ? '需要更新' : '无需更新')
    console.log('')
    
    console.log('🎉 数据管理器测试完成！功能正常')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

// 测试请求优化器
async function testRequestOptimizer() {
  console.log('🧪 开始测试请求优化器...\n')

  try {
    // 导入请求优化器
    const { RequestOptimizer } = require('./utils/requestOptimizer.js')

    // 初始化
    RequestOptimizer.init()

    // 测试1: 基本请求
    console.log('📋 测试1: 基本请求')
    const result1 = await RequestOptimizer.callFunction({
      name: 'dataAPI',
      data: { action: 'getEmojis', data: { category: 'funny', page: 1, limit: 5 } }
    })
    console.log('✅ 基本请求成功:', result1?.success)
    console.log('')

    // 测试2: 缓存功能
    console.log('📋 测试2: 缓存功能')
    const result2 = await RequestOptimizer.callFunction({
      name: 'dataAPI',
      data: { action: 'getEmojis', data: { category: 'funny', page: 1, limit: 5 } }
    }, { useCache: true })
    console.log('✅ 缓存请求成功:', result2?.success)
    console.log('')

    // 测试3: 请求统计
    console.log('📋 测试3: 请求统计')
    const stats = RequestOptimizer.getRequestStats()
    console.log('✅ 请求统计:', stats)
    console.log('')

    console.log('🎉 请求优化器测试完成！')

  } catch (error) {
    console.error('❌ 请求优化器测试失败:', error)
  }
}

// 测试分页管理器
async function testPaginationManager() {
  console.log('🧪 开始测试分页管理器...\n')

  try {
    // 导入分页管理器
    const { PaginationManager } = require('./utils/paginationManager.js')

    // 测试1: 创建分页实例
    console.log('📋 测试1: 创建分页实例')
    const pagination = PaginationManager.createInstance({
      pageSize: 10,
      dataSource: 'emojis',
      category: 'funny',
      onDataLoad: (result) => {
        console.log('📄 分页数据加载:', result.type, result.data.length, '个')
      },
      onError: (error) => {
        console.error('❌ 分页错误:', error.message)
      }
    })
    console.log('✅ 分页实例创建成功')
    console.log('')

    // 测试2: 初始化加载
    console.log('📋 测试2: 初始化加载')
    await pagination.init()
    console.log('✅ 初始化加载完成')
    console.log('')

    // 测试3: 加载更多
    console.log('📋 测试3: 加载更多')
    await pagination.loadMore()
    console.log('✅ 加载更多完成')
    console.log('')

    // 测试4: 获取状态
    console.log('📋 测试4: 获取状态')
    const state = pagination.getState()
    console.log('✅ 分页状态:', state)
    console.log('')

    console.log('🎉 分页管理器测试完成！')

  } catch (error) {
    console.error('❌ 分页管理器测试失败:', error)
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始运行完整测试套件...\n')

  await testDataManager()
  console.log('\n' + '='.repeat(50) + '\n')
  await testRequestOptimizer()
  console.log('\n' + '='.repeat(50) + '\n')
  await testPaginationManager()

  console.log('\n✨ 所有测试完成！')
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = {
  testDataManager,
  testRequestOptimizer,
  testPaginationManager,
  runTests
}
