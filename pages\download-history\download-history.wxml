<!--pages/download-history/download-history.wxml-->
<view class="container">

  <!-- 复用主页面的表情包列表样式 -->
  <view class="emoji-list" wx:if="{{downloadedEmojis.length > 0}}">
    <view
      class="emoji-item"
      wx:for="{{downloadedEmojis}}"
      wx:key="id"
      bindtap="onEmojiTap"
      data-emoji="{{item}}"
    >
      <!-- 表情包图片 -->
      <view class="emoji-image-container">
        <image
          class="emoji-image"
          src="{{item.imageUrl}}"
          mode="aspectFill"
        />
      </view>

      <!-- 表情包信息 -->
      <view class="emoji-info">
        <text class="emoji-title">{{item.title}}</text>
        <text class="emoji-category">{{item.category}}</text>

        <!-- 标签区域 -->
        <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view
            class="tag-item"
            wx:for="{{item.tags}}"
            wx:for-item="tag"
            wx:key="*this"
          >
            {{tag}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <text class="empty-icon">📥</text>
    <text class="empty-title">还没有下载记录</text>
    <text class="empty-desc">下载表情包后会在这里显示记录</text>
    <button class="explore-btn" bindtap="onGoExplore">去探索</button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>
