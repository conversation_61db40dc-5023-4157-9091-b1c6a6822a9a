<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速数据检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .data-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .data-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            margin-left: 10px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 快速数据检查</h1>
        
        <div class="data-section">
            <h3>云数据库连接状态</h3>
            <span id="connection-status" class="status info">检查中...</span>
            <button onclick="checkConnection()">检查连接</button>
        </div>

        <div class="data-section">
            <h3>📂 分类数据 (categories)</h3>
            <span id="categories-status" class="status info">未检查</span>
            <button onclick="loadCategories()">加载分类</button>
            <div id="categories-data"></div>
        </div>

        <div class="data-section">
            <h3>😊 表情包数据 (emojis)</h3>
            <span id="emojis-status" class="status info">未检查</span>
            <button onclick="loadEmojis()">加载表情包</button>
            <div id="emojis-data"></div>
        </div>

        <div class="data-section">
            <h3>🎯 横幅数据 (banners)</h3>
            <span id="banners-status" class="status info">未检查</span>
            <button onclick="loadBanners()">加载横幅</button>
            <div id="banners-data"></div>
        </div>

        <div class="data-section">
            <h3>🔄 一键检查所有数据</h3>
            <button onclick="checkAllData()">检查所有数据</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js"></script>
    
    <script>
        // 云开发配置
        const CloudConfig = {
            env: 'cloud1-5g6pvnpl88dc0142',
            initialized: false
        };

        let tcbApp = null;

        function updateStatus(elementId, status, className) {
            const element = document.getElementById(elementId);
            element.textContent = status;
            element.className = `status ${className}`;
        }

        function displayData(containerId, data, title) {
            const container = document.getElementById(containerId);
            if (!data || data.length === 0) {
                container.innerHTML = `<p style="color: #666;">暂无${title}数据</p>`;
                return;
            }

            let html = `<p><strong>共 ${data.length} 条${title}数据：</strong></p>`;
            
            // 显示前5条数据的详细信息
            const displayData = data.slice(0, 5);
            displayData.forEach((item, index) => {
                html += `
                    <div class="data-item">
                        <strong>${index + 1}. ${item.name || item.title || '未命名'}</strong>
                        <pre>${JSON.stringify(item, null, 2)}</pre>
                    </div>
                `;
            });

            if (data.length > 5) {
                html += `<p style="color: #666;">... 还有 ${data.length - 5} 条数据</p>`;
            }

            container.innerHTML = html;
        }

        async function checkConnection() {
            updateStatus('connection-status', '🔄 连接中...', 'info');
            
            try {
                if (!window.tcb) {
                    throw new Error('云开发SDK未加载');
                }

                tcbApp = tcb.init({
                    env: CloudConfig.env
                });

                const auth = tcbApp.auth();
                await auth.signInAnonymously();

                // 测试数据库连接
                const db = tcbApp.database();
                await db.collection('categories').limit(1).get();

                CloudConfig.initialized = true;
                updateStatus('connection-status', '✅ 连接成功', 'success');
                
            } catch (error) {
                updateStatus('connection-status', `❌ 连接失败: ${error.message}`, 'error');
                console.error('连接失败:', error);
            }
        }

        async function loadCategories() {
            updateStatus('categories-status', '🔄 加载中...', 'info');
            
            try {
                if (!CloudConfig.initialized) {
                    await checkConnection();
                }

                const db = tcbApp.database();
                const result = await db.collection('categories').get();
                
                updateStatus('categories-status', `✅ 加载成功 (${result.data.length} 条)`, 'success');
                displayData('categories-data', result.data, '分类');
                
            } catch (error) {
                updateStatus('categories-status', `❌ 加载失败: ${error.message}`, 'error');
                console.error('加载分类失败:', error);
            }
        }

        async function loadEmojis() {
            updateStatus('emojis-status', '🔄 加载中...', 'info');
            
            try {
                if (!CloudConfig.initialized) {
                    await checkConnection();
                }

                const db = tcbApp.database();
                const result = await db.collection('emojis').get();
                
                updateStatus('emojis-status', `✅ 加载成功 (${result.data.length} 条)`, 'success');
                displayData('emojis-data', result.data, '表情包');
                
            } catch (error) {
                updateStatus('emojis-status', `❌ 加载失败: ${error.message}`, 'error');
                console.error('加载表情包失败:', error);
            }
        }

        async function loadBanners() {
            updateStatus('banners-status', '🔄 加载中...', 'info');
            
            try {
                if (!CloudConfig.initialized) {
                    await checkConnection();
                }

                const db = tcbApp.database();
                const result = await db.collection('banners').get();
                
                updateStatus('banners-status', `✅ 加载成功 (${result.data.length} 条)`, 'success');
                displayData('banners-data', result.data, '横幅');
                
            } catch (error) {
                updateStatus('banners-status', `❌ 加载失败: ${error.message}`, 'error');
                console.error('加载横幅失败:', error);
            }
        }

        async function checkAllData() {
            console.log('🔍 开始检查所有数据...');
            
            await checkConnection();
            
            if (CloudConfig.initialized) {
                await Promise.all([
                    loadCategories(),
                    loadEmojis(),
                    loadBanners()
                ]);
                console.log('✅ 所有数据检查完成');
            }
        }

        function clearResults() {
            document.getElementById('categories-data').innerHTML = '';
            document.getElementById('emojis-data').innerHTML = '';
            document.getElementById('banners-data').innerHTML = '';
            
            updateStatus('categories-status', '未检查', 'info');
            updateStatus('emojis-status', '未检查', 'info');
            updateStatus('banners-status', '未检查', 'info');
        }

        // 页面加载时自动检查连接
        window.addEventListener('load', () => {
            setTimeout(checkConnection, 1000);
        });
    </script>
</body>
</html>
