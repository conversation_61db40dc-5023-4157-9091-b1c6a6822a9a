/**app.wxss**/
/* 全局样式优化 */

/* 页面基础样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #333333;
}

/* TabBar 炫酷动效样式 */
.wx-tabbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
  box-shadow: 0 -2px 25px rgba(255, 140, 0, 0.15) !important;
  position: relative !important;
}

/* TabBar 毛玻璃效果增强 */
.wx-tabbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 248, 240, 0.8) 100%);
  pointer-events: none;
  z-index: -1;
}

/* TabBar 项目基础样式 */
.wx-tabbar-item {
  font-size: 12px !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  position: relative !important;
}

/* 图标动效 - 弹跳效果 */
.wx-tabbar-item .wx-tabbar-icon {
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  transform-origin: center bottom !important;
}

/* 选中状态 - 图标弹跳 + 放大 */
.wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-icon {
  transform: translateY(-3px) scale(1.15) !important;
  animation: iconBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

/* 弹跳动画关键帧 */
@keyframes iconBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  30% {
    transform: translateY(-5px) scale(1.2);
  }
  50% {
    transform: translateY(-2px) scale(1.1);
  }
  70% {
    transform: translateY(-4px) scale(1.18);
  }
  100% {
    transform: translateY(-3px) scale(1.15);
  }
}

/* 选中状态背景光晕效果 */
.wx-tabbar-item.wx-tabbar-item_selected::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 45px;
  height: 45px;
  background: radial-gradient(circle, rgba(255, 140, 0, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  animation: glowPulse 2s ease-in-out infinite alternate;
}

/* 光晕脉动动画 */
@keyframes glowPulse {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 文字样式增强 */
.wx-tabbar-item .wx-tabbar-label {
  transition: all 0.3s ease !important;
  transform-origin: center !important;
}

/* 选中状态文字效果 */
.wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-label {
  color: #FF8C00 !important;
  font-weight: 600 !important;
  transform: scale(1.05) !important;
  text-shadow: 0 0 8px rgba(255, 140, 0, 0.3) !important;
}

/* 点击反馈动效 */
.wx-tabbar-item:active {
  transform: scale(0.95) !important;
  transition: transform 0.15s ease !important;
}

.wx-tabbar-item:active .wx-tabbar-icon {
  transform: translateY(-1px) scale(1.05) !important;
  transition: transform 0.15s ease !important;
}

/* 悬浮效果（如果支持） */
.wx-tabbar-item:hover .wx-tabbar-icon {
  transform: translateY(-1px) scale(1.08) !important;
}

/* 波纹扩散效果 */
.wx-tabbar-item.wx-tabbar-item_selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 140, 0, 0.1);
  transform: translate(-50%, -50%);
  animation: rippleEffect 0.8s ease-out;
  pointer-events: none;
}

/* 波纹动画 */
@keyframes rippleEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 60px;
    height: 60px;
    opacity: 0;
  }
}

/* 图标旋转效果（可选） */
.wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-icon {
  animation: iconBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55), 
             iconRotate 0.8s ease-out 0.2s;
}

@keyframes iconRotate {
  0% {
    transform: translateY(-3px) scale(1.15) rotate(0deg);
  }
  50% {
    transform: translateY(-3px) scale(1.15) rotate(5deg);
  }
  100% {
    transform: translateY(-3px) scale(1.15) rotate(0deg);
  }
}

/* 底部安全区域适配 */
.tab-bar-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 响应式动效 */
@media (max-width: 375px) {
  .wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-icon {
    transform: translateY(-2px) scale(1.1) !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .wx-tabbar-item,
  .wx-tabbar-item .wx-tabbar-icon,
  .wx-tabbar-item .wx-tabbar-label {
    transition: none !important;
    animation: none !important;
  }
}

.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 通用按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-radius: 50rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.btn-warning {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: white;
  border-radius: 50rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.btn-info {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: white;
  border-radius: 50rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 文字样式 */
.text-primary {
  color: #8B5CF6;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}