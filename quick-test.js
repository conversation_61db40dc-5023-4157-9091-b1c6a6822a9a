// 快速测试脚本 - 在小程序控制台中运行
// 复制以下代码到微信开发者工具的控制台中执行

console.log('🧪 开始快速诊断表情包显示问题');
console.log('='.repeat(60));

// 获取当前页面实例
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];

if (!currentPage) {
  console.error('❌ 无法获取当前页面实例');
} else {
  console.log('✅ 获取到当前页面实例');
  
  // 检查页面数据
  const data = currentPage.data;
  console.log('\n📊 页面数据状态:');
  console.log('- 页面路径:', currentPage.route);
  console.log('- emojiList:', data.emojiList);
  console.log('- emojiList.length:', data.emojiList?.length || 0);
  console.log('- searchResults:', data.searchResults);
  console.log('- searchResults.length:', data.searchResults?.length || 0);
  console.log('- hotCategories.length:', data.hotCategories?.length || 0);
  console.log('- bannerList.length:', data.bannerList?.length || 0);
  
  // 检查显示条件
  const showCondition = data.searchResults?.length === 0 && data.emojiList?.length > 0;
  console.log('\n🔍 显示条件检查:');
  console.log('- searchResults.length === 0:', data.searchResults?.length === 0);
  console.log('- emojiList.length > 0:', data.emojiList?.length > 0);
  console.log('- 最终显示条件:', showCondition);
  
  if (showCondition) {
    console.log('✅ 显示条件满足，表情包列表应该显示');
  } else {
    console.log('❌ 显示条件不满足，表情包列表不会显示');
    
    if (data.emojiList?.length === 0) {
      console.log('💡 建议: emojiList 为空，需要加载数据');
    }
    
    if (data.searchResults?.length > 0) {
      console.log('💡 建议: searchResults 不为空，需要清空搜索状态');
    }
  }
  
  // 如果是首页，尝试手动设置测试数据
  if (currentPage.route === 'pages/index/index') {
    console.log('\n🔧 检测到首页，尝试设置测试数据...');
    
    const testData = [
      {
        "_id": "test1",
        "title": "测试表情1",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2NjAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIg8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test2",
        "title": "测试表情2",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZkNzAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIo8L3RleHQ+Cjwvc3ZnPgo="
      },
      {
        "_id": "test3",
        "title": "测试表情3",
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBiY2ZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmI08L3RleHQ+Cjwvc3ZnPgo="
      }
    ];
    
    try {
      currentPage.setData({
        emojiList: testData,
        searchResults: []
      });
      
      console.log('✅ 测试数据设置成功');
      
      // 验证设置结果
      setTimeout(() => {
        const newData = currentPage.data;
        console.log('\n🔍 验证设置结果:');
        console.log('- emojiList.length:', newData.emojiList?.length || 0);
        console.log('- searchResults.length:', newData.searchResults?.length || 0);
        console.log('- 显示条件:', newData.searchResults?.length === 0 && newData.emojiList?.length > 0);
        
        if (newData.searchResults?.length === 0 && newData.emojiList?.length > 0) {
          console.log('✅ 现在表情包列表应该显示了！');
        } else {
          console.log('❌ 表情包列表仍然不会显示');
        }
      }, 100);
      
    } catch (error) {
      console.error('❌ 设置测试数据失败:', error);
    }
  }
}

// 测试云函数调用
console.log('\n☁️ 测试云函数调用...');
wx.cloud.callFunction({
  name: 'dataAPI',
  data: {
    action: 'getEmojis',
    data: {
      category: 'all',
      page: 1,
      limit: 3
    }
  }
}).then(result => {
  console.log('✅ 云函数调用成功:', result.result);
  
  if (result.result && result.result.success && result.result.data) {
    console.log(`📦 获取到 ${result.result.data.length} 条数据`);
    
    // 如果是首页，尝试用云函数数据更新页面
    if (currentPage && currentPage.route === 'pages/index/index') {
      console.log('🔄 尝试用云函数数据更新首页...');
      
      currentPage.setData({
        emojiList: result.result.data,
        searchResults: []
      });
      
      console.log('✅ 云函数数据已设置到首页');
    }
  }
}).catch(error => {
  console.error('❌ 云函数调用失败:', error);
});

console.log('\n📋 诊断完成！');
console.log('💡 如果表情包仍然不显示，请检查:');
console.log('1. WXML 模板中的显示条件');
console.log('2. CSS 样式是否隐藏了元素');
console.log('3. 页面生命周期是否正确调用了数据加载方法');
console.log('4. 是否有其他代码重置了 emojiList');

// 提供手动测试函数
window.manualTest = {
  setTestData: () => {
    if (currentPage) {
      const testData = [
        { "_id": "manual1", "title": "手动测试1", "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmY2NjAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPvCfmIg8L3RleHQ+Cjwvc3ZnPgo=" }
      ];
      currentPage.setData({ emojiList: testData, searchResults: [] });
      console.log('✅ 手动设置测试数据完成');
    }
  },
  
  clearData: () => {
    if (currentPage) {
      currentPage.setData({ emojiList: [], searchResults: [] });
      console.log('✅ 清空数据完成');
    }
  },
  
  checkData: () => {
    if (currentPage) {
      const data = currentPage.data;
      console.log('📊 当前数据:', {
        emojiList: data.emojiList?.length || 0,
        searchResults: data.searchResults?.length || 0,
        showCondition: data.searchResults?.length === 0 && data.emojiList?.length > 0
      });
    }
  }
};

console.log('\n🛠️ 手动测试函数已注册:');
console.log('- manualTest.setTestData() - 设置测试数据');
console.log('- manualTest.clearData() - 清空数据');
console.log('- manualTest.checkData() - 检查当前数据状态');
