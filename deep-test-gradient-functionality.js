// 深度测试渐变色功能
const { chromium } = require('playwright');

async function deepTestGradientFunctionality() {
    console.log('🎨 深度测试渐变色功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('渐变') || text.includes('gradient') || text.includes('预览') || text.includes('applyGradientPreset')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 点击添加分类按钮');
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        if (await addCategoryBtn.isVisible()) {
            await addCategoryBtn.click();
            console.log('✅ 已点击添加分类按钮');
            await page.waitForTimeout(3000);
        }
        
        console.log('\n📍 检查渐变相关元素');
        
        // 检查渐变相关元素
        const gradientElements = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };
            
            const gradientSelect = modal.querySelector('#category-gradient-preset');
            const gradientInput = modal.querySelector('#category-gradient');
            const gradientPreview = modal.querySelector('#gradient-preview');
            
            return {
                modalExists: true,
                gradientSelectExists: !!gradientSelect,
                gradientInputExists: !!gradientInput,
                gradientPreviewExists: !!gradientPreview,
                gradientSelectValue: gradientSelect ? gradientSelect.value : null,
                gradientInputValue: gradientInput ? gradientInput.value : null,
                gradientPreviewStyle: gradientPreview ? gradientPreview.style.background : null,
                gradientPreviewComputedStyle: gradientPreview ? window.getComputedStyle(gradientPreview).background : null
            };
        });
        
        console.log('📊 渐变元素检查:');
        console.log('渐变选择框存在:', gradientElements.gradientSelectExists);
        console.log('渐变输入框存在:', gradientElements.gradientInputExists);
        console.log('渐变预览存在:', gradientElements.gradientPreviewExists);
        console.log('渐变选择框值:', gradientElements.gradientSelectValue);
        console.log('渐变输入框值:', gradientElements.gradientInputValue);
        console.log('渐变预览样式:', gradientElements.gradientPreviewStyle);
        console.log('渐变预览计算样式:', gradientElements.gradientPreviewComputedStyle);
        
        console.log('\n📍 测试渐变选择功能');
        
        // 选择渐变
        const gradientSelect = await page.locator('#category-gradient-preset').first();
        if (await gradientSelect.isVisible()) {
            console.log('✅ 渐变选择框可见');
            
            // 选择蓝紫渐变
            await gradientSelect.selectOption('linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
            console.log('✅ 已选择蓝紫渐变');
            
            // 等待渐变预览更新
            await page.waitForTimeout(2000);
            
            // 检查渐变预览更新
            const afterSelectGradient = await page.evaluate(() => {
                const gradientSelect = document.querySelector('#category-gradient-preset');
                const gradientInput = document.querySelector('#category-gradient');
                const gradientPreview = document.querySelector('#gradient-preview');
                
                return {
                    selectValue: gradientSelect ? gradientSelect.value : null,
                    inputValue: gradientInput ? gradientInput.value : null,
                    previewStyle: gradientPreview ? gradientPreview.style.background : null,
                    previewComputedStyle: gradientPreview ? window.getComputedStyle(gradientPreview).background : null,
                    previewInnerHTML: gradientPreview ? gradientPreview.innerHTML : null
                };
            });
            
            console.log('📊 选择渐变后的状态:');
            console.log('选择框值:', afterSelectGradient.selectValue);
            console.log('输入框值:', afterSelectGradient.inputValue);
            console.log('预览样式:', afterSelectGradient.previewStyle);
            console.log('预览计算样式:', afterSelectGradient.previewComputedStyle);
            console.log('预览内容:', afterSelectGradient.previewInnerHTML);
            
            // 检查applyGradientPreset函数是否存在
            const functionCheck = await page.evaluate(() => {
                return {
                    applyGradientPresetExists: typeof applyGradientPreset === 'function',
                    updateGradientPreviewExists: typeof updateGradientPreview === 'function'
                };
            });
            
            console.log('📊 渐变函数检查:');
            console.log('applyGradientPreset函数存在:', functionCheck.applyGradientPresetExists);
            console.log('updateGradientPreview函数存在:', functionCheck.updateGradientPreviewExists);
            
            // 手动调用渐变预览函数
            if (functionCheck.applyGradientPresetExists) {
                console.log('\n📍 手动调用applyGradientPreset函数');
                const manualCall = await page.evaluate(() => {
                    try {
                        applyGradientPreset();
                        
                        const gradientPreview = document.querySelector('#gradient-preview');
                        return {
                            success: true,
                            previewStyle: gradientPreview ? gradientPreview.style.background : null
                        };
                    } catch (error) {
                        return {
                            success: false,
                            error: error.message
                        };
                    }
                });
                
                console.log('手动调用结果:', manualCall);
            }
            
            // 手动设置渐变预览
            console.log('\n📍 手动设置渐变预览');
            const manualSetGradient = await page.evaluate(() => {
                const gradientPreview = document.querySelector('#gradient-preview');
                const gradientInput = document.querySelector('#category-gradient');
                
                if (gradientPreview && gradientInput) {
                    const gradientValue = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    gradientInput.value = gradientValue;
                    gradientPreview.style.background = gradientValue;
                    
                    return {
                        success: true,
                        inputValue: gradientInput.value,
                        previewStyle: gradientPreview.style.background,
                        previewComputedStyle: window.getComputedStyle(gradientPreview).background
                    };
                } else {
                    return {
                        success: false,
                        error: '渐变元素不存在'
                    };
                }
            });
            
            console.log('手动设置渐变结果:', manualSetGradient);
            
        } else {
            console.log('❌ 渐变选择框不可见');
        }
        
        console.log('\n📍 检查渐变相关JavaScript函数');
        
        // 检查渐变相关的JavaScript函数定义
        const jsCheck = await page.evaluate(() => {
            // 查找所有包含gradient的函数
            const scripts = Array.from(document.querySelectorAll('script')).map(script => script.textContent);
            const allScript = scripts.join('\n');
            
            const hasApplyGradientPreset = allScript.includes('function applyGradientPreset') || allScript.includes('applyGradientPreset =');
            const hasUpdateGradientPreview = allScript.includes('function updateGradientPreview') || allScript.includes('updateGradientPreview =');
            
            return {
                hasApplyGradientPreset,
                hasUpdateGradientPreview,
                scriptCount: scripts.length,
                totalScriptLength: allScript.length
            };
        });
        
        console.log('📊 JavaScript函数检查:');
        console.log('包含applyGradientPreset:', jsCheck.hasApplyGradientPreset);
        console.log('包含updateGradientPreview:', jsCheck.hasUpdateGradientPreview);
        console.log('脚本数量:', jsCheck.scriptCount);
        console.log('脚本总长度:', jsCheck.totalScriptLength);
        
        // 截图
        await page.screenshot({ path: 'deep-test-gradient-functionality.png', fullPage: true });
        console.log('\n📸 渐变功能测试截图已保存: deep-test-gradient-functionality.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开20秒供查看...');
        await page.waitForTimeout(20000);
        
        return {
            success: true,
            gradientElementsExist: gradientElements.gradientSelectExists && gradientElements.gradientInputExists && gradientElements.gradientPreviewExists,
            gradientFunctionsExist: jsCheck.hasApplyGradientPreset && jsCheck.hasUpdateGradientPreview
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'gradient-test-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
deepTestGradientFunctionality().then(result => {
    console.log('\n🎯 渐变功能测试结果:', result);
}).catch(console.error);
