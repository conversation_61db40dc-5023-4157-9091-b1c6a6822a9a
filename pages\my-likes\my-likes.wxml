<!--pages/my-likes/my-likes.wxml-->
<view class="container">

  <view class="emoji-grid" wx:if="{{likedEmojis.length > 0}}">
    <view
      class="emoji-item card"
      wx:for="{{likedEmojis}}"
      wx:key="id"
      bindtap="onEmojiTap"
      data-emoji="{{item}}"
    >
      <image class="emoji-image" src="{{item.imageUrl}}" mode="aspectFill" />
      <view class="emoji-info">
        <text class="emoji-title">{{item.title}}</text>
        <text class="emoji-category">{{item.category}}</text>
        <view class="emoji-stats">
          <text class="likes {{item.isLiked ? 'liked' : ''}}">{{item.isLiked ? '❤️' : '🤍'}} {{item.likesText}}</text>
          <text class="collections {{item.isCollected ? 'collected' : ''}}">{{item.isCollected ? '⭐' : '☆'}} {{item.collectionsText}}</text>
        </view>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <text class="empty-icon">💔</text>
    <text class="empty-title">还没有点赞的表情包</text>
    <text class="empty-desc">去发现更多有趣的表情包吧</text>
    <button class="explore-btn" bindtap="onGoExplore">去探索</button>
  </view>

  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>