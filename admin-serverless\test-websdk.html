<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web SDK 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status {
            padding: 8px 16px;
            border-radius: 6px;
            margin: 8px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 Web SDK 免费方案测试</h1>
    <p>测试云开发Web SDK直接操作数据库的免费方案</p>

    <!-- SDK初始化测试 -->
    <div class="test-card">
        <div class="test-title">1. 🌤️ Web SDK 初始化测试</div>
        <button class="test-button" onclick="testWebSDKInit()">初始化 Web SDK</button>
        <div id="sdk-init-result" class="test-result"></div>
    </div>

    <!-- 管理员登录测试 -->
    <div class="test-card">
        <div class="test-title">2. 🔐 管理员身份验证测试</div>
        <button class="test-button" onclick="testAdminAuth()">测试管理员登录</button>
        <div id="admin-auth-result" class="test-result"></div>
    </div>

    <!-- 数据库操作测试 -->
    <div class="test-card">
        <div class="test-title">3. 📝 数据库直接操作测试</div>
        <button class="test-button" onclick="testDatabaseInit()">初始化测试数据</button>
        <button class="test-button" onclick="testDatabaseQuery()">查询数据</button>
        <button class="test-button" onclick="testDatabaseStats()">获取统计</button>
        <div id="database-result" class="test-result"></div>
    </div>

    <!-- 完整功能测试 -->
    <div class="test-card">
        <div class="test-title">4. 🎯 完整功能测试</div>
        <button class="test-button" onclick="testCompleteFlow()">完整流程测试</button>
        <div id="complete-result" class="test-result"></div>
    </div>

    <script>
        // 引入改造后的app.js中的函数
        let testResults = {};

        // 测试Web SDK初始化
        async function testWebSDKInit() {
            const resultDiv = document.getElementById('sdk-init-result');
            resultDiv.textContent = '正在初始化Web SDK...\n';

            try {
                // 动态加载云开发Web SDK
                if (!window.cloudbase) {
                    resultDiv.textContent += '📦 加载云开发SDK (cloudbase 2.17.5)...\n';
                    await loadScript('https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js');
                    resultDiv.textContent += '✅ SDK加载成功\n';
                }

                // 初始化云开发应用 - 使用cloudbase 2.x版本
                window.tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142' // 2.x版本必需参数
                });

                resultDiv.textContent += '✅ Web SDK初始化成功\n';
                resultDiv.textContent += `🔧 环境ID: cloud1-5g6pvnpl88dc0142\n`;
                
                testResults.sdkInit = true;
                
            } catch (error) {
                resultDiv.textContent += `❌ 初始化失败: ${error.message}\n`;
                testResults.sdkInit = false;
            }
        }

        // 测试管理员身份验证
        async function testAdminAuth() {
            const resultDiv = document.getElementById('admin-auth-result');
            resultDiv.textContent = '正在测试管理员身份验证...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const auth = window.tcbApp.auth();
                resultDiv.textContent += '🔐 尝试匿名登录（开发模式）...\n';
                
                await auth.signInAnonymously();
                
                const user = auth.currentUser;
                resultDiv.textContent += `✅ 登录成功\n`;
                resultDiv.textContent += `👤 用户ID: ${user.uid}\n`;
                resultDiv.textContent += `🕐 登录时间: ${new Date().toLocaleString()}\n`;
                
                testResults.auth = true;
                
            } catch (error) {
                resultDiv.textContent += `❌ 身份验证失败: ${error.message}\n`;
                testResults.auth = false;
            }
        }

        // 测试数据库初始化
        async function testDatabaseInit() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.textContent = '正在初始化测试数据...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const db = window.tcbApp.database();
                
                // 测试数据 - 匹配云函数期望的字段结构
                const testCategories = [
                    {
                        id: 'funny',
                        name: '搞笑',
                        icon: '😂',
                        sort: 1,
                        status: 'show',  // 云函数查询条件
                        description: '搞笑幽默表情包'
                    },
                    {
                        id: 'cute',
                        name: '可爱',
                        icon: '🥰',
                        sort: 2,
                        status: 'show',  // 云函数查询条件
                        description: '可爱萌宠表情包'
                    }
                ];

                const testEmojis = [
                    {
                        id: 'emoji1',
                        name: '哈哈大笑',
                        title: '哈哈大笑',
                        url: 'https://example.com/emoji1.gif',
                        imageUrl: 'https://example.com/emoji1.gif',
                        category: 'funny',
                        categoryId: 'funny',  // 云函数查询条件
                        tags: ['搞笑', '大笑'],
                        downloads: 0,
                        likes: 0,
                        collections: 0,
                        status: 'published'  // 云函数查询条件
                    }
                ];

                // 批量写入分类数据
                resultDiv.textContent += '📂 写入分类数据...\n';
                for (const category of testCategories) {
                    await db.collection('categories').add({
                        data: {
                            ...category,
                            createTime: new Date(),
                            updateTime: new Date()
                        }
                    });
                }
                
                // 批量写入表情包数据
                resultDiv.textContent += '😀 写入表情包数据...\n';
                for (const emoji of testEmojis) {
                    await db.collection('emojis').add({
                        data: {
                            ...emoji,
                            createTime: new Date(),
                            updateTime: new Date()
                        }
                    });
                }
                
                resultDiv.textContent += `✅ 测试数据初始化完成\n`;
                resultDiv.textContent += `📊 成功创建 ${testCategories.length} 个分类和 ${testEmojis.length} 个表情包\n`;
                
                testResults.dbInit = true;
                
            } catch (error) {
                resultDiv.textContent += `❌ 数据库初始化失败: ${error.message}\n`;
                testResults.dbInit = false;
            }
        }

        // 测试数据库查询
        async function testDatabaseQuery() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.textContent += '\n正在查询数据...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const db = window.tcbApp.database();
                
                // 查询分类
                const categories = await db.collection('categories').get();
                resultDiv.textContent += `📂 分类数量: ${categories.data.length}\n`;
                
                // 查询表情包
                const emojis = await db.collection('emojis').get();
                resultDiv.textContent += `😀 表情包数量: ${emojis.data.length}\n`;
                
                if (categories.data.length > 0) {
                    resultDiv.textContent += `📋 分类示例: ${JSON.stringify(categories.data[0], null, 2)}\n`;
                }
                
                testResults.dbQuery = true;
                
            } catch (error) {
                resultDiv.textContent += `❌ 数据查询失败: ${error.message}\n`;
                testResults.dbQuery = false;
            }
        }

        // 测试统计数据获取
        async function testDatabaseStats() {
            const resultDiv = document.getElementById('database-result');
            resultDiv.textContent += '\n正在获取统计数据...\n';

            try {
                if (!window.tcbApp) {
                    throw new Error('请先初始化Web SDK');
                }

                const db = window.tcbApp.database();
                
                // 获取统计数据
                const [categoriesCount, emojisCount] = await Promise.all([
                    db.collection('categories').count(),
                    db.collection('emojis').count()
                ]);
                
                const stats = {
                    categories: categoriesCount.total,
                    emojis: emojisCount.total,
                    users: 0,
                    downloads: 0
                };
                
                resultDiv.textContent += `📊 统计数据:\n`;
                resultDiv.textContent += `   分类总数: ${stats.categories}\n`;
                resultDiv.textContent += `   表情包总数: ${stats.emojis}\n`;
                resultDiv.textContent += `   用户总数: ${stats.users}\n`;
                resultDiv.textContent += `   下载总数: ${stats.downloads}\n`;
                
                testResults.dbStats = true;
                
            } catch (error) {
                resultDiv.textContent += `❌ 统计数据获取失败: ${error.message}\n`;
                testResults.dbStats = false;
            }
        }

        // 完整流程测试
        async function testCompleteFlow() {
            const resultDiv = document.getElementById('complete-result');
            resultDiv.textContent = '开始完整流程测试...\n';

            try {
                // 1. 初始化SDK
                resultDiv.textContent += '1️⃣ 初始化Web SDK...\n';
                if (!testResults.sdkInit) {
                    await testWebSDKInit();
                }
                
                // 2. 身份验证
                resultDiv.textContent += '2️⃣ 管理员身份验证...\n';
                if (!testResults.auth) {
                    await testAdminAuth();
                }
                
                // 3. 数据库操作
                resultDiv.textContent += '3️⃣ 数据库操作测试...\n';
                if (!testResults.dbInit) {
                    await testDatabaseInit();
                }
                
                // 4. 数据查询
                resultDiv.textContent += '4️⃣ 数据查询测试...\n';
                await testDatabaseQuery();
                
                // 5. 统计数据
                resultDiv.textContent += '5️⃣ 统计数据测试...\n';
                await testDatabaseStats();
                
                resultDiv.textContent += '\n🎉 完整流程测试成功！\n';
                resultDiv.textContent += '✅ 免费方案验证通过，可以替代付费的HTTP访问服务\n';
                
            } catch (error) {
                resultDiv.textContent += `❌ 完整流程测试失败: ${error.message}\n`;
            }
        }

        // 动态加载脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
    </script>
</body>
</html>
