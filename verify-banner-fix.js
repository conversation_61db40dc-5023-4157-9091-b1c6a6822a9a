// 验证横幅修复状态
const { chromium } = require('playwright');

async function verifyBannerFix() {
    console.log('🔧 验证横幅修复状态...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理横幅数据') || text.includes('横幅数据加载') || text.includes('未命名横幅')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 进入横幅管理页面');
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查AdminApp中的横幅数据');
        
        // 检查AdminApp中的数据
        const adminAppData = await page.evaluate(() => {
            return {
                hasBanners: !!AdminApp.data.banners,
                bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                banners: AdminApp.data.banners || []
            };
        });
        
        console.log('📊 AdminApp横幅数据:');
        console.log('有横幅数据:', adminAppData.hasBanners);
        console.log('横幅数量:', adminAppData.bannersCount);
        
        if (adminAppData.banners.length > 0) {
            console.log('\n📋 AdminApp中的横幅数据:');
            adminAppData.banners.forEach((banner, index) => {
                console.log(`\n横幅 ${index + 1}:`);
                console.log(`  ID: ${banner._id}`);
                console.log(`  标题: "${banner.title}"`);
                console.log(`  状态: "${banner.status}"`);
                console.log(`  优先级: ${banner.priority}`);
                console.log(`  图片URL: ${banner.imageUrl}`);
                
                // 检查修复是否生效
                if (banner.title === undefined || banner.title === 'undefined') {
                    console.log(`  🔴 标题仍然是undefined - 修复未生效`);
                } else if (banner.title === '未命名横幅') {
                    console.log(`  🟡 标题被设置为默认值 - 修复部分生效`);
                } else if (banner.title && banner.title.trim() !== '') {
                    console.log(`  ✅ 标题正常 - 修复生效`);
                } else {
                    console.log(`  🔴 标题异常: "${banner.title}"`);
                }
            });
        }
        
        console.log('\n📍 手动重新加载横幅数据');
        
        // 手动调用loadBanners函数
        const reloadResult = await page.evaluate(async () => {
            try {
                console.log('🔧 手动重新加载横幅数据...');
                await loadBanners();
                
                return {
                    success: true,
                    bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                    sampleBanner: AdminApp.data.banners && AdminApp.data.banners.length > 0 ? 
                        AdminApp.data.banners[0] : null
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 重新加载结果:');
        console.log('成功:', reloadResult.success);
        console.log('横幅数量:', reloadResult.bannersCount);
        
        if (reloadResult.sampleBanner) {
            console.log('\n📋 重新加载后的示例横幅:');
            console.log('标题:', reloadResult.sampleBanner.title);
            console.log('状态:', reloadResult.sampleBanner.status);
            
            if (reloadResult.sampleBanner.title === '未命名横幅') {
                console.log('✅ 修复生效 - 默认标题已设置');
            } else if (reloadResult.sampleBanner.title && reloadResult.sampleBanner.title !== 'undefined') {
                console.log('✅ 修复生效 - 标题正常');
            } else {
                console.log('🔴 修复未生效 - 标题仍然有问题');
            }
        }
        
        console.log('\n📍 检查表格显示');
        
        // 检查表格显示
        const tableData = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#banner-tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const titleCell = cells[2]; // 标题列
                const statusCell = cells[8]; // 状态列
                
                return {
                    index: index + 1,
                    title: titleCell ? titleCell.textContent?.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent?.trim() : 'N/A'
                };
            });
        });
        
        console.log('📊 表格显示结果:');
        tableData.forEach(row => {
            console.log(`\n表格行 ${row.index}:`);
            console.log(`  标题: "${row.title}"`);
            console.log(`  状态: "${row.status}"`);
            
            if (row.title === 'undefined') {
                console.log('  🔴 表格仍显示undefined');
            } else if (row.title === '未命名横幅') {
                console.log('  🟡 表格显示默认标题');
            } else if (row.title && row.title !== 'N/A') {
                console.log('  ✅ 表格显示正常');
            }
        });
        
        // 截图
        await page.screenshot({ path: 'verify-banner-fix.png', fullPage: true });
        console.log('\n📸 验证截图已保存: verify-banner-fix.png');
        
        console.log('\n⏸️ 验证完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 验证过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行验证
verifyBannerFix().catch(console.error);
