<!-- 自定义TabBar模板 -->
<view class="tab-bar">
  <!-- 背景光晕效果 -->
  <view class="tab-bar-bg"></view>
  
  <!-- TabBar项目 -->
  <view 
    wx:for="{{list}}" 
    wx:key="index" 
    class="tab-bar-item {{selected === index ? 'selected' : ''}} {{item.isAnimating ? 'animating' : ''}}"
    data-path="{{item.pagePath}}" 
    data-index="{{index}}" 
    bindtap="switchTab"
  >
    <!-- 选中状态背景光晕 -->
    <view class="item-glow {{selected === index ? 'active' : ''}}"></view>
    
    <!-- 图标容器 -->
    <view class="icon-container">
      <image 
        src="{{selected === index ? item.selectedIconPath : item.iconPath}}" 
        class="icon {{selected === index ? 'icon-selected' : ''}}"
      />
      <!-- 点击波纹效果 -->
      <view class="ripple {{item.isAnimating ? 'ripple-active' : ''}}"></view>
    </view>
    
    <!-- 文字 -->
    <view class="text {{selected === index ? 'text-selected' : ''}}">
      {{item.text}}
    </view>
  </view>
</view>