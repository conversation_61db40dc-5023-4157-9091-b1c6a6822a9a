// pages/fix-sync/fix-sync.js
Page({
  data: {
    envResult: '等待检查...',
    initResult: '',
    testDataResult: '',
    verifyResult: '',
    logText: '🚀 数据同步修复工具已加载\n⚠️ 请按顺序执行修复步骤\n\n',
    scrollTop: 0
  },

  onLoad() {
    this.log('🚀 数据同步修复工具已加载');
    this.log('⚠️ 请按顺序执行修复步骤，或直接点击"一键修复"');
  },

  // 日志记录
  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : 
                  type === 'success' ? '✅' : 
                  type === 'warning' ? '⚠️' : 'ℹ️';
    
    const newLog = `[${timestamp}] ${prefix} ${message}\n`;
    
    this.setData({
      logText: this.data.logText + newLog,
      scrollTop: this.data.scrollTop + 100
    });
  },

  // 检查环境
  async checkEnvironment() {
    this.log('🔍 开始检查环境...');
    
    try {
      // 测试云函数连接
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'ping' }
      });
      
      if (result.result && result.result.success) {
        this.log('✅ 云函数连接正常');
        this.setData({ envResult: '✅ 环境正常' });
      } else {
        throw new Error('云函数响应异常');
      }
    } catch (error) {
      this.log(`❌ 环境检查失败: ${error.message}`, 'error');
      this.setData({ envResult: `❌ ${error.message}` });
    }
  },

  // 强制初始化数据库
  async forceInitDatabase() {
    this.log('💥 开始强制初始化数据库...');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'forceInitDatabase' }
      });
      
      if (result.result && result.result.success) {
        this.log('✅ 数据库强制初始化成功');
        this.setData({ initResult: '✅ 初始化成功' });
      } else {
        throw new Error(result.result?.message || '初始化失败');
      }
    } catch (error) {
      this.log(`❌ 数据库初始化失败: ${error.message}`, 'error');
      this.setData({ initResult: `❌ ${error.message}` });
    }
  },

  // 添加测试数据
  async initTestData() {
    this.log('📦 开始添加测试数据...');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'initTestData' }
      });
      
      if (result.result && result.result.success) {
        this.log('✅ 测试数据添加成功');
        this.setData({ testDataResult: '✅ 数据添加成功' });
      } else {
        throw new Error(result.result?.message || '数据添加失败');
      }
    } catch (error) {
      this.log(`❌ 测试数据添加失败: ${error.message}`, 'error');
      this.setData({ testDataResult: `❌ ${error.message}` });
    }
  },

  // 验证数据
  async verifyData() {
    this.log('🧪 开始验证数据...');
    
    try {
      // 验证分类数据
      const categoriesResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getCategories' }
      });
      
      // 验证表情包数据
      const emojisResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getEmojis', data: { page: 1, limit: 10 } }
      });
      
      // 验证横幅数据
      const bannersResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: { action: 'getBanners' }
      });
      
      const categories = categoriesResult.result?.data || [];
      const emojis = emojisResult.result?.data || [];
      const banners = bannersResult.result?.data || [];
      
      const resultText = `分类:${categories.length}个 表情包:${emojis.length}个 横幅:${banners.length}个`;
      
      if (categories.length > 0 && emojis.length > 0) {
        this.log('🎉 数据验证成功！小程序现在应该能正常显示数据了');
        this.setData({ verifyResult: `✅ ${resultText}` });
        
        // 显示成功提示
        wx.showModal({
          title: '修复成功！',
          content: `数据验证通过：\n${resultText}\n\n现在可以回到首页查看效果！`,
          showCancel: false,
          confirmText: '返回首页',
          success: () => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        });
      } else {
        this.log('⚠️ 数据验证异常，可能需要重新初始化', 'warning');
        this.setData({ verifyResult: `⚠️ ${resultText}` });
      }
      
    } catch (error) {
      this.log(`❌ 数据验证失败: ${error.message}`, 'error');
      this.setData({ verifyResult: `❌ 验证失败` });
    }
  },

  // 一键修复
  async oneClickFix() {
    this.log('🚀 开始一键修复...');
    
    wx.showLoading({
      title: '正在修复...',
      mask: true
    });
    
    try {
      // 第1步：检查环境
      this.log('第1步：检查环境...');
      await this.checkEnvironment();
      
      // 第2步：强制初始化数据库
      this.log('第2步：强制初始化数据库...');
      await this.forceInitDatabase();
      
      // 等待一下
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 第3步：添加测试数据
      this.log('第3步：添加测试数据...');
      await this.initTestData();
      
      // 等待一下
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 第4步：验证数据
      this.log('第4步：验证数据...');
      await this.verifyData();
      
      this.log('🎉 一键修复完成！');
      
    } catch (error) {
      this.log(`❌ 一键修复过程中出现错误: ${error.message}`, 'error');
      wx.showModal({
        title: '修复失败',
        content: `修复过程中出现错误：${error.message}\n\n请尝试手动执行各个步骤`,
        showCancel: false
      });
    } finally {
      wx.hideLoading();
    }
  }
});
