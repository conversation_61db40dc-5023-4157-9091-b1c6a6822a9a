/**
 * 分页管理器
 * 提供通用的分页加载功能，支持上拉刷新和下拉加载更多
 */

const { DataManager } = require('./newDataManager.js')

const PaginationManager = {
  /**
   * 创建分页实例
   * @param {Object} options - 配置选项
   * @returns {Object} 分页实例
   */
  createInstance(options = {}) {
    const {
      pageSize = 20,
      dataSource = 'emojis',
      category = 'all',
      searchKeyword = '',
      onDataLoad = null,
      onError = null,
      enablePullRefresh = true,
      enableLoadMore = true
    } = options

    return new PaginationInstance({
      pageSize,
      dataSource,
      category,
      searchKeyword,
      onDataLoad,
      onError,
      enablePullRefresh,
      enableLoadMore
    })
  }
}

/**
 * 分页实例类
 */
class PaginationInstance {
  constructor(options) {
    this.pageSize = options.pageSize
    this.dataSource = options.dataSource
    this.category = options.category
    this.searchKeyword = options.searchKeyword
    this.onDataLoad = options.onDataLoad
    this.onError = options.onError
    this.enablePullRefresh = options.enablePullRefresh
    this.enableLoadMore = options.enableLoadMore

    // 分页状态
    this.currentPage = 1
    this.totalPages = 0
    this.totalCount = 0
    this.hasMore = true
    this.loading = false
    this.refreshing = false
    
    // 数据存储
    this.dataList = []
    this.lastLoadTime = null
  }

  /**
   * 初始化加载数据
   */
  async init() {
    console.log('📄 分页管理器初始化')
    await this.refresh()
  }

  /**
   * 刷新数据（下拉刷新）
   */
  async refresh() {
    if (this.refreshing) {
      console.log('⚠️ 正在刷新中，跳过重复请求')
      return
    }

    console.log('🔄 开始刷新数据')
    this.refreshing = true
    this.currentPage = 1
    this.hasMore = true
    
    try {
      const result = await this.loadData(1, true)
      if (result.success) {
        this.dataList = result.data
        this.updatePaginationInfo(result)
        this.notifyDataChange('refresh', result.data)
      }
    } catch (error) {
      this.handleError('refresh', error)
    } finally {
      this.refreshing = false
    }
  }

  /**
   * 加载更多数据（上拉加载）
   */
  async loadMore() {
    if (this.loading || !this.hasMore) {
      console.log('⚠️ 无法加载更多:', { loading: this.loading, hasMore: this.hasMore })
      return
    }

    console.log(`📄 加载更多数据: 第 ${this.currentPage + 1} 页`)
    this.loading = true

    try {
      const result = await this.loadData(this.currentPage + 1, false)
      if (result.success && result.data.length > 0) {
        this.currentPage++
        this.dataList = [...this.dataList, ...result.data]
        this.updatePaginationInfo(result)
        this.notifyDataChange('loadMore', result.data)
      } else {
        this.hasMore = false
        console.log('📄 没有更多数据了')
      }
    } catch (error) {
      this.handleError('loadMore', error)
    } finally {
      this.loading = false
    }
  }

  /**
   * 加载数据
   * @param {number} page - 页码
   * @param {boolean} isRefresh - 是否是刷新操作
   * @returns {Promise<Object>} 加载结果
   */
  async loadData(page, isRefresh = false) {
    console.log(`☁️ 加载数据: ${this.dataSource}, 页码: ${page}, 分类: ${this.category}`)

    try {
      let result
      
      switch (this.dataSource) {
        case 'emojis':
          if (this.searchKeyword) {
            // 搜索模式
            result = await DataManager.searchEmojis(
              this.searchKeyword, 
              page, 
              this.pageSize
            )
            return {
              success: true,
              data: result,
              pagination: {
                page,
                hasMore: result.length === this.pageSize,
                total: result.length
              }
            }
          } else {
            // 普通列表模式 - 直接调用云函数
            const cloudResult = await wx.cloud.callFunction({
              name: 'dataAPI',
              data: {
                action: 'getEmojis',
                data: {
                  category: this.category,
                  page: page,
                  limit: this.pageSize
                }
              }
            })

            console.log('☁️ 云函数调用结果:', cloudResult.result)

            if (cloudResult.result && cloudResult.result.success) {
              return cloudResult.result
            } else {
              throw new Error(cloudResult.result?.message || '数据加载失败')
            }
          }
          
        case 'categories':
          result = await DataManager.getCategoriesWithStats({
            forceRefresh: isRefresh
          })
          return {
            success: true,
            data: result,
            pagination: {
              page: 1,
              hasMore: false,
              total: result.length
            }
          }
          
        default:
          throw new Error(`未知数据源: ${this.dataSource}`)
      }
    } catch (error) {
      console.error('❌ 数据加载失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  /**
   * 更新分页信息
   * @param {Object} result - 加载结果
   */
  updatePaginationInfo(result) {
    // 处理新的云函数返回格式
    if (result.hasMore !== undefined) {
      this.hasMore = result.hasMore
      this.totalCount = result.total || 0
    } else if (result.pagination) {
      this.hasMore = result.pagination.hasMore
      this.totalCount = result.pagination.total || this.dataList.length
    } else {
      // 兜底逻辑
      this.hasMore = result.data.length === this.pageSize
    }

    this.lastLoadTime = Date.now()

    console.log('📊 分页信息更新:', {
      currentPage: this.currentPage,
      hasMore: this.hasMore,
      totalCount: this.totalCount,
      dataLength: this.dataList.length
    })
  }

  /**
   * 通知数据变更
   * @param {string} type - 变更类型
   * @param {Array} newData - 新数据
   */
  notifyDataChange(type, newData) {
    if (this.onDataLoad) {
      try {
        this.onDataLoad({
          type,
          data: this.dataList,
          newData,
          pagination: {
            currentPage: this.currentPage,
            hasMore: this.hasMore,
            totalCount: this.totalCount,
            loading: this.loading,
            refreshing: this.refreshing
          }
        })
      } catch (error) {
        console.error('❌ 数据变更通知失败:', error)
      }
    }
  }

  /**
   * 处理错误
   * @param {string} operation - 操作类型
   * @param {Error} error - 错误对象
   */
  handleError(operation, error) {
    console.error(`❌ ${operation} 操作失败:`, error)
    
    if (this.onError) {
      try {
        this.onError({
          operation,
          error,
          message: error.message || '操作失败'
        })
      } catch (callbackError) {
        console.error('❌ 错误处理回调失败:', callbackError)
      }
    }
  }

  /**
   * 更新搜索关键词
   * @param {string} keyword - 搜索关键词
   */
  async updateSearchKeyword(keyword) {
    if (this.searchKeyword === keyword) {
      return
    }
    
    console.log(`🔍 更新搜索关键词: "${this.searchKeyword}" → "${keyword}"`)
    this.searchKeyword = keyword
    await this.refresh()
  }

  /**
   * 更新分类
   * @param {string} category - 分类ID
   */
  async updateCategory(category) {
    if (this.category === category) {
      return
    }
    
    console.log(`📂 更新分类: "${this.category}" → "${category}"`)
    this.category = category
    await this.refresh()
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getState() {
    return {
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      totalCount: this.totalCount,
      hasMore: this.hasMore,
      loading: this.loading,
      refreshing: this.refreshing,
      dataLength: this.dataList.length,
      lastLoadTime: this.lastLoadTime,
      category: this.category,
      searchKeyword: this.searchKeyword
    }
  }

  /**
   * 重置分页状态
   */
  reset() {
    console.log('🔄 重置分页状态')
    this.currentPage = 1
    this.totalPages = 0
    this.totalCount = 0
    this.hasMore = true
    this.loading = false
    this.refreshing = false
    this.dataList = []
    this.lastLoadTime = null
  }

  /**
   * 销毁分页实例
   */
  destroy() {
    console.log('🗑️ 销毁分页实例')
    this.reset()
    this.onDataLoad = null
    this.onError = null
  }
}

module.exports = {
  PaginationManager,
  PaginationInstance
}
