// 调试导航链接
const { chromium } = require('playwright');

async function debugNavigationLinks() {
    console.log('🔍 调试导航链接...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 检查所有导航链接');
        
        // 检查所有导航链接
        const navLinks = await page.evaluate(() => {
            const links = Array.from(document.querySelectorAll('.nav-link'));
            return links.map((link, index) => {
                const icon = link.querySelector('.nav-icon');
                return {
                    index: index + 1,
                    text: link.textContent?.trim(),
                    icon: icon ? icon.textContent?.trim() : 'N/A',
                    onclick: link.getAttribute('onclick'),
                    visible: link.offsetWidth > 0 && link.offsetHeight > 0
                };
            });
        });
        
        console.log('📊 所有导航链接:');
        navLinks.forEach(link => {
            console.log(`\n链接 ${link.index}:`);
            console.log(`  文本: "${link.text}"`);
            console.log(`  图标: "${link.icon}"`);
            console.log(`  点击事件: ${link.onclick}`);
            console.log(`  可见: ${link.visible}`);
        });
        
        console.log('\n📍 查找表情包相关链接');
        
        const emojiLinks = navLinks.filter(link => 
            link.text?.includes('表情包') || 
            link.text?.includes('emoji') ||
            link.onclick?.includes('emoji')
        );
        
        console.log('📊 表情包相关链接:');
        if (emojiLinks.length > 0) {
            emojiLinks.forEach(link => {
                console.log(`  找到: "${link.text}" (${link.icon})`);
            });
        } else {
            console.log('  ❌ 未找到表情包相关链接');
        }
        
        console.log('\n📍 检查页面内容');
        
        // 检查页面是否包含表情包管理相关内容
        const pageContent = await page.evaluate(() => {
            const body = document.body.textContent;
            return {
                hasEmojiManagement: body.includes('表情包管理'),
                hasEmojiPage: !!document.getElementById('emoji-management-page'),
                hasAddEmojiBtn: body.includes('添加表情包'),
                allPageSections: Array.from(document.querySelectorAll('.page-section')).map(section => ({
                    id: section.id,
                    visible: section.classList.contains('active') || section.style.display !== 'none'
                }))
            };
        });
        
        console.log('📊 页面内容检查:');
        console.log('包含"表情包管理"文本:', pageContent.hasEmojiManagement);
        console.log('有emoji-management-page元素:', pageContent.hasEmojiPage);
        console.log('包含"添加表情包"文本:', pageContent.hasAddEmojiBtn);
        console.log('所有页面区域:', pageContent.allPageSections);
        
        // 尝试直接调用showPage函数
        console.log('\n📍 尝试直接调用showPage函数');
        
        const directCall = await page.evaluate(() => {
            try {
                if (typeof showPage === 'function') {
                    showPage('emoji-management');
                    return { success: true, message: 'showPage函数调用成功' };
                } else {
                    return { success: false, error: 'showPage函数不存在' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
        
        console.log('直接调用结果:', directCall);
        
        if (directCall.success) {
            await page.waitForTimeout(3000);
            
            // 检查页面是否切换成功
            const pageSwitch = await page.evaluate(() => {
                const emojiPage = document.getElementById('emoji-management-page');
                return {
                    emojiPageExists: !!emojiPage,
                    emojiPageActive: emojiPage ? emojiPage.classList.contains('active') : false,
                    emojiPageVisible: emojiPage ? emojiPage.style.display !== 'none' : false
                };
            });
            
            console.log('页面切换结果:', pageSwitch);
        }
        
        // 截图
        await page.screenshot({ path: 'debug-navigation-links.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-navigation-links.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugNavigationLinks().catch(console.error);
