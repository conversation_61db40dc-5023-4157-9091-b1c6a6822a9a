// 强制刷新缓存测试
const { chromium } = require('playwright');

async function testCacheRefresh() {
    console.log('🔄 强制刷新缓存测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext({
        // 禁用缓存
        ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // 禁用缓存
    await page.route('**/*', route => {
        const headers = {
            ...route.request().headers(),
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        };
        route.continue({ headers });
    });
    
    // 监听控制台消息，特别关注分类数据处理
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理分类数据') || text.includes('分类数据加载') || text.includes('过滤掉无效分类')) {
            console.log('🔧', text);
        }
    });
    
    try {
        console.log('📍 强制刷新访问管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 强制刷新页面
        await page.reload({ waitUntil: 'networkidle' });
        
        // 等待初始化完成
        await page.waitForTimeout(8000);
        
        console.log('\n📍 检查初始化后的分类数据');
        const initData = await page.evaluate(() => {
            return {
                categoriesLength: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                sampleCategories: AdminApp.data.categories ? AdminApp.data.categories.slice(0, 2) : []
            };
        });
        
        console.log('📊 初始化数据:', JSON.stringify(initData, null, 2));
        
        console.log('\n📍 手动调用loadCategories函数');
        const manualLoadResult = await page.evaluate(async () => {
            try {
                // 添加详细调试
                console.log('=== 开始手动调用loadCategories ===');
                
                // 先获取原始数据看看
                const rawResult = await CloudAPI.database.get('categories');
                console.log('原始数据库结果:', rawResult);
                
                if (rawResult.success && rawResult.data && rawResult.data.length > 0) {
                    console.log('第一条原始数据:', rawResult.data[0]);
                    
                    // 手动处理第一条数据看看
                    const firstCategory = rawResult.data[0];
                    const categoryData = firstCategory.data || firstCategory;
                    console.log('提取的categoryData:', categoryData);
                    console.log('categoryData.name:', categoryData.name);
                    console.log('typeof categoryData.name:', typeof categoryData.name);
                }
                
                // 现在调用loadCategories
                await loadCategories();
                
                return {
                    success: true,
                    categoriesAfterLoad: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                    sampleData: AdminApp.data.categories ? AdminApp.data.categories.slice(0, 2) : []
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    stack: error.stack
                };
            }
        });
        
        console.log('📊 手动加载结果:', JSON.stringify(manualLoadResult, null, 2));
        
        if (manualLoadResult.success) {
            if (manualLoadResult.categoriesAfterLoad > 0) {
                console.log('\n✅ 成功！分类数据正常加载');
                
                // 测试分类管理界面
                console.log('\n📍 测试分类管理界面');
                const categoryLink = await page.locator('text=分类管理').first();
                if (await categoryLink.isVisible()) {
                    await categoryLink.click();
                    await page.waitForTimeout(2000);
                    
                    const tableRows = await page.locator('table tbody tr').count();
                    console.log('📋 表格行数:', tableRows);
                    
                    if (tableRows > 0) {
                        const firstRowData = await page.locator('table tbody tr').first().locator('td').allTextContents();
                        console.log('📋 第一行数据:', firstRowData);
                        
                        if (firstRowData.some(cell => cell.includes('undefined'))) {
                            console.log('❌ 表格中仍有undefined数据');
                        } else {
                            console.log('✅ 表格数据正常');
                        }
                    }
                }
            } else {
                console.log('\n❌ 分类数据仍然为空，需要进一步调试');
            }
        } else {
            console.log('\n❌ 手动加载失败:', manualLoadResult.error);
        }
        
        // 截图
        await page.screenshot({ path: 'cache-refresh-test.png', fullPage: true });
        console.log('\n📸 测试截图已保存: cache-refresh-test.png');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'cache-refresh-error.png' });
    } finally {
        await browser.close();
    }
}

// 运行测试
testCacheRefresh().catch(console.error);
