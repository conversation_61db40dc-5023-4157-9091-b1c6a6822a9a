/* pages/my-collections/my-collections.wxss */
.container {
  padding: 0 20rpx 20rpx 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}



.emoji-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 20rpx;
}

.emoji-item {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.emoji-content {
  cursor: pointer;
}

.emoji-image {
  width: 100%;
  height: 300rpx;
}

.emoji-info {
  padding: 24rpx;
}

.emoji-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.emoji-category {
  font-size: 22rpx;
  color: #8B5CF6;
  margin-bottom: 12rpx;
  display: block;
}

.emoji-stats {
  display: flex;
  justify-content: space-between;
}

.likes, .collections {
  font-size: 22rpx;
  color: #666;
}

.likes.liked {
  color: #ff4757;
}

.collections.collected {
  color: #ffa502;
}

/* 标签区域样式 */
.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #666;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.tag-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}



.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.explore-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.explore-btn::after {
  border: none;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
}
