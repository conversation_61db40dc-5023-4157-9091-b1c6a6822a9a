// pages/profile/profile.js
const { DataManager } = require('../../utils/newDataManager.js')
const { TestData } = require('../../utils/testData.js')
const { AuthManager } = require('../../utils/authManager.js')
const { LoginTest } = require('../../utils/loginTest.js')
const { CloudDiagnostic } = require('../../utils/cloudDiagnostic.js')

Page({
  data: {
    isLoggedIn: false,
    userInfo: {},
    likedCount: 0,
    collectedCount: 0,
    downloadCount: 0,
    recentEmojis: [],
    showLoginModal: false,
    // 智能功能展示配置
    featureConfig: {
      showStats: false,        // 是否显示统计数据
      showMenus: false,        // 是否显示功能菜单
      showAdvanced: false      // 是否显示高级功能
    }
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 同步自定义TabBar状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }
    
    // 检查登录状态
    this.checkLoginStatus()

    // 延迟加载统计数据，确保数据同步
    setTimeout(() => {
      this.loadUserStats()
    }, 150)
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 添加登录状态监听器
    AuthManager.addLoginListener(this.onLoginStatusChange.bind(this))

    // 检查登录状态
    this.checkLoginStatus()

    // 加载数据
    this.loadUserStats()
    // this.loadRecentEmojis() // 暂时隐藏最近浏览功能
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const currentUser = AuthManager.getCurrentUser()
    console.log('🔍 检查登录状态 - AuthManager返回:', currentUser)

    this.setData({
      isLoggedIn: currentUser.isLoggedIn,
      userInfo: currentUser.userInfo || {}
    })

    console.log('🔍 个人中心登录状态:', currentUser.isLoggedIn, currentUser.userInfo?.nickName)
    console.log('🔍 个人中心头像URL:', currentUser.userInfo?.avatarUrl)
    console.log('🔍 页面数据更新后:', this.data.userInfo)
  },

  /**
   * 登录状态变化回调
   */
  onLoginStatusChange(isLoggedIn, userInfo) {
    console.log('🔄 登录状态变化回调:', isLoggedIn, userInfo)

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo || {}
    })

    if (isLoggedIn) {
      // 登录成功后重新加载数据
      this.loadUserStats()
      // this.loadRecentEmojis() // 暂时隐藏最近浏览功能
    }
  },

  /**
   * 登录提示点击
   */
  onLoginPrompt() {
    this.setData({
      showLoginModal: true
    })
  },

  /**
   * 登录弹窗关闭
   */
  onLoginModalClose() {
    this.setData({
      showLoginModal: false
    })
  },

  /**
   * 登录成功回调
   */
  onLoginSuccess(e) {
    const { userInfo, openid } = e.detail
    console.log('✅ 个人中心收到登录成功事件:', userInfo.nickName, openid)

    // 弹窗会自动关闭，这里只需要处理登录成功后的逻辑
    // 数据会通过登录状态监听器自动更新

    // 刷新页面数据
    setTimeout(() => {
      this.checkLoginStatus()
      this.loadUserStats()
    }, 500)
  },





  loadUserStats() {
    // 使用本地存储获取用户统计数据
    try {
      const likedEmojis = wx.getStorageSync('likedEmojis') || []
      const collectedEmojis = wx.getStorageSync('collectedEmojis') || []
      const downloadedEmojis = wx.getStorageSync('downloadedEmojis') || []

      const stats = {
        likedCount: likedEmojis.length,
        collectedCount: collectedEmojis.length,
        downloadCount: downloadedEmojis.length
      }

      // 智能计算功能展示配置
      const featureConfig = this.calculateFeatureConfig(stats)

      this.setData({
        ...stats,
        featureConfig
      })

      console.log('个人中心统计数据:', stats)
      console.log('功能配置:', featureConfig)
    } catch (error) {
      console.error('读取本地存储失败:', error)
      this.setData({
        likedCount: 0,
        collectedCount: 0,
        downloadCount: 0,
        featureConfig: {
          showStats: false,
          showMenus: false,
          showAdvanced: false
        }
      })
    }
  },

  /**
   * 智能计算功能展示配置
   */
  calculateFeatureConfig(stats) {
    const totalActions = stats.likedCount + stats.collectedCount + stats.downloadCount

    // 第一版：完全隐藏高级功能
    // 后续可以根据用户行为智能展示
    return {
      showStats: false,        // totalActions > 0
      showMenus: false,        // totalActions > 5
      showAdvanced: false      // totalActions > 20
    }

    // 未来的智能展示逻辑（已注释）：
    /*
    return {
      showStats: totalActions > 0,
      showMenus: totalActions > 5,
      showAdvanced: totalActions > 20
    }
    */
  },

  /**
   * 从云端获取功能配置（预留接口）
   * 利用系统现有的实时同步能力
   */
  async loadFeatureConfigFromCloud() {
    try {
      // 利用现有的dataAPI云函数获取用户功能配置
      const result = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getUserFeatureConfig',
          data: {
            openid: wx.getStorageSync('openid')
          }
        }
      })

      if (result.result && result.result.success) {
        const cloudConfig = result.result.data
        this.setData({
          featureConfig: {
            ...this.data.featureConfig,
            ...cloudConfig
          }
        })
        console.log('从云端加载功能配置:', cloudConfig)
      }
    } catch (error) {
      console.log('云端功能配置加载失败，使用本地配置:', error)
      // 降级到本地计算，保证功能正常
    }
  },

  // 暂时隐藏最近浏览功能 - 后续产品迭代再考虑
  /*
  loadRecentEmojis() {
    // 从本地存储获取最近浏览的表情包
    try {
      const recentEmojis = wx.getStorageSync('recentEmojis') || []

      // 获取最近5个浏览记录，并从全局数据管理器获取完整信息
      const recentWithDetails = recentEmojis.slice(0, 5).map(emojiId => {
        const emojiData = DataManager.getEmojiData(emojiId)
        return emojiData || null
      }).filter(item => item !== null)

      this.setData({
        recentEmojis: recentWithDetails
      })

      console.log('最近浏览表情包:', recentWithDetails)
    } catch (error) {
      console.error('读取最近浏览记录失败:', error)
      this.setData({
        recentEmojis: []
      })
    }
  },
  */

  onMyLikesTap() {
    wx.navigateTo({
      url: '/pages/my-likes/my-likes'
    })
  },

  onMyCollectionsTap() {
    wx.navigateTo({
      url: '/pages/my-collections/my-collections'
    })
  },

  onBrowseHistoryTap() {
    wx.navigateTo({
      url: '/pages/browse-history/browse-history'
    })
  },

  onDownloadHistoryTap() {
    wx.navigateTo({
      url: '/pages/download-history/download-history'
    })
  },

  onSettingsTap() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 暂时隐藏最近浏览功能 - 后续产品迭代再考虑
  /*
  onViewAllRecent() {
    wx.navigateTo({
      url: '/pages/recent-history/recent-history'
    })
  },

  onRecentEmojiTap(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },
  */

  onShareAppMessage() {
    return {
      title: '我的表情包收藏',
      path: '/pages/profile/profile'
    }
  },

  // ==================== 用户信息编辑相关方法 ====================

  /**
   * 编辑个人资料 - 简化为只修改昵称
   */
  onEditProfile() {
    if (!this.data.isLoggedIn) {
      this.onLoginPrompt()
      return
    }

    console.log('点击编辑个人资料')

    // 直接修改昵称，头像通过点击头像区域来更换
    this.editNickname()
  },



  /**
   * 头像选择回调
   */
  onChooseAvatar(e) {
    console.log('🎯 头像选择事件触发:', e.detail)
    console.log('🎯 当前用户信息:', this.data.userInfo)

    const { avatarUrl } = e.detail

    if (!avatarUrl) {
      console.log('❌ 未获取到头像URL')
      wx.showToast({
        title: '头像选择失败',
        icon: 'error'
      })
      return
    }

    console.log('✅ 选择的头像URL:', avatarUrl)

    wx.showLoading({
      title: '更新头像中...'
    })

    // 立即更新页面显示，不等待模拟延迟
    const updatedUserInfo = {
      ...this.data.userInfo,
      avatarUrl: avatarUrl
    }

    console.log('📝 准备更新的用户信息:', updatedUserInfo)

    // 立即更新页面数据
    this.setData({
      userInfo: updatedUserInfo
    }, () => {
      console.log('📱 页面数据已更新:', this.data.userInfo)
    })

    // 更新AuthManager中的用户信息
    const updateResult = AuthManager.updateUserInfo(updatedUserInfo)
    console.log('🔄 AuthManager更新结果:', updateResult)

    // 模拟上传头像处理
    setTimeout(() => {
      try {
        wx.hideLoading()
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        })

        console.log('✅ 头像更新完成')

        // 再次确认数据状态
        console.log('🔍 最终用户信息:', this.data.userInfo)
        console.log('🔍 AuthManager用户信息:', AuthManager.getCurrentUser())

      } catch (error) {
        console.error('❌ 头像更新失败:', error)
        wx.hideLoading()
        wx.showToast({
          title: '头像更新失败',
          icon: 'error'
        })
      }
    }, 800)
  },

  /**
   * 编辑昵称
   */
  editNickname() {
    wx.showModal({
      title: '修改昵称',
      content: '请输入新昵称',
      editable: true,
      placeholderText: this.data.userInfo.nickName || '请输入昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          const newNickname = res.content.trim()
          if (newNickname.length > 0) {
            this.updateNickname(newNickname)
          }
        }
      }
    })
  },

  /**
   * 更新昵称
   */
  updateNickname(nickname) {
    const updatedUserInfo = {
      ...this.data.userInfo,
      nickName: nickname
    }

    // 更新AuthManager中的用户信息
    AuthManager.updateUserInfo(updatedUserInfo)

    // 更新页面数据
    this.setData({
      userInfo: updatedUserInfo
    })

    wx.showToast({
      title: '昵称更新成功',
      icon: 'success'
    })
  }
})