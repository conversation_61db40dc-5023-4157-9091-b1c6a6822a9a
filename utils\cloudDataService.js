// utils/cloudDataService.js - 云端数据服务模拟器

/**
 * 云端数据服务
 * 在云开发不可用时提供模拟的云端数据存储和同步功能
 */
const CloudDataService = {
  // 模拟的云端数据存储
  _cloudData: {},
  
  // 是否启用云端模拟
  _enableCloudSimulation: true,

  /**
   * 初始化云端数据服务
   */
  init() {
    console.log('🌐 CloudDataService 初始化')
    
    // 从本地存储加载模拟的云端数据
    this.loadCloudDataFromLocal()
  },

  /**
   * 从本地存储加载模拟的云端数据
   */
  loadCloudDataFromLocal() {
    try {
      const cloudData = wx.getStorageSync('simulatedCloudData')
      if (cloudData) {
        this._cloudData = cloudData
        console.log('📱 从本地加载模拟云端数据:', this._cloudData)
      } else {
        // 初始化空的云端数据结构
        this._cloudData = {}
        console.log('🆕 初始化空的云端数据结构')
      }
    } catch (error) {
      console.error('❌ 加载模拟云端数据失败:', error)
      this._cloudData = {}
    }
  },

  /**
   * 保存模拟的云端数据到本地存储
   */
  saveCloudDataToLocal() {
    try {
      wx.setStorageSync('simulatedCloudData', this._cloudData)
      console.log('💾 模拟云端数据已保存到本地')
    } catch (error) {
      console.error('❌ 保存模拟云端数据失败:', error)
    }
  },

  /**
   * 获取用户的云端数据
   */
  async getUserData(openid) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const userData = this._cloudData[openid] || {
          likedEmojis: [],
          collectedEmojis: [],
          downloadedEmojis: [],
          downloadTimes: {},
          lastSyncTime: new Date().toISOString()
        }
        
        console.log('☁️ 获取用户云端数据:', openid, userData)
        resolve({
          success: true,
          data: userData
        })
      }, 300) // 模拟网络延迟
    })
  },

  /**
   * 同步用户数据到云端
   */
  async syncUserData(openid, userData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 更新云端数据
        this._cloudData[openid] = {
          ...userData,
          lastSyncTime: new Date().toISOString()
        }
        
        // 保存到本地存储
        this.saveCloudDataToLocal()
        
        console.log('☁️ 用户数据已同步到云端:', openid, userData)
        resolve({
          success: true,
          message: '数据同步成功'
        })
      }, 500) // 模拟网络延迟
    })
  },

  /**
   * 同步点赞操作到云端
   */
  async syncLikeOperation(openid, emojiId, isLiked) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!this._cloudData[openid]) {
          this._cloudData[openid] = {
            likedEmojis: [],
            collectedEmojis: [],
            downloadedEmojis: [],
            downloadTimes: {}
          }
        }

        const userData = this._cloudData[openid]
        
        if (isLiked) {
          if (!userData.likedEmojis.includes(emojiId)) {
            userData.likedEmojis.push(emojiId)
          }
        } else {
          userData.likedEmojis = userData.likedEmojis.filter(id => id !== emojiId)
        }
        
        userData.lastSyncTime = new Date().toISOString()
        this.saveCloudDataToLocal()
        
        console.log('👍 点赞操作已同步到云端:', { openid, emojiId, isLiked })
        resolve({
          success: true,
          message: '点赞同步成功'
        })
      }, 200)
    })
  },

  /**
   * 同步收藏操作到云端
   */
  async syncCollectOperation(openid, emojiId, isCollected) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!this._cloudData[openid]) {
          this._cloudData[openid] = {
            likedEmojis: [],
            collectedEmojis: [],
            downloadedEmojis: [],
            downloadTimes: {}
          }
        }

        const userData = this._cloudData[openid]
        
        if (isCollected) {
          if (!userData.collectedEmojis.includes(emojiId)) {
            userData.collectedEmojis.push(emojiId)
          }
        } else {
          userData.collectedEmojis = userData.collectedEmojis.filter(id => id !== emojiId)
        }
        
        userData.lastSyncTime = new Date().toISOString()
        this.saveCloudDataToLocal()
        
        console.log('⭐ 收藏操作已同步到云端:', { openid, emojiId, isCollected })
        resolve({
          success: true,
          message: '收藏同步成功'
        })
      }, 200)
    })
  },

  /**
   * 同步下载操作到云端
   */
  async syncDownloadOperation(openid, emojiId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (!this._cloudData[openid]) {
          this._cloudData[openid] = {
            likedEmojis: [],
            collectedEmojis: [],
            downloadedEmojis: [],
            downloadTimes: {}
          }
        }

        const userData = this._cloudData[openid]
        
        if (!userData.downloadedEmojis.includes(emojiId)) {
          userData.downloadedEmojis.push(emojiId)
        }
        
        userData.downloadTimes[emojiId] = new Date().toISOString()
        userData.lastSyncTime = new Date().toISOString()
        this.saveCloudDataToLocal()
        
        console.log('📥 下载操作已同步到云端:', { openid, emojiId })
        resolve({
          success: true,
          message: '下载同步成功'
        })
      }, 200)
    })
  },

  /**
   * 清除用户的云端数据
   */
  async clearUserData(openid) {
    return new Promise((resolve) => {
      setTimeout(() => {
        delete this._cloudData[openid]
        this.saveCloudDataToLocal()
        
        console.log('🗑️ 用户云端数据已清除:', openid)
        resolve({
          success: true,
          message: '数据清除成功'
        })
      }, 200)
    })
  },

  /**
   * 获取所有云端数据（调试用）
   */
  getAllCloudData() {
    return this._cloudData
  },

  /**
   * 重置云端数据（调试用）
   */
  resetCloudData() {
    this._cloudData = {}
    this.saveCloudDataToLocal()
    console.log('🔄 云端数据已重置')
  },

  /**
   * 检查云端数据服务是否可用
   */
  isAvailable() {
    return this._enableCloudSimulation
  },

  /**
   * 启用/禁用云端模拟
   */
  setEnabled(enabled) {
    this._enableCloudSimulation = enabled
    console.log(`🌐 云端数据模拟已${enabled ? '启用' : '禁用'}`)
  }
}

module.exports = {
  CloudDataService
}
