{"rules": {"categories": {"read": "auth != null", "write": false, "validate": {"name": {"required": true, "type": "string", "maxLength": 50}, "icon": {"required": true, "type": "string", "maxLength": 100}, "description": {"type": "string", "maxLength": 200}, "sort": {"type": "number", "min": 0}, "status": {"type": "string", "enum": ["active", "inactive", "deleted"]}}}, "emojis": {"read": "auth != null", "write": false, "validate": {"title": {"required": true, "type": "string", "maxLength": 100}, "imageUrl": {"required": true, "type": "string", "pattern": "^https?:\\/\\/.+\\.(jpg|jpeg|png|gif|webp)$"}, "categoryId": {"type": "string", "maxLength": 50}, "tags": {"type": "array", "maxItems": 10, "items": {"type": "string", "maxLength": 20}}, "description": {"type": "string", "maxLength": 500}, "status": {"type": "string", "enum": ["published", "draft", "deleted"]}, "likes": {"type": "number", "min": 0}, "downloads": {"type": "number", "min": 0}, "views": {"type": "number", "min": 0}}}, "banners": {"read": "auth != null", "write": false, "validate": {"title": {"required": true, "type": "string", "maxLength": 100}, "imageUrl": {"required": true, "type": "string", "pattern": "^https?:\\/\\/.+\\.(jpg|jpeg|png|gif|webp)$"}, "linkUrl": {"type": "string", "pattern": "^https?:\\/\\/.+"}, "sort": {"type": "number", "min": 0}, "status": {"type": "string", "enum": ["active", "inactive", "deleted"]}, "clickCount": {"type": "number", "min": 0}}}, "sync_notifications": {"read": false, "write": false, "description": "同步通知表，仅云函数可访问"}, "admin_logs": {"read": false, "write": false, "description": "管理员操作日志，仅云函数可访问"}}, "indexes": {"categories": [{"name": "status_sort_index", "fields": [{"field": "status", "order": "asc"}, {"field": "sort", "order": "asc"}]}, {"name": "id_index", "fields": [{"field": "id", "order": "asc"}], "unique": true}], "emojis": [{"name": "status_createTime_index", "fields": [{"field": "status", "order": "asc"}, {"field": "createTime", "order": "desc"}]}, {"name": "categoryId_createTime_index", "fields": [{"field": "categoryId", "order": "asc"}, {"field": "createTime", "order": "desc"}]}, {"name": "tags_index", "fields": [{"field": "tags", "order": "asc"}]}, {"name": "likes_index", "fields": [{"field": "likes", "order": "desc"}]}, {"name": "downloads_index", "fields": [{"field": "downloads", "order": "desc"}]}, {"name": "id_index", "fields": [{"field": "id", "order": "asc"}], "unique": true}], "banners": [{"name": "status_sort_index", "fields": [{"field": "status", "order": "asc"}, {"field": "sort", "order": "asc"}]}, {"name": "id_index", "fields": [{"field": "id", "order": "asc"}], "unique": true}], "sync_notifications": [{"name": "timestamp_index", "fields": [{"field": "timestamp", "order": "desc"}]}, {"name": "dataType_timestamp_index", "fields": [{"field": "dataType", "order": "asc"}, {"field": "timestamp", "order": "desc"}]}, {"name": "processed_index", "fields": [{"field": "processed", "order": "asc"}]}], "admin_logs": [{"name": "timestamp_index", "fields": [{"field": "timestamp", "order": "desc"}]}, {"name": "adminId_timestamp_index", "fields": [{"field": "adminId", "order": "asc"}, {"field": "timestamp", "order": "desc"}]}, {"name": "action_index", "fields": [{"field": "action", "order": "asc"}]}]}, "triggers": {"categories": {"onCreate": {"function": "webAdminAPI", "action": "onCategoryCreate"}, "onUpdate": {"function": "webAdminAPI", "action": "onCategoryUpdate"}, "onDelete": {"function": "webAdminAPI", "action": "onCategoryDelete"}}, "emojis": {"onCreate": {"function": "webAdminAPI", "action": "onEmojiCreate"}, "onUpdate": {"function": "webAdminAPI", "action": "onEmojiUpdate"}, "onDelete": {"function": "webAdminAPI", "action": "onEmojiDelete"}}, "banners": {"onCreate": {"function": "webAdminAPI", "action": "onBannerCreate"}, "onUpdate": {"function": "webAdminAPI", "action": "onBannerUpdate"}, "onDelete": {"function": "webAdminAPI", "action": "onBannerDelete"}}}, "settings": {"enableRealtime": true, "enableBackup": true, "backupSchedule": "0 2 * * *", "backupRetention": 7, "enableAuditLog": true, "enablePerformanceMonitoring": true, "slowQueryThreshold": 1000, "connectionPoolSize": 10, "queryTimeout": 30000}}