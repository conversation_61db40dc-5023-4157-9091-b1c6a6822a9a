// 安全测试
const { describe, test, expect, beforeAll, afterAll } = require('@jest/globals');

// 安全测试工具类
class SecurityTester {
  constructor() {
    this.vulnerabilities = [];
    this.securityChecks = [];
  }

  // 模拟JWT令牌验证
  validateJWTSecurity(token) {
    const checks = {
      hasValidStructure: this.checkJWTStructure(token),
      hasSecureAlgorithm: this.checkJWTAlgorithm(token),
      hasExpirationTime: this.checkJWTExpiration(token),
      hasSecureSecret: this.checkJWTSecret(token)
    };

    return checks;
  }

  // 检查JWT结构
  checkJWTStructure(token) {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const parts = token.split('.');
    return parts.length === 3;
  }

  // 检查JWT算法安全性
  checkJWTAlgorithm(token) {
    try {
      const header = JSON.parse(Buffer.from(token.split('.')[0], 'base64').toString());
      // 确保使用安全的算法，避免none算法
      return header.alg && header.alg !== 'none' && header.alg.startsWith('HS');
    } catch (error) {
      return false;
    }
  }

  // 检查JWT过期时间
  checkJWTExpiration(token) {
    try {
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      return payload.exp && payload.exp > Date.now() / 1000;
    } catch (error) {
      return false;
    }
  }

  // 检查JWT密钥安全性
  checkJWTSecret(secret) {
    if (!secret || secret.length < 32) {
      return false;
    }
    
    // 检查是否包含常见的弱密钥
    const weakSecrets = ['secret', 'password', '123456', 'admin'];
    return !weakSecrets.some(weak => secret.toLowerCase().includes(weak));
  }

  // 模拟SQL注入测试
  testSQLInjection(input) {
    const sqlInjectionPatterns = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM admin --",
      "1' OR 1=1 --",
      "admin'--",
      "' OR 'a'='a"
    ];

    const vulnerablePatterns = sqlInjectionPatterns.filter(pattern => {
      // 模拟检查输入是否包含SQL注入模式
      return input.includes(pattern);
    });

    return {
      isVulnerable: vulnerablePatterns.length > 0,
      detectedPatterns: vulnerablePatterns
    };
  }

  // 模拟XSS攻击测试
  testXSSVulnerability(input) {
    const xssPatterns = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("XSS")',
      '<svg onload="alert(1)">',
      '"><script>alert("XSS")</script>',
      "';alert('XSS');//"
    ];

    const vulnerablePatterns = xssPatterns.filter(pattern => {
      return input.includes(pattern);
    });

    return {
      isVulnerable: vulnerablePatterns.length > 0,
      detectedPatterns: vulnerablePatterns,
      sanitized: this.sanitizeInput(input)
    };
  }

  // 输入清理
  sanitizeInput(input) {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  // 模拟CSRF攻击测试
  testCSRFProtection(request) {
    const hasCSRFToken = request.headers && request.headers['x-csrf-token'];
    const hasRefererCheck = request.headers && request.headers['referer'];
    const hasSameSitePolicy = request.cookies && request.cookies.sameSite;

    return {
      hasCSRFToken: !!hasCSRFToken,
      hasRefererCheck: !!hasRefererCheck,
      hasSameSitePolicy: !!hasSameSitePolicy,
      isProtected: hasCSRFToken || (hasRefererCheck && hasSameSitePolicy)
    };
  }

  // 模拟权限验证
  testAuthorizationBypass(userRole, requiredRole, resource) {
    const roleHierarchy = {
      'guest': 0,
      'user': 1,
      'admin': 2,
      'superadmin': 3
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return {
      hasAccess: userLevel >= requiredLevel,
      userLevel,
      requiredLevel,
      resource
    };
  }

  // 模拟暴力破解防护测试
  testBruteForceProtection(attempts, timeWindow) {
    const maxAttempts = 5;
    const lockoutTime = 300000; // 5分钟

    const isBlocked = attempts.length >= maxAttempts;
    const recentAttempts = attempts.filter(attempt => 
      Date.now() - attempt.timestamp < timeWindow
    );

    return {
      isBlocked,
      attemptCount: recentAttempts.length,
      maxAttempts,
      lockoutTime,
      nextAllowedTime: isBlocked ? Date.now() + lockoutTime : null
    };
  }

  // 模拟数据泄露检测
  testDataLeakage(response) {
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /token/i,
      /key/i,
      /\d{4}-\d{4}-\d{4}-\d{4}/, // 信用卡号
      /\d{3}-\d{2}-\d{4}/, // SSN
      /_id.*ObjectId/i // 数据库ID
    ];

    const leaks = [];
    const responseStr = JSON.stringify(response);

    sensitivePatterns.forEach((pattern, index) => {
      if (pattern.test(responseStr)) {
        leaks.push({
          pattern: pattern.toString(),
          type: ['password', 'secret', 'token', 'key', 'credit_card', 'ssn', 'database_id'][index]
        });
      }
    });

    return {
      hasLeaks: leaks.length > 0,
      leaks,
      sanitizedResponse: this.sanitizeResponse(response)
    };
  }

  // 清理响应中的敏感信息
  sanitizeResponse(response) {
    const sanitized = JSON.parse(JSON.stringify(response));
    
    const sensitiveFields = ['password', 'secret', 'token', 'key', '_id'];
    
    function recursiveSanitize(obj) {
      if (typeof obj === 'object' && obj !== null) {
        for (const key in obj) {
          if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
            obj[key] = '***';
          } else if (typeof obj[key] === 'object') {
            recursiveSanitize(obj[key]);
          }
        }
      }
    }

    recursiveSanitize(sanitized);
    return sanitized;
  }

  // 生成安全报告
  generateSecurityReport(testResults) {
    const vulnerabilities = [];
    const recommendations = [];

    Object.entries(testResults).forEach(([testName, result]) => {
      if (result.isVulnerable || result.hasLeaks || !result.isProtected) {
        vulnerabilities.push({
          test: testName,
          severity: this.assessSeverity(testName, result),
          details: result
        });
      }
    });

    // 生成安全建议
    if (vulnerabilities.length > 0) {
      recommendations.push({
        priority: 'high',
        message: '发现安全漏洞，需要立即修复',
        actions: [
          '修复所有高危漏洞',
          '加强输入验证',
          '实施安全编码规范',
          '定期进行安全审计'
        ]
      });
    }

    return {
      summary: {
        totalTests: Object.keys(testResults).length,
        vulnerabilities: vulnerabilities.length,
        securityScore: this.calculateSecurityScore(testResults),
        timestamp: new Date().toISOString()
      },
      vulnerabilities,
      recommendations
    };
  }

  // 评估漏洞严重程度
  assessSeverity(testName, result) {
    if (testName.includes('SQL') || testName.includes('XSS')) {
      return 'critical';
    }
    if (testName.includes('CSRF') || testName.includes('Authorization')) {
      return 'high';
    }
    if (testName.includes('BruteForce') || testName.includes('DataLeakage')) {
      return 'medium';
    }
    return 'low';
  }

  // 计算安全评分
  calculateSecurityScore(testResults) {
    const totalTests = Object.keys(testResults).length;
    let passedTests = 0;

    Object.values(testResults).forEach(result => {
      if (!result.isVulnerable && !result.hasLeaks && result.isProtected !== false) {
        passedTests++;
      }
    });

    return totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
  }
}

describe('安全测试', () => {
  let securityTester;

  beforeAll(() => {
    securityTester = new SecurityTester();
  });

  describe('T6.1 认证安全测试', () => {
    test('T6.1.1 JWT令牌安全性验证', () => {
      // 测试有效的JWT令牌
      const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbklkIjoiYWRtaW4iLCJwZXJtaXNzaW9ucyI6WyJyZWFkIiwid3JpdGUiLCJkZWxldGUiXSwiZXhwIjoxOTk5OTk5OTk5fQ.signature';
      
      const validation = securityTester.validateJWTSecurity(validToken);
      
      expect(validation.hasValidStructure).toBe(true);
      expect(validation.hasSecureAlgorithm).toBe(true);
      expect(validation.hasExpirationTime).toBe(true);
    });

    test('T6.1.2 JWT密钥强度测试', () => {
      const weakSecret = 'secret123';
      const strongSecret = 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6';
      
      expect(securityTester.checkJWTSecret(weakSecret)).toBe(false);
      expect(securityTester.checkJWTSecret(strongSecret)).toBe(true);
    });

    test('T6.1.3 暴力破解防护测试', () => {
      const attempts = Array.from({ length: 6 }, (_, i) => ({
        timestamp: Date.now() - (i * 1000),
        ip: '*************'
      }));

      const protection = securityTester.testBruteForceProtection(attempts, 300000);
      
      expect(protection.isBlocked).toBe(true);
      expect(protection.attemptCount).toBeGreaterThan(5);
      expect(protection.nextAllowedTime).toBeGreaterThan(Date.now());
    });
  });

  describe('T6.2 数据安全测试', () => {
    test('T6.2.1 SQL注入防护测试', () => {
      const maliciousInputs = [
        "admin'; DROP TABLE users; --",
        "' OR '1'='1",
        "1' UNION SELECT * FROM admin --"
      ];

      maliciousInputs.forEach(input => {
        const result = securityTester.testSQLInjection(input);
        expect(result.isVulnerable).toBe(true);
        expect(result.detectedPatterns.length).toBeGreaterThan(0);
      });

      // 测试安全输入
      const safeInput = "normal_category_name";
      const safeResult = securityTester.testSQLInjection(safeInput);
      expect(safeResult.isVulnerable).toBe(false);
    });

    test('T6.2.2 XSS攻击防护测试', () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(1)">',
        '"><script>alert("XSS")</script>'
      ];

      xssPayloads.forEach(payload => {
        const result = securityTester.testXSSVulnerability(payload);
        expect(result.isVulnerable).toBe(true);
        expect(result.sanitized).not.toContain('<script>');
        expect(result.sanitized).not.toContain('onerror');
      });
    });

    test('T6.2.3 CSRF攻击防护测试', () => {
      // 测试没有CSRF保护的请求
      const vulnerableRequest = {
        method: 'POST',
        headers: {},
        cookies: {}
      };

      const vulnerableResult = securityTester.testCSRFProtection(vulnerableRequest);
      expect(vulnerableResult.isProtected).toBe(false);

      // 测试有CSRF保护的请求
      const protectedRequest = {
        method: 'POST',
        headers: {
          'x-csrf-token': 'valid-csrf-token',
          'referer': 'https://trusted-domain.com'
        },
        cookies: {
          sameSite: 'strict'
        }
      };

      const protectedResult = securityTester.testCSRFProtection(protectedRequest);
      expect(protectedResult.isProtected).toBe(true);
    });
  });

  describe('T6.3 权限控制测试', () => {
    test('T6.3.1 未授权访问防护测试', () => {
      // 测试普通用户访问管理员资源
      const unauthorizedAccess = securityTester.testAuthorizationBypass(
        'user', 
        'admin', 
        '/admin/categories'
      );
      
      expect(unauthorizedAccess.hasAccess).toBe(false);

      // 测试管理员访问管理员资源
      const authorizedAccess = securityTester.testAuthorizationBypass(
        'admin', 
        'admin', 
        '/admin/categories'
      );
      
      expect(authorizedAccess.hasAccess).toBe(true);
    });

    test('T6.3.2 权限提升防护测试', () => {
      // 测试用户尝试提升权限
      const privilegeEscalation = securityTester.testAuthorizationBypass(
        'user', 
        'superadmin', 
        '/admin/system'
      );
      
      expect(privilegeEscalation.hasAccess).toBe(false);
      expect(privilegeEscalation.userLevel).toBeLessThan(privilegeEscalation.requiredLevel);
    });

    test('T6.3.3 数据泄露防护测试', () => {
      // 测试包含敏感信息的响应
      const sensitiveResponse = {
        user: {
          id: 'user123',
          name: 'John Doe',
          password: 'secret123',
          token: 'jwt-token-here',
          _id: 'ObjectId("507f1f77bcf86cd799439011")'
        }
      };

      const leakageTest = securityTester.testDataLeakage(sensitiveResponse);
      
      expect(leakageTest.hasLeaks).toBe(true);
      expect(leakageTest.leaks.length).toBeGreaterThan(0);
      expect(leakageTest.sanitizedResponse.user.password).toBe('***');
      expect(leakageTest.sanitizedResponse.user.token).toBe('***');
    });
  });

  describe('T6.4 综合安全评估', () => {
    test('T6.4.1 系统整体安全评分', () => {
      const testResults = {
        jwtSecurity: { isVulnerable: false, isProtected: true },
        sqlInjection: { isVulnerable: false, isProtected: true },
        xssProtection: { isVulnerable: false, isProtected: true },
        csrfProtection: { isVulnerable: false, isProtected: true },
        authorization: { isVulnerable: false, isProtected: true },
        dataLeakage: { hasLeaks: false, isProtected: true }
      };

      const securityScore = securityTester.calculateSecurityScore(testResults);
      expect(securityScore).toBeGreaterThanOrEqual(80); // 安全评分应该大于80分

      const report = securityTester.generateSecurityReport(testResults);
      expect(report.vulnerabilities.length).toBe(0);
      expect(report.summary.securityScore).toBeGreaterThanOrEqual(80);
    });

    test('T6.4.2 安全漏洞报告生成', () => {
      const vulnerableResults = {
        sqlInjection: { isVulnerable: true, detectedPatterns: ["' OR '1'='1"] },
        xssProtection: { isVulnerable: true, detectedPatterns: ['<script>'] },
        dataLeakage: { hasLeaks: true, leaks: [{ type: 'password' }] }
      };

      const report = securityTester.generateSecurityReport(vulnerableResults);
      
      expect(report.vulnerabilities.length).toBe(3);
      expect(report.summary.securityScore).toBeLessThan(50);
      expect(report.recommendations.length).toBeGreaterThan(0);
    });
  });

  afterAll(() => {
    console.log('安全测试完成');
  });
});
