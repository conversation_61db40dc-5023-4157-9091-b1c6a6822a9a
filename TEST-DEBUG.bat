@echo off
cls

echo ========================================
echo    DEBUG TEST - STEP BY STEP
echo ========================================
echo.

echo Step 1: Check Node.js
node --version
if %errorlevel% neq 0 (
    echo FAIL: Node.js not found
    pause
    exit /b 1
)
echo PASS: Node.js found
echo.

echo Step 2: Check admin-unified directory
if not exist "admin-unified" (
    echo FAIL: admin-unified directory not found
    echo Current directory: %CD%
    dir
    pause
    exit /b 1
)
echo PASS: admin-unified directory found
echo.

echo Step 3: Enter admin-unified directory
cd /d admin-unified
echo Current directory: %CD%
echo.

echo Step 4: Check files
echo Checking test-minimal.html...
if exist "test-minimal.html" (
    echo PASS: test-minimal.html found
) else (
    echo FAIL: test-minimal.html not found
)

echo Checking index-production.html...
if exist "index-production.html" (
    echo PASS: index-production.html found
) else (
    echo FAIL: index-production.html not found
)
echo.

echo Step 5: Create debug server
echo Creating debug server with detailed logging...
(
echo const http = require('http'^);
echo const fs = require('fs'^);
echo const PORT = 8000;
echo.
echo console.log('Starting debug server...'^);
echo.
echo const server = http.createServer((req, res^) =^> {
echo   console.log('=== REQUEST ==='^);
echo   console.log('Method:', req.method^);
echo   console.log('URL:', req.url^);
echo   console.log('Headers:', req.headers^);
echo.
echo   let filePath = req.url === '/' ? 'test-minimal.html' : req.url.substring(1^);
echo   console.log('Serving file:', filePath^);
echo.
echo   fs.readFile(filePath, (err, data^) =^> {
echo     if (err^) {
echo       console.log('ERROR reading file:', err.message^);
echo       res.writeHead(404, {'Content-Type': 'text/plain'^}^);
echo       res.end('File not found: ' + filePath + '\nError: ' + err.message^);
echo       return;
echo     }
echo.
echo     console.log('SUCCESS: File read, size:', data.length, 'bytes'^);
echo     res.writeHead(200, {
echo       'Content-Type': 'text/html; charset=utf-8',
echo       'Cache-Control': 'no-cache'
echo     }^);
echo     res.end(data^);
echo     console.log('Response sent successfully'^);
echo   }^);
echo }^);
echo.
echo server.listen(PORT, (^) =^> {
echo   console.log('=== SERVER STARTED ==='^);
echo   console.log('URL: http://localhost:' + PORT^);
echo   console.log('Time:', new Date(^).toLocaleString(^)^);
echo   console.log('Press Ctrl+C to stop'^);
echo   console.log('======================'^);
echo }^);
echo.
echo server.on('error', (err^) =^> {
echo   console.log('SERVER ERROR:', err.message^);
echo }^);
) > debug-server.js

echo PASS: Debug server created
echo.

echo Step 6: Start server
echo Starting debug server on port 8000...
echo Check console output for detailed logs
echo.

timeout /t 2 /nobreak >nul
start http://localhost:8000

echo Server starting...
node debug-server.js
