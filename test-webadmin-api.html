<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAdminAPI测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebAdminAPI测试</h1>
        
        <div style="text-align: center;">
            <button onclick="testWebAdminAPI()">测试WebAdminAPI云函数</button>
            <button onclick="testCreateData()">测试创建数据</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <!-- 云开发SDK -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    
    <script>
        let tcbApp = null;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 初始化SDK（无需登录）
        async function initSDK() {
            if (tcbApp) return tcbApp;
            
            try {
                log('🚀 初始化CloudBase SDK...');
                
                if (typeof window.cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }

                tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                log('✅ SDK初始化成功（无需登录）', 'success');
                return tcbApp;
                
            } catch (error) {
                log('❌ SDK初始化失败: ' + error.message, 'error');
                throw error;
            }
        }

        // 调用webAdminAPI云函数
        async function callWebAdminAPI(action, data = {}) {
            try {
                await initSDK();
                
                log(`📞 调用webAdminAPI: ${action}`);
                
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: action,
                        data: data,
                        adminPassword: 'admin123456'  // 管理员密码验证
                    }
                });

                log('📊 云函数调用结果:');
                log('  - 调用成功: ' + (result ? '是' : '否'));
                
                if (result && result.result) {
                    log('  - success: ' + result.result.success);
                    log('  - 结果详情: ' + JSON.stringify(result.result, null, 2));
                    
                    if (result.result.success) {
                        return result.result.data;
                    } else {
                        throw new Error(result.result.error || '云函数执行失败');
                    }
                } else {
                    throw new Error('云函数返回结果为空');
                }
                
            } catch (error) {
                log('❌ webAdminAPI调用失败: ' + (error.message || error), 'error');
                throw error;
            }
        }

        // 测试webAdminAPI云函数
        async function testWebAdminAPI() {
            try {
                log('🧪 开始测试webAdminAPI云函数...');
                
                // 测试1: 获取分类列表
                log('📋 测试1: 获取分类列表');
                try {
                    const categories = await callWebAdminAPI('getCategoryList');
                    log('✅ 获取分类成功: ' + (categories ? categories.length : 0) + ' 个分类', 'success');
                    if (categories && categories.length > 0) {
                        log('  - 分类示例: ' + JSON.stringify(categories[0], null, 2));
                    }
                } catch (e) {
                    log('⚠️ 获取分类失败: ' + e.message, 'warning');
                }

                // 测试2: 获取表情包列表
                log('📋 测试2: 获取表情包列表');
                try {
                    const emojis = await callWebAdminAPI('getEmojiList', { page: 1, limit: 5 });
                    log('✅ 获取表情包成功: ' + (emojis ? emojis.length : 0) + ' 个表情包', 'success');
                    if (emojis && emojis.length > 0) {
                        log('  - 表情包示例: ' + JSON.stringify(emojis[0], null, 2));
                    }
                } catch (e) {
                    log('⚠️ 获取表情包失败: ' + e.message, 'warning');
                }

                // 测试3: 获取横幅列表
                log('📋 测试3: 获取横幅列表');
                try {
                    const banners = await callWebAdminAPI('getBannerList');
                    log('✅ 获取横幅成功: ' + (banners ? banners.length : 0) + ' 个横幅', 'success');
                    if (banners && banners.length > 0) {
                        log('  - 横幅示例: ' + JSON.stringify(banners[0], null, 2));
                    }
                } catch (e) {
                    log('⚠️ 获取横幅失败: ' + e.message, 'warning');
                }

                log('🎉 webAdminAPI测试完成', 'success');
                
            } catch (error) {
                log('❌ webAdminAPI测试失败: ' + (error.message || error), 'error');
            }
        }

        // 测试创建数据
        async function testCreateData() {
            try {
                log('🧪 开始测试创建数据...');
                
                // 创建测试分类
                log('📋 创建测试分类...');
                try {
                    const categoryData = {
                        name: '测试分类',
                        icon: '🧪',
                        description: '这是一个测试分类',
                        sort: 999
                    };
                    
                    const result = await callWebAdminAPI('addCategory', categoryData);
                    log('✅ 创建分类成功: ' + JSON.stringify(result, null, 2), 'success');
                } catch (e) {
                    log('⚠️ 创建分类失败: ' + e.message, 'warning');
                }

                // 创建测试表情包
                log('📋 创建测试表情包...');
                try {
                    const emojiData = {
                        title: '测试表情包',
                        category: '测试分类',
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI0ZGRDcwMCIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8Y2lyY2xlIGN4PSIzNSIgY3k9IjQwIiByPSI1IiBmaWxsPSIjMzMzIi8+CiAgPGNpcmNsZSBjeD0iNjUiIGN5PSI0MCIgcj0iNSIgZmlsbD0iIzMzMyIvPgogIDxwYXRoIGQ9Ik0zMCA2NSBRNTAgNzUgNzAgNjUiIHN0cm9rZT0iIzMzMyIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+Cjwvc3ZnPg==',
                        description: '这是一个测试表情包',
                        tags: ['测试', '表情包']
                    };
                    
                    const result = await callWebAdminAPI('addEmoji', emojiData);
                    log('✅ 创建表情包成功: ' + JSON.stringify(result, null, 2), 'success');
                } catch (e) {
                    log('⚠️ 创建表情包失败: ' + e.message, 'warning');
                }

                // 创建测试横幅
                log('📋 创建测试横幅...');
                try {
                    const bannerData = {
                        title: '测试横幅',
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iIzQyODVGNCIvPgogIDx0ZXh0IHg9IjE1MCIgeT0iNzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7mtYvor5XmqKrlubU8L3RleHQ+Cjwvc3ZnPg==',
                        linkType: 'page',
                        link: '/pages/index/index',
                        priority: 999,
                        description: '这是一个测试横幅'
                    };
                    
                    const result = await callWebAdminAPI('addBanner', bannerData);
                    log('✅ 创建横幅成功: ' + JSON.stringify(result, null, 2), 'success');
                } catch (e) {
                    log('⚠️ 创建横幅失败: ' + e.message, 'warning');
                }

                log('🎉 创建数据测试完成', 'success');
                
            } catch (error) {
                log('❌ 创建数据测试失败: ' + (error.message || error), 'error');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            log('页面加载完成，webAdminAPI方式无需登录，可直接测试');
            log('💡 提示：webAdminAPI使用管理员密码验证，绕过身份认证问题');
        });
    </script>
</body>
</html>
