<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接连接测试</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
            border: 1px solid #00ff00;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .log-container {
            background: #000;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .btn {
            background: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        .btn:hover {
            background: #00ff00;
            color: #000;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        .timestamp { color: #888; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 直接HTTP API连接测试</h1>
            <p>环境ID: cloud1-5g6pvnpl88dc0142</p>
            <p style="color: #ffff00;">⚠️ 绕过SDK，直接通过HTTP API连接云开发</p>
        </div>

        <div class="test-section">
            <h3>🧪 测试步骤</h3>
            <button class="btn" onclick="testDirectAPI()">1. 直接API连接测试</button>
            <button class="btn" onclick="testCloudFunction()">2. 云函数调用测试</button>
            <button class="btn" onclick="testDataOperation()">3. 数据操作测试</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="info">📋 准备直接通过HTTP API连接云开发...</div>
            <div class="warning">⚠️ 这将绕过SDK，直接调用云开发HTTP API</div>
        </div>
    </div>

    <script>
        const envId = 'cloud1-5g6pvnpl88dc0142';
        const apiUrl = `https://${envId}.service.tcloudbase.com`;

        // 日志函数
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = document.createElement('div');
            logLine.className = type;
            logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            container.appendChild(logLine);
            container.scrollTop = container.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = 
                '<div class="info">📋 日志已清空</div>';
        }

        // 1. 直接API连接测试
        async function testDirectAPI() {
            addLog('🔗 开始直接HTTP API连接测试...', 'info');

            try {
                // 测试adminAPI云函数的HTTP触发器
                const testUrl = `${apiUrl}/adminAPI`;
                addLog(`📡 测试连接: ${testUrl}`, 'info');

                const response = await fetch(testUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'getStats'
                    })
                });

                addLog(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    addLog('✅ HTTP API连接成功！', 'success');
                    
                    try {
                        const data = await response.text();
                        addLog(`📄 响应内容: ${data.substring(0, 200)}...`, 'info');
                    } catch (e) {
                        addLog('📄 响应内容无法解析为文本', 'warning');
                    }
                } else {
                    addLog(`⚠️ HTTP响应异常，但连接成功`, 'warning');
                }
                
            } catch (error) {
                addLog(`❌ 直接API连接失败: ${error.message}`, 'error');
                console.error('API连接失败:', error);
            }
        }

        // 2. 云函数调用测试
        async function testCloudFunction() {
            addLog('☁️ 开始云函数调用测试...', 'info');

            try {
                const functionUrl = `${apiUrl}/adminAPI`;
                const requestData = {
                    action: 'getCategoryList'
                };

                addLog(`📡 调用云函数: adminAPI`, 'info');
                addLog(`📦 请求数据: ${JSON.stringify(requestData)}`, 'info');

                const response = await fetch(functionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                addLog(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const result = await response.json();
                    addLog('✅ 云函数调用成功！', 'success');
                    addLog(`📊 返回数据: ${JSON.stringify(result, null, 2)}`, 'info');
                    
                    if (result && result.data) {
                        addLog('🎉 获取到真实数据，与小程序完全同步！', 'success');
                    }
                } else {
                    const errorText = await response.text();
                    addLog(`⚠️ 云函数调用失败: ${response.status}`, 'warning');
                    addLog(`📄 错误信息: ${errorText}`, 'warning');
                }
                
            } catch (error) {
                addLog(`❌ 云函数调用失败: ${error.message}`, 'error');
                console.error('云函数调用失败:', error);
            }
        }

        // 3. 数据操作测试
        async function testDataOperation() {
            addLog('📝 开始数据操作测试...', 'info');

            try {
                // 直接调用adminAPI获取分类数据
                const functionUrl = `${apiUrl}/adminAPI`;
                const requestData = {
                    action: 'getCategoryList'
                };

                addLog(`📡 获取分类数据...`, 'info');

                const response = await fetch(functionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('✅ 分类数据获取成功！', 'success');
                    addLog(`📊 数据内容: ${JSON.stringify(result, null, 2)}`, 'info');
                    
                    if (result && result.data && Array.isArray(result.data)) {
                        addLog(`📋 找到 ${result.data.length} 个分类`, 'success');
                        addLog('🎯 数据操作测试通过！管理后台可以正常操作数据！', 'success');
                    } else {
                        addLog('⚠️ 数据格式异常，需要检查云函数', 'warning');
                    }
                } else {
                    const errorText = await response.text();
                    addLog(`⚠️ 数据获取失败: ${response.status}`, 'warning');
                    addLog(`📄 错误信息: ${errorText}`, 'warning');
                }
                
            } catch (error) {
                addLog(`❌ 数据操作测试失败: ${error.message}`, 'error');
                console.error('数据操作测试失败:', error);
            }
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            addLog('🌐 页面加载完成', 'info');
            addLog('💡 这个测试将绕过SDK，直接通过HTTP API连接云开发', 'info');
            addLog('🎯 如果这个测试成功，说明云开发环境正常，问题在于SDK加载', 'info');
        });
    </script>
</body>
</html>
