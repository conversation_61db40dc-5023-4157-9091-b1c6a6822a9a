/**
 * 网络请求优化管理器
 * 实现请求去重、缓存、并发控制和网络状态监控
 */

const RequestOptimizer = {
  // 请求缓存
  _requestCache: new Map(),
  // 正在进行的请求
  _pendingRequests: new Map(),
  // 请求队列
  _requestQueue: [],
  // 并发控制
  _maxConcurrent: 5,
  _activeRequests: 0,
  // 网络状态
  _networkStatus: {
    isConnected: true,
    networkType: 'unknown'
  },
  // 配置选项
  _options: {
    cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
    maxCacheSize: 100,
    retryCount: 3,
    retryDelay: 1000,
    timeout: 10000
  },

  /**
   * 初始化请求优化器
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    this._options = { ...this._options, ...options }
    console.log('🌐 网络请求优化器初始化')
    
    // 监听网络状态变化
    this.initNetworkMonitor()
    
    // 定期清理过期缓存
    this.startCacheCleanup()
  },

  /**
   * 初始化网络监控
   */
  initNetworkMonitor() {
    // 获取初始网络状态
    wx.getNetworkType({
      success: (res) => {
        this._networkStatus = {
          isConnected: res.networkType !== 'none',
          networkType: res.networkType
        }
        console.log('📶 网络状态:', this._networkStatus)
      }
    })

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      const wasConnected = this._networkStatus.isConnected
      this._networkStatus = {
        isConnected: res.isConnected,
        networkType: res.networkType
      }
      
      console.log('📶 网络状态变化:', this._networkStatus)
      
      // 网络恢复时处理队列中的请求
      if (!wasConnected && res.isConnected) {
        this.processQueuedRequests()
      }
    })
  },

  /**
   * 优化的云函数调用
   * @param {Object} params - 调用参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 调用结果
   */
  async callFunction(params, options = {}) {
    const {
      useCache = true,
      priority = 'normal',
      timeout = this._options.timeout,
      retryCount = this._options.retryCount
    } = options

    const requestKey = this.generateRequestKey(params)
    
    // 检查缓存
    if (useCache) {
      const cachedResult = this.getFromCache(requestKey)
      if (cachedResult) {
        console.log('📦 使用缓存结果:', requestKey)
        return cachedResult
      }
    }

    // 检查是否有相同的请求正在进行
    if (this._pendingRequests.has(requestKey)) {
      console.log('⏳ 等待相同请求完成:', requestKey)
      return await this._pendingRequests.get(requestKey)
    }

    // 检查网络状态
    if (!this._networkStatus.isConnected) {
      console.log('📵 网络未连接，请求加入队列:', requestKey)
      return await this.queueRequest(params, options)
    }

    // 检查并发限制
    if (this._activeRequests >= this._maxConcurrent) {
      console.log('🚦 达到并发限制，请求加入队列:', requestKey)
      return await this.queueRequest(params, options)
    }

    // 执行请求
    const requestPromise = this.executeRequest(params, { timeout, retryCount })
    this._pendingRequests.set(requestKey, requestPromise)

    try {
      const result = await requestPromise
      
      // 缓存结果
      if (useCache && result.success) {
        this.saveToCache(requestKey, result)
      }
      
      return result
    } finally {
      this._pendingRequests.delete(requestKey)
    }
  },

  /**
   * 执行请求
   * @param {Object} params - 请求参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 请求结果
   */
  async executeRequest(params, options = {}) {
    const { timeout, retryCount } = options
    
    this._activeRequests++
    
    try {
      for (let attempt = 0; attempt <= retryCount; attempt++) {
        try {
          console.log(`☁️ 执行云函数调用 (${attempt + 1}/${retryCount + 1}):`, params.name)
          
          const result = await Promise.race([
            wx.cloud.callFunction(params),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('请求超时')), timeout)
            )
          ])
          
          return result.result || result
        } catch (error) {
          console.warn(`⚠️ 请求失败 (${attempt + 1}/${retryCount + 1}):`, error.message)
          
          if (attempt === retryCount) {
            throw error
          }
          
          // 指数退避重试
          const delay = this._options.retryDelay * Math.pow(2, attempt)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    } finally {
      this._activeRequests--
      // 处理队列中的下一个请求
      this.processNextRequest()
    }
  },

  /**
   * 将请求加入队列
   * @param {Object} params - 请求参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 请求结果
   */
  async queueRequest(params, options = {}) {
    return new Promise((resolve, reject) => {
      const queueItem = {
        params,
        options,
        resolve,
        reject,
        timestamp: Date.now(),
        priority: options.priority || 'normal'
      }
      
      // 根据优先级插入队列
      if (options.priority === 'high') {
        this._requestQueue.unshift(queueItem)
      } else {
        this._requestQueue.push(queueItem)
      }
      
      console.log(`📋 请求已加入队列: ${params.name}, 队列长度: ${this._requestQueue.length}`)
    })
  },

  /**
   * 处理队列中的请求
   */
  async processQueuedRequests() {
    console.log(`🔄 开始处理队列请求: ${this._requestQueue.length} 个`)
    
    while (this._requestQueue.length > 0 && this._activeRequests < this._maxConcurrent) {
      const queueItem = this._requestQueue.shift()
      
      try {
        const result = await this.executeRequest(queueItem.params, queueItem.options)
        queueItem.resolve(result)
      } catch (error) {
        queueItem.reject(error)
      }
    }
  },

  /**
   * 处理下一个请求
   */
  processNextRequest() {
    if (this._requestQueue.length > 0 && this._activeRequests < this._maxConcurrent) {
      const queueItem = this._requestQueue.shift()
      
      this.executeRequest(queueItem.params, queueItem.options)
        .then(queueItem.resolve)
        .catch(queueItem.reject)
    }
  },

  /**
   * 生成请求键
   * @param {Object} params - 请求参数
   * @returns {string} 请求键
   */
  generateRequestKey(params) {
    const keyData = {
      name: params.name,
      data: params.data
    }
    return JSON.stringify(keyData)
  },

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存结果
   */
  getFromCache(key) {
    const cached = this._requestCache.get(key)
    if (!cached) {
      return null
    }
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this._options.cacheTimeout) {
      this._requestCache.delete(key)
      return null
    }
    
    // 更新访问时间
    cached.lastAccess = Date.now()
    return cached.data
  },

  /**
   * 保存到缓存
   * @param {string} key - 缓存键
   * @param {Object} data - 数据
   */
  saveToCache(key, data) {
    // 检查缓存大小
    if (this._requestCache.size >= this._options.maxCacheSize) {
      this.cleanOldestCache()
    }
    
    this._requestCache.set(key, {
      data,
      timestamp: Date.now(),
      lastAccess: Date.now()
    })
  },

  /**
   * 清理最旧的缓存
   */
  cleanOldestCache() {
    let oldestKey = null
    let oldestTime = Date.now()
    
    for (const [key, value] of this._requestCache.entries()) {
      if (value.lastAccess < oldestTime) {
        oldestTime = value.lastAccess
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this._requestCache.delete(oldestKey)
      console.log('🗑️ 清理旧缓存:', oldestKey)
    }
  },

  /**
   * 开始缓存清理
   */
  startCacheCleanup() {
    setInterval(() => {
      this.cleanExpiredCache()
    }, 60000) // 每分钟清理一次
  },

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now()
    let cleanedCount = 0
    
    for (const [key, value] of this._requestCache.entries()) {
      if (now - value.timestamp > this._options.cacheTimeout) {
        this._requestCache.delete(key)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🗑️ 清理过期缓存: ${cleanedCount} 个`)
    }
  },

  /**
   * 获取网络状态
   * @returns {Object} 网络状态
   */
  getNetworkStatus() {
    return { ...this._networkStatus }
  },

  /**
   * 获取请求统计
   * @returns {Object} 请求统计
   */
  getRequestStats() {
    return {
      activeRequests: this._activeRequests,
      queueLength: this._requestQueue.length,
      cacheSize: this._requestCache.size,
      pendingRequests: this._pendingRequests.size,
      networkStatus: this._networkStatus
    }
  },

  /**
   * 清空所有缓存和队列
   */
  clear() {
    this._requestCache.clear()
    this._requestQueue = []
    this._pendingRequests.clear()
    console.log('🗑️ 请求缓存和队列已清空')
  },

  /**
   * 销毁请求优化器
   */
  destroy() {
    this.clear()
    this._activeRequests = 0
    console.log('🗑️ 网络请求优化器已销毁')
  }
}

module.exports = {
  RequestOptimizer
}
