/**
 * 资源管理器
 * 统一管理定时器、观察器、监听器等资源，确保页面卸载时正确清理
 */

const ResourceManager = {
  // 页面资源映射
  _pageResources: new Map(), // pageId -> { timers: [], observers: [], listeners: [] }
  
  // 全局资源
  _globalResources: {
    timers: new Map(),      // timerId -> { type, callback, interval }
    observers: new Map(),   // observerId -> observer
    listeners: new Map()    // listenerId -> { element, event, callback }
  },

  /**
   * 初始化资源管理器
   */
  init() {
    console.log('🔧 资源管理器初始化')
    
    // 监听应用隐藏事件，清理资源
    if (typeof wx !== 'undefined') {
      wx.onAppHide(() => {
        console.log('📱 应用进入后台，清理非必要资源')
        this.cleanupNonEssentialResources()
      })
    }
  },

  /**
   * 为页面创建定时器
   * @param {string} pageId - 页面ID
   * @param {Function} callback - 回调函数
   * @param {number} delay - 延迟时间
   * @param {boolean} isInterval - 是否为间隔定时器
   * @returns {number} 定时器ID
   */
  createTimer(pageId, callback, delay, isInterval = false) {
    const timerId = isInterval ? 
      setInterval(callback, delay) : 
      setTimeout(callback, delay)
    
    // 记录定时器
    this.addPageResource(pageId, 'timers', {
      id: timerId,
      type: isInterval ? 'interval' : 'timeout',
      callback,
      delay,
      createTime: Date.now()
    })
    
    console.log(`⏰ 创建${isInterval ? '间隔' : '延时'}定时器: ${timerId} (页面: ${pageId})`)
    return timerId
  },

  /**
   * 清理定时器
   * @param {string} pageId - 页面ID
   * @param {number} timerId - 定时器ID
   */
  clearTimer(pageId, timerId) {
    const pageResources = this._pageResources.get(pageId)
    if (!pageResources) return

    const timerIndex = pageResources.timers.findIndex(t => t.id === timerId)
    if (timerIndex === -1) return

    const timer = pageResources.timers[timerIndex]
    
    // 清理定时器
    if (timer.type === 'interval') {
      clearInterval(timerId)
    } else {
      clearTimeout(timerId)
    }
    
    // 从记录中移除
    pageResources.timers.splice(timerIndex, 1)
    console.log(`🗑️ 清理定时器: ${timerId}`)
  },

  /**
   * 为页面创建观察器
   * @param {string} pageId - 页面ID
   * @param {string} type - 观察器类型
   * @param {Object} observer - 观察器实例
   * @returns {string} 观察器ID
   */
  createObserver(pageId, type, observer) {
    const observerId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    this.addPageResource(pageId, 'observers', {
      id: observerId,
      type,
      observer,
      createTime: Date.now()
    })
    
    console.log(`👁️ 创建观察器: ${observerId} (类型: ${type}, 页面: ${pageId})`)
    return observerId
  },

  /**
   * 清理观察器
   * @param {string} pageId - 页面ID
   * @param {string} observerId - 观察器ID
   */
  clearObserver(pageId, observerId) {
    const pageResources = this._pageResources.get(pageId)
    if (!pageResources) return

    const observerIndex = pageResources.observers.findIndex(o => o.id === observerId)
    if (observerIndex === -1) return

    const observerInfo = pageResources.observers[observerIndex]
    
    // 断开观察器
    if (observerInfo.observer && typeof observerInfo.observer.disconnect === 'function') {
      observerInfo.observer.disconnect()
    }
    
    // 从记录中移除
    pageResources.observers.splice(observerIndex, 1)
    console.log(`🗑️ 清理观察器: ${observerId}`)
  },

  /**
   * 为页面添加事件监听器
   * @param {string} pageId - 页面ID
   * @param {Object} element - DOM元素或对象
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {string} 监听器ID
   */
  addEventListener(pageId, element, event, callback) {
    const listenerId = `${event}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // 添加事件监听器
    if (element && typeof element.addEventListener === 'function') {
      element.addEventListener(event, callback)
    } else if (element && typeof element.on === 'function') {
      element.on(event, callback)
    }
    
    this.addPageResource(pageId, 'listeners', {
      id: listenerId,
      element,
      event,
      callback,
      createTime: Date.now()
    })
    
    console.log(`📡 添加事件监听器: ${listenerId} (事件: ${event}, 页面: ${pageId})`)
    return listenerId
  },

  /**
   * 移除事件监听器
   * @param {string} pageId - 页面ID
   * @param {string} listenerId - 监听器ID
   */
  removeEventListener(pageId, listenerId) {
    const pageResources = this._pageResources.get(pageId)
    if (!pageResources) return

    const listenerIndex = pageResources.listeners.findIndex(l => l.id === listenerId)
    if (listenerIndex === -1) return

    const listenerInfo = pageResources.listeners[listenerIndex]
    
    // 移除事件监听器
    if (listenerInfo.element && typeof listenerInfo.element.removeEventListener === 'function') {
      listenerInfo.element.removeEventListener(listenerInfo.event, listenerInfo.callback)
    } else if (listenerInfo.element && typeof listenerInfo.element.off === 'function') {
      listenerInfo.element.off(listenerInfo.event, listenerInfo.callback)
    }
    
    // 从记录中移除
    pageResources.listeners.splice(listenerIndex, 1)
    console.log(`🗑️ 移除事件监听器: ${listenerId}`)
  },

  /**
   * 添加页面资源
   * @param {string} pageId - 页面ID
   * @param {string} type - 资源类型
   * @param {Object} resource - 资源信息
   */
  addPageResource(pageId, type, resource) {
    if (!this._pageResources.has(pageId)) {
      this._pageResources.set(pageId, {
        timers: [],
        observers: [],
        listeners: []
      })
    }
    
    const pageResources = this._pageResources.get(pageId)
    pageResources[type].push(resource)
  },

  /**
   * 清理页面所有资源
   * @param {string} pageId - 页面ID
   */
  cleanupPageResources(pageId) {
    const pageResources = this._pageResources.get(pageId)
    if (!pageResources) return

    let cleanedCount = 0

    // 清理定时器
    pageResources.timers.forEach(timer => {
      if (timer.type === 'interval') {
        clearInterval(timer.id)
      } else {
        clearTimeout(timer.id)
      }
      cleanedCount++
    })

    // 清理观察器
    pageResources.observers.forEach(observerInfo => {
      if (observerInfo.observer && typeof observerInfo.observer.disconnect === 'function') {
        observerInfo.observer.disconnect()
      }
      cleanedCount++
    })

    // 清理事件监听器
    pageResources.listeners.forEach(listenerInfo => {
      if (listenerInfo.element && typeof listenerInfo.element.removeEventListener === 'function') {
        listenerInfo.element.removeEventListener(listenerInfo.event, listenerInfo.callback)
      } else if (listenerInfo.element && typeof listenerInfo.element.off === 'function') {
        listenerInfo.element.off(listenerInfo.event, listenerInfo.callback)
      }
      cleanedCount++
    })

    // 移除页面资源记录
    this._pageResources.delete(pageId)
    console.log(`🧹 清理页面 ${pageId} 的 ${cleanedCount} 个资源`)
  },

  /**
   * 清理非必要资源（应用进入后台时）
   */
  cleanupNonEssentialResources() {
    let cleanedCount = 0
    
    // 清理长时间未使用的页面资源
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5分钟
    
    for (const [pageId, resources] of this._pageResources.entries()) {
      let hasOldResources = false
      
      // 检查是否有超时的资源
      const allResources = [
        ...resources.timers,
        ...resources.observers,
        ...resources.listeners
      ]
      
      for (const resource of allResources) {
        if (now - resource.createTime > maxAge) {
          hasOldResources = true
          break
        }
      }
      
      if (hasOldResources) {
        this.cleanupPageResources(pageId)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个页面的过期资源`)
    }
  },

  /**
   * 获取资源统计信息
   */
  getResourceStats() {
    const stats = {
      totalPages: this._pageResources.size,
      totalTimers: 0,
      totalObservers: 0,
      totalListeners: 0,
      pages: {}
    }
    
    for (const [pageId, resources] of this._pageResources.entries()) {
      stats.totalTimers += resources.timers.length
      stats.totalObservers += resources.observers.length
      stats.totalListeners += resources.listeners.length
      
      stats.pages[pageId] = {
        timers: resources.timers.length,
        observers: resources.observers.length,
        listeners: resources.listeners.length
      }
    }
    
    return stats
  }
}

module.exports = ResourceManager
