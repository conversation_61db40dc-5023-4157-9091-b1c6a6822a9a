# API接口规范文档

## 1. 接口概述

### 1.1 基本信息
- **Base URL**: `https://api.emoji-admin.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 认证方式
使用JWT Bearer Token认证：
```
Authorization: Bearer <JWT_TOKEN>
```

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_xxxxxxxxxx"
}
```

### 1.4 错误码定义
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 重新登录 |
| 403 | 权限不足 | 联系管理员 |
| 404 | 资源不存在 | 检查资源ID |
| 429 | 请求频率限制 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系技术支持 |

## 2. 认证接口

### 2.1 管理员登录
**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "captcha_xxx"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_xxx",
    "expiresIn": 7200,
    "user": {
      "id": "admin_001",
      "username": "admin",
      "nickname": "管理员",
      "avatar": "https://cdn.example.com/avatar.jpg",
      "role": "admin",
      "permissions": ["emoji:read", "emoji:write", "user:read"]
    }
  }
}
```

### 2.2 刷新Token
**接口**: `POST /auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "refresh_token_xxx"
}
```

### 2.3 退出登录
**接口**: `POST /auth/logout`

## 3. 表情包管理接口

### 3.1 获取表情包列表
**接口**: `GET /emojis`

**查询参数**:
```
page=1              # 页码，默认1
limit=20            # 每页数量，默认20，最大100
keyword=搞笑        # 搜索关键词
category=funny      # 分类筛选
status=published    # 状态筛选：published/draft/archived
sortBy=createTime   # 排序字段：createTime/likes/downloads
sortOrder=desc      # 排序方向：asc/desc
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "emoji_001",
      "title": "哈哈哈笑死我了",
      "description": "超级搞笑的表情包",
      "imageUrl": "https://cdn.example.com/emoji_001.jpg",
      "thumbnailUrl": "https://cdn.example.com/emoji_001_thumb.jpg",
      "category": {
        "id": "funny",
        "name": "搞笑幽默",
        "color": "#FF6B6B"
      },
      "tags": ["搞笑", "哈哈", "笑死"],
      "fileSize": 245760,
      "dimensions": {
        "width": 500,
        "height": 500
      },
      "stats": {
        "views": 25680,
        "likes": 12340,
        "downloads": 3702,
        "collections": 1856
      },
      "status": "published",
      "sortOrder": 0,
      "createdBy": {
        "id": "admin_001",
        "nickname": "管理员"
      },
      "createdAt": "2024-01-15T00:00:00Z",
      "updatedAt": "2024-01-15T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 26,
    "totalPages": 2
  }
}
```

### 3.2 获取表情包详情
**接口**: `GET /emojis/:id`

**路径参数**:
- `id`: 表情包ID

**响应数据**: 同列表接口中的单个表情包对象

### 3.3 创建表情包
**接口**: `POST /emojis`

**请求参数**:
```json
{
  "title": "新表情包",
  "description": "表情包描述",
  "categoryId": "funny",
  "tags": ["搞笑", "新年"],
  "imageUrl": "https://cdn.example.com/new_emoji.jpg",
  "status": "published",
  "sortOrder": 0
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "emoji_new",
    // ... 完整表情包信息
  }
}
```

### 3.4 更新表情包
**接口**: `PUT /emojis/:id`

**请求参数**: 同创建接口，所有字段可选

### 3.5 删除表情包
**接口**: `DELETE /emojis/:id`

**响应数据**:
```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 3.6 批量操作表情包
**接口**: `POST /emojis/batch`

**请求参数**:
```json
{
  "action": "delete",  // delete/updateStatus/updateCategory
  "ids": ["emoji_001", "emoji_002"],
  "data": {
    "status": "archived",
    "categoryId": "funny"
  }
}
```

### 3.7 上传表情包图片
**接口**: `POST /emojis/upload`

**请求格式**: `multipart/form-data`

**请求参数**:
- `file`: 图片文件（必填）
- `type`: 上传类型，image/thumbnail（可选，默认image）

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://cdn.example.com/emoji_xxx.jpg",
    "thumbnailUrl": "https://cdn.example.com/emoji_xxx_thumb.jpg",
    "fileSize": 245760,
    "dimensions": {
      "width": 500,
      "height": 500
    }
  }
}
```

## 4. 分类管理接口

### 4.1 获取分类列表
**接口**: `GET /categories`

**查询参数**:
```
includeCount=true   # 是否包含表情包数量统计
parentId=null       # 父分类ID，null获取顶级分类
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "funny",
      "name": "搞笑幽默",
      "slug": "funny",
      "icon": "😂",
      "color": "#FF6B6B",
      "description": "搞笑幽默类表情包",
      "parentId": null,
      "sortOrder": 1,
      "status": "active",
      "emojiCount": 5,
      "children": [
        {
          "id": "funny_animal",
          "name": "沙雕萌宠",
          "emojiCount": 2
        }
      ],
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 4.2 创建分类
**接口**: `POST /categories`

**请求参数**:
```json
{
  "name": "新分类",
  "slug": "new-category",
  "icon": "🎉",
  "color": "#FF6B6B",
  "description": "分类描述",
  "parentId": null,
  "sortOrder": 1,
  "status": "active"
}
```

### 4.3 更新分类
**接口**: `PUT /categories/:id`

### 4.4 删除分类
**接口**: `DELETE /categories/:id`

**注意**: 只能删除没有表情包的分类

### 4.5 分类排序
**接口**: `PUT /categories/sort`

**请求参数**:
```json
{
  "categories": [
    {"id": "funny", "sortOrder": 1},
    {"id": "cute", "sortOrder": 2}
  ]
}
```

## 5. 轮播图管理接口

### 5.1 获取轮播图列表
**接口**: `GET /banners`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "banner_001",
      "title": "新年表情包",
      "subtitle": "龙年大吉，表情包拜年",
      "image": "https://cdn.example.com/banner_001.jpg",
      "linkType": "category",
      "linkValue": "/category/节日庆典",
      "buttonText": "点击查看详情",
      "sortOrder": 1,
      "status": "active",
      "startTime": "2024-01-01T00:00:00Z",
      "endTime": "2024-02-01T00:00:00Z",
      "clickCount": 1250,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 5.2 创建轮播图
**接口**: `POST /banners`

### 5.3 更新轮播图
**接口**: `PUT /banners/:id`

### 5.4 删除轮播图
**接口**: `DELETE /banners/:id`

## 6. 用户管理接口

### 6.1 获取用户列表
**接口**: `GET /users`

**查询参数**:
```
page=1
limit=20
keyword=张三        # 搜索昵称、邮箱
role=admin          # 角色筛选
status=active       # 状态筛选
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "user_001",
      "openid": "oxxxxxxxxxxxxxxxxxxxxxx",
      "unionid": "uxxxxxxxxxxxxxxxxxxxxxx",
      "nickname": "张三",
      "avatar": "https://cdn.example.com/avatar_001.jpg",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "role": "admin",
      "status": "active",
      "permissions": ["emoji:read", "emoji:write"],
      "stats": {
        "likeCount": 156,
        "collectCount": 89,
        "downloadCount": 234,
        "loginCount": 45
      },
      "lastLoginAt": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 6.2 获取用户详情
**接口**: `GET /users/:id`

### 6.3 更新用户状态
**接口**: `PUT /users/:id/status`

**请求参数**:
```json
{
  "status": "inactive",  // active/inactive/banned
  "reason": "违规操作"
}
```

### 6.4 更新用户角色
**接口**: `PUT /users/:id/role`

**请求参数**:
```json
{
  "role": "admin",
  "permissions": ["emoji:read", "emoji:write", "user:read"]
}
```

## 7. 数据统计接口

### 7.1 概览统计
**接口**: `GET /stats/overview`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalEmojis": 26,
    "totalCategories": 6,
    "totalUsers": 1250,
    "totalDownloads": 209388,
    "dailyActiveUsers": 450,
    "weeklyActiveUsers": 1200,
    "monthlyActiveUsers": 3500,
    "todayDownloads": 1250,
    "todayUploads": 5,
    "averageSessionTime": 180
  }
}
```

### 7.2 趋势数据
**接口**: `GET /stats/trends`

**查询参数**:
```
type=users          # 数据类型：users/downloads/uploads
period=7d           # 时间周期：1d/7d/30d/90d
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "labels": ["2024-01-09", "2024-01-10", "2024-01-11"],
    "datasets": [
      {
        "label": "新增用户",
        "data": [45, 52, 38]
      },
      {
        "label": "活跃用户",
        "data": [120, 135, 98]
      }
    ]
  }
}
```

### 7.3 热门内容
**接口**: `GET /stats/popular`

**查询参数**:
```
type=emojis         # 类型：emojis/categories/keywords
period=7d           # 时间周期
limit=10            # 返回数量
```

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "emoji_001",
      "title": "哈哈哈笑死我了",
      "thumbnail": "https://cdn.example.com/emoji_001_thumb.jpg",
      "stats": {
        "downloads": 1250,
        "likes": 890,
        "views": 5600
      },
      "rank": 1,
      "change": "+2"  // 排名变化
    }
  ]
}
```

### 7.4 用户统计
**接口**: `GET /stats/users`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "roleDistribution": [
      {"role": "admin", "count": 2, "percentage": 0.16},
      {"role": "user", "count": 1248, "percentage": 99.84}
    ],
    "statusDistribution": [
      {"status": "active", "count": 1200, "percentage": 96},
      {"status": "inactive", "count": 50, "percentage": 4}
    ],
    "registrationTrend": {
      "labels": ["1月", "2月", "3月", "4月", "5月"],
      "data": [200, 350, 280, 420, 380]
    },
    "retentionRate": {
      "day1": 0.75,
      "day7": 0.45,
      "day30": 0.25
    }
  }
}
```

## 8. 系统管理接口

### 8.1 获取系统配置
**接口**: `GET /system/config`

### 8.2 更新系统配置
**接口**: `PUT /system/config`

### 8.3 获取操作日志
**接口**: `GET /system/logs`

**查询参数**:
```
page=1
limit=50
level=info          # 日志级别：debug/info/warn/error
module=emoji        # 模块：emoji/user/category/system
startTime=2024-01-01T00:00:00Z
endTime=2024-01-15T23:59:59Z
```

### 8.4 系统健康检查
**接口**: `GET /system/health`

**响应数据**:
```json
{
  "code": 200,
  "message": "系统运行正常",
  "data": {
    "status": "healthy",
    "uptime": 86400,
    "version": "1.0.0",
    "database": {
      "status": "connected",
      "responseTime": 5
    },
    "redis": {
      "status": "connected",
      "responseTime": 2
    },
    "storage": {
      "status": "available",
      "freeSpace": "500GB"
    }
  }
}
```

## 9. 错误处理

### 9.1 参数验证错误
```json
{
  "code": 400,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "title",
      "message": "标题不能为空"
    },
    {
      "field": "categoryId",
      "message": "分类ID格式错误"
    }
  ]
}
```

### 9.2 业务逻辑错误
```json
{
  "code": 422,
  "message": "无法删除包含表情包的分类",
  "data": {
    "categoryId": "funny",
    "emojiCount": 5
  }
}
```

### 9.3 权限错误
```json
{
  "code": 403,
  "message": "权限不足",
  "data": {
    "required": "emoji:write",
    "current": ["emoji:read"]
  }
}
```

## 10. 请求限制

### 10.1 频率限制
- 普通接口：100次/分钟
- 上传接口：10次/分钟
- 批量操作：5次/分钟

### 10.2 文件上传限制
- 单文件大小：10MB
- 支持格式：jpg, png, gif, webp
- 图片尺寸：最大4096x4096

### 10.3 批量操作限制
- 批量删除：最多100个
- 批量更新：最多50个

---

**文档版本**: v1.0  
**最后更新**: 2024-01-15