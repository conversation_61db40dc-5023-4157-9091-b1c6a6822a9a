<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据状态检查</title>
    <script src="https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@2.17.5/dist/index.umd.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 数据状态检查</h1>
    
    <div class="section">
        <h2>📊 数据库连接状态</h2>
        <div id="connection-status"></div>
        <button onclick="checkConnection()">检查连接</button>
    </div>

    <div class="section">
        <h2>📁 数据集合状态</h2>
        <div id="collections-status"></div>
        <button onclick="checkCollections()">检查集合</button>
    </div>

    <div class="section">
        <h2>📋 分类数据</h2>
        <div id="categories-data"></div>
        <button onclick="checkCategories()">检查分类</button>
    </div>

    <div class="section">
        <h2>😊 表情包数据</h2>
        <div id="emojis-data"></div>
        <button onclick="checkEmojis()">检查表情包</button>
    </div>

    <div class="section">
        <h2>🔧 数据修复工具</h2>
        <div id="repair-status"></div>
        <button onclick="initializeData()">初始化测试数据</button>
        <button onclick="repairData()">修复数据结构</button>
    </div>

    <script>
        let app = null;

        // 初始化云开发
        async function initCloudbase() {
            try {
                console.log('🚀 初始化云开发...');
                app = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142'
                });

                // 匿名登录
                const auth = app.auth();
                await auth.signInAnonymously();
                console.log('✅ 匿名登录成功');
                
                return true;
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                return false;
            }
        }

        // 检查连接
        async function checkConnection() {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在检查连接...</div>';

            try {
                if (!app) {
                    const initResult = await initCloudbase();
                    if (!initResult) {
                        throw new Error('云开发初始化失败');
                    }
                }

                // 测试数据库连接
                const db = app.database();
                const result = await db.collection('categories').limit(1).get();
                
                statusDiv.innerHTML = `
                    <div class="success">✅ 数据库连接成功</div>
                    <div class="info">📊 连接信息: ${JSON.stringify({
                        total: result.total,
                        data_length: result.data.length
                    })}</div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error">❌ 连接失败: ${error.message}</div>
                `;
            }
        }

        // 检查集合
        async function checkCollections() {
            const statusDiv = document.getElementById('collections-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在检查集合...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const db = app.database();
                const collections = ['categories', 'emojis', 'users', 'operation_logs'];
                let results = [];

                for (const collectionName of collections) {
                    try {
                        const result = await db.collection(collectionName).limit(1).get();
                        results.push({
                            name: collectionName,
                            status: '✅ 存在',
                            count: result.total || 0
                        });
                    } catch (error) {
                        results.push({
                            name: collectionName,
                            status: '❌ 不存在或无权限',
                            count: 0,
                            error: error.message
                        });
                    }
                }

                let html = '<table class="data-table"><tr><th>集合名</th><th>状态</th><th>数据量</th><th>备注</th></tr>';
                results.forEach(item => {
                    html += `<tr>
                        <td>${item.name}</td>
                        <td>${item.status}</td>
                        <td>${item.count}</td>
                        <td>${item.error || '-'}</td>
                    </tr>`;
                });
                html += '</table>';

                statusDiv.innerHTML = html;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }

        // 检查分类数据
        async function checkCategories() {
            const statusDiv = document.getElementById('categories-data');
            statusDiv.innerHTML = '<div class="info">🔄 正在检查分类数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const db = app.database();
                const result = await db.collection('categories').get();
                
                if (result.data.length === 0) {
                    statusDiv.innerHTML = `
                        <div class="warning">⚠️ 分类数据为空</div>
                        <div class="info">建议点击"初始化测试数据"按钮创建默认分类</div>
                    `;
                } else {
                    let html = `<div class="success">✅ 找到 ${result.data.length} 个分类</div>`;
                    html += '<table class="data-table"><tr><th>ID</th><th>名称</th><th>描述</th><th>创建时间</th></tr>';
                    result.data.forEach(item => {
                        html += `<tr>
                            <td>${item._id}</td>
                            <td>${item.name}</td>
                            <td>${item.description || '-'}</td>
                            <td>${item.createTime || '-'}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    statusDiv.innerHTML = html;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }

        // 检查表情包数据
        async function checkEmojis() {
            const statusDiv = document.getElementById('emojis-data');
            statusDiv.innerHTML = '<div class="info">🔄 正在检查表情包数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                const db = app.database();
                const result = await db.collection('emojis').limit(10).get();
                
                if (result.data.length === 0) {
                    statusDiv.innerHTML = `
                        <div class="warning">⚠️ 表情包数据为空</div>
                        <div class="info">建议点击"初始化测试数据"按钮创建测试表情包</div>
                    `;
                } else {
                    let html = `<div class="success">✅ 找到 ${result.total} 个表情包（显示前10个）</div>`;
                    html += '<table class="data-table"><tr><th>ID</th><th>名称</th><th>分类</th><th>URL</th><th>创建时间</th></tr>';
                    result.data.forEach(item => {
                        html += `<tr>
                            <td>${item._id}</td>
                            <td>${item.name}</td>
                            <td>${item.categoryId}</td>
                            <td>${item.url ? '✅' : '❌'}</td>
                            <td>${item.createTime || '-'}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    statusDiv.innerHTML = html;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }

        // 初始化测试数据
        async function initializeData() {
            const statusDiv = document.getElementById('repair-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在初始化测试数据...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                // 使用webAdminAPI云函数初始化数据
                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'initializeTestData',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 测试数据初始化成功</div>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 初始化失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 初始化失败: ${error.message}</div>`;
            }
        }

        // 修复数据
        async function repairData() {
            const statusDiv = document.getElementById('repair-status');
            statusDiv.innerHTML = '<div class="info">🔄 正在修复数据结构...</div>';

            try {
                if (!app) {
                    await initCloudbase();
                }

                // 使用webAdminAPI云函数修复数据
                const result = await app.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'repairData',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    statusDiv.innerHTML = `
                        <div class="success">✅ 数据修复成功</div>
                        <pre>${JSON.stringify(result.result, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="error">❌ 修复失败: ${result.result?.error || '未知错误'}</div>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 修复失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动检查连接
        window.addEventListener('load', function() {
            setTimeout(checkConnection, 1000);
        });
    </script>
</body>
</html>
