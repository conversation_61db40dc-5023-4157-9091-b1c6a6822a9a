/**
 * 离线模式测试页面
 * 用于验证小程序在云函数不可用时的降级功能
 */

const { DataManager } = require('../../utils/newDataManager.js')
const { StateManager } = require('../../utils/stateManager.js')

Page({
  data: {
    testResults: [],
    testing: false,
    cloudStatus: 'unknown'
  },

  onLoad() {
    console.log('🧪 离线模式测试页面加载')
    this.checkCloudStatus()
  },

  /**
   * 检查云开发状态
   */
  async checkCloudStatus() {
    try {
      const app = getApp()
      const cloudInitialized = app.globalData.cloudInitialized
      
      this.setData({
        cloudStatus: cloudInitialized ? 'connected' : 'offline'
      })
      
      console.log('☁️ 云开发状态:', cloudInitialized ? '已连接' : '离线模式')
    } catch (error) {
      this.setData({
        cloudStatus: 'error'
      })
    }
  },

  /**
   * 运行所有测试
   */
  async runAllTests() {
    this.setData({
      testing: true,
      testResults: []
    })

    const tests = [
      { name: '获取表情包列表', method: this.testGetEmojis },
      { name: '搜索表情包', method: this.testSearchEmojis },
      { name: '获取分类列表', method: this.testGetCategories },
      { name: '获取横幅数据', method: this.testGetBanners },
      { name: '状态管理测试', method: this.testStateManager }
    ]

    for (const test of tests) {
      try {
        console.log(`🧪 开始测试: ${test.name}`)
        const startTime = Date.now()
        
        const result = await test.method.call(this)
        const duration = Date.now() - startTime
        
        this.addTestResult({
          name: test.name,
          status: 'success',
          duration,
          message: result.message || '测试通过'
        })
        
      } catch (error) {
        this.addTestResult({
          name: test.name,
          status: 'error',
          duration: 0,
          message: error.message
        })
      }
    }

    this.setData({
      testing: false
    })

    wx.showToast({
      title: '测试完成',
      icon: 'success'
    })
  },

  /**
   * 添加测试结果
   */
  addTestResult(result) {
    const results = [...this.data.testResults, result]
    this.setData({
      testResults: results
    })
  },

  /**
   * 测试获取表情包列表
   */
  async testGetEmojis() {
    const result = await DataManager.getEmojis('all', 1, 10)
    
    if (!result || !result.data || !Array.isArray(result.data)) {
      throw new Error('获取表情包列表失败')
    }
    
    return {
      message: `成功获取 ${result.data.length} 个表情包`
    }
  },

  /**
   * 测试搜索表情包
   */
  async testSearchEmojis() {
    const result = await DataManager.searchEmojis('开心', 1, 5)
    
    if (!result || !result.data || !Array.isArray(result.data)) {
      throw new Error('搜索表情包失败')
    }
    
    return {
      message: `搜索到 ${result.data.length} 个相关表情包`
    }
  },

  /**
   * 测试获取分类列表
   */
  async testGetCategories() {
    const result = await DataManager.getCategories()
    
    if (!result || !Array.isArray(result)) {
      throw new Error('获取分类列表失败')
    }
    
    return {
      message: `成功获取 ${result.length} 个分类`
    }
  },

  /**
   * 测试获取横幅数据
   */
  async testGetBanners() {
    const result = await DataManager.getBanners()
    
    if (!result || !Array.isArray(result)) {
      throw new Error('获取横幅数据失败')
    }
    
    return {
      message: `成功获取 ${result.length} 个横幅`
    }
  },

  /**
   * 测试状态管理
   */
  async testStateManager() {
    const testEmojiId = 'test_offline_emoji'
    
    // 测试点赞
    StateManager.toggleLike(testEmojiId)
    const likeState = StateManager.isLiked(testEmojiId)
    
    // 测试收藏
    StateManager.toggleCollect(testEmojiId)
    const collectState = StateManager.isCollected(testEmojiId)
    
    // 测试状态获取
    const emojiState = StateManager.getEmojiState(testEmojiId)
    
    if (!likeState || !collectState || !emojiState.isLiked || !emojiState.isCollected) {
      throw new Error('状态管理功能异常')
    }
    
    return {
      message: '状态管理功能正常'
    }
  },

  /**
   * 清除测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    })
  },

  /**
   * 返回首页
   */
  goToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
