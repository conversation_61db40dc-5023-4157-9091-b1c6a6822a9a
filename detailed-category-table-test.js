// 详细测试分类表格渲染
const { chromium } = require('playwright');

async function detailedCategoryTableTest() {
    console.log('🔍 详细测试分类表格渲染...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类') || text.includes('renderCategoryTable')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 详细检查表格内容');
        
        // 检查表格的详细内容
        const tableAnalysis = await page.evaluate(() => {
            const table = document.querySelector('#category-table');
            const tbody = document.querySelector('#category-tbody');
            
            if (!table || !tbody) {
                return {
                    tableExists: !!table,
                    tbodyExists: !!tbody,
                    error: '表格或表格体不存在'
                };
            }
            
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const rowsData = rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                return {
                    index: index + 1,
                    cellCount: cells.length,
                    cellContents: cells.map(cell => cell.textContent?.trim()).slice(0, 8), // 前8列
                    hasSystemStatus: cells.some(cell => 
                        cell.textContent?.includes('服务器状态') || 
                        cell.textContent?.includes('数据库连接') ||
                        cell.textContent?.includes('运行中')
                    ),
                    hasCategoryData: cells.some(cell => 
                        cell.textContent?.includes('测试2') || 
                        cell.textContent?.includes('1')
                    ),
                    hasGradientPreview: !!row.querySelector('div[style*="background"]'),
                    hasActionButtons: !!row.querySelector('.action-buttons')
                };
            });
            
            return {
                tableExists: true,
                tbodyExists: true,
                rowCount: rows.length,
                rowsData: rowsData,
                tableHTML: table.outerHTML.substring(0, 1000) // 前1000字符
            };
        });
        
        console.log('📊 表格分析结果:');
        console.log('表格存在:', tableAnalysis.tableExists);
        console.log('表格体存在:', tableAnalysis.tbodyExists);
        console.log('行数:', tableAnalysis.rowCount);
        
        if (tableAnalysis.error) {
            console.log('❌ 错误:', tableAnalysis.error);
        } else {
            console.log('\n📋 表格行详细分析:');
            tableAnalysis.rowsData.forEach(row => {
                console.log(`\n行 ${row.index}:`);
                console.log(`  单元格数量: ${row.cellCount}`);
                console.log(`  内容: [${row.cellContents.join(', ')}]`);
                console.log(`  包含系统状态: ${row.hasSystemStatus}`);
                console.log(`  包含分类数据: ${row.hasCategoryData}`);
                console.log(`  有渐变预览: ${row.hasGradientPreview}`);
                console.log(`  有操作按钮: ${row.hasActionButtons}`);
                
                if (row.hasSystemStatus) {
                    console.log('  🔴 这一行显示的是系统状态数据！');
                } else if (row.hasCategoryData) {
                    console.log('  ✅ 这一行显示的是分类数据');
                } else {
                    console.log('  ⚠️ 这一行的数据类型不明确');
                }
            });
            
            console.log('\n📋 表格HTML预览:');
            console.log(tableAnalysis.tableHTML);
        }
        
        console.log('\n📍 检查AdminApp数据和实际渲染的一致性');
        
        // 检查数据一致性
        const dataConsistency = await page.evaluate(() => {
            const adminCategories = AdminApp.data.categories || [];
            const tableRows = Array.from(document.querySelectorAll('#category-tbody tr'));
            
            return {
                adminCategoriesCount: adminCategories.length,
                tableRowsCount: tableRows.length,
                adminCategoriesNames: adminCategories.map(c => c.name),
                tableFirstRowContent: tableRows.length > 0 ? 
                    Array.from(tableRows[0].querySelectorAll('td')).map(td => td.textContent?.trim()).slice(0, 5) : []
            };
        });
        
        console.log('📊 数据一致性检查:');
        console.log('AdminApp分类数量:', dataConsistency.adminCategoriesCount);
        console.log('表格行数量:', dataConsistency.tableRowsCount);
        console.log('AdminApp分类名称:', dataConsistency.adminCategoriesNames);
        console.log('表格第一行内容:', dataConsistency.tableFirstRowContent);
        
        if (dataConsistency.adminCategoriesCount !== dataConsistency.tableRowsCount) {
            console.log('🔴 数据不一致：AdminApp中的分类数量与表格行数不匹配');
        } else {
            console.log('✅ 数据数量一致');
        }
        
        // 截图
        await page.screenshot({ path: 'detailed-category-table-test.png', fullPage: true });
        console.log('\n📸 详细测试截图已保存: detailed-category-table-test.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            tableExists: tableAnalysis.tableExists,
            rowCount: tableAnalysis.rowCount,
            hasSystemStatusData: tableAnalysis.rowsData.some(row => row.hasSystemStatus),
            hasCategoryData: tableAnalysis.rowsData.some(row => row.hasCategoryData)
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'category-table-test-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
detailedCategoryTableTest().then(result => {
    console.log('\n🎯 详细测试结果:', result);
}).catch(console.error);
