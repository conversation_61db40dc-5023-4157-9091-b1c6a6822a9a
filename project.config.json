{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": false, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "lazyCodeLoading": "requiredComponents"}, "compileType": "miniprogram", "libVersion": "3.8.11", "appid": "wxa343fb2b31f727a4", "projectname": "表情包小程序", "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "simulatorPluginLibVersion": {}, "editorSetting": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloudfunctionRoot": "cloudfunctions/", "cloudenv": "cloud1-5g6pvnpl88dc0142"}