<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查数据库标签数据</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; color: #856404; }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .test-button:hover {
            opacity: 0.9;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .tags-cell {
            max-width: 200px;
            word-wrap: break-word;
        }
        
        .tag-item {
            display: inline-block;
            background: #e9ecef;
            padding: 2px 6px;
            margin: 2px;
            border-radius: 4px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 检查数据库标签数据</h1>
        
        <div class="test-section">
            <div class="test-title">测试控制</div>
            <button class="test-button" onclick="checkDatabaseTags()">检查数据库标签</button>
            <button class="test-button" onclick="addTestTagsData()">添加测试标签数据</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">检查结果</div>
            <div id="testLog" class="log">等待检查...</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">表情包标签数据</div>
            <div id="dataDisplay"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.innerHTML += `<span class="${type}">${logEntry}</span>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空...\n';
            document.getElementById('dataDisplay').innerHTML = '';
        }

        async function checkDatabaseTags() {
            log('🔍 开始检查数据库中的标签数据...', 'info');
            
            try {
                // 模拟云函数调用检查数据库
                log('☁️ 模拟调用云函数获取表情包数据...', 'info');
                
                // 这里应该是真实的云函数调用，现在用模拟数据
                const mockData = [
                    {
                        _id: 'emoji_001',
                        title: '搞笑表情包1',
                        category: '搞笑幽默',
                        tags: ['搞笑', '幽默', '表情'],
                        status: 'published',
                        createTime: new Date().toISOString()
                    },
                    {
                        _id: 'emoji_002',
                        title: '可爱表情包2',
                        category: '可爱萌宠',
                        tags: ['可爱', '萌宠', '小动物'],
                        status: 'published',
                        createTime: new Date().toISOString()
                    },
                    {
                        _id: 'emoji_003',
                        title: '无标签表情包',
                        category: '其他',
                        tags: [],
                        status: 'published',
                        createTime: new Date().toISOString()
                    },
                    {
                        _id: 'emoji_004',
                        title: '空标签表情包',
                        category: '其他',
                        tags: null,
                        status: 'published',
                        createTime: new Date().toISOString()
                    }
                ];
                
                log(`✅ 获取到 ${mockData.length} 个表情包数据`, 'success');
                
                // 分析标签数据
                let hasTagsCount = 0;
                let emptyTagsCount = 0;
                let nullTagsCount = 0;
                
                mockData.forEach(emoji => {
                    if (emoji.tags && Array.isArray(emoji.tags) && emoji.tags.length > 0) {
                        hasTagsCount++;
                    } else if (emoji.tags && Array.isArray(emoji.tags) && emoji.tags.length === 0) {
                        emptyTagsCount++;
                    } else {
                        nullTagsCount++;
                    }
                });
                
                log(`📊 标签数据统计:`, 'info');
                log(`  - 有标签的表情包: ${hasTagsCount} 个`, 'success');
                log(`  - 空标签数组的表情包: ${emptyTagsCount} 个`, 'warning');
                log(`  - 无标签字段的表情包: ${nullTagsCount} 个`, 'error');
                
                // 显示详细数据表格
                displayDataTable(mockData);
                
                // 检查条件渲染逻辑
                log('\n🔍 检查条件渲染逻辑:', 'info');
                mockData.forEach((emoji, index) => {
                    const condition = emoji.tags && emoji.tags.length > 0;
                    log(`  表情包${index + 1}: wx:if条件 = ${condition}`, condition ? 'success' : 'warning');
                });
                
            } catch (error) {
                log(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        function displayDataTable(data) {
            const displayElement = document.getElementById('dataDisplay');
            
            let html = '<table class="data-table">';
            html += '<tr><th>ID</th><th>标题</th><th>分类</th><th>标签</th><th>标签数量</th><th>条件满足</th></tr>';
            
            data.forEach(emoji => {
                const tagsDisplay = emoji.tags && Array.isArray(emoji.tags) 
                    ? emoji.tags.map(tag => `<span class="tag-item">${tag}</span>`).join('')
                    : (emoji.tags === null ? '(null)' : '(空数组)');
                
                const tagsCount = emoji.tags && Array.isArray(emoji.tags) ? emoji.tags.length : 0;
                const condition = emoji.tags && emoji.tags.length > 0;
                
                html += `<tr>
                    <td>${emoji._id}</td>
                    <td>${emoji.title}</td>
                    <td>${emoji.category}</td>
                    <td class="tags-cell">${tagsDisplay}</td>
                    <td>${tagsCount}</td>
                    <td style="color: ${condition ? 'green' : 'red'}">${condition ? '✅' : '❌'}</td>
                </tr>`;
            });
            
            html += '</table>';
            displayElement.innerHTML = html;
        }

        async function addTestTagsData() {
            log('🏷️ 开始添加测试标签数据...', 'info');
            
            try {
                // 这里应该调用云函数来更新数据库中的标签数据
                log('☁️ 模拟更新数据库中的标签数据...', 'info');
                
                const updateData = [
                    {
                        id: 'emoji_001',
                        tags: ['搞笑', '幽默', '表情', '开心']
                    },
                    {
                        id: 'emoji_002', 
                        tags: ['可爱', '萌宠', '小动物', '治愈']
                    },
                    {
                        id: 'emoji_003',
                        tags: ['情感', '表达', '心情']
                    }
                ];
                
                log(`✅ 模拟更新了 ${updateData.length} 个表情包的标签数据`, 'success');
                log('📝 实际使用时，需要调用云函数来更新数据库', 'info');
                
                // 提供云函数调用示例
                log('\n💡 云函数调用示例:', 'info');
                log(`
wx.cloud.callFunction({
  name: 'updateEmojiTags',
  data: {
    updates: [
      { id: 'emoji_001', tags: ['搞笑', '幽默', '表情'] },
      { id: 'emoji_002', tags: ['可爱', '萌宠', '小动物'] }
    ]
  }
}).then(result => {
  console.log('标签更新成功:', result);
}).catch(error => {
  console.error('标签更新失败:', error);
});
                `, 'info');
                
            } catch (error) {
                log(`❌ 添加测试数据失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动检查
        window.onload = function() {
            log('🚀 页面加载完成，开始检查数据库标签数据', 'info');
            setTimeout(checkDatabaseTags, 1000);
        };
    </script>
</body>
</html>
