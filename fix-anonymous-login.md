# 修复匿名登录配置指南

## 问题描述
匿名登录失败，返回 `credentials not found` 错误，导致：
- 无法调用云函数
- 无法访问数据库
- Web端管理工具无法正常工作

## 根本原因
云开发环境的匿名登录功能被禁用或配置不正确。

## 解决步骤

### 步骤1: 打开云开发控制台

**方法A: 通过微信开发者工具**
1. 打开微信开发者工具
2. 打开当前项目
3. 点击工具栏中的"云开发"按钮
4. 会自动打开云开发控制台

**方法B: 直接访问**
1. 访问：https://console.cloud.tencent.com/tcb
2. 登录微信开发者账号
3. 选择对应的云开发环境

### 步骤2: 选择正确的环境

确保选择的环境ID是：`cloud1-5g6pvnpl88dc0142`

### 步骤3: 启用匿名登录

1. **进入登录授权页面**：
   - 点击左侧菜单"环境" → "登录授权"
   - 或点击"用户管理" → "登录设置"

2. **启用匿名登录**：
   - 找到"匿名登录"选项
   - 如果显示"未启用"，点击"启用"按钮
   - 确认启用操作

3. **验证启用状态**：
   - 确保匿名登录显示为"已启用"状态
   - 记录下匿名登录的配置信息

### 步骤4: 配置数据库权限

1. **进入数据库权限设置**：
   - 点击左侧菜单"数据库" → "权限设置"

2. **设置匿名用户权限**：
   - 找到"匿名用户"权限配置
   - 确保至少有以下权限：
     - `categories` 集合：读取权限
     - `emojis` 集合：读取权限
     - `banners` 集合：读取权限

3. **推荐权限配置**：
   ```json
   {
     "read": true,
     "write": false,
     "create": false,
     "delete": false
   }
   ```

### 步骤5: 配置云函数权限

1. **进入云函数管理**：
   - 点击左侧菜单"云函数"

2. **检查云函数权限**：
   - 确保以下云函数允许匿名调用：
     - `dataAPI`
     - `webAdminAPI`
     - `adminAPI`

## 验证配置

配置完成后，使用以下工具验证：

### 工具1: 简单SDK测试
1. 打开：`simple-sdk-test.html`
2. 点击"测试SDK初始化"
3. 点击"测试云函数调用"
4. 检查匿名登录是否成功

### 工具2: WebAdminAPI诊断
1. 打开：`diagnose-webadmin-api.html`
2. 点击"诊断WebAdminAPI"
3. 检查是否还有认证错误

## 预期结果

配置成功后，应该看到：
- ✅ 匿名登录成功
- ✅ 云函数调用成功
- ✅ 数据库查询成功
- ❌ 不再出现 `credentials not found` 错误

## 常见问题

### Q1: 找不到"匿名登录"选项
**A**: 可能是因为：
- 环境版本过旧，需要升级
- 权限不足，需要管理员权限
- 界面改版，查找"未登录用户"或"游客模式"

### Q2: 启用后仍然失败
**A**: 尝试以下解决方案：
- 等待5-10分钟让配置生效
- 清除浏览器缓存重新测试
- 检查环境ID是否正确
- 重新部署相关云函数

### Q3: 数据库权限配置复杂
**A**: 可以使用以下简化配置：
```javascript
// 允许所有匿名用户读取
{
  "read": "auth != null || auth == null"
}
```

## 替代方案

如果无法启用匿名登录，可以考虑：

1. **使用自定义登录**：
   - 实现简单的用户名密码登录
   - 在云函数中验证身份

2. **使用管理员API**：
   - 所有操作都通过webAdminAPI进行
   - 在云函数内部处理数据库操作

3. **联系技术支持**：
   - 如果是企业账号，可能有特殊限制
   - 需要技术支持协助配置
