// 首页数据加载测试脚本
// 在微信开发者工具控制台中运行

console.log('🧪 开始首页数据加载测试...');

// 获取首页实例
const pages = getCurrentPages();
const currentPage = pages[pages.length - 1];

if (currentPage.route !== 'pages/index/index') {
  console.error('❌ 请在首页运行此测试');
} else {
  console.log('✅ 当前在首页，开始测试...');
  
  // 测试1: 检查数据状态
  console.log('\n📊 测试1: 检查当前数据状态');
  console.log('表情包数量:', currentPage.data.emojiList.length);
  console.log('分类数量:', currentPage.data.hotCategories.length);
  console.log('横幅数量:', currentPage.data.bannerList.length);
  console.log('搜索结果数量:', currentPage.data.searchResults.length);
  
  // 测试2: 检查渲染条件
  console.log('\n🎨 测试2: 检查渲染条件');
  const shouldShowEmojis = currentPage.data.searchResults.length === 0 && currentPage.data.emojiList.length > 0;
  const shouldShowCategories = currentPage.data.searchResults.length === 0 && currentPage.data.hotCategories.length > 0;
  const shouldShowBanners = currentPage.data.searchResults.length === 0 && currentPage.data.bannerList.length > 0;
  
  console.log('应该显示表情包:', shouldShowEmojis);
  console.log('应该显示分类:', shouldShowCategories);
  console.log('应该显示横幅:', shouldShowBanners);
  
  // 测试3: 重新加载数据
  console.log('\n🔄 测试3: 重新加载数据');
  
  Promise.all([
    currentPage.loadEmojiData(),
    currentPage.loadCategoryData(),
    currentPage.loadBannerData()
  ]).then(() => {
    console.log('✅ 数据重新加载完成');
    console.log('新的表情包数量:', currentPage.data.emojiList.length);
    console.log('新的分类数量:', currentPage.data.hotCategories.length);
    console.log('新的横幅数量:', currentPage.data.bannerList.length);
    
    // 测试4: 检查数据结构
    console.log('\n🔍 测试4: 检查数据结构');
    if (currentPage.data.emojiList.length > 0) {
      console.log('第一个表情包:', currentPage.data.emojiList[0]);
    }
    if (currentPage.data.hotCategories.length > 0) {
      console.log('第一个分类:', currentPage.data.hotCategories[0]);
    }
    
  }).catch(error => {
    console.error('❌ 数据加载失败:', error);
  });
}