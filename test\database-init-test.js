/**
 * 数据库初始化测试脚本
 * 验证数据库初始化功能的正确性
 */

// 模拟微信小程序环境
global.wx = {
  cloud: {
    database: () => ({
      collection: (name) => ({
        limit: (num) => ({
          get: async () => {
            console.log(`✅ 模拟查询集合 ${name}`)
            return { data: [] } // 模拟空集合
          }
        }),
        count: async () => {
          console.log(`✅ 模拟统计集合 ${name}`)
          return { total: 0 }
        },
        add: async (options) => {
          console.log(`✅ 模拟插入数据到 ${name}:`, options.data.length || 1, '条记录')
          return { _id: 'mock_id' }
        }
      })
    })
  }
}

// 模拟 getApp
global.getApp = () => ({
  globalData: {
    cloudInitialized: true
  }
})

// 引入数据库初始化模块
const { DatabaseInit } = require('../utils/databaseInit.js')

// 测试函数
async function testDatabaseInit() {
  console.log('🧪 开始测试数据库初始化...\n')

  try {
    // 测试1: 检查集合配置
    console.log('📋 测试1: 检查集合配置')
    const collections = DatabaseInit.collections
    console.log(`   配置的集合数量: ${Object.keys(collections).length}`)
    
    let requiredCount = 0
    for (const [name, config] of Object.entries(collections)) {
      if (config.required) requiredCount++
      console.log(`   - ${name}: ${config.description} (${config.required ? '必需' : '可选'})`)
    }
    console.log(`   必需集合: ${requiredCount}`)
    console.log('   ✅ 通过\n')

    // 测试2: 检查数据库连接
    console.log('📋 测试2: 检查数据库连接')
    const connectionResult = await DatabaseInit.checkConnection()
    if (connectionResult) {
      console.log('   ✅ 数据库连接正常')
    } else {
      console.log('   ⚠️ 数据库连接异常（在测试环境中这是正常的）')
    }
    console.log('')

    // 测试3: 生成默认数据
    console.log('📋 测试3: 生成默认数据')
    
    const categories = await DatabaseInit.getDefaultCategories()
    console.log(`   默认分类数量: ${categories.length}`)
    console.log(`   分类示例: ${categories[0].name}`)
    
    const appConfig = await DatabaseInit.getDefaultAppConfig()
    console.log(`   默认配置数量: ${appConfig.length}`)
    console.log(`   配置示例: ${appConfig[0].key}`)
    
    const banners = await DatabaseInit.getDefaultBanners()
    console.log(`   默认轮播图数量: ${banners.length}`)
    
    console.log('   ✅ 默认数据生成正常\n')

    // 测试4: 初始化集合
    console.log('📋 测试4: 初始化集合')
    const initResult = await DatabaseInit.initCollections()
    console.log(`   初始化结果: 成功 ${initResult.successCount}/${initResult.total}`)
    
    if (initResult.success) {
      console.log('   ✅ 集合初始化成功')
    } else {
      console.log('   ⚠️ 部分集合初始化失败（在测试环境中这是正常的）')
    }
    console.log('')

    // 测试5: 检查数据完整性
    console.log('📋 测试5: 检查数据完整性')
    const integrityResult = await DatabaseInit.checkDataIntegrity()
    console.log(`   完整性检查: ${integrityResult.success ? '通过' : '发现问题'}`)
    
    if (!integrityResult.success) {
      console.log(`   发现问题数量: ${integrityResult.issues.length}`)
      integrityResult.issues.forEach(issue => {
        console.log(`   - ${issue.type}: ${issue.message}`)
      })
    }
    console.log('   ✅ 完整性检查完成\n')

    // 测试6: 数据修复
    console.log('📋 测试6: 数据修复')
    const repairResult = await DatabaseInit.repairData()
    console.log(`   修复结果: 修复了 ${repairResult.repaired} 个问题`)
    console.log('   ✅ 数据修复完成\n')

    // 测试7: 完整初始化流程
    console.log('📋 测试7: 完整初始化流程')
    const fullResult = await DatabaseInit.fullInitialize()
    console.log('   完整初始化结果:')
    console.log(`   - 连接检查: ${fullResult.connection ? '✅' : '❌'}`)
    console.log(`   - 集合初始化: ${fullResult.collections?.success ? '✅' : '❌'}`)
    console.log(`   - 完整性检查: ${fullResult.integrity?.success ? '✅' : '❌'}`)
    console.log(`   - 数据修复: ${fullResult.repair ? '✅' : '跳过'}`)
    console.log(`   - 最终状态: ${fullResult.success ? '✅' : '❌'}`)
    console.log('')

    // 测试8: 安全数据库操作
    console.log('📋 测试8: 安全数据库操作')
    const safeResult = await DatabaseInit.safeDbOperation(async () => {
      return { test: 'success' }
    })
    
    if (safeResult) {
      console.log('   ✅ 安全操作执行成功')
    } else {
      console.log('   ⚠️ 安全操作被拦截（这是正常的保护机制）')
    }
    console.log('')

    console.log('🎉 所有测试完成！数据库初始化模块工作正常')
    return true

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    console.error('   错误详情:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  testDatabaseInit().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = { testDatabaseInit }
