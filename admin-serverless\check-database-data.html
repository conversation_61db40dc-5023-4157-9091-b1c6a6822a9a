<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云数据库数据检查工具</title>

    <!-- 加载CloudBase SDK - 使用官方推荐的CDN -->
    <script src="https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
        }

        .check-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .check-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }

        .check-section h3::before {
            content: "🔍";
            margin-right: 10px;
        }

        .check-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .check-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .check-btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }

        .result-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .result-success {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #2d3748;
        }

        .result-error {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            color: #2d3748;
        }

        .result-info {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            color: #2d3748;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e8ed;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .timestamp {
            color: #666;
            font-size: 12px;
            margin-top: 10px;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 管理后台数据检查工具</h1>
            <p>检查管理后台添加的数据存储情况（本地存储 + 云数据库同步状态）</p>
        </div>

        <div class="content">
            <!-- 本地存储数据检查 -->
            <div class="check-section">
                <h3>本地存储数据检查</h3>
                <button class="check-btn" onclick="checkLocalStorage()">检查本地数据</button>
                <button class="check-btn" onclick="showLocalStorageStats()">数据统计</button>
                <div id="localStorage-result"></div>
            </div>

            <!-- 云数据库连接检查 -->
            <div class="check-section">
                <h3>☁️ 云数据库状态检查</h3>
                <button class="check-btn" onclick="checkCloudConnection()">检查云连接</button>
                <button class="check-btn" onclick="checkRealCloudData()">🔍 检查真实云数据库</button>
                <button class="check-btn" onclick="testDatabasePermissions()">🔐 测试数据库权限</button>
                <button class="check-btn" onclick="testWebAdminAPI()">测试webAdminAPI</button>
                <div id="cloud-result"></div>
            </div>

            <!-- 分类数据检查 -->
            <div class="check-section">
                <h3>分类数据检查</h3>
                <button class="check-btn" onclick="checkCategories()">检查分类数据</button>
                <button class="check-btn" onclick="checkCategoriesDetail()">详细分类信息</button>
                <div id="categories-result"></div>
            </div>

            <!-- 表情包数据检查 -->
            <div class="check-section">
                <h3>表情包数据检查</h3>
                <button class="check-btn" onclick="checkEmojis()">检查表情包数据</button>
                <button class="check-btn" onclick="checkEmojisDetail()">详细表情包信息</button>
                <button class="check-btn" onclick="checkRecentEmojis()">最近添加的表情包</button>
                <div id="emojis-result"></div>
            </div>

            <!-- 数据诊断 -->
            <div class="check-section">
                <h3>🔍 数据来源诊断</h3>
                <button class="check-btn" onclick="diagnoseDataSource()">诊断当前数据来源</button>
                <button class="check-btn" onclick="compareAllDataSources()">对比所有数据源</button>
                <button class="check-btn" onclick="migrateCloudToLocal()" style="background: #28a745;">迁移云数据库到本地存储</button>
                <div id="diagnose-result"></div>
            </div>

            <!-- 数据清理和同步 -->
            <div class="check-section">
                <h3>🧹 数据清理</h3>
                <button class="check-btn" onclick="clearLocalStorage()" style="background: #ff6b6b;">清理本地缓存数据</button>
                <button class="check-btn" onclick="clearRealCloudDatabase()" style="background: #ff4757;">🔥 真正清空云数据库</button>
                <div id="clear-result"></div>
            </div>

            <!-- 数据同步到云数据库 -->
            <div class="check-section">
                <h3>🚀 数据同步到云数据库</h3>
                <button class="check-btn" onclick="syncCategoriesToCloud()">同步分类数据</button>
                <button class="check-btn" onclick="syncEmojisToCloud()">同步表情包数据</button>
                <button class="check-btn" onclick="syncBannersToCloud()">同步横幅数据</button>
                <button class="check-btn" onclick="syncAllToCloud()" style="background: #e53e3e;">🔄 全量同步</button>
                <div id="sync-result"></div>
            </div>

            <!-- 同步状态检查 -->
            <div class="check-section">
                <h3>同步状态检查</h3>
                <button class="check-btn" onclick="checkSyncStatus()">检查同步状态</button>
                <button class="check-btn" onclick="compareLocalAndCloud()">对比本地与云端数据</button>
                <div id="sync-status-result"></div>
            </div>
        </div>
    </div>

    <script>
        // 根据技术文档的标准解决方案：使用webAdminAPI云函数绕过身份认证问题
        let tcbApp;

        // 使用SDK直接调用云函数 - 根据技术文档的标准做法
        async function callWebAdminAPIDirectly(action, data = {}) {
            try {
                // 确保云开发已初始化
                if (!window.tcbApp) {
                    throw new Error('云开发未初始化，请先初始化SDK');
                }

                // 使用SDK直接调用云函数（正确的方式）
                const result = await window.tcbApp.callFunction({
                    name: 'webAdminAPI',  // 使用正确的云函数名称
                    data: {
                        action: action,
                        data: data,
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    return result.result.data;
                } else {
                    throw new Error(result.result?.error || '云函数调用失败');
                }
            } catch (error) {
                console.error('webAdminAPI调用失败:', error);
                throw error;
            }
        }

        // 初始化云开发SDK - 根据技术文档的标准配置
        async function initCloudbase() {
            try {
                // 检查SDK是否已加载
                if (typeof cloudbase === 'undefined') {
                    throw new Error('CloudBase SDK未加载');
                }

                // 使用正确的初始化方式（CloudBase 2.x版本）
                window.tcbApp = window.cloudbase.init({
                    env: 'cloud1-5g6pvnpl88dc0142',
                    clientId: 'cloud1-5g6pvnpl88dc0142' // 2.x版本必需参数
                });

                // 进行匿名登录
                const auth = window.tcbApp.auth();
                await auth.signInAnonymously();

                // 测试云函数调用
                await callWebAdminAPIDirectly('getStats');
                console.log('✅ 云开发SDK初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 云开发初始化失败:', error);
                return false;
            }
        }

        // 显示加载状态
        function showLoadingStatus(message, isError = false) {
            const statusDiv = document.getElementById('loading-status');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div style="padding: 20px; text-align: center; ${isError ? 'color: red;' : 'color: blue;'}">
                        <h3>${isError ? '❌' : '🔄'} ${message}</h3>
                        ${isError ? '<p>请检查网络连接或稍后重试</p>' : '<p>请稍候...</p>'}
                    </div>
                `;
            }
        }

        // 页面加载时初始化
        window.addEventListener('DOMContentLoaded', async () => {
            // 添加加载状态显示
            document.body.insertAdjacentHTML('afterbegin', '<div id="loading-status"></div>');

            showLoadingStatus('正在加载云开发SDK...');

            const initSuccess = await initCloudbase();

            if (initSuccess) {
                showLoadingStatus('SDK初始化成功，准备就绪！');
                setTimeout(() => {
                    const statusDiv = document.getElementById('loading-status');
                    if (statusDiv) statusDiv.style.display = 'none';
                }, 2000);
            } else {
                showLoadingStatus('云开发SDK初始化失败', true);

                // 显示详细的错误信息和解决建议
                document.getElementById('loading-status').innerHTML += `
                    <div style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: left;">
                        <h4>🔧 可能的解决方案：</h4>
                        <ol>
                            <li><strong>检查网络连接</strong> - 确保能访问外网</li>
                            <li><strong>检查防火墙设置</strong> - 可能阻止了CDN访问</li>
                            <li><strong>尝试刷新页面</strong> - 重新加载SDK</li>
                            <li><strong>检查云开发环境</strong> - 确认环境ID正确</li>
                            <li><strong>查看浏览器控制台</strong> - 获取详细错误信息</li>
                        </ol>
                        <p><strong>技术支持：</strong>如果问题持续，请查看技术文档库中的SDK问题解决方案</p>
                    </div>
                `;
            }
        });

        // 工具函数
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result-box result-${type}">${content}<div class="timestamp">检查时间: ${new Date().toLocaleString()}</div></div>`;
        }

        function showLoading(elementId, message = '正在检查...') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result-box result-info"><span class="loading"></span>${message}</div>`;
        }

        // 标准的webAdminAPI调用函数 - 使用HTTP直连方式
        async function callWebAdminAPI(action, data = {}) {
            return await callWebAdminAPIDirectly(action, data);
        }

        // 本地存储数据检查 - 这是管理后台数据的真实存储位置
        function checkLocalStorage() {
            showLoading('localStorage-result', '正在检查本地存储数据...');

            try {
                const PREFIX = 'emoji_admin_';
                const collections = ['emojis', 'categories', 'users', 'banners', 'system_config'];

                let report = `📦 本地存储数据检查报告:\n\n`;
                let totalItems = 0;

                collections.forEach(collection => {
                    const key = PREFIX + collection;
                    const data = localStorage.getItem(key);

                    if (data) {
                        try {
                            const parsedData = JSON.parse(data);
                            const count = Array.isArray(parsedData) ? parsedData.length : 1;
                            totalItems += count;

                            report += `📂 ${collection}:\n`;
                            report += `   数据量: ${count} 条\n`;
                            report += `   存储大小: ${(data.length / 1024).toFixed(2)} KB\n`;

                            if (Array.isArray(parsedData) && parsedData.length > 0) {
                                const sample = parsedData[0];
                                report += `   示例数据: ${sample.title || sample.name || sample._id || '无标题'}\n`;
                                if (sample.createTime) {
                                    report += `   最新创建: ${new Date(sample.createTime).toLocaleString()}\n`;
                                }
                            }
                            report += `\n`;
                        } catch (parseError) {
                            report += `📂 ${collection}: ❌ 数据解析失败\n\n`;
                        }
                    } else {
                        report += `📂 ${collection}: 📭 无数据\n\n`;
                    }
                });

                report += `📊 总计: ${totalItems} 条数据\n`;
                report += `💾 存储状态: ${totalItems > 0 ? '✅ 有数据' : '📭 空数据'}\n`;

                showResult('localStorage-result', report, totalItems > 0 ? 'success' : 'info');

            } catch (error) {
                showResult('localStorage-result', `❌ 本地存储检查失败: ${error.message}`, 'error');
            }
        }

        // 显示本地存储统计
        function showLocalStorageStats() {
            showLoading('localStorage-result', '正在生成数据统计...');

            try {
                const PREFIX = 'emoji_admin_';

                // 获取分类数据
                const categoriesData = JSON.parse(localStorage.getItem(PREFIX + 'categories') || '[]');
                const activeCategories = categoriesData.filter(cat => cat.status === 'show');

                // 获取表情包数据
                const emojisData = JSON.parse(localStorage.getItem(PREFIX + 'emojis') || '[]');
                const publishedEmojis = emojisData.filter(emoji => emoji.status === 'published');

                // 获取横幅数据
                const bannersData = JSON.parse(localStorage.getItem(PREFIX + 'banners') || '[]');
                const activeBanners = bannersData.filter(banner => banner.status === 'show');

                const statsHtml = `
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${categoriesData.length}</div>
                            <div class="stat-label">总分类数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${activeCategories.length}</div>
                            <div class="stat-label">活跃分类</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${emojisData.length}</div>
                            <div class="stat-label">总表情包</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${publishedEmojis.length}</div>
                            <div class="stat-label">已发布表情包</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${bannersData.length}</div>
                            <div class="stat-label">总横幅数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${activeBanners.length}</div>
                            <div class="stat-label">活跃横幅</div>
                        </div>
                    </div>

                    📊 详细统计信息:

                    📂 分类统计:
                    ${categoriesData.map((cat, index) => `${index + 1}. ${cat.name} (${cat.icon}) - ${cat.status}`).join('\n')}

                    😊 表情包统计:
                    最近添加的表情包:
                    ${emojisData.slice(-5).map((emoji, index) => `${index + 1}. ${emoji.title} - ${emoji.status}`).join('\n')}
                `;

                showResult('localStorage-result', statsHtml, 'success');

            } catch (error) {
                showResult('localStorage-result', `❌ 统计生成失败: ${error.message}`, 'error');
            }
        }

        // 检查云数据库连接
        async function checkCloudConnection() {
            showLoading('cloud-result', '正在检查云数据库连接...');

            try {
                const data = await callWebAdminAPI('getStats');

                showResult('cloud-result', `✅ 云数据库连接正常
🌐 环境ID: cloud1-5g6pvnpl88dc0142
📡 webAdminAPI云函数: 可访问
📊 云端数据统计: ${JSON.stringify(data, null, 2)}`, 'success');

            } catch (error) {
                showResult('cloud-result', `❌ 云数据库连接失败
错误信息: ${error.message}

💡 说明: 这是正常的！您的管理后台使用本地存储模式，数据存储在浏览器中。
云数据库连接失败不影响管理后台的正常使用。`, 'info');
            }
        }

        // 测试webAdminAPI
        async function testWebAdminAPI() {
            showLoading('cloud-result', '正在测试webAdminAPI云函数...');

            try {
                // 直接测试云函数URL
                const response = await fetch('https://cloud1-5g6pvnpl88dc0142.service.tcloudbase.com/webAdminAPI', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'getStats',
                        adminPassword: 'admin123456'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showResult('cloud-result', `✅ webAdminAPI测试成功
📊 云端统计数据: ${JSON.stringify(result.data, null, 2)}`, 'success');
                } else {
                    showResult('cloud-result', `⚠️ webAdminAPI响应异常: ${result.error}`, 'error');
                }

            } catch (error) {
                showResult('cloud-result', `❌ webAdminAPI测试失败: ${error.message}

💡 这表明您的管理后台确实是本地存储模式，这是正常的设计！`, 'info');
            }
        }

        // 使用webAdminAPI云函数进行数据同步
        async function callSyncData(type, data) {
            try {
                // 使用webAdminAPI云函数的syncData功能
                const result = await callWebAdminAPIDirectly('syncData', {
                    type: type,
                    data: data
                });

                return { success: true, data: result };
            } catch (error) {
                throw new Error(`同步失败: ${error.message}`);
            }
        }

        // 同步分类数据到云数据库
        async function syncCategoriesToCloud() {
            showLoading('sync-result', '正在同步分类数据到云数据库...');

            try {
                // 从本地存储获取分类数据
                const categoriesData = JSON.parse(localStorage.getItem('emoji_admin_categories') || '[]');
                const activeCategories = categoriesData.filter(cat => cat.status === 'show');

                if (activeCategories.length === 0) {
                    showResult('sync-result', '⚠️ 没有找到需要同步的分类数据（状态为show的分类）', 'info');
                    return;
                }

                // 调用syncData云函数
                const result = await callSyncData('categories', activeCategories);

                if (result.success) {
                    showResult('sync-result', `✅ 分类数据同步成功！
📊 同步数量: ${activeCategories.length} 个分类
📋 同步的分类:
${activeCategories.map((cat, index) => `${index + 1}. ${cat.name} (${cat.icon})`).join('\n')}

🎉 现在小程序可以获取到这些分类数据了！`, 'success');
                } else {
                    showResult('sync-result', `❌ 分类数据同步失败: ${result.error}`, 'error');
                }

            } catch (error) {
                showResult('sync-result', `❌ 分类数据同步失败: ${error.message}`, 'error');
            }
        }

        // 诊断数据来源 - 简化版
        async function diagnoseDataSource() {
            showLoading('diagnose-result', '正在诊断数据来源...');

            try {
                // 检查本地存储
                const localEmojis = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]');
                const localCategories = JSON.parse(localStorage.getItem('emoji_admin_categories') || '[]');
                const localBanners = JSON.parse(localStorage.getItem('emoji_admin_banners') || '[]');

                let cloudEmojisCount = 0;
                let cloudCategoriesCount = 0;
                let cloudError = null;

                // 尝试检查云数据库
                try {
                    const cloudEmojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 100 });
                    if (cloudEmojis.success) {
                        cloudEmojisCount = cloudEmojis.data?.length || 0;
                    }
                } catch (error) {
                    cloudError = error.message;
                }

                try {
                    const cloudCategories = await callWebAdminAPI('getCategoryList', {});
                    if (cloudCategories.success) {
                        cloudCategoriesCount = cloudCategories.data?.length || 0;
                    }
                } catch (error) {
                    if (!cloudError) cloudError = error.message;
                }

                const report = `🔍 数据来源诊断报告

📱 本地存储数据:
- 表情包: ${localEmojis.length} 个
- 分类: ${localCategories.length} 个
- 横幅: ${localBanners.length} 个

☁️ 云数据库数据:
${cloudError ? `❌ 云数据库检查失败: ${cloudError}` : `- 表情包: ${cloudEmojisCount} 个\n- 分类: ${cloudCategoriesCount} 个`}

📋 详细分析:
${localEmojis.length === 0 ? '⚠️ 本地存储为空 - 这就是为什么同步没有数据的原因！' : '✅ 本地存储有数据'}
${cloudEmojisCount > 0 ? '⚠️ 云数据库中有数据' : '✅ 云数据库已清空'}

💡 解决方案:
${localEmojis.length === 0 && cloudEmojisCount > 0 ?
  '🔄 您的数据在云数据库中，需要迁移到本地存储\n👉 点击"迁移云数据库到本地存储"按钮' :
  localEmojis.length === 0 ?
    '📝 本地和云端都没有数据，请重新在管理后台创建表情包' :
    '✅ 数据在本地存储中，可以直接同步'}

${localEmojis.length > 0 ? '\n🎯 本地存储中的表情包:\n' + localEmojis.slice(0, 3).map((emoji, i) => `${i+1}. ${emoji.title}`).join('\n') + (localEmojis.length > 3 ? `\n... 还有 ${localEmojis.length - 3} 个` : '') : ''}`;

                showResult('diagnose-result', report, localEmojis.length === 0 ? 'warning' : 'info');

            } catch (error) {
                showResult('diagnose-result', `❌ 诊断失败: ${error.message}`, 'error');
            }
        }

        // 对比所有数据源
        async function compareAllDataSources() {
            showLoading('diagnose-result', '正在对比所有数据源...');

            try {
                // 获取所有数据源的详细信息
                const localEmojis = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]');
                const cloudResult = await callWebAdminAPI('getEmojis', { page: 1, limit: 100 });
                const cloudEmojis = cloudResult.success ? (cloudResult.data || []) : [];

                let report = `📊 数据源详细对比

🏠 本地存储表情包 (${localEmojis.length}个):
${localEmojis.length > 0 ? localEmojis.map((emoji, i) => {
    // 尝试从分类数据中获取分类名称
    const categoryName = localCategories.find(cat => cat.id === emoji.category)?.name || emoji.category;
    return `${i+1}. ${emoji.title} (${categoryName})`;
}).join('\n') : '无数据'}

☁️ 云数据库表情包 (${cloudEmojis.length}个):
${cloudEmojis.length > 0 ? cloudEmojis.map((emoji, i) => `${i+1}. ${emoji.title || emoji.name} (${emoji.category})`).join('\n') : '无数据'}

🔍 数据一致性分析:`;

                if (localEmojis.length === 0 && cloudEmojis.length === 0) {
                    report += '\n✅ 所有数据源都已清空，管理后台显示的是系统默认数据';
                } else if (localEmojis.length === 0 && cloudEmojis.length > 0) {
                    report += '\n⚠️ 本地已清空，但云数据库有数据，管理后台从云数据库加载';
                } else if (localEmojis.length > 0 && cloudEmojis.length === 0) {
                    report += '\n⚠️ 云数据库已清空，但本地有缓存数据';
                } else {
                    report += '\n⚠️ 本地和云端都有数据，可能存在不一致';
                }

                showResult('diagnose-result', report, 'info');

            } catch (error) {
                showResult('diagnose-result', `❌ 对比失败: ${error.message}`, 'error');
            }
        }

        // 迁移云数据库数据到本地存储
        async function migrateCloudToLocal() {
            if (!confirm('🔄 确定要将云数据库中的数据迁移到本地存储吗？\n这将覆盖当前的本地存储数据。')) {
                return;
            }

            showLoading('diagnose-result', '正在迁移数据...');

            try {
                // 获取云数据库中的数据
                const cloudEmojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 100 });
                const cloudCategories = await callWebAdminAPI('getCategoryList', {});

                let migratedCount = 0;

                // 迁移表情包数据
                if (cloudEmojis.success && cloudEmojis.data && cloudEmojis.data.length > 0) {
                    const emojisData = cloudEmojis.data.map(emoji => ({
                        id: emoji._id,
                        title: emoji.title || emoji.name,
                        category: emoji.category,
                        imageUrl: emoji.imageUrl || emoji.url,
                        description: emoji.description || '',
                        tags: emoji.tags || [],
                        status: 'published',
                        likes: emoji.likes || 0,
                        downloads: emoji.downloads || 0,
                        collections: emoji.collections || 0,
                        createTime: emoji.createTime || new Date(),
                        updateTime: new Date()
                    }));

                    localStorage.setItem('emoji_admin_emojis', JSON.stringify(emojisData));
                    migratedCount += emojisData.length;
                    console.log('✅ 表情包数据迁移完成:', emojisData.length, '条');
                }

                // 迁移分类数据
                if (cloudCategories.success && cloudCategories.data && cloudCategories.data.length > 0) {
                    const categoriesData = cloudCategories.data.map(category => ({
                        id: category._id,
                        name: category.name,
                        description: category.description || '',
                        icon: category.icon || '',
                        sort: category.sort || 0,
                        status: 'show',
                        createTime: category.createTime || new Date(),
                        updateTime: new Date()
                    }));

                    localStorage.setItem('emoji_admin_categories', JSON.stringify(categoriesData));
                    console.log('✅ 分类数据迁移完成:', categoriesData.length, '条');
                }

                const report = `✅ 数据迁移完成！

📊 迁移统计:
- 表情包: ${cloudEmojis.data?.length || 0} 条
- 分类: ${cloudCategories.data?.length || 0} 条

💡 现在您可以:
1. 刷新管理后台页面查看迁移的数据
2. 点击"同步表情包数据"将数据同步到云数据库
3. 管理后台的修改将正确保存到本地存储

🎉 数据迁移成功，现在管理后台和同步功能将正常工作！`;

                showResult('diagnose-result', report, 'success');

            } catch (error) {
                showResult('diagnose-result', `❌ 数据迁移失败: ${error.message}`, 'error');
            }
        }

        // 清理本地缓存数据
        async function clearLocalStorage() {
            if (!confirm('⚠️ 确定要清理本地缓存数据吗？这将删除所有本地存储的表情包、分类和横幅数据！')) {
                return;
            }

            showLoading('clear-result', '正在清理本地缓存数据...');

            try {
                // 清理所有相关的本地存储数据
                localStorage.removeItem('emoji_admin_emojis');
                localStorage.removeItem('emoji_admin_categories');
                localStorage.removeItem('emoji_admin_banners');
                localStorage.removeItem('emoji_admin_settings');

                // 清理其他可能的缓存
                const keys = Object.keys(localStorage);
                keys.forEach(key => {
                    if (key.startsWith('emoji_') || key.startsWith('admin_')) {
                        localStorage.removeItem(key);
                    }
                });

                showResult('clear-result', '✅ 本地缓存数据清理完成！\n现在本地存储中没有任何历史数据了。', 'success');
            } catch (error) {
                showResult('clear-result', `❌ 清理本地缓存失败: ${error.message}`, 'error');
            }
        }

        // 检查真实云数据库数据
        async function checkRealCloudData() {
            showLoading('cloud-result', '正在直接连接云数据库检查数据...');

            try {
                // 确保CloudBase已初始化
                if (!window.tcbApp) {
                    await initCloudBase();
                }

                const db = window.tcbApp.database();

                // 直接查询云数据库
                const emojisResult = await db.collection('emojis').limit(100).get();
                const categoriesResult = await db.collection('categories').limit(100).get();
                const bannersResult = await db.collection('banners').limit(100).get();

                const report = `☁️ 真实云数据库数据统计

📊 数据概览:
- 表情包: ${emojisResult.data.length} 个
- 分类: ${categoriesResult.data.length} 个
- 横幅: ${bannersResult.data.length} 个

📋 表情包列表 (前10个):
${emojisResult.data.slice(0, 10).map((emoji, i) => `${i+1}. ${emoji.title || emoji.name || '未命名'} (${emoji.category || '未分类'})`).join('\n')}
${emojisResult.data.length > 10 ? `... 还有 ${emojisResult.data.length - 10} 个` : ''}

📂 分类列表:
${categoriesResult.data.map((cat, i) => `${i+1}. ${cat.name} (${cat.icon || '无图标'})`).join('\n')}

🖼️ 横幅列表:
${bannersResult.data.map((banner, i) => `${i+1}. ${banner.title} (${banner.status || '未知状态'})`).join('\n')}

⚠️ 注意：这是云数据库中的真实数据，与之前显示的0个数据不符！
说明云函数调用存在问题，需要修复云函数连接。`;

                showResult('cloud-result', report, 'warning');

            } catch (error) {
                showResult('cloud-result', `❌ 直接连接云数据库失败: ${error.message}

可能的原因：
1. CloudBase SDK未正确初始化
2. 数据库权限配置问题
3. 网络连接问题

请检查浏览器控制台获取详细错误信息。`, 'error');
            }
        }

        // 测试数据库权限
        async function testDatabasePermissions() {
            showLoading('cloud-result', '正在测试数据库权限...');

            try {
                // 确保CloudBase已初始化
                if (!window.tcbApp) {
                    await initCloudBase();
                }

                const db = window.tcbApp.database();
                let report = '🔐 数据库权限测试报告:\n\n';

                // 测试读取权限
                try {
                    const readTest = await db.collection('emojis').limit(1).get();
                    report += `✅ 读取权限: 正常 (查询到${readTest.data.length}条数据)\n`;
                } catch (error) {
                    report += `❌ 读取权限: 失败 - ${error.message}\n`;
                }

                // 测试写入权限
                try {
                    const writeTest = await db.collection('emojis').add({
                        data: {
                            title: '权限测试数据',
                            category: '测试',
                            status: 'test',
                            createTime: new Date()
                        }
                    });
                    report += `✅ 写入权限: 正常 (创建了测试数据: ${writeTest._id})\n`;

                    // 测试删除权限
                    try {
                        const deleteTest = await db.collection('emojis').doc(writeTest._id).remove();
                        const deleteResult = deleteTest.deleted || deleteTest.stats?.removed || 1;
                        report += `✅ 删除权限: 正常 (删除了${deleteResult}条测试数据)\n`;
                    } catch (deleteError) {
                        report += `❌ 删除权限: 失败 - ${deleteError.message}\n`;
                        report += `⚠️ 这就是为什么清空数据库失败的原因！\n`;
                    }
                } catch (writeError) {
                    report += `❌ 写入权限: 失败 - ${writeError.message}\n`;
                }

                report += `\n💡 权限设置建议:\n`;
                report += `1. 打开云开发控制台: https://console.cloud.tencent.com/tcb\n`;
                report += `2. 选择环境: cloud1-5g6pvnpl88dc0142\n`;
                report += `3. 进入数据库 → 选择集合 → 权限设置\n`;
                report += `4. 将权限改为"所有用户可读写"\n`;

                showResult('cloud-result', report, 'info');

            } catch (error) {
                showResult('cloud-result', `❌ 权限测试失败: ${error.message}`, 'error');
            }
        }

        // 真正清空云数据库 - 直接操作
        async function clearRealCloudDatabase() {
            if (!confirm('🚨 警告：这将直接删除云数据库中的所有真实数据！\n\n根据您的截图，云数据库中有：\n- 很多表情包数据\n- 7个分类数据\n- 5个横幅数据\n\n确定要全部删除吗？')) {
                return;
            }

            if (!confirm('⚠️ 最后确认：删除后无法恢复！确定继续吗？')) {
                return;
            }

            showLoading('clear-result', '正在直接清空云数据库...');

            try {
                // 确保CloudBase已初始化
                if (!window.tcbApp) {
                    await initCloudBase();
                }

                const db = window.tcbApp.database();
                let totalDeleted = 0;
                let report = '🔥 真实云数据库清空进度:\n\n';

                // 清空表情包数据 - 改进版
                try {
                    // 先获取所有数据
                    const emojisQuery = await db.collection('emojis').limit(1000).get();
                    const emojisCount = emojisQuery.data.length;

                    if (emojisCount > 0) {
                        // 批量删除
                        const emojisResult = await db.collection('emojis').where({}).remove();
                        const emojisDeleted = emojisResult.deleted || emojisResult.stats?.removed || emojisCount;
                        totalDeleted += emojisDeleted;
                        report += `✅ 删除表情包: ${emojisDeleted} 个 (查询到${emojisCount}个)\n`;
                    } else {
                        report += `ℹ️ 表情包集合为空\n`;
                    }
                } catch (error) {
                    report += `❌ 删除表情包失败: ${error.message}\n`;
                    console.error('删除表情包详细错误:', error);
                }

                // 清空分类数据
                try {
                    const categoriesResult = await db.collection('categories').where({}).remove();
                    const categoriesDeleted = categoriesResult.deleted;
                    totalDeleted += categoriesDeleted;
                    report += `✅ 删除分类: ${categoriesDeleted} 个\n`;
                } catch (error) {
                    report += `❌ 删除分类失败: ${error.message}\n`;
                }

                // 清空横幅数据
                try {
                    const bannersResult = await db.collection('banners').where({}).remove();
                    const bannersDeleted = bannersResult.deleted;
                    totalDeleted += bannersDeleted;
                    report += `✅ 删除横幅: ${bannersDeleted} 个\n`;
                } catch (error) {
                    report += `❌ 删除横幅失败: ${error.message}\n`;
                }

                // 清空用户行为数据
                try {
                    const userActionsResult = await db.collection('user-actions').where({}).remove();
                    const userActionsDeleted = userActionsResult.deleted;
                    totalDeleted += userActionsDeleted;
                    report += `✅ 删除用户行为: ${userActionsDeleted} 个\n`;
                } catch (error) {
                    report += `⚠️ 用户行为数据清理: ${error.message}\n`;
                }

                report += `\n🎉 云数据库真实清空完成！\n总共删除 ${totalDeleted} 条真实数据\n\n现在云数据库应该是真正的空白状态了！`;
                showResult('clear-result', report, 'success');

            } catch (error) {
                showResult('clear-result', `❌ 真实清空云数据库失败: ${error.message}\n\n可能的原因：\n1. CloudBase SDK未正确初始化\n2. 数据库权限不足\n3. 网络连接问题`, 'error');
            }
        }

        // 旧的清空云数据库函数（通过云函数，已知有问题）
        async function clearCloudDatabase() {
            if (!confirm('⚠️ 确定要清空云数据库吗？这将删除所有云端的表情包、分类和横幅数据！')) {
                return;
            }

            showLoading('clear-result', '正在清空云数据库...');

            try {
                let totalDeleted = 0;
                let report = '📊 清空进度:\n\n';

                // 方法1：尝试使用clearAllData云函数
                try {
                    const result = await callWebAdminAPI('clearAllData', {});
                    if (result.success) {
                        showResult('clear-result', `✅ 云数据库清空完成！

📊 清空统计:
- 表情包: ${result.data?.emojis || 0} 条
- 分类: ${result.data?.categories || 0} 条
- 横幅: ${result.data?.banners || 0} 条

🎉 所有数据已删除，云数据库已清空！`, 'success');
                        return;
                    }
                } catch (error) {
                    console.log('clearAllData云函数不可用，使用备用方案');
                }

                // 方法2：备用方案 - 逐个删除
                report += '⚠️ 使用备用清空方案...\n\n';

                // 获取并删除表情包
                try {
                    const emojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 1000 });
                    if (emojis.success && emojis.data && emojis.data.length > 0) {
                        for (const emoji of emojis.data) {
                            try {
                                await callWebAdminAPI('deleteEmoji', { id: emoji._id });
                                totalDeleted++;
                            } catch (deleteError) {
                                console.warn('删除表情包失败:', emoji._id, deleteError);
                            }
                        }
                        report += `✅ 删除表情包: ${emojis.data.length} 个\n`;
                    }
                } catch (error) {
                    report += `❌ 删除表情包失败: ${error.message}\n`;
                }

                // 获取并删除分类
                try {
                    const categories = await callWebAdminAPI('getCategoryList', {});
                    if (categories.success && categories.data && categories.data.length > 0) {
                        for (const category of categories.data) {
                            try {
                                await callWebAdminAPI('deleteCategory', { id: category._id });
                                totalDeleted++;
                            } catch (deleteError) {
                                console.warn('删除分类失败:', category._id, deleteError);
                            }
                        }
                        report += `✅ 删除分类: ${categories.data.length} 个\n`;
                    }
                } catch (error) {
                    report += `❌ 删除分类失败: ${error.message}\n`;
                }

                report += `\n🎉 清空完成！总共删除 ${totalDeleted} 条数据`;
                showResult('clear-result', report, 'success');

            } catch (error) {
                showResult('clear-result', `❌ 清空云数据库失败: ${error.message}`, 'error');
            }
        }

        // 同步表情包数据到云数据库
        async function syncEmojisToCloud() {
            showLoading('sync-result', '正在同步表情包数据到云数据库...');

            try {
                // 从本地存储获取表情包数据
                const emojisData = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]');
                const publishedEmojis = emojisData.filter(emoji => emoji.status === 'published');

                console.log('🔍 本地存储中的所有表情包数据:', emojisData);
                console.log('📋 筛选出的已发布表情包:', publishedEmojis);

                if (publishedEmojis.length === 0) {
                    showResult('sync-result', `⚠️ 没有找到需要同步的表情包数据

📊 本地存储统计:
- 总表情包数量: ${emojisData.length}
- 已发布表情包: ${publishedEmojis.length}

💡 请确保:
1. 在管理后台添加了表情包
2. 表情包状态设置为"已发布"(published)`, 'info');
                    return;
                }

                // 调用syncData云函数
                const result = await callSyncData('emojis', publishedEmojis);

                if (result.success) {
                    showResult('sync-result', `✅ 表情包数据全量同步成功！
📊 同步模式: 全量替换 (删除云端所有数据，重新上传管理后台数据)
📊 同步结果: ${result.message || `同步${publishedEmojis.length}个表情包`}
📋 同步的表情包:
${publishedEmojis.slice(0, 5).map((emoji, index) => `${index + 1}. ${emoji.title} (${emoji.category})`).join('\n')}
${publishedEmojis.length > 5 ? `... 还有 ${publishedEmojis.length - 5} 个表情包` : ''}

🎉 现在小程序端显示的数据与管理后台完全一致！
✅ 管理后台删除的数据，小程序端也会同步删除
✅ 管理后台下架的数据，小程序端不会显示`, 'success');
                } else {
                    showResult('sync-result', `❌ 表情包数据同步失败: ${result.error}`, 'error');
                }

            } catch (error) {
                showResult('sync-result', `❌ 表情包数据同步失败: ${error.message}`, 'error');
            }
        }

        // 同步横幅数据到云数据库
        async function syncBannersToCloud() {
            showLoading('sync-result', '正在同步横幅数据到云数据库...');

            try {
                // 从本地存储获取横幅数据
                const bannersData = JSON.parse(localStorage.getItem('emoji_admin_banners') || '[]');
                const activeBanners = bannersData.filter(banner => banner.status === 'show');

                if (activeBanners.length === 0) {
                    showResult('sync-result', '⚠️ 没有找到需要同步的横幅数据（状态为show的横幅）', 'info');
                    return;
                }

                // 调用syncData云函数
                const result = await callSyncData('banners', activeBanners);

                if (result.success) {
                    showResult('sync-result', `✅ 横幅数据同步成功！
📊 同步数量: ${activeBanners.length} 个横幅
📋 同步的横幅:
${activeBanners.map((banner, index) => `${index + 1}. ${banner.title}`).join('\n')}

🎉 现在小程序可以获取到这些横幅数据了！`, 'success');
                } else {
                    showResult('sync-result', `❌ 横幅数据同步失败: ${result.error}`, 'error');
                }

            } catch (error) {
                showResult('sync-result', `❌ 横幅数据同步失败: ${error.message}`, 'error');
            }
        }

        // 全量同步所有数据
        async function syncAllToCloud() {
            showLoading('sync-result', '正在进行全量数据同步...');

            try {
                let syncReport = `🚀 全量数据同步报告:\n\n`;
                let successCount = 0;
                let totalCount = 0;

                // 同步分类
                try {
                    const categoriesData = JSON.parse(localStorage.getItem('emoji_admin_categories') || '[]');
                    const activeCategories = categoriesData.filter(cat => cat.status === 'show');

                    if (activeCategories.length > 0) {
                        const result = await callSyncData('categories', activeCategories);
                        if (result.success) {
                            syncReport += `✅ 分类同步成功: ${activeCategories.length} 个\n`;
                            successCount++;
                        } else {
                            syncReport += `❌ 分类同步失败: ${result.error}\n`;
                        }
                    } else {
                        syncReport += `⚠️ 分类: 无需同步的数据\n`;
                    }
                    totalCount++;
                } catch (error) {
                    syncReport += `❌ 分类同步异常: ${error.message}\n`;
                    totalCount++;
                }

                // 同步表情包
                try {
                    const emojisData = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]');
                    const publishedEmojis = emojisData.filter(emoji => emoji.status === 'published');

                    if (publishedEmojis.length > 0) {
                        const result = await callSyncData('emojis', publishedEmojis);
                        if (result.success) {
                            syncReport += `✅ 表情包同步成功: ${publishedEmojis.length} 个\n`;
                            successCount++;
                        } else {
                            syncReport += `❌ 表情包同步失败: ${result.error}\n`;
                        }
                    } else {
                        syncReport += `⚠️ 表情包: 无需同步的数据\n`;
                    }
                    totalCount++;
                } catch (error) {
                    syncReport += `❌ 表情包同步异常: ${error.message}\n`;
                    totalCount++;
                }

                // 同步横幅
                try {
                    const bannersData = JSON.parse(localStorage.getItem('emoji_admin_banners') || '[]');
                    const activeBanners = bannersData.filter(banner => banner.status === 'show');

                    if (activeBanners.length > 0) {
                        const result = await callSyncData('banners', activeBanners);
                        if (result.success) {
                            syncReport += `✅ 横幅同步成功: ${activeBanners.length} 个\n`;
                            successCount++;
                        } else {
                            syncReport += `❌ 横幅同步失败: ${result.error}\n`;
                        }
                    } else {
                        syncReport += `⚠️ 横幅: 无需同步的数据\n`;
                    }
                    totalCount++;
                } catch (error) {
                    syncReport += `❌ 横幅同步异常: ${error.message}\n`;
                    totalCount++;
                }

                syncReport += `\n📊 同步总结:\n`;
                syncReport += `成功: ${successCount}/${totalCount}\n`;
                syncReport += `状态: ${successCount === totalCount ? '✅ 全部成功' : successCount > 0 ? '⚠️ 部分成功' : '❌ 全部失败'}\n\n`;
                syncReport += `🎉 同步完成！现在小程序可以获取到您的数据了！`;

                showResult('sync-result', syncReport, successCount > 0 ? 'success' : 'error');

            } catch (error) {
                showResult('sync-result', `❌ 全量同步失败: ${error.message}`, 'error');
            }
        }

        // 检查数据库统计
        async function checkStats() {
            showLoading('stats-result', '正在获取数据库统计信息...');

            try {
                const stats = await callWebAdminAPI('getStats');

                const statsHtml = `
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${stats.usersCount || 0}</div>
                            <div class="stat-label">用户数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.emojisCount || 0}</div>
                            <div class="stat-label">表情包数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.categoriesCount || 0}</div>
                            <div class="stat-label">分类数量</div>
                        </div>
                    </div>

                    📊 详细统计信息:
                    ${JSON.stringify(stats, null, 2)}
                `;
                showResult('stats-result', statsHtml, 'success');
            } catch (error) {
                showResult('stats-result', `❌ 获取统计信息失败: ${error.message}`, 'error');
            }
        }

        // 检查分类数据
        async function checkCategories() {
            showLoading('categories-result', '正在检查分类数据...');

            try {
                const categories = await callWebAdminAPI('getCategoryList');

                const categoryInfo = `✅ 分类数据检查完成
📊 总分类数: ${categories.length}
📋 分类列表:
${categories.map((cat, index) => `${index + 1}. ${cat.name} (${cat.icon}) - 状态: ${cat.status}`).join('\n')}

🔍 详细数据:
${JSON.stringify(categories, null, 2)}`;
                showResult('categories-result', categoryInfo, 'success');
            } catch (error) {
                showResult('categories-result', `❌ 检查分类数据失败: ${error.message}`, 'error');
            }
        }

        // 检查分类详细信息
        async function checkCategoriesDetail() {
            showLoading('categories-result', '正在获取详细分类信息...');

            try {
                const categories = await callWebAdminAPI('getCategoryList');

                let detailInfo = `📋 分类详细信息 (共${categories.length}个):\n\n`;

                categories.forEach((cat, index) => {
                    detailInfo += `${index + 1}. 【${cat.name}】\n`;
                    detailInfo += `   ID: ${cat._id}\n`;
                    detailInfo += `   图标: ${cat.icon}\n`;
                    detailInfo += `   描述: ${cat.description || '无'}\n`;
                    detailInfo += `   排序: ${cat.sort || 0}\n`;
                    detailInfo += `   状态: ${cat.status}\n`;
                    detailInfo += `   表情包数量: ${cat.emojiCount || 0}\n`;
                    detailInfo += `   创建时间: ${cat.createTime ? new Date(cat.createTime).toLocaleString() : '未知'}\n`;
                    detailInfo += `   更新时间: ${cat.updateTime ? new Date(cat.updateTime).toLocaleString() : '未知'}\n\n`;
                });

                showResult('categories-result', detailInfo, 'success');
            } catch (error) {
                showResult('categories-result', `❌ 检查分类详细信息失败: ${error.message}`, 'error');
            }
        }

        // 检查表情包数据
        async function checkEmojis() {
            showLoading('emojis-result', '正在检查表情包数据...');

            try {
                const emojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 10 });

                const emojiInfo = `✅ 表情包数据检查完成
📊 获取到的表情包数: ${emojis.length}
📋 表情包列表 (最新10个):
${emojis.map((emoji, index) => `${index + 1}. ${emoji.title} - 分类: ${emoji.category} - 状态: ${emoji.status}`).join('\n')}

🔍 详细数据:
${JSON.stringify(emojis, null, 2)}`;
                showResult('emojis-result', emojiInfo, 'success');
            } catch (error) {
                showResult('emojis-result', `❌ 检查表情包数据失败: ${error.message}`, 'error');
            }
        }

        // 检查表情包详细信息
        async function checkEmojisDetail() {
            showLoading('emojis-result', '正在获取详细表情包信息...');

            try {
                const emojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 20 });

                let detailInfo = `📋 表情包详细信息 (共${emojis.length}个):\n\n`;

                emojis.forEach((emoji, index) => {
                    detailInfo += `${index + 1}. 【${emoji.title}】\n`;
                    detailInfo += `   ID: ${emoji._id}\n`;
                    detailInfo += `   分类: ${emoji.category}\n`;
                    detailInfo += `   图片URL: ${emoji.imageUrl}\n`;
                    detailInfo += `   状态: ${emoji.status}\n`;
                    detailInfo += `   点赞数: ${emoji.likes || 0}\n`;
                    detailInfo += `   收藏数: ${emoji.collections || 0}\n`;
                    detailInfo += `   下载数: ${emoji.downloads || 0}\n`;
                    detailInfo += `   创建时间: ${emoji.createTime ? new Date(emoji.createTime).toLocaleString() : '未知'}\n\n`;
                });

                showResult('emojis-result', detailInfo, 'success');
            } catch (error) {
                showResult('emojis-result', `❌ 检查表情包详细信息失败: ${error.message}`, 'error');
            }
        }

        // 检查最近添加的表情包
        async function checkRecentEmojis() {
            showLoading('emojis-result', '正在检查最近添加的表情包...');

            try {
                const emojis = await callWebAdminAPI('getEmojis', { page: 1, limit: 5 });

                const recentInfo = `📅 最近添加的表情包 (最新5个):
${emojis.map((emoji, index) => {
    const createTime = emoji.createTime ? new Date(emoji.createTime).toLocaleString() : '未知时间';
    return `${index + 1}. ${emoji.title} - ${createTime}`;
}).join('\n')}

🔍 详细信息:
${JSON.stringify(emojis, null, 2)}`;
                showResult('emojis-result', recentInfo, 'success');
            } catch (error) {
                showResult('emojis-result', `❌ 检查最近表情包失败: ${error.message}`, 'error');
            }
        }

        // 数据完整性检查
        async function checkDataIntegrity() {
            showLoading('integrity-result', '正在进行数据完整性检查...');

            try {
                const [stats, categories, emojis] = await Promise.all([
                    callWebAdminAPI('getStats'),
                    callWebAdminAPI('getCategoryList'),
                    callWebAdminAPI('getEmojis', { page: 1, limit: 100 })
                ]);

                let integrityReport = `🔍 数据完整性检查报告:\n\n`;

                // 检查统计数据一致性
                integrityReport += `📊 统计数据一致性:\n`;
                integrityReport += `   统计显示分类数: ${stats.categoriesCount}\n`;
                integrityReport += `   实际分类数: ${categories.length}\n`;
                integrityReport += `   一致性: ${stats.categoriesCount === categories.length ? '✅ 一致' : '❌ 不一致'}\n\n`;

                // 检查分类状态
                const activeCategories = categories.filter(cat => cat.status === 'active');
                integrityReport += `📂 分类状态检查:\n`;
                integrityReport += `   总分类数: ${categories.length}\n`;
                integrityReport += `   活跃分类数: ${activeCategories.length}\n`;
                integrityReport += `   非活跃分类数: ${categories.length - activeCategories.length}\n\n`;

                // 检查表情包数据
                const publishedEmojis = emojis.filter(emoji => emoji.status === 'published');
                integrityReport += `😊 表情包状态检查:\n`;
                integrityReport += `   检查的表情包数: ${emojis.length}\n`;
                integrityReport += `   已发布表情包数: ${publishedEmojis.length}\n`;
                integrityReport += `   未发布表情包数: ${emojis.length - publishedEmojis.length}\n\n`;

                // 检查数据关联性
                const categoryIds = new Set(categories.map(cat => cat._id));
                const orphanEmojis = emojis.filter(emoji => emoji.categoryId && !categoryIds.has(emoji.categoryId));
                integrityReport += `🔗 数据关联性检查:\n`;
                integrityReport += `   孤立表情包数 (分类不存在): ${orphanEmojis.length}\n`;
                if (orphanEmojis.length > 0) {
                    integrityReport += `   孤立表情包: ${orphanEmojis.map(e => e.title).join(', ')}\n`;
                }

                showResult('integrity-result', integrityReport, orphanEmojis.length > 0 ? 'error' : 'success');
            } catch (error) {
                showResult('integrity-result', `❌ 数据完整性检查失败: ${error.message}`, 'error');
            }
        }

        // 数据一致性检查
        async function checkDataConsistency() {
            showLoading('integrity-result', '正在进行数据一致性检查...');

            try {
                const categories = await callWebAdminAPI('getCategoryList');

                let consistencyReport = `🔄 数据一致性检查报告:\n\n`;

                // 检查每个分类的表情包计数
                for (const category of categories) {
                    try {
                        // 这里应该调用一个专门的API来获取特定分类的表情包数量
                        // 由于当前API限制，我们使用现有数据进行检查
                        consistencyReport += `📂 分类: ${category.name}\n`;
                        consistencyReport += `   记录的表情包数量: ${category.emojiCount || 0}\n`;
                        consistencyReport += `   状态: ${category.status}\n`;
                        consistencyReport += `   最后更新: ${category.updateTime ? new Date(category.updateTime).toLocaleString() : '未知'}\n\n`;
                    } catch (error) {
                        consistencyReport += `   ❌ 检查失败: ${error.message}\n\n`;
                    }
                }

                showResult('integrity-result', consistencyReport, 'success');
            } catch (error) {
                showResult('integrity-result', `❌ 数据一致性检查失败: ${error.message}`, 'error');
            }
        }

        // 实时同步测试
        async function testRealTimeSync() {
            showLoading('sync-result', '正在测试实时数据同步...');

            try {
                // 创建一个测试分类
                const testCategoryName = `测试分类_${Date.now()}`;
                const createResult = await callWebAdminAPI('createCategory', {
                    name: testCategoryName,
                    icon: '🧪',
                    description: '数据同步测试分类',
                    sort: 999
                });

                let syncReport = `🔄 实时同步测试报告:\n\n`;
                syncReport += `✅ 步骤1: 创建测试分类成功\n`;
                syncReport += `   分类名称: ${testCategoryName}\n`;
                syncReport += `   分类ID: ${createResult.id}\n\n`;

                // 等待一秒后检查数据是否同步
                await new Promise(resolve => setTimeout(resolve, 1000));

                const categories = await callWebAdminAPI('getCategoryList');
                const testCategory = categories.find(cat => cat.name === testCategoryName);

                if (testCategory) {
                    syncReport += `✅ 步骤2: 数据同步验证成功\n`;
                    syncReport += `   在分类列表中找到测试分类\n`;
                    syncReport += `   同步延迟: < 1秒\n\n`;

                    // 清理测试数据
                    try {
                        await callWebAdminAPI('deleteCategory', { id: testCategory._id });
                        syncReport += `✅ 步骤3: 清理测试数据成功\n`;
                        syncReport += `   测试分类已删除\n\n`;
                    } catch (deleteError) {
                        syncReport += `⚠️ 步骤3: 清理测试数据失败\n`;
                        syncReport += `   请手动删除测试分类: ${testCategoryName}\n`;
                        syncReport += `   错误: ${deleteError.message}\n\n`;
                    }
                } else {
                    syncReport += `❌ 步骤2: 数据同步验证失败\n`;
                    syncReport += `   在分类列表中未找到测试分类\n`;
                    syncReport += `   可能的原因: 同步延迟或同步机制异常\n\n`;
                }

                syncReport += `📊 同步测试总结:\n`;
                syncReport += `   创建操作: ${createResult ? '成功' : '失败'}\n`;
                syncReport += `   数据同步: ${testCategory ? '成功' : '失败'}\n`;
                syncReport += `   数据清理: 已尝试\n`;

                showResult('sync-result', syncReport, testCategory ? 'success' : 'error');
            } catch (error) {
                showResult('sync-result', `❌ 实时同步测试失败: ${error.message}`, 'error');
            }
        }

        // 检查分类数据
        async function checkCategories() {
            showLoading('categories-result', '正在检查分类数据...');

            try {
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getCategoryList',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    const categories = result.result.data;
                    const categoryInfo = `✅ 分类数据检查完成
📊 总分类数: ${categories.length}
📋 分类列表:
${categories.map((cat, index) => `${index + 1}. ${cat.name} (${cat.icon}) - 状态: ${cat.status}`).join('\n')}

🔍 详细数据:
${JSON.stringify(categories, null, 2)}`;
                    showResult('categories-result', categoryInfo, 'success');
                } else {
                    showResult('categories-result', `❌ 获取分类数据失败: ${result.result?.error}`, 'error');
                }
            } catch (error) {
                showResult('categories-result', `❌ 检查分类数据出错: ${error.message}`, 'error');
            }
        }

        // 检查分类详细信息
        async function checkCategoriesDetail() {
            showLoading('categories-result', '正在获取详细分类信息...');

            try {
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getCategoryList',
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    const categories = result.result.data;
                    let detailInfo = `📋 分类详细信息 (共${categories.length}个):\n\n`;

                    categories.forEach((cat, index) => {
                        detailInfo += `${index + 1}. 【${cat.name}】\n`;
                        detailInfo += `   ID: ${cat._id}\n`;
                        detailInfo += `   图标: ${cat.icon}\n`;
                        detailInfo += `   描述: ${cat.description || '无'}\n`;
                        detailInfo += `   排序: ${cat.sort || 0}\n`;
                        detailInfo += `   状态: ${cat.status}\n`;
                        detailInfo += `   表情包数量: ${cat.emojiCount || 0}\n`;
                        detailInfo += `   创建时间: ${cat.createTime ? new Date(cat.createTime).toLocaleString() : '未知'}\n`;
                        detailInfo += `   更新时间: ${cat.updateTime ? new Date(cat.updateTime).toLocaleString() : '未知'}\n\n`;
                    });

                    showResult('categories-result', detailInfo, 'success');
                } else {
                    showResult('categories-result', `❌ 获取分类详细信息失败: ${result.result?.error}`, 'error');
                }
            } catch (error) {
                showResult('categories-result', `❌ 检查分类详细信息出错: ${error.message}`, 'error');
            }
        }

        // 检查表情包数据
        async function checkEmojis() {
            showLoading('emojis-result', '正在检查表情包数据...');

            try {
                const result = await tcbApp.callFunction({
                    name: 'webAdminAPI',
                    data: {
                        action: 'getEmojis',
                        data: { page: 1, limit: 10 },
                        adminPassword: 'admin123456'
                    }
                });

                if (result.result && result.result.success) {
                    const emojis = result.result.data;
                    const emojiInfo = `✅ 表情包数据检查完成
📊 获取到的表情包数: ${emojis.length}
📋 表情包列表 (最新10个):
${emojis.map((emoji, index) => `${index + 1}. ${emoji.title} - 分类: ${emoji.category} - 状态: ${emoji.status}`).join('\n')}

🔍 详细数据:
${JSON.stringify(emojis, null, 2)}`;
                    showResult('emojis-result', emojiInfo, 'success');
                } else {
                    showResult('emojis-result', `❌ 获取表情包数据失败: ${result.result?.error}`, 'error');
                }
            } catch (error) {
                showResult('emojis-result', `❌ 检查表情包数据出错: ${error.message}`, 'error');
            }
        }

        // 检查同步状态
        async function checkSyncStatus() {
            showLoading('sync-status-result', '正在检查同步状态...');

            try {
                // 获取本地数据统计
                const categoriesData = JSON.parse(localStorage.getItem('emoji_admin_categories') || '[]');
                const emojisData = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]');
                const bannersData = JSON.parse(localStorage.getItem('emoji_admin_banners') || '[]');

                const localStats = {
                    categories: categoriesData.filter(cat => cat.status === 'show').length,
                    emojis: emojisData.filter(emoji => emoji.status === 'published').length,
                    banners: bannersData.filter(banner => banner.status === 'show').length
                };

                let statusReport = `📊 数据同步状态报告:\n\n`;
                statusReport += `📱 本地数据统计:\n`;
                statusReport += `   分类: ${localStats.categories} 个\n`;
                statusReport += `   表情包: ${localStats.emojis} 个\n`;
                statusReport += `   横幅: ${localStats.banners} 个\n\n`;

                statusReport += `💡 同步建议:\n`;
                if (localStats.categories > 0 || localStats.emojis > 0 || localStats.banners > 0) {
                    statusReport += `您有数据需要同步到云数据库，点击"全量同步"按钮即可！\n`;
                    statusReport += `同步后，小程序就可以获取到这些数据了。`;
                } else {
                    statusReport += `暂无需要同步的数据，请先在管理后台添加分类和表情包。`;
                }

                showResult('sync-status-result', statusReport, 'success');

            } catch (error) {
                showResult('sync-status-result', `❌ 同步状态检查失败: ${error.message}`, 'error');
            }
        }

        // 对比本地与云端数据
        async function compareLocalAndCloud() {
            showLoading('sync-status-result', '正在对比本地与云端数据...');

            try {
                // 获取本地数据
                const localCategories = JSON.parse(localStorage.getItem('emoji_admin_categories') || '[]')
                    .filter(cat => cat.status === 'show');
                const localEmojis = JSON.parse(localStorage.getItem('emoji_admin_emojis') || '[]')
                    .filter(emoji => emoji.status === 'published');

                let compareReport = `🔍 本地与云端数据对比:\n\n`;

                compareReport += `📂 分类数据对比:\n`;
                compareReport += `本地分类 (${localCategories.length}个):\n`;
                localCategories.forEach((cat, index) => {
                    compareReport += `${index + 1}. ${cat.name} (${cat.icon}) - 创建于 ${new Date(cat.createTime).toLocaleDateString()}\n`;
                });

                compareReport += `\n😊 表情包数据对比:\n`;
                compareReport += `本地表情包 (${localEmojis.length}个):\n`;
                localEmojis.slice(0, 10).forEach((emoji, index) => {
                    compareReport += `${index + 1}. ${emoji.title} - 分类: ${emoji.category} - 创建于 ${new Date(emoji.createTime).toLocaleDateString()}\n`;
                });

                if (localEmojis.length > 10) {
                    compareReport += `... 还有 ${localEmojis.length - 10} 个表情包\n`;
                }

                compareReport += `\n💡 数据同步建议:\n`;
                if (localCategories.length > 0 || localEmojis.length > 0) {
                    compareReport += `您有 ${localCategories.length} 个分类和 ${localEmojis.length} 个表情包需要同步到云数据库\n`;
                    compareReport += `点击"全量同步"按钮即可将这些数据同步到云数据库，让小程序可以获取到！`;
                } else {
                    compareReport += `暂无需要同步的数据，请先在管理后台添加分类和表情包`;
                }

                showResult('sync-status-result', compareReport, 'success');

            } catch (error) {
                showResult('sync-status-result', `❌ 数据对比失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
