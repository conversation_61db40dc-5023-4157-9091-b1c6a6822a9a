<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V1.0 部署和测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .section {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            color: #374151;
        }

        .step {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #d1d5db;
            background: #f9fafb;
        }

        .step.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .step.failed {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .step.running {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }

        .step-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .step-content {
            font-size: 14px;
            color: #6b7280;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .btn-success {
            background: #10b981;
        }

        .btn-danger {
            background: #ef4444;
        }

        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #f3f4f6;
            color: #374151;
        }

        .status-running {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        .deployment-guide {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .deployment-guide h3 {
            color: #92400e;
            margin-bottom: 10px;
        }

        .deployment-guide ol {
            margin-left: 20px;
        }

        .deployment-guide li {
            margin-bottom: 8px;
            color: #78350f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">V1.0 部署和测试 - 按任务清单执行</h1>
        
        <!-- 部署指南 -->
        <div class="deployment-guide">
            <h3>📋 当前任务：2.1.7 部署云函数并测试基本功能</h3>
            <p><strong>重要提示：</strong>由于我们在Web环境中，无法直接部署云函数。请按照以下步骤手动部署：</p>
            <ol>
                <li>打开微信开发者工具</li>
                <li>导入项目，选择 <code>v1.0-project</code> 目录</li>
                <li>右键点击 <code>cloudfunctions/loginAPI</code> → 上传并部署：云端安装依赖</li>
                <li>右键点击 <code>cloudfunctions/webAdminAPI</code> → 上传并部署：云端安装依赖</li>
                <li>部署完成后，回到此页面进行功能测试</li>
            </ol>
        </div>

        <!-- 部署状态检查 -->
        <div class="section">
            <h2 class="section-title">🚀 部署状态检查</h2>
            
            <div class="step" id="step-loginapi-deploy">
                <div class="step-title">
                    <span class="status status-pending" id="status-loginapi">待检查</span>
                    2.1.7.1 检查 loginAPI 云函数部署状态
                </div>
                <div class="step-content" id="content-loginapi">等待检查...</div>
            </div>

            <div class="step" id="step-webadminapi-deploy">
                <div class="step-title">
                    <span class="status status-pending" id="status-webadminapi">待检查</span>
                    2.1.7.2 检查 webAdminAPI 云函数部署状态
                </div>
                <div class="step-content" id="content-webadminapi">等待检查...</div>
            </div>

            <div>
                <button class="btn" onclick="checkDeploymentStatus()">检查部署状态</button>
                <button class="btn btn-success" onclick="proceedToTesting()" id="proceedBtn" disabled>继续进行测试</button>
            </div>
        </div>

        <!-- 认证系统集成测试 -->
        <div class="section">
            <h2 class="section-title">🧪 2.3 认证系统集成测试</h2>
            
            <div class="step" id="step-test-login">
                <div class="step-title">
                    <span class="status status-pending" id="status-test-login">待测试</span>
                    2.3.1 测试登录流程
                </div>
                <div class="step-content" id="content-test-login">等待执行...</div>
            </div>

            <div class="step" id="step-test-token">
                <div class="step-title">
                    <span class="status status-pending" id="status-test-token">待测试</span>
                    2.3.2 测试令牌验证
                </div>
                <div class="step-content" id="content-test-token">等待执行...</div>
            </div>

            <div class="step" id="step-test-refresh">
                <div class="step-title">
                    <span class="status status-pending" id="status-test-refresh">待测试</span>
                    2.3.3 测试令牌刷新
                </div>
                <div class="step-content" id="content-test-refresh">等待执行...</div>
            </div>

            <div class="step" id="step-test-logout">
                <div class="step-title">
                    <span class="status status-pending" id="status-test-logout">待测试</span>
                    2.3.4 测试登出流程
                </div>
                <div class="step-content" id="content-test-logout">等待执行...</div>
            </div>

            <div class="step" id="step-test-error">
                <div class="step-title">
                    <span class="status status-pending" id="status-test-error">待测试</span>
                    2.3.5 测试异常情况处理
                </div>
                <div class="step-content" id="content-test-error">等待执行...</div>
            </div>

            <div>
                <button class="btn" onclick="runAuthTests()" id="authTestBtn" disabled>运行认证系统测试</button>
                <button class="btn btn-success" onclick="completeStage2()" id="completeBtn" disabled>完成阶段二</button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="section">
            <h2 class="section-title">📋 测试日志</h2>
            <div class="log" id="testLog">
[等待开始] 请先检查云函数部署状态...
            </div>
        </div>
    </div>

    <!-- CloudBase Web SDK -->
    <script src="https://web.sdk.qcloud.com/tcb-js-sdk/1.10.10/tcb.js"></script>
    
    <!-- 管理器脚本 -->
    <script src="./admin-web/js/auth-manager.js"></script>
    <script src="./admin-web/js/api-manager.js"></script>

    <script>
        // 部署和测试管理器
        class DeploymentTester {
            constructor() {
                this.logElement = document.getElementById('testLog');
                this.deploymentStatus = {
                    loginAPI: false,
                    webAdminAPI: false
                };
                this.testResults = {
                    login: false,
                    token: false,
                    refresh: false,
                    logout: false,
                    error: false
                };
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = {
                    'info': 'ℹ️',
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️'
                }[type] || 'ℹ️';
                
                const logMessage = `[${timestamp}] ${prefix} ${message}\n`;
                this.logElement.textContent += logMessage;
                this.logElement.scrollTop = this.logElement.scrollHeight;
                
                console.log(`[DEPLOY-TEST] ${message}`);
            }

            setStepStatus(stepId, status, content) {
                const stepElement = document.getElementById(stepId);
                const statusElement = document.getElementById(`status-${stepId.replace('step-', '')}`);
                const contentElement = document.getElementById(`content-${stepId.replace('step-', '')}`);
                
                if (stepElement && statusElement && contentElement) {
                    // 清除旧状态
                    stepElement.classList.remove('completed', 'failed', 'running');
                    statusElement.classList.remove('status-pending', 'status-running', 'status-completed', 'status-failed');
                    
                    // 设置新状态
                    stepElement.classList.add(status);
                    statusElement.classList.add(`status-${status}`);
                    statusElement.textContent = {
                        'pending': '待检查',
                        'running': '检查中',
                        'completed': '已完成',
                        'failed': '失败'
                    }[status] || status;
                    
                    contentElement.textContent = content;
                }
            }

            // 检查部署状态
            async checkDeploymentStatus() {
                this.log('开始检查云函数部署状态...');
                
                // 检查 loginAPI
                this.setStepStatus('step-loginapi-deploy', 'running', '正在检查 loginAPI 云函数...');
                
                try {
                    // 尝试调用 loginAPI
                    if (!window.authManager.cloudApp) {
                        await window.authManager.initCloudBase();
                    }
                    
                    const result = await window.authManager.cloudApp.callFunction({
                        name: 'loginAPI',
                        data: {
                            action: 'validateToken',
                            token: 'test-token'
                        }
                    });
                    
                    if (result.result) {
                        this.deploymentStatus.loginAPI = true;
                        this.setStepStatus('step-loginapi-deploy', 'completed', 'loginAPI 云函数部署成功，可以正常调用');
                        this.log('loginAPI 云函数部署检查通过', 'success');
                    } else {
                        throw new Error('云函数返回异常');
                    }
                } catch (error) {
                    this.setStepStatus('step-loginapi-deploy', 'failed', `loginAPI 部署检查失败: ${error.message}`);
                    this.log(`loginAPI 部署检查失败: ${error.message}`, 'error');
                }

                // 检查 webAdminAPI
                this.setStepStatus('step-webadminapi-deploy', 'running', '正在检查 webAdminAPI 云函数...');
                
                try {
                    const result = await window.authManager.cloudApp.callFunction({
                        name: 'webAdminAPI',
                        data: {
                            action: 'getStats',
                            token: 'test-token'
                        }
                    });
                    
                    if (result.result) {
                        this.deploymentStatus.webAdminAPI = true;
                        this.setStepStatus('step-webadminapi-deploy', 'completed', 'webAdminAPI 云函数部署成功，可以正常调用');
                        this.log('webAdminAPI 云函数部署检查通过', 'success');
                    } else {
                        throw new Error('云函数返回异常');
                    }
                } catch (error) {
                    this.setStepStatus('step-webadminapi-deploy', 'failed', `webAdminAPI 部署检查失败: ${error.message}`);
                    this.log(`webAdminAPI 部署检查失败: ${error.message}`, 'error');
                }

                // 检查是否可以继续测试
                if (this.deploymentStatus.loginAPI && this.deploymentStatus.webAdminAPI) {
                    document.getElementById('proceedBtn').disabled = false;
                    document.getElementById('authTestBtn').disabled = false;
                    this.log('所有云函数部署检查通过，可以开始功能测试', 'success');
                } else {
                    this.log('部分云函数部署检查失败，请先完成部署', 'warning');
                }
            }

            // 运行认证系统测试
            async runAuthTests() {
                this.log('开始运行认证系统集成测试...', 'info');
                
                // 2.3.1 测试登录流程
                await this.testLoginFlow();
                
                // 2.3.2 测试令牌验证
                await this.testTokenValidation();
                
                // 2.3.3 测试令牌刷新
                await this.testTokenRefresh();
                
                // 2.3.4 测试登出流程
                await this.testLogoutFlow();
                
                // 2.3.5 测试异常情况处理
                await this.testErrorHandling();
                
                // 检查是否所有测试都通过
                const allPassed = Object.values(this.testResults).every(result => result);
                if (allPassed) {
                    document.getElementById('completeBtn').disabled = false;
                    this.log('🎉 认证系统集成测试全部通过！', 'success');
                } else {
                    this.log('⚠️ 部分测试失败，请检查问题', 'warning');
                }
            }

            async testLoginFlow() {
                this.setStepStatus('step-test-login', 'running', '正在测试登录流程...');
                
                try {
                    // 先确保登出状态
                    await window.authManager.logout();
                    
                    // 测试正确登录
                    const result = await window.authManager.login('admin', 'admin123456');
                    
                    if (result.success && window.authManager.isLoggedIn()) {
                        this.testResults.login = true;
                        this.setStepStatus('step-test-login', 'completed', '登录流程测试通过：正确凭据登录成功');
                        this.log('2.3.1 登录流程测试通过', 'success');
                    } else {
                        throw new Error('登录失败或状态异常');
                    }
                } catch (error) {
                    this.setStepStatus('step-test-login', 'failed', `登录流程测试失败: ${error.message}`);
                    this.log(`2.3.1 登录流程测试失败: ${error.message}`, 'error');
                }
            }

            async testTokenValidation() {
                this.setStepStatus('step-test-token', 'running', '正在测试令牌验证...');
                
                try {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    const result = await window.authManager.validateToken();
                    
                    if (result.success && result.data.adminId === 'admin') {
                        this.testResults.token = true;
                        this.setStepStatus('step-test-token', 'completed', '令牌验证测试通过：令牌有效且信息正确');
                        this.log('2.3.2 令牌验证测试通过', 'success');
                    } else {
                        throw new Error('令牌验证失败或信息不正确');
                    }
                } catch (error) {
                    this.setStepStatus('step-test-token', 'failed', `令牌验证测试失败: ${error.message}`);
                    this.log(`2.3.2 令牌验证测试失败: ${error.message}`, 'error');
                }
            }

            async testTokenRefresh() {
                this.setStepStatus('step-test-refresh', 'running', '正在测试令牌刷新...');
                
                try {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    const oldToken = localStorage.getItem('admin_token');
                    const result = await window.authManager.refreshToken();
                    const newToken = localStorage.getItem('admin_token');
                    
                    if (result.success && oldToken !== newToken) {
                        this.testResults.refresh = true;
                        this.setStepStatus('step-test-refresh', 'completed', '令牌刷新测试通过：令牌已更新');
                        this.log('2.3.3 令牌刷新测试通过', 'success');
                    } else {
                        throw new Error('令牌刷新失败或令牌未更新');
                    }
                } catch (error) {
                    this.setStepStatus('step-test-refresh', 'failed', `令牌刷新测试失败: ${error.message}`);
                    this.log(`2.3.3 令牌刷新测试失败: ${error.message}`, 'error');
                }
            }

            async testLogoutFlow() {
                this.setStepStatus('step-test-logout', 'running', '正在测试登出流程...');
                
                try {
                    // 确保已登录
                    if (!window.authManager.isLoggedIn()) {
                        await window.authManager.login('admin', 'admin123456');
                    }
                    
                    await window.authManager.logout();
                    
                    const isLoggedOut = !window.authManager.isLoggedIn();
                    const tokenCleared = !localStorage.getItem('admin_token');
                    
                    if (isLoggedOut && tokenCleared) {
                        this.testResults.logout = true;
                        this.setStepStatus('step-test-logout', 'completed', '登出流程测试通过：状态已清除');
                        this.log('2.3.4 登出流程测试通过', 'success');
                    } else {
                        throw new Error('登出后状态或令牌未正确清除');
                    }
                } catch (error) {
                    this.setStepStatus('step-test-logout', 'failed', `登出流程测试失败: ${error.message}`);
                    this.log(`2.3.4 登出流程测试失败: ${error.message}`, 'error');
                }
            }

            async testErrorHandling() {
                this.setStepStatus('step-test-error', 'running', '正在测试异常情况处理...');
                
                try {
                    // 测试错误凭据
                    const wrongResult = await window.authManager.login('wronguser', 'wrongpass');
                    
                    if (!wrongResult.success && wrongResult.error.includes('用户名或密码错误')) {
                        this.testResults.error = true;
                        this.setStepStatus('step-test-error', 'completed', '异常处理测试通过：正确拒绝错误凭据');
                        this.log('2.3.5 异常情况处理测试通过', 'success');
                    } else {
                        throw new Error('错误凭据处理异常');
                    }
                } catch (error) {
                    this.setStepStatus('step-test-error', 'failed', `异常处理测试失败: ${error.message}`);
                    this.log(`2.3.5 异常情况处理测试失败: ${error.message}`, 'error');
                }
            }

            completeStage2() {
                this.log('🎉 阶段二：JWT认证系统开发 - 完成！', 'success');
                this.log('✅ 所有认证系统功能测试通过', 'success');
                this.log('📋 下一步：开始阶段三 - 数据库事务系统开发', 'info');
                
                // 更新任务清单状态
                this.updateTaskList();
                
                alert('🎉 阶段二完成！\n\n✅ JWT认证系统开发完成\n✅ 所有功能测试通过\n\n请继续阶段三的开发工作。');
            }

            updateTaskList() {
                // 这里可以更新任务清单文件的状态
                this.log('📋 任务清单状态已更新', 'info');
            }
        }

        // 全局实例
        window.deploymentTester = new DeploymentTester();

        // 全局函数
        function checkDeploymentStatus() {
            window.deploymentTester.checkDeploymentStatus();
        }

        function proceedToTesting() {
            window.deploymentTester.log('✅ 部署检查完成，可以开始功能测试', 'success');
            document.getElementById('authTestBtn').disabled = false;
        }

        function runAuthTests() {
            window.deploymentTester.runAuthTests();
        }

        function completeStage2() {
            window.deploymentTester.completeStage2();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.deploymentTester.log('🚀 V1.0 部署和测试工具初始化完成', 'success');
            window.deploymentTester.log('📋 当前任务：2.1.7 部署云函数并测试基本功能', 'info');
        });
    </script>
</body>
</html>
