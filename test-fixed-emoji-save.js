// 测试修复后的表情包保存功能
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testFixedEmojiSave() {
    console.log('🔧 测试修复后的表情包保存功能...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        console.log(`[CONSOLE] ${msg.text()}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 进入表情包管理页面');
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(5000);
        
        console.log('\n📍 创建新表情包');
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        await addEmojiBtn.click();
        await page.waitForTimeout(3000);
        
        // 填写表情包信息
        await page.fill('#emoji-title', '修复测试表情包');
        console.log('✅ 已填写表情包名称');
        
        // 选择分类
        const categoryOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-category');
            if (!select) return [];
            return Array.from(select.options).map(option => ({
                value: option.value,
                text: option.textContent
            }));
        });
        
        if (categoryOptions.length > 1) {
            await page.selectOption('#emoji-category', categoryOptions[1].value);
            console.log(`✅ 已选择分类: ${categoryOptions[1].text}`);
        }
        
        // 设置状态为已发布
        await page.selectOption('#emoji-status', 'published');
        console.log('✅ 已设置状态为已发布');
        
        // 创建测试图片
        const testImagePath = path.join(__dirname, 'test-fix-emoji.png');
        const imageBuffer = await page.evaluate(() => {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(0, 0, 100, 100);
                ctx.fillStyle = '#ffffff';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('FIX', 50, 35);
                ctx.fillText('TEST', 50, 65);
                
                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const arrayBuffer = reader.result;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        resolve(Array.from(uint8Array));
                    };
                    reader.readAsArrayBuffer(blob);
                }, 'image/png');
            });
        });
        
        fs.writeFileSync(testImagePath, Buffer.from(imageBuffer));
        console.log('✅ 测试图片已创建');
        
        // 上传图片
        const fileInput = await page.locator('#emoji-image-file');
        await fileInput.setInputFiles(testImagePath);
        console.log('✅ 已上传测试图片');
        
        console.log('\n📍 保存表情包');
        
        // 保存表情包
        const saveResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveResult.success) {
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(10000); // 等待保存完成
        }
        
        console.log('\n📍 验证保存结果');
        
        // 重新查询数据库数据
        const databaseData = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('emojis');
                
                if (result.success && result.data) {
                    return {
                        success: true,
                        count: result.data.length,
                        latestEmoji: result.data[result.data.length - 1] // 最新的表情包
                    };
                } else {
                    return {
                        success: false,
                        error: result.error || '查询失败'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 保存后数据库查询结果:');
        if (databaseData.success) {
            console.log(`表情包总数: ${databaseData.count}`);
            
            const latest = databaseData.latestEmoji;
            console.log('\n最新表情包数据:');
            console.log(`  ID: ${latest._id}`);
            console.log(`  标题: ${latest.title}`);
            console.log(`  状态: ${latest.status}`);
            console.log(`  分类: ${latest.category}`);
            console.log(`  图片URL: ${latest.imageUrl ? latest.imageUrl.substring(0, 100) : 'N/A'}`);
            console.log(`  创建时间: ${latest.createTime}`);
            
            // 检查修复结果
            const isFixed = latest.title && latest.status && latest.category;
            console.log(`\n🎯 修复结果: ${isFixed ? '✅ 成功' : '🔴 失败'}`);
            
            if (isFixed) {
                console.log('✅ 数据字段不再是undefined');
                if (latest.status === 'published') {
                    console.log('✅ 状态正确保存为published');
                } else {
                    console.log(`🔴 状态保存错误: ${latest.status}`);
                }
                
                if (latest.imageUrl) {
                    console.log('✅ 图片URL正确保存');
                } else {
                    console.log('🔴 图片URL仍然为空');
                }
            } else {
                console.log('🔴 数据字段仍然是undefined，修复失败');
            }
        } else {
            console.log(`查询失败: ${databaseData.error}`);
        }
        
        // 截图
        await page.screenshot({ path: 'test-fixed-emoji-save.png', fullPage: true });
        console.log('\n📸 测试截图已保存: test-fixed-emoji-save.png');
        
        // 清理测试文件
        if (fs.existsSync(testImagePath)) {
            fs.unlinkSync(testImagePath);
        }
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
        return {
            success: true,
            fixed: databaseData.success && databaseData.latestEmoji.title !== undefined
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'test-fix-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行测试
testFixedEmojiSave().then(result => {
    console.log('\n🎯 修复测试最终结果:', result);
}).catch(console.error);
