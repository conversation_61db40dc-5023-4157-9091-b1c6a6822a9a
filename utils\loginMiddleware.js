// utils/loginMiddleware.js - 登录状态检查中间件
// 为需要登录的功能提供统一的登录状态检查

const { AuthManager } = require('./authManager.js')

const LoginMiddleware = {
  /**
   * 检查登录状态，未登录时显示登录弹窗
   * @param {Object} options 配置选项
   * @param {string} options.title 弹窗标题
   * @param {string} options.content 弹窗内容
   * @param {Function} options.onSuccess 登录成功回调
   * @param {Function} options.onCancel 取消登录回调
   * @returns {Promise<boolean>} 是否已登录或登录成功
   */
  async requireLogin(options = {}) {
    const {
      title = '需要登录',
      content = '此功能需要登录后才能使用，是否立即登录？',
      onSuccess,
      onCancel
    } = options
    
    // 如果已经登录，直接返回成功
    if (AuthManager.isLoggedIn) {
      return true
    }
    
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        confirmText: '立即登录',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              const loginResult = await AuthManager.login()
              
              if (loginResult.success) {
                // 登录成功
                if (typeof onSuccess === 'function') {
                  onSuccess(loginResult)
                }
                resolve(true)
              } else {
                // 登录失败
                wx.showToast({
                  title: loginResult.error || '登录失败',
                  icon: 'error'
                })
                resolve(false)
              }
            } catch (error) {
              console.error('登录异常:', error)
              wx.showToast({
                title: '登录异常，请重试',
                icon: 'error'
              })
              resolve(false)
            }
          } else {
            // 用户取消登录
            if (typeof onCancel === 'function') {
              onCancel()
            }
            resolve(false)
          }
        }
      })
    })
  },
  
  /**
   * 点赞功能登录检查
   */
  async requireLoginForLike() {
    return await this.requireLogin({
      title: '登录后点赞',
      content: '登录后可以点赞表情包，并且数据会同步到云端',
      onSuccess: () => {
        wx.showToast({
          title: '登录成功，可以点赞了',
          icon: 'success'
        })
      }
    })
  },
  
  /**
   * 收藏功能登录检查
   */
  async requireLoginForCollect() {
    return await this.requireLogin({
      title: '登录后收藏',
      content: '登录后可以收藏表情包，并且数据会同步到云端',
      onSuccess: () => {
        wx.showToast({
          title: '登录成功，可以收藏了',
          icon: 'success'
        })
      }
    })
  },
  
  /**
   * 下载功能登录检查
   */
  async requireLoginForDownload() {
    return await this.requireLogin({
      title: '登录后下载',
      content: '登录后可以下载表情包，并且会记录下载历史',
      onSuccess: () => {
        wx.showToast({
          title: '登录成功，可以下载了',
          icon: 'success'
        })
      }
    })
  },
  
  /**
   * 个人中心功能登录检查
   */
  async requireLoginForProfile() {
    return await this.requireLogin({
      title: '登录查看个人中心',
      content: '登录后可以查看个人数据统计和管理收藏',
      onSuccess: () => {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      }
    })
  },
  
  /**
   * 通用的功能访问检查
   * @param {string} feature 功能名称
   * @param {Function} callback 登录成功后的回调函数
   * @returns {Promise<boolean>} 是否可以访问功能
   */
  async checkFeatureAccess(feature, callback) {
    const featureConfig = {
      like: {
        title: '登录后点赞',
        content: '登录后可以点赞表情包，数据会同步到云端'
      },
      collect: {
        title: '登录后收藏',
        content: '登录后可以收藏表情包，数据会同步到云端'
      },
      download: {
        title: '登录后下载',
        content: '登录后可以下载表情包，并记录下载历史'
      },
      profile: {
        title: '登录查看个人中心',
        content: '登录后可以查看个人数据统计和管理收藏'
      },
      comment: {
        title: '登录后评论',
        content: '登录后可以发表评论和查看评论历史'
      },
      share: {
        title: '登录后分享',
        content: '登录后可以分享表情包给好友'
      }
    }
    
    const config = featureConfig[feature] || {
      title: '需要登录',
      content: '此功能需要登录后才能使用'
    }
    
    const hasAccess = await this.requireLogin({
      ...config,
      onSuccess: () => {
        if (typeof callback === 'function') {
          callback()
        }
      }
    })
    
    return hasAccess
  },
  
  /**
   * 批量检查多个功能的访问权限
   * @param {Array<string>} features 功能列表
   * @returns {Promise<Object>} 各功能的访问权限结果
   */
  async checkMultipleFeatures(features) {
    const results = {}
    
    // 如果已登录，所有功能都可访问
    if (AuthManager.isLoggedIn) {
      features.forEach(feature => {
        results[feature] = true
      })
      return results
    }
    
    // 未登录时，所有功能都不可访问
    features.forEach(feature => {
      results[feature] = false
    })
    
    return results
  },
  
  /**
   * 获取当前登录状态信息
   */
  getLoginStatus() {
    return {
      isLoggedIn: AuthManager.isLoggedIn,
      userInfo: AuthManager.userInfo,
      openid: AuthManager.openid
    }
  },
  
  /**
   * 显示登录引导提示
   * @param {string} message 提示消息
   */
  showLoginGuide(message = '登录后享受更多功能') {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  },
  
  /**
   * 页面级别的登录检查装饰器
   * 在页面的onLoad或onShow中调用
   * @param {Object} page 页面实例
   * @param {Object} options 配置选项
   */
  decoratePage(page, options = {}) {
    const {
      requireLogin = false,
      loginRedirect = '/pages/profile/profile'
    } = options
    
    // 保存原始的onLoad和onShow方法
    const originalOnLoad = page.onLoad
    const originalOnShow = page.onShow
    
    // 重写onLoad方法
    page.onLoad = function(query) {
      if (requireLogin && !AuthManager.isLoggedIn) {
        // 需要登录但未登录，跳转到登录页面
        wx.redirectTo({
          url: loginRedirect
        })
        return
      }
      
      // 调用原始的onLoad方法
      if (typeof originalOnLoad === 'function') {
        originalOnLoad.call(this, query)
      }
    }
    
    // 重写onShow方法
    page.onShow = function() {
      if (requireLogin && !AuthManager.isLoggedIn) {
        // 需要登录但未登录，跳转到登录页面
        wx.redirectTo({
          url: loginRedirect
        })
        return
      }
      
      // 调用原始的onShow方法
      if (typeof originalOnShow === 'function') {
        originalOnShow.call(this)
      }
    }
  }
}

module.exports = {
  LoginMiddleware
}
