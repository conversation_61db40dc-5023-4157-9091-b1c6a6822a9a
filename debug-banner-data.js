// 调试横幅数据结构
const { chromium } = require('playwright');

async function debugBannerData() {
    console.log('🔍 调试横幅数据结构...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(8000);
        }
        
        console.log('📍 获取原始横幅数据');
        
        // 直接调用CloudAPI获取原始数据
        const rawData = await page.evaluate(async () => {
            try {
                const result = await CloudAPI.database.get('banners');
                return {
                    success: result.success,
                    dataLength: result.data ? result.data.length : 0,
                    rawData: result.data,
                    sampleData: result.data && result.data.length > 0 ? result.data[0] : null
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 原始横幅数据结构:');
        console.log('成功:', rawData.success);
        console.log('数据长度:', rawData.dataLength);
        
        if (rawData.sampleData) {
            console.log('\n📋 示例横幅数据:');
            console.log('完整数据:', JSON.stringify(rawData.sampleData, null, 2));
            
            console.log('\n🔍 数据字段分析:');
            console.log('_id:', rawData.sampleData._id);
            console.log('data字段存在:', !!rawData.sampleData.data);
            
            if (rawData.sampleData.data) {
                console.log('data内容:', JSON.stringify(rawData.sampleData.data, null, 2));
            }
            
            // 检查直接字段
            console.log('直接字段:');
            console.log('  title:', rawData.sampleData.title);
            console.log('  imageUrl:', rawData.sampleData.imageUrl);
            console.log('  status:', rawData.sampleData.status);
            console.log('  priority:', rawData.sampleData.priority);
        }
        
        console.log('\n📍 检查AdminApp中的处理后数据');
        
        const processedData = await page.evaluate(() => {
            return {
                hasBanners: !!AdminApp.data.banners,
                bannersCount: AdminApp.data.banners ? AdminApp.data.banners.length : 0,
                sampleBanner: AdminApp.data.banners && AdminApp.data.banners.length > 0 ? 
                    AdminApp.data.banners[0] : null
            };
        });
        
        console.log('📊 AdminApp处理后数据:');
        console.log('有横幅数据:', processedData.hasBanners);
        console.log('横幅数量:', processedData.bannersCount);
        
        if (processedData.sampleBanner) {
            console.log('\n📋 处理后的示例横幅:');
            console.log('完整数据:', JSON.stringify(processedData.sampleBanner, null, 2));
        }
        
        console.log('\n📍 检查表格渲染问题');
        
        // 检查表格容器
        const tableInfo = await page.evaluate(() => {
            const container = document.getElementById('banner-content');
            const table = container ? container.querySelector('table') : null;
            const tbody = table ? table.querySelector('tbody') : null;
            const rows = tbody ? Array.from(tbody.querySelectorAll('tr')) : [];
            
            return {
                hasContainer: !!container,
                hasTable: !!table,
                hasTbody: !!tbody,
                rowCount: rows.length,
                containerHTML: container ? container.innerHTML.substring(0, 500) + '...' : null
            };
        });
        
        console.log('📊 表格渲染信息:');
        console.log('有容器:', tableInfo.hasContainer);
        console.log('有表格:', tableInfo.hasTable);
        console.log('有tbody:', tableInfo.hasTbody);
        console.log('行数:', tableInfo.rowCount);
        
        if (tableInfo.containerHTML) {
            console.log('\n📋 容器HTML片段:');
            console.log(tableInfo.containerHTML);
        }
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugBannerData().catch(console.error);
