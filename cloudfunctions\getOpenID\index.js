// 云函数入口文件 - 获取当前用户OpenID
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    console.log('获取OpenID请求:', wxContext.OPENID)
    
    // 获取当前用户信息
    let userInfo = null
    try {
      const userResult = await db.collection('users').where({
        openid: wxContext.OPENID
      }).get()
      
      if (userResult.data.length > 0) {
        userInfo = userResult.data[0]
      }
    } catch (dbError) {
      console.log('查询用户信息失败:', dbError.message)
    }
    
    return {
      success: true,
      openid: wxContext.OPENID,
      unionid: wxContext.UNIONID,
      appid: wxContext.APPID,
      env: wxContext.ENV,
      userInfo: userInfo,
      message: 'OpenID获取成功'
    }
  } catch (error) {
    console.error('获取OpenID失败:', error)
    return {
      success: false,
      error: error.message,
      message: '获取OpenID失败'
    }
  }
}
