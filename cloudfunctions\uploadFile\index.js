// 云函数入口文件 - 文件上传处理
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    switch (action) {
      case 'getUploadUrl':
        return await getUploadUrl(data, OPENID)
      case 'confirmUpload':
        return await confirmUpload(data, OPENID)
      case 'deleteFile':
        return await deleteFile(data, OPENID)
      default:
        return {
          success: false,
          error: '未知操作',
          code: 400
        }
    }
  } catch (error) {
    console.error('文件上传操作失败:', error)
    return {
      success: false,
      error: error.message,
      code: 500
    }
  }
}

// 获取上传URL
async function getUploadUrl(data, openid) {
  try {
    const { fileType, fileName, fileSize } = data
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(fileType)) {
      throw new Error('不支持的文件类型')
    }
    
    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024
    if (fileSize > maxSize) {
      throw new Error('文件大小超过限制')
    }
    
    // 生成文件路径
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2)
    const fileExtension = getFileExtension(fileType)
    const cloudPath = `emojis/${timestamp}_${randomStr}.${fileExtension}`
    
    // 获取上传URL
    const result = await cloud.uploadFile({
      cloudPath,
      fileContent: Buffer.alloc(0) // 临时空文件
    })
    
    // 记录上传记录
    await db.collection('upload_records').add({
      data: {
        cloudPath,
        fileName,
        fileType,
        fileSize,
        status: 'pending',
        uploader: openid,
        createTime: new Date()
      }
    })
    
    return {
      success: true,
      data: {
        uploadUrl: result.fileID,
        cloudPath,
        fileId: result.fileID
      }
    }
  } catch (error) {
    throw new Error('获取上传URL失败: ' + error.message)
  }
}

// 确认上传完成
async function confirmUpload(data, openid) {
  try {
    const { cloudPath, fileId } = data
    
    if (!cloudPath || !fileId) {
      throw new Error('缺少必要参数')
    }
    
    // 更新上传记录
    const uploadRecord = await db.collection('upload_records').where({
      cloudPath,
      uploader: openid
    }).get()
    
    if (uploadRecord.data.length === 0) {
      throw new Error('上传记录不存在')
    }
    
    await db.collection('upload_records').doc(uploadRecord.data[0]._id).update({
      data: {
        status: 'completed',
        fileId,
        updateTime: new Date()
      }
    })
    
    // 生成缩略图（如果是图片）
    const thumbnailUrl = await generateThumbnail(fileId)
    
    return {
      success: true,
      data: {
        fileId,
        cloudPath,
        thumbnailUrl,
        url: await getDownloadUrl(fileId)
      }
    }
  } catch (error) {
    throw new Error('确认上传失败: ' + error.message)
  }
}

// 删除文件
async function deleteFile(data, openid) {
  try {
    const { fileId } = data
    
    if (!fileId) {
      throw new Error('文件ID不能为空')
    }
    
    // 验证权限（管理员或文件上传者）
    const uploadRecord = await db.collection('upload_records').where({
      fileId,
      uploader: openid
    }).get()
    
    if (uploadRecord.data.length === 0) {
      // 检查是否为管理员
      const userResult = await db.collection('users').where({
        openid,
        role: 'admin'
      }).get()
      
      if (userResult.data.length === 0) {
        throw new Error('权限不足')
      }
    }
    
    // 删除云存储文件
    await cloud.deleteFile({
      fileList: [fileId]
    })
    
    // 更新记录状态
    await db.collection('upload_records').where({
      fileId
    }).update({
      data: {
        status: 'deleted',
        updateTime: new Date()
      }
    })
    
    return {
      success: true,
      message: '文件删除成功'
    }
  } catch (error) {
    throw new Error('删除文件失败: ' + error.message)
  }
}

// 获取文件扩展名
function getFileExtension(fileType) {
  const typeMap = {
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp'
  }
  return typeMap[fileType] || 'jpg'
}

// 生成缩略图
async function generateThumbnail(fileId) {
  try {
    // 这里可以调用图片处理服务生成缩略图
    // 由于云开发限制，这里返回原图URL
    return await getDownloadUrl(fileId)
  } catch (error) {
    console.error('生成缩略图失败:', error)
    return null
  }
}

// 获取下载URL
async function getDownloadUrl(fileId) {
  try {
    const result = await cloud.getTempFileURL({
      fileList: [fileId]
    })
    
    if (result.fileList && result.fileList.length > 0) {
      return result.fileList[0].tempFileURL
    }
    
    return null
  } catch (error) {
    console.error('获取下载URL失败:', error)
    return null
  }
}