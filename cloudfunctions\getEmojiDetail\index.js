// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { emojiId } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    // 获取表情包详情
    const emojiResult = await db.collection('emojis').doc(emojiId).get()

    if (!emojiResult.data) {
      return {
        success: false,
        error: '表情包不存在'
      }
    }

    const emoji = emojiResult.data

    // 获取分类名称
    if (emoji.category) {
      try {
        const categoryResult = await db.collection('categories').doc(emoji.category).get()
        if (categoryResult.data) {
          emoji.categoryName = categoryResult.data.name
        } else {
          emoji.categoryName = '未分类'
        }
      } catch (error) {
        console.warn('获取分类名称失败:', error)
        emoji.categoryName = '未分类'
      }
    } else {
      emoji.categoryName = '未分类'
    }

    // 增加浏览次数
    await db.collection('emojis').doc(emojiId).update({
      data: {
        views: _.inc(1)
      }
    })

    // 检查用户是否点赞和收藏
    const [likeResult, collectResult] = await Promise.all([
      db.collection('user_likes').where({
        userId: OPENID,
        emojiId: emojiId
      }).get(),
      db.collection('user_collections').where({
        userId: OPENID,
        emojiId: emojiId
      }).get()
    ])

    emoji.isLiked = likeResult.data.length > 0
    emoji.isCollected = collectResult.data.length > 0

    // 转换云存储URL为可下载的HTTPS URL
    if (emoji.imageUrl && emoji.imageUrl.startsWith('cloud://')) {
      try {
        console.log('🔄 开始转换云存储URL:', emoji.imageUrl)

        const fileList = await cloud.getTempFileURL({
          fileList: [emoji.imageUrl]
        })

        console.log('📋 getTempFileURL结果:', JSON.stringify(fileList, null, 2))

        if (fileList.fileList && fileList.fileList.length > 0) {
          const fileInfo = fileList.fileList[0]
          if (fileInfo.status === 0 && fileInfo.tempFileURL) {
            emoji.imageUrl = fileInfo.tempFileURL
            console.log('✅ URL转换成功:', emoji.imageUrl)
          } else {
            console.error('❌ URL转换失败:', {
              status: fileInfo.status,
              errCode: fileInfo.errCode,
              errMsg: fileInfo.errMsg,
              originalUrl: emoji.imageUrl
            })
            // 转换失败时保持原URL
          }
        } else {
          console.warn('⚠️ getTempFileURL返回空结果，使用原URL:', emoji.imageUrl)
        }
      } catch (error) {
        console.error('❌ URL转换异常:', {
          error: error.message,
          stack: error.stack,
          originalUrl: emoji.imageUrl
        })
        // 转换失败时保持原URL，让前端处理
      }
    }

    return {
      success: true,
      data: emoji
    }
  } catch (error) {
    console.error('获取表情包详情失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}