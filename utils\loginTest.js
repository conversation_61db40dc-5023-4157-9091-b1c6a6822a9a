// utils/loginTest.js - 登录功能测试工具
// 用于在开发环境中测试登录功能

const { AuthManager } = require('./authManager.js')
const { DataSync } = require('./dataSync.js')
const { LoginMiddleware } = require('./loginMiddleware.js')

const LoginTest = {
  /**
   * 测试基本登录流程
   */
  async testBasicLogin() {
    console.log('=== 开始测试基本登录流程 ===')

    try {
      // 1. 检查初始状态
      console.log('1. 检查初始登录状态:', AuthManager.isLoggedIn)

      // 2. 执行测试登录（不需要用户手势）
      console.log('2. 开始测试登录...')
      const result = await AuthManager.testLogin()

      if (result.success) {
        console.log('✅ 测试登录成功:', result.userInfo.nickName)
        console.log('   OpenID:', result.openid)
        console.log('   是否为测试登录:', result.isTestLogin)
      } else {
        console.log('❌ 测试登录失败:', result.error)
      }

      // 3. 检查登录后状态
      console.log('3. 登录后状态:', AuthManager.isLoggedIn)
      console.log('   用户信息:', AuthManager.userInfo)

      return result
    } catch (error) {
      console.error('❌ 登录测试异常:', error)
      return { success: false, error: error.message }
    }
  },
  
  /**
   * 测试登录状态检查
   */
  testLoginStatusCheck() {
    console.log('=== 开始测试登录状态检查 ===')
    
    const status = AuthManager.getCurrentUser()
    console.log('当前用户状态:', status)
    
    // 测试本地存储
    const storedUserInfo = wx.getStorageSync('userInfo')
    const storedOpenid = wx.getStorageSync('openid')
    const storedLoginTime = wx.getStorageSync('loginTime')
    
    console.log('本地存储状态:')
    console.log('  用户信息:', storedUserInfo)
    console.log('  OpenID:', storedOpenid)
    console.log('  登录时间:', storedLoginTime ? new Date(storedLoginTime) : null)
    
    return status
  },
  
  /**
   * 测试登录中间件
   */
  async testLoginMiddleware() {
    console.log('=== 开始测试登录中间件 ===')
    
    try {
      // 测试点赞功能登录检查
      console.log('1. 测试点赞功能登录检查...')
      const likeResult = await LoginMiddleware.requireLoginForLike()
      console.log('   点赞功能可用:', likeResult)
      
      // 测试收藏功能登录检查
      console.log('2. 测试收藏功能登录检查...')
      const collectResult = await LoginMiddleware.requireLoginForCollect()
      console.log('   收藏功能可用:', collectResult)
      
      // 测试批量功能检查
      console.log('3. 测试批量功能检查...')
      const features = ['like', 'collect', 'download', 'profile']
      const multiResult = await LoginMiddleware.checkMultipleFeatures(features)
      console.log('   批量功能检查结果:', multiResult)
      
      return {
        like: likeResult,
        collect: collectResult,
        multiple: multiResult
      }
    } catch (error) {
      console.error('❌ 登录中间件测试异常:', error)
      return { success: false, error: error.message }
    }
  },
  
  /**
   * 测试数据同步
   */
  async testDataSync() {
    console.log('=== 开始测试数据同步 ===')
    
    if (!AuthManager.isLoggedIn) {
      console.log('❌ 用户未登录，跳过数据同步测试')
      return { success: false, error: '用户未登录' }
    }
    
    try {
      // 初始化数据同步
      DataSync.init()
      
      // 模拟一些本地数据
      const testData = {
        likedEmojis: ['1', '2', '3'],
        collectedEmojis: ['2', '4', '5'],
        downloadedEmojis: ['1', '3', '6'],
        downloadTimes: {
          '1': new Date().toISOString(),
          '3': new Date().toISOString(),
          '6': new Date().toISOString()
        }
      }
      
      // 保存测试数据到本地存储
      wx.setStorageSync('likedEmojis', testData.likedEmojis)
      wx.setStorageSync('collectedEmojis', testData.collectedEmojis)
      wx.setStorageSync('downloadedEmojis', testData.downloadedEmojis)
      wx.setStorageSync('downloadTimes', testData.downloadTimes)
      
      console.log('1. 测试数据已保存到本地存储')
      
      // 同步到云端
      console.log('2. 开始同步数据到云端...')
      await DataSync.syncLocalDataToCloud()
      console.log('✅ 数据同步完成')
      
      // 测试实时同步
      console.log('3. 测试实时同步...')
      await DataSync.syncUserAction('like', '7', { isLiked: true })
      await DataSync.syncUserAction('collect', '8', { isCollected: true })
      console.log('✅ 实时同步测试完成')
      
      return { success: true, message: '数据同步测试完成' }
    } catch (error) {
      console.error('❌ 数据同步测试异常:', error)
      return { success: false, error: error.message }
    }
  },
  
  /**
   * 测试退出登录
   */
  testLogout() {
    console.log('=== 开始测试退出登录 ===')
    
    console.log('1. 登录前状态:', AuthManager.isLoggedIn)
    
    // 执行退出登录
    AuthManager.logout()
    
    console.log('2. 退出后状态:', AuthManager.isLoggedIn)
    console.log('3. 用户信息:', AuthManager.userInfo)
    
    // 检查本地存储是否清除
    const storedUserInfo = wx.getStorageSync('userInfo')
    const storedOpenid = wx.getStorageSync('openid')
    
    console.log('4. 本地存储清除状态:')
    console.log('   用户信息:', storedUserInfo)
    console.log('   OpenID:', storedOpenid)
    
    return {
      isLoggedIn: AuthManager.isLoggedIn,
      localDataCleared: !storedUserInfo && !storedOpenid
    }
  },
  
  /**
   * 运行完整测试套件
   */
  async runFullTest() {
    console.log('🚀 开始运行完整登录功能测试套件')
    console.log('=====================================')
    
    const results = {}
    
    try {
      // 1. 测试基本登录
      results.basicLogin = await this.testBasicLogin()
      
      // 2. 测试登录状态检查
      results.statusCheck = this.testLoginStatusCheck()
      
      // 3. 测试登录中间件
      results.middleware = await this.testLoginMiddleware()
      
      // 4. 测试数据同步
      results.dataSync = await this.testDataSync()
      
      // 5. 测试退出登录
      results.logout = this.testLogout()
      
      console.log('=====================================')
      console.log('🎉 完整测试套件运行完成')
      console.log('测试结果汇总:', results)
      
      return results
    } catch (error) {
      console.error('❌ 测试套件运行异常:', error)
      return { success: false, error: error.message }
    }
  },
  
  /**
   * 清理测试数据
   */
  cleanupTestData() {
    console.log('🧹 清理测试数据...')
    
    try {
      // 清除本地存储
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('openid')
      wx.removeStorageSync('loginTime')
      wx.removeStorageSync('likedEmojis')
      wx.removeStorageSync('collectedEmojis')
      wx.removeStorageSync('downloadedEmojis')
      wx.removeStorageSync('downloadTimes')
      
      // 重置AuthManager状态
      AuthManager.clearLoginData()
      
      console.log('✅ 测试数据清理完成')
      return { success: true }
    } catch (error) {
      console.error('❌ 清理测试数据失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = {
  LoginTest
}
