/* pages/offline-test/offline-test.wxss */

.offline-test-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 状态部分 */
.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.status-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #ccc;
}

.status-dot.connected {
  background: #28a745;
}

.status-dot.offline {
  background: #ffc107;
}

.status-dot.error {
  background: #dc3545;
}

.status-text {
  font-size: 28rpx;
  color: #333;
}

/* 测试控制 */
.test-controls {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.test-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.test-btn.primary {
  background: #007aff;
  color: white;
}

.test-btn.primary[disabled] {
  background: #ccc;
}

.test-btn.secondary {
  background: white;
  color: #007aff;
  border: 2rpx solid #007aff;
}

/* 测试结果 */
.test-results {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.result-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.result-status {
  font-size: 32rpx;
}

.result-status.success {
  color: #28a745;
}

.result-status.error {
  color: #dc3545;
}

.result-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-message {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.result-duration {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

/* 说明信息 */
.info-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 底部按钮 */
.bottom-actions {
  margin-top: 48rpx;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
}
