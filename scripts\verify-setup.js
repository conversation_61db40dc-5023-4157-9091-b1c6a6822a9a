/**
 * 环境验证脚本
 * 验证所有必要的配置和依赖
 */

const fs = require('fs')
const path = require('path')

class SetupVerifier {
  constructor() {
    this.errors = []
    this.warnings = []
    this.passed = []
  }

  // 验证Node.js版本
  verifyNodeVersion() {
    console.log('🔍 检查Node.js版本...')
    
    const version = process.version
    const majorVersion = parseInt(version.slice(1).split('.')[0])
    
    if (majorVersion >= 14) {
      this.passed.push(`✅ Node.js版本: ${version}`)
    } else {
      this.errors.push(`❌ Node.js版本过低: ${version}，需要14.0.0或更高版本`)
    }
  }

  // 验证配置文件
  verifyConfigFiles() {
    console.log('🔍 检查配置文件...')
    
    const configFiles = [
      'admin-unified/config/cloud-config.js',
      'config/sync-config.js',
      'admin-unified/config/database-schema.js'
    ]
    
    configFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.passed.push(`✅ 配置文件存在: ${file}`)
        
        // 检查配置文件内容
        try {
          const config = require(path.resolve(file))
          if (file.includes('cloud-config.js')) {
            this.verifyCloudConfig(config)
          }
        } catch (error) {
          this.errors.push(`❌ 配置文件格式错误: ${file} - ${error.message}`)
        }
      } else {
        this.errors.push(`❌ 配置文件缺失: ${file}`)
      }
    })
  }

  // 验证云开发配置
  verifyCloudConfig(config) {
    console.log('🔍 检查云开发配置...')
    
    if (!config.env || config.env === 'your-cloud-env-id') {
      this.errors.push('❌ 请配置正确的云开发环境ID')
    } else {
      this.passed.push(`✅ 云开发环境ID: ${config.env}`)
    }
    
    if (process.env.NODE_ENV === 'production') {
      if (!config.credentials.secretId || config.credentials.secretId === 'your-secret-id') {
        this.errors.push('❌ 生产环境请配置正确的secretId')
      } else {
        this.passed.push('✅ secretId已配置')
      }
      
      if (!config.credentials.secretKey || config.credentials.secretKey === 'your-secret-key') {
        this.errors.push('❌ 生产环境请配置正确的secretKey')
      } else {
        this.passed.push('✅ secretKey已配置')
      }
    } else {
      this.warnings.push('⚠️ 开发环境，跳过密钥验证')
    }
  }

  // 验证依赖包
  verifyDependencies() {
    console.log('🔍 检查依赖包...')
    
    const packageJsonPath = 'admin-unified/package.json'
    const nodeModulesPath = 'admin-unified/node_modules'
    
    if (fs.existsSync(packageJsonPath)) {
      this.passed.push('✅ package.json存在')
      
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
        const requiredDeps = ['wx-server-sdk', 'express', 'cors']
        
        requiredDeps.forEach(dep => {
          if (packageJson.dependencies && packageJson.dependencies[dep]) {
            this.passed.push(`✅ 依赖包已声明: ${dep}`)
          } else {
            this.warnings.push(`⚠️ 依赖包未声明: ${dep}`)
          }
        })
      } catch (error) {
        this.errors.push(`❌ package.json格式错误: ${error.message}`)
      }
    } else {
      this.warnings.push('⚠️ package.json不存在')
    }
    
    if (fs.existsSync(nodeModulesPath)) {
      this.passed.push('✅ node_modules目录存在')
      
      // 检查关键依赖包
      const requiredModules = ['wx-server-sdk', 'express', 'cors']
      requiredModules.forEach(module => {
        if (fs.existsSync(path.join(nodeModulesPath, module))) {
          this.passed.push(`✅ 依赖包已安装: ${module}`)
        } else {
          this.errors.push(`❌ 依赖包未安装: ${module}`)
        }
      })
    } else {
      this.errors.push('❌ node_modules目录不存在，请运行 npm install')
    }
  }

  // 验证目录结构
  verifyDirectoryStructure() {
    console.log('🔍 检查目录结构...')
    
    const requiredDirs = [
      'admin-unified',
      'admin-unified/config',
      'cloudfunctions',
      'pages',
      'utils',
      'config',
      'scripts'
    ]
    
    const requiredFiles = [
      'admin-unified/index-production.html',
      'cloudfunctions/dataAPI/index.js',
      'pages/index/index.js',
      'app.js',
      'project.config.json'
    ]
    
    requiredDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        this.passed.push(`✅ 目录存在: ${dir}`)
      } else {
        this.errors.push(`❌ 目录缺失: ${dir}`)
      }
    })
    
    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.passed.push(`✅ 文件存在: ${file}`)
      } else {
        this.errors.push(`❌ 文件缺失: ${file}`)
      }
    })
  }

  // 验证端口可用性
  async verifyPortAvailability() {
    console.log('🔍 检查端口可用性...')
    
    const net = require('net')
    const port = 8001
    
    return new Promise((resolve) => {
      const server = net.createServer()
      
      server.listen(port, () => {
        server.close(() => {
          this.passed.push(`✅ 端口${port}可用`)
          resolve()
        })
      })
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          this.warnings.push(`⚠️ 端口${port}被占用，可能需要停止现有服务`)
        } else {
          this.errors.push(`❌ 端口${port}检查失败: ${err.message}`)
        }
        resolve()
      })
    })
  }

  // 验证环境变量
  verifyEnvironmentVariables() {
    console.log('🔍 检查环境变量...')
    
    const env = process.env.NODE_ENV || 'development'
    this.passed.push(`✅ 运行环境: ${env}`)
    
    if (env === 'production') {
      const requiredEnvVars = ['CLOUD_SECRET_ID', 'CLOUD_SECRET_KEY']
      
      requiredEnvVars.forEach(envVar => {
        if (process.env[envVar]) {
          this.passed.push(`✅ 环境变量已设置: ${envVar}`)
        } else {
          this.errors.push(`❌ 生产环境缺少环境变量: ${envVar}`)
        }
      })
    }
  }

  // 运行所有验证
  async runAllVerifications() {
    console.log('========================================')
    console.log('🔍 开始环境验证...')
    console.log('========================================')
    console.log()
    
    this.verifyNodeVersion()
    this.verifyConfigFiles()
    this.verifyDependencies()
    this.verifyDirectoryStructure()
    this.verifyEnvironmentVariables()
    await this.verifyPortAvailability()
    
    console.log()
    console.log('========================================')
    console.log('📊 验证结果')
    console.log('========================================')
    
    if (this.passed.length > 0) {
      console.log()
      console.log('✅ 通过的检查:')
      this.passed.forEach(msg => console.log(`  ${msg}`))
    }
    
    if (this.warnings.length > 0) {
      console.log()
      console.log('⚠️ 警告:')
      this.warnings.forEach(msg => console.log(`  ${msg}`))
    }
    
    if (this.errors.length > 0) {
      console.log()
      console.log('❌ 错误:')
      this.errors.forEach(msg => console.log(`  ${msg}`))
      console.log()
      console.log('请修复上述错误后重新运行验证')
      process.exit(1)
    } else {
      console.log()
      console.log('🎉 所有验证通过！系统可以正常启动')
      process.exit(0)
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const verifier = new SetupVerifier()
  verifier.runAllVerifications().catch(error => {
    console.error('验证过程出错:', error)
    process.exit(1)
  })
}

module.exports = SetupVerifier
