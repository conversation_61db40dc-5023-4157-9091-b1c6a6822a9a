# 部署 syncData 云函数指南

## 1. 云函数文件准备

已创建的文件：
- `cloudfunctions/syncData/index.js` - 云函数主文件
- `cloudfunctions/syncData/package.json` - 依赖配置文件

## 2. 部署步骤

### 方法一：使用微信开发者工具部署

1. 打开微信开发者工具
2. 在项目中找到 `cloudfunctions/syncData` 文件夹
3. 右键点击 `syncData` 文件夹
4. 选择 "上传并部署：云端安装依赖"
5. 等待部署完成

### 方法二：使用命令行部署

```bash
# 进入云函数目录
cd cloudfunctions/syncData

# 安装依赖
npm install

# 使用微信云开发CLI部署
tcb fn deploy syncData --env your-env-id
```

## 3. 验证部署

部署完成后，可以通过以下方式验证：

1. 在微信开发者工具的云开发控制台中查看云函数列表
2. 确认 `syncData` 函数已成功部署
3. 在管理后台点击"🧪 测试同步"按钮进行测试

## 4. 云函数功能说明

`syncData` 云函数支持以下数据类型同步：

- `emojis` - 表情包数据同步
- `categories` - 分类数据同步  
- `banners` - 横幅数据同步
- `config` - 系统配置同步

## 5. 调用示例

```javascript
// 同步表情包数据
const result = await CloudAPI.callFunction('syncData', {
    type: 'emojis',
    data: emojiArray
});

// 同步分类数据
const result = await CloudAPI.callFunction('syncData', {
    type: 'categories', 
    data: categoryArray
});
```

## 6. 错误处理

如果部署失败，请检查：

1. 云开发环境是否已开通
2. 网络连接是否正常
3. 微信开发者工具版本是否最新
4. 云函数代码是否有语法错误

## 7. 数据库权限设置

确保云函数有以下数据库操作权限：

- `emojis` 集合：读写权限
- `categories` 集合：读写权限
- `banners` 集合：读写权限
- `system_config` 集合：读写权限

## 8. 测试数据同步

部署完成后：

1. 在管理后台添加一些测试数据
2. 点击"📱 全量同步"按钮
3. 检查小程序端是否能正确显示同步的数据
4. 使用"🧪 测试同步"功能验证各个数据类型的同步

## 9. 监控和日志

- 在云开发控制台的"云函数"页面可以查看调用日志
- 监控云函数的调用次数和错误率
- 根据日志信息调试同步问题

## 10. 注意事项

- 同步操作会清空现有数据后重新插入，请谨慎操作
- 建议在测试环境先验证功能正常后再在生产环境使用
- 大量数据同步可能需要较长时间，请耐心等待
- 如果同步失败，可以查看控制台日志获取详细错误信息
