// 数据库初始化数据脚本
// 请在云开发控制台的数据库中手动执行

// 1. 初始化分类数据
const categories = [
  {
    _id: 'funny',
    name: '搞笑幽默',
    icon: '😂',
    color: '#FF6B6B',
    description: '搞笑幽默类表情包，让你笑到停不下来',
    sortOrder: 1,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    _id: 'cute',
    name: '可爱萌宠',
    icon: '😍',
    color: '#FF69B4',
    description: '可爱萌宠类表情包，萌化你的心',
    sortOrder: 2,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    _id: 'emotion',
    name: '情感表达',
    icon: '❤️',
    color: '#FF4757',
    description: '情感表达类表情包，传递你的心情',
    sortOrder: 3,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    _id: 'festival',
    name: '节日庆典',
    icon: '🎉',
    color: '#5F27CD',
    description: '节日庆典类表情包，庆祝每个特殊时刻',
    sortOrder: 4,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    _id: 'hot',
    name: '网络热梗',
    icon: '🔥',
    color: '#FFA726',
    description: '网络热梗类表情包，紧跟时代潮流',
    sortOrder: 5,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    _id: '2d',
    name: '动漫二次元',
    icon: '🌟',
    color: '#667EEA',
    description: '动漫二次元类表情包，二次元的快乐',
    sortOrder: 6,
    status: 'active',
    emojiCount: 0,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  }
]

// 2. 初始化表情包数据
const emojis = [
  {
    title: '哈哈哈笑死我了',
    description: '超级搞笑的表情包，让你笑到停不下来',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'funny',
    tags: ['搞笑', '哈哈', '笑死'],
    status: 'published',
    likes: 12340,
    collections: 8500,
    views: 25680,
    downloads: 3702,
    createTime: new Date('2024-01-15T00:00:00Z'),
    updateTime: new Date('2024-01-15T00:00:00Z')
  },
  {
    title: '可爱小猫咪',
    description: '超级可爱的小猫咪表情包',
    imageUrl: 'https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'cute',
    tags: ['可爱', '猫咪', '萌宠'],
    status: 'published',
    likes: 9870,
    collections: 6540,
    views: 18900,
    downloads: 2961,
    createTime: new Date('2024-01-14T00:00:00Z'),
    updateTime: new Date('2024-01-14T00:00:00Z')
  },
  {
    title: '爱你么么哒',
    description: '表达爱意的甜蜜表情包',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'emotion',
    tags: ['爱情', '表白', '么么哒'],
    status: 'published',
    likes: 7560,
    collections: 5430,
    views: 15600,
    downloads: 2268,
    createTime: new Date('2024-01-13T00:00:00Z'),
    updateTime: new Date('2024-01-13T00:00:00Z')
  },
  {
    title: '新年快乐',
    description: '新年祝福表情包，龙年大吉',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'festival',
    tags: ['新年', '快乐', '庆祝'],
    status: 'published',
    likes: 10980,
    collections: 7890,
    views: 22100,
    downloads: 3294,
    createTime: new Date('2024-01-12T00:00:00Z'),
    updateTime: new Date('2024-01-12T00:00:00Z')
  },
  {
    title: '社死现场',
    description: '网络热梗表情包，社死时刻专用',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'hot',
    tags: ['社死', '尴尬', '无语'],
    status: 'published',
    likes: 14560,
    collections: 9870,
    views: 28900,
    downloads: 4368,
    createTime: new Date('2024-01-11T00:00:00Z'),
    updateTime: new Date('2024-01-11T00:00:00Z')
  },
  {
    title: '二次元萌妹',
    description: '可爱的二次元角色表情包',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: '2d',
    tags: ['二次元', '萌妹', '可爱'],
    status: 'published',
    likes: 8230,
    collections: 5670,
    views: 16800,
    downloads: 2469,
    createTime: new Date('2024-01-10T00:00:00Z'),
    updateTime: new Date('2024-01-10T00:00:00Z')
  }
]

// 3. 初始化轮播图数据
const banners = [
  {
    title: '新年表情包',
    subtitle: '龙年大吉，表情包拜年',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600',
    linkType: 'category',
    linkValue: 'festival',
    buttonText: '点击查看详情',
    sortOrder: 1,
    status: 'active',
    startTime: new Date('2024-01-01T00:00:00Z'),
    endTime: new Date('2024-02-29T23:59:59Z'),
    clickCount: 1250,
    impressions: 15600,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-15T00:00:00Z')
  },
  {
    title: '热门推荐',
    subtitle: '本周最火表情包',
    imageUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600',
    linkType: 'category',
    linkValue: 'hot',
    buttonText: '立即查看',
    sortOrder: 2,
    status: 'active',
    startTime: new Date('2024-01-01T00:00:00Z'),
    endTime: new Date('2024-12-31T23:59:59Z'),
    clickCount: 890,
    impressions: 12300,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-15T00:00:00Z')
  },
  {
    title: '可爱动物',
    subtitle: '萌宠表情包合集',
    imageUrl: 'https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg?auto=compress&cs=tinysrgb&w=1920&h=600',
    linkType: 'category',
    linkValue: 'cute',
    buttonText: '查看更多',
    sortOrder: 3,
    status: 'active',
    startTime: new Date('2024-01-01T00:00:00Z'),
    endTime: new Date('2024-12-31T23:59:59Z'),
    clickCount: 567,
    impressions: 9800,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-15T00:00:00Z')
  }
]

// 4. 初始化管理员用户
const adminUser = {
  openid: 'admin_openid_placeholder',
  unionid: 'admin_unionid_placeholder',
  nickName: '系统管理员',
  avatarUrl: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg?auto=compress&cs=tinysrgb&w=200&h=200',
  role: 'admin',
  status: 'active',
  likeCount: 0,
  collectCount: 0,
  downloadCount: 0,
  createTime: new Date('2024-01-01T00:00:00Z'),
  updateTime: new Date('2024-01-01T00:00:00Z'),
  lastLoginTime: new Date('2024-01-15T00:00:00Z')
}

// 5. 系统配置数据
const systemConfigs = [
  {
    key: 'upload_settings',
    value: {
      maxFileSize: 10485760, // 10MB
      allowedFormats: ['jpg', 'png', 'gif', 'webp'],
      imageQuality: 85,
      thumbnailSize: 150,
      watermark: {
        enabled: false,
        text: '表情包小程序',
        position: 'bottom-right',
        opacity: 0.5
      }
    },
    description: '文件上传相关设置',
    category: 'upload',
    isPublic: false,
    version: 1,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  },
  {
    key: 'app_settings',
    value: {
      appName: '表情包小程序',
      version: '1.0.0',
      description: '最好玩的表情包小程序',
      contactEmail: '<EMAIL>',
      privacyPolicy: 'https://example.com/privacy',
      termsOfService: 'https://example.com/terms'
    },
    description: '应用基本设置',
    category: 'app',
    isPublic: true,
    version: 1,
    createTime: new Date('2024-01-01T00:00:00Z'),
    updateTime: new Date('2024-01-01T00:00:00Z')
  }
]

// 导出数据（在云开发控制台中手动执行）
console.log('=== 分类数据 ===')
console.log(JSON.stringify(categories, null, 2))

console.log('\n=== 表情包数据 ===')
console.log(JSON.stringify(emojis, null, 2))

console.log('\n=== 轮播图数据 ===')
console.log(JSON.stringify(banners, null, 2))

console.log('\n=== 管理员用户数据 ===')
console.log(JSON.stringify(adminUser, null, 2))

console.log('\n=== 系统配置数据 ===')
console.log(JSON.stringify(systemConfigs, null, 2))

// 使用说明：
// 1. 在云开发控制台的数据库中创建对应的集合
// 2. 将上述数据手动添加到对应的集合中
// 3. 注意修改管理员用户的 openid 和 unionid 为实际值