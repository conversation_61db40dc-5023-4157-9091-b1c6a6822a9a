// 数据库验证测试脚本
// 验证管理后台更新的分类统计数据是否真的保存到数据库中

console.log('🧪 开始数据库验证测试...')

// 模拟云函数调用
async function callCloudFunction(name, data) {
  try {
    console.log(`☁️ 调用云函数 ${name}:`, data)
    
    // 这里应该是实际的云函数调用
    // 由于我们无法直接调用云函数，我们需要通过其他方式验证
    
    return {
      success: false,
      message: '无法直接调用云函数，需要通过微信开发者工具'
    }
  } catch (error) {
    console.error('❌ 云函数调用失败:', error)
    return { success: false, error: error.message }
  }
}

// 测试获取分类数据
async function testGetCategories() {
  console.log('\n📂 测试获取分类数据...')
  
  const result = await callCloudFunction('dataAPI', {
    action: 'getCategories'
  })
  
  if (result.success && result.data) {
    console.log('✅ 分类数据获取成功:')
    result.data.forEach(category => {
      console.log(`  - ${category.name}: ${category.emojiCount || 0} 个表情包`)
    })
    
    // 检查特定分类
    const testCategories = ['测试', '测试2', '测试3']
    testCategories.forEach(name => {
      const category = result.data.find(c => c.name === name)
      if (category) {
        console.log(`✅ 找到分类 "${name}": ${category.emojiCount || 0} 个表情包`)
      } else {
        console.log(`❌ 未找到分类 "${name}"`)
      }
    })
  } else {
    console.log('❌ 分类数据获取失败:', result.message || result.error)
  }
}

// 测试获取分类统计
async function testGetCategoryStats() {
  console.log('\n📊 测试获取分类统计...')
  
  const result = await callCloudFunction('dataAPI', {
    action: 'getCategoryStats'
  })
  
  if (result.success && result.data) {
    console.log('✅ 分类统计获取成功:')
    result.data.forEach(category => {
      console.log(`  - ${category.name}: ${category.count || 0} 个表情包`)
    })
  } else {
    console.log('❌ 分类统计获取失败:', result.message || result.error)
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始数据库验证测试...\n')
  
  await testGetCategories()
  await testGetCategoryStats()
  
  console.log('\n📝 测试总结:')
  console.log('由于无法直接调用云函数，我们需要:')
  console.log('1. 在微信开发者工具中运行此测试')
  console.log('2. 或者通过管理后台的云函数连接版本验证')
  console.log('3. 或者直接查看小程序端的网络请求日志')
  
  console.log('\n🔍 问题分析:')
  console.log('从截图可以看到:')
  console.log('- 管理后台显示: 测试(2个), 测试2(2个), 测试3(1个)')
  console.log('- 小程序端显示: 测试(0个), 测试2(0个), 测试3(0个)')
  console.log('- 这说明数据同步存在问题')
  
  console.log('\n💡 可能的原因:')
  console.log('1. 管理后台的统计更新没有真正保存到数据库')
  console.log('2. 小程序端获取数据的逻辑有问题')
  console.log('3. 字段映射不一致 (emojiCount vs count)')
  console.log('4. 数据库查询条件不匹配')
  console.log('5. 缓存问题导致数据不同步')
}

// 运行测试
runTests().catch(console.error)
