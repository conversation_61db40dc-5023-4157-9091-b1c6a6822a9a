<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接诊断工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>🔧 云开发连接诊断工具</h1>
    <p>专门用于诊断云开发SDK连接问题</p>

    <div class="card">
        <h3>📋 诊断步骤</h3>
        <button class="btn" onclick="step1()">1. 基础环境检测</button>
        <button class="btn" onclick="step2()">2. CDN连接测试</button>
        <button class="btn" onclick="step3()">3. SDK加载测试</button>
        <button class="btn" onclick="step4()">4. 云环境连接</button>
        <button class="btn" onclick="runAllTests()">🚀 运行全部测试</button>
        <div id="result" class="result"></div>
    </div>

    <div class="card">
        <h3>🔧 手动配置测试</h3>
        <label>云环境ID: </label>
        <input type="text" id="envId" value="cloud1-5g6pvnpl88dc0142" style="width: 300px; padding: 8px; margin: 8px;">
        <br>
        <button class="btn" onclick="testCustomEnv()">测试自定义环境</button>
        <div id="custom-result" class="result"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            resultDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('result').innerHTML = '';
        }

        // 步骤1：基础环境检测
        async function step1() {
            log('🔍 开始基础环境检测...', 'info');
            
            // 检测浏览器基础功能
            if (typeof Promise === 'undefined') {
                log('❌ 浏览器不支持Promise', 'error');
                return false;
            }
            log('✅ Promise支持正常', 'success');

            if (typeof fetch === 'undefined') {
                log('❌ 浏览器不支持fetch', 'error');
                return false;
            }
            log('✅ fetch支持正常', 'success');

            // 检测网络连接
            try {
                log('🌐 测试网络连接...', 'info');
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);
                
                await fetch('https://www.baidu.com', { 
                    method: 'HEAD', 
                    mode: 'no-cors',
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                log('✅ 网络连接正常', 'success');
            } catch (error) {
                log(`❌ 网络连接失败: ${error.message}`, 'error');
                return false;
            }

            log('✅ 基础环境检测通过', 'success');
            return true;
        }

        // 步骤2：CDN连接测试
        async function step2() {
            log('📦 开始CDN连接测试...', 'info');
            
            const cdnUrls = [
                'https://web.sdk.qcloud.com/tcb/1.10.10/tcb.js',
                'https://imgcache.qq.com/qcloud/tcbjs/1.10.10/tcb.js',
                'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@1.7.0/dist/index.umd.js'
            ];

            let availableCdn = null;
            
            for (const url of cdnUrls) {
                try {
                    log(`🔄 测试CDN: ${url}`, 'info');
                    const response = await fetch(url, { 
                        method: 'HEAD',
                        timeout: 10000
                    });
                    
                    if (response.ok) {
                        log(`✅ CDN可用: ${url}`, 'success');
                        availableCdn = url;
                        break;
                    } else {
                        log(`❌ CDN响应异常: ${url} (状态码: ${response.status})`, 'error');
                    }
                } catch (error) {
                    log(`❌ CDN连接失败: ${url} (${error.message})`, 'error');
                }
            }

            if (availableCdn) {
                log(`🎯 找到可用CDN: ${availableCdn}`, 'success');
                window.availableCdn = availableCdn;
                return true;
            } else {
                log('❌ 所有CDN都不可用', 'error');
                return false;
            }
        }

        // 步骤3：SDK加载测试
        async function step3() {
            log('🔧 开始SDK加载测试...', 'info');
            
            if (!window.availableCdn) {
                log('❌ 请先运行CDN连接测试', 'error');
                return false;
            }

            try {
                log(`📥 加载SDK: ${window.availableCdn}`, 'info');
                await loadScript(window.availableCdn);
                
                // 检测SDK是否加载成功
                if (window.tcb) {
                    log('✅ tcb SDK加载成功', 'success');
                    window.cloudSDK = window.tcb;
                    return true;
                } else if (window.cloudbase) {
                    log('✅ cloudbase SDK加载成功', 'success');
                    window.cloudSDK = window.cloudbase;
                    return true;
                } else {
                    log('❌ SDK加载后未找到tcb或cloudbase对象', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ SDK加载失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 步骤4：云环境连接测试
        async function step4() {
            log('☁️ 开始云环境连接测试...', 'info');
            
            if (!window.cloudSDK) {
                log('❌ 请先运行SDK加载测试', 'error');
                return false;
            }

            try {
                const envId = document.getElementById('envId').value || 'cloud1-5g6pvnpl88dc0142';
                log(`🔧 初始化云环境: ${envId}`, 'info');
                
                const app = window.cloudSDK.init({
                    env: envId
                });

                log('🔐 尝试匿名登录...', 'info');
                const auth = app.auth();
                await auth.signInAnonymously();
                
                const user = auth.currentUser;
                if (user && user.uid) {
                    log(`✅ 匿名登录成功，用户ID: ${user.uid}`, 'success');
                    
                    // 测试数据库连接
                    log('🗄️ 测试数据库连接...', 'info');
                    const db = app.database();
                    
                    // 尝试查询一个集合
                    try {
                        const result = await db.collection('categories').limit(1).get();
                        log(`✅ 数据库连接成功，查询到 ${result.data.length} 条记录`, 'success');
                    } catch (dbError) {
                        log(`⚠️ 数据库查询失败: ${dbError.message}`, 'warning');
                        log('💡 这可能是因为集合不存在，但连接是正常的', 'info');
                    }
                    
                    window.cloudApp = app;
                    return true;
                } else {
                    log('❌ 登录失败，未获取到用户信息', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 云环境连接失败: ${error.message}`, 'error');
                log('💡 可能的原因:', 'info');
                log('   1. 云环境ID不正确', 'info');
                log('   2. 云开发服务未开启', 'info');
                log('   3. 网络防火墙阻止连接', 'info');
                return false;
            }
        }

        // 运行全部测试
        async function runAllTests() {
            clearLog();
            log('🚀 开始运行全部诊断测试...', 'info');
            
            const step1Result = await step1();
            if (!step1Result) return;
            
            const step2Result = await step2();
            if (!step2Result) return;
            
            const step3Result = await step3();
            if (!step3Result) return;
            
            const step4Result = await step4();
            if (step4Result) {
                log('🎉 所有测试通过！云开发连接正常', 'success');
                log('✅ 现在可以尝试数据同步测试了', 'success');
            }
        }

        // 测试自定义环境
        async function testCustomEnv() {
            const resultDiv = document.getElementById('custom-result');
            const envId = document.getElementById('envId').value;
            
            if (!envId) {
                resultDiv.textContent = '❌ 请输入云环境ID';
                return;
            }

            resultDiv.textContent = `正在测试环境: ${envId}...\n`;
            
            try {
                if (!window.cloudSDK) {
                    resultDiv.textContent += '❌ 请先运行SDK加载测试\n';
                    return;
                }

                const app = window.cloudSDK.init({ env: envId });
                const auth = app.auth();
                await auth.signInAnonymously();
                
                const user = auth.currentUser;
                if (user) {
                    resultDiv.textContent += `✅ 环境连接成功: ${envId}\n`;
                    resultDiv.textContent += `👤 用户ID: ${user.uid}\n`;
                } else {
                    resultDiv.textContent += `❌ 环境连接失败: ${envId}\n`;
                }
            } catch (error) {
                resultDiv.textContent += `❌ 测试失败: ${error.message}\n`;
            }
        }

        // 动态加载脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                // 检查是否已经加载过
                const existingScript = document.querySelector(`script[src="${src}"]`);
                if (existingScript) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
                document.head.appendChild(script);
            });
        }
    </script>
</body>
</html>
