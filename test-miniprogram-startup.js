// 测试小程序启动问题修复
const { chromium } = require('playwright');
const path = require('path');

async function testMiniprogramStartup() {
    console.log('🔧 测试小程序启动问题修复...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        console.log(`[CONSOLE] ${text}`);
    });
    
    try {
        console.log('\n📍 第一步：验证databaseInit.js文件是否创建成功');
        
        // 检查文件是否存在
        const fs = require('fs');
        const databaseInitPath = path.join(__dirname, 'utils/databaseInit.js');
        
        if (fs.existsSync(databaseInitPath)) {
            console.log('✅ utils/databaseInit.js 文件已创建');
            
            // 读取文件内容验证
            const content = fs.readFileSync(databaseInitPath, 'utf8');
            const hasFullInitialize = content.includes('fullInitialize');
            const hasRepairData = content.includes('repairData');
            const hasValidateDataIntegrity = content.includes('_validateDataIntegrity');
            
            console.log(`  包含fullInitialize方法: ${hasFullInitialize ? '✅' : '🔴'}`);
            console.log(`  包含repairData方法: ${hasRepairData ? '✅' : '🔴'}`);
            console.log(`  包含数据验证方法: ${hasValidateDataIntegrity ? '✅' : '🔴'}`);
            
            if (hasFullInitialize && hasRepairData && hasValidateDataIntegrity) {
                console.log('✅ databaseInit.js 文件内容完整');
            } else {
                console.log('⚠️ databaseInit.js 文件内容可能不完整');
            }
        } else {
            console.log('🔴 utils/databaseInit.js 文件不存在');
            return {
                success: false,
                error: 'databaseInit.js文件未创建'
            };
        }
        
        console.log('\n📍 第二步：检查其他可能缺失的依赖');
        
        // 检查app.js中引用的其他文件
        const appJsPath = path.join(__dirname, 'app.js');
        const appJsContent = fs.readFileSync(appJsPath, 'utf8');
        
        // 提取require语句
        const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
        const requires = [];
        let match;
        
        while ((match = requireRegex.exec(appJsContent)) !== null) {
            requires.push(match[1]);
        }
        
        console.log('📋 app.js中的依赖文件:');
        const missingFiles = [];
        
        for (const req of requires) {
            if (req.startsWith('./')) {
                const filePath = path.join(__dirname, req + '.js');
                const exists = fs.existsSync(filePath);
                console.log(`  ${req}: ${exists ? '✅ 存在' : '🔴 缺失'}`);
                
                if (!exists) {
                    missingFiles.push(req);
                }
            }
        }
        
        if (missingFiles.length > 0) {
            console.log(`\n⚠️ 发现 ${missingFiles.length} 个缺失的文件:`);
            missingFiles.forEach(file => console.log(`  - ${file}`));
        } else {
            console.log('\n✅ 所有依赖文件都存在');
        }
        
        console.log('\n📍 第三步：分析小程序项目结构');
        
        // 检查关键目录和文件
        const criticalPaths = [
            'app.js',
            'app.json',
            'app.wxss',
            'project.config.json',
            'pages',
            'utils',
            'config',
            'components'
        ];
        
        console.log('📋 关键文件和目录检查:');
        for (const criticalPath of criticalPaths) {
            const fullPath = path.join(__dirname, criticalPath);
            const exists = fs.existsSync(fullPath);
            const isDir = exists && fs.statSync(fullPath).isDirectory();
            
            console.log(`  ${criticalPath}: ${exists ? '✅ 存在' : '🔴 缺失'} ${isDir ? '(目录)' : '(文件)'}`);
        }
        
        console.log('\n📍 第四步：检查pages目录结构');
        
        const pagesDir = path.join(__dirname, 'pages');
        if (fs.existsSync(pagesDir)) {
            const pages = fs.readdirSync(pagesDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);
            
            console.log(`📋 发现 ${pages.length} 个页面目录:`);
            
            for (const pageName of pages) {
                const pageDir = path.join(pagesDir, pageName);
                const requiredFiles = ['index.js', 'index.wxml', 'index.wxss', 'index.json'];
                
                console.log(`\n  📄 ${pageName} 页面:`);
                
                for (const file of requiredFiles) {
                    const filePath = path.join(pageDir, file);
                    const exists = fs.existsSync(filePath);
                    console.log(`    ${file}: ${exists ? '✅' : '🔴'}`);
                }
            }
        }
        
        console.log('\n📍 第五步：检查app.json配置');
        
        const appJsonPath = path.join(__dirname, 'app.json');
        if (fs.existsSync(appJsonPath)) {
            const appJsonContent = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
            
            console.log('📋 app.json配置检查:');
            console.log(`  页面数量: ${appJsonContent.pages ? appJsonContent.pages.length : 0}`);
            console.log(`  有tabBar配置: ${!!appJsonContent.tabBar}`);
            console.log(`  有window配置: ${!!appJsonContent.window}`);
            
            if (appJsonContent.pages) {
                console.log('\n  📄 配置的页面:');
                for (const page of appJsonContent.pages) {
                    const pageJsPath = path.join(__dirname, page + '.js');
                    const exists = fs.existsSync(pageJsPath);
                    console.log(`    ${page}: ${exists ? '✅' : '🔴'}`);
                }
            }
        }
        
        console.log('\n📍 第六步：生成修复建议');
        
        const suggestions = [];
        
        if (missingFiles.length > 0) {
            suggestions.push(`创建缺失的依赖文件: ${missingFiles.join(', ')}`);
        }
        
        // 检查是否有页面文件缺失
        if (fs.existsSync(appJsonPath)) {
            const appJsonContent = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
            if (appJsonContent.pages) {
                const missingPages = appJsonContent.pages.filter(page => {
                    const pageJsPath = path.join(__dirname, page + '.js');
                    return !fs.existsSync(pageJsPath);
                });
                
                if (missingPages.length > 0) {
                    suggestions.push(`创建缺失的页面文件: ${missingPages.join(', ')}`);
                }
            }
        }
        
        console.log('💡 修复建议:');
        if (suggestions.length > 0) {
            suggestions.forEach((suggestion, index) => {
                console.log(`  ${index + 1}. ${suggestion}`);
            });
        } else {
            console.log('  ✅ 暂无发现需要修复的问题');
        }
        
        console.log('\n🎯 小程序启动问题修复测试总结:');
        
        const hasAllDependencies = missingFiles.length === 0;
        const hasCriticalFiles = fs.existsSync(path.join(__dirname, 'app.js')) && 
                                fs.existsSync(path.join(__dirname, 'app.json'));
        
        console.log(`依赖文件完整: ${hasAllDependencies ? '✅ 是' : '🔴 否'}`);
        console.log(`关键文件存在: ${hasCriticalFiles ? '✅ 是' : '🔴 否'}`);
        console.log(`databaseInit.js已修复: ✅ 是`);
        
        const overallSuccess = hasAllDependencies && hasCriticalFiles;
        
        console.log(`\n🎯 修复结果: ${overallSuccess ? '🎉 成功' : '⚠️ 需要进一步处理'}`);
        
        if (overallSuccess) {
            console.log('✅ 小程序启动问题已修复，应该可以正常启动了！');
            console.log('💡 请在微信开发者工具中重新编译项目进行验证。');
        } else {
            console.log('⚠️ 仍有问题需要解决，请根据上述建议进行修复。');
        }
        
        return {
            success: true,
            overallSuccess: overallSuccess,
            missingFiles: missingFiles,
            suggestions: suggestions,
            hasCriticalFiles: hasCriticalFiles,
            hasAllDependencies: hasAllDependencies
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 测试完成');
        await browser.close();
    }
}

// 运行测试
testMiniprogramStartup().then(result => {
    console.log('\n🎯 小程序启动问题修复测试最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.overallSuccess) {
        console.log('🎉 小程序启动问题已完全修复！');
    } else if (result.success) {
        console.log('⚠️ 部分问题已修复，仍需进一步处理。');
    } else {
        console.log('❌ 修复测试失败。');
    }
}).catch(console.error);
