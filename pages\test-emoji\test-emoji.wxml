<!--pages/test-emoji/test-emoji.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🧪 表情包显示测试</text>
    <text class="subtitle">当前测试: {{currentTest || '等待开始'}}</text>
  </view>

  <!-- 测试按钮 -->
  <view class="test-buttons">
    <button class="test-btn" bindtap="testCloudFunction">测试云函数</button>
    <button class="test-btn" bindtap="testSetEmojiData">设置测试数据</button>
    <button class="test-btn" bindtap="testWithCloudData">使用云函数数据</button>
    <button class="test-btn" bindtap="testDisplayCondition">检查显示条件</button>
    <button class="test-btn primary" bindtap="runAllTests">运行所有测试</button>
    <button class="test-btn secondary" bindtap="clearLogs">清空日志</button>
  </view>

  <!-- 表情包显示测试区域 -->
  <view class="emoji-test-section">
    <view class="section-title">📱 表情包显示测试</view>
    
    <!-- 显示条件检查 -->
    <view class="condition-check">
      <text class="condition-text">显示条件: searchResults.length === 0 && emojiList.length > 0</text>
      <text class="condition-result">
        {{searchResults.length}} === 0 && {{emojiList.length}} > 0 = 
        {{(searchResults.length === 0 && emojiList.length > 0) ? '✅ true' : '❌ false'}}
      </text>
    </view>

    <!-- 表情包列表 (复制首页的显示逻辑) -->
    <view class="emoji-section" wx:if="{{searchResults.length === 0 && emojiList.length > 0}}">
      <view class="section-header">
        <text class="section-title">🎭 表情包列表 ({{emojiList.length}}个)</text>
      </view>
      
      <view class="emoji-list">
        <view
          class="emoji-item"
          wx:for="{{emojiList}}"
          wx:key="_id"
          bindtap="onEmojiTap"
          data-emoji="{{item}}"
        >
          <view class="emoji-image-container">
            <image
              class="emoji-image"
              src="{{item.imageUrl}}"
              mode="aspectFill"
            />
          </view>
          <view class="emoji-info">
            <text class="emoji-title">{{item.title}}</text>
            <text class="emoji-id">{{item._id}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" wx:if="{{emojiList.length === 0}}">
      <text class="no-data-text">📭 暂无表情包数据</text>
      <text class="no-data-hint">请点击上方按钮进行测试</text>
    </view>

    <!-- 搜索结果干扰提示 -->
    <view class="search-interference" wx:if="{{searchResults.length > 0}}">
      <text class="interference-text">⚠️ 搜索结果不为空，阻止了表情包显示</text>
      <text class="interference-hint">searchResults.length = {{searchResults.length}}</text>
    </view>
  </view>

  <!-- 测试日志 -->
  <view class="log-section">
    <view class="section-title">📝 测试日志</view>
    <scroll-view class="log-container" scroll-y="true">
      <view 
        class="log-item {{item.type}}"
        wx:for="{{testResults}}"
        wx:key="id"
      >
        <text class="log-time">[{{item.time}}]</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      <view wx:if="{{testResults.length === 0}}" class="no-logs">
        <text>暂无日志，请开始测试</text>
      </view>
    </scroll-view>
  </view>
</view>
