<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表情包管理后台 - 微信云版本</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary: #07c160;
            --primary-dark: #06ad56;
            --success: #07c160;
            --warning: #ff976a;
            --danger: #fa5151;
            --gray-50: #f7f8fa;
            --gray-100: #f1f2f4;
            --gray-200: #e5e6eb;
            --gray-500: #8b929d;
            --gray-600: #6d7278;
            --gray-700: #4e5969;
            --gray-800: #1d2129;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary);
            display: block;
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 14px;
        }

        .actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card h3 {
            margin-bottom: 20px;
            color: var(--gray-800);
            font-size: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
        }

        .table tr:hover {
            background: var(--gray-50);
        }

        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
            background: var(--gray-50);
        }

        .status.success {
            border-left-color: var(--success);
            background: #f0f9ff;
        }

        .status.warning {
            border-left-color: var(--warning);
            background: #fffbf0;
        }

        .data-table {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .data-table h3 {
            margin-bottom: 15px;
            color: var(--gray-800);
            border-bottom: 2px solid var(--gray-200);
            padding-bottom: 10px;
        }

        .data-table table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .data-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
        }

        .data-table tr:hover {
            background: var(--gray-50);
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .role-badge.admin {
            background: var(--danger);
            color: white;
        }

        .role-badge.user {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.active {
            background: var(--success);
            color: white;
        }

        .status-badge.inactive {
            background: var(--gray-500);
            color: white;
        }

        .status.error {
            border-left-color: var(--danger);
            background: #fef2f2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: var(--gray-500);
        }

        .result-area {
            background: var(--gray-50);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .env-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--primary);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 9999;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="env-badge">微信云环境</div>

    <div class="header">
        <h1>🎭 表情包管理后台</h1>
        <p>WeChat Cloud Development Admin Dashboard</p>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number" id="totalEmojis">-</span>
                <span class="stat-label">表情包总数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalCategories">-</span>
                <span class="stat-label">分类总数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalUsers">-</span>
                <span class="stat-label">用户总数</span>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="totalDownloads">-</span>
                <span class="stat-label">总下载数</span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
            <button class="btn btn-primary" onclick="loadStats()">
                📊 加载统计数据
            </button>
            <button class="btn btn-success" onclick="loadEmojis()">
                📦 加载表情包
            </button>
            <button class="btn btn-success" onclick="loadCategories()">
                📂 加载分类
            </button>
            <button class="btn btn-info" onclick="loadEmojiList()">
                🎭 查看表情包
            </button>
            <button class="btn btn-secondary" onclick="loadUserList()">
                👥 用户管理
            </button>
            <button class="btn btn-warning" onclick="initTestData()">
                🔧 初始化测试数据
            </button>
            <button class="btn btn-danger" onclick="testCloudAPI()">
                ☁️ 测试云函数API
            </button>
        </div>

        <!-- 系统状态 -->
        <div class="card">
            <h3>📊 系统状态</h3>
            <div class="status success">
                <strong>✅ 微信云开发环境</strong>
                <p>管理后台已部署到微信云开发环境，支持完整的后端功能测试。</p>
            </div>
            <div class="table">
                <table class="table">
                    <tr>
                        <td>部署环境</td>
                        <td><span style="color: var(--success); font-weight: 600;">微信云开发</span></td>
                    </tr>
                    <tr>
                        <td>云函数状态</td>
                        <td><span style="color: var(--success); font-weight: 600;" id="cloudStatus">✅ 已连接</span></td>
                    </tr>
                    <tr>
                        <td>数据库状态</td>
                        <td><span style="color: var(--success); font-weight: 600;" id="dbStatus">✅ 已连接</span></td>
                    </tr>
                    <tr>
                        <td>最后更新</td>
                        <td id="lastUpdate">-</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div class="card">
            <h3>📋 数据管理</h3>
            <div id="dataContent">
                <div class="loading">点击上方按钮加载数据</div>
            </div>
        </div>

        <!-- 测试结果区域 -->
        <div class="card">
            <h3>🧪 测试结果</h3>
            <div id="testResults" class="result-area">等待测试...</div>
        </div>
    </div>

    <!-- 微信云开发SDK -->
    <script src="https://res.wx.qq.com/open/js/cloudbase/1.1.0/cloud.js"></script>

    <script>
        // 微信云开发初始化
        let cloudApp = null;
        let db = null;

        // 初始化云开发
        function initCloud() {
            try {
                cloudApp = cloud.init({
                    env: 'cloud1-5g6pvnpl88dc0142' // 你的云环境ID
                });
                db = cloudApp.database();
                console.log('✅ 微信云开发初始化成功');
                logResult('云开发环境连接成功: cloud1-5g6pvnpl88dc0142', 'success');
                updateLastUpdate();
                return true;
            } catch (error) {
                console.error('❌ 微信云开发初始化失败:', error);
                logResult('微信云开发初始化失败: ' + error.message, 'error');
                return false;
            }
        }

        // 更新最后更新时间
        function updateLastUpdate() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            const element = document.getElementById('lastUpdate');
            if (element) {
                element.textContent = timeString;
            }
        }

        // 记录测试结果
        function logResult(message, type = 'info') {
            const resultArea = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';

            resultArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            resultArea.scrollTop = resultArea.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 加载统计数据
        async function loadStats() {
            logResult('开始加载统计数据...');

            try {
                // 调用云函数获取统计数据
                const result = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getStats'
                    }
                });

                if (result.result && result.result.success) {
                    const stats = result.result.data;

                    // 更新统计显示
                    document.getElementById('totalEmojis').textContent = stats.totalEmojis || 0;
                    document.getElementById('totalCategories').textContent = stats.totalCategories || 0;
                    document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
                    document.getElementById('totalDownloads').textContent = stats.totalDownloads || 0;

                    logResult('统计数据加载成功', 'success');
                    updateLastUpdate();
                } else {
                    throw new Error(result.result?.message || '获取统计数据失败');
                }
            } catch (error) {
                logResult('统计数据加载失败: ' + error.message, 'error');

                // 显示模拟数据
                document.getElementById('totalEmojis').textContent = '156';
                document.getElementById('totalCategories').textContent = '8';
                document.getElementById('totalUsers').textContent = '1234';
                document.getElementById('totalDownloads').textContent = '5678';

                logResult('已显示模拟统计数据', 'info');
            }
        }

        // 加载表情包数据
        async function loadEmojis() {
            logResult('开始加载表情包数据...');
            const container = document.getElementById('dataContent');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 调用云函数获取表情包数据
                const result = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getEmojis',
                        data: { page: 1, limit: 20 }
                    }
                });

                if (result.result && result.result.success) {
                    const emojis = result.result.data;
                    renderEmojiTable(emojis);
                    logResult(`表情包数据加载成功，共 ${emojis.length} 条`, 'success');
                } else {
                    throw new Error(result.result?.message || '获取表情包数据失败');
                }
            } catch (error) {
                logResult('表情包数据加载失败: ' + error.message, 'error');

                // 显示模拟数据
                const mockEmojis = [
                    { _id: '1', title: '开心笑脸', category: '情感类', likes: 123, downloads: 456, status: '已发布' },
                    { _id: '2', title: '可爱猫咪', category: '动物类', likes: 234, downloads: 567, status: '已发布' },
                    { _id: '3', title: '工作加油', category: '生活类', likes: 345, downloads: 678, status: '已发布' }
                ];
                renderEmojiTable(mockEmojis);
                logResult('已显示模拟表情包数据', 'info');
            }
        }

        // 渲染表情包表格
        function renderEmojiTable(emojis) {
            const container = document.getElementById('dataContent');

            if (emojis.length === 0) {
                container.innerHTML = '<div class="loading">暂无表情包数据</div>';
                return;
            }

            let html = `
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>分类</th>
                                <th>点赞数</th>
                                <th>下载数</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            emojis.forEach(emoji => {
                html += `
                    <tr>
                        <td>${emoji._id || emoji.id}</td>
                        <td>${emoji.title}</td>
                        <td>${emoji.category}</td>
                        <td>${emoji.likes || 0}</td>
                        <td>${emoji.downloads || 0}</td>
                        <td><span style="color: var(--success); font-weight: 600;">${emoji.status || '已发布'}</span></td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 加载分类数据
        async function loadCategories() {
            logResult('开始加载分类数据...');
            const container = document.getElementById('dataContent');
            container.innerHTML = '<div class="loading">加载中...</div>';

            try {
                // 调用云函数获取分类数据
                const result = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getCategoryStats'
                    }
                });

                if (result.result && result.result.success) {
                    const categories = result.result.data;
                    renderCategoryTable(categories);
                    logResult(`分类数据加载成功，共 ${categories.length} 条`, 'success');
                } else {
                    throw new Error(result.result?.message || '获取分类数据失败');
                }
            } catch (error) {
                logResult('分类数据加载失败: ' + error.message, 'error');

                // 显示模拟数据
                const mockCategories = [
                    { id: 'emotion', name: '情感类', icon: '😊', count: 25 },
                    { id: 'animal', name: '动物类', icon: '🐱', count: 18 },
                    { id: 'life', name: '生活类', icon: '🏠', count: 32 }
                ];
                renderCategoryTable(mockCategories);
                logResult('已显示模拟分类数据', 'info');
            }
        }

        // 渲染分类表格
        function renderCategoryTable(categories) {
            const container = document.getElementById('dataContent');

            if (categories.length === 0) {
                container.innerHTML = '<div class="loading">暂无分类数据</div>';
                return;
            }

            let html = `
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>图标</th>
                                <th>名称</th>
                                <th>ID</th>
                                <th>表情包数量</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            categories.forEach(category => {
                html += `
                    <tr>
                        <td style="font-size: 20px;">${category.icon}</td>
                        <td>${category.name}</td>
                        <td>${category.id}</td>
                        <td>${category.count || 0} 个</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 加载表情包列表
        async function loadEmojiList() {
            logResult('正在加载表情包列表...');

            try {
                const result = await cloudApp.callFunction({
                    name: 'dataAPI',
                    data: {
                        action: 'getEmojis',
                        page: 1,
                        limit: 20
                    }
                });

                if (result.result && result.result.success) {
                    const emojis = result.result.data || [];
                    logResult(`表情包列表加载成功，共 ${emojis.length} 个`, 'success');

                    // 显示表情包列表
                    displayEmojiList(emojis);
                } else {
                    throw new Error(result.result?.message || '加载表情包列表失败');
                }
            } catch (error) {
                logResult('加载表情包列表失败: ' + error.message, 'error');
            }
        }

        // 显示表情包列表
        function displayEmojiList(emojis) {
            const container = document.getElementById('dataDisplay');
            if (!container) return;

            let html = '<div class="data-table"><h3>📋 表情包列表</h3>';

            if (emojis.length === 0) {
                html += '<p>暂无表情包数据</p>';
            } else {
                html += '<table><thead><tr><th>ID</th><th>标题</th><th>分类</th><th>点赞数</th><th>下载数</th><th>状态</th></tr></thead><tbody>';

                emojis.forEach(emoji => {
                    html += `<tr>
                        <td>${emoji._id || 'N/A'}</td>
                        <td>${emoji.title || 'N/A'}</td>
                        <td>${emoji.category || 'N/A'}</td>
                        <td>${emoji.likes || 0}</td>
                        <td>${emoji.downloads || 0}</td>
                        <td>${emoji.status || 'active'}</td>
                    </tr>`;
                });

                html += '</tbody></table>';
            }

            html += '</div>';
            container.innerHTML = html;
        }

        // 加载用户列表
        async function loadUserList() {
            logResult('正在加载用户列表...');

            try {
                const result = await cloudApp.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'getUsers'
                    }
                });

                if (result.result && result.result.success) {
                    const users = result.result.data || [];
                    logResult(`用户列表加载成功，共 ${users.length} 个用户`, 'success');

                    // 显示用户列表
                    displayUserList(users);
                } else {
                    throw new Error(result.result?.message || '加载用户列表失败');
                }
            } catch (error) {
                logResult('加载用户列表失败: ' + error.message, 'error');
            }
        }

        // 显示用户列表
        function displayUserList(users) {
            const container = document.getElementById('dataDisplay');
            if (!container) return;

            let html = '<div class="data-table"><h3>👥 用户列表</h3>';

            if (users.length === 0) {
                html += '<p>暂无用户数据</p>';
            } else {
                html += '<table><thead><tr><th>昵称</th><th>角色</th><th>状态</th><th>点赞数</th><th>收藏数</th><th>注册时间</th></tr></thead><tbody>';

                users.forEach(user => {
                    const nickname = user.profile?.nickname || '未知用户';
                    const role = user.auth?.role || 'user';
                    const status = user.auth?.status || 'active';
                    const likes = user.stats?.likeCount || 0;
                    const collections = user.stats?.collectCount || 0;
                    const createTime = user.createTime ? new Date(user.createTime).toLocaleDateString() : 'N/A';

                    html += `<tr>
                        <td>${nickname}</td>
                        <td><span class="role-badge ${role}">${role}</span></td>
                        <td><span class="status-badge ${status}">${status}</span></td>
                        <td>${likes}</td>
                        <td>${collections}</td>
                        <td>${createTime}</td>
                    </tr>`;
                });

                html += '</tbody></table>';
            }

            html += '</div>';
            container.innerHTML = html;
        }

        // 初始化测试数据
        async function initTestData() {
            logResult('开始初始化测试数据...');

            try {
                // 调用云函数初始化测试数据
                const result = await cloudApp.callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'initDatabase'
                    }
                });

                if (result.result && result.result.success) {
                    logResult('测试数据初始化成功', 'success');
                    // 重新加载统计数据
                    await loadStats();
                } else {
                    throw new Error(result.result?.message || '初始化测试数据失败');
                }
            } catch (error) {
                logResult('测试数据初始化失败: ' + error.message, 'error');
            }
        }

        // 测试云函数API
        async function testCloudAPI() {
            logResult('开始测试云函数API...');

            const testCases = [
                { name: 'dataAPI', action: 'getStats' },
                { name: 'dataAPI', action: 'getEmojis', data: { page: 1, limit: 5 } },
                { name: 'dataAPI', action: 'getCategoryStats' },
                { name: 'adminAPI', action: 'getSystemInfo' }
            ];

            for (const testCase of testCases) {
                try {
                    logResult(`测试 ${testCase.name}.${testCase.action}...`);

                    const result = await cloudApp.callFunction({
                        name: testCase.name,
                        data: {
                            action: testCase.action,
                            data: testCase.data || {}
                        }
                    });

                    if (result.result && result.result.success) {
                        logResult(`✅ ${testCase.name}.${testCase.action} 测试通过`, 'success');
                    } else {
                        logResult(`❌ ${testCase.name}.${testCase.action} 测试失败: ${result.result?.message || '未知错误'}`, 'error');
                    }
                } catch (error) {
                    logResult(`❌ ${testCase.name}.${testCase.action} 测试异常: ${error.message}`, 'error');
                }
            }

            logResult('云函数API测试完成', 'info');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logResult('微信云管理后台启动中...');

            if (initCloud()) {
                logResult('云开发环境初始化成功，可以开始测试', 'success');
                // 自动加载统计数据
                setTimeout(loadStats, 1000);
            } else {
                logResult('云开发环境初始化失败，请检查配置', 'error');
            }
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            logResult('全局错误: ' + event.error.message, 'error');
        });

        console.log('✅ 微信云管理后台脚本加载完成');
    </script>
</body>
</html>
