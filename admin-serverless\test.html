<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <h1>🧪 管理后台功能测试</h1>
    
    <div class="test-section">
        <h2>📊 仪表盘测试</h2>
        <button class="test-button" onclick="testDashboard()">测试统计数据加载</button>
        <div id="dashboard-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>📁 分类管理测试</h2>
        <button class="test-button" onclick="testCategories()">测试分类列表</button>
        <button class="test-button" onclick="testAddCategory()">测试添加分类</button>
        <div id="categories-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>😊 表情包管理测试</h2>
        <button class="test-button" onclick="testEmojis()">测试表情包列表</button>
        <button class="test-button" onclick="testAddEmoji()">测试添加表情包</button>
        <div id="emojis-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🎯 横幅管理测试</h2>
        <button class="test-button" onclick="testBanners()">测试横幅列表</button>
        <button class="test-button" onclick="testAddBanner()">测试添加横幅</button>
        <div id="banners-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>👥 用户管理测试</h2>
        <button class="test-button" onclick="testUsers()">测试用户列表</button>
        <div id="users-result" class="test-result"></div>
    </div>

    <script src="./js/app.js"></script>
    <script>
        // 测试函数
        async function testDashboard() {
            const resultDiv = document.getElementById('dashboard-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callCloudFunction('adminAPI', { action: 'getStats' });
                resultDiv.textContent = `✅ 测试成功\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testCategories() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callCloudFunction('adminAPI', { action: 'getCategoryList' });
                resultDiv.textContent = `✅ 测试成功\n获取到 ${result.data.length} 个分类\n${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testAddCategory() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.textContent = '正在测试添加分类...';
            
            try {
                const testData = {
                    name: '测试分类_' + Date.now(),
                    icon: '🧪',
                    description: '这是一个测试分类',
                    sort: 999
                };
                
                const result = await callCloudFunction('adminAPI', { action: 'createCategory', data: testData });
                resultDiv.textContent = `✅ 添加分类测试成功\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 添加分类测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testEmojis() {
            const resultDiv = document.getElementById('emojis-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callCloudFunction('adminAPI', { action: 'getEmojiList' });
                resultDiv.textContent = `✅ 测试成功\n获取到 ${result.data.length} 个表情包\n${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testAddEmoji() {
            const resultDiv = document.getElementById('emojis-result');
            resultDiv.textContent = '正在测试添加表情包...';
            
            try {
                const testData = {
                    title: '测试表情包_' + Date.now(),
                    category: '情感表达',
                    imageUrl: 'https://example.com/test.jpg',
                    description: '这是一个测试表情包',
                    tags: ['测试', '表情'],
                    status: 'published'
                };
                
                const result = await callCloudFunction('adminAPI', { action: 'addEmoji', data: testData });
                resultDiv.textContent = `✅ 添加表情包测试成功\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 添加表情包测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testBanners() {
            const resultDiv = document.getElementById('banners-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callCloudFunction('adminAPI', { action: 'getBannerList' });
                resultDiv.textContent = `✅ 测试成功\n获取到 ${result.data.length} 个横幅\n${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testAddBanner() {
            const resultDiv = document.getElementById('banners-result');
            resultDiv.textContent = '正在测试添加横幅...';
            
            try {
                const testData = {
                    title: '测试横幅_' + Date.now(),
                    imageUrl: 'https://example.com/banner.jpg',
                    link: 'https://example.com',
                    priority: 1,
                    status: 'show'
                };
                
                const result = await callCloudFunction('adminAPI', { action: 'addBanner', data: testData });
                resultDiv.textContent = `✅ 添加横幅测试成功\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 添加横幅测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        async function testUsers() {
            const resultDiv = document.getElementById('users-result');
            resultDiv.textContent = '正在测试...';
            
            try {
                const result = await callCloudFunction('adminAPI', { action: 'getUserList' });
                resultDiv.textContent = `✅ 测试成功\n获取到 ${result.data.length} 个用户\n${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 测试失败\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            console.log('🧪 开始自动测试...');
            setTimeout(() => {
                testDashboard();
                setTimeout(() => testCategories(), 1000);
                setTimeout(() => testEmojis(), 2000);
                setTimeout(() => testBanners(), 3000);
                setTimeout(() => testUsers(), 4000);
            }, 1000);
        });
    </script>
</body>
</html>
