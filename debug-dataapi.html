<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>dataAPI调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-button {
            background: #07c160;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .debug-button:hover {
            background: #06ad56;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="debug-section">
        <h1>🔧 dataAPI云函数调试工具</h1>
        <p>这个工具帮助你调试dataAPI云函数的问题</p>
    </div>

    <div class="debug-section">
        <h2>📋 调试步骤</h2>
        
        <div class="step">
            <div class="step-title">步骤1: 检查云函数基本调用</div>
            <button class="debug-button" onclick="testBasicCall()">测试基本调用</button>
            <div id="basic-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="step">
            <div class="step-title">步骤2: 检查数据库连接</div>
            <button class="debug-button" onclick="testDatabaseConnection()">测试数据库连接</button>
            <div id="db-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="step">
            <div class="step-title">步骤3: 检查分类数据</div>
            <button class="debug-button" onclick="testGetCategories()">测试获取分类</button>
            <div id="categories-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="step">
            <div class="step-title">步骤4: 检查表情包数据</div>
            <button class="debug-button" onclick="testGetEmojis()">测试获取表情包</button>
            <div id="emojis-result" class="result-box" style="display: none;"></div>
        </div>

        <div class="step">
            <div class="step-title">步骤5: 检查权限配置</div>
            <button class="debug-button" onclick="testPermissions()">测试权限</button>
            <div id="permissions-result" class="result-box" style="display: none;"></div>
        </div>
    </div>

    <div class="debug-section">
        <h2>📝 调试说明</h2>
        <p><strong>注意：</strong> 这是一个静态HTML页面，无法直接调用微信云函数。</p>
        <p>请按照以下步骤在微信开发者工具中进行调试：</p>
        <ol>
            <li>打开微信开发者工具的控制台</li>
            <li>复制下面的调试代码到控制台执行</li>
            <li>查看返回结果和错误信息</li>
        </ol>
    </div>

    <div class="debug-section">
        <h2>💻 调试代码</h2>
        <div class="result-box">
// 1. 测试基本云函数调用
wx.cloud.callFunction({
  name: 'dataAPI',
  data: { action: 'testDatabaseConnection' }
}).then(res => {
  console.log('✅ 基本调用成功:', res);
}).catch(err => {
  console.error('❌ 基本调用失败:', err);
});

// 2. 测试获取分类数据
wx.cloud.callFunction({
  name: 'dataAPI',
  data: { action: 'getCategories' }
}).then(res => {
  console.log('📋 分类数据:', res);
  if (res.result && res.result.data) {
    console.log(`获取到 ${res.result.data.length} 个分类`);
  }
}).catch(err => {
  console.error('❌ 获取分类失败:', err);
});

// 3. 测试获取表情包数据
wx.cloud.callFunction({
  name: 'dataAPI',
  data: { 
    action: 'getEmojis',
    data: { category: 'all', page: 1, limit: 5 }
  }
}).then(res => {
  console.log('😀 表情包数据:', res);
  if (res.result && res.result.data) {
    console.log(`获取到 ${res.result.data.length} 个表情包`);
    res.result.data.forEach((emoji, index) => {
      console.log(`${index + 1}. ${emoji.title} - ${emoji.imageUrl}`);
    });
  }
}).catch(err => {
  console.error('❌ 获取表情包失败:', err);
});

// 4. 检查数据库中的实际数据
wx.cloud.database().collection('categories').get().then(res => {
  console.log('📊 数据库分类数据:', res);
}).catch(err => {
  console.error('❌ 数据库分类查询失败:', err);
});

wx.cloud.database().collection('emojis').limit(5).get().then(res => {
  console.log('📊 数据库表情包数据:', res);
}).catch(err => {
  console.error('❌ 数据库表情包查询失败:', err);
});
        </div>
    </div>

    <div class="debug-section">
        <h2>🚨 常见问题排查</h2>
        <div class="result-box">
问题1: 云函数调用失败
- 检查云函数是否正确部署
- 检查云函数名称是否正确
- 查看云函数调用日志

问题2: 数据库查询返回空
- 检查数据库集合是否存在
- 检查数据库权限配置
- 验证数据是否正确插入

问题3: 图片链接转换失败
- 检查云存储权限
- 验证fileID格式是否正确
- 查看getTempFileURL调用日志

问题4: 前端显示空白
- 检查前端错误日志
- 验证数据绑定是否正确
- 确认网络请求是否成功
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result-box ${type}`;
            element.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
        }

        function testBasicCall() {
            showResult('basic-result', '请在微信开发者工具控制台中执行调试代码', 'warning');
        }

        function testDatabaseConnection() {
            showResult('db-result', '请在微信开发者工具控制台中执行调试代码', 'warning');
        }

        function testGetCategories() {
            showResult('categories-result', '请在微信开发者工具控制台中执行调试代码', 'warning');
        }

        function testGetEmojis() {
            showResult('emojis-result', '请在微信开发者工具控制台中执行调试代码', 'warning');
        }

        function testPermissions() {
            showResult('permissions-result', '请在微信开发者工具控制台中执行调试代码', 'warning');
        }
    </script>
</body>
</html>
