// 真正的横幅管理深度测试
const { chromium } = require('playwright');

async function realBannerTest() {
    console.log('🎨 开始横幅管理深度测试...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500,
        args: ['--start-maximized']
    });
    
    const context = await browser.newContext({
        viewport: { width: 1920, height: 1080 }
    });
    
    const page = await context.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('ERROR') || text.includes('error') || text.includes('undefined') || text.includes('失败')) {
            console.log('🔴 [ERROR]', text);
        } else if (text.includes('SUCCESS') || text.includes('成功')) {
            console.log('🟢 [SUCCESS]', text);
        }
    });
    
    try {
        console.log('📍 步骤1: 登录管理后台');
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        await page.waitForTimeout(3000);
        
        // 登录
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            console.log('✅ 登录完成');
            await page.waitForTimeout(8000);
        }
        
        console.log('\n📍 步骤2: 进入横幅配置管理');
        
        // 点击横幅配置 - 使用正确的选择器
        const bannerLink = await page.locator('text=🎨 横幅配置').first();
        if (await bannerLink.isVisible()) {
            await bannerLink.click();
            console.log('✅ 已进入横幅配置页面');
            await page.waitForTimeout(3000);
        } else {
            console.log('❌ 未找到横幅配置链接');
            return;
        }
        
        console.log('\n📍 步骤3: 检查现有横幅数据');
        
        // 检查现有横幅数据
        const existingBanners = await page.evaluate(() => {
            // 查找横幅表格或列表
            const bannerRows = Array.from(document.querySelectorAll('table tbody tr, .banner-item'));
            return bannerRows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td, .banner-cell'));
                return {
                    index: index,
                    data: cells.map(cell => cell.textContent?.trim()),
                    hasUndefined: cells.some(cell => cell.textContent?.includes('undefined')),
                    hasImage: !!row.querySelector('img'),
                    imageSource: row.querySelector('img')?.src || null
                };
            });
        });
        
        console.log('📋 现有横幅数据:');
        existingBanners.forEach((banner, index) => {
            console.log(`  ${index + 1}. 数据: [${banner.data.join(', ')}]`);
            console.log(`     有undefined: ${banner.hasUndefined}`);
            console.log(`     有图片: ${banner.hasImage}`);
            console.log(`     图片源: ${banner.imageSource}`);
            
            if (banner.hasUndefined) {
                console.log('🔴 发现undefined数据！');
            }
        });
        
        console.log('\n📍 步骤4: 创建新横幅');
        
        // 点击添加横幅按钮
        const addBannerBtn = await page.locator('text=➕ 添加横幅').first();
        if (await addBannerBtn.isVisible()) {
            await addBannerBtn.click();
            console.log('✅ 已点击添加横幅按钮');
            await page.waitForTimeout(2000);
        } else {
            console.log('❌ 未找到添加横幅按钮');
            return;
        }
        
        // 填写横幅信息
        console.log('📝 填写横幅信息...');
        
        // 横幅标题
        const titleInput = await page.locator('input[placeholder*="标题"], input[name="title"]').first();
        if (await titleInput.isVisible()) {
            await titleInput.fill('测试横幅标题123');
            console.log('✅ 已填写标题: 测试横幅标题123');
        }
        
        // 描述
        const descInput = await page.locator('textarea[placeholder*="描述"], textarea[name="description"]').first();
        if (await descInput.isVisible()) {
            await descInput.fill('这是一个测试横幅的详细描述');
            console.log('✅ 已填写描述');
        }
        
        // 优先级
        const priorityInput = await page.locator('input[name="priority"], input[placeholder*="优先级"]').first();
        if (await priorityInput.isVisible()) {
            await priorityInput.fill('10');
            console.log('✅ 已设置优先级: 10');
        }
        
        // 设置状态为显示（发布）
        const statusSelect = await page.locator('select[name="status"], .status-select').first();
        if (await statusSelect.isVisible()) {
            await statusSelect.selectOption('show');
            console.log('✅ 已设置状态为显示');
        } else {
            // 尝试点击显示选项
            const showOption = await page.locator('text=显示').first();
            if (await showOption.isVisible()) {
                await showOption.click();
                console.log('✅ 已选择显示状态');
            }
        }
        
        // 链接类型
        const linkTypeSelect = await page.locator('select[name="linkType"]').first();
        if (await linkTypeSelect.isVisible()) {
            await linkTypeSelect.selectOption('none');
            console.log('✅ 已设置链接类型为无链接');
        }
        
        console.log('\n📍 步骤5: 上传横幅图片');
        
        // 模拟上传图片
        const fileInput = await page.locator('input[type="file"]').first();
        if (await fileInput.isVisible()) {
            // 创建一个测试图片数据
            const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            
            // 使用JavaScript设置文件
            await page.evaluate((imageData) => {
                const fileInput = document.querySelector('input[type="file"]');
                if (fileInput) {
                    // 创建一个模拟的文件对象
                    const blob = new Blob(['test image data'], { type: 'image/png' });
                    const file = new File([blob], 'test-banner.png', { type: 'image/png' });
                    
                    // 创建文件列表
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    fileInput.files = dataTransfer.files;
                    
                    // 触发change事件
                    fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
            }, testImageData);
            
            console.log('✅ 已模拟上传图片');
            await page.waitForTimeout(3000);
        }
        
        console.log('\n📍 步骤6: 保存横幅');
        
        // 保存横幅
        const saveButton = await page.locator('button:has-text("保存横幅"), button:has-text("保存"), .save-btn').first();
        if (await saveButton.isVisible()) {
            await saveButton.click();
            console.log('✅ 已点击保存按钮');
            await page.waitForTimeout(5000);
        } else {
            console.log('❌ 未找到保存按钮');
        }
        
        console.log('\n📍 步骤7: 验证横幅创建结果');
        
        // 等待页面更新
        await page.waitForTimeout(3000);
        
        // 重新检查横幅列表
        const updatedBanners = await page.evaluate(() => {
            const bannerRows = Array.from(document.querySelectorAll('table tbody tr, .banner-item'));
            return bannerRows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td, .banner-cell'));
                const imageElement = row.querySelector('img');
                
                return {
                    index: index,
                    data: cells.map(cell => cell.textContent?.trim()),
                    hasUndefined: cells.some(cell => cell.textContent?.includes('undefined')),
                    hasImage: !!imageElement,
                    imageSource: imageElement?.src || null,
                    imageAlt: imageElement?.alt || null,
                    statusText: cells.find(cell => 
                        cell.textContent?.includes('显示') || 
                        cell.textContent?.includes('隐藏') ||
                        cell.textContent?.includes('发布') ||
                        cell.textContent?.includes('草稿')
                    )?.textContent?.trim()
                };
            });
        });
        
        console.log('📋 更新后的横幅数据:');
        let hasIssues = false;
        
        updatedBanners.forEach((banner, index) => {
            console.log(`\n  横幅 ${index + 1}:`);
            console.log(`    数据: [${banner.data.join(', ')}]`);
            console.log(`    有undefined: ${banner.hasUndefined}`);
            console.log(`    有图片: ${banner.hasImage}`);
            console.log(`    图片源: ${banner.imageSource}`);
            console.log(`    状态文本: ${banner.statusText}`);
            
            // 检查问题
            if (banner.hasUndefined) {
                console.log('    🔴 问题: 发现undefined数据');
                hasIssues = true;
            }
            
            if (!banner.hasImage && banner.imageSource) {
                console.log('    🔴 问题: 图片预览异常');
                hasIssues = true;
            }
            
            if (banner.statusText && (banner.statusText.includes('草稿') || banner.statusText.includes('draft'))) {
                console.log('    🔴 问题: 状态显示错误 - 应该是显示/发布状态');
                hasIssues = true;
            }
            
            // 检查是否包含我们刚创建的横幅
            if (banner.data.some(cell => cell.includes('测试横幅标题123'))) {
                console.log('    ✅ 找到新创建的横幅');
            }
        });
        
        if (!hasIssues) {
            console.log('\n✅ 横幅功能测试通过 - 未发现问题');
        } else {
            console.log('\n🔴 横幅功能测试发现问题 - 需要修复');
        }
        
        // 截图
        await page.screenshot({ path: 'banner-test-result.png', fullPage: true });
        console.log('\n📸 横幅测试截图已保存: banner-test-result.png');
        
        // 保持浏览器打开供查看
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        
    } catch (error) {
        console.error('❌ 横幅测试过程中出错:', error);
        await page.screenshot({ path: 'banner-test-error.png' });
    } finally {
        await browser.close();
    }
}

// 运行横幅测试
realBannerTest().catch(console.error);
