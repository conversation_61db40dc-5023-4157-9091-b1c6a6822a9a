// 调试分类页面渲染问题
const { chromium } = require('playwright');

async function debugCategoryRendering() {
    console.log('🔍 调试分类页面渲染问题...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类') || text.includes('renderCategoryTable') || text.includes('loadCategories')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 检查AdminApp中的分类数据');
        
        // 检查AdminApp中的数据
        const adminAppData = await page.evaluate(() => {
            return {
                hasCategories: !!AdminApp.data.categories,
                categoriesCount: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                categories: AdminApp.data.categories || [],
                currentPage: AdminApp.data.currentPage
            };
        });
        
        console.log('📊 AdminApp分类数据:');
        console.log('有分类数据:', adminAppData.hasCategories);
        console.log('分类数量:', adminAppData.categoriesCount);
        console.log('当前页面:', adminAppData.currentPage);
        
        if (adminAppData.categories.length > 0) {
            console.log('\n📋 AdminApp中的分类数据:');
            adminAppData.categories.forEach((category, index) => {
                console.log(`分类 ${index + 1}: ${category.name} (ID: ${category._id})`);
            });
        }
        
        console.log('\n📍 进入分类管理页面');
        const categoryLink = await page.locator('text=📂 分类管理').first();
        if (await categoryLink.isVisible()) {
            await categoryLink.click();
            await page.waitForTimeout(5000);
        }
        
        console.log('\n📍 检查页面切换后的状态');
        
        // 检查页面切换后的状态
        const pageState = await page.evaluate(() => {
            return {
                currentPage: AdminApp.data.currentPage,
                categoryContentExists: !!document.getElementById('category-content'),
                categoryContentHTML: document.getElementById('category-content')?.innerHTML?.substring(0, 500) || 'N/A',
                categoryPageVisible: document.getElementById('category-management-page')?.classList.contains('active'),
                dashboardPageVisible: document.getElementById('dashboard-page')?.classList.contains('active')
            };
        });
        
        console.log('📊 页面状态:');
        console.log('当前页面:', pageState.currentPage);
        console.log('category-content存在:', pageState.categoryContentExists);
        console.log('分类页面可见:', pageState.categoryPageVisible);
        console.log('仪表板页面可见:', pageState.dashboardPageVisible);
        console.log('category-content内容预览:', pageState.categoryContentHTML);
        
        console.log('\n📍 手动调用renderCategoryTable');
        
        // 手动调用renderCategoryTable函数
        const renderResult = await page.evaluate(() => {
            try {
                console.log('🔧 手动调用renderCategoryTable...');
                
                if (typeof renderCategoryTable === 'function') {
                    renderCategoryTable(AdminApp.data.categories);
                    
                    return {
                        success: true,
                        categoriesCount: AdminApp.data.categories ? AdminApp.data.categories.length : 0,
                        contentAfterRender: document.getElementById('category-content')?.innerHTML?.substring(0, 500) || 'N/A'
                    };
                } else {
                    return {
                        success: false,
                        error: 'renderCategoryTable函数不存在'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 手动渲染结果:');
        console.log('成功:', renderResult.success);
        if (renderResult.success) {
            console.log('分类数量:', renderResult.categoriesCount);
            console.log('渲染后内容预览:', renderResult.contentAfterRender);
        } else {
            console.log('错误:', renderResult.error);
        }
        
        console.log('\n📍 检查DOM结构');
        
        // 检查DOM结构
        const domStructure = await page.evaluate(() => {
            const categoryPage = document.getElementById('category-management-page');
            const categoryContent = document.getElementById('category-content');
            
            return {
                categoryPageExists: !!categoryPage,
                categoryPageActive: categoryPage?.classList.contains('active'),
                categoryContentExists: !!categoryContent,
                categoryContentParent: categoryContent?.parentElement?.tagName,
                allPageSections: Array.from(document.querySelectorAll('.page-section')).map(section => ({
                    id: section.id,
                    active: section.classList.contains('active')
                }))
            };
        });
        
        console.log('📊 DOM结构:');
        console.log('分类页面存在:', domStructure.categoryPageExists);
        console.log('分类页面激活:', domStructure.categoryPageActive);
        console.log('分类内容容器存在:', domStructure.categoryContentExists);
        console.log('分类内容容器父元素:', domStructure.categoryContentParent);
        console.log('所有页面状态:', domStructure.allPageSections);
        
        // 截图
        await page.screenshot({ path: 'debug-category-rendering.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-category-rendering.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开10秒供查看...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
    } finally {
        await browser.close();
    }
}

// 运行调试
debugCategoryRendering().catch(console.error);
