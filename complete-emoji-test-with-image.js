// 完整的表情包测试（包含真实图片上传）
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function completeEmojiTestWithImage() {
    console.log('🎯 完整的表情包测试（包含真实图片上传）...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1500
    });
    
    const page = await browser.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        console.log(`[CONSOLE] ${msg.text()}`);
    });
    
    // 监听JavaScript错误
    page.on('pageerror', error => {
        console.log(`[PAGE ERROR] ${error.message}`);
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：检查现有表情包数据');
        
        // 进入表情包管理页面
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(5000);
        
        // 等待表格完全加载
        await page.waitForTimeout(5000);

        // 检查表格是否存在
        const tableCheck = await page.evaluate(() => {
            const container = document.querySelector('#emoji-content');
            const table = document.querySelector('#emoji-content table');
            const tbody = document.querySelector('#emoji-content tbody');
            const rows = document.querySelectorAll('#emoji-content tbody tr');
            return {
                containerExists: !!container,
                tableExists: !!table,
                tbodyExists: !!tbody,
                rowCount: rows.length,
                containerHTML: container ? container.innerHTML.substring(0, 500) : 'N/A'
            };
        });

        console.log('表格检查结果:', tableCheck);

        // 检查现有表情包数据
        const existingEmojis = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-content tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const statusCell = cells[7];
                const imageCell = cells[1];
                const img = imageCell ? imageCell.querySelector('img') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent.trim() : 'N/A',
                    hasImage: !!img,
                    imageSrc: img ? img.src : 'N/A',
                    imageAlt: img ? img.alt : 'N/A',
                    imageError: img ? (img.complete && img.naturalHeight === 0) : false
                };
            });
        });
        
        console.log('📊 现有表情包详细分析:');
        console.log(`现有表情包总数: ${existingEmojis.length}`);
        existingEmojis.forEach(emoji => {
            console.log(`\n表情包 ${emoji.index}: ${emoji.name}`);
            console.log(`  状态: ${emoji.status}`);
            console.log(`  有图片元素: ${emoji.hasImage}`);
            console.log(`  图片源: ${emoji.imageSrc}`);
            console.log(`  图片描述: ${emoji.imageAlt}`);
            console.log(`  图片加载失败: ${emoji.imageError}`);
            
            // 分析问题
            if (emoji.status === '草稿') {
                console.log(`  🔴 问题1: 状态显示为草稿`);
            }
            if (!emoji.hasImage) {
                console.log(`  🔴 问题2: 没有图片元素`);
            } else if (emoji.imageSrc === 'N/A' || emoji.imageSrc === '') {
                console.log(`  🔴 问题3: 图片源为空`);
            } else if (emoji.imageError) {
                console.log(`  🔴 问题4: 图片加载失败`);
            }
        });
        
        console.log('\n📍 第二步：创建测试图片文件');
        
        // 创建一个真实的测试图片文件
        const testImagePath = path.join(__dirname, 'test-emoji.png');
        
        // 使用Canvas API创建一个简单的PNG图片
        const imageBuffer = await page.evaluate(() => {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');
                
                // 绘制背景
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(0, 0, 200, 200);
                
                // 绘制边框
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 4;
                ctx.strokeRect(10, 10, 180, 180);
                
                // 绘制文字
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('TEST', 100, 80);
                ctx.fillText('EMOJI', 100, 120);
                ctx.fillText('2024', 100, 160);
                
                // 转换为blob
                canvas.toBlob((blob) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const arrayBuffer = reader.result;
                        const uint8Array = new Uint8Array(arrayBuffer);
                        resolve(Array.from(uint8Array));
                    };
                    reader.readAsArrayBuffer(blob);
                }, 'image/png');
            });
        });
        
        // 保存图片文件
        fs.writeFileSync(testImagePath, Buffer.from(imageBuffer));
        console.log(`✅ 测试图片已创建: ${testImagePath}`);
        
        console.log('\n📍 第三步：创建新表情包');
        
        // 点击添加表情包按钮
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        await addEmojiBtn.click();
        await page.waitForTimeout(3000);
        
        // 检查弹窗内的元素
        const modalElements = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            if (!modal) return { modalExists: false };

            return {
                modalExists: true,
                titleInput: !!modal.querySelector('#emoji-title'),
                categorySelect: !!modal.querySelector('#emoji-category'),
                statusSelect: !!modal.querySelector('#emoji-status'),
                fileInput: !!modal.querySelector('#emoji-image-file'),
                submitBtn: !!modal.querySelector('button[type="submit"]'),
                allInputs: Array.from(modal.querySelectorAll('input, select')).map(el => ({
                    tag: el.tagName,
                    id: el.id,
                    name: el.name,
                    type: el.type
                }))
            };
        });

        console.log('弹窗元素检查:', modalElements);

        // 填写表情包信息
        await page.fill('#emoji-title', '完整测试表情包');
        console.log('✅ 已填写表情包名称');
        
        // 选择分类
        const categoryOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-category');
            if (!select) {
                console.log('❌ 未找到分类选择框 #emoji-category');
                return [];
            }
            return Array.from(select.options).map(option => ({
                value: option.value,
                text: option.textContent
            }));
        });
        
        if (categoryOptions.length > 1) {
            await page.selectOption('#emoji-category', categoryOptions[1].value);
            console.log(`✅ 已选择分类: ${categoryOptions[1].text}`);
        }
        
        // 重点测试：明确设置状态为"已发布"
        console.log('\n🎯 重点测试：设置状态为"已发布"');
        
        const statusBefore = await page.evaluate(() => {
            const select = document.querySelector('#emoji-status');
            return {
                value: select.value,
                text: select.options[select.selectedIndex].textContent
            };
        });
        console.log('设置前状态:', statusBefore);
        
        await page.selectOption('#emoji-status', 'published');
        
        const statusAfter = await page.evaluate(() => {
            const select = document.querySelector('#emoji-status');
            return {
                value: select.value,
                text: select.options[select.selectedIndex].textContent
            };
        });
        console.log('设置后状态:', statusAfter);
        
        // 上传图片文件
        console.log('\n🎯 重点测试：上传真实图片文件');
        
        const fileInput = await page.locator('#emoji-image-file');
        await fileInput.setInputFiles(testImagePath);
        console.log('✅ 已上传测试图片');
        
        // 验证文件上传
        const fileInfo = await page.evaluate(() => {
            const input = document.querySelector('#emoji-image-file');
            const file = input.files[0];
            return file ? {
                name: file.name,
                size: file.size,
                type: file.type
            } : null;
        });
        console.log('上传的文件信息:', fileInfo);
        
        console.log('\n📍 第四步：保存表情包');
        
        // 保存表情包
        const saveResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        console.log('保存操作结果:', saveResult);
        
        if (saveResult.success) {
            console.log('✅ 已点击保存按钮，等待保存完成...');
            await page.waitForTimeout(10000); // 等待保存完成
        }
        
        console.log('\n📍 第五步：验证保存结果');

        // 等待表格重新渲染
        await page.waitForTimeout(8000);

        // 再次检查表格状态
        const updatedTableCheck = await page.evaluate(() => {
            const container = document.querySelector('#emoji-content');
            const table = document.querySelector('#emoji-content table');
            const tbody = document.querySelector('#emoji-content tbody');
            const rows = document.querySelectorAll('#emoji-content tbody tr');
            return {
                containerExists: !!container,
                tableExists: !!table,
                tbodyExists: !!tbody,
                rowCount: rows.length,
                containerHTML: container ? container.innerHTML.substring(0, 500) : 'N/A'
            };
        });

        console.log('保存后表格检查结果:', updatedTableCheck);

        // 检查保存后的表情包列表
        const updatedEmojis = await page.evaluate(() => {
            const rows = Array.from(document.querySelectorAll('#emoji-content tbody tr'));
            return rows.map((row, index) => {
                const cells = Array.from(row.querySelectorAll('td'));
                const nameCell = cells[2];
                const statusCell = cells[7];
                const imageCell = cells[1];
                const img = imageCell ? imageCell.querySelector('img') : null;
                
                return {
                    index: index + 1,
                    name: nameCell ? nameCell.textContent.trim() : 'N/A',
                    status: statusCell ? statusCell.textContent.trim() : 'N/A',
                    isNewEmoji: nameCell ? nameCell.textContent.includes('完整测试表情包') : false,
                    hasImage: !!img,
                    imageSrc: img ? img.src : 'N/A',
                    imageWidth: img ? img.width : 0,
                    imageHeight: img ? img.height : 0,
                    imageComplete: img ? img.complete : false,
                    imageNaturalWidth: img ? img.naturalWidth : 0,
                    imageNaturalHeight: img ? img.naturalHeight : 0
                };
            });
        });
        
        console.log('📊 保存后的表情包列表:');
        console.log(`保存后表情包总数: ${updatedEmojis.length}`);
        console.log(`表情包数量变化: ${existingEmojis.length} -> ${updatedEmojis.length}`);

        let newEmojiFound = false;
        let statusCorrect = false;
        let imagePreviewWorking = false;
        
        updatedEmojis.forEach(emoji => {
            console.log(`\n表情包 ${emoji.index}: ${emoji.name}`);
            console.log(`  状态: ${emoji.status}`);
            console.log(`  是新表情包: ${emoji.isNewEmoji}`);
            console.log(`  有图片元素: ${emoji.hasImage}`);
            console.log(`  图片源: ${emoji.imageSrc}`);
            console.log(`  图片尺寸: ${emoji.imageWidth}x${emoji.imageHeight}`);
            console.log(`  图片完成加载: ${emoji.imageComplete}`);
            console.log(`  图片自然尺寸: ${emoji.imageNaturalWidth}x${emoji.imageNaturalHeight}`);
            
            if (emoji.isNewEmoji) {
                newEmojiFound = true;
                console.log('  ✅ 找到新创建的表情包！');
            } else if (updatedEmojis.length > existingEmojis.length) {
                // 如果表情包数量增加了，但是没有找到包含"完整测试表情包"的名称
                // 可能是名称显示有问题，检查最后一个表情包
                if (emoji.index === updatedEmojis.length) {
                    console.log('  🔍 检查最后一个表情包（可能是新创建的）');
                    newEmojiFound = true;
                }
            }

            if (newEmojiFound && emoji.index === updatedEmojis.length) {
                
                // 检查状态
                if (emoji.status === '已发布') {
                    statusCorrect = true;
                    console.log('  ✅ 状态正确：已发布');
                } else {
                    console.log(`  🔴 状态错误：${emoji.status}（应该是"已发布"）`);
                }

                // 检查图片预览
                if (emoji.hasImage && emoji.imageSrc !== 'N/A' && emoji.imageNaturalWidth > 0) {
                    imagePreviewWorking = true;
                    console.log('  ✅ 图片预览正常工作');
                } else {
                    console.log('  🔴 图片预览有问题');
                    if (!emoji.hasImage) {
                        console.log('    - 没有图片元素');
                    }
                    if (emoji.imageSrc === 'N/A') {
                        console.log('    - 图片源为空');
                    }
                    if (emoji.imageNaturalWidth === 0) {
                        console.log('    - 图片加载失败');
                    }
                }
            }
        });
        
        console.log('\n📊 完整表情包测试结果总结:');
        console.log(`表情包创建: ${newEmojiFound ? '✅ 成功' : '🔴 失败'}`);
        console.log(`状态保存: ${statusCorrect ? '✅ 正确' : '🔴 错误'}`);
        console.log(`图片预览: ${imagePreviewWorking ? '✅ 正常' : '🔴 异常'}`);
        
        if (!statusCorrect || !imagePreviewWorking) {
            console.log('\n🔍 需要深入分析的问题:');
            if (!statusCorrect) {
                console.log('1. 状态保存问题 - 需要检查saveEmoji函数的状态字段处理');
            }
            if (!imagePreviewWorking) {
                console.log('2. 图片预览问题 - 需要检查图片上传和URL生成逻辑');
            }
        }
        
        // 截图
        await page.screenshot({ path: 'complete-emoji-test-with-image.png', fullPage: true });
        console.log('\n📸 完整测试截图已保存: complete-emoji-test-with-image.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开30秒供查看...');
        await page.waitForTimeout(30000);
        
        // 清理测试文件
        if (fs.existsSync(testImagePath)) {
            fs.unlinkSync(testImagePath);
            console.log('🗑️ 测试图片文件已清理');
        }
        
        return {
            success: true,
            emojiCreated: newEmojiFound,
            statusCorrect: statusCorrect,
            imagePreviewWorking: imagePreviewWorking,
            existingEmojisCount: existingEmojis.length,
            newEmojisCount: updatedEmojis.length
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'complete-emoji-test-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        await browser.close();
    }
}

// 运行完整测试
completeEmojiTestWithImage().then(result => {
    console.log('\n🎯 完整表情包测试最终结果:', result);
}).catch(console.error);
