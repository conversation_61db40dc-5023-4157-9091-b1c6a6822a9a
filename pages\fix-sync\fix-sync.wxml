<!--pages/fix-sync/fix-sync.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🔧 修复数据同步问题</text>
  </view>

  <view class="alert alert-danger">
    <text class="alert-title">🚨 问题确认：</text>
    <text class="alert-text">• 管理后台使用的是模拟数据，不是真正的云数据库</text>
    <text class="alert-text">• 在管理后台添加的数据只存在于内存中，小程序无法访问</text>
    <text class="alert-text">• 需要将数据真正写入微信云数据库</text>
  </view>

  <view class="step">
    <text class="step-title">📋 第1步：检查环境</text>
    <button class="btn" bindtap="checkEnvironment">🔍 检查环境</button>
    <view class="result">{{envResult}}</view>
  </view>

  <view class="step">
    <text class="step-title">💥 第2步：强制初始化云数据库</text>
    <button class="btn btn-danger" bindtap="forceInitDatabase">💥 强制初始化数据库</button>
    <view class="result">{{initResult}}</view>
  </view>

  <view class="step">
    <text class="step-title">📦 第3步：添加测试数据</text>
    <button class="btn btn-success" bindtap="initTestData">📦 添加测试数据</button>
    <view class="result">{{testDataResult}}</view>
  </view>

  <view class="step">
    <text class="step-title">✅第4步：验证数据</text>
    <button class="btn" bindtap="verifyData">🧪 验证数据</button>
    <view class="result">{{verifyResult}}</view>
  </view>

  <view class="step">
    <text class="step-title">🚀 一键修复（推荐）</text>
    <button class="btn btn-primary" bindtap="oneClickFix">🚀 一键修复所有问题</button>
  </view>

  <view class="log-section">
    <text class="log-title">📝 操作日志</text>
    <scroll-view class="log-area" scroll-y="true" scroll-top="{{scrollTop}}">
      <text class="log-text">{{logText}}</text>
    </scroll-view>
  </view>
</view>
