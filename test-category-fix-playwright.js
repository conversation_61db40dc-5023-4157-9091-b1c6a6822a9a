// 使用Playwright测试分类数据修复效果
const { chromium } = require('playwright');

async function testCategoryFix() {
    console.log('🔧 测试分类数据修复效果...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // 减慢操作速度以便观察
    });
    
    try {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // 1. 首先测试管理后台
        console.log('📊 步骤1: 测试管理后台分类数据...');
        await testAdminBackend(page);
        
        // 2. 然后测试小程序端（如果有模拟器）
        console.log('📱 步骤2: 测试小程序端分类数据...');
        await testMiniProgram(page);
        
        console.log('✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await browser.close();
    }
}

async function testAdminBackend(page) {
    try {
        // 访问管理后台
        await page.goto('http://localhost:3000');
        await page.waitForTimeout(2000);
        
        // 登录
        await page.fill('input[placeholder="用户名"]', 'admin');
        await page.fill('input[placeholder="密码"]', 'admin123');
        await page.click('button:has-text("登录")');
        await page.waitForTimeout(3000);
        
        // 点击分类管理
        await page.click('text=分类管理');
        await page.waitForTimeout(2000);
        
        // 检查分类数据
        const categories = await page.evaluate(() => {
            const categoryElements = document.querySelectorAll('.category-item');
            return Array.from(categoryElements).map(el => {
                const name = el.querySelector('.category-name')?.textContent || '';
                const count = el.querySelector('.category-count')?.textContent || '';
                return { name, count };
            });
        });
        
        console.log('📊 管理后台分类数据:');
        categories.forEach(cat => {
            console.log(`  - ${cat.name}: ${cat.count}`);
        });
        
        return categories;
        
    } catch (error) {
        console.error('❌ 管理后台测试失败:', error);
        return [];
    }
}

async function testMiniProgram(page) {
    try {
        // 这里需要根据实际情况调整
        // 如果有小程序模拟器或H5版本，可以在这里测试
        console.log('💡 小程序端测试需要在微信开发者工具中进行');
        console.log('请在微信开发者工具控制台中运行以下代码:');
        console.log(`
// 测试修复后的分类数据
async function testCategoryData() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'dataAPI',
      data: { action: 'getCategories' }
    });
    
    if (result.result && result.result.success) {
      console.log('✅ 分类数据获取成功:');
      result.result.data.forEach(category => {
        console.log(\`  - \${category.name}: \${category.emojiCount || 0} 个表情包\`);
      });
    } else {
      console.log('❌ 分类数据获取失败:', result.result?.message);
    }
  } catch (error) {
    console.error('❌ 云函数调用失败:', error);
  }
}

testCategoryData();
        `);
        
    } catch (error) {
        console.error('❌ 小程序端测试失败:', error);
    }
}

// 运行测试
if (require.main === module) {
    testCategoryFix().catch(console.error);
}

module.exports = { testCategoryFix };
