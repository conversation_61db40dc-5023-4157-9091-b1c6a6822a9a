// 完整的管理后台深度测试
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class AdminTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            login: false,
            bannerCreate: false,
            bannerDisplay: false,
            emojiCreate: false,
            emojiDisplay: false,
            categoryCreate: false,
            categoryDisplay: false,
            wechatSync: false,
            issues: []
        };
    }

    async init() {
        console.log('🚀 启动完整的管理后台测试...\n');
        
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1500,
            args: ['--start-maximized']
        });
        
        const context = await this.browser.newContext({
            viewport: { width: 1920, height: 1080 }
        });
        
        this.page = await context.newPage();
        
        // 监听所有控制台消息
        this.page.on('console', msg => {
            const text = msg.text();
            if (text.includes('ERROR') || text.includes('error') || text.includes('undefined') || text.includes('失败')) {
                console.log('🔴 [ERROR]', text);
                this.testResults.issues.push(`Console Error: ${text}`);
            } else if (text.includes('SUCCESS') || text.includes('成功')) {
                console.log('🟢 [SUCCESS]', text);
            } else if (text.includes('WARN') || text.includes('警告')) {
                console.log('🟡 [WARN]', text);
            }
        });

        // 监听网络错误
        this.page.on('response', response => {
            if (response.status() >= 400) {
                console.log(`🔴 [HTTP ERROR] ${response.status()} - ${response.url()}`);
                this.testResults.issues.push(`HTTP Error: ${response.status()} - ${response.url()}`);
            }
        });
    }

    async login() {
        console.log('📍 步骤1: 登录管理后台');
        
        try {
            await this.page.goto('http://localhost:9001/main.html', { 
                waitUntil: 'networkidle',
                timeout: 30000 
            });
            
            await this.page.waitForTimeout(3000);
            
            // 检查登录表单
            const usernameInput = await this.page.locator('input[type="text"]').first();
            const passwordInput = await this.page.locator('input[type="password"]').first();
            const loginButton = await this.page.locator('button:has-text("登录")').first();
            
            if (await usernameInput.isVisible()) {
                await usernameInput.fill('admin');
                await passwordInput.fill('admin123');
                await loginButton.click();
                
                console.log('✅ 登录信息已提交');
                
                // 等待登录完成
                await this.page.waitForTimeout(8000);
                
                // 验证登录成功 - 检查多个可能的元素
                const dashboardVisible = await this.page.locator('text=仪表板').isVisible().catch(() => false);
                const categoryManageVisible = await this.page.locator('text=分类管理').isVisible().catch(() => false);
                const bannerManageVisible = await this.page.locator('text=横幅管理').isVisible().catch(() => false);
                const emojiManageVisible = await this.page.locator('text=表情包管理').isVisible().catch(() => false);
                const adminTitleVisible = await this.page.locator('text=表情包管理后台').isVisible().catch(() => false);

                console.log('🔍 检查登录状态:');
                console.log(`  仪表板: ${dashboardVisible}`);
                console.log(`  分类管理: ${categoryManageVisible}`);
                console.log(`  横幅管理: ${bannerManageVisible}`);
                console.log(`  表情包管理: ${emojiManageVisible}`);
                console.log(`  管理后台标题: ${adminTitleVisible}`);

                if (dashboardVisible || categoryManageVisible || bannerManageVisible || emojiManageVisible || adminTitleVisible) {
                    console.log('✅ 登录成功');
                    this.testResults.login = true;
                    return true;
                } else {
                    console.log('❌ 登录失败 - 未找到管理界面');

                    // 输出当前页面内容用于调试
                    const pageTitle = await this.page.title();
                    const currentUrl = this.page.url();
                    console.log(`当前页面标题: ${pageTitle}`);
                    console.log(`当前URL: ${currentUrl}`);

                    this.testResults.issues.push('登录失败 - 未找到管理界面');
                    return false;
                }
            } else {
                console.log('ℹ️ 可能已经登录或无需登录');
                this.testResults.login = true;
                return true;
            }
        } catch (error) {
            console.log('❌ 登录过程出错:', error.message);
            this.testResults.issues.push(`登录错误: ${error.message}`);
            return false;
        }
    }

    async testBannerManagement() {
        console.log('\n📍 步骤2: 测试横幅管理功能');
        
        try {
            // 点击横幅管理
            const bannerLink = await this.page.locator('text=横幅管理').first();
            if (await bannerLink.isVisible()) {
                await bannerLink.click();
                console.log('✅ 已进入横幅管理页面');
                await this.page.waitForTimeout(3000);
            } else {
                console.log('❌ 未找到横幅管理链接');
                this.testResults.issues.push('未找到横幅管理链接');
                return false;
            }
            
            // 点击添加横幅
            const addButton = await this.page.locator('button:has-text("添加横幅"), .add-banner-btn').first();
            if (await addButton.isVisible()) {
                await addButton.click();
                console.log('✅ 已点击添加横幅按钮');
                await this.page.waitForTimeout(2000);
            } else {
                console.log('❌ 未找到添加横幅按钮');
                this.testResults.issues.push('未找到添加横幅按钮');
                return false;
            }
            
            // 填写横幅信息
            const titleInput = await this.page.locator('input[placeholder*="标题"], input[name="title"]').first();
            if (await titleInput.isVisible()) {
                await titleInput.fill('测试横幅标题');
                console.log('✅ 已填写横幅标题');
            }
            
            const descInput = await this.page.locator('textarea[placeholder*="描述"], textarea[name="description"]').first();
            if (await descInput.isVisible()) {
                await descInput.fill('这是一个测试横幅的描述');
                console.log('✅ 已填写横幅描述');
            }
            
            // 设置状态为发布
            const statusSelect = await this.page.locator('select[name="status"], .status-select').first();
            if (await statusSelect.isVisible()) {
                await statusSelect.selectOption('published');
                console.log('✅ 已设置状态为发布');
            }
            
            // 上传图片（模拟）
            const fileInput = await this.page.locator('input[type="file"]').first();
            if (await fileInput.isVisible()) {
                // 创建一个测试图片文件
                const testImagePath = path.join(__dirname, 'test-banner.jpg');
                if (!fs.existsSync(testImagePath)) {
                    // 创建一个简单的测试文件
                    fs.writeFileSync(testImagePath, 'test image data');
                }
                
                await fileInput.setInputFiles(testImagePath);
                console.log('✅ 已上传测试图片');
                await this.page.waitForTimeout(3000);
            }
            
            // 保存横幅
            const saveButton = await this.page.locator('button:has-text("保存"), button:has-text("确定"), .save-btn').first();
            if (await saveButton.isVisible()) {
                await saveButton.click();
                console.log('✅ 已点击保存按钮');
                await this.page.waitForTimeout(5000);
                
                this.testResults.bannerCreate = true;
            }
            
            // 验证横幅列表显示
            await this.testBannerDisplay();
            
            return true;
            
        } catch (error) {
            console.log('❌ 横幅管理测试出错:', error.message);
            this.testResults.issues.push(`横幅管理错误: ${error.message}`);
            return false;
        }
    }

    async testBannerDisplay() {
        console.log('\n📍 验证横幅显示问题');
        
        try {
            // 回到横幅列表
            const bannerLink = await this.page.locator('text=横幅管理').first();
            if (await bannerLink.isVisible()) {
                await bannerLink.click();
                await this.page.waitForTimeout(3000);
            }
            
            // 检查表格数据
            const tableRows = await this.page.locator('table tbody tr').count();
            console.log(`📋 横幅表格行数: ${tableRows}`);
            
            if (tableRows > 0) {
                // 检查第一行数据
                const firstRow = this.page.locator('table tbody tr').first();
                const cells = await firstRow.locator('td').allTextContents();
                
                console.log('📋 第一行横幅数据:', cells);
                
                // 检查是否有undefined
                const hasUndefined = cells.some(cell => cell.includes('undefined'));
                if (hasUndefined) {
                    console.log('❌ 发现undefined数据');
                    this.testResults.issues.push('横幅数据显示undefined');
                } else {
                    console.log('✅ 横幅数据显示正常');
                    this.testResults.bannerDisplay = true;
                }
                
                // 检查图片预览
                const imagePreview = await firstRow.locator('img').first();
                if (await imagePreview.isVisible()) {
                    const imageSrc = await imagePreview.getAttribute('src');
                    if (imageSrc && !imageSrc.includes('undefined') && imageSrc !== '') {
                        console.log('✅ 图片预览正常');
                    } else {
                        console.log('❌ 图片预览异常:', imageSrc);
                        this.testResults.issues.push('横幅图片预览异常');
                    }
                } else {
                    console.log('❌ 未找到图片预览');
                    this.testResults.issues.push('横幅未找到图片预览');
                }
                
                // 检查状态显示
                const statusCell = cells.find(cell => cell.includes('发布') || cell.includes('草稿') || cell.includes('published') || cell.includes('draft'));
                if (statusCell) {
                    if (statusCell.includes('草稿') || statusCell.includes('draft')) {
                        console.log('❌ 状态显示错误 - 应该是发布状态');
                        this.testResults.issues.push('横幅状态显示错误 - 显示为草稿而非发布');
                    } else {
                        console.log('✅ 状态显示正确');
                    }
                } else {
                    console.log('❌ 未找到状态信息');
                    this.testResults.issues.push('横幅未找到状态信息');
                }
            } else {
                console.log('❌ 横幅表格无数据');
                this.testResults.issues.push('横幅表格无数据');
            }
            
        } catch (error) {
            console.log('❌ 横幅显示验证出错:', error.message);
            this.testResults.issues.push(`横幅显示验证错误: ${error.message}`);
        }
    }

    async takeScreenshot(name) {
        try {
            await this.page.screenshot({ 
                path: `test-${name}-${Date.now()}.png`, 
                fullPage: true 
            });
            console.log(`📸 已保存截图: test-${name}-${Date.now()}.png`);
        } catch (error) {
            console.log('❌ 截图失败:', error.message);
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async generateReport() {
        console.log('\n📊 测试报告:');
        console.log('='.repeat(50));
        console.log(`登录测试: ${this.testResults.login ? '✅ 通过' : '❌ 失败'}`);
        console.log(`横幅创建: ${this.testResults.bannerCreate ? '✅ 通过' : '❌ 失败'}`);
        console.log(`横幅显示: ${this.testResults.bannerDisplay ? '✅ 通过' : '❌ 失败'}`);
        console.log(`表情包创建: ${this.testResults.emojiCreate ? '✅ 通过' : '❌ 失败'}`);
        console.log(`表情包显示: ${this.testResults.emojiDisplay ? '✅ 通过' : '❌ 失败'}`);
        console.log(`分类创建: ${this.testResults.categoryCreate ? '✅ 通过' : '❌ 失败'}`);
        console.log(`分类显示: ${this.testResults.categoryDisplay ? '✅ 通过' : '❌ 失败'}`);
        console.log(`微信同步: ${this.testResults.wechatSync ? '✅ 通过' : '❌ 失败'}`);
        
        if (this.testResults.issues.length > 0) {
            console.log('\n🔴 发现的问题:');
            this.testResults.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
        } else {
            console.log('\n✅ 未发现问题');
        }
        
        console.log('='.repeat(50));
    }
}

// 运行测试
async function runComprehensiveTest() {
    const tester = new AdminTester();
    
    try {
        await tester.init();
        
        // 执行测试步骤
        const loginSuccess = await tester.login();
        if (!loginSuccess) {
            console.log('❌ 登录失败，终止测试');
            return;
        }
        
        await tester.takeScreenshot('after-login');
        
        // 测试横幅管理
        await tester.testBannerManagement();
        await tester.takeScreenshot('after-banner-test');
        
        // 生成报告
        await tester.generateReport();
        
        // 保持浏览器打开以便查看
        console.log('\n⏸️ 测试完成，浏览器将保持打开10秒供查看...');
        await tester.page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出现严重错误:', error);
    } finally {
        await tester.cleanup();
    }
}

// 启动测试
runComprehensiveTest().catch(console.error);
