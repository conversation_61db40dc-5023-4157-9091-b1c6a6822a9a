/**
 * 云配置测试脚本
 * 验证云开发配置的正确性
 */

// 模拟微信小程序环境
global.wx = {
  cloud: {
    init: (config) => {
      console.log('✅ 云开发初始化配置:', config)
      return true
    },
    callFunction: async (options) => {
      console.log('✅ 云函数调用配置:', options)
      return { result: { success: true } }
    }
  }
}

// 引入配置模块
const { CloudConfig } = require('../config/cloud.js')

// 测试函数
async function testCloudConfig() {
  console.log('🧪 开始测试云开发配置...\n')

  try {
    // 测试1: 获取当前环境
    console.log('📋 测试1: 获取当前环境')
    const currentEnv = CloudConfig.getCurrentEnv()
    console.log(`   当前环境: ${currentEnv}`)
    console.log('   ✅ 通过\n')

    // 测试2: 获取配置
    console.log('📋 测试2: 获取环境配置')
    const config = CloudConfig.getCurrentConfig()
    console.log(`   环境名称: ${config.name}`)
    console.log(`   环境ID: ${config.envId}`)
    console.log(`   超时时间: ${config.timeout}ms`)
    console.log('   ✅ 通过\n')

    // 测试3: 验证配置
    console.log('📋 测试3: 验证配置有效性')
    const validation = CloudConfig.validateConfig()
    if (validation.isValid) {
      console.log('   ✅ 配置验证通过')
    } else {
      console.log('   ❌ 配置验证失败:', validation.errors)
      throw new Error('配置验证失败')
    }
    console.log('')

    // 测试4: 获取初始化配置
    console.log('📋 测试4: 获取初始化配置')
    const initConfig = CloudConfig.getInitConfig()
    console.log('   初始化配置:', initConfig)
    if (initConfig.env && initConfig.traceUser !== undefined) {
      console.log('   ✅ 初始化配置正确')
    } else {
      throw new Error('初始化配置不完整')
    }
    console.log('')

    // 测试5: 打印配置信息
    console.log('📋 测试5: 打印配置信息')
    CloudConfig.printCurrentConfig()
    console.log('   ✅ 配置信息打印正常\n')

    // 测试6: 检查云服务状态
    console.log('📋 测试6: 检查云服务状态')
    const status = await CloudConfig.checkCloudStatus()
    console.log('   云服务状态:', status)
    if (status.status === 'healthy') {
      console.log('   ✅ 云服务状态检查通过')
    } else {
      console.log('   ⚠️ 云服务状态异常（在测试环境中这是正常的）')
    }
    console.log('')

    // 测试7: 获取各种配置
    console.log('📋 测试7: 获取各种专用配置')
    const cloudFunctionConfig = CloudConfig.getCloudFunctionConfig()
    const databaseConfig = CloudConfig.getDatabaseConfig()
    const storageConfig = CloudConfig.getStorageConfig()
    
    console.log('   云函数配置:', cloudFunctionConfig)
    console.log('   数据库配置:', databaseConfig)
    console.log('   存储配置:', storageConfig)
    console.log('   ✅ 专用配置获取正常\n')

    console.log('🎉 所有测试通过！云配置模块工作正常')
    return true

  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    console.error('   错误详情:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  testCloudConfig().then(success => {
    process.exit(success ? 0 : 1)
  })
}

module.exports = { testCloudConfig }
