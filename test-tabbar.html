<!DOCTYPE html>
<html>
<head>
    <title>TabBar动效测试预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
        }
        
        .preview-container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .phone-screen {
            height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        
        /* 复制自定义TabBar样式 */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-top: 1px solid rgba(255, 140, 0, 0.1);
            box-shadow: 0 -2px 25px rgba(255, 140, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-around;
            z-index: 1000;
        }
        
        .tab-bar-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, 
                rgba(255, 255, 255, 0.9) 0%, 
                rgba(255, 248, 240, 0.8) 100%);
            pointer-events: none;
            z-index: -1;
        }
        
        .tab-bar-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .item-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 140, 0, 0.2) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
            z-index: -1;
        }
        
        .item-glow.active {
            width: 45px;
            height: 45px;
            animation: glowPulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes glowPulse {
            0% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(0.8);
            }
            100% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }
        
        .icon-container {
            position: relative;
            width: 24px;
            height: 24px;
            margin-bottom: 2px;
        }
        
        .icon {
            width: 100%;
            height: 100%;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            transform-origin: center bottom;
            border-radius: 4px;
        }
        
        .icon-selected {
            transform: translateY(-3px) scale(1.15);
            animation: iconBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        
        @keyframes iconBounce {
            0% { transform: translateY(0) scale(1); }
            30% { transform: translateY(-5px) scale(1.2); }
            50% { transform: translateY(-2px) scale(1.1); }
            70% { transform: translateY(-4px) scale(1.18); }
            100% { transform: translateY(-3px) scale(1.15); }
        }
        
        .ripple {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 140, 0, 0.3);
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        
        .ripple-active {
            animation: rippleEffect 0.6s ease-out;
        }
        
        @keyframes rippleEffect {
            0% { width: 0; height: 0; opacity: 1; }
            100% { width: 50px; height: 50px; opacity: 0; }
        }
        
        .text {
            font-size: 10px;
            color: #8A8A8A;
            transition: all 0.3s ease;
            font-weight: 500;
            letter-spacing: 0.2px;
        }
        
        .text-selected {
            color: #FF8C00;
            font-weight: 600;
            transform: scale(1.05);
            text-shadow: 0 0 8px rgba(255, 140, 0, 0.3);
        }
        
        .tab-bar-item:active {
            transform: scale(0.95);
        }
        
        .tab-bar-item.selected {
            transform: translateY(-1px);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .instructions {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            padding: 0 20px;
        }
    </style>
</head>
<body>
    <h1>🎨 TabBar动效预览</h1>
    <p class="instructions">点击下方TabBar项目查看动效！这就是你的小程序TabBar的实际效果。</p>
    
    <div class="preview-container">
        <div class="phone-screen">
            <div class="tab-bar">
                <div class="tab-bar-bg"></div>
                
                <div class="tab-bar-item selected" data-index="0">
                    <div class="item-glow active"></div>
                    <div class="icon-container">
                        <div class="icon icon-selected" style="background: url('images/home.png') center/cover;"></div>
                        <div class="ripple"></div>
                    </div>
                    <div class="text text-selected">首页</div>
                </div>
                
                <div class="tab-bar-item" data-index="1">
                    <div class="item-glow"></div>
                    <div class="icon-container">
                        <div class="icon" style="background: url('images/search.png') center/cover;"></div>
                        <div class="ripple"></div>
                    </div>
                    <div class="text">搜索</div>
                </div>
                
                <div class="tab-bar-item" data-index="2">
                    <div class="item-glow"></div>
                    <div class="icon-container">
                        <div class="icon" style="background: url('images/category.png') center/cover;"></div>
                        <div class="ripple"></div>
                    </div>
                    <div class="text">分类</div>
                </div>
                
                <div class="tab-bar-item" data-index="3">
                    <div class="item-glow"></div>
                    <div class="icon-container">
                        <div class="icon" style="background: url('images/profile.png') center/cover;"></div>
                        <div class="ripple"></div>
                    </div>
                    <div class="text">我的</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 20px; color: #666;">
        <p>✨ 动效特色：</p>
        <ul style="text-align: left; display: inline-block;">
            <li>🎯 图标弹跳 + 放大效果</li>
            <li>🌟 橙色光晕脉动</li>
            <li>💫 点击波纹扩散</li>
            <li>✨ 文字发光阴影</li>
            <li>🎨 毛玻璃背景</li>
        </ul>
    </div>

    <script>
        // 添加交互效果
        const tabItems = document.querySelectorAll('.tab-bar-item');
        let currentSelected = 0;
        
        tabItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                // 移除之前选中的状态
                tabItems[currentSelected].classList.remove('selected');
                tabItems[currentSelected].querySelector('.item-glow').classList.remove('active');
                tabItems[currentSelected].querySelector('.icon').classList.remove('icon-selected');
                tabItems[currentSelected].querySelector('.text').classList.remove('text-selected');
                
                // 添加新选中的状态
                item.classList.add('selected');
                item.querySelector('.item-glow').classList.add('active');
                item.querySelector('.icon').classList.add('icon-selected');
                item.querySelector('.text').classList.add('text-selected');
                
                // 触发波纹效果
                const ripple = item.querySelector('.ripple');
                ripple.classList.add('ripple-active');
                setTimeout(() => {
                    ripple.classList.remove('ripple-active');
                }, 600);
                
                currentSelected = index;
            });
        });
    </script>
</body>
</html>