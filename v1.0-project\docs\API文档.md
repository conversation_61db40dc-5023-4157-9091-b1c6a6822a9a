# V1.0 API文档

## 概述

V1.0系统提供三个主要的云函数API，用于支持表情包小程序的完整功能。

## 认证说明

除了公开的数据查询接口外，所有管理接口都需要JWT认证。

### 获取访问令牌

```javascript
// 登录获取令牌
const result = await wx.cloud.callFunction({
  name: 'loginAPI',
  data: {
    action: 'login',
    username: 'admin',
    password: 'your_password'
  }
});

const token = result.result.data.token;
```

### 使用令牌

```javascript
// 在后续请求中携带令牌
const result = await wx.cloud.callFunction({
  name: 'webAdminAPI',
  data: {
    action: 'createCategory',
    token: token,
    name: '新分类',
    icon: 'icon-url'
  }
});
```

## 1. loginAPI - 认证服务

### 1.1 用户登录

**接口**: `loginAPI`  
**Action**: `login`  
**方法**: POST  
**认证**: 无需认证

**请求参数**:
```json
{
  "action": "login",
  "username": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "adminId": "admin",
    "expiresIn": "24h",
    "permissions": ["read", "write", "delete"]
  },
  "message": "登录成功"
}
```

### 1.2 令牌验证

**Action**: `validateToken`

**请求参数**:
```json
{
  "action": "validateToken",
  "token": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "adminId": "admin",
    "permissions": ["read", "write", "delete"],
    "isValid": true
  }
}
```

### 1.3 令牌刷新

**Action**: `refreshToken`

**请求参数**:
```json
{
  "action": "refreshToken",
  "token": "string"
}
```

### 1.4 用户登出

**Action**: `logout`

**请求参数**:
```json
{
  "action": "logout",
  "token": "string"
}
```

## 2. webAdminAPI - 管理后台服务

### 2.1 分类管理

#### 2.1.1 创建分类

**Action**: `createCategory`  
**认证**: 需要write权限

**请求参数**:
```json
{
  "action": "createCategory",
  "token": "string",
  "name": "分类名称",
  "icon": "图标URL",
  "description": "分类描述",
  "sort": 0
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "cat_1640995200000_abc123",
    "_id": "61c8f...",
    "name": "分类名称"
  },
  "message": "分类创建成功"
}
```

#### 2.1.2 更新分类

**Action**: `updateCategory`

**请求参数**:
```json
{
  "action": "updateCategory",
  "token": "string",
  "id": "分类ID",
  "updates": {
    "name": "新名称",
    "description": "新描述"
  }
}
```

#### 2.1.3 删除分类

**Action**: `deleteCategory`  
**认证**: 需要delete权限

**请求参数**:
```json
{
  "action": "deleteCategory",
  "token": "string",
  "id": "分类ID"
}
```

#### 2.1.4 获取分类列表

**Action**: `getCategories`

**请求参数**:
```json
{
  "action": "getCategories",
  "token": "string",
  "status": "active"
}
```

### 2.2 表情包管理

#### 2.2.1 创建表情包

**Action**: `createEmoji`

**请求参数**:
```json
{
  "action": "createEmoji",
  "token": "string",
  "title": "表情包标题",
  "imageUrl": "图片URL",
  "categoryId": "分类ID",
  "tags": ["标签1", "标签2"],
  "description": "描述"
}
```

#### 2.2.2 更新表情包

**Action**: `updateEmoji`

**请求参数**:
```json
{
  "action": "updateEmoji",
  "token": "string",
  "id": "表情包ID",
  "updates": {
    "title": "新标题",
    "tags": ["新标签"]
  }
}
```

#### 2.2.3 删除表情包

**Action**: `deleteEmoji`

**请求参数**:
```json
{
  "action": "deleteEmoji",
  "token": "string",
  "id": "表情包ID"
}
```

### 2.3 横幅管理

#### 2.3.1 创建横幅

**Action**: `createBanner`

**请求参数**:
```json
{
  "action": "createBanner",
  "token": "string",
  "title": "横幅标题",
  "imageUrl": "图片URL",
  "linkUrl": "链接URL",
  "sort": 0
}
```

#### 2.3.2 更新横幅

**Action**: `updateBanner`

#### 2.3.3 删除横幅

**Action**: `deleteBanner`

### 2.4 统计信息

**Action**: `getStats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "categories": 10,
    "emojis": 500,
    "banners": 3,
    "totalLikes": 1250,
    "totalDownloads": 5000
  }
}
```

## 3. dataAPI - 数据查询服务

### 3.1 分类数据

#### 3.1.1 获取分类列表

**Action**: `getCategories`  
**认证**: 无需认证

**请求参数**:
```json
{
  "action": "getCategories"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_001",
      "name": "搞笑",
      "icon": "icon_url",
      "description": "搞笑表情包",
      "emojiCount": 50
    }
  ],
  "fromCache": true,
  "timestamp": 1640995200000
}
```

#### 3.1.2 获取分类详情

**Action**: `getCategoryDetail`

**请求参数**:
```json
{
  "action": "getCategoryDetail",
  "params": {
    "categoryId": "cat_001"
  }
}
```

### 3.2 表情包数据

#### 3.2.1 获取表情包列表

**Action**: `getEmojis`

**请求参数**:
```json
{
  "action": "getEmojis",
  "params": {
    "page": 1,
    "pageSize": 20,
    "categoryId": "cat_001",
    "sortBy": "createTime",
    "sortOrder": "desc"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": "emoji_001",
        "title": "开心",
        "imageUrl": "image_url",
        "categoryId": "cat_001",
        "tags": ["开心", "笑脸"],
        "likes": 100,
        "downloads": 500
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

#### 3.2.2 获取表情包详情

**Action**: `getEmojiDetail`

**请求参数**:
```json
{
  "action": "getEmojiDetail",
  "params": {
    "emojiId": "emoji_001"
  }
}
```

#### 3.2.3 搜索表情包

**Action**: `searchEmojis`

**请求参数**:
```json
{
  "action": "searchEmojis",
  "params": {
    "keyword": "开心",
    "page": 1,
    "pageSize": 20
  }
}
```

### 3.3 横幅数据

#### 3.3.1 获取横幅列表

**Action**: `getBanners`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "banner_001",
      "title": "活动横幅",
      "imageUrl": "banner_image_url",
      "linkUrl": "activity_url",
      "sort": 0
    }
  ]
}
```

### 3.4 统计数据

#### 3.4.1 获取统计信息

**Action**: `getStats`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "categories": 10,
    "emojis": 500,
    "banners": 3,
    "totalLikes": 1250,
    "totalDownloads": 5000,
    "lastUpdate": 1640995200000
  },
  "fromCache": true
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| INVALID_TOKEN | 无效的访问令牌 |
| TOKEN_EXPIRED | 令牌已过期 |
| INSUFFICIENT_PERMISSION | 权限不足 |
| INVALID_DATA | 数据验证失败 |
| RESOURCE_NOT_FOUND | 资源不存在 |
| DUPLICATE_RESOURCE | 资源已存在 |
| INTERNAL_ERROR | 服务器内部错误 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 |

## 请求频率限制

- **dataAPI**: 每分钟100次请求
- **webAdminAPI**: 每分钟50次请求  
- **loginAPI**: 每5分钟10次请求

## 缓存策略

- **分类数据**: 缓存5分钟
- **表情包数据**: 缓存3分钟
- **横幅数据**: 缓存10分钟
- **统计数据**: 缓存1分钟

## 数据格式说明

### 时间格式
所有时间字段使用ISO 8601格式：`2021-12-31T23:59:59.999Z`

### ID格式
- 分类ID: `cat_` + 时间戳 + 随机字符串
- 表情包ID: `emoji_` + 时间戳 + 随机字符串  
- 横幅ID: `banner_` + 时间戳 + 随机字符串

### 图片URL要求
- 支持格式：jpg, jpeg, png, gif, webp
- 必须使用HTTPS协议
- 建议尺寸：表情包128x128px，横幅750x200px
