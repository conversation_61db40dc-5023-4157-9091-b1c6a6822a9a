// 强制刷新分类数据测试修复效果
const { chromium } = require('playwright');

async function testForceRefreshCategories() {
    console.log('🔄 强制刷新分类数据测试修复效果...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('清理分类数据') || text.includes('渐变') || text.includes('gradient')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：强制刷新页面');
        
        // 强制刷新页面
        await page.reload({ waitUntil: 'networkidle' });
        await page.waitForTimeout(5000);
        
        // 重新登录
        const usernameInput2 = await page.locator('input[type="text"]').first();
        const passwordInput2 = await page.locator('input[type="password"]').first();
        const loginButton2 = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput2.isVisible()) {
            await usernameInput2.fill('admin');
            await passwordInput2.fill('admin123');
            await loginButton2.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第二步：进入分类管理页面');
        
        // 进入分类管理页面
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(5000);
        
        console.log('\n📍 第三步：检查修复后的数据清理过程');
        
        // 检查数据清理过程中的渐变字段
        const dataCleaningCheck = await page.evaluate(() => {
            // 检查AdminApp.data.categories中的数据
            const categories = window.AdminApp && window.AdminApp.data && window.AdminApp.data.categories;
            
            if (!categories) {
                return { error: '未找到AdminApp.data.categories' };
            }
            
            return {
                found: true,
                count: categories.length,
                categories: categories.map(cat => ({
                    _id: cat._id,
                    name: cat.name,
                    hasGradient: !!cat.gradient,
                    gradientValue: cat.gradient || 'N/A',
                    hasColor: !!cat.color,
                    colorValue: cat.color || 'N/A',
                    allFields: Object.keys(cat)
                }))
            };
        });
        
        console.log('📊 数据清理后的内存数据检查:');
        if (dataCleaningCheck.found) {
            console.log(`内存中分类数量: ${dataCleaningCheck.count}`);
            
            dataCleaningCheck.categories.forEach((cat, index) => {
                console.log(`\n内存分类 ${index + 1}:`);
                console.log(`  ID: ${cat._id}`);
                console.log(`  名称: ${cat.name}`);
                console.log(`  有渐变字段: ${cat.hasGradient}`);
                console.log(`  渐变值: ${cat.gradientValue.substring(0, 50)}...`);
                console.log(`  有颜色字段: ${cat.hasColor}`);
                console.log(`  颜色值: ${cat.colorValue}`);
                console.log(`  所有字段: [${cat.allFields.join(', ')}]`);
                
                if (cat.hasGradient) {
                    console.log(`  ✅ 内存数据包含渐变字段`);
                } else {
                    console.log(`  🔴 内存数据缺少渐变字段`);
                }
            });
            
            // 检查修复效果
            const withGradient = dataCleaningCheck.categories.filter(cat => cat.hasGradient).length;
            console.log(`\n📊 内存数据修复效果: ${withGradient}/${dataCleaningCheck.count} 个分类有渐变数据`);
            
            if (withGradient === dataCleaningCheck.count) {
                console.log('🎉 内存数据修复完全成功！');
            } else if (withGradient > 0) {
                console.log('✅ 内存数据部分修复成功');
            } else {
                console.log('🔴 内存数据修复失败');
            }
            
        } else {
            console.log(`内存数据检查失败: ${dataCleaningCheck.error}`);
        }
        
        console.log('\n📍 第四步：检查表格渲染结果');
        
        // 检查表格渲染结果
        const tableRenderCheck = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                found: true,
                totalRows: rows.length,
                categories: rows.map((row, index) => {
                    const nameCell = row.querySelector('td:nth-child(3)');
                    const gradientCell = row.querySelector('td:nth-child(4)');
                    
                    // 提取渐变单元格的详细信息
                    let gradientInfo = { hasDiv: false, divStyle: '', divText: '' };
                    if (gradientCell) {
                        const gradientDiv = gradientCell.querySelector('div');
                        if (gradientDiv) {
                            gradientInfo = {
                                hasDiv: true,
                                divStyle: gradientDiv.style.cssText || gradientDiv.getAttribute('style') || '',
                                divText: gradientDiv.textContent || '',
                                hasGradientInStyle: (gradientDiv.style.cssText || gradientDiv.getAttribute('style') || '').includes('gradient')
                            };
                        }
                    }
                    
                    return {
                        index: index + 1,
                        name: nameCell ? nameCell.textContent.trim() : 'N/A',
                        gradientInfo: gradientInfo,
                        hasGradientDisplay: gradientInfo.hasGradientInStyle
                    };
                })
            };
        });
        
        console.log('📊 表格渲染检查:');
        if (tableRenderCheck.found) {
            console.log(`表格总行数: ${tableRenderCheck.totalRows}`);
            
            tableRenderCheck.categories.forEach(cat => {
                console.log(`\n表格分类 ${cat.index}:`);
                console.log(`  名称: ${cat.name}`);
                console.log(`  有渐变div: ${cat.gradientInfo.hasDiv}`);
                console.log(`  div样式: ${cat.gradientInfo.divStyle}`);
                console.log(`  div文本: ${cat.gradientInfo.divText}`);
                console.log(`  样式包含gradient: ${cat.gradientInfo.hasGradientInStyle}`);
                console.log(`  渐变显示正常: ${cat.hasGradientDisplay}`);
                
                if (cat.hasGradientDisplay) {
                    console.log(`  ✅ 表格渐变显示正常`);
                } else {
                    console.log(`  🔴 表格渐变显示异常`);
                }
            });
            
            // 统计表格渲染效果
            const withGradientDisplay = tableRenderCheck.categories.filter(cat => cat.hasGradientDisplay).length;
            console.log(`\n📊 表格渲染效果: ${withGradientDisplay}/${tableRenderCheck.totalRows} 个分类正确显示渐变`);
            
        } else {
            console.log(`表格渲染检查失败: ${tableRenderCheck.error}`);
        }
        
        console.log('\n📍 第五步：手动调用数据重新加载');
        
        // 手动调用loadCategories函数重新加载数据
        const manualReloadResult = await page.evaluate(async () => {
            try {
                // 手动调用loadCategories函数
                if (typeof loadCategories === 'function') {
                    await loadCategories();
                    return { success: true, message: '手动重新加载完成' };
                } else {
                    return { success: false, error: 'loadCategories函数不存在' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
        
        console.log('📊 手动重新加载结果:');
        console.log(`重新加载成功: ${manualReloadResult.success}`);
        if (manualReloadResult.success) {
            console.log(`消息: ${manualReloadResult.message}`);
        } else {
            console.log(`错误: ${manualReloadResult.error}`);
        }
        
        // 等待重新加载完成
        await page.waitForTimeout(3000);
        
        // 再次检查表格渲染结果
        const finalTableCheck = await page.evaluate(() => {
            const categoryTable = document.querySelector('#category-content table');
            if (!categoryTable) return { error: '未找到分类表格' };
            
            const rows = Array.from(categoryTable.querySelectorAll('tbody tr'));
            return {
                found: true,
                totalRows: rows.length,
                withGradient: rows.filter(row => {
                    const gradientCell = row.querySelector('td:nth-child(4)');
                    if (!gradientCell) return false;
                    const gradientDiv = gradientCell.querySelector('div');
                    if (!gradientDiv) return false;
                    const style = gradientDiv.style.cssText || gradientDiv.getAttribute('style') || '';
                    return style.includes('gradient');
                }).length
            };
        });
        
        console.log('\n📊 最终表格检查:');
        if (finalTableCheck.found) {
            console.log(`表格总行数: ${finalTableCheck.totalRows}`);
            console.log(`有渐变显示: ${finalTableCheck.withGradient}`);
            
            if (finalTableCheck.withGradient > 0) {
                console.log('🎉 修复成功！表格现在正确显示渐变！');
            } else {
                console.log('🔴 修复仍未生效，需要进一步调试');
            }
        }
        
        // 截图
        await page.screenshot({ path: 'force-refresh-categories.png', fullPage: true });
        console.log('\n📸 测试截图已保存: force-refresh-categories.png');
        
        console.log('\n🎯 强制刷新测试总结:');
        
        const memoryDataFixed = dataCleaningCheck.found && 
            dataCleaningCheck.categories.some(cat => cat.hasGradient);
        const tableDisplayFixed = finalTableCheck.found && 
            finalTableCheck.withGradient > 0;
        
        console.log(`内存数据修复: ${memoryDataFixed ? '✅ 成功' : '🔴 失败'}`);
        console.log(`表格显示修复: ${tableDisplayFixed ? '✅ 成功' : '🔴 失败'}`);
        
        if (memoryDataFixed && tableDisplayFixed) {
            console.log('\n🎉 渐变显示问题完全修复！');
            return { success: true, allFixed: true };
        } else {
            console.log('\n⚠️ 渐变显示问题仍需进一步修复');
            return { success: true, allFixed: false, issues: {
                memoryData: !memoryDataFixed,
                tableDisplay: !tableDisplayFixed
            }};
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'force-refresh-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行测试
testForceRefreshCategories().then(result => {
    console.log('\n🎯 强制刷新测试最终结果:', result);
}).catch(console.error);
