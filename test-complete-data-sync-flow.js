// 完整测试数据同步流程
const { chromium } = require('playwright');

async function testCompleteDataSyncFlow() {
    console.log('🔄 完整测试数据同步流程...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('同步测试') || text.includes('创建') || text.includes('获取')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：记录当前数据基线');
        
        // 记录当前数据基线
        const baselineData = await page.evaluate(async () => {
            try {
                // 获取管理后台数据
                const categoriesResult = await CloudAPI.database.get('categories');
                const emojisResult = await CloudAPI.database.get('emojis');
                const bannersResult = await CloudAPI.database.get('banners');
                
                // 获取云函数数据
                const categoryCloudResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                const emojiCloudResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 20 }
                    }
                });
                
                const bannerCloudResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                return {
                    success: true,
                    admin: {
                        categories: categoriesResult.success ? categoriesResult.data.length : 0,
                        emojis: emojisResult.success ? emojisResult.data.length : 0,
                        banners: bannersResult.success ? bannersResult.data.length : 0
                    },
                    cloud: {
                        categories: categoryCloudResult.result?.success ? categoryCloudResult.result.data.length : 0,
                        emojis: emojiCloudResult.result?.success ? emojiCloudResult.result.data.length : 0,
                        banners: bannerCloudResult.result?.success ? bannerCloudResult.result.data.length : 0
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据基线:');
        if (baselineData.success) {
            console.log(`管理后台 - 分类: ${baselineData.admin.categories}, 表情包: ${baselineData.admin.emojis}, 横幅: ${baselineData.admin.banners}`);
            console.log(`云函数 - 分类: ${baselineData.cloud.categories}, 表情包: ${baselineData.cloud.emojis}, 横幅: ${baselineData.cloud.banners}`);
        } else {
            console.log(`❌ 基线数据获取失败: ${baselineData.error}`);
        }
        
        console.log('\n📍 第二步：在管理后台创建测试数据');
        
        // 创建测试分类
        const categoryLink = await page.locator('[onclick="showPage(\'category-management\')"]').first();
        await categoryLink.click();
        await page.waitForTimeout(3000);
        
        const addCategoryBtn = await page.locator('text=➕ 添加分类').first();
        await addCategoryBtn.click();
        await page.waitForTimeout(2000);
        
        const testCategoryName = `同步测试分类_${Date.now()}`;
        await page.fill('#category-name', testCategoryName);
        await page.fill('#category-description', '测试完整数据同步流程');
        await page.selectOption('#category-gradient-preset', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
        await page.selectOption('#category-status', 'show');
        
        // 保存分类
        const saveCategoryResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveCategoryResult.success) {
            console.log(`✅ 创建测试分类: ${testCategoryName}`);
            await page.waitForTimeout(5000);
        } else {
            console.log(`🔴 创建分类失败: ${saveCategoryResult.error}`);
        }
        
        // 创建测试表情包
        const emojiLink = await page.locator('[onclick="showPage(\'emoji-management\')"]').first();
        await emojiLink.click();
        await page.waitForTimeout(3000);
        
        const addEmojiBtn = await page.locator('text=➕ 添加表情包').first();
        await addEmojiBtn.click();
        await page.waitForTimeout(2000);
        
        const testEmojiTitle = `同步测试表情包_${Date.now()}`;
        await page.fill('#emoji-title', testEmojiTitle);
        await page.fill('#emoji-description', '测试完整数据同步流程的表情包');
        
        // 选择刚创建的分类
        const categoryOptions = await page.evaluate(() => {
            const select = document.querySelector('#emoji-category');
            return Array.from(select.options).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        });
        
        const testCategory = categoryOptions.find(opt => opt.text.includes('同步测试分类'));
        if (testCategory) {
            await page.selectOption('#emoji-category', testCategory.value);
            console.log(`✅ 选择测试分类: ${testCategory.text}`);
        }
        
        await page.selectOption('#emoji-status', 'published');
        
        // 上传测试图片（使用base64）
        const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        await page.evaluate((base64) => {
            const input = document.querySelector('#emoji-image');
            if (input) {
                input.value = base64;
                // 触发change事件
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            }
        }, testImageBase64);
        
        // 保存表情包
        const saveEmojiResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveEmojiResult.success) {
            console.log(`✅ 创建测试表情包: ${testEmojiTitle}`);
            await page.waitForTimeout(5000);
        } else {
            console.log(`🔴 创建表情包失败: ${saveEmojiResult.error}`);
        }
        
        // 创建测试横幅
        const bannerLink = await page.locator('[onclick="showPage(\'banner-management\')"]').first();
        await bannerLink.click();
        await page.waitForTimeout(3000);
        
        const addBannerBtn = await page.locator('text=➕ 添加横幅').first();
        await addBannerBtn.click();
        await page.waitForTimeout(2000);
        
        const testBannerTitle = `同步测试横幅_${Date.now()}`;
        await page.fill('#banner-title', testBannerTitle);
        await page.fill('#banner-description', '测试完整数据同步流程的横幅');
        await page.selectOption('#banner-status', 'show');
        
        // 上传测试图片
        await page.evaluate((base64) => {
            const input = document.querySelector('#banner-image');
            if (input) {
                input.value = base64;
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            }
        }, testImageBase64);
        
        // 保存横幅
        const saveBannerResult = await page.evaluate(() => {
            const modal = document.querySelector('[style*="position: fixed"]');
            const submitBtn = modal ? modal.querySelector('button[type="submit"]') : null;
            
            if (submitBtn) {
                submitBtn.click();
                return { success: true };
            } else {
                return { success: false, error: '未找到提交按钮' };
            }
        });
        
        if (saveBannerResult.success) {
            console.log(`✅ 创建测试横幅: ${testBannerTitle}`);
            await page.waitForTimeout(5000);
        } else {
            console.log(`🔴 创建横幅失败: ${saveBannerResult.error}`);
        }
        
        console.log('\n📍 第三步：验证数据已保存到数据库');
        
        // 验证数据库中的新数据
        const databaseVerification = await page.evaluate(async (testNames) => {
            try {
                const categoriesResult = await CloudAPI.database.get('categories');
                const emojisResult = await CloudAPI.database.get('emojis');
                const bannersResult = await CloudAPI.database.get('banners');
                
                const foundCategory = categoriesResult.success ? 
                    categoriesResult.data.find(cat => cat.name && cat.name.includes('同步测试分类')) : null;
                const foundEmoji = emojisResult.success ? 
                    emojisResult.data.find(emoji => emoji.title && emoji.title.includes('同步测试表情包')) : null;
                const foundBanner = bannersResult.success ? 
                    bannersResult.data.find(banner => banner.title && banner.title.includes('同步测试横幅')) : null;
                
                return {
                    success: true,
                    category: {
                        found: !!foundCategory,
                        id: foundCategory?._id,
                        name: foundCategory?.name
                    },
                    emoji: {
                        found: !!foundEmoji,
                        id: foundEmoji?._id,
                        title: foundEmoji?.title,
                        categoryId: foundEmoji?.categoryId
                    },
                    banner: {
                        found: !!foundBanner,
                        id: foundBanner?._id,
                        title: foundBanner?.title
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }, { testCategoryName, testEmojiTitle, testBannerTitle });
        
        console.log('📊 数据库验证结果:');
        if (databaseVerification.success) {
            console.log(`测试分类: ${databaseVerification.category.found ? '✅ 已保存' : '🔴 未找到'} (${databaseVerification.category.name})`);
            console.log(`测试表情包: ${databaseVerification.emoji.found ? '✅ 已保存' : '🔴 未找到'} (${databaseVerification.emoji.title})`);
            console.log(`测试横幅: ${databaseVerification.banner.found ? '✅ 已保存' : '🔴 未找到'} (${databaseVerification.banner.title})`);
        } else {
            console.log(`❌ 数据库验证失败: ${databaseVerification.error}`);
        }
        
        console.log('\n📍 第四步：验证云函数能获取新数据');
        
        // 验证云函数能获取新数据
        const cloudFunctionVerification = await page.evaluate(async () => {
            try {
                const categoryResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                const emojiResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: 'all', page: 1, limit: 50 }
                    }
                });
                
                const bannerResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getBanners' }
                });
                
                const foundCategory = categoryResult.result?.success ? 
                    categoryResult.result.data.find(cat => cat.name && cat.name.includes('同步测试分类')) : null;
                const foundEmoji = emojiResult.result?.success ? 
                    emojiResult.result.data.find(emoji => emoji.title && emoji.title.includes('同步测试表情包')) : null;
                const foundBanner = bannerResult.result?.success ? 
                    bannerResult.result.data.find(banner => banner.title && banner.title.includes('同步测试横幅')) : null;
                
                return {
                    success: true,
                    category: {
                        found: !!foundCategory,
                        name: foundCategory?.name,
                        emojiCount: foundCategory?.emojiCount
                    },
                    emoji: {
                        found: !!foundEmoji,
                        title: foundEmoji?.title,
                        categoryId: foundEmoji?.categoryId
                    },
                    banner: {
                        found: !!foundBanner,
                        title: foundBanner?.title
                    },
                    totals: {
                        categories: categoryResult.result?.data?.length || 0,
                        emojis: emojiResult.result?.data?.length || 0,
                        banners: bannerResult.result?.data?.length || 0
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 云函数验证结果:');
        if (cloudFunctionVerification.success) {
            console.log(`测试分类: ${cloudFunctionVerification.category.found ? '✅ 可获取' : '🔴 未找到'} (${cloudFunctionVerification.category.name})`);
            console.log(`测试表情包: ${cloudFunctionVerification.emoji.found ? '✅ 可获取' : '🔴 未找到'} (${cloudFunctionVerification.emoji.title})`);
            console.log(`测试横幅: ${cloudFunctionVerification.banner.found ? '✅ 可获取' : '🔴 未找到'} (${cloudFunctionVerification.banner.title})`);
            console.log(`云函数总数 - 分类: ${cloudFunctionVerification.totals.categories}, 表情包: ${cloudFunctionVerification.totals.emojis}, 横幅: ${cloudFunctionVerification.totals.banners}`);
        } else {
            console.log(`❌ 云函数验证失败: ${cloudFunctionVerification.error}`);
        }
        
        console.log('\n📍 第五步：验证数据关联关系');
        
        // 验证分类和表情包的关联关系
        const relationshipVerification = await page.evaluate(async () => {
            try {
                // 获取测试分类ID
                const categoryResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { action: 'getCategories' }
                });
                
                const testCategory = categoryResult.result?.success ? 
                    categoryResult.result.data.find(cat => cat.name && cat.name.includes('同步测试分类')) : null;
                
                if (!testCategory) {
                    return { success: false, error: '未找到测试分类' };
                }
                
                // 获取该分类下的表情包
                const emojiResult = await window.tcbApp.callFunction({
                    name: 'dataAPI',
                    data: { 
                        action: 'getEmojis',
                        data: { category: testCategory._id, page: 1, limit: 10 }
                    }
                });
                
                const categoryEmojis = emojiResult.result?.success ? emojiResult.result.data : [];
                const testEmoji = categoryEmojis.find(emoji => emoji.title && emoji.title.includes('同步测试表情包'));
                
                return {
                    success: true,
                    categoryId: testCategory._id,
                    categoryName: testCategory.name,
                    categoryEmojiCount: testCategory.emojiCount || 0,
                    actualEmojisInCategory: categoryEmojis.length,
                    testEmojiFound: !!testEmoji,
                    testEmojiCategoryId: testEmoji?.categoryId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 关联关系验证结果:');
        if (relationshipVerification.success) {
            console.log(`测试分类ID: ${relationshipVerification.categoryId}`);
            console.log(`分类显示表情包数量: ${relationshipVerification.categoryEmojiCount}`);
            console.log(`实际查询到的表情包数量: ${relationshipVerification.actualEmojisInCategory}`);
            console.log(`测试表情包在分类中: ${relationshipVerification.testEmojiFound ? '✅ 找到' : '🔴 未找到'}`);
            console.log(`表情包的分类ID: ${relationshipVerification.testEmojiCategoryId}`);
            
            const relationshipCorrect = relationshipVerification.testEmojiFound && 
                relationshipVerification.testEmojiCategoryId === relationshipVerification.categoryId;
            console.log(`关联关系: ${relationshipCorrect ? '✅ 正确' : '🔴 错误'}`);
        } else {
            console.log(`❌ 关联关系验证失败: ${relationshipVerification.error}`);
        }
        
        console.log('\n🎯 完整数据同步流程测试总结:');
        
        const allStepsSuccess = baselineData.success && 
                              databaseVerification.success && 
                              cloudFunctionVerification.success && 
                              relationshipVerification.success;
        
        const dataCreated = databaseVerification.category.found && 
                           databaseVerification.emoji.found && 
                           databaseVerification.banner.found;
        
        const cloudFunctionWorking = cloudFunctionVerification.category.found && 
                                   cloudFunctionVerification.emoji.found && 
                                   cloudFunctionVerification.banner.found;
        
        const relationshipWorking = relationshipVerification.success && 
                                  relationshipVerification.testEmojiFound;
        
        console.log(`数据创建: ${dataCreated ? '✅ 成功' : '🔴 失败'}`);
        console.log(`数据库保存: ${databaseVerification.success ? '✅ 成功' : '🔴 失败'}`);
        console.log(`云函数获取: ${cloudFunctionWorking ? '✅ 成功' : '🔴 失败'}`);
        console.log(`关联关系: ${relationshipWorking ? '✅ 正确' : '🔴 错误'}`);
        
        const overallSuccess = allStepsSuccess && dataCreated && cloudFunctionWorking && relationshipWorking;
        
        console.log(`\n🎯 整体同步流程: ${overallSuccess ? '🎉 完全成功！' : '⚠️ 存在问题'}`);
        
        if (overallSuccess) {
            console.log('✅ 管理后台 → 数据库 → 云函数 → 小程序 的完整数据流程已打通！');
        } else {
            console.log('🔴 数据同步流程存在问题，需要进一步修复。');
        }
        
        return {
            success: allStepsSuccess,
            overallSuccess: overallSuccess,
            details: {
                baseline: baselineData,
                database: databaseVerification,
                cloudFunction: cloudFunctionVerification,
                relationship: relationshipVerification
            }
        };
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
        await page.screenshot({ path: 'complete-data-sync-flow-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'complete-data-sync-flow.png', fullPage: true });
        console.log('\n📸 测试截图已保存: complete-data-sync-flow.png');
        
        console.log('\n⏸️ 测试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行完整测试
testCompleteDataSyncFlow().then(result => {
    console.log('\n🎯 完整数据同步流程测试最终结果:', result.success ? '成功' : '失败');
    
    if (result.success && result.overallSuccess) {
        console.log('🎉 完整数据同步流程测试通过！系统工作正常。');
    } else if (result.success) {
        console.log('⚠️ 测试完成但发现问题，需要进一步修复。');
    } else {
        console.log('❌ 测试失败，系统存在严重问题。');
    }
}).catch(console.error);
