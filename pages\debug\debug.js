// pages/debug/debug.js
Page({
  data: {
    logs: [],
    testResults: {}
  },

  onLoad() {
    this.log('调试页面加载完成')
  },

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
    const logMessage = `[${timestamp}] ${prefix} ${message}`
    
    console.log(logMessage)
    
    this.setData({
      logs: [...this.data.logs, logMessage]
    })
  },

  // 测试云函数调用
  async testCloudFunction() {
    this.setData({ logs: [] })
    this.log('开始测试云函数调用...')

    try {
      // 测试 dataAPI - getCategories
      this.log('测试获取分类数据...')
      const categoryResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getCategories'
        }
      })

      this.log(`分类数据调用结果: ${JSON.stringify(categoryResult.result)}`)
      
      if (categoryResult.result && categoryResult.result.success) {
        const categories = categoryResult.result.data || []
        this.log(`获取到 ${categories.length} 个分类`, 'success')
        categories.forEach(cat => {
          this.log(`  - ${cat.name} (${cat.emojiCount || 0} 个表情包)`)
        })
      } else {
        this.log(`分类数据获取失败: ${categoryResult.result?.message}`, 'error')
      }

      // 测试 dataAPI - getEmojis
      this.log('测试获取表情包数据...')
      const emojiResult = await wx.cloud.callFunction({
        name: 'dataAPI',
        data: {
          action: 'getEmojis',
          data: { category: 'all', page: 1, limit: 10 }
        }
      })

      this.log(`表情包数据调用结果: ${JSON.stringify(emojiResult.result)}`)
      
      if (emojiResult.result && emojiResult.result.success) {
        const emojis = emojiResult.result.data || []
        this.log(`获取到 ${emojis.length} 个表情包`, 'success')
        emojis.forEach(emoji => {
          this.log(`  - ${emoji.title} (${emoji.likes || 0} 赞)`)
        })
      } else {
        this.log(`表情包数据获取失败: ${emojiResult.result?.message}`, 'error')
      }

      // 测试 getBanners
      this.log('测试获取横幅数据...')
      const bannerResult = await wx.cloud.callFunction({
        name: 'getBanners'
      })

      this.log(`横幅数据调用结果: ${JSON.stringify(bannerResult.result)}`)
      
      if (bannerResult.result && bannerResult.result.success) {
        const banners = bannerResult.result.data || []
        this.log(`获取到 ${banners.length} 个横幅`, 'success')
        banners.forEach(banner => {
          this.log(`  - ${banner.title}`)
        })
      } else {
        this.log(`横幅数据获取失败: ${bannerResult.result?.message}`, 'error')
      }

    } catch (error) {
      this.log(`云函数调用异常: ${error.message}`, 'error')
    }
  },

  // 测试数据管理器
  async testDataManager() {
    this.setData({ logs: [] })
    this.log('开始测试数据管理器...')

    try {
      const DataManager = require('../../utils/newDataManager.js').DataManager

      // 测试获取分类数据
      this.log('测试 DataManager.getCategoriesWithStats...')
      const categories = await DataManager.getCategoriesWithStats({ forceRefresh: true })
      this.log(`DataManager 获取到 ${categories.length} 个分类`, categories.length > 0 ? 'success' : 'warning')

      // 测试获取表情包数据
      this.log('测试 DataManager.getAllEmojiData...')
      const emojis = await DataManager.getAllEmojiData('all', 1, 10, { forceRefresh: true })
      this.log(`DataManager 获取到 ${emojis.length} 个表情包`, emojis.length > 0 ? 'success' : 'warning')

      // 测试获取横幅数据
      this.log('测试 DataManager.getBannersData...')
      const banners = await DataManager.getBannersData({ forceRefresh: true })
      this.log(`DataManager 获取到 ${banners.length} 个横幅`, banners.length > 0 ? 'success' : 'warning')

    } catch (error) {
      this.log(`数据管理器测试异常: ${error.message}`, 'error')
    }
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] })
  },

  // 复制日志
  copyLogs() {
    const logText = this.data.logs.join('\n')
    wx.setClipboardData({
      data: logText,
      success: () => {
        wx.showToast({ title: '日志已复制', icon: 'success' })
      }
    })
  }
})
