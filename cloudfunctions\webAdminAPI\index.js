const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
  // 允许未认证用户调用（Web端专用）
  traceUser: false
})

const db = cloud.database()

// Web端专用管理API - 无需权限验证
exports.main = async (event, context) => {
  console.log('🌐 Web端管理API调用:', event)
  
  const { action, data, adminPassword } = event
  
  // 简单的密码验证
  if (adminPassword !== 'admin123456') {
    return {
      success: false,
      error: '管理员密码错误',
      code: 403
    }
  }
  
  console.log('✅ 密码验证通过，执行操作:', action)
  
  try {
    switch (action) {
      case 'getStats':
        return await getStats()
      case 'createCategory':
        return await createCategory(data)
      case 'getCategoryList':
        return await getCategoryList()
      case 'getCategories':
        return await getCategoryList() // 兼容性别名
      case 'addEmoji':
        return await addEmoji(data)
      case 'getEmojis':
        return await getEmojis(data)
      case 'deleteEmoji':
        return await deleteEmoji(data)
      case 'deleteCategory':
        return await deleteCategory(data)
      case 'syncData':
        return await syncData(data)
      case 'clearAllData':
        return await clearAllData()
      case 'initSyncNotifications':
        return await initSyncNotifications()
      case 'getSyncNotifications':
        return await getSyncNotifications(data?.limit)
      default:
        return { success: false, error: '未知操作: ' + action }
    }
  } catch (error) {
    console.error('操作失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取统计数据
async function getStats() {
  try {
    const usersResult = await db.collection('users').count()
    const emojisResult = await db.collection('emojis').count()
    const categoriesResult = await db.collection('categories').count()
    
    return {
      success: true,
      data: {
        usersCount: usersResult.total || 0,
        emojisCount: emojisResult.total || 0,
        categoriesCount: categoriesResult.total || 0,
        timestamp: new Date()
      }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 创建分类
async function createCategory(data) {
  try {
    const { name, icon, description, sort = 0 } = data
    
    const result = await db.collection('categories').add({
      data: {
        name,
        icon,
        description: description || '',
        sort,
        status: 'show',  // 使用管理后台期望的状态值
        count: 0,        // 使用管理后台期望的字段名
        createTime: new Date(),
        updateTime: new Date()
      }
    })
    
    return { success: true, message: '分类创建成功', id: result._id }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 获取分类列表
async function getCategoryList() {
  try {
    const categories = await db.collection('categories')
      .orderBy('sort', 'asc')
      .get()
    
    return { success: true, data: categories.data }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 添加表情包
async function addEmoji(data) {
  try {
    const {
      title, name,
      categoryId,
      category,
      tags,
      imageUrl, fileUrl,
      description,
      author
    } = data

    // 获取分类信息（如果没有提供category）
    let categoryName = category
    if (!categoryName && categoryId) {
      try {
        const categoryDoc = await db.collection('categories').doc(categoryId).get()
        if (categoryDoc.data) {
          categoryName = categoryDoc.data.name
        }
      } catch (error) {
        console.warn('获取分类名称失败:', error)
      }
    }

    const result = await db.collection('emojis').add({
      data: {
        title: title || name,           // 使用 title，兼容 name
        category: categoryName || '未分类',  // 主要使用category字段存储分类名称
        categoryId,                     // 保留categoryId用于查询优化
        imageUrl: imageUrl || fileUrl,  // 使用 imageUrl，兼容 fileUrl
        tags: tags || [],
        description: description || '',
        author: author || '管理员',
        status: 'published',
        likes: 0,                       // 使用 likes 而不是 likeCount
        collections: 0,
        downloads: 0,                   // 使用 downloads 而不是 downloadCount
        views: 0,                       // 添加浏览次数字段
        createTime: new Date(),
        updateTime: new Date()
      }
    })

    return { success: true, message: '表情包添加成功', id: result._id }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 获取表情包列表
async function getEmojis(data) {
  try {
    const { page = 1, limit = 20 } = data || {}
    const skip = (page - 1) * limit
    
    const emojis = await db.collection('emojis')
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()
    
    return { success: true, data: emojis.data }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 删除表情包
async function deleteEmoji(data) {
  try {
    const { id } = data
    await db.collection('emojis').doc(id).remove()
    return { success: true, message: '表情包删除成功' }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 删除分类
async function deleteCategory(data) {
  try {
    const { id } = data
    await db.collection('categories').doc(id).remove()
    return { success: true, message: '分类删除成功' }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 创建同步通知记录
async function createSyncNotification(type, operation, data) {
  try {
    const notification = {
      type: type, // 'emojis', 'categories', 'banners'
      operation: operation, // 'create', 'update', 'delete', 'sync'
      timestamp: new Date(),
      dataCount: Array.isArray(data) ? data.length : 1,
      status: 'pending',
      details: {
        affectedIds: Array.isArray(data) ? data.map(item => item._id || item.id) : [data._id || data.id],
        summary: `${operation} ${type}: ${Array.isArray(data) ? data.length : 1} items`
      }
    }

    const result = await db.collection('sync_notifications').add({
      data: notification
    })

    console.log('✅ 同步通知创建成功:', result._id)
    return { success: true, notificationId: result._id }
  } catch (error) {
    console.error('❌ 创建同步通知失败:', error)
    return { success: false, error: error.message }
  }
}

// 数据同步功能 - 增强版，支持实时通知
async function syncData(data) {
  try {
    const { type, data: syncData } = data
    console.log('🔄 开始同步数据:', type, '数量:', syncData?.length || 0)

    // 创建同步通知
    await createSyncNotification(type, 'sync', syncData)

    let result
    switch (type) {
      case 'categories':
        result = await syncCategories(syncData)
        break
      case 'emojis':
        result = await syncEmojis(syncData)
        break
      case 'banners':
        result = await syncBanners(syncData)
        break
      default:
        return { success: false, error: '未知的同步类型: ' + type }
    }

    // 如果同步成功，更新通知状态
    if (result.success) {
      await updateSyncNotificationStatus(type, 'completed')
    } else {
      await updateSyncNotificationStatus(type, 'failed')
    }

    return result
  } catch (error) {
    console.error('同步失败:', error)
    await updateSyncNotificationStatus(type, 'failed')
    return { success: false, error: error.message }
  }
}

// 更新同步通知状态
async function updateSyncNotificationStatus(type, status) {
  try {
    // 查找最近的待处理通知
    const notifications = await db.collection('sync_notifications')
      .where({
        type: type,
        status: 'pending'
      })
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get()

    if (notifications.data.length > 0) {
      const notificationId = notifications.data[0]._id
      await db.collection('sync_notifications').doc(notificationId).update({
        data: {
          status: status,
          completedAt: new Date()
        }
      })
      console.log(`✅ 同步通知状态更新: ${type} -> ${status}`)
    }
  } catch (error) {
    console.error('❌ 更新同步通知状态失败:', error)
  }
}

// 同步分类数据 - 全量替换模式
async function syncCategories(categories) {
  try {
    console.log('🔄 开始全量同步分类数据，数量:', categories.length)

    // 第一步：清空云数据库中的所有分类数据
    const removeResult = await db.collection('categories').where({}).remove()
    console.log('✅ 清空云数据库分类数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (categories.length > 0) {
      for (const category of categories) {
        try {
          await db.collection('categories').add({
            data: {
              name: category.name,
              icon: category.icon || '',
              description: category.description || '',
              sort: category.sort || 0,
              status: 'active',
              emojiCount: 0,
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入分类失败:', category.name, error)
        }
      }
    }

    console.log('🎉 分类数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: categories.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步分类数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 同步表情包数据 - 全量替换模式
async function syncEmojis(emojis) {
  try {
    console.log('🔄 开始全量同步表情包数据，数量:', emojis.length)

    // 第一步：清空云数据库中的所有表情包数据
    const removeResult = await db.collection('emojis').where({}).remove()
    console.log('✅ 清空云数据库表情包数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (emojis.length > 0) {
      for (const emoji of emojis) {
        try {
          await db.collection('emojis').add({
            data: {
              title: emoji.title,
              category: emoji.category,
              imageUrl: emoji.imageUrl,
              tags: emoji.tags || [],
              description: emoji.description || '',
              status: 'published',
              likes: emoji.likes || 0,
              downloads: emoji.downloads || 0,
              collections: emoji.collections || 0,
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入表情包失败:', emoji.title, error)
        }
      }
    }

    console.log('🎉 表情包数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: emojis.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步表情包数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 同步横幅数据 - 全量替换模式
async function syncBanners(banners) {
  try {
    console.log('🔄 开始全量同步横幅数据，数量:', banners.length)

    // 第一步：清空云数据库中的所有横幅数据
    const removeResult = await db.collection('banners').where({}).remove()
    console.log('✅ 清空云数据库横幅数据:', removeResult.stats.removed, '条')

    // 第二步：批量插入管理后台的数据
    let successCount = 0
    if (banners.length > 0) {
      for (const banner of banners) {
        try {
          await db.collection('banners').add({
            data: {
              title: banner.title,
              imageUrl: banner.imageUrl,
              linkUrl: banner.linkUrl || '',
              linkType: banner.linkType || 'page',
              priority: banner.priority || banner.sort || 0,  // 使用priority字段
              status: 'show',  // 使用管理后台期望的状态值
              description: banner.description || '',
              createTime: new Date(),
              updateTime: new Date(),
              syncTime: new Date()
            }
          })
          successCount++
        } catch (error) {
          console.error('插入横幅失败:', banner.title, error)
        }
      }
    }

    console.log('🎉 横幅数据全量同步完成')
    return {
      success: true,
      message: `全量同步完成：删除${removeResult.stats.removed}条，新增${successCount}条`,
      syncedCount: successCount,
      totalCount: banners.length,
      removed: removeResult.stats.removed,
      syncMode: 'full_replace'
    }
  } catch (error) {
    console.error('同步横幅数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 清空所有数据
async function clearAllData() {
  try {
    console.log('开始清空所有数据...')

    // 清空表情包数据
    const emojisResult = await db.collection('emojis').where({}).remove()
    console.log('清空表情包数据:', emojisResult.stats.removed, '条')

    // 清空分类数据
    const categoriesResult = await db.collection('categories').where({}).remove()
    console.log('清空分类数据:', categoriesResult.stats.removed, '条')

    // 清空横幅数据
    const bannersResult = await db.collection('banners').where({}).remove()
    console.log('清空横幅数据:', bannersResult.stats.removed, '条')

    // 清空用户行为数据（可选）
    const actionsResult = await db.collection('user_actions').where({}).remove()
    console.log('清空用户行为数据:', actionsResult.stats.removed, '条')

    return {
      success: true,
      message: '数据清空完成',
      data: {
        emojis: emojisResult.stats.removed,
        categories: categoriesResult.stats.removed,
        banners: bannersResult.stats.removed,
        userActions: actionsResult.stats.removed
      }
    }
  } catch (error) {
    console.error('清空数据失败:', error)
    return { success: false, error: error.message }
  }
}

// 创建同步通知记录
async function createSyncNotification(type, operation, data) {
  try {
    const notification = {
      type: type, // 'emojis', 'categories', 'banners'
      operation: operation, // 'create', 'update', 'delete', 'sync'
      timestamp: new Date(),
      dataCount: Array.isArray(data) ? data.length : 1,
      status: 'pending',
      details: {
        affectedIds: Array.isArray(data) ? data.map(item => item._id || item.id) : [data._id || data.id],
        summary: `${operation} ${type}: ${Array.isArray(data) ? data.length : 1} items`,
        source: 'admin'
      },
      metadata: {
        version: '1.0',
        environment: cloud.DYNAMIC_CURRENT_ENV,
        createdBy: 'webAdmin'
      }
    }

    const result = await db.collection('sync_notifications').add({
      data: notification
    })

    console.log('✅ 同步通知创建成功:', result._id)
    return { success: true, notificationId: result._id }
  } catch (error) {
    console.error('❌ 创建同步通知失败:', error)
    return { success: false, error: error.message }
  }
}

// 更新同步通知状态
async function updateSyncNotificationStatus(type, status) {
  try {
    // 查找最近的待处理通知
    const notifications = await db.collection('sync_notifications')
      .where({
        type: type,
        status: 'pending'
      })
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get()

    if (notifications.data.length > 0) {
      const notificationId = notifications.data[0]._id
      await db.collection('sync_notifications').doc(notificationId).update({
        data: {
          status: status,
          completedAt: new Date(),
          'metadata.updatedBy': 'webAdmin'
        }
      })
      console.log(`✅ 同步通知状态更新: ${type} -> ${status}`)
      return { success: true, notificationId }
    } else {
      console.log(`⚠️ 未找到待更新的通知: ${type}`)
      return { success: false, error: '未找到待更新的通知' }
    }
  } catch (error) {
    console.error('❌ 更新同步通知状态失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取同步通知列表
async function getSyncNotifications(limit = 50) {
  try {
    const result = await db.collection('sync_notifications')
      .orderBy('timestamp', 'desc')
      .limit(limit)
      .get()

    return {
      success: true,
      data: result.data,
      total: result.data.length
    }
  } catch (error) {
    console.error('❌ 获取同步通知失败:', error)
    return { success: false, error: error.message }
  }
}

// 初始化同步通知集合
async function initSyncNotifications() {
  try {
    console.log('🔄 开始初始化 sync_notifications 集合...')

    // 1. 创建集合（如果不存在）
    try {
      await db.createCollection('sync_notifications')
      console.log('✅ sync_notifications 集合创建成功')
    } catch (error) {
      if (error.errCode === -1 && error.errMsg.includes('collection already exists')) {
        console.log('ℹ️ sync_notifications 集合已存在')
      } else {
        throw error
      }
    }

    // 2. 创建索引以优化查询性能
    const collection = db.collection('sync_notifications')

    // 按时间戳索引（用于按时间排序）
    try {
      await collection.createIndex({
        keys: { timestamp: -1 },
        name: 'timestamp_desc'
      })
      console.log('✅ timestamp 索引创建成功')
    } catch (error) {
      console.log('ℹ️ timestamp 索引可能已存在:', error.errMsg)
    }

    // 按类型和状态索引（用于查询特定类型的通知）
    try {
      await collection.createIndex({
        keys: { type: 1, status: 1 },
        name: 'type_status'
      })
      console.log('✅ type_status 索引创建成功')
    } catch (error) {
      console.log('ℹ️ type_status 索引可能已存在:', error.errMsg)
    }

    // 3. 验证集合和索引
    const stats = await collection.count()
    console.log(`📊 sync_notifications 集合统计: ${stats.total} 条记录`)

    return {
      success: true,
      message: 'sync_notifications 集合初始化完成',
      details: {
        collectionExists: true,
        indexesCreated: ['timestamp_desc', 'type_status'],
        recordCount: stats.total
      }
    }

  } catch (error) {
    console.error('❌ 初始化 sync_notifications 集合失败:', error)
    return {
      success: false,
      error: error.message,
      details: error
    }
  }
}
