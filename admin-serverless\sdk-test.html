<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDK加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SDK加载测试</h1>
        <p>测试不同CDN源的CloudBase SDK加载情况</p>
        
        <button onclick="testSDKLoading()">开始测试SDK加载</button>
        
        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        async function loadScript(src, name) {
            return new Promise((resolve, reject) => {
                log(`尝试加载 ${name}: ${src}`, 'info');
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    log(`✅ ${name} 加载成功`, 'success');
                    resolve(true);
                };
                script.onerror = () => {
                    log(`❌ ${name} 加载失败`, 'error');
                    resolve(false);
                };
                document.head.appendChild(script);
            });
        }

        async function testSDKLoading() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status info">开始测试SDK加载...</div>';

            // 清理之前的SDK
            if (window.cloudbase) {
                delete window.cloudbase;
            }

            const cdnSources = [
                {
                    name: 'unpkg CDN (1.8.0)',
                    url: 'https://unpkg.com/@cloudbase/js-sdk@1.8.0/dist/index.umd.js'
                },
                {
                    name: 'jsdelivr CDN (1.8.0)',
                    url: 'https://cdn.jsdelivr.net/npm/@cloudbase/js-sdk@1.8.0/dist/index.umd.js'
                },
                {
                    name: '腾讯云官方CDN (1.8.0)',
                    url: 'https://static.cloudbase.net/cloudbase-js-sdk/1.8.0/cloudbase.full.js'
                },
                {
                    name: '腾讯云官方CDN (2.17.5)',
                    url: 'https://static.cloudbase.net/cloudbase-js-sdk/2.17.5/cloudbase.full.js'
                }
            ];

            for (const source of cdnSources) {
                const success = await loadScript(source.url, source.name);
                
                if (success) {
                    // 检查SDK是否真的可用
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                    
                    if (typeof window.cloudbase !== 'undefined') {
                        log(`🎉 SDK加载成功！类型: ${typeof window.cloudbase}`, 'success');
                        log(`SDK版本: ${JSON.stringify(window.cloudbase.version || 'unknown')}`, 'info');
                        
                        // 测试初始化
                        try {
                            const app = window.cloudbase.init({
                                env: 'cloud1-5g6pvnpl88dc0142'
                            });
                            log('✅ SDK初始化成功', 'success');
                            
                            const auth = app.auth();
                            log(`Auth对象类型: ${typeof auth}`, 'info');
                            log(`anonymousAuthProvider方法: ${typeof auth.anonymousAuthProvider}`, 'info');
                            
                            if (typeof auth.anonymousAuthProvider === 'function') {
                                log('🎯 匿名登录方法可用！', 'success');
                            } else {
                                log('⚠️ 匿名登录方法不可用', 'warning');
                            }
                            
                        } catch (initError) {
                            log(`❌ SDK初始化失败: ${initError.message}`, 'error');
                        }
                        
                        break; // 成功了就不用继续测试其他CDN
                    } else {
                        log(`⚠️ 脚本加载了但SDK对象不存在`, 'warning');
                    }
                }
            }

            if (typeof window.cloudbase === 'undefined') {
                log('❌ 所有CDN源都失败了', 'error');
                log('建议检查网络连接或使用本地SDK文件', 'warning');
            }
        }
    </script>
</body>
</html>
