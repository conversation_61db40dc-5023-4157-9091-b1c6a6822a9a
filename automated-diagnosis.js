/**
 * 自动化数据同步问题诊断
 * 不依赖Playwright，直接分析文件和配置
 */

const fs = require('fs');
const path = require('path');

class AutomatedDiagnosis {
    constructor() {
        this.results = {
            fileAnalysis: {},
            configAnalysis: {},
            codeAnalysis: {},
            recommendations: []
        };
    }

    // 分析管理后台文件
    analyzeAdminFiles() {
        console.log('🔍 分析管理后台文件...');
        
        const adminPath = path.join(__dirname, 'admin', 'index.html');
        
        if (!fs.existsSync(adminPath)) {
            this.results.fileAnalysis.admin = {
                exists: false,
                error: '管理后台文件不存在'
            };
            return;
        }

        const adminContent = fs.readFileSync(adminPath, 'utf8');
        
        // 检查云开发SDK引用
        const hasCloudbaseSDK = adminContent.includes('cloudbase') || adminContent.includes('tcb');
        const cloudbaseSDKPattern = /https:\/\/.*cloudbase.*\.js/g;
        const sdkUrls = adminContent.match(cloudbaseSDKPattern) || [];
        
        // 检查环境配置
        const envPattern = /env:\s*['"`]([^'"`]+)['"`]/g;
        const envMatches = [...adminContent.matchAll(envPattern)];
        const environments = envMatches.map(match => match[1]);
        
        // 检查数据保存逻辑
        const hasDatabaseAdd = adminContent.includes('.add(') || adminContent.includes('database.add');
        const hasCloudAPICall = adminContent.includes('CloudAPI') || adminContent.includes('cloudAPI');
        
        // 检查字段使用
        const usesCategoryField = adminContent.includes('category:') && !adminContent.includes('categoryId:');
        const usesCategoryIdField = adminContent.includes('categoryId:');
        
        this.results.fileAnalysis.admin = {
            exists: true,
            hasCloudbaseSDK,
            sdkUrls,
            environments,
            hasDatabaseAdd,
            hasCloudAPICall,
            usesCategoryField,
            usesCategoryIdField,
            fileSize: adminContent.length
        };

        console.log('✅ 管理后台文件分析完成');
        console.log('   - 云开发SDK:', hasCloudbaseSDK ? '✅' : '❌');
        console.log('   - 环境配置:', environments.length > 0 ? environments.join(', ') : '❌ 未找到');
        console.log('   - 数据保存逻辑:', hasDatabaseAdd ? '✅' : '❌');
        console.log('   - 字段使用: category字段', usesCategoryField ? '✅' : '❌');
    }

    // 分析云函数文件
    analyzeCloudFunctions() {
        console.log('🔍 分析云函数文件...');
        
        const dataAPIPath = path.join(__dirname, 'cloudfunctions', 'dataAPI', 'index.js');
        
        if (!fs.existsSync(dataAPIPath)) {
            this.results.fileAnalysis.cloudFunction = {
                exists: false,
                error: 'dataAPI云函数文件不存在'
            };
            return;
        }

        const cloudFunctionContent = fs.readFileSync(dataAPIPath, 'utf8');
        
        // 检查修复代码
        const hasOrQuery = cloudFunctionContent.includes('$or');
        const hasCategoryCompatibility = cloudFunctionContent.includes('category:') && cloudFunctionContent.includes('categoryId:');
        const hasFixedGetEmojis = cloudFunctionContent.includes('categoryConditions');
        
        // 检查函数导出
        const hasGetCategories = cloudFunctionContent.includes('getCategories');
        const hasGetEmojis = cloudFunctionContent.includes('getEmojis');
        const hasSearchEmojis = cloudFunctionContent.includes('searchEmojis');
        
        this.results.fileAnalysis.cloudFunction = {
            exists: true,
            hasOrQuery,
            hasCategoryCompatibility,
            hasFixedGetEmojis,
            hasGetCategories,
            hasGetEmojis,
            hasSearchEmojis,
            fileSize: cloudFunctionContent.length
        };

        console.log('✅ 云函数文件分析完成');
        console.log('   - 修复代码($or查询):', hasOrQuery ? '✅' : '❌');
        console.log('   - 字段兼容性:', hasCategoryCompatibility ? '✅' : '❌');
        console.log('   - 修复的getEmojis:', hasFixedGetEmojis ? '✅' : '❌');
    }

    // 分析小程序文件
    analyzeMiniProgramFiles() {
        console.log('🔍 分析小程序文件...');
        
        const indexJSPath = path.join(__dirname, 'pages', 'index', 'index.js');
        const categoryJSPath = path.join(__dirname, 'pages', 'category', 'category.js');
        
        const results = {};
        
        // 分析首页文件
        if (fs.existsSync(indexJSPath)) {
            const indexContent = fs.readFileSync(indexJSPath, 'utf8');
            const hasDataAPICall = indexContent.includes('dataAPI');
            const hasGetCategories = indexContent.includes('getCategories');
            const hasGetEmojis = indexContent.includes('getEmojis');
            
            results.index = {
                exists: true,
                hasDataAPICall,
                hasGetCategories,
                hasGetEmojis
            };
        } else {
            results.index = { exists: false };
        }
        
        // 分析分类页面文件
        if (fs.existsSync(categoryJSPath)) {
            const categoryContent = fs.readFileSync(categoryJSPath, 'utf8');
            const hasDataAPICall = categoryContent.includes('dataAPI');
            const hasLoadData = categoryContent.includes('loadData') || categoryContent.includes('loadCategoryData');
            
            results.category = {
                exists: true,
                hasDataAPICall,
                hasLoadData
            };
        } else {
            results.category = { exists: false };
        }
        
        this.results.fileAnalysis.miniProgram = results;
        
        console.log('✅ 小程序文件分析完成');
        console.log('   - 首页文件:', results.index.exists ? '✅' : '❌');
        console.log('   - 分类页面文件:', results.category.exists ? '✅' : '❌');
    }

    // 分析配置文件
    analyzeConfigFiles() {
        console.log('🔍 分析配置文件...');
        
        // 分析project.config.json
        const projectConfigPath = path.join(__dirname, 'project.config.json');
        let projectConfig = {};
        
        if (fs.existsSync(projectConfigPath)) {
            try {
                const content = fs.readFileSync(projectConfigPath, 'utf8');
                projectConfig = JSON.parse(content);
            } catch (error) {
                projectConfig = { error: '配置文件解析失败' };
            }
        }
        
        // 分析app.json
        const appConfigPath = path.join(__dirname, 'app.json');
        let appConfig = {};
        
        if (fs.existsSync(appConfigPath)) {
            try {
                const content = fs.readFileSync(appConfigPath, 'utf8');
                appConfig = JSON.parse(content);
            } catch (error) {
                appConfig = { error: '应用配置文件解析失败' };
            }
        }
        
        this.results.configAnalysis = {
            projectConfig,
            appConfig,
            hasCloudFunction: projectConfig.cloudfunctionRoot ? true : false,
            cloudFunctionRoot: projectConfig.cloudfunctionRoot
        };
        
        console.log('✅ 配置文件分析完成');
        console.log('   - 项目配置:', fs.existsSync(projectConfigPath) ? '✅' : '❌');
        console.log('   - 应用配置:', fs.existsSync(appConfigPath) ? '✅' : '❌');
        console.log('   - 云函数配置:', this.results.configAnalysis.hasCloudFunction ? '✅' : '❌');
    }

    // 生成问题诊断
    generateDiagnosis() {
        console.log('🔍 生成问题诊断...');
        
        const issues = [];
        const recommendations = [];
        
        // 检查管理后台问题
        const admin = this.results.fileAnalysis.admin;
        if (!admin || !admin.exists) {
            issues.push('❌ 管理后台文件不存在');
            recommendations.push('检查admin/index.html文件是否存在');
        } else {
            if (!admin.hasCloudbaseSDK) {
                issues.push('❌ 管理后台缺少云开发SDK');
                recommendations.push('在管理后台添加云开发SDK引用');
            }
            
            if (admin.environments.length === 0) {
                issues.push('❌ 管理后台没有配置云开发环境ID');
                recommendations.push('检查管理后台的云开发环境配置');
            }
            
            if (!admin.hasDatabaseAdd) {
                issues.push('❌ 管理后台缺少数据库保存逻辑');
                recommendations.push('检查管理后台的数据保存代码');
            }
            
            if (admin.usesCategoryField && !admin.usesCategoryIdField) {
                issues.push('⚠️ 管理后台使用category字段，可能与云函数不兼容');
                recommendations.push('这是数据同步问题的主要原因，需要云函数兼容性修复');
            }
        }
        
        // 检查云函数问题
        const cloudFunc = this.results.fileAnalysis.cloudFunction;
        if (!cloudFunc || !cloudFunc.exists) {
            issues.push('❌ dataAPI云函数文件不存在');
            recommendations.push('检查cloudfunctions/dataAPI/index.js文件');
        } else {
            if (!cloudFunc.hasOrQuery) {
                issues.push('❌ 云函数缺少$or查询修复');
                recommendations.push('云函数需要添加字段兼容性修复');
            }
            
            if (!cloudFunc.hasCategoryCompatibility) {
                issues.push('❌ 云函数缺少category字段兼容性');
                recommendations.push('云函数需要同时支持category和categoryId字段');
            }
        }
        
        // 检查小程序问题
        const miniProgram = this.results.fileAnalysis.miniProgram;
        if (!miniProgram.index.exists) {
            issues.push('❌ 小程序首页文件不存在');
        }
        if (!miniProgram.category.exists) {
            issues.push('❌ 小程序分类页面文件不存在');
        }
        
        // 根本原因分析
        let rootCause = '未知问题';
        if (issues.some(issue => issue.includes('管理后台缺少云开发SDK'))) {
            rootCause = '管理后台云开发SDK配置问题';
        } else if (issues.some(issue => issue.includes('category字段'))) {
            rootCause = '数据字段格式不兼容问题';
        } else if (issues.some(issue => issue.includes('云函数'))) {
            rootCause = '云函数部署或代码问题';
        }
        
        this.results.diagnosis = {
            issues,
            recommendations,
            rootCause,
            severity: issues.length > 3 ? 'high' : issues.length > 1 ? 'medium' : 'low'
        };
        
        console.log('\n📊 诊断结果:');
        console.log('   - 发现问题:', issues.length, '个');
        console.log('   - 问题严重程度:', this.results.diagnosis.severity);
        console.log('   - 根本原因:', rootCause);
        
        if (issues.length > 0) {
            console.log('\n❌ 发现的问题:');
            issues.forEach((issue, index) => {
                console.log(`   ${index + 1}. ${issue}`);
            });
        }
        
        if (recommendations.length > 0) {
            console.log('\n💡 建议的解决方案:');
            recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
        }
    }

    // 生成修复脚本
    generateFixScript() {
        console.log('🔧 生成修复脚本...');
        
        const admin = this.results.fileAnalysis.admin;
        const cloudFunc = this.results.fileAnalysis.cloudFunction;
        
        let fixScript = '#!/bin/bash\n# 自动修复脚本\n\n';
        
        // 如果管理后台缺少SDK
        if (admin && admin.exists && !admin.hasCloudbaseSDK) {
            fixScript += 'echo "修复管理后台云开发SDK..."\n';
            fixScript += '# 需要手动在admin/index.html中添加云开发SDK引用\n\n';
        }
        
        // 如果云函数需要修复
        if (cloudFunc && cloudFunc.exists && !cloudFunc.hasOrQuery) {
            fixScript += 'echo "部署修复后的云函数..."\n';
            fixScript += 'cd cloudfunctions/dataAPI\n';
            fixScript += 'npm install\n';
            fixScript += 'cd ../..\n';
            fixScript += '# 使用微信开发者工具部署云函数\n\n';
        }
        
        fixScript += 'echo "修复完成！请重新测试数据同步。"\n';
        
        fs.writeFileSync('auto-fix.sh', fixScript);
        console.log('✅ 修复脚本已生成: auto-fix.sh');
    }

    // 运行完整诊断
    async run() {
        console.log('🚀 开始自动化数据同步问题诊断...\n');
        
        try {
            this.analyzeAdminFiles();
            console.log('');
            
            this.analyzeCloudFunctions();
            console.log('');
            
            this.analyzeMiniProgramFiles();
            console.log('');
            
            this.analyzeConfigFiles();
            console.log('');
            
            this.generateDiagnosis();
            console.log('');
            
            this.generateFixScript();
            
            // 保存完整报告
            const report = {
                timestamp: new Date().toISOString(),
                results: this.results,
                summary: {
                    totalIssues: this.results.diagnosis.issues.length,
                    severity: this.results.diagnosis.severity,
                    rootCause: this.results.diagnosis.rootCause
                }
            };
            
            fs.writeFileSync('diagnosis-report.json', JSON.stringify(report, null, 2));
            console.log('📋 完整诊断报告已保存: diagnosis-report.json');
            
            return report;
            
        } catch (error) {
            console.error('❌ 诊断过程中发生错误:', error);
            throw error;
        }
    }
}

// 运行诊断
async function runAutomatedDiagnosis() {
    const diagnosis = new AutomatedDiagnosis();
    return await diagnosis.run();
}

// 如果直接运行此文件
if (require.main === module) {
    runAutomatedDiagnosis().catch(console.error);
}

module.exports = { AutomatedDiagnosis, runAutomatedDiagnosis };
