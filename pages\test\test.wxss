/* 测试页面样式 */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

.test-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 20rpx;
}

.btn.primary {
  background: #27ae60;
  color: white;
}

.btn.secondary {
  background: #3498db;
  color: white;
}

.btn.admin {
  background: #9b59b6;
  color: white;
}

.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #3498db;
  padding-left: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin: 10rpx 0;
  border-radius: 10rpx;
  background: #f8f9fa;
}

.status-label {
  font-size: 28rpx;
  color: #2c3e50;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #3498db;
  padding-left: 20rpx;
}

.result-content {
  background: #f8f9fa;
  border: 2rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 30rpx;
  max-height: 800rpx;
  overflow-y: auto;
}

.result-text {
  font-family: monospace;
  font-size: 24rpx;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.help-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.help-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
  border-left: 8rpx solid #3498db;
  padding-left: 20rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
}

.help-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  line-height: 1.5;
}
