<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云开发诊断页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .section {
            background: white;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border-left: 4px solid #007bff;
        }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .step { margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔍 云开发诊断页面</h1>
    
    <div class="section">
        <h2>📋 诊断步骤</h2>
        <button onclick="runFullDiagnosis()">🚀 开始完整诊断</button>
        <button onclick="clearLogs()">🧹 清空日志</button>
        <div id="diagnosisLog" class="log"></div>
    </div>
    
    <div class="section">
        <h2>🔧 手动测试</h2>
        <button onclick="testSDKLoad()">1. 测试SDK加载</button>
        <button onclick="testCloudInit()">2. 测试云开发初始化</button>
        <button onclick="testAuth()">3. 测试身份认证</button>
        <button onclick="testFunction()">4. 测试云函数调用</button>
    </div>

    <!-- 尝试多个SDK版本 -->
    <script id="sdk1" src="https://unpkg.com/@cloudbase/js-sdk@2.0.1/dist/index.umd.js"></script>
    <script id="sdk2" src="https://static.cloudbase.net/cloudbase-js-sdk/1.6.0/cloudbase.full.js"></script>
    
    <script>
        let logDiv = document.getElementById('diagnosisLog');
        let tcbApp = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logMessage;
            logDiv.className = `log ${type}`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            logDiv.textContent = '';
            logDiv.className = 'log';
        }
        
        async function runFullDiagnosis() {
            clearLogs();
            log('🚀 开始完整诊断...', 'info');
            
            try {
                await testSDKLoad();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testCloudInit();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testAuth();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testFunction();
                
                log('✅ 诊断完成！', 'success');
            } catch (error) {
                log(`❌ 诊断过程中出错: ${error.message}`, 'error');
            }
        }
        
        async function testSDKLoad() {
            log('📦 测试SDK加载...', 'info');
            
            // 检查全局对象
            log(`typeof cloudbase: ${typeof cloudbase}`);
            log(`typeof tcb: ${typeof tcb}`);
            log(`typeof window.cloudbase: ${typeof window.cloudbase}`);
            log(`typeof window.tcb: ${typeof window.tcb}`);
            
            // 等待SDK加载
            let attempts = 0;
            while (attempts < 10) {
                if (typeof cloudbase !== 'undefined' || typeof tcb !== 'undefined') {
                    log('✅ SDK加载成功', 'success');
                    return;
                }
                await new Promise(resolve => setTimeout(resolve, 500));
                attempts++;
                log(`⏳ 等待SDK加载... (${attempts}/10)`);
            }
            
            throw new Error('SDK加载失败');
        }
        
        async function testCloudInit() {
            log('☁️ 测试云开发初始化...', 'info');
            
            const envId = 'cloud1-5g6pvnpl88dc0142';
            log(`环境ID: ${envId}`);
            
            try {
                let sdk = null;
                if (typeof cloudbase !== 'undefined') {
                    sdk = cloudbase;
                    log('使用 cloudbase SDK');
                } else if (typeof tcb !== 'undefined') {
                    sdk = tcb;
                    log('使用 tcb SDK');
                } else {
                    throw new Error('没有可用的SDK');
                }
                
                // 尝试不同的初始化配置
                const configs = [
                    { env: envId },
                    { env: envId, region: 'ap-shanghai' },
                    { env: envId, region: 'ap-beijing' },
                    { env: envId, region: 'ap-guangzhou' }
                ];
                
                for (let config of configs) {
                    try {
                        log(`尝试配置: ${JSON.stringify(config)}`);
                        tcbApp = sdk.init(config);
                        log('✅ 云开发初始化成功', 'success');
                        return;
                    } catch (error) {
                        log(`❌ 配置失败: ${error.message}`, 'warning');
                    }
                }
                
                throw new Error('所有初始化配置都失败');
                
            } catch (error) {
                log(`❌ 云开发初始化失败: ${error.message}`, 'error');
                throw error;
            }
        }
        
        async function testAuth() {
            log('🔐 测试身份认证...', 'info');
            
            if (!tcbApp) {
                throw new Error('云开发未初始化');
            }
            
            try {
                const auth = tcbApp.auth();
                log('✅ 获取auth对象成功');
                
                // 检查登录状态
                try {
                    const loginState = await auth.getLoginState();
                    log(`当前登录状态: ${JSON.stringify(loginState)}`);
                } catch (error) {
                    log(`获取登录状态失败: ${error.message}`, 'warning');
                }
                
                // 尝试匿名登录
                try {
                    await auth.anonymousAuthProvider().signIn();
                    log('✅ 匿名登录成功', 'success');
                } catch (error) {
                    log(`匿名登录失败: ${error.message}`, 'warning');
                }
                
            } catch (error) {
                log(`❌ 身份认证测试失败: ${error.message}`, 'error');
                throw error;
            }
        }
        
        async function testFunction() {
            log('⚡ 测试云函数调用...', 'info');
            
            if (!tcbApp) {
                throw new Error('云开发未初始化');
            }
            
            try {
                // 检查云函数调用方法
                log(`tcbApp.callFunction: ${typeof tcbApp.callFunction}`);
                log(`tcbApp.functions: ${typeof tcbApp.functions}`);
                
                const callFunction = tcbApp.callFunction || tcbApp.functions;
                if (!callFunction) {
                    throw new Error('云函数调用方法不存在');
                }
                
                // 尝试调用云函数
                const result = await callFunction({
                    name: 'adminAPI',
                    data: {
                        action: 'getStats',
                        adminPassword: 'admin123456'
                    }
                });
                
                log(`✅ 云函数调用成功: ${JSON.stringify(result)}`, 'success');
                
            } catch (error) {
                log(`❌ 云函数调用失败: ${error.message}`, 'error');
                log(`错误详情: ${JSON.stringify(error)}`, 'error');
                throw error;
            }
        }
        
        // 页面加载时自动开始诊断
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('🔍 页面加载完成，等待SDK加载...');
                setTimeout(runFullDiagnosis, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
