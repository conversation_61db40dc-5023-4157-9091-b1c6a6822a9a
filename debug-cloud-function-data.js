// 深度调试云函数获取的数据结构
const { chromium } = require('playwright');

async function debugCloudFunctionData() {
    console.log('🔍 深度调试云函数获取的数据结构...\n');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('分类数据查询结果') || text.includes('表情包查询结果') || text.includes('dataAPI')) {
            console.log(`[CONSOLE] ${text}`);
        }
    });
    
    try {
        await page.goto('http://localhost:9001/main.html', { 
            waitUntil: 'networkidle',
            timeout: 30000 
        });
        
        // 登录管理后台
        await page.waitForTimeout(3000);
        const usernameInput = await page.locator('input[type="text"]').first();
        const passwordInput = await page.locator('input[type="password"]').first();
        const loginButton = await page.locator('button:has-text("登录")').first();
        
        if (await usernameInput.isVisible()) {
            await usernameInput.fill('admin');
            await passwordInput.fill('admin123');
            await loginButton.click();
            await page.waitForTimeout(10000);
        }
        
        console.log('\n📍 第一步：直接查询数据库原始数据');
        
        // 直接查询数据库原始数据（不经过管理后台的修复逻辑）
        const rawDatabaseData = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                
                // 直接查询分类数据
                const categoriesResult = await db.collection('categories').get();
                
                // 直接查询表情包数据
                const emojisResult = await db.collection('emojis').get();
                
                // 直接查询横幅数据
                const bannersResult = await db.collection('banners').get();
                
                return {
                    success: true,
                    categories: {
                        count: categoriesResult.data.length,
                        data: categoriesResult.data.map(cat => ({
                            _id: cat._id,
                            name: cat.name,
                            status: cat.status,
                            gradient: cat.gradient,
                            allFields: Object.keys(cat),
                            hasDataField: !!cat.data,
                            dataContent: cat.data ? Object.keys(cat.data) : null
                        }))
                    },
                    emojis: {
                        count: emojisResult.data.length,
                        data: emojisResult.data.map(emoji => ({
                            _id: emoji._id,
                            title: emoji.title,
                            status: emoji.status,
                            categoryId: emoji.categoryId,
                            allFields: Object.keys(emoji),
                            hasDataField: !!emoji.data,
                            dataContent: emoji.data ? Object.keys(emoji.data) : null
                        }))
                    },
                    banners: {
                        count: bannersResult.data.length,
                        data: bannersResult.data.map(banner => ({
                            _id: banner._id,
                            title: banner.title,
                            status: banner.status,
                            allFields: Object.keys(banner),
                            hasDataField: !!banner.data,
                            dataContent: banner.data ? Object.keys(banner.data) : null
                        }))
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 数据库原始数据结构:');
        if (rawDatabaseData.success) {
            console.log(`\n📋 分类原始数据 (${rawDatabaseData.categories.count}条):`);
            rawDatabaseData.categories.data.forEach((cat, index) => {
                console.log(`  ${index + 1}. ID: ${cat._id}`);
                console.log(`     名称: ${cat.name} (类型: ${typeof cat.name})`);
                console.log(`     状态: ${cat.status} (类型: ${typeof cat.status})`);
                console.log(`     渐变: ${cat.gradient ? cat.gradient.substring(0, 50) + '...' : 'N/A'}`);
                console.log(`     所有字段: [${cat.allFields.join(', ')}]`);
                console.log(`     有data字段: ${cat.hasDataField}`);
                if (cat.hasDataField) {
                    console.log(`     data内容: [${cat.dataContent.join(', ')}]`);
                }
                console.log('');
            });
            
            console.log(`\n📋 表情包原始数据 (${rawDatabaseData.emojis.count}条):`);
            rawDatabaseData.emojis.data.forEach((emoji, index) => {
                console.log(`  ${index + 1}. ID: ${emoji._id}`);
                console.log(`     标题: ${emoji.title} (类型: ${typeof emoji.title})`);
                console.log(`     状态: ${emoji.status} (类型: ${typeof emoji.status})`);
                console.log(`     分类ID: ${emoji.categoryId} (类型: ${typeof emoji.categoryId})`);
                console.log(`     所有字段: [${emoji.allFields.join(', ')}]`);
                console.log(`     有data字段: ${emoji.hasDataField}`);
                if (emoji.hasDataField) {
                    console.log(`     data内容: [${emoji.dataContent.join(', ')}]`);
                }
                console.log('');
            });
            
            console.log(`\n📋 横幅原始数据 (${rawDatabaseData.banners.count}条):`);
            rawDatabaseData.banners.data.forEach((banner, index) => {
                console.log(`  ${index + 1}. ID: ${banner._id}`);
                console.log(`     标题: ${banner.title} (类型: ${typeof banner.title})`);
                console.log(`     状态: ${banner.status} (类型: ${typeof banner.status})`);
                console.log(`     所有字段: [${banner.allFields.join(', ')}]`);
                console.log(`     有data字段: ${banner.hasDataField}`);
                if (banner.hasDataField) {
                    console.log(`     data内容: [${banner.dataContent.join(', ')}]`);
                }
                console.log('');
            });
        } else {
            console.log(`❌ 原始数据查询失败: ${rawDatabaseData.error}`);
        }
        
        console.log('\n📍 第二步：模拟云函数查询条件');
        
        // 模拟云函数的查询条件
        const cloudFunctionQuery = await page.evaluate(async () => {
            try {
                const db = window.tcbApp.database();
                
                // 模拟分类云函数查询
                const categoriesQuery = await db.collection('categories')
                    .where({ status: 'show' })
                    .orderBy('sort', 'asc')
                    .get();
                
                // 模拟表情包云函数查询
                const emojisQuery = await db.collection('emojis')
                    .where({ status: 'published' })
                    .orderBy('createTime', 'desc')
                    .limit(20)
                    .get();
                
                // 模拟横幅云函数查询
                const bannersQuery = await db.collection('banners')
                    .where({ status: 'show' })
                    .get();
                
                return {
                    success: true,
                    categories: {
                        count: categoriesQuery.data.length,
                        data: categoriesQuery.data.map(cat => ({
                            _id: cat._id,
                            name: cat.name,
                            status: cat.status,
                            sort: cat.sort,
                            hasValidName: !!cat.name && cat.name !== 'undefined',
                            hasValidStatus: !!cat.status && cat.status !== 'undefined'
                        }))
                    },
                    emojis: {
                        count: emojisQuery.data.length,
                        data: emojisQuery.data.map(emoji => ({
                            _id: emoji._id,
                            title: emoji.title,
                            status: emoji.status,
                            categoryId: emoji.categoryId,
                            hasValidTitle: !!emoji.title && emoji.title !== 'undefined',
                            hasValidStatus: !!emoji.status && emoji.status !== 'undefined'
                        }))
                    },
                    banners: {
                        count: bannersQuery.data.length,
                        data: bannersQuery.data.map(banner => ({
                            _id: banner._id,
                            title: banner.title,
                            status: banner.status,
                            hasValidTitle: !!banner.title && banner.title !== 'undefined',
                            hasValidStatus: !!banner.status && banner.status !== 'undefined'
                        }))
                    }
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });
        
        console.log('📊 云函数查询条件模拟结果:');
        if (cloudFunctionQuery.success) {
            console.log(`\n📋 分类查询结果 (${cloudFunctionQuery.categories.count}条):`);
            cloudFunctionQuery.categories.data.forEach((cat, index) => {
                console.log(`  ${index + 1}. ${cat.name} (状态: ${cat.status}, 排序: ${cat.sort})`);
                console.log(`     有效名称: ${cat.hasValidName}, 有效状态: ${cat.hasValidStatus}`);
                
                if (!cat.hasValidName || !cat.hasValidStatus) {
                    console.log(`     🔴 数据无效，会被云函数过滤`);
                } else {
                    console.log(`     ✅ 数据有效，会被云函数返回`);
                }
            });
            
            console.log(`\n📋 表情包查询结果 (${cloudFunctionQuery.emojis.count}条):`);
            cloudFunctionQuery.emojis.data.forEach((emoji, index) => {
                console.log(`  ${index + 1}. ${emoji.title} (状态: ${emoji.status})`);
                console.log(`     有效标题: ${emoji.hasValidTitle}, 有效状态: ${emoji.hasValidStatus}`);
                
                if (!emoji.hasValidTitle || !emoji.hasValidStatus) {
                    console.log(`     🔴 数据无效，会被云函数过滤`);
                } else {
                    console.log(`     ✅ 数据有效，会被云函数返回`);
                }
            });
            
            console.log(`\n📋 横幅查询结果 (${cloudFunctionQuery.banners.count}条):`);
            cloudFunctionQuery.banners.data.forEach((banner, index) => {
                console.log(`  ${index + 1}. ${banner.title} (状态: ${banner.status})`);
                console.log(`     有效标题: ${banner.hasValidTitle}, 有效状态: ${banner.hasValidStatus}`);
                
                if (!banner.hasValidTitle || !banner.hasValidStatus) {
                    console.log(`     🔴 数据无效，会被云函数过滤`);
                } else {
                    console.log(`     ✅ 数据有效，会被云函数返回`);
                }
            });
        } else {
            console.log(`❌ 云函数查询模拟失败: ${cloudFunctionQuery.error}`);
        }
        
        console.log('\n📍 第三步：问题根源分析');
        
        if (rawDatabaseData.success && cloudFunctionQuery.success) {
            // 分析分类数据问题
            const categoriesWithData = rawDatabaseData.categories.data.filter(cat => cat.hasDataField);
            const categoriesWithValidData = cloudFunctionQuery.categories.data.filter(cat => cat.hasValidName && cat.hasValidStatus);
            
            console.log('\n🔍 分类数据问题分析:');
            console.log(`原始数据总数: ${rawDatabaseData.categories.count}`);
            console.log(`有data字段的数据: ${categoriesWithData.length}`);
            console.log(`查询条件匹配的数据: ${cloudFunctionQuery.categories.count}`);
            console.log(`有效数据: ${categoriesWithValidData.length}`);
            
            if (categoriesWithData.length > 0) {
                console.log('🔴 发现问题: 数据被错误包装在data字段中，云函数无法正确查询');
            }
            
            // 分析表情包数据问题
            const emojisWithData = rawDatabaseData.emojis.data.filter(emoji => emoji.hasDataField);
            const emojisWithValidData = cloudFunctionQuery.emojis.data.filter(emoji => emoji.hasValidTitle && emoji.hasValidStatus);
            
            console.log('\n🔍 表情包数据问题分析:');
            console.log(`原始数据总数: ${rawDatabaseData.emojis.count}`);
            console.log(`有data字段的数据: ${emojisWithData.length}`);
            console.log(`查询条件匹配的数据: ${cloudFunctionQuery.emojis.count}`);
            console.log(`有效数据: ${emojisWithValidData.length}`);
            
            if (emojisWithData.length > 0) {
                console.log('🔴 发现问题: 数据被错误包装在data字段中，云函数无法正确查询');
            }
            
            // 分析横幅数据问题
            const bannersWithData = rawDatabaseData.banners.data.filter(banner => banner.hasDataField);
            const bannersWithValidData = cloudFunctionQuery.banners.data.filter(banner => banner.hasValidTitle && banner.hasValidStatus);
            
            console.log('\n🔍 横幅数据问题分析:');
            console.log(`原始数据总数: ${rawDatabaseData.banners.count}`);
            console.log(`有data字段的数据: ${bannersWithData.length}`);
            console.log(`查询条件匹配的数据: ${cloudFunctionQuery.banners.count}`);
            console.log(`有效数据: ${bannersWithValidData.length}`);
            
            if (bannersWithData.length > 0) {
                console.log('🔴 发现问题: 数据被错误包装在data字段中，云函数无法正确查询');
            }
        }
        
        console.log('\n🎯 问题总结:');
        console.log('1. 数据库中的数据被错误包装在data字段中');
        console.log('2. 云函数直接查询数据库，无法获取到正确的字段值');
        console.log('3. 管理后台通过修复逻辑能正确显示，但小程序获取不到');
        console.log('4. 需要修复云函数的数据处理逻辑，或者修复数据库中的数据结构');
        
        return {
            success: true,
            rawData: rawDatabaseData,
            queryResult: cloudFunctionQuery
        };
        
    } catch (error) {
        console.error('❌ 调试过程中出错:', error);
        await page.screenshot({ path: 'debug-cloud-function-error.png' });
        return {
            success: false,
            error: error.message
        };
    } finally {
        // 截图
        await page.screenshot({ path: 'debug-cloud-function-data.png', fullPage: true });
        console.log('\n📸 调试截图已保存: debug-cloud-function-data.png');
        
        console.log('\n⏸️ 调试完成，浏览器将保持打开15秒供查看...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

// 运行调试
debugCloudFunctionData().then(result => {
    console.log('\n🎯 云函数数据调试最终结果:', result.success ? '成功' : '失败');
}).catch(console.error);
