/**
 * 调试访问问题
 * 检查可能的访问问题
 */

const https = require('https');
const http = require('http');

console.log('🔍 开始调试访问问题...\n');

// 测试不同的URL
const testUrls = [
    'https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/',
    'https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/',
    'https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/admin/index.html',
    'https://cloud1-5g6pvnpl88dc0142.tcloudbaseapp.com/index.html'
];

async function testUrl(url) {
    return new Promise((resolve) => {
        const client = url.startsWith('https') ? https : http;
        
        console.log(`🌐 测试: ${url}`);
        
        const req = client.get(url, (res) => {
            console.log(`   状态码: ${res.statusCode}`);
            console.log(`   响应头: ${JSON.stringify(res.headers, null, 2)}`);
            
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log(`   ✅ 成功访问`);
                    if (data.includes('<title>')) {
                        const title = data.match(/<title>(.*?)<\/title>/i);
                        console.log(`   📄 页面标题: ${title ? title[1] : '未找到'}`);
                    }
                } else {
                    console.log(`   ❌ 访问失败`);
                    console.log(`   📄 响应内容: ${data.substring(0, 200)}...`);
                }
                resolve();
            });
        });
        
        req.on('error', (error) => {
            console.log(`   ❌ 请求错误: ${error.message}`);
            resolve();
        });
        
        req.setTimeout(10000, () => {
            console.log(`   ⏰ 请求超时`);
            req.destroy();
            resolve();
        });
    });
}

async function runTests() {
    for (const url of testUrls) {
        await testUrl(url);
        console.log(''); // 空行分隔
    }
    
    console.log('🔧 可能的解决方案:');
    console.log('1. 检查静态网站托管是否已开通');
    console.log('2. 检查文件是否上传到正确位置');
    console.log('3. 检查域名配置是否正确');
    console.log('4. 尝试直接访问 index.html');
    console.log('5. 检查微信开发者工具中的静态托管状态');
}

runTests();
