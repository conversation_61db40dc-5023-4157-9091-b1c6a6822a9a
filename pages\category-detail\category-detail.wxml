<!--pages/category-detail/category-detail.wxml-->
<view class="container">
  <view class="emoji-grid">
    <view 
      class="emoji-item"
      wx:for="{{emojiList}}" 
      wx:key="id"
      bindtap="onEmojiTap"
      data-emoji="{{item}}"
    >
      <image class="emoji-image" src="{{item.imageUrl}}" mode="aspectFill" />
      <view class="emoji-info">
        <text class="emoji-title">{{item.title || item.name}}</text>
        <!-- 标签区域 -->
        <view class="emoji-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view
            class="tag-item"
            wx:for="{{item.tags}}"
            wx:for-item="tag"
            wx:key="*this"
          >
            {{tag}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>