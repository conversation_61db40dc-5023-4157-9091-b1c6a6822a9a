/* 🎨 TabBar 高级优化样式 */

/* ===== 配色主题方案 ===== */

/* 主题1: 现代紫色 (默认) */
:root {
  --tab-color-normal: #9CA3AF;
  --tab-color-active: #8B5CF6;
  --tab-bg-primary: #FFFFFF;
  --tab-bg-secondary: #FEFEFE;
  --tab-shadow: rgba(139, 92, 246, 0.15);
}

/* 主题2: 活力橙色 */
.theme-orange {
  --tab-color-normal: #9CA3AF;
  --tab-color-active: #F97316;
  --tab-bg-primary: #FFFFFF;
  --tab-bg-secondary: #FFFBF5;
  --tab-shadow: rgba(249, 115, 22, 0.15);
}

/* 主题3: 清新绿色 */
.theme-green {
  --tab-color-normal: #9CA3AF;
  --tab-color-active: #10B981;
  --tab-bg-primary: #FFFFFF;
  --tab-bg-secondary: #F0FDF4;
  --tab-shadow: rgba(16, 185, 129, 0.15);
}

/* 主题4: 经典蓝色 */
.theme-blue {
  --tab-color-normal: #9CA3AF;
  --tab-color-active: #3B82F6;
  --tab-bg-primary: #FFFFFF;
  --tab-bg-secondary: #F0F9FF;
  --tab-shadow: rgba(59, 130, 246, 0.15);
}

/* ===== TabBar 增强样式 ===== */

/* TabBar 容器优化 */
.wx-tabbar {
  background: linear-gradient(180deg, var(--tab-bg-primary) 0%, var(--tab-bg-secondary) 100%) !important;
  box-shadow: 0 -4px 20px var(--tab-shadow) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.8) !important;
  height: 50px !important;
}

/* TabBar 项目容器 */
.wx-tabbar-item {
  position: relative !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 11px !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
}

/* 选中状态动画 */
.wx-tabbar-item.wx-tabbar-item_selected {
  transform: translateY(-2px) scale(1.05) !important;
  font-weight: 600 !important;
  color: var(--tab-color-active) !important;
}

/* 点击动画效果 */
.wx-tabbar-item:active {
  transform: scale(0.95) !important;
  transition: transform 0.15s ease !important;
}

/* 选中状态背景光晕 */
.wx-tabbar-item.wx-tabbar-item_selected::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, var(--tab-shadow) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  animation: tabGlow 0.3s ease-out;
}

/* 光晕动画 */
@keyframes tabGlow {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 文字渐变效果 */
.wx-tabbar-item.wx-tabbar-item_selected .wx-tabbar-label {
  background: linear-gradient(135deg, var(--tab-color-active) 0%, var(--tab-color-active) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600 !important;
}

/* 弹跳动画 */
.wx-tabbar-item.wx-tabbar-item_selected {
  animation: tabBounce 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes tabBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-4px) scale(1.1);
  }
  100% {
    transform: translateY(-2px) scale(1.05);
  }
}

/* ===== 高级视觉效果 ===== */

/* 毛玻璃效果增强 */
.wx-tabbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  z-index: -1;
}

/* 顶部分割线 */
.wx-tabbar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.1) 50%, 
    transparent 100%);
}

/* ===== 响应式适配 ===== */

/* iPhone SE 等小屏设备 */
@media (max-width: 375px) {
  .wx-tabbar-item {
    font-size: 10px !important;
  }
}

/* iPhone 12 Pro Max 等大屏设备 */
@media (min-width: 414px) {
  .wx-tabbar-item {
    font-size: 12px !important;
  }
}

/* 横屏适配 */
@media (orientation: landscape) {
  .wx-tabbar {
    height: 45px !important;
  }
  
  .wx-tabbar-item {
    font-size: 10px !important;
  }
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --tab-color-normal: #6B7280;
    --tab-color-active: #A78BFA;
    --tab-bg-primary: #1F2937;
    --tab-bg-secondary: #111827;
    --tab-shadow: rgba(167, 139, 250, 0.2);
  }
  
  .wx-tabbar {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}

/* ===== 无障碍支持 ===== */
@media (prefers-reduced-motion: reduce) {
  .wx-tabbar-item,
  .wx-tabbar-item.wx-tabbar-item_selected {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
}

/* ===== 徽章样式 ===== */
.tab-badge {
  position: absolute;
  top: 2px;
  right: 8px;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}